const { defineConfig, devices } = require('@playwright/test'); module.exports = defineConfig({ testDir: './tests/e2e', fullyParallel: false, // Maintain test order for database consistency forbidOnly: !!process.env.CI, retries: process.env.CI ? 2 : 0, workers: process.env.CI ? 1 : 1, // Single worker to avoid conflicts // Enhanced reporting reporter: [ ['html', { open: 'never', outputFolder: 'test-results/html-report', attachmentsBaseURL: 'file://' }], ['json', { outputFile: 'test-results/results.json' }], ['junit', { outputFile: 'test-results/junit.xml' }], ['list'], ['github'] // GitHub Actions integration ], // Timeouts timeout: 60000, // 60 seconds per test expect: { timeout: 15000 // 15 seconds for assertions }, // Global test setup - DISABLED FOR FRONTEND-ONLY TESTING // globalSetup: require.resolve('./tests/setup/global-setup.js'), // globalTeardown: require.resolve('./tests/setup/global-teardown.js'), // Default test configuration use: { baseURL: process.env.BASE_URL || 'http://localhost:3001', trace: 'on-first-retry', screenshot: 'only-on-failure', video: 'retain-on-failure', actionTimeout: 30000, navigationTimeout: 30000, // Enhanced headers for API testing extraHTTPHeaders: { 'Accept': 'application/json', 'Content-Type': 'application/json', 'User-Agent': 'Playwright-E2E-Tests' } }, // Test projects for different browsers and viewports projects: [ // Desktop browsers { name: 'chromium-desktop', use: { ...devices['Desktop Chrome'], viewport: { width: 1920, height: 1080 } }, }, { name: 'firefox-desktop', use: { ...devices['Desktop Firefox'], viewport: { width: 1920, height: 1080 } }, }, { name: 'webkit-desktop', use: { ...devices['Desktop Safari'], viewport: { width: 1920, height: 1080 } }, }, // Mobile devices { name: 'mobile-chrome', use: { ...devices['Pixel 5'] }, }, { name: 'mobile-safari', use: { ...devices['iPhone 12'] }, }, // Tablet { name: 'tablet', use: { ...devices['iPad Pro'], viewport: { width: 1024, height: 768 } }, } ], // Output directory outputDir: 'test-results/artifacts', // Web server configuration for local development webServer: process.env.CI ? undefined : [ { command: 'npm run start:backend-only', port: 5000, timeout: 120 * 1000, reuseExistingServer: !process.env.CI, env: { NODE_ENV: 'test', PORT: '5000', MONGODB_URI: process.env.TEST_MONGODB_URI || 'mongodb://localhost:27017/chatbot-test', JWT_SECRET: 'test-jwt-secret-key', SESSION_SECRET: 'test-session-secret' } }, { command: 'npm run start:frontend-only', port: 3001, timeout: 120 * 1000, reuseExistingServer: !process.env.CI, env: { REACT_APP_API_URL: 'http://localhost:5000', REACT_APP_ENV: 'test', PORT: '3001' } } ] });