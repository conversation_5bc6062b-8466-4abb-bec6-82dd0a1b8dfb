# Données de Configuration AWS pour Migration - Free Mobile Chatbot

## 🔐 **Informations de Sécurité AWS (À COMPLÉTER)**

### **Credentials AWS**
```bash
# À configurer via AWS CLI
aws configure
AWS Access Key ID: [********************]
AWS Secret Access Key: [crgKyRuF83a8lfuuzpSZgUUo5/Fc0RvONgwx855t]
Default region: eu-west-3  # Paris (recommandé pour Free Mobile)
Default output format: json
```

### **Compte AWS**
- **Account ID**: [************]
- **Region Principal**: `eu-west-3` (Paris) - Proche de Free Mobile
- **Region Backup**: `eu-west-1` (Irlande) - Pour disaster recovery
- **IAM Role**: `FreeMobileChatbotDeploymentRole`
- **Billing Alert**: Configuré pour > 100€/mois

---

## 🖥️ **Configuration EC2**

### **Instances Specifications**
```yaml
Development Environment:
  Instance Type: t3.medium
  CPU: 2 vCPU
  RAM: 4 GB
  Storage: 20 GB gp3 SSD
  Network: Enhanced Networking

Production Environment:
  Instance Type: t3.large (initial) / c5.xlarge (scaled)
  CPU: 2-4 vCPU
  RAM: 8-16 GB
  Storage: 50 GB gp3 SSD + 100 GB EBS pour logs
  Network: Enhanced Networking + Placement Groups
```

### **Auto Scaling Configuration**
```yaml
Auto Scaling Group:
  Min Instances: 2
  Max Instances: 20
  Desired Capacity: 2
  Health Check: ELB + EC2
  Health Check Grace Period: 300s
  Default Cooldown: 300s
  
Scaling Policies:
  Scale Up: CPU > 70% for 2 minutes
  Scale Down: CPU < 30% for 5 minutes
  Target Tracking: 60% CPU utilization
```

### **Security Groups**
```yaml
EC2-Web-SG:
  Inbound:
    - Port 22 (SSH): Your-IP-Only/32
    - Port 80 (HTTP): 0.0.0.0/0
    - Port 443 (HTTPS): 0.0.0.0/0
    - Port 3000 (React): ALB-SG
    - Port 5000 (API): ALB-SG
  Outbound:
    - All traffic: 0.0.0.0/0

ALB-SG:
  Inbound:
    - Port 80: 0.0.0.0/0
    - Port 443: 0.0.0.0/0
  Outbound:
    - Port 3000: EC2-Web-SG
    - Port 5000: EC2-Web-SG
```

### **Key Pair**
```bash
# À créer AVANT la migration
Key Pair Name: free-mobile-chatbot-keypair
Key Type: RSA
Key Format: .pem
Storage: Sécurisé localement + AWS Systems Manager
```

### **User Data Script Template**
```bash
# Instance Metadata Requirements
Instance Name: FreeMobile-Chatbot-Web-${ENVIRONMENT}
Environment Tags:
  - Environment: production/staging/development
  - Project: FreeMobileChatbot
  - Owner: [VOTRE_EMAIL]
  - CostCenter: SAV-Digital
  - Backup: required
```

---

## 🗄️ **Configuration RDS**

### **Database Specifications**
```yaml
Engine: PostgreSQL
Version: 15.4 (latest stable)
License: postgresql-license

Instance Classes:
  Development: db.t3.micro (1 vCPU, 1 GB RAM)
  Staging: db.t3.small (1 vCPU, 2 GB RAM)  
  Production: db.r6g.large (2 vCPU, 16 GB RAM)

Storage:
  Type: gp3 (General Purpose SSD)
  Size: 100 GB (initial) - Auto Scaling enabled
  IOPS: 3000 (baseline)
  Throughput: 125 MB/s
```

### **Database Configuration**
```yaml
DB Instance Identifier: chatbotDB
Database Name: ProgresChatbot01
Master Username: chatbotdb
Master Password: [Adan=20102016]

Network & Security:
  VPC: freemobile-chatbot-vpc
  Subnet Group: freemobile-db-subnet-group
  Security Group: RDS-PostgreSQL-SG
  Point de terminaison: chatbotdb.cpac68qq0b0k.eu-west-3.rds.amazonaws.com
  Public Access: No
  Port: 5432

Backup & Maintenance:
  Backup Retention: 7 days
  Backup Window: 03:00-04:00 UTC (5h-6h Paris)
  Maintenance Window: Sunday 04:00-05:00 UTC
  Copy Tags: Yes
  Encryption: Enabled (AWS KMS)
```

### **Security Group RDS**
```yaml
RDS-PostgreSQL-SG:
  Inbound:
    - Port 5432: EC2-Web-SG
    - Port 5432: Your-IP/32 (pour admin)
  Outbound:
    - None required
```

### **Connection String Template**
```javascript
// Production
const connectionString = {
  host: 'chatbotdb.cpac68qq0b0k.eu-west-3.rds.amazonaws.com',
  port: 5432,
  database: 'ProgresChatbot01',
  username: 'chatbotdb',
  password: '${Adan=20102016}',
  ssl: true,
  max: 20, // Connection pool
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000
}
```

---

## ⚡ **Configuration ElastiCache**

### **Redis Cluster Specifications**
```yaml
Engine: Redis
Version: 7.0.7 (latest)

Node Type:
  Development: cache.t3.micro (1 vCPU, 0.5 GB)
  Production: cache.r6g.large (2 vCPU, 13.07 GB)

Cluster Configuration:
  Cluster Mode: Enabled
  Replication Groups: 2
  Replicas per Shard: 1
  Shards: 2
  Total Nodes: 4
```

### **Network Configuration**
```yaml
Subnet Group: freemobile-redis-subnet-group
Security Group: ElastiCache-Redis-SG
Parameter Group: default.redis7.cluster.on
Port: 6379
Encryption:
  At Rest: Enabled
  In Transit: Enabled
  Auth Token: Enabled
```

### **Security Group ElastiCache**
```yaml
ElastiCache-Redis-SG:
  Inbound:
    - Port 6379: EC2-Web-SG
  Outbound:
    - None required
```

### **Connection Configuration**
```javascript
// Redis Configuration
const redisConfig = {
  nodes: [
    {
      host: 'rncpchatbot-kcimce.serverless.euw3.cache.amazonaws.com:6379',
      port: 6379
    }
  ],
  options: {
    redisOptions: {
      password: '${Adan=20102016}',
      tls: {
        checkServerIdentity: false
      }
    }
  }
}
```

---

## 🌐 **Configuration Réseau (VPC)**

### **VPC Specifications**
```yaml
VPC:
  Name: freemobile-chatbot-vpc
  CIDR Block: 10.0.0.0/16
  Enable DNS Hostnames: Yes
  Enable DNS Resolution: Yes
  
Internet Gateway:
  Name: freemobile-chatbot-igw
  
NAT Gateways:
  Quantity: 2 (Multi-AZ)
  Type: Gateway (managed)
```

### **Subnets Configuration**
```yaml
Public Subnets (ALB):
  Subnet-1: ********/24 (eu-west-3a)
  Subnet-2: ********/24 (eu-west-3b)
  
Private Subnets (EC2):
  Subnet-3: ********/24 (eu-west-3a)
  Subnet-4: ********/24 (eu-west-3b)
  
Database Subnets (RDS):
  Subnet-5: ********/24 (eu-west-3a)
  Subnet-6: ********/24 (eu-west-3b)
  
Cache Subnets (ElastiCache):
  Subnet-7: ********/24 (eu-west-3a)
  Subnet-8: ********/24 (eu-west-3b)
```

---

## 🔧 **Configuration Application Load Balancer**

### **ALB Specifications**
```yaml
Name: freemobile-chatbot-alb
Scheme: Internet-facing
IP Address Type: ipv4
Security Groups: ALB-SG
Subnets: Public Subnets (********/24, ********/24)

Target Groups:
  Frontend-TG:
    Protocol: HTTP
    Port: 3000
    Health Check Path: /
    Health Check Interval: 30s
    
  Backend-TG:
    Protocol: HTTP
    Port: 5000
    Health Check Path: /health
    Health Check Interval: 30s
```

### **SSL Certificate**
```yaml
Certificate Source: AWS Certificate Manager (ACM)
Domain: humanitasdigitalstore.fr
Alternative Names:
  - api.chatbot.freemobile.fr
  - admin.chatbot.freemobile.fr
Validation: DNS Validation
```

---

## 📊 **Configuration CloudWatch**

### **Log Groups**
```yaml
Log Groups:
  - /aws/ec2/freemobile-chatbot/application
  - /aws/ec2/freemobile-chatbot/nginx
  - /aws/ec2/freemobile-chatbot/pm2
  - /aws/rds/freemobile-chatbot/postgresql
  - /aws/elasticache/freemobile-chatbot/redis

Retention: 7 days (dev) / 30 days (prod)
```

### **Custom Metrics**
```yaml
Application Metrics:
  - Authentication.RequestsPerMinute
  - AI.ResponseTime
  - Database.ConnectionPoolUsage
  - Cache.HitRatio
  - API.ErrorRate

Business Metrics:
  - ActiveUsers.Count
  - Conversations.Created
  - Escalations.ToAgents
```

### **Alarms Configuration**
```yaml
Critical Alarms:
  - EC2 CPU > 80% for 5 minutes
  - RDS CPU > 85% for 5 minutes
  - ALB 5XX errors > 10 in 5 minutes
  - ElastiCache CPU > 80%

Warning Alarms:
  - EC2 Memory > 75%
  - RDS Connections > 80% max
  - API Response Time > 2s
  - Cache Hit Ratio < 80%
```

---

## 🔐 **Configuration Secrets Manager**

### **Secrets à Créer**
```yaml
Database Credentials:
  Name: freemobile-chatbot/db/credentials
  Content:
    username: chatbot_admin
    password: [Adan=20102016]
    engine: postgres
    host: [RDS-ENDPOINT]
    port: 5432
    dbname: freemobile_chatbot

JWT Secrets:
  Name: freemobile-chatbot/jwt/secrets
  Content:
    jwt_secret: [AUTO-GENERATED-256-BIT]
    jwt_refresh_secret: [AUTO-GENERATED-256-BIT]
    jwt_expiry: 7d

Redis Auth:
  Name: freemobile-chatbot/redis/auth
  Content:
    auth_token: [AUTO-GENERATED]
    host: [ELASTICACHE-ENDPOINT]
    port: 6379

External APIs:
  Name: freemobile-chatbot/external/apis
  Content:
    openai_api_key: [VOTRE_OPENAI_KEY]
    bedrock_access_key: [AWS_BEDROCK_KEY]
    free_mobile_api_key: [FREE_MOBILE_API_KEY]
```

---

## 🎯 **Variables d'Environnement de Production**

### **Environment Variables Template**
```bash
# AWS Configuration
AWS_REGION=eu-west-3
AWS_ACCOUNT_ID=[VOTRE_ACCOUNT_ID]

# Application
NODE_ENV=production
PORT=5000
FRONTEND_PORT=3000

# Database (Retrieved from Secrets Manager)
DB_HOST=${aws:secretsmanager:freemobile-chatbot/db/credentials:host}
DB_PORT=${aws:secretsmanager:freemobile-chatbot/db/credentials:port}
DB_NAME=${aws:secretsmanager:freemobile-chatbot/db/credentials:dbname}
DB_USER=${aws:secretsmanager:freemobile-chatbot/db/credentials:username}
DB_PASSWORD=${aws:secretsmanager:freemobile-chatbot/db/credentials:password}

# Cache
REDIS_HOST=${aws:secretsmanager:freemobile-chatbot/redis/auth:host}
REDIS_PORT=${aws:secretsmanager:freemobile-chatbot/redis/auth:port}
REDIS_AUTH_TOKEN=${aws:secretsmanager:freemobile-chatbot/redis/auth:auth_token}

# JWT
JWT_SECRET=${aws:secretsmanager:freemobile-chatbot/jwt/secrets:jwt_secret}
JWT_REFRESH_SECRET=${aws:secretsmanager:freemobile-chatbot/jwt/secrets:jwt_refresh_secret}
JWT_EXPIRY=${aws:secretsmanager:freemobile-chatbot/jwt/secrets:jwt_expiry}

# External Services
OPENAI_API_KEY=${aws:secretsmanager:freemobile-chatbot/external/apis:openai_api_key}
BEDROCK_ACCESS_KEY=${aws:secretsmanager:freemobile-chatbot/external/apis:bedrock_access_key}

# Monitoring
CLOUDWATCH_LOG_GROUP=/aws/ec2/freemobile-chatbot/application
XRAY_TRACING_ENABLED=true

# Load Balancer
ALB_DNS_NAME=freemobile-chatbot-alb-xxxxxxxxx.eu-west-3.elb.amazonaws.com
DOMAIN_NAME=chatbot.freemobile.fr
```

---

## 📋 **Checklist Pré-Migration**

### **Avant de Commencer**
- [ ] Compte AWS configuré avec billing alerts
- [ ] AWS CLI installé et configuré
- [ ] Terraform/AWS CDK installé
- [ ] Domaine DNS disponible (ou sous-domaine)
- [ ] Certificat SSL préparé
- [ ] Backup de la base locale effectué
- [ ] Tests de l'application en local validés

### **Credentials Nécessaires**
- [ ] AWS Access Key ID / Secret Access Key
- [ ] OpenAI API Key (si utilisé)
- [ ] Clés API Free Mobile (si intégration requise)
- [ ] Certificats SSL/TLS

### **Budget Estimé**
```yaml
Coût Mensuel Estimé (EU-West-3):
  EC2 (2x t3.large): ~140€
  RDS (db.r6g.large): ~280€
  ElastiCache (cache.r6g.large): ~200€
  ALB: ~20€
  Data Transfer: ~50€
  CloudWatch: ~10€
  
Total Estimé: ~700€/mois (production)
Development: ~100€/mois
```

---

## 🔄 **Plan de Rollback**

### **Stratégie de Retour**
```yaml
Database Rollback:
  - Snapshot RDS avant migration
  - Export SQL de sauvegarde
  - Script de restauration local

Application Rollback:
  - AMI snapshot avant déploiement
  - Git tag version stable
  - Blue/Green deployment ready

DNS Rollback:
  - TTL réduit à 300s pendant migration
  - Route 53 health checks
  - Basculement automatique si échec
```

Cette configuration complète fournira à Cursor toutes les informations nécessaires pour effectuer une migration sécurisée et professionnelle vers AWS.