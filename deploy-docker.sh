#!/bin/bash

# 🚀 Script de Déploiement Docker - Chatbot Free Mobile
# Usage: ./deploy-docker.sh [dev|prod|backup|restore|logs|update|clean]

set -e

# =============================================
# 🎨 Configuration des couleurs et émojis
# =============================================
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Émojis
ROCKET="🚀"
CHECK="✅"
CROSS="❌"
WARNING="⚠️"
INFO="ℹ️"
GEAR="⚙️"
PACKAGE="📦"
SHIELD="🛡️"
CLOCK="⏰"
BACKUP="💾"
CLEAN="🧹"

# =============================================
# 📝 Configuration du projet
# =============================================
PROJECT_NAME="free-mobile-chatbot"
COMPOSE_FILE_DEV="docker-compose.yml"
COMPOSE_FILE_PROD="docker-compose.prod.yml"
ENV_FILE=".env"
BACKUP_DIR="./backups"
LOG_DIR="./logs"

# =============================================
# 🛠️ Fonctions utilitaires
# =============================================

# Affichage avec couleurs
log_info() {
    echo -e "${BLUE}${INFO}${NC} ${WHITE}$1${NC}"
}

log_success() {
    echo -e "${GREEN}${CHECK}${NC} ${WHITE}$1${NC}"
}

log_warning() {
    echo -e "${YELLOW}${WARNING}${NC} ${WHITE}$1${NC}"
}

log_error() {
    echo -e "${RED}${CROSS}${NC} ${WHITE}$1${NC}"
}

log_step() {
    echo -e "${PURPLE}${GEAR}${NC} ${WHITE}$1${NC}"
}

# Affichage de bannière
print_banner() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    🤖 CHATBOT FREE MOBILE                   ║"
    echo "║               Docker Deployment Manager v2.0                ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# Barre de progression
show_progress() {
    local duration=$1
    local message=$2
    
    echo -ne "${BLUE}${CLOCK}${NC} ${message} "
    
    for ((i=0; i<=duration; i++)); do
        sleep 0.1
        echo -ne "▓"
    done
    
    echo -e " ${GREEN}${CHECK}${NC}"
}

# =============================================
# 🔍 Vérifications des prérequis
# =============================================
check_prerequisites() {
    log_step "Vérification des prérequis..."
    
    # Vérifier Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker n'est pas installé. Installez Docker Desktop ou Docker Engine."
        exit 1
    fi
    
    # Vérifier Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose n'est pas installé. Installez Docker Compose."
        exit 1
    fi
    
    # Vérifier que Docker fonctionne
    if ! docker info &> /dev/null; then
        log_error "Docker daemon n'est pas démarré. Démarrez Docker."
        exit 1
    fi
    
    # Vérifier les fichiers requis
    if [[ "$ENVIRONMENT" == "prod" && ! -f "$COMPOSE_FILE_PROD" ]]; then
        log_error "Fichier $COMPOSE_FILE_PROD introuvable."
        exit 1
    fi
    
    if [[ "$ENVIRONMENT" == "dev" && ! -f "$COMPOSE_FILE_DEV" ]]; then
        log_error "Fichier $COMPOSE_FILE_DEV introuvable."
        exit 1
    fi
    
    log_success "Prérequis validés"
}

# =============================================
# 🔧 Gestion des variables d'environnement
# =============================================
setup_environment() {
    log_step "Configuration de l'environnement..."
    
    # Créer le fichier .env s'il n'existe pas
    if [[ ! -f "$ENV_FILE" ]]; then
        if [[ -f "env.template" ]]; then
            log_info "Création du fichier .env depuis le template..."
            cp env.template "$ENV_FILE"
            log_warning "Veuillez configurer les variables dans $ENV_FILE avant de continuer"
            return 1
        else
            log_error "Fichier $ENV_FILE et template introuvables"
            return 1
        fi
    fi
    
    # Charger les variables d'environnement
    export $(cat "$ENV_FILE" | grep -v '^#' | grep -v '^$' | xargs)
    
    # Vérifier les variables critiques
    local required_vars=("MONGO_USERNAME" "MONGO_PASSWORD" "JWT_SECRET")
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            log_error "Variable d'environnement $var non définie dans $ENV_FILE"
            return 1
        fi
    done
    
    # Vérifier la sécurité en production
    if [[ "$ENVIRONMENT" == "prod" ]]; then
        if [[ "$JWT_SECRET" == "dev-secret-key-not-for-production-please-change" ]]; then
            log_error "JWT_SECRET de développement détecté en production! Changez-le."
            return 1
        fi
        
        if [[ "$MONGO_PASSWORD" == "freemobile123" ]]; then
            log_warning "Mot de passe MongoDB par défaut détecté. Recommandé de le changer."
        fi
    fi
    
    log_success "Environnement configuré"
}

# =============================================
# 📂 Création des dossiers nécessaires
# =============================================
create_directories() {
    log_step "Création des dossiers nécessaires..."
    
    local dirs=("$LOG_DIR" "$BACKUP_DIR" "data/mongodb" "data/redis")
    
    for dir in "${dirs[@]}"; do
        if [[ ! -d "$dir" ]]; then
            mkdir -p "$dir"
            log_info "Dossier créé: $dir"
        fi
    done
    
    log_success "Dossiers créés"
}

# =============================================
# 🏗️ Construction des images Docker
# =============================================
build_images() {
    log_step "Construction des images Docker..."
    
    local compose_file=$1
    
    show_progress 20 "Construction en cours..."
    
    if [[ "$ENVIRONMENT" == "dev" ]]; then
        docker-compose -f "$compose_file" build --parallel
    else
        docker-compose -f "$compose_file" build --parallel --no-cache
    fi
    
    log_success "Images construites"
}

# =============================================
# 🚀 Démarrage des services
# =============================================
start_services() {
    log_step "Démarrage des services..."
    
    local compose_file=$1
    
    # Démarrer les services
    docker-compose -f "$compose_file" up -d
    
    # Attendre que les services soient prêts
    log_info "Attente du démarrage des services..."
    
    local services=("mongodb" "redis" "backend" "rasa" "frontend")
    local max_wait=300 # 5 minutes
    local wait_time=0
    
    for service in "${services[@]}"; do
        log_info "Vérification du service: $service"
        
        while ! docker-compose -f "$compose_file" ps "$service" | grep -q "Up"; do
            if [[ $wait_time -ge $max_wait ]]; then
                log_error "Timeout: Le service $service n'a pas démarré dans les temps"
                return 1
            fi
            
            sleep 5
            wait_time=$((wait_time + 5))
            echo -ne "."
        done
        
        log_success "Service $service démarré"
    done
    
    log_success "Tous les services sont démarrés"
}

# =============================================
# 🏥 Vérifications de santé
# =============================================
health_checks() {
    log_step "Vérification de la santé des services..."
    
    local compose_file=$1
    local max_attempts=30
    local attempt=1
    
    # URLs de health check
    local endpoints=(
        "http://localhost:3000/health:Frontend"
        "http://localhost:5000/health:Backend"
        "http://localhost:5005/status:Rasa"
    )
    
    for endpoint_info in "${endpoints[@]}"; do
        IFS=':' read -r url service <<< "$endpoint_info"
        
        log_info "Test de santé: $service ($url)"
        
        while [[ $attempt -le $max_attempts ]]; do
            if curl -f -s "$url" > /dev/null 2>&1; then
                log_success "$service est en bonne santé"
                break
            fi
            
            if [[ $attempt -eq $max_attempts ]]; then
                log_warning "$service ne répond pas après $max_attempts tentatives"
                break
            fi
            
            sleep 10
            attempt=$((attempt + 1))
        done
        
        attempt=1
    done
}

# =============================================
# 💾 Sauvegarde des données
# =============================================
backup_data() {
    log_step "Sauvegarde des données..."
    
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local backup_name="backup_${timestamp}"
    local backup_path="$BACKUP_DIR/$backup_name"
    
    mkdir -p "$backup_path"
    
    # Sauvegarde MongoDB
    log_info "Sauvegarde MongoDB..."
    if docker-compose ps mongodb | grep -q "Up"; then
        docker-compose exec -T mongodb mongodump --out /tmp/backup_mongo 2>/dev/null
        docker cp $(docker-compose ps -q mongodb):/tmp/backup_mongo "$backup_path/mongodb"
        log_success "MongoDB sauvegardé"
    else
        log_warning "MongoDB n'est pas démarré, sauvegarde ignorée"
    fi
    
    # Sauvegarde Redis
    log_info "Sauvegarde Redis..."
    if docker-compose ps redis | grep -q "Up"; then
        docker-compose exec -T redis redis-cli BGSAVE
        sleep 5
        docker cp $(docker-compose ps -q redis):/data/dump.rdb "$backup_path/redis_dump.rdb"
        log_success "Redis sauvegardé"
    else
        log_warning "Redis n'est pas démarré, sauvegarde ignorée"
    fi
    
    # Sauvegarde des logs
    if [[ -d "$LOG_DIR" ]]; then
        cp -r "$LOG_DIR" "$backup_path/logs"
        log_success "Logs sauvegardés"
    fi
    
    # Compression de la sauvegarde
    log_info "Compression de la sauvegarde..."
    tar -czf "$backup_path.tar.gz" -C "$BACKUP_DIR" "$backup_name"
    rm -rf "$backup_path"
    
    log_success "Sauvegarde créée: $backup_path.tar.gz"
    
    # Nettoyage des anciennes sauvegardes (garde les 7 dernières)
    log_info "Nettoyage des anciennes sauvegardes..."
    find "$BACKUP_DIR" -name "backup_*.tar.gz" -type f -mtime +7 -delete
    
    echo "$backup_path.tar.gz"
}

# =============================================
# 🔄 Restauration des données
# =============================================
restore_data() {
    local backup_file=$1
    
    if [[ ! -f "$backup_file" ]]; then
        log_error "Fichier de sauvegarde introuvable: $backup_file"
        return 1
    fi
    
    log_step "Restauration depuis: $backup_file"
    
    # Extraction de la sauvegarde
    local temp_dir="/tmp/restore_$(date +%s)"
    mkdir -p "$temp_dir"
    tar -xzf "$backup_file" -C "$temp_dir"
    
    local backup_name=$(basename "$backup_file" .tar.gz)
    local backup_path="$temp_dir/$backup_name"
    
    # Restauration MongoDB
    if [[ -d "$backup_path/mongodb" ]]; then
        log_info "Restauration MongoDB..."
        docker cp "$backup_path/mongodb" $(docker-compose ps -q mongodb):/tmp/restore_mongo
        docker-compose exec -T mongodb mongorestore --drop /tmp/restore_mongo
        log_success "MongoDB restauré"
    fi
    
    # Restauration Redis
    if [[ -f "$backup_path/redis_dump.rdb" ]]; then
        log_info "Restauration Redis..."
        docker-compose stop redis
        docker cp "$backup_path/redis_dump.rdb" $(docker-compose ps -q redis):/data/dump.rdb
        docker-compose start redis
        log_success "Redis restauré"
    fi
    
    # Nettoyage
    rm -rf "$temp_dir"
    
    log_success "Restauration terminée"
}

# =============================================
# 📊 Affichage des logs
# =============================================
show_logs() {
    local service=$1
    local compose_file=$2
    
    if [[ -n "$service" ]]; then
        log_info "Logs du service: $service"
        docker-compose -f "$compose_file" logs -f --tail=100 "$service"
    else
        log_info "Logs de tous les services"
        docker-compose -f "$compose_file" logs -f --tail=50
    fi
}

# =============================================
# 🔄 Mise à jour sans interruption
# =============================================
update_services() {
    log_step "Mise à jour des services..."
    
    local compose_file=$1
    
    # Sauvegarde automatique avant mise à jour
    log_info "Sauvegarde automatique avant mise à jour..."
    local backup_file=$(backup_data)
    
    # Pull des nouvelles images
    log_info "Téléchargement des nouvelles images..."
    docker-compose -f "$compose_file" pull
    
    # Reconstruction des images locales
    build_images "$compose_file"
    
    # Redémarrage progressif des services
    local services=("backend" "rasa" "frontend")
    
    for service in "${services[@]}"; do
        log_info "Mise à jour du service: $service"
        docker-compose -f "$compose_file" up -d --no-deps "$service"
        
        # Attendre que le service soit prêt
        sleep 10
        
        # Vérifier la santé du service
        local attempt=1
        local max_attempts=12
        
        while [[ $attempt -le $max_attempts ]]; do
            if docker-compose -f "$compose_file" ps "$service" | grep -q "Up"; then
                log_success "Service $service mis à jour et opérationnel"
                break
            fi
            
            if [[ $attempt -eq $max_attempts ]]; then
                log_error "Échec de la mise à jour du service $service"
                log_warning "Rollback depuis la sauvegarde: $backup_file"
                restore_data "$backup_file"
                return 1
            fi
            
            sleep 5
            attempt=$((attempt + 1))
        done
    done
    
    log_success "Mise à jour terminée avec succès"
}

# =============================================
# 🧹 Nettoyage du système
# =============================================
clean_system() {
    log_step "Nettoyage du système Docker..."
    
    # Confirmation avant nettoyage
    echo -e "${YELLOW}${WARNING}${NC} Cette action va supprimer:"
    echo "  - Images Docker inutilisées"
    echo "  - Volumes anonymes"
    echo "  - Réseaux inutilisés"
    echo "  - Cache de build"
    echo ""
    read -p "Continuer? (y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Nettoyage annulé"
        return 0
    fi
    
    # Nettoyage progressif
    log_info "Suppression des images inutilisées..."
    docker image prune -f
    
    log_info "Suppression des volumes anonymes..."
    docker volume prune -f
    
    log_info "Suppression des réseaux inutilisés..."
    docker network prune -f
    
    log_info "Suppression du cache de build..."
    docker builder prune -f
    
    # Statistiques d'espace libéré
    log_success "Nettoyage terminé"
    docker system df
}

# =============================================
# 📋 Affichage du statut
# =============================================
show_status() {
    local compose_file=$1
    
    echo -e "${CYAN}📊 Statut des Services${NC}"
    echo "════════════════════════"
    
    # Statut des conteneurs
    docker-compose -f "$compose_file" ps
    
    echo -e "\n${CYAN}💾 Utilisation du disque${NC}"
    echo "═══════════════════════"
    docker system df
    
    echo -e "\n${CYAN}🌐 URLs d'accès${NC}"
    echo "═══════════════"
    echo "Frontend:  http://localhost:3000"
    echo "Backend:   http://localhost:5000"
    echo "Rasa API:  http://localhost:5005"
    
    if [[ "$ENVIRONMENT" == "dev" ]]; then
        echo "MongoDB:   mongodb://localhost:27017"
        echo "Redis:     redis://localhost:6379"
        echo "Adminer:   http://localhost:8080"
        echo "Redis UI:  http://localhost:8081"
    fi
}

# =============================================
# 🚀 Fonction principale
# =============================================
main() {
    print_banner
    
    # Gestion des arguments
    local command=${1:-"help"}
    ENVIRONMENT=""
    
    case $command in
        "dev")
            ENVIRONMENT="dev"
            COMPOSE_FILE="$COMPOSE_FILE_DEV"
            ;;
        "prod")
            ENVIRONMENT="prod"
            COMPOSE_FILE="$COMPOSE_FILE_PROD"
            ;;
        "backup"|"restore"|"logs"|"update"|"clean"|"status")
            # Ces commandes nécessitent de détecter l'environnement
            if [[ -f ".env" ]] && grep -q "NODE_ENV=production" ".env"; then
                ENVIRONMENT="prod"
                COMPOSE_FILE="$COMPOSE_FILE_PROD"
            else
                ENVIRONMENT="dev"
                COMPOSE_FILE="$COMPOSE_FILE_DEV"
            fi
            ;;
        "help"|*)
            show_help
            exit 0
            ;;
    esac
    
    # Exécution des commandes
    case $command in
        "dev"|"prod")
            log_info "Mode: $ENVIRONMENT"
            check_prerequisites
            setup_environment || exit 1
            create_directories
            build_images "$COMPOSE_FILE"
            start_services "$COMPOSE_FILE"
            health_checks "$COMPOSE_FILE"
            show_status "$COMPOSE_FILE"
            ;;
        "backup")
            backup_data
            ;;
        "restore")
            if [[ -z "$2" ]]; then
                log_error "Usage: $0 restore <fichier-sauvegarde>"
                exit 1
            fi
            restore_data "$2"
            ;;
        "logs")
            show_logs "$2" "$COMPOSE_FILE"
            ;;
        "update")
            update_services "$COMPOSE_FILE"
            ;;
        "clean")
            clean_system
            ;;
        "status")
            show_status "$COMPOSE_FILE"
            ;;
    esac
}

# =============================================
# 📖 Aide
# =============================================
show_help() {
    echo -e "${WHITE}🚀 Script de Déploiement Docker - Chatbot Free Mobile${NC}"
    echo ""
    echo -e "${YELLOW}USAGE:${NC}"
    echo "  $0 <commande> [options]"
    echo ""
    echo -e "${YELLOW}COMMANDES:${NC}"
    echo -e "  ${GREEN}dev${NC}              Démarrer l'environnement de développement"
    echo -e "  ${GREEN}prod${NC}             Démarrer l'environnement de production"
    echo -e "  ${GREEN}backup${NC}           Créer une sauvegarde des données"
    echo -e "  ${GREEN}restore${NC} <file>   Restaurer depuis une sauvegarde"
    echo -e "  ${GREEN}logs${NC} [service]   Afficher les logs (service optionnel)"
    echo -e "  ${GREEN}update${NC}           Mettre à jour les services sans interruption"
    echo -e "  ${GREEN}clean${NC}            Nettoyer le système Docker"
    echo -e "  ${GREEN}status${NC}           Afficher le statut des services"
    echo -e "  ${GREEN}help${NC}             Afficher cette aide"
    echo ""
    echo -e "${YELLOW}EXEMPLES:${NC}"
    echo "  $0 dev                    # Démarrer en mode développement"
    echo "  $0 prod                   # Démarrer en mode production"
    echo "  $0 backup                 # Créer une sauvegarde"
    echo "  $0 logs backend           # Voir les logs du backend"
    echo "  $0 update                 # Mettre à jour tous les services"
    echo ""
    echo -e "${YELLOW}NOTES:${NC}"
    echo "  - Assurez-vous que Docker est démarré"
    echo "  - Configurez le fichier .env avant le premier lancement"
    echo "  - Les sauvegardes sont stockées dans ./backups/"
    echo "  - Les logs sont dans ./logs/"
}

# Gestion des signaux
trap 'log_error "Script interrompu"; exit 1' INT TERM

# Exécution du script principal
main "$@" 