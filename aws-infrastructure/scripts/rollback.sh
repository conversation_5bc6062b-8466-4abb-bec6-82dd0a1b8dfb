#!/bin/bash
# AWS Migration Rollback Script
# Emergency rollback procedures for Free Mobile Chatbot RNCP

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="freemobile-chatbot"
AWS_REGION="eu-west-3"
TERRAFORM_DIR="$(dirname "$0")/../terraform"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

confirm_rollback() {
    echo
    log_warning "🚨 EMERGENCY ROLLBACK PROCEDURE 🚨"
    echo "This will destroy the AWS infrastructure and attempt to restore the previous state."
    echo "This action is IRREVERSIBLE and will result in:"
    echo "  - Termination of all EC2 instances"
    echo "  - Deletion of RDS database (unless protected)"
    echo "  - Removal of ElastiCache cluster"
    echo "  - Destruction of Load Balancer and networking"
    echo "  - Loss of all data not backed up"
    echo
    
    read -p "Are you absolutely sure you want to proceed? Type 'ROLLBACK' to confirm: " -r
    if [ "$REPLY" != "ROLLBACK" ]; then
        log_info "Rollback cancelled by user"
        exit 0
    fi
    
    echo
    read -p "This is your final warning. Type 'DESTROY' to proceed: " -r
    if [ "$REPLY" != "DESTROY" ]; then
        log_info "Rollback cancelled by user"
        exit 0
    fi
    
    log_warning "Proceeding with rollback in 10 seconds... Press Ctrl+C to cancel"
    sleep 10
}

backup_current_state() {
    log_info "Creating backup of current state..."
    
    BACKUP_DIR="/tmp/freemobile-chatbot-rollback-$(date +%Y%m%d-%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    cd "$TERRAFORM_DIR"
    
    # Backup Terraform state
    if [ -f "terraform.tfstate" ]; then
        cp terraform.tfstate "$BACKUP_DIR/"
        log_success "Terraform state backed up"
    fi
    
    # Backup Terraform outputs
    if terraform output -json > "$BACKUP_DIR/terraform-outputs.json" 2>/dev/null; then
        log_success "Terraform outputs backed up"
    fi
    
    # Create infrastructure inventory
    cat > "$BACKUP_DIR/infrastructure-inventory.txt" << EOF
# Infrastructure Inventory - $(date)
# Generated before rollback

VPC_ID=$(terraform output -raw vpc_id 2>/dev/null || echo "N/A")
ALB_DNS=$(terraform output -raw load_balancer_dns 2>/dev/null || echo "N/A")
DB_ENDPOINT=$(terraform output -raw database_endpoint 2>/dev/null || echo "N/A")
REDIS_ENDPOINT=$(terraform output -raw redis_primary_endpoint 2>/dev/null || echo "N/A")
ASG_NAME=$(terraform output -raw autoscaling_group_name 2>/dev/null || echo "N/A")
EOF
    
    log_success "Infrastructure inventory created at $BACKUP_DIR"
    echo "Backup location: $BACKUP_DIR"
}

create_database_snapshot() {
    log_info "Creating database snapshot before rollback..."
    
    DB_IDENTIFIER="${PROJECT_NAME}-db"
    SNAPSHOT_ID="${PROJECT_NAME}-rollback-snapshot-$(date +%Y%m%d-%H%M%S)"
    
    # Check if database exists
    if aws rds describe-db-instances --db-instance-identifier "$DB_IDENTIFIER" --region "$AWS_REGION" > /dev/null 2>&1; then
        log_info "Creating RDS snapshot: $SNAPSHOT_ID"
        
        aws rds create-db-snapshot \
            --db-instance-identifier "$DB_IDENTIFIER" \
            --db-snapshot-identifier "$SNAPSHOT_ID" \
            --region "$AWS_REGION" \
            --output text > /dev/null
        
        log_info "Waiting for snapshot to complete (this may take several minutes)..."
        aws rds wait db-snapshot-completed \
            --db-snapshot-identifier "$SNAPSHOT_ID" \
            --region "$AWS_REGION"
        
        log_success "Database snapshot created: $SNAPSHOT_ID"
        echo "Snapshot ARN: arn:aws:rds:$AWS_REGION:$(aws sts get-caller-identity --query Account --output text):snapshot:$SNAPSHOT_ID"
    else
        log_warning "Database not found, skipping snapshot creation"
    fi
}

scale_down_instances() {
    log_info "Scaling down Auto Scaling Group..."
    
    ASG_NAME=$(terraform output -raw autoscaling_group_name 2>/dev/null || echo "")
    
    if [ -n "$ASG_NAME" ]; then
        # Get current ASG configuration
        CURRENT_CONFIG=$(aws autoscaling describe-auto-scaling-groups \
            --auto-scaling-group-names "$ASG_NAME" \
            --region "$AWS_REGION" \
            --query 'AutoScalingGroups[0].[MinSize,MaxSize,DesiredCapacity]' \
            --output text 2>/dev/null || echo "")
        
        if [ -n "$CURRENT_CONFIG" ]; then
            log_info "Current ASG configuration: $CURRENT_CONFIG"
            
            # Scale down to 0
            aws autoscaling update-auto-scaling-group \
                --auto-scaling-group-name "$ASG_NAME" \
                --min-size 0 \
                --max-size 0 \
                --desired-capacity 0 \
                --region "$AWS_REGION" \
                --output text > /dev/null
            
            log_info "Waiting for instances to terminate..."
            
            # Wait for instances to terminate
            while true; do
                INSTANCE_COUNT=$(aws autoscaling describe-auto-scaling-groups \
                    --auto-scaling-group-names "$ASG_NAME" \
                    --region "$AWS_REGION" \
                    --query 'AutoScalingGroups[0].Instances | length(@)' \
                    --output text 2>/dev/null || echo "0")
                
                if [ "$INSTANCE_COUNT" -eq 0 ]; then
                    break
                fi
                
                log_info "Waiting for $INSTANCE_COUNT instances to terminate..."
                sleep 30
            done
            
            log_success "All instances terminated"
        else
            log_warning "Could not retrieve ASG configuration"
        fi
    else
        log_warning "Auto Scaling Group name not found"
    fi
}

remove_dns_records() {
    log_info "Removing DNS records (if configured)..."
    
    # This is a placeholder - actual implementation would depend on your DNS setup
    # For Route 53, you would need to identify and remove the records pointing to the ALB
    
    log_warning "DNS record removal not implemented - please manually update DNS records"
    echo "If you have DNS records pointing to the load balancer, please update them to point to your previous infrastructure"
}

disable_alarms() {
    log_info "Disabling CloudWatch alarms..."
    
    # Get all alarms for the project
    ALARMS=$(aws cloudwatch describe-alarms \
        --alarm-name-prefix "$PROJECT_NAME" \
        --region "$AWS_REGION" \
        --query 'MetricAlarms[].AlarmName' \
        --output text 2>/dev/null || echo "")
    
    if [ -n "$ALARMS" ]; then
        for alarm in $ALARMS; do
            aws cloudwatch disable-alarm-actions \
                --alarm-names "$alarm" \
                --region "$AWS_REGION" \
                --output text > /dev/null 2>&1 || true
            log_info "Disabled alarm: $alarm"
        done
        log_success "All alarms disabled"
    else
        log_warning "No alarms found to disable"
    fi
}

terraform_destroy() {
    log_info "Destroying infrastructure with Terraform..."
    
    cd "$TERRAFORM_DIR"
    
    # Remove deletion protection from RDS if enabled
    DB_IDENTIFIER="${PROJECT_NAME}-db"
    if aws rds describe-db-instances --db-instance-identifier "$DB_IDENTIFIER" --region "$AWS_REGION" > /dev/null 2>&1; then
        DELETION_PROTECTION=$(aws rds describe-db-instances \
            --db-instance-identifier "$DB_IDENTIFIER" \
            --region "$AWS_REGION" \
            --query 'DBInstances[0].DeletionProtection' \
            --output text 2>/dev/null || echo "false")
        
        if [ "$DELETION_PROTECTION" = "true" ]; then
            log_info "Removing deletion protection from RDS instance..."
            aws rds modify-db-instance \
                --db-instance-identifier "$DB_IDENTIFIER" \
                --no-deletion-protection \
                --apply-immediately \
                --region "$AWS_REGION" \
                --output text > /dev/null
            
            log_info "Waiting for modification to complete..."
            aws rds wait db-instance-available \
                --db-instance-identifier "$DB_IDENTIFIER" \
                --region "$AWS_REGION"
        fi
    fi
    
    # Remove deletion protection from ALB if enabled
    ALB_ARN=$(terraform output -raw load_balancer_arn 2>/dev/null || echo "")
    if [ -n "$ALB_ARN" ]; then
        aws elbv2 modify-load-balancer-attributes \
            --load-balancer-arn "$ALB_ARN" \
            --attributes Key=deletion_protection.enabled,Value=false \
            --region "$AWS_REGION" \
            --output text > /dev/null 2>&1 || true
    fi
    
    # Plan destroy
    log_info "Planning Terraform destroy..."
    terraform plan -destroy \
        -var="aws_region=$AWS_REGION" \
        -var="environment=production" \
        -var="project_name=$PROJECT_NAME" \
        -out=destroy.tfplan
    
    # Execute destroy
    log_warning "Executing Terraform destroy..."
    terraform apply destroy.tfplan
    
    log_success "Infrastructure destroyed"
}

cleanup_remaining_resources() {
    log_info "Cleaning up any remaining resources..."
    
    # Clean up S3 buckets (Terraform may not delete non-empty buckets)
    BUCKETS=$(aws s3api list-buckets --query "Buckets[?contains(Name, '$PROJECT_NAME')].Name" --output text 2>/dev/null || echo "")
    
    if [ -n "$BUCKETS" ]; then
        for bucket in $BUCKETS; do
            log_info "Cleaning up S3 bucket: $bucket"
            
            # Delete all objects
            aws s3 rm "s3://$bucket" --recursive --output text > /dev/null 2>&1 || true
            
            # Delete all versions
            aws s3api delete-objects \
                --bucket "$bucket" \
                --delete "$(aws s3api list-object-versions \
                    --bucket "$bucket" \
                    --output json \
                    --query '{Objects: Versions[].{Key:Key,VersionId:VersionId}}')" \
                --output text > /dev/null 2>&1 || true
            
            # Delete bucket
            aws s3api delete-bucket --bucket "$bucket" --output text > /dev/null 2>&1 || true
            
            log_info "Bucket $bucket cleaned up"
        done
    fi
    
    # Clean up CloudWatch log groups
    LOG_GROUPS=$(aws logs describe-log-groups \
        --log-group-name-prefix "/aws/ec2/$PROJECT_NAME" \
        --region "$AWS_REGION" \
        --query 'logGroups[].logGroupName' \
        --output text 2>/dev/null || echo "")
    
    if [ -n "$LOG_GROUPS" ]; then
        for log_group in $LOG_GROUPS; do
            aws logs delete-log-group \
                --log-group-name "$log_group" \
                --region "$AWS_REGION" \
                --output text > /dev/null 2>&1 || true
            log_info "Deleted log group: $log_group"
        done
    fi
    
    log_success "Cleanup completed"
}

verify_rollback() {
    log_info "Verifying rollback completion..."
    
    # Check if major resources are gone
    RESOURCES_REMAINING=0
    
    # Check VPC
    VPC_ID=$(terraform output -raw vpc_id 2>/dev/null || echo "")
    if [ -n "$VPC_ID" ]; then
        if aws ec2 describe-vpcs --vpc-ids "$VPC_ID" --region "$AWS_REGION" > /dev/null 2>&1; then
            log_warning "VPC still exists: $VPC_ID"
            ((RESOURCES_REMAINING++))
        fi
    fi
    
    # Check RDS
    DB_IDENTIFIER="${PROJECT_NAME}-db"
    if aws rds describe-db-instances --db-instance-identifier "$DB_IDENTIFIER" --region "$AWS_REGION" > /dev/null 2>&1; then
        log_warning "RDS instance still exists: $DB_IDENTIFIER"
        ((RESOURCES_REMAINING++))
    fi
    
    # Check ElastiCache
    REDIS_ID="${PROJECT_NAME}-redis"
    if aws elasticache describe-replication-groups --replication-group-id "$REDIS_ID" --region "$AWS_REGION" > /dev/null 2>&1; then
        log_warning "ElastiCache cluster still exists: $REDIS_ID"
        ((RESOURCES_REMAINING++))
    fi
    
    if [ $RESOURCES_REMAINING -eq 0 ]; then
        log_success "Rollback verification passed - all major resources removed"
    else
        log_warning "Rollback verification found $RESOURCES_REMAINING remaining resources"
        log_warning "Some resources may take additional time to be fully deleted"
    fi
}

generate_rollback_report() {
    echo
    echo "=========================================="
    echo "AWS Migration Rollback Report"
    echo "=========================================="
    echo "Date: $(date)"
    echo "Project: $PROJECT_NAME"
    echo "Region: $AWS_REGION"
    echo
    
    echo "Rollback Actions Completed:"
    echo "✅ Infrastructure backup created"
    echo "✅ Database snapshot created (if applicable)"
    echo "✅ Auto Scaling Group scaled down"
    echo "✅ CloudWatch alarms disabled"
    echo "✅ Terraform infrastructure destroyed"
    echo "✅ Remaining resources cleaned up"
    echo "✅ Rollback verification completed"
    echo
    
    echo "Important Notes:"
    echo "🔄 Database snapshots are retained for recovery"
    echo "📧 Update DNS records to point to previous infrastructure"
    echo "🔐 Rotate any credentials that may have been compromised"
    echo "📊 Review CloudWatch logs for any issues during rollback"
    echo
    
    echo "Next Steps:"
    echo "1. Verify that your previous infrastructure is operational"
    echo "2. Update DNS records if necessary"
    echo "3. Monitor application performance and availability"
    echo "4. Review what went wrong and plan improvements"
    echo "5. Clean up any remaining AWS resources manually if needed"
    echo
    
    log_success "Rollback completed successfully"
}

# Main execution
main() {
    log_info "Starting AWS Migration Rollback"
    echo "=================================="
    
    confirm_rollback
    backup_current_state
    create_database_snapshot
    remove_dns_records
    disable_alarms
    scale_down_instances
    terraform_destroy
    cleanup_remaining_resources
    verify_rollback
    generate_rollback_report
    
    log_success "Emergency rollback procedure completed"
}

# Handle script interruption
trap 'log_error "Rollback interrupted. Please check the state and continue manually if necessary."; exit 1' INT TERM

# Run main function
main "$@"
