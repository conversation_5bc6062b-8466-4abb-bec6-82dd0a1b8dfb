#!/bin/bash
# AWS Migration Deployment Script
# Free Mobile Chatbot RNCP - Complete AWS Infrastructure Deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="freemobile-chatbot"
AWS_REGION="eu-west-3"
ENVIRONMENT="production"
TERRAFORM_DIR="$(dirname "$0")/../terraform"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if AWS CLI is installed
    if ! command -v aws &> /dev/null; then
        log_error "AWS CLI is not installed. Please install it first."
        exit 1
    fi
    
    # Check if Terraform is installed
    if ! command -v terraform &> /dev/null; then
        log_error "Terraform is not installed. Please install it first."
        exit 1
    fi
    
    # Check if jq is installed
    if ! command -v jq &> /dev/null; then
        log_error "jq is not installed. Please install it first."
        exit 1
    fi
    
    # Check AWS credentials
    if ! aws sts get-caller-identity &> /dev/null; then
        log_error "AWS credentials are not configured. Please run 'aws configure'."
        exit 1
    fi
    
    log_success "All prerequisites met"
}

create_ssh_key() {
    log_info "Creating SSH key pair..."
    
    SSH_KEY_PATH="$HOME/.ssh/freemobile_chatbot_rsa"
    
    if [ ! -f "$SSH_KEY_PATH" ]; then
        ssh-keygen -t rsa -b 4096 -f "$SSH_KEY_PATH" -N "" -C "freemobile-chatbot-deployment"
        log_success "SSH key pair created at $SSH_KEY_PATH"
    else
        log_warning "SSH key pair already exists at $SSH_KEY_PATH"
    fi
}

setup_terraform_backend() {
    log_info "Setting up Terraform backend..."
    
    BUCKET_NAME="${PROJECT_NAME}-terraform-state-$(date +%s)"
    
    # Create S3 bucket for Terraform state
    aws s3api create-bucket \
        --bucket "$BUCKET_NAME" \
        --region "$AWS_REGION" \
        --create-bucket-configuration LocationConstraint="$AWS_REGION" \
        --output text > /dev/null
    
    # Enable versioning
    aws s3api put-bucket-versioning \
        --bucket "$BUCKET_NAME" \
        --versioning-configuration Status=Enabled \
        --output text > /dev/null
    
    # Enable encryption
    aws s3api put-bucket-encryption \
        --bucket "$BUCKET_NAME" \
        --server-side-encryption-configuration '{
            "Rules": [{
                "ApplyServerSideEncryptionByDefault": {
                    "SSEAlgorithm": "AES256"
                }
            }]
        }' \
        --output text > /dev/null
    
    # Create DynamoDB table for state locking
    aws dynamodb create-table \
        --table-name "${PROJECT_NAME}-terraform-locks" \
        --attribute-definitions AttributeName=LockID,AttributeType=S \
        --key-schema AttributeName=LockID,KeyType=HASH \
        --provisioned-throughput ReadCapacityUnits=5,WriteCapacityUnits=5 \
        --region "$AWS_REGION" \
        --output text > /dev/null
    
    # Update Terraform backend configuration
    cat > "$TERRAFORM_DIR/backend.tf" << EOF
terraform {
  backend "s3" {
    bucket         = "$BUCKET_NAME"
    key            = "infrastructure/terraform.tfstate"
    region         = "$AWS_REGION"
    dynamodb_table = "${PROJECT_NAME}-terraform-locks"
    encrypt        = true
  }
}
EOF
    
    log_success "Terraform backend configured with bucket: $BUCKET_NAME"
}

rotate_credentials() {
    log_warning "SECURITY ALERT: Rotating compromised credentials..."
    
    # Get current user
    CURRENT_USER=$(aws sts get-caller-identity --query 'Arn' --output text | cut -d'/' -f2)
    
    # Create new access key
    NEW_KEYS=$(aws iam create-access-key --user-name "$CURRENT_USER" --output json)
    NEW_ACCESS_KEY=$(echo "$NEW_KEYS" | jq -r '.AccessKey.AccessKeyId')
    NEW_SECRET_KEY=$(echo "$NEW_KEYS" | jq -r '.AccessKey.SecretAccessKey')
    
    log_info "New credentials created. Please update your AWS configuration:"
    echo "AWS Access Key ID: $NEW_ACCESS_KEY"
    echo "AWS Secret Access Key: $NEW_SECRET_KEY"
    
    read -p "Have you updated your AWS credentials? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_error "Please update your AWS credentials and run the script again."
        exit 1
    fi
    
    # Delete old access key (this should be done after confirming new keys work)
    log_warning "Remember to delete the old access key after confirming the new one works"
}

deploy_infrastructure() {
    log_info "Deploying AWS infrastructure with Terraform..."
    
    cd "$TERRAFORM_DIR"
    
    # Initialize Terraform
    log_info "Initializing Terraform..."
    terraform init
    
    # Plan deployment
    log_info "Planning Terraform deployment..."
    terraform plan \
        -var="aws_region=$AWS_REGION" \
        -var="environment=$ENVIRONMENT" \
        -var="project_name=$PROJECT_NAME" \
        -out=tfplan
    
    # Ask for confirmation
    echo
    read -p "Do you want to proceed with the deployment? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_warning "Deployment cancelled by user"
        exit 0
    fi
    
    # Apply deployment
    log_info "Applying Terraform deployment..."
    terraform apply tfplan
    
    # Save outputs
    terraform output -json > terraform-outputs.json
    
    log_success "Infrastructure deployment completed"
}

wait_for_services() {
    log_info "Waiting for services to be ready..."
    
    # Get RDS endpoint
    RDS_ENDPOINT=$(terraform output -raw database_endpoint)
    
    # Wait for RDS to be available
    log_info "Waiting for RDS database to be available..."
    aws rds wait db-instance-available --db-instance-identifier "$PROJECT_NAME-db" --region "$AWS_REGION"
    
    # Wait for ElastiCache to be available
    log_info "Waiting for ElastiCache to be available..."
    while true; do
        STATUS=$(aws elasticache describe-replication-groups \
            --replication-group-id "$PROJECT_NAME-redis" \
            --region "$AWS_REGION" \
            --query 'ReplicationGroups[0].Status' \
            --output text)
        
        if [ "$STATUS" = "available" ]; then
            break
        fi
        
        log_info "ElastiCache status: $STATUS. Waiting..."
        sleep 30
    done
    
    log_success "All services are ready"
}

run_database_migrations() {
    log_info "Running database migrations..."
    
    # Get database connection details from Terraform outputs
    DB_ENDPOINT=$(terraform output -raw database_endpoint)
    DB_SECRET_ARN=$(terraform output -raw database_secret_arn)
    
    # Get database credentials from Secrets Manager
    DB_CREDENTIALS=$(aws secretsmanager get-secret-value \
        --secret-id "$DB_SECRET_ARN" \
        --region "$AWS_REGION" \
        --query 'SecretString' \
        --output text)
    
    DB_USER=$(echo "$DB_CREDENTIALS" | jq -r '.username')
    DB_PASSWORD=$(echo "$DB_CREDENTIALS" | jq -r '.password')
    DB_NAME=$(echo "$DB_CREDENTIALS" | jq -r '.dbname')
    
    # Create migration script
    cat > /tmp/migrate.sql << EOF
-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL DEFAULT 'user',
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create sessions table
CREATE TABLE IF NOT EXISTS user_sessions (
    session_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    jwt_token TEXT NOT NULL,
    refresh_token TEXT,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_accessed TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create conversations table
CREATE TABLE IF NOT EXISTS conversations (
    conversation_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    agent_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    status VARCHAR(50) DEFAULT 'active',
    urgency_level VARCHAR(20) DEFAULT 'medium',
    frustration_score DECIMAL(3,2) DEFAULT 0.0,
    complexity_score DECIMAL(3,2) DEFAULT 0.0,
    ai_analysis JSONB,
    messages JSONB,
    escalated_at TIMESTAMP,
    resolved_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create call logs table
CREATE TABLE IF NOT EXISTS call_logs (
    call_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID REFERENCES conversations(conversation_id),
    user_id INTEGER REFERENCES users(id),
    agent_id INTEGER REFERENCES users(id),
    call_type VARCHAR(50) NOT NULL,
    duration_seconds INTEGER,
    resolution_status VARCHAR(50),
    satisfaction_score INTEGER CHECK (satisfaction_score >= 1 AND satisfaction_score <= 5),
    ai_suggestions JSONB,
    call_metadata JSONB,
    started_at TIMESTAMP NOT NULL,
    ended_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create AI suggestions cache table
CREATE TABLE IF NOT EXISTS ai_suggestions_cache (
    cache_key VARCHAR(255) PRIMARY KEY,
    suggestion_data JSONB NOT NULL,
    confidence_score DECIMAL(3,2),
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_sessions_expires_at ON user_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_conversations_user_id ON conversations(user_id);
CREATE INDEX IF NOT EXISTS idx_conversations_status ON conversations(status);
CREATE INDEX IF NOT EXISTS idx_conversations_created_at ON conversations(created_at);
CREATE INDEX IF NOT EXISTS idx_call_logs_user_id ON call_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_call_logs_started_at ON call_logs(started_at);
CREATE INDEX IF NOT EXISTS idx_ai_cache_expires_at ON ai_suggestions_cache(expires_at);
EOF
    
    # Run migrations
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_ENDPOINT" -U "$DB_USER" -d "$DB_NAME" -f /tmp/migrate.sql
    
    # Clean up
    rm /tmp/migrate.sql
    
    log_success "Database migrations completed"
}

validate_deployment() {
    log_info "Validating deployment..."
    
    # Get ALB DNS name
    ALB_DNS=$(terraform output -raw load_balancer_dns)
    
    # Test ALB health
    log_info "Testing load balancer health..."
    if curl -f "http://$ALB_DNS/health" > /dev/null 2>&1; then
        log_success "Load balancer is responding"
    else
        log_warning "Load balancer health check failed (this is expected initially)"
    fi
    
    # Check Auto Scaling Group
    ASG_NAME=$(terraform output -raw autoscaling_group_name)
    INSTANCE_COUNT=$(aws autoscaling describe-auto-scaling-groups \
        --auto-scaling-group-names "$ASG_NAME" \
        --region "$AWS_REGION" \
        --query 'AutoScalingGroups[0].Instances | length(@)' \
        --output text)
    
    log_info "Auto Scaling Group has $INSTANCE_COUNT instances"
    
    # Check RDS status
    RDS_STATUS=$(aws rds describe-db-instances \
        --db-instance-identifier "$PROJECT_NAME-db" \
        --region "$AWS_REGION" \
        --query 'DBInstances[0].DBInstanceStatus' \
        --output text)
    
    log_info "RDS status: $RDS_STATUS"
    
    # Check ElastiCache status
    REDIS_STATUS=$(aws elasticache describe-replication-groups \
        --replication-group-id "$PROJECT_NAME-redis" \
        --region "$AWS_REGION" \
        --query 'ReplicationGroups[0].Status' \
        --output text)
    
    log_info "ElastiCache status: $REDIS_STATUS"
    
    log_success "Deployment validation completed"
}

print_summary() {
    log_success "AWS Migration Deployment Summary"
    echo "=================================="
    
    cd "$TERRAFORM_DIR"
    
    echo "🌐 Load Balancer DNS: $(terraform output -raw load_balancer_dns)"
    echo "🔒 SSL Certificate: $(terraform output -raw ssl_certificate_arn)"
    echo "🗄️  Database Endpoint: $(terraform output -raw database_endpoint)"
    echo "⚡ Redis Endpoint: $(terraform output -raw redis_primary_endpoint)"
    echo "📊 CloudWatch Dashboard: $(terraform output -raw cloudwatch_dashboard_url)"
    echo "🛡️  WAF Web ACL: $(terraform output -raw waf_web_acl_arn)"
    
    echo
    echo "Next Steps:"
    echo "1. Configure DNS to point to the load balancer"
    echo "2. Validate SSL certificate via DNS"
    echo "3. Test application functionality"
    echo "4. Set up monitoring alerts"
    echo "5. Configure backup procedures"
    
    echo
    log_warning "Important Security Notes:"
    echo "- Rotate the compromised AWS credentials immediately"
    echo "- Update all application secrets in AWS Secrets Manager"
    echo "- Review and update security group rules as needed"
    echo "- Enable AWS CloudTrail for audit logging"
}

# Main execution
main() {
    log_info "Starting AWS Migration Deployment for Free Mobile Chatbot RNCP"
    echo "=================================================================="
    
    check_prerequisites
    create_ssh_key
    
    # Ask about credential rotation
    echo
    log_warning "The configuration files contain compromised AWS credentials."
    read -p "Do you want to rotate credentials now? (recommended) (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        rotate_credentials
    fi
    
    setup_terraform_backend
    deploy_infrastructure
    wait_for_services
    run_database_migrations
    validate_deployment
    print_summary
    
    log_success "AWS Migration Deployment completed successfully!"
}

# Handle script interruption
trap 'log_error "Deployment interrupted. Please check the state and clean up if necessary."; exit 1' INT TERM

# Run main function
main "$@"
