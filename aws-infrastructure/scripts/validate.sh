#!/bin/bash
# AWS Migration Validation Script
# Comprehensive testing and validation of the deployed infrastructure

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="freemobile-chatbot"
AWS_REGION="eu-west-3"
TERRAFORM_DIR="$(dirname "$0")/../terraform"

# Test results
TESTS_PASSED=0
TESTS_FAILED=0
FAILED_TESTS=()

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

test_passed() {
    ((TESTS_PASSED++))
    log_success "$1"
}

test_failed() {
    ((TESTS_FAILED++))
    FAILED_TESTS+=("$1")
    log_error "$1"
}

get_terraform_output() {
    cd "$TERRAFORM_DIR"
    terraform output -raw "$1" 2>/dev/null || echo ""
}

test_infrastructure_components() {
    log_info "Testing infrastructure components..."
    
    # Test VPC
    VPC_ID=$(get_terraform_output "vpc_id")
    if [ -n "$VPC_ID" ]; then
        VPC_STATE=$(aws ec2 describe-vpcs --vpc-ids "$VPC_ID" --region "$AWS_REGION" --query 'Vpcs[0].State' --output text 2>/dev/null)
        if [ "$VPC_STATE" = "available" ]; then
            test_passed "VPC is available ($VPC_ID)"
        else
            test_failed "VPC is not available ($VPC_ID)"
        fi
    else
        test_failed "VPC ID not found in Terraform outputs"
    fi
    
    # Test Load Balancer
    ALB_DNS=$(get_terraform_output "load_balancer_dns")
    if [ -n "$ALB_DNS" ]; then
        ALB_STATE=$(aws elbv2 describe-load-balancers --region "$AWS_REGION" --query "LoadBalancers[?DNSName=='$ALB_DNS'].State.Code" --output text 2>/dev/null)
        if [ "$ALB_STATE" = "active" ]; then
            test_passed "Application Load Balancer is active ($ALB_DNS)"
        else
            test_failed "Application Load Balancer is not active ($ALB_DNS)"
        fi
    else
        test_failed "Load Balancer DNS not found in Terraform outputs"
    fi
    
    # Test Auto Scaling Group
    ASG_NAME=$(get_terraform_output "autoscaling_group_name")
    if [ -n "$ASG_NAME" ]; then
        INSTANCE_COUNT=$(aws autoscaling describe-auto-scaling-groups --auto-scaling-group-names "$ASG_NAME" --region "$AWS_REGION" --query 'AutoScalingGroups[0].Instances | length(@)' --output text 2>/dev/null)
        if [ "$INSTANCE_COUNT" -gt 0 ]; then
            test_passed "Auto Scaling Group has $INSTANCE_COUNT instances"
        else
            test_failed "Auto Scaling Group has no instances"
        fi
    else
        test_failed "Auto Scaling Group name not found in Terraform outputs"
    fi
}

test_database_connectivity() {
    log_info "Testing database connectivity..."
    
    DB_ENDPOINT=$(get_terraform_output "database_endpoint")
    DB_SECRET_ARN=$(get_terraform_output "database_secret_arn")
    
    if [ -n "$DB_ENDPOINT" ] && [ -n "$DB_SECRET_ARN" ]; then
        # Get database credentials
        DB_CREDENTIALS=$(aws secretsmanager get-secret-value --secret-id "$DB_SECRET_ARN" --region "$AWS_REGION" --query 'SecretString' --output text 2>/dev/null)
        
        if [ -n "$DB_CREDENTIALS" ]; then
            DB_USER=$(echo "$DB_CREDENTIALS" | jq -r '.username')
            DB_PASSWORD=$(echo "$DB_CREDENTIALS" | jq -r '.password')
            DB_NAME=$(echo "$DB_CREDENTIALS" | jq -r '.dbname')
            
            # Test database connection
            if PGPASSWORD="$DB_PASSWORD" psql -h "$DB_ENDPOINT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT version();" > /dev/null 2>&1; then
                test_passed "Database connection successful"
                
                # Test table existence
                TABLES=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_ENDPOINT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" 2>/dev/null | tr -d ' ')
                if [ "$TABLES" -gt 0 ]; then
                    test_passed "Database tables exist ($TABLES tables found)"
                else
                    test_failed "No database tables found"
                fi
            else
                test_failed "Database connection failed"
            fi
        else
            test_failed "Could not retrieve database credentials"
        fi
    else
        test_failed "Database endpoint or secret ARN not found"
    fi
}

test_redis_connectivity() {
    log_info "Testing Redis connectivity..."
    
    REDIS_ENDPOINT=$(get_terraform_output "redis_primary_endpoint")
    REDIS_SECRET_ARN=$(get_terraform_output "redis_secret_arn")
    
    if [ -n "$REDIS_ENDPOINT" ] && [ -n "$REDIS_SECRET_ARN" ]; then
        # Get Redis credentials
        REDIS_CREDENTIALS=$(aws secretsmanager get-secret-value --secret-id "$REDIS_SECRET_ARN" --region "$AWS_REGION" --query 'SecretString' --output text 2>/dev/null)
        
        if [ -n "$REDIS_CREDENTIALS" ]; then
            REDIS_AUTH_TOKEN=$(echo "$REDIS_CREDENTIALS" | jq -r '.auth_token')
            REDIS_PORT=$(echo "$REDIS_CREDENTIALS" | jq -r '.port')
            
            # Test Redis connection
            if redis-cli -h "$REDIS_ENDPOINT" -p "$REDIS_PORT" -a "$REDIS_AUTH_TOKEN" --tls ping > /dev/null 2>&1; then
                test_passed "Redis connection successful"
            else
                test_failed "Redis connection failed"
            fi
        else
            test_failed "Could not retrieve Redis credentials"
        fi
    else
        test_failed "Redis endpoint or secret ARN not found"
    fi
}

test_application_endpoints() {
    log_info "Testing application endpoints..."
    
    ALB_DNS=$(get_terraform_output "load_balancer_dns")
    
    if [ -n "$ALB_DNS" ]; then
        # Test HTTP redirect to HTTPS
        HTTP_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "http://$ALB_DNS" 2>/dev/null || echo "000")
        if [ "$HTTP_RESPONSE" = "301" ] || [ "$HTTP_RESPONSE" = "302" ]; then
            test_passed "HTTP to HTTPS redirect working ($HTTP_RESPONSE)"
        else
            test_failed "HTTP to HTTPS redirect not working (got $HTTP_RESPONSE)"
        fi
        
        # Test HTTPS endpoint (may fail due to SSL certificate validation)
        HTTPS_RESPONSE=$(curl -s -k -o /dev/null -w "%{http_code}" "https://$ALB_DNS" 2>/dev/null || echo "000")
        if [ "$HTTPS_RESPONSE" = "200" ] || [ "$HTTPS_RESPONSE" = "503" ]; then
            test_passed "HTTPS endpoint responding ($HTTPS_RESPONSE)"
        else
            test_warning "HTTPS endpoint not responding properly (got $HTTPS_RESPONSE)"
        fi
        
        # Test health endpoint
        HEALTH_RESPONSE=$(curl -s -k -o /dev/null -w "%{http_code}" "https://$ALB_DNS/health" 2>/dev/null || echo "000")
        if [ "$HEALTH_RESPONSE" = "200" ]; then
            test_passed "Health endpoint responding"
        else
            test_warning "Health endpoint not responding (got $HEALTH_RESPONSE) - instances may still be starting"
        fi
    else
        test_failed "Load Balancer DNS not available for testing"
    fi
}

test_ssl_certificate() {
    log_info "Testing SSL certificate..."
    
    SSL_CERT_ARN=$(get_terraform_output "ssl_certificate_arn")
    
    if [ -n "$SSL_CERT_ARN" ]; then
        CERT_STATUS=$(aws acm describe-certificate --certificate-arn "$SSL_CERT_ARN" --region "$AWS_REGION" --query 'Certificate.Status' --output text 2>/dev/null)
        
        if [ "$CERT_STATUS" = "ISSUED" ]; then
            test_passed "SSL certificate is issued"
        elif [ "$CERT_STATUS" = "PENDING_VALIDATION" ]; then
            test_warning "SSL certificate is pending validation"
        else
            test_failed "SSL certificate status: $CERT_STATUS"
        fi
    else
        test_failed "SSL certificate ARN not found"
    fi
}

test_security_groups() {
    log_info "Testing security groups..."
    
    # Get security group IDs
    ALB_SG_ID=$(get_terraform_output "security_group_alb_id")
    WEB_SG_ID=$(get_terraform_output "security_group_web_id")
    DB_SG_ID=$(get_terraform_output "security_group_database_id")
    CACHE_SG_ID=$(get_terraform_output "security_group_cache_id")
    
    # Test ALB security group
    if [ -n "$ALB_SG_ID" ]; then
        ALB_RULES=$(aws ec2 describe-security-groups --group-ids "$ALB_SG_ID" --region "$AWS_REGION" --query 'SecurityGroups[0].IpPermissions | length(@)' --output text 2>/dev/null)
        if [ "$ALB_RULES" -gt 0 ]; then
            test_passed "ALB security group has $ALB_RULES inbound rules"
        else
            test_failed "ALB security group has no inbound rules"
        fi
    else
        test_failed "ALB security group ID not found"
    fi
    
    # Test Web security group
    if [ -n "$WEB_SG_ID" ]; then
        WEB_RULES=$(aws ec2 describe-security-groups --group-ids "$WEB_SG_ID" --region "$AWS_REGION" --query 'SecurityGroups[0].IpPermissions | length(@)' --output text 2>/dev/null)
        if [ "$WEB_RULES" -gt 0 ]; then
            test_passed "Web security group has $WEB_RULES inbound rules"
        else
            test_failed "Web security group has no inbound rules"
        fi
    else
        test_failed "Web security group ID not found"
    fi
}

test_monitoring() {
    log_info "Testing monitoring setup..."
    
    # Test CloudWatch log groups
    LOG_GROUPS=$(aws logs describe-log-groups --log-group-name-prefix "/aws/ec2/$PROJECT_NAME" --region "$AWS_REGION" --query 'logGroups | length(@)' --output text 2>/dev/null)
    if [ "$LOG_GROUPS" -gt 0 ]; then
        test_passed "CloudWatch log groups created ($LOG_GROUPS groups)"
    else
        test_failed "No CloudWatch log groups found"
    fi
    
    # Test SNS topic
    SNS_TOPIC_ARN=$(get_terraform_output "sns_topic_arn")
    if [ -n "$SNS_TOPIC_ARN" ]; then
        TOPIC_EXISTS=$(aws sns get-topic-attributes --topic-arn "$SNS_TOPIC_ARN" --region "$AWS_REGION" --query 'Attributes.TopicArn' --output text 2>/dev/null)
        if [ -n "$TOPIC_EXISTS" ]; then
            test_passed "SNS topic exists for alerts"
        else
            test_failed "SNS topic not found"
        fi
    else
        test_failed "SNS topic ARN not found"
    fi
    
    # Test CloudWatch alarms
    ALARMS=$(aws cloudwatch describe-alarms --alarm-name-prefix "$PROJECT_NAME" --region "$AWS_REGION" --query 'MetricAlarms | length(@)' --output text 2>/dev/null)
    if [ "$ALARMS" -gt 0 ]; then
        test_passed "CloudWatch alarms configured ($ALARMS alarms)"
    else
        test_failed "No CloudWatch alarms found"
    fi
}

test_waf() {
    log_info "Testing WAF configuration..."
    
    WAF_ARN=$(get_terraform_output "waf_web_acl_arn")
    
    if [ -n "$WAF_ARN" ]; then
        WAF_STATUS=$(aws wafv2 get-web-acl --scope REGIONAL --id "${WAF_ARN##*/}" --region "$AWS_REGION" --query 'WebACL.Name' --output text 2>/dev/null)
        if [ -n "$WAF_STATUS" ]; then
            test_passed "WAF Web ACL is configured"
        else
            test_failed "WAF Web ACL not found"
        fi
    else
        test_failed "WAF Web ACL ARN not found"
    fi
}

test_secrets_manager() {
    log_info "Testing Secrets Manager..."
    
    # Test database secret
    DB_SECRET_ARN=$(get_terraform_output "database_secret_arn")
    if [ -n "$DB_SECRET_ARN" ]; then
        DB_SECRET=$(aws secretsmanager get-secret-value --secret-id "$DB_SECRET_ARN" --region "$AWS_REGION" --query 'SecretString' --output text 2>/dev/null)
        if [ -n "$DB_SECRET" ]; then
            test_passed "Database secret accessible"
        else
            test_failed "Database secret not accessible"
        fi
    else
        test_failed "Database secret ARN not found"
    fi
    
    # Test Redis secret
    REDIS_SECRET_ARN=$(get_terraform_output "redis_secret_arn")
    if [ -n "$REDIS_SECRET_ARN" ]; then
        REDIS_SECRET=$(aws secretsmanager get-secret-value --secret-id "$REDIS_SECRET_ARN" --region "$AWS_REGION" --query 'SecretString' --output text 2>/dev/null)
        if [ -n "$REDIS_SECRET" ]; then
            test_passed "Redis secret accessible"
        else
            test_failed "Redis secret not accessible"
        fi
    else
        test_failed "Redis secret ARN not found"
    fi
    
    # Test JWT secret
    JWT_SECRET_ARN=$(get_terraform_output "jwt_secret_arn")
    if [ -n "$JWT_SECRET_ARN" ]; then
        JWT_SECRET=$(aws secretsmanager get-secret-value --secret-id "$JWT_SECRET_ARN" --region "$AWS_REGION" --query 'SecretString' --output text 2>/dev/null)
        if [ -n "$JWT_SECRET" ]; then
            test_passed "JWT secret accessible"
        else
            test_failed "JWT secret not accessible"
        fi
    else
        test_failed "JWT secret ARN not found"
    fi
}

run_load_test() {
    log_info "Running basic load test..."
    
    ALB_DNS=$(get_terraform_output "load_balancer_dns")
    
    if [ -n "$ALB_DNS" ]; then
        # Simple load test with curl
        CONCURRENT_REQUESTS=10
        TOTAL_REQUESTS=100
        
        log_info "Running $TOTAL_REQUESTS requests with $CONCURRENT_REQUESTS concurrent connections..."
        
        # Create temporary script for load testing
        cat > /tmp/load_test.sh << EOF
#!/bin/bash
for i in \$(seq 1 $TOTAL_REQUESTS); do
    curl -s -k -o /dev/null -w "%{http_code}\\n" "https://$ALB_DNS/health" &
    if [ \$((\$i % $CONCURRENT_REQUESTS)) -eq 0 ]; then
        wait
    fi
done
wait
EOF
        
        chmod +x /tmp/load_test.sh
        
        # Run load test and count successful responses
        RESPONSES=$(/tmp/load_test.sh | sort | uniq -c)
        SUCCESS_COUNT=$(echo "$RESPONSES" | grep -E "(200|503)" | awk '{sum += $1} END {print sum}' || echo "0")
        
        rm /tmp/load_test.sh
        
        if [ "$SUCCESS_COUNT" -gt $((TOTAL_REQUESTS * 80 / 100)) ]; then
            test_passed "Load test passed ($SUCCESS_COUNT/$TOTAL_REQUESTS successful responses)"
        else
            test_warning "Load test had issues ($SUCCESS_COUNT/$TOTAL_REQUESTS successful responses)"
        fi
    else
        test_failed "Cannot run load test - ALB DNS not available"
    fi
}

generate_report() {
    echo
    echo "=========================================="
    echo "AWS Migration Validation Report"
    echo "=========================================="
    echo "Date: $(date)"
    echo "Project: $PROJECT_NAME"
    echo "Region: $AWS_REGION"
    echo
    
    echo "Test Results:"
    echo "✅ Tests Passed: $TESTS_PASSED"
    echo "❌ Tests Failed: $TESTS_FAILED"
    echo "📊 Success Rate: $(( TESTS_PASSED * 100 / (TESTS_PASSED + TESTS_FAILED) ))%"
    echo
    
    if [ $TESTS_FAILED -gt 0 ]; then
        echo "Failed Tests:"
        for test in "${FAILED_TESTS[@]}"; do
            echo "  - $test"
        done
        echo
    fi
    
    echo "Infrastructure Status:"
    echo "🌐 Load Balancer: $(get_terraform_output "load_balancer_dns")"
    echo "🔒 SSL Certificate: $(get_terraform_output "ssl_certificate_arn")"
    echo "🗄️  Database: $(get_terraform_output "database_endpoint")"
    echo "⚡ Redis: $(get_terraform_output "redis_primary_endpoint")"
    echo "📊 Dashboard: $(get_terraform_output "cloudwatch_dashboard_url")"
    echo
    
    if [ $TESTS_FAILED -eq 0 ]; then
        log_success "All validation tests passed! 🎉"
        echo "The AWS migration appears to be successful."
        echo "You can proceed with DNS configuration and final testing."
    else
        log_warning "Some validation tests failed."
        echo "Please review the failed tests and address any issues before proceeding."
    fi
}

# Main execution
main() {
    log_info "Starting AWS Migration Validation"
    echo "=================================="
    
    # Check if Terraform outputs are available
    if [ ! -f "$TERRAFORM_DIR/terraform-outputs.json" ]; then
        log_warning "Terraform outputs not found. Attempting to generate..."
        cd "$TERRAFORM_DIR"
        terraform output -json > terraform-outputs.json 2>/dev/null || {
            log_error "Could not generate Terraform outputs. Please ensure the infrastructure is deployed."
            exit 1
        }
    fi
    
    # Run all tests
    test_infrastructure_components
    test_database_connectivity
    test_redis_connectivity
    test_ssl_certificate
    test_security_groups
    test_monitoring
    test_waf
    test_secrets_manager
    test_application_endpoints
    run_load_test
    
    # Generate final report
    generate_report
    
    # Exit with appropriate code
    if [ $TESTS_FAILED -eq 0 ]; then
        exit 0
    else
        exit 1
    fi
}

# Run main function
main "$@"
