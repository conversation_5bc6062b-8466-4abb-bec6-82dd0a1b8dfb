#!/bin/bash
# Pre-Flight Migration Check
# Validates all prerequisites before executing AWS migration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="freemobile-chatbot"
AWS_REGION="eu-west-3"

# Test results
CHECKS_PASSED=0
CHECKS_FAILED=0
FAILED_CHECKS=()

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_passed() {
    ((CHECKS_PASSED++))
    log_success "$1"
}

check_failed() {
    ((CHECKS_FAILED++))
    FAILED_CHECKS+=("$1")
    log_error "$1"
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check AWS CLI
    if command -v aws &> /dev/null; then
        AWS_VERSION=$(aws --version 2>&1 | cut -d/ -f2 | cut -d' ' -f1)
        check_passed "AWS CLI installed (version $AWS_VERSION)"
    else
        check_failed "AWS CLI is not installed"
    fi
    
    # Check Terraform
    if command -v terraform &> /dev/null; then
        TERRAFORM_VERSION=$(terraform version | head -n1 | cut -d' ' -f2)
        check_passed "Terraform installed ($TERRAFORM_VERSION)"
    else
        check_failed "Terraform is not installed"
    fi
    
    # Check jq
    if command -v jq &> /dev/null; then
        JQ_VERSION=$(jq --version)
        check_passed "jq installed ($JQ_VERSION)"
    else
        check_failed "jq is not installed"
    fi
    
    # Check PostgreSQL client
    if command -v psql &> /dev/null; then
        PSQL_VERSION=$(psql --version | cut -d' ' -f3)
        check_passed "PostgreSQL client installed ($PSQL_VERSION)"
    else
        check_failed "PostgreSQL client (psql) is not installed"
    fi
    
    # Check Redis client
    if command -v redis-cli &> /dev/null; then
        REDIS_VERSION=$(redis-cli --version | cut -d' ' -f2)
        check_passed "Redis client installed ($REDIS_VERSION)"
    else
        check_failed "Redis client (redis-cli) is not installed"
    fi
    
    # Check curl
    if command -v curl &> /dev/null; then
        check_passed "curl is available"
    else
        check_failed "curl is not installed"
    fi
}

check_aws_credentials() {
    log_info "Checking AWS credentials..."
    
    # Check if AWS credentials are configured
    if aws sts get-caller-identity &> /dev/null; then
        ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
        USER_ARN=$(aws sts get-caller-identity --query Arn --output text)
        check_passed "AWS credentials configured (Account: $ACCOUNT_ID)"
        log_info "User ARN: $USER_ARN"
        
        # Check if using compromised credentials
        ACCESS_KEY=$(aws configure get aws_access_key_id)
        if [ "$ACCESS_KEY" = "AKIA2UTVLESWYKBOIWRF" ]; then
            check_failed "CRITICAL: Using compromised AWS credentials! Must rotate immediately!"
        else
            check_passed "Not using known compromised credentials"
        fi
        
        # Check region
        CONFIGURED_REGION=$(aws configure get region)
        if [ "$CONFIGURED_REGION" = "$AWS_REGION" ]; then
            check_passed "AWS region configured correctly ($AWS_REGION)"
        else
            log_warning "AWS region is $CONFIGURED_REGION, expected $AWS_REGION"
        fi
        
    else
        check_failed "AWS credentials are not configured or invalid"
    fi
}

check_aws_permissions() {
    log_info "Checking AWS permissions..."
    
    # Test EC2 permissions
    if aws ec2 describe-regions --region $AWS_REGION &> /dev/null; then
        check_passed "EC2 permissions available"
    else
        check_failed "EC2 permissions missing"
    fi
    
    # Test RDS permissions
    if aws rds describe-db-engine-versions --region $AWS_REGION --max-items 1 &> /dev/null; then
        check_passed "RDS permissions available"
    else
        check_failed "RDS permissions missing"
    fi
    
    # Test ElastiCache permissions
    if aws elasticache describe-cache-engine-versions --region $AWS_REGION --max-items 1 &> /dev/null; then
        check_passed "ElastiCache permissions available"
    else
        check_failed "ElastiCache permissions missing"
    fi
    
    # Test IAM permissions
    if aws iam get-user &> /dev/null; then
        check_passed "IAM permissions available"
    else
        check_failed "IAM permissions missing"
    fi
    
    # Test Secrets Manager permissions
    if aws secretsmanager list-secrets --region $AWS_REGION --max-items 1 &> /dev/null; then
        check_passed "Secrets Manager permissions available"
    else
        check_failed "Secrets Manager permissions missing"
    fi
    
    # Test CloudWatch permissions
    if aws cloudwatch list-metrics --region $AWS_REGION --max-items 1 &> /dev/null; then
        check_passed "CloudWatch permissions available"
    else
        check_failed "CloudWatch permissions missing"
    fi
}

check_terraform_files() {
    log_info "Checking Terraform configuration files..."
    
    TERRAFORM_DIR="$(dirname "$0")/../terraform"
    
    # Check if Terraform directory exists
    if [ -d "$TERRAFORM_DIR" ]; then
        check_passed "Terraform directory exists"
    else
        check_failed "Terraform directory not found"
        return
    fi
    
    # Check required Terraform files
    REQUIRED_FILES=("main.tf" "database.tf" "application.tf" "monitoring.tf" "user-data.sh")
    
    for file in "${REQUIRED_FILES[@]}"; do
        if [ -f "$TERRAFORM_DIR/$file" ]; then
            check_passed "Terraform file exists: $file"
        else
            check_failed "Terraform file missing: $file"
        fi
    done
    
    # Check Terraform syntax
    cd "$TERRAFORM_DIR"
    if terraform fmt -check &> /dev/null; then
        check_passed "Terraform files are properly formatted"
    else
        log_warning "Terraform files need formatting (run 'terraform fmt')"
    fi
    
    # Validate Terraform configuration
    if terraform validate &> /dev/null; then
        check_passed "Terraform configuration is valid"
    else
        check_failed "Terraform configuration validation failed"
    fi
}

check_ssh_key() {
    log_info "Checking SSH key configuration..."
    
    SSH_KEY_PATH="$HOME/.ssh/freemobile_chatbot_rsa"
    
    if [ -f "$SSH_KEY_PATH" ]; then
        check_passed "SSH private key exists"
        
        if [ -f "$SSH_KEY_PATH.pub" ]; then
            check_passed "SSH public key exists"
        else
            check_failed "SSH public key missing"
        fi
        
        # Check key permissions
        KEY_PERMS=$(stat -c "%a" "$SSH_KEY_PATH" 2>/dev/null || stat -f "%A" "$SSH_KEY_PATH" 2>/dev/null || echo "unknown")
        if [ "$KEY_PERMS" = "600" ]; then
            check_passed "SSH key permissions are secure (600)"
        else
            log_warning "SSH key permissions should be 600 (currently $KEY_PERMS)"
        fi
    else
        log_warning "SSH key not found - will be generated during deployment"
    fi
}

check_existing_resources() {
    log_info "Checking for existing AWS resources..."
    
    # Check for existing VPC
    EXISTING_VPCS=$(aws ec2 describe-vpcs --filters "Name=tag:Name,Values=$PROJECT_NAME-vpc" --region $AWS_REGION --query 'Vpcs | length(@)' --output text 2>/dev/null || echo "0")
    if [ "$EXISTING_VPCS" -gt 0 ]; then
        log_warning "Found $EXISTING_VPCS existing VPC(s) with project name"
    else
        check_passed "No conflicting VPCs found"
    fi
    
    # Check for existing RDS instances
    EXISTING_RDS=$(aws rds describe-db-instances --db-instance-identifier "$PROJECT_NAME-db" --region $AWS_REGION --query 'DBInstances | length(@)' --output text 2>/dev/null || echo "0")
    if [ "$EXISTING_RDS" -gt 0 ]; then
        log_warning "Found existing RDS instance with project name"
    else
        check_passed "No conflicting RDS instances found"
    fi
    
    # Check for existing ElastiCache clusters
    EXISTING_REDIS=$(aws elasticache describe-replication-groups --replication-group-id "$PROJECT_NAME-redis" --region $AWS_REGION --query 'ReplicationGroups | length(@)' --output text 2>/dev/null || echo "0")
    if [ "$EXISTING_REDIS" -gt 0 ]; then
        log_warning "Found existing ElastiCache cluster with project name"
    else
        check_passed "No conflicting ElastiCache clusters found"
    fi
}

check_domain_readiness() {
    log_info "Checking domain configuration readiness..."
    
    DOMAIN="chatbot.freemobile.fr"
    
    # Check if domain resolves
    if nslookup "$DOMAIN" &> /dev/null; then
        CURRENT_IP=$(nslookup "$DOMAIN" | grep -A1 "Name:" | tail -n1 | awk '{print $2}' || echo "unknown")
        log_warning "Domain $DOMAIN currently resolves to $CURRENT_IP"
        log_info "You will need to update DNS records after deployment"
    else
        check_passed "Domain $DOMAIN does not currently resolve (ready for configuration)"
    fi
}

check_quota_limits() {
    log_info "Checking AWS service quotas..."
    
    # Check EC2 instance limits
    EC2_LIMIT=$(aws service-quotas get-service-quota --service-code ec2 --quota-code L-1216C47A --region $AWS_REGION --query 'Quota.Value' --output text 2>/dev/null || echo "unknown")
    if [ "$EC2_LIMIT" != "unknown" ] && [ "$EC2_LIMIT" -ge 20 ]; then
        check_passed "EC2 instance limit sufficient ($EC2_LIMIT)"
    else
        log_warning "EC2 instance limit may be insufficient (current: $EC2_LIMIT, needed: 20)"
    fi
    
    # Check VPC limits
    VPC_LIMIT=$(aws service-quotas get-service-quota --service-code vpc --quota-code L-F678F1CE --region $AWS_REGION --query 'Quota.Value' --output text 2>/dev/null || echo "unknown")
    if [ "$VPC_LIMIT" != "unknown" ] && [ "$VPC_LIMIT" -ge 1 ]; then
        check_passed "VPC limit sufficient ($VPC_LIMIT)"
    else
        log_warning "VPC limit may be insufficient (current: $VPC_LIMIT, needed: 1)"
    fi
}

generate_pre_flight_report() {
    echo
    echo "=========================================="
    echo "Pre-Flight Migration Check Report"
    echo "=========================================="
    echo "Date: $(date)"
    echo "Project: $PROJECT_NAME"
    echo "Region: $AWS_REGION"
    echo
    
    echo "Check Results:"
    echo "✅ Checks Passed: $CHECKS_PASSED"
    echo "❌ Checks Failed: $CHECKS_FAILED"
    
    if [ $CHECKS_FAILED -gt 0 ]; then
        echo
        echo "Failed Checks:"
        for check in "${FAILED_CHECKS[@]}"; do
            echo "  - $check"
        done
        echo
        echo "❌ MIGRATION NOT READY"
        echo "Please resolve the failed checks before proceeding with migration."
        return 1
    else
        echo
        echo "✅ ALL CHECKS PASSED"
        echo "Migration is ready to proceed!"
        echo
        echo "Next Steps:"
        echo "1. Execute: ./scripts/deploy.sh"
        echo "2. Monitor deployment progress"
        echo "3. Run validation after completion"
        return 0
    fi
}

# Main execution
main() {
    log_info "Starting Pre-Flight Migration Check"
    echo "====================================="
    
    check_prerequisites
    check_aws_credentials
    check_aws_permissions
    check_terraform_files
    check_ssh_key
    check_existing_resources
    check_domain_readiness
    check_quota_limits
    
    generate_pre_flight_report
}

# Run main function
main "$@"
