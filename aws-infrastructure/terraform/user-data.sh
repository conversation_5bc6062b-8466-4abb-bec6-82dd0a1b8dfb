#!/bin/bash
# User Data Script for Free Mobile Chatbot EC2 Instances
# This script sets up the complete application environment

set -e

# Variables from Terraform
PROJECT_NAME="${project_name}"
AWS_REGION="${aws_region}"
ENVIRONMENT="${environment}"

# Logging
exec > >(tee /var/log/user-data.log|logger -t user-data -s 2>/dev/console) 2>&1
echo "Starting user data script execution at $(date)"

# Update system
echo "Updating system packages..."
yum update -y
yum install -y git nginx jq awscli

# Install Node.js 18
echo "Installing Node.js 18..."
curl -fsSL https://rpm.nodesource.com/setup_18.x | bash -
yum install -y nodejs

# Verify Node.js installation
node --version
npm --version

# Install PM2 globally
echo "Installing PM2..."
npm install -g pm2

# Create application user
echo "Creating application user..."
useradd -m -s /bin/bash chatbot
usermod -aG wheel chatbot

# Create application directory
echo "Setting up application directory..."
mkdir -p /opt/freemobile-chatbot
chown chatbot:chatbot /opt/freemobile-chatbot

# Install AWS CLI v2
echo "Installing AWS CLI v2..."
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
./aws/install
rm -rf aws awscliv2.zip

# Configure AWS CLI region
aws configure set region $AWS_REGION

# Clone application repository
echo "Cloning application repository..."
cd /opt/freemobile-chatbot

# Use deployment key or HTTPS (adjust as needed)
git clone https://github.com/Anderson-Archimede/ChatbotRNCP.git .
chown -R chatbot:chatbot /opt/freemobile-chatbot

# Install application dependencies
echo "Installing application dependencies..."
sudo -u chatbot npm install

# Install frontend dependencies and build
echo "Building frontend application..."
cd /opt/freemobile-chatbot/frontend
sudo -u chatbot npm install
sudo -u chatbot npm run build

# Install backend dependencies
echo "Installing backend dependencies..."
cd /opt/freemobile-chatbot/backend
sudo -u chatbot npm install

# Go back to root directory
cd /opt/freemobile-chatbot

# Retrieve secrets from AWS Secrets Manager
echo "Retrieving application secrets..."
DB_SECRET=$(aws secretsmanager get-secret-value --secret-id "$PROJECT_NAME/database/credentials" --region $AWS_REGION --query SecretString --output text)
REDIS_SECRET=$(aws secretsmanager get-secret-value --secret-id "$PROJECT_NAME/redis/auth" --region $AWS_REGION --query SecretString --output text)
JWT_SECRET=$(aws secretsmanager get-secret-value --secret-id "$PROJECT_NAME/jwt/secrets" --region $AWS_REGION --query SecretString --output text)

# Parse secrets
DB_HOST=$(echo $DB_SECRET | jq -r '.host')
DB_PORT=$(echo $DB_SECRET | jq -r '.port')
DB_NAME=$(echo $DB_SECRET | jq -r '.dbname')
DB_USER=$(echo $DB_SECRET | jq -r '.username')
DB_PASSWORD=$(echo $DB_SECRET | jq -r '.password')

REDIS_HOST=$(echo $REDIS_SECRET | jq -r '.host')
REDIS_PORT=$(echo $REDIS_SECRET | jq -r '.port')
REDIS_AUTH_TOKEN=$(echo $REDIS_SECRET | jq -r '.auth_token')

JWT_SECRET_KEY=$(echo $JWT_SECRET | jq -r '.jwt_secret')
JWT_REFRESH_SECRET_KEY=$(echo $JWT_SECRET | jq -r '.jwt_refresh_secret')
JWT_EXPIRY=$(echo $JWT_SECRET | jq -r '.jwt_expiry')

# Create production environment file
echo "Creating production environment configuration..."
cat > /opt/freemobile-chatbot/.env << EOF
# Environment
NODE_ENV=production
ENVIRONMENT=$ENVIRONMENT

# Application ports
PORT=5000
FRONTEND_PORT=3000

# Database configuration
DB_HOST=$DB_HOST
DB_PORT=$DB_PORT
DB_NAME=$DB_NAME
DB_USER=$DB_USER
DB_PASSWORD=$DB_PASSWORD
DATABASE_URL=postgresql://$DB_USER:$DB_PASSWORD@$DB_HOST:$DB_PORT/$DB_NAME

# Redis configuration
REDIS_HOST=$REDIS_HOST
REDIS_PORT=$REDIS_PORT
REDIS_AUTH_TOKEN=$REDIS_AUTH_TOKEN
REDIS_URL=redis://:$REDIS_AUTH_TOKEN@$REDIS_HOST:$REDIS_PORT

# JWT configuration
JWT_SECRET=$JWT_SECRET_KEY
JWT_REFRESH_SECRET=$JWT_REFRESH_SECRET_KEY
JWT_EXPIRY=$JWT_EXPIRY

# AWS configuration
AWS_REGION=$AWS_REGION
CLOUDWATCH_LOG_GROUP=/aws/ec2/$PROJECT_NAME/application

# CORS configuration
CORS_ORIGIN=https://chatbot.freemobile.fr
CORS_CREDENTIALS=true

# Security configuration
BCRYPT_ROUNDS=10
SESSION_SECRET=$JWT_SECRET_KEY

# Monitoring
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=true
EOF

# Secure environment file
chown chatbot:chatbot /opt/freemobile-chatbot/.env
chmod 600 /opt/freemobile-chatbot/.env

# Create PM2 ecosystem configuration
echo "Creating PM2 ecosystem configuration..."
cat > /opt/freemobile-chatbot/ecosystem.config.js << 'EOF'
module.exports = {
  apps: [
    {
      name: 'freemobile-backend',
      script: './backend/auth-server.js',
      instances: 'max',
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 5000
      },
      error_file: '/var/log/pm2/backend-error.log',
      out_file: '/var/log/pm2/backend-out.log',
      log_file: '/var/log/pm2/backend.log',
      time: true,
      max_memory_restart: '1G',
      node_args: '--max-old-space-size=1024'
    },
    {
      name: 'freemobile-frontend',
      script: 'npx',
      args: 'serve -s frontend/build -p 3000',
      instances: 1,
      env: {
        NODE_ENV: 'production'
      },
      error_file: '/var/log/pm2/frontend-error.log',
      out_file: '/var/log/pm2/frontend-out.log',
      log_file: '/var/log/pm2/frontend.log',
      time: true,
      max_memory_restart: '512M'
    }
  ]
};
EOF

chown chatbot:chatbot /opt/freemobile-chatbot/ecosystem.config.js

# Create PM2 log directory
mkdir -p /var/log/pm2
chown chatbot:chatbot /var/log/pm2

# Install serve package for frontend
echo "Installing serve package for frontend..."
sudo -u chatbot npm install -g serve

# Run database migrations (if needed)
echo "Running database migrations..."
cd /opt/freemobile-chatbot/backend
sudo -u chatbot node -e "
const { Pool } = require('pg');
const bcrypt = require('bcryptjs');

async function runMigrations() {
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: { rejectUnauthorized: false }
  });
  
  try {
    // Create users table if not exists
    await pool.query(\`
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        role VARCHAR(50) NOT NULL DEFAULT 'user',
        first_name VARCHAR(100),
        last_name VARCHAR(100),
        last_login TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    \`);
    
    // Insert default users
    const adminPassword = await bcrypt.hash('AdminPassword123!', 10);
    const agentPassword = await bcrypt.hash('AdminPassword123!', 10);
    const userPassword = await bcrypt.hash('AdminPassword123!', 10);
    
    await pool.query(\`
      INSERT INTO users (email, password_hash, role, first_name, last_name)
      VALUES 
        ('<EMAIL>', \$1, 'admin', 'Admin', 'Free Mobile'),
        ('<EMAIL>', \$2, 'agent', 'Agent', 'Support'),
        ('<EMAIL>', \$3, 'user', 'User', 'Test')
      ON CONFLICT (email) DO NOTHING
    \`, [adminPassword, agentPassword, userPassword]);
    
    console.log('✅ Database migrations completed');
  } catch (error) {
    console.error('❌ Database migration failed:', error);
  } finally {
    await pool.end();
  }
}

runMigrations();
"

# Start applications with PM2
echo "Starting applications with PM2..."
cd /opt/freemobile-chatbot
sudo -u chatbot pm2 start ecosystem.config.js
sudo -u chatbot pm2 save

# Setup PM2 startup script
sudo -u chatbot pm2 startup systemd -u chatbot --hp /home/<USER>
systemctl enable pm2-chatbot

# Configure Nginx reverse proxy
echo "Configuring Nginx..."
cat > /etc/nginx/conf.d/freemobile-chatbot.conf << 'EOF'
upstream backend {
    server 127.0.0.1:5000;
}

upstream frontend {
    server 127.0.0.1:3000;
}

server {
    listen 80;
    server_name _;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # Frontend routes
    location / {
        proxy_pass http://frontend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }
    
    # Backend API routes
    location /api/ {
        proxy_pass http://backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }
    
    # Health check endpoint
    location /health {
        proxy_pass http://backend/health;
        access_log off;
        proxy_read_timeout 10;
    }
    
    # Static assets caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF

# Test Nginx configuration
nginx -t

# Start and enable Nginx
systemctl start nginx
systemctl enable nginx

# Install and configure CloudWatch agent
echo "Installing CloudWatch agent..."
yum install -y amazon-cloudwatch-agent

# Create CloudWatch agent configuration
cat > /opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json << EOF
{
  "logs": {
    "logs_collected": {
      "files": {
        "collect_list": [
          {
            "file_path": "/var/log/pm2/backend.log",
            "log_group_name": "/aws/ec2/$PROJECT_NAME/application",
            "log_stream_name": "{instance_id}/backend"
          },
          {
            "file_path": "/var/log/pm2/frontend.log",
            "log_group_name": "/aws/ec2/$PROJECT_NAME/application",
            "log_stream_name": "{instance_id}/frontend"
          },
          {
            "file_path": "/var/log/nginx/access.log",
            "log_group_name": "/aws/ec2/$PROJECT_NAME/nginx",
            "log_stream_name": "{instance_id}/access"
          },
          {
            "file_path": "/var/log/nginx/error.log",
            "log_group_name": "/aws/ec2/$PROJECT_NAME/nginx",
            "log_stream_name": "{instance_id}/error"
          }
        ]
      }
    }
  },
  "metrics": {
    "namespace": "FreeMobileChatbot",
    "metrics_collected": {
      "cpu": {
        "measurement": ["cpu_usage_idle", "cpu_usage_iowait", "cpu_usage_user", "cpu_usage_system"],
        "metrics_collection_interval": 60
      },
      "disk": {
        "measurement": ["used_percent"],
        "metrics_collection_interval": 60,
        "resources": ["*"]
      },
      "mem": {
        "measurement": ["mem_used_percent"],
        "metrics_collection_interval": 60
      },
      "netstat": {
        "measurement": ["tcp_established", "tcp_time_wait"],
        "metrics_collection_interval": 60
      }
    }
  }
}
EOF

# Start CloudWatch agent
/opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl -a fetch-config -m ec2 -c file:/opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json -s

# Create health check script
cat > /usr/local/bin/health-check.sh << 'EOF'
#!/bin/bash
# Health check script for monitoring

# Check if PM2 processes are running
pm2_status=$(sudo -u chatbot pm2 jlist | jq -r '.[].pm2_env.status' | grep -v "online" | wc -l)
if [ $pm2_status -gt 0 ]; then
    echo "❌ PM2 processes not healthy"
    exit 1
fi

# Check if Nginx is running
if ! systemctl is-active --quiet nginx; then
    echo "❌ Nginx is not running"
    exit 1
fi

# Check application endpoints
if ! curl -f http://localhost:3000 > /dev/null 2>&1; then
    echo "❌ Frontend not responding"
    exit 1
fi

if ! curl -f http://localhost:5000/health > /dev/null 2>&1; then
    echo "❌ Backend not responding"
    exit 1
fi

echo "✅ All services healthy"
exit 0
EOF

chmod +x /usr/local/bin/health-check.sh

# Create cron job for health checks
echo "*/5 * * * * /usr/local/bin/health-check.sh >> /var/log/health-check.log 2>&1" | crontab -

# Final verification
echo "Running final verification..."
sleep 30

# Check if services are running
systemctl status nginx
sudo -u chatbot pm2 status

# Test endpoints
curl -f http://localhost:3000 || echo "Frontend not ready yet"
curl -f http://localhost:5000/health || echo "Backend not ready yet"

echo "✅ User data script completed successfully at $(date)"
echo "🚀 Free Mobile Chatbot application is now running!"
echo "📊 Frontend: http://localhost:3000"
echo "🔧 Backend: http://localhost:5000"
echo "💚 Health: http://localhost:5000/health"
