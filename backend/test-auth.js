const axios = require('axios');

async function testAuthentication() {
  try {
    console.log('🔐 Testing authentication...');
    
    const response = await axios.post('http://localhost:5000/api/auth/login', {
      email: '<EMAIL>',
      password: 'password'
    });
    
    console.log('✅ Authentication successful!');
    console.log('Response:', response.data);
    
    // Test accessing protected route
    const token = response.data.token;
    const meResponse = await axios.get('http://localhost:5000/api/auth/me', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('✅ Protected route access successful!');
    console.log('User data:', meResponse.data);
    
  } catch (error) {
    console.error('❌ Authentication failed:');
    console.error('Error:', error.response?.data || error.message);
  }
}

testAuthentication();
