# 🚀 Backend Dockerfile - Node.js Multi-stage
# Optimisé pour production avec sécurité et performance

# =============================================
# 📦 STAGE 1: Dependencies
# =============================================
FROM node:18-alpine AS dependencies

# Install security updates and required packages
RUN apk update && apk upgrade && \
    apk add --no-cache dumb-init curl && \
    rm -rf /var/cache/apk/*

# Set working directory
WORKDIR /app

# Copy package files for better layer caching
COPY package*.json ./

# Install production dependencies only
RUN npm ci --only=production --silent && \
    npm cache clean --force

# =============================================
# 🏗️ STAGE 2: Development Dependencies
# =============================================
FROM dependencies AS dev-dependencies

# Install all dependencies (including dev)
RUN npm ci --silent

# =============================================
# 🔧 STAGE 3: Build (if needed)
# =============================================
FROM dev-dependencies AS builder

# Copy source code
COPY . .

# Build application if needed (TypeScript, etc.)
# RUN npm run build

# =============================================
# 🚀 STAGE 4: Production
# =============================================
FROM node:18-alpine AS production

# Install security updates and minimal packages
RUN apk update && apk upgrade && \
    apk add --no-cache dumb-init curl && \
    rm -rf /var/cache/apk/*

# Create application directory
WORKDIR /app

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodeuser -u 1001 -G nodejs

# Copy dependencies from dependencies stage
COPY --from=dependencies --chown=nodeuser:nodejs /app/node_modules ./node_modules

# Copy application source
COPY --chown=nodeuser:nodejs . .

# Create necessary directories
RUN mkdir -p logs uploads temp && \
    chown -R nodeuser:nodejs logs uploads temp

# Set proper permissions
RUN chown -R nodeuser:nodejs /app

# Switch to non-root user
USER nodeuser

# Environment variables
ENV NODE_ENV=production
ENV PORT=5000

# Health check script
COPY --chown=nodeuser:nodejs docker-healthcheck.js ./

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD node docker-healthcheck.js || exit 1

# Expose port
EXPOSE 5000

# Labels for metadata
LABEL maintainer="Free Mobile <<EMAIL>>"
LABEL version="1.0"
LABEL description="Chatbot Free Mobile Backend - Node.js API"

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start application
CMD ["node", "server.js"] 