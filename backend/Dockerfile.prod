# Backend Production Dockerfile
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies (production only)
RUN npm ci --only=production && npm cache clean --force

# Copy source code
COPY . .

# Create uploads directory structure
RUN mkdir -p uploads/chat uploads/tickets

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S freemobile -u 1001

# Production stage
FROM node:18-alpine AS production

WORKDIR /app

# Install security updates and curl for health checks
RUN apk update && apk upgrade && \
    apk add --no-cache dumb-init curl && \
    rm -rf /var/cache/apk/*

# Copy from builder
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package*.json ./
COPY --from=builder /app/src ./src
COPY --from=builder /app/server.js ./
COPY --from=builder /app/uploads ./uploads

# Create logs directory
RUN mkdir -p logs && \
    addgroup -g 1001 -S nodejs && \
    adduser -S freemobile -u 1001 && \
    chown -R freemobile:nodejs /app

# Switch to non-root user
USER freemobile

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:5000/health || exit 1

# Expose port
EXPOSE 5000

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start application
CMD ["node", "server.js"] 