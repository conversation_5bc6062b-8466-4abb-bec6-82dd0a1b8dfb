/**
 * Simple Backend Server for ChatbotRNCP
 * Provides basic API endpoints for testing and development
 */

const express = require('express');
const cors = require('cors');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

const app = express();
const PORT = process.env.PORT || 5000;
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-here';

// Middleware
app.use(cors());
app.use(express.json());

// Mock database - In production, this would be MongoDB
const users = [
  {
    id: '1',
    email: '<EMAIL>',
    password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
    role: 'admin',
    profile: {
      firstName: 'Admin',
      lastName: 'Free Mobile'
    }
  },
  {
    id: '2',
    email: '<EMAIL>',
    password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
    role: 'user',
    profile: {
      firstName: 'User',
      lastName: 'Test'
    }
  }
];

const conversations = [];
const messages = [];

// Helper functions
const generateToken = (userId) => {
  return jwt.sign({ userId }, JWT_SECRET, { expiresIn: '7d' });
};

const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Token d\'accès requis' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Token invalide' });
    }
    req.user = users.find(u => u.id === user.userId);
    next();
  });
};

// Routes

// Health check
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    message: 'ChatbotRNCP Backend Server is running',
    timestamp: new Date().toISOString()
  });
});

// Authentication routes
app.post('/api/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({
        error: 'Email et mot de passe requis'
      });
    }

    const user = users.find(u => u.email === email);
    if (!user) {
      return res.status(401).json({
        error: 'Identifiants incorrects'
      });
    }

    // For demo purposes, accept "password" as the password
    const isValidPassword = password === 'password' || await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json({
        error: 'Identifiants incorrects'
      });
    }

    const token = generateToken(user.id);

    res.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        profile: user.profile
      },
      token,
      expiresIn: '7d'
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      error: 'Erreur serveur lors de la connexion'
    });
  }
});

app.post('/api/auth/register', async (req, res) => {
  try {
    const { email, password, firstName, lastName } = req.body;

    if (!email || !password || !firstName || !lastName) {
      return res.status(400).json({
        error: 'Tous les champs sont requis'
      });
    }

    const existingUser = users.find(u => u.email === email);
    if (existingUser) {
      return res.status(409).json({
        error: 'Un compte avec cet email existe déjà'
      });
    }

    const hashedPassword = await bcrypt.hash(password, 10);
    const newUser = {
      id: (users.length + 1).toString(),
      email,
      password: hashedPassword,
      role: 'user',
      profile: { firstName, lastName }
    };

    users.push(newUser);
    const token = generateToken(newUser.id);

    res.status(201).json({
      success: true,
      user: {
        id: newUser.id,
        email: newUser.email,
        role: newUser.role,
        profile: newUser.profile
      },
      token
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      error: 'Erreur serveur lors de la création du compte'
    });
  }
});

// Get current user
app.get('/api/auth/me', authenticateToken, (req, res) => {
  res.json({
    success: true,
    user: {
      id: req.user.id,
      email: req.user.email,
      role: req.user.role,
      profile: req.user.profile
    }
  });
});

// Chat routes
app.post('/api/chat/conversations', authenticateToken, (req, res) => {
  const conversation = {
    id: (conversations.length + 1).toString(),
    userId: req.user.id,
    sessionId: `session-${Date.now()}`,
    channel: req.body.channel || 'web',
    status: 'active',
    startedAt: new Date().toISOString()
  };

  conversations.push(conversation);

  res.json({
    success: true,
    conversationId: conversation.id,
    sessionId: conversation.sessionId,
    welcomeMessage: "Bonjour! Je suis l'assistant virtuel Free Mobile. Comment puis-je vous aider aujourd'hui?",
    channel: conversation.channel,
    startedAt: conversation.startedAt
  });
});

app.post('/api/chat/messages', authenticateToken, (req, res) => {
  const { conversationId, message } = req.body;

  if (!conversationId || !message) {
    return res.status(400).json({
      error: 'conversationId et message sont requis'
    });
  }

  const conversation = conversations.find(c => c.id === conversationId && c.userId === req.user.id);
  if (!conversation) {
    return res.status(404).json({
      error: 'Conversation non trouvée'
    });
  }

  // Save user message
  const userMessage = {
    id: (messages.length + 1).toString(),
    conversationId,
    sender: 'user',
    content: { text: message, type: 'text' },
    timestamp: new Date().toISOString()
  };
  messages.push(userMessage);

  // Generate bot response
  const botResponse = generateBotResponse(message);
  const botMessage = {
    id: (messages.length + 1).toString(),
    conversationId,
    sender: 'bot',
    content: { text: botResponse, type: 'text' },
    timestamp: new Date().toISOString()
  };
  messages.push(botMessage);

  res.json({
    success: true,
    response: {
      text: botResponse,
      type: 'text',
      mode: 'intelligent'
    },
    messageId: botMessage.id,
    timestamp: botMessage.timestamp
  });
});

// Simple bot response generator
function generateBotResponse(message) {
  const lowerMessage = message.toLowerCase();
  
  if (lowerMessage.includes('bonjour') || lowerMessage.includes('salut')) {
    return "Bonjour! Comment puis-je vous aider avec vos services Free Mobile aujourd'hui?";
  }
  
  if (lowerMessage.includes('forfait') || lowerMessage.includes('abonnement')) {
    return "Je peux vous aider avec vos questions sur les forfaits Free Mobile. Que souhaitez-vous savoir?";
  }
  
  if (lowerMessage.includes('facture') || lowerMessage.includes('paiement')) {
    return "Pour les questions de facturation, je peux vous orienter vers votre espace client ou un conseiller.";
  }
  
  if (lowerMessage.includes('problème') || lowerMessage.includes('panne')) {
    return "Je comprends que vous rencontrez un problème. Pouvez-vous me donner plus de détails?";
  }
  
  return "Je comprends votre demande. Un conseiller Free Mobile peut vous aider davantage. Souhaitez-vous que je vous mette en relation?";
}

// Dashboard routes
app.get('/api/dashboard/stats', authenticateToken, (req, res) => {
  res.json({
    success: true,
    stats: {
      totalUsers: users.length,
      activeConversations: conversations.filter(c => c.status === 'active').length,
      totalMessages: messages.length,
      avgResponseTime: 2.3
    }
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    error: 'Erreur serveur interne'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Endpoint non trouvé'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 ChatbotRNCP Backend Server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
  console.log(`🔐 Test credentials: <EMAIL> / password`);
  console.log(`📱 Ready for frontend connection!`);
});

module.exports = app;
