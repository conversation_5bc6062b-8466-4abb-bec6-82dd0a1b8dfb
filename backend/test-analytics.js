const axios = require('axios');

async function testAnalyticsDashboard() {
  try {
    console.log('🔐 Authenticating...');
    
    // First authenticate
    const authResponse = await axios.post('http://localhost:5000/api/auth/login', {
      email: '<EMAIL>',
      password: 'password'
    });
    
    const token = authResponse.data.token;
    console.log('✅ Authentication successful!');
    
    // Test analytics endpoints
    console.log('\n📊 Testing analytics endpoints...');
    
    try {
      const dashboardStats = await axios.get('http://localhost:5000/api/dashboard/stats', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      console.log('✅ Dashboard stats endpoint working:', dashboardStats.data);
    } catch (error) {
      console.log('❌ Dashboard stats error:', error.response?.data || error.message);
    }
    
    // Test if there are any analytics-specific endpoints
    try {
      const analyticsResponse = await axios.get('http://localhost:5000/api/analytics/dashboard', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      console.log('✅ Analytics dashboard endpoint working:', analyticsResponse.data);
    } catch (error) {
      console.log('ℹ️ Analytics dashboard endpoint not found (expected for frontend-only charts)');
    }
    
    console.log('\n🎯 Backend authentication and API access confirmed!');
    console.log('📱 Frontend should be able to authenticate and access protected routes.');
    
  } catch (error) {
    console.error('❌ Test failed:');
    console.error('Error:', error.response?.data || error.message);
  }
}

testAnalyticsDashboard();
