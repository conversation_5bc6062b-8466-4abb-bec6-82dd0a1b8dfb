#!/usr/bin/env node /** * Docker Health Check Script * Vérifie la santé du backend Node.js et ses dépendances */ const http = require('http'); const { MongoClient } = require('mongodb'); const redis = require('redis'); // Configuration const config = { port: process.env.PORT || 5000, mongoUri: process.env.MONGODB_URI || '************************************************************************************', redisUrl: process.env.REDIS_URL || 'redis://redis:6379', timeout: 5000, }; // Codes de sortie const EXIT_SUCCESS = 0; const EXIT_FAILURE = 1; /** * Vérifie la santé du serveur HTTP */ function checkHttpServer() { return new Promise((resolve, reject) => { const req = http.request({ host: 'localhost', port: config.port, path: '/health', method: 'GET', timeout: config.timeout, }, (res) => { if (res.statusCode === 200) { resolve('[COMPLETE] HTTP Server: Healthy'); } else { reject(new Error(`[FAILED] HTTP Server: Status ${res.statusCode}`)); } }); req.on('error', (err) => { reject(new Error(`[FAILED] HTTP Server: ${err.message}`)); }); req.on('timeout', () => { req.destroy(); reject(new Error(`[FAILED] HTTP Server: Timeout after ${config.timeout}ms`)); }); req.end(); }); } /** * Vérifie la connexion MongoDB */ async function checkMongoDB() { let client; try { client = new MongoClient(config.mongoUri, { serverSelectionTimeoutMS: config.timeout, connectTimeoutMS: config.timeout, }); await client.connect(); await client.db().admin().ping(); return '[COMPLETE] MongoDB: Connected'; } catch (error) { throw new Error(`[FAILED] MongoDB: ${error.message}`); } finally { if (client) { await client.close(); } } } /** * Vérifie la connexion Redis */ async function checkRedis() { let client; try { client = redis.createClient({ url: config.redisUrl, socket: { connectTimeout: config.timeout, commandTimeout: config.timeout, }, }); client.on('error', (err) => { throw new Error(`[FAILED] Redis: ${err.message}`); }); await client.connect(); await client.ping(); return '[COMPLETE] Redis: Connected'; } catch (error) { throw new Error(`[FAILED] Redis: ${error.message}`); } finally { if (client && client.isOpen) { await client.disconnect(); } } } /** * Vérifie la mémoire disponible */ function checkMemory() { const memoryUsage = process.memoryUsage(); const totalMemory = memoryUsage.heapTotal / 1024 / 1024; // MB const usedMemory = memoryUsage.heapUsed / 1024 / 1024; // MB const memoryPercent = (usedMemory / totalMemory) * 100; if (memoryPercent > 90) { throw new Error(`[FAILED] Memory: High usage ${memoryPercent.toFixed(1)}%`); } return `[COMPLETE] Memory: ${memoryPercent.toFixed(1)}% used (${usedMemory.toFixed(1)}MB/${totalMemory.toFixed(1)}MB)`; } /** * Fonction principale de health check */ async function healthCheck() { const checks = []; let allHealthy = true; console.log(' Starting health checks...\n'); // Vérification du serveur HTTP try { const httpResult = await checkHttpServer(); checks.push(httpResult); console.log(httpResult); } catch (error) { checks.push(error.message); console.error(error.message); allHealthy = false; } // Vérification MongoDB try { const mongoResult = await checkMongoDB(); checks.push(mongoResult); console.log(mongoResult); } catch (error) { checks.push(error.message); console.error(error.message); allHealthy = false; } // Vérification Redis try { const redisResult = await checkRedis(); checks.push(redisResult); console.log(redisResult); } catch (error) { checks.push(error.message); console.error(error.message); allHealthy = false; } // Vérification mémoire try { const memoryResult = checkMemory(); checks.push(memoryResult); console.log(memoryResult); } catch (error) { checks.push(error.message); console.error(error.message); allHealthy = false; } // Résultat final console.log('\n' + '='.repeat(50)); if (allHealthy) { console.log(' All health checks passed!'); console.log(`[ANALYTICS] Status: HEALTHY at ${new Date().toISOString()}`); process.exit(EXIT_SUCCESS); } else { console.log(' Some health checks failed!'); console.log(`[ANALYTICS] Status: UNHEALTHY at ${new Date().toISOString()}`); process.exit(EXIT_FAILURE); } } // Gestion des signaux process.on('SIGTERM', () => { console.log(' Health check interrupted by SIGTERM'); process.exit(EXIT_FAILURE); }); process.on('SIGINT', () => { console.log(' Health check interrupted by SIGINT'); process.exit(EXIT_FAILURE); }); // Timeout global pour éviter les blocages setTimeout(() => { console.error('⏰ Health check timeout - taking too long'); process.exit(EXIT_FAILURE); }, config.timeout * 2); // Démarrage du health check healthCheck().catch((error) => { console.error(' Unexpected error during health check:', error.message); process.exit(EXIT_FAILURE); });