const mongoose = require('mongoose'); const redis = require('redis'); const logger = require('../config/logger'); /** * Comprehensive health check middleware */ class HealthCheckService { constructor() { this.checks = new Map(); this.lastHealthStatus = null; this.healthCheckInterval = null; // Initialize health checks this.initializeHealthChecks(); } /** * Initialize all health check functions */ initializeHealthChecks() { this.checks.set('database', this.checkDatabase.bind(this)); this.checks.set('redis', this.checkRedis.bind(this)); this.checks.set('memory', this.checkMemory.bind(this)); this.checks.set('disk', this.checkDisk.bind(this)); this.checks.set('external_services', this.checkExternalServices.bind(this)); this.checks.set('features', this.checkFeatures.bind(this)); } /** * Check MongoDB database connection */ async checkDatabase() { try { const startTime = Date.now(); if (mongoose.connection.readyState !== 1) { throw new Error('MongoDB not connected'); } // Test database operation await mongoose.connection.db.admin().ping(); const responseTime = Date.now() - startTime; return { status: 'healthy', responseTime, connection: { state: mongoose.connection.readyState, host: mongoose.connection.host, port: mongoose.connection.port, database: mongoose.connection.name } }; } catch (error) { logger.error('Database health check failed:', error); return { status: 'unhealthy', error: error.message, connection: { state: mongoose.connection.readyState } }; } } /** * Check Redis cache connection */ async checkRedis() { try { const startTime = Date.now(); // Create temporary Redis client for health check const client = redis.createClient({ url: process.env.REDIS_URL, socket: { connectTimeout: 5000, commandTimeout: 5000 } }); await client.connect(); await client.ping(); const responseTime = Date.now() - startTime; await client.disconnect(); return { status: 'healthy', responseTime, connection: 'active' }; } catch (error) { logger.error('Redis health check failed:', error); return { status: 'unhealthy', error: error.message, connection: 'failed' }; } } /** * Check memory usage */ async checkMemory() { try { const memoryUsage = process.memoryUsage(); const threshold = parseInt(process.env.HEALTH_CHECK_MEMORY_THRESHOLD) || 536870912; // 512MB const isHealthy = memoryUsage.heapUsed < threshold; return { status: isHealthy ? 'healthy' : 'warning', usage: { heapUsed: memoryUsage.heapUsed, heapTotal: memoryUsage.heapTotal, external: memoryUsage.external, rss: memoryUsage.rss }, threshold, percentage: Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100) }; } catch (error) { logger.error('Memory health check failed:', error); return { status: 'unhealthy', error: error.message }; } } /** * Check disk space (simplified) */ async checkDisk() { try { // Basic disk check - in production, use proper disk monitoring const stats = require('fs').statSync('./'); return { status: 'healthy', available: true, note: 'Basic disk check - implement proper monitoring in production' }; } catch (error) { logger.error('Disk health check failed:', error); return { status: 'unhealthy', error: error.message }; } } /** * Check external services */ async checkExternalServices() { const services = {}; try { // Check OpenAI API (if configured) if (process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== 'sk-YOUR-REAL-OPENAI-API-KEY-HERE') { services.openai = { status: 'configured', note: 'API key present' }; } else { services.openai = { status: 'not_configured', note: 'API key missing or placeholder' }; } // Check Rasa service if (process.env.RASA_SERVER_URL) { services.rasa = { status: 'configured', url: process.env.RASA_SERVER_URL }; } else { services.rasa = { status: 'not_configured', note: 'Rasa URL not configured' }; } // Check ML service if (process.env.ML_SERVICE_URL) { services.ml_service = { status: 'configured', url: process.env.ML_SERVICE_URL }; } else { services.ml_service = { status: 'not_configured', note: 'ML service URL not configured' }; } return { status: 'healthy', services }; } catch (error) { logger.error('External services health check failed:', error); return { status: 'warning', error: error.message, services }; } } /** * Check feature flags and configuration */ async checkFeatures() { try { const features = { chat_history: process.env.FEATURE_CHAT_HISTORY === 'true', file_upload: process.env.FEATURE_FILE_UPLOAD === 'true', voice_messages: process.env.FEATURE_VOICE_MESSAGES === 'true', video_calls: process.env.FEATURE_VIDEO_CALLS === 'true', multi_language: process.env.FEATURE_MULTI_LANGUAGE === 'true', dark_mode: process.env.FEATURE_DARK_MODE === 'true', analytics: process.env.FEATURE_ANALYTICS === 'true', predictive: process.env.FEATURE_PREDICTIVE === 'true', simulation: process.env.FEATURE_SIMULATION === 'true' }; const enabledFeatures = Object.values(features).filter(Boolean).length; const totalFeatures = Object.keys(features).length; return { status: 'healthy', features, summary: { enabled: enabledFeatures, total: totalFeatures, percentage: Math.round((enabledFeatures / totalFeatures) * 100) } }; } catch (error) { logger.error('Features health check failed:', error); return { status: 'unhealthy', error: error.message }; } } /** * Run all health checks */ async runHealthChecks() { const startTime = Date.now(); const results = {}; const errors = []; try { // Run all health checks in parallel const checkPromises = Array.from(this.checks.entries()).map(async ([name, checkFn]) => { try { const result = await Promise.race([ checkFn(), new Promise((_, reject) => setTimeout(() => reject(new Error('Health check timeout')), 5000) ) ]); results[name] = result; } catch (error) { results[name] = { status: 'unhealthy', error: error.message }; errors.push(`${name}: ${error.message}`); } }); await Promise.all(checkPromises); // Calculate overall health status const statuses = Object.values(results).map(r => r.status); const hasUnhealthy = statuses.includes('unhealthy'); const hasWarning = statuses.includes('warning'); let overallStatus = 'healthy'; if (hasUnhealthy) overallStatus = 'unhealthy'; else if (hasWarning) overallStatus = 'warning'; const healthReport = { status: overallStatus, timestamp: new Date().toISOString(), responseTime: Date.now() - startTime, environment: process.env.NODE_ENV, version: process.env.DEPLOYMENT_VERSION || '1.0.0', uptime: process.uptime(), checks: results, summary: { total: this.checks.size, healthy: statuses.filter(s => s === 'healthy').length, warning: statuses.filter(s => s === 'warning').length, unhealthy: statuses.filter(s => s === 'unhealthy').length } }; // Log health status changes if (this.lastHealthStatus !== overallStatus) { logger.info(`Health status changed: ${this.lastHealthStatus} -> ${overallStatus}`, { type: 'health_change', previous: this.lastHealthStatus, current: overallStatus, errors: errors.length > 0 ? errors : undefined }); this.lastHealthStatus = overallStatus; } return healthReport; } catch (error) { logger.error('Health check system failed:', error); return { status: 'unhealthy', timestamp: new Date().toISOString(), error: error.message, uptime: process.uptime() }; } } /** * Express middleware for health check endpoint */ middleware() { return async (req, res) => { try { const healthReport = await this.runHealthChecks(); // Set appropriate status code const statusCode = healthReport.status === 'healthy' ? 200 : healthReport.status === 'warning' ? 200 : 503; // Set cache headers res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate'); res.setHeader('Pragma', 'no-cache'); res.setHeader('Expires', '0'); res.setHeader('Content-Type', 'application/json'); res.status(statusCode).json(healthReport); } catch (error) { logger.error('Health check endpoint failed:', error); res.status(503).json({ status: 'unhealthy', timestamp: new Date().toISOString(), error: 'Health check system failure', uptime: process.uptime() }); } }; } /** * Start periodic health monitoring */ startMonitoring(intervalMs = 60000) { if (this.healthCheckInterval) { clearInterval(this.healthCheckInterval); } this.healthCheckInterval = setInterval(async () => { try { await this.runHealthChecks(); } catch (error) { logger.error('Periodic health check failed:', error); } }, intervalMs); logger.info('Health monitoring started', { interval: intervalMs }); } /** * Stop periodic health monitoring */ stopMonitoring() { if (this.healthCheckInterval) { clearInterval(this.healthCheckInterval); this.healthCheckInterval = null; logger.info('Health monitoring stopped'); } } } // Create singleton instance const healthCheckService = new HealthCheckService(); module.exports = { healthCheckService, healthCheckMiddleware: healthCheckService.middleware() };