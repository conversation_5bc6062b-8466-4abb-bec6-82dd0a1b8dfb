/** * ============================================= * ENHANCED RATE LIMITING MIDDLEWARE * Advanced rate limiting with Redis backend * Supports different limits per user role and endpoint * ============================================= */ const rateLimit = require('express-rate-limit'); const RedisStore = require('rate-limit-redis'); const Redis = require('ioredis'); const logger = require('../utils/logger'); // Initialize Redis client for rate limiting const redis = new Redis({ host: process.env.REDIS_HOST || 'localhost', port: process.env.REDIS_PORT || 6379, password: process.env.REDIS_PASSWORD, db: 1, // Use separate database for rate limiting retryDelayOnFailover: 100, enableReadyCheck: false, maxRetriesPerRequest: null, }); redis.on('error', (err) => { logger.error('Redis rate limit store error:', err); }); redis.on('connect', () => { logger.info('Redis rate limit store connected'); }); /** * Create rate limiter with Redis store */ function createRateLimiter(options = {}) { const defaultOptions = { windowMs: 15 * 60 * 1000, // 15 minutes max: 100, // limit each IP to 100 requests per windowMs message: { success: false, error: 'Too many requests from this IP, please try again later', retryAfter: Math.ceil(options.windowMs / 1000) || 900 }, standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers legacyHeaders: false, // Disable the `X-RateLimit-*` headers store: new RedisStore({ sendCommand: (...args) => redis.call(...args), }), keyGenerator: (req) => { // Use user ID if authenticated, otherwise IP return req.user?.id || req.ip; }, skip: (req) => { // Skip rate limiting for health checks return req.path === '/health' || req.path === '/api/health'; }, onLimitReached: (req, res, options) => { logger.warn('Rate limit exceeded', { ip: req.ip, userId: req.user?.id, userAgent: req.get('User-Agent'), path: req.path, method: req.method }); } }; return rateLimit({ ...defaultOptions, ...options }); } /** * Role-based rate limiting */ function createRoleBasedRateLimiter(roleConfig = {}) { const defaultRoleConfig = { admin: { windowMs: 15 * 60 * 1000, max: 1000 }, supervisor: { windowMs: 15 * 60 * 1000, max: 500 }, agent: { windowMs: 15 * 60 * 1000, max: 200 }, guest: { windowMs: 15 * 60 * 1000, max: 50 } }; const config = { ...defaultRoleConfig, ...roleConfig }; return (req, res, next) => { const userRole = req.user?.role || 'guest'; const roleSettings = config[userRole] || config.guest; const limiter = createRateLimiter({ ...roleSettings, keyGenerator: (req) => `${userRole}:${req.user?.id || req.ip}`, message: { success: false, error: `Rate limit exceeded for ${userRole} role`, retryAfter: Math.ceil(roleSettings.windowMs / 1000) } }); return limiter(req, res, next); }; } /** * Endpoint-specific rate limiting */ function createEndpointRateLimiter(endpointConfig = {}) { return (req, res, next) => { const endpoint = req.route?.path || req.path; const method = req.method.toLowerCase(); const key = `${method}:${endpoint}`; const endpointSettings = endpointConfig[key] || endpointConfig.default || { windowMs: 15 * 60 * 1000, max: 100 }; const limiter = createRateLimiter({ ...endpointSettings, keyGenerator: (req) => `endpoint:${key}:${req.user?.id || req.ip}`, message: { success: false, error: `Rate limit exceeded for ${endpoint}`, retryAfter: Math.ceil(endpointSettings.windowMs / 1000) } }); return limiter(req, res, next); }; } /** * Adaptive rate limiting based on system load */ function createAdaptiveRateLimiter(baseConfig = {}) { const defaultConfig = { windowMs: 15 * 60 * 1000, baseMax: 100, loadThresholds: { low: { multiplier: 1.5, cpuThreshold: 50 }, medium: { multiplier: 1.0, cpuThreshold: 70 }, high: { multiplier: 0.5, cpuThreshold: 85 }, critical: { multiplier: 0.2, cpuThreshold: 95 } } }; const config = { ...defaultConfig, ...baseConfig }; return async (req, res, next) => { try { // Get current system load const systemLoad = await getSystemLoad(); // Determine load level let loadLevel = 'low'; for (const [level, threshold] of Object.entries(config.loadThresholds)) { if (systemLoad.cpu >= threshold.cpuThreshold) { loadLevel = level; } } // Calculate adaptive max requests const adaptiveMax = Math.floor( config.baseMax * config.loadThresholds[loadLevel].multiplier ); const limiter = createRateLimiter({ windowMs: config.windowMs, max: adaptiveMax, keyGenerator: (req) => `adaptive:${req.user?.id || req.ip}`, message: { success: false, error: `Rate limit exceeded (system load: ${loadLevel})`, retryAfter: Math.ceil(config.windowMs / 1000), systemLoad: loadLevel } }); return limiter(req, res, next); } catch (error) { logger.error('Error in adaptive rate limiter:', error); // Fallback to default rate limiting const fallbackLimiter = createRateLimiter(config); return fallbackLimiter(req, res, next); } }; } /** * Get current system load */ async function getSystemLoad() { try { const os = require('os'); const cpuUsage = await getCPUUsage(); const memoryUsage = (1 - (os.freemem() / os.totalmem())) * 100; const loadAverage = os.loadavg()[0]; return { cpu: cpuUsage, memory: memoryUsage, load: loadAverage }; } catch (error) { logger.error('Error getting system load:', error); return { cpu: 50, memory: 50, load: 1 }; // Default values } } /** * Calculate CPU usage percentage */ function getCPUUsage() { return new Promise((resolve) => { const os = require('os'); const cpus = os.cpus(); let totalIdle = 0; let totalTick = 0; cpus.forEach((cpu) => { for (const type in cpu.times) { totalTick += cpu.times[type]; } totalIdle += cpu.times.idle; }); setTimeout(() => { const cpus2 = os.cpus(); let totalIdle2 = 0; let totalTick2 = 0; cpus2.forEach((cpu) => { for (const type in cpu.times) { totalTick2 += cpu.times[type]; } totalIdle2 += cpu.times.idle; }); const idle = totalIdle2 - totalIdle; const total = totalTick2 - totalTick; const usage = 100 - ~~(100 * idle / total); resolve(usage); }, 100); }); } /** * Burst rate limiting for specific actions */ function createBurstRateLimiter(burstConfig = {}) { const defaultConfig = { burstWindowMs: 60 * 1000, // 1 minute burst window burstMax: 10, // max requests in burst window sustainedWindowMs: 15 * 60 * 1000, // 15 minute sustained window sustainedMax: 100 // max requests in sustained window }; const config = { ...defaultConfig, ...burstConfig }; const burstLimiter = createRateLimiter({ windowMs: config.burstWindowMs, max: config.burstMax, keyGenerator: (req) => `burst:${req.user?.id || req.ip}`, message: { success: false, error: 'Burst rate limit exceeded', retryAfter: Math.ceil(config.burstWindowMs / 1000) } }); const sustainedLimiter = createRateLimiter({ windowMs: config.sustainedWindowMs, max: config.sustainedMax, keyGenerator: (req) => `sustained:${req.user?.id || req.ip}`, message: { success: false, error: 'Sustained rate limit exceeded', retryAfter: Math.ceil(config.sustainedWindowMs / 1000) } }); return (req, res, next) => { burstLimiter(req, res, (err) => { if (err) return next(err); sustainedLimiter(req, res, next); }); }; } /** * IP whitelist bypass */ function createWhitelistRateLimiter(whitelist = [], options = {}) { const limiter = createRateLimiter(options); return (req, res, next) => { // Check if IP is whitelisted if (whitelist.includes(req.ip)) { return next(); } return limiter(req, res, next); }; } /** * Rate limiting with custom key generator */ function createCustomKeyRateLimiter(keyGenerator, options = {}) { return createRateLimiter({ ...options, keyGenerator }); } /** * Rate limiting middleware factory */ function createRateLimitMiddleware(type = 'default', config = {}) { switch (type) { case 'role-based': return createRoleBasedRateLimiter(config); case 'endpoint-specific': return createEndpointRateLimiter(config); case 'adaptive': return createAdaptiveRateLimiter(config); case 'burst': return createBurstRateLimiter(config); case 'whitelist': return createWhitelistRateLimiter(config.whitelist, config.options); case 'custom-key': return createCustomKeyRateLimiter(config.keyGenerator, config.options); default: return createRateLimiter(config); } } /** * Rate limit status endpoint */ async function getRateLimitStatus(req, res) { try { const key = req.user?.id || req.ip; const status = await redis.get(`rl:${key}`); res.json({ success: true, data: { key, remaining: status ? JSON.parse(status).remaining : null, resetTime: status ? JSON.parse(status).resetTime : null } }); } catch (error) { logger.error('Error getting rate limit status:', error); res.status(500).json({ success: false, error: 'Failed to get rate limit status' }); } } /** * Clear rate limit for user (admin only) */ async function clearRateLimit(req, res) { try { const { userId, ip } = req.body; const key = userId || ip; if (!key) { return res.status(400).json({ success: false, error: 'User ID or IP required' }); } // Clear all rate limit keys for the user/IP const keys = await redis.keys(`*:${key}`); if (keys.length > 0) { await redis.del(...keys); } res.json({ success: true, data: { cleared: keys.length, key } }); } catch (error) { logger.error('Error clearing rate limit:', error); res.status(500).json({ success: false, error: 'Failed to clear rate limit' }); } } module.exports = { createRateLimiter, createRoleBasedRateLimiter, createEndpointRateLimiter, createAdaptiveRateLimiter, createBurstRateLimiter, createWhitelistRateLimiter, createCustomKeyRateLimiter, createRateLimitMiddleware, getRateLimitStatus, clearRateLimit, redis };