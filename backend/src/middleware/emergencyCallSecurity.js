/** * ============================================= * EMERGENCY CALL SECURITY MIDDLEWARE * Security, compliance, and audit logging for emergency calls * Complies with French telecommunications regulations * ============================================= */ const logger = require('../config/logger'); const EmergencyCall = require('../models/EmergencyCall'); const User = require('../models/User'); const rateLimit = require('express-rate-limit'); const { body, validationResult } = require('express-validator'); /** * Enhanced rate limiting for emergency calls */ const emergencyCallRateLimit = rateLimit({ windowMs: 5 * 60 * 1000, // 5 minutes max: 3, // Maximum 3 emergency calls per 5 minutes per IP message: { success: false, error: 'Emergency call rate limit exceeded', message: 'Trop d\'appels d\'urgence récents. Veuillez patienter 5 minutes.', retryAfter: 300, emergencyContact: '9198' }, standardHeaders: true, legacyHeaders: false, keyGenerator: (req) => { // Use combination of IP and user ID for more accurate rate limiting return `${req.ip}_${req.user?.id || 'anonymous'}`; }, handler: (req, res) => { // Log rate limit violations logger.warn(' Emergency call rate limit exceeded', { ip: req.ip, userId: req.user?.id, userAgent: req.get('User-Agent'), timestamp: new Date(), endpoint: req.path }); // Audit trail for security monitoring if (req.user?.id) { EmergencyCall.create({ emergencyCallId: `rate_limit_${Date.now()}`, userId: req.user.id, status: 'cancelled', description: 'Rate limit exceeded', compliance: { auditTrail: [{ action: 'Rate limit violation', timestamp: new Date(), userId: req.user.id, details: { ip: req.ip, userAgent: req.get('User-Agent'), endpoint: req.path } }] } }).catch(err => logger.error('Failed to log rate limit violation:', err)); } res.status(429).json({ success: false, error: 'Emergency call rate limit exceeded', message: 'Trop d\'appels d\'urgence récents. Veuillez patienter.', retryAfter: 300, emergencyContact: '9198' }); } }); /** * Validate emergency call permissions */ const validateEmergencyCallPermissions = async (req, res, next) => { try { const user = req.user; if (!user) { return res.status(401).json({ success: false, error: 'Authentication required', message: 'Authentification requise pour les appels d\'urgence' }); } // Check if user account is active if (user.status !== 'active') { logger.warn(' Emergency call attempt by inactive user', { userId: user.id, status: user.status, ip: req.ip }); return res.status(403).json({ success: false, error: 'Account not active', message: 'Compte non actif. Contactez le support.', emergencyContact: '9198' }); } // Check for suspended users (security measure) if (user.profile?.suspended) { logger.warn(' Emergency call attempt by suspended user', { userId: user.id, suspensionReason: user.profile.suspensionReason, ip: req.ip }); return res.status(403).json({ success: false, error: 'Account suspended', message: 'Compte suspendu. Contactez le support.', emergencyContact: '9198' }); } // Log emergency call attempt for audit logger.info(' Emergency call permission validated', { userId: user.id, email: user.email, ip: req.ip, userAgent: req.get('User-Agent'), endpoint: req.path }); next(); } catch (error) { logger.error('Emergency call permission validation failed:', error); res.status(500).json({ success: false, error: 'Permission validation failed', message: 'Erreur de validation. Veuillez réessayer.', emergencyContact: '9198' }); } }; /** * Audit logging middleware for emergency calls */ const auditEmergencyCall = (action) => { return async (req, res, next) => { try { const auditData = { action, userId: req.user?.id, ip: req.ip, userAgent: req.get('User-Agent'), timestamp: new Date(), endpoint: req.path, method: req.method, body: { // Log only safe fields, exclude sensitive data urgencyLevel: req.body?.urgencyLevel, emergencyCallId: req.body?.emergencyCallId || req.params?.emergencyCallId, reason: req.body?.reason }, headers: { 'x-forwarded-for': req.get('X-Forwarded-For'), 'x-real-ip': req.get('X-Real-IP') } }; // Log to audit system logger.info(` Emergency call audit: ${action}`, auditData); // Store in database for compliance if (req.body?.emergencyCallId || req.params?.emergencyCallId) { const emergencyCallId = req.body?.emergencyCallId || req.params?.emergencyCallId; try { await EmergencyCall.findOneAndUpdate( { emergencyCallId }, { $push: { 'compliance.auditTrail': { action, timestamp: new Date(), userId: req.user?.id, details: auditData } } } ); } catch (dbError) { logger.error('Failed to store emergency call audit trail:', dbError); } } // Continue to next middleware next(); } catch (error) { logger.error('Emergency call audit logging failed:', error); // Don't block the request, just log the error next(); } }; }; /** * French telecommunications compliance middleware */ const frenchTelecomCompliance = async (req, res, next) => { try { // Ensure GDPR compliance if (req.body && req.user) { // Add GDPR consent tracking req.body.gdprConsent = { dataProcessingConsent: true, recordingConsent: true, dataRetentionNotified: true, consentTimestamp: new Date(), consentVersion: '1.0' }; // Add French telecom regulation compliance req.body.frenchTelecomCompliance = { regulationVersion: 'ARCEP-2024', emergencyServiceCompliance: true, dataLocalization: 'France', retentionPeriod: '90 days', complianceTimestamp: new Date() }; } // Log compliance check logger.info(' French telecom compliance validated', { userId: req.user?.id, endpoint: req.path, complianceVersion: 'ARCEP-2024' }); next(); } catch (error) { logger.error('French telecom compliance check failed:', error); res.status(500).json({ success: false, error: 'Compliance validation failed', message: 'Erreur de conformité réglementaire' }); } }; /** * Data sanitization for emergency calls */ const sanitizeEmergencyCallData = (req, res, next) => { try { if (req.body) { // Sanitize description if (req.body.description) { req.body.description = req.body.description .trim() .substring(0, 1000) // Limit length .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove scripts .replace(/[<>]/g, ''); // Remove HTML tags } // Sanitize reason if (req.body.reason) { req.body.reason = req.body.reason .trim() .substring(0, 200) .replace(/[<>]/g, ''); } // Validate urgency level if (req.body.urgencyLevel) { const validLevels = ['low', 'normal', 'medium', 'high', 'urgent', 'critical']; if (!validLevels.includes(req.body.urgencyLevel)) { req.body.urgencyLevel = 'high'; // Default to high for safety } } } next(); } catch (error) { logger.error('Emergency call data sanitization failed:', error); res.status(400).json({ success: false, error: 'Data validation failed', message: 'Données invalides' }); } }; /** * Emergency call input validation */ const validateEmergencyCallInput = [ body('userId') .isMongoId() .withMessage('Invalid user ID format'), body('urgencyLevel') .optional() .isIn(['low', 'normal', 'medium', 'high', 'urgent', 'critical']) .withMessage('Invalid urgency level'), body('description') .isLength({ min: 10, max: 1000 }) .withMessage('Description must be between 10 and 1000 characters') .trim() .escape(), body('conversationHistory') .optional() .isArray({ max: 50 }) .withMessage('Conversation history must be an array with maximum 50 messages'), // Validation error handler (req, res, next) => { const errors = validationResult(req); if (!errors.isEmpty()) { logger.warn(' Emergency call validation failed', { userId: req.user?.id, errors: errors.array(), ip: req.ip }); return res.status(400).json({ success: false, error: 'Validation failed', details: errors.array(), message: 'Données invalides pour l\'appel d\'urgence' }); } next(); } ]; /** * Security headers for emergency call responses */ const setEmergencyCallSecurityHeaders = (req, res, next) => { // Set security headers res.setHeader('X-Content-Type-Options', 'nosniff'); res.setHeader('X-Frame-Options', 'DENY'); res.setHeader('X-XSS-Protection', '1; mode=block'); res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains'); res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate'); res.setHeader('Pragma', 'no-cache'); res.setHeader('Expires', '0'); next(); }; module.exports = { emergencyCallRateLimit, validateEmergencyCallPermissions, auditEmergencyCall, frenchTelecomCompliance, sanitizeEmergencyCallData, validateEmergencyCallInput, setEmergencyCallSecurityHeaders };