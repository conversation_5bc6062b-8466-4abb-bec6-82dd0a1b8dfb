/** * ============================================= * ENHANCED ERROR HANDLING MIDDLEWARE * Comprehensive error handling and logging * Production-ready error responses * ============================================= */ const logger = require('../utils/logger'); const { v4: uuidv4 } = require('uuid'); /** * Custom error classes */ class AppError extends Error { constructor(message, statusCode, code = null, details = null) { super(message); this.statusCode = statusCode; this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error'; this.isOperational = true; this.code = code; this.details = details; this.timestamp = new Date().toISOString(); Error.captureStackTrace(this, this.constructor); } } class ValidationError extends AppError { constructor(message, details = null) { super(message, 400, 'VALIDATION_ERROR', details); } } class AuthenticationError extends AppError { constructor(message = 'Authentication failed') { super(message, 401, 'AUTHENTICATION_ERROR'); } } class AuthorizationError extends AppError { constructor(message = 'Access denied') { super(message, 403, 'AUTHORIZATION_ERROR'); } } class NotFoundError extends AppError { constructor(message = 'Resource not found') { super(message, 404, 'NOT_FOUND_ERROR'); } } class ConflictError extends AppError { constructor(message = 'Resource conflict') { super(message, 409, 'CONFLICT_ERROR'); } } class RateLimitError extends AppError { constructor(message = 'Rate limit exceeded', retryAfter = null) { super(message, 429, 'RATE_LIMIT_ERROR', { retryAfter }); } } class DatabaseError extends AppError { constructor(message = 'Database operation failed') { super(message, 500, 'DATABASE_ERROR'); } } class ExternalServiceError extends AppError { constructor(message = 'External service error', service = null) { super(message, 502, 'EXTERNAL_SERVICE_ERROR', { service }); } } /** * Error handler middleware */ const errorHandler = (err, req, res, next) => { // Generate unique error ID for tracking const errorId = uuidv4(); // Add error ID to error object err.errorId = errorId; // Log error details logError(err, req, errorId); // Handle specific error types if (err.name === 'ValidationError') { return handleValidationError(err, req, res); } if (err.name === 'CastError') { return handleCastError(err, req, res); } if (err.code === 11000) { return handleDuplicateFieldError(err, req, res); } if (err.name === 'JsonWebTokenError') { return handleJWTError(err, req, res); } if (err.name === 'TokenExpiredError') { return handleJWTExpiredError(err, req, res); } if (err.name === 'MongoError' || err.name === 'MongooseError') { return handleDatabaseError(err, req, res); } if (err.type === 'entity.parse.failed') { return handleJSONParseError(err, req, res); } if (err.code === 'LIMIT_FILE_SIZE') { return handleFileSizeError(err, req, res); } // Handle operational errors if (err.isOperational) { return sendErrorResponse(res, err); } // Handle programming errors return handleProgrammingError(err, req, res); }; /** * Log error details */ const logError = (err, req, errorId) => { const errorDetails = { errorId, message: err.message, stack: err.stack, statusCode: err.statusCode, code: err.code, url: req.originalUrl, method: req.method, ip: req.ip, userAgent: req.get('User-Agent'), userId: req.user?.id, timestamp: new Date().toISOString(), body: req.method !== 'GET' ? req.body : undefined, query: req.query, params: req.params }; // Log based on severity if (err.statusCode >= 500) { logger.error('Server Error:', errorDetails); } else if (err.statusCode >= 400) { logger.warn('Client Error:', errorDetails); } else { logger.info('Error:', errorDetails); } // Send to external error tracking service in production if (process.env.NODE_ENV === 'production') { sendToErrorTracking(errorDetails); } }; /** * Handle specific error types */ const handleValidationError = (err, req, res) => { const errors = Object.values(err.errors).map(val => ({ field: val.path, message: val.message, value: val.value })); const error = new ValidationError('Validation failed', errors); return sendErrorResponse(res, error); }; const handleCastError = (err, req, res) => { const message = `Invalid ${err.path}: ${err.value}`; const error = new ValidationError(message); return sendErrorResponse(res, error); }; const handleDuplicateFieldError = (err, req, res) => { const field = Object.keys(err.keyValue)[0]; const value = err.keyValue[field]; const message = `${field} '${value}' already exists`; const error = new ConflictError(message); return sendErrorResponse(res, error); }; const handleJWTError = (err, req, res) => { const error = new AuthenticationError('Invalid token'); return sendErrorResponse(res, error); }; const handleJWTExpiredError = (err, req, res) => { const error = new AuthenticationError('Token expired'); return sendErrorResponse(res, error); }; const handleDatabaseError = (err, req, res) => { const error = new DatabaseError('Database operation failed'); return sendErrorResponse(res, error); }; const handleJSONParseError = (err, req, res) => { const error = new ValidationError('Invalid JSON format'); return sendErrorResponse(res, error); }; const handleFileSizeError = (err, req, res) => { const error = new ValidationError('File size too large'); return sendErrorResponse(res, error); }; const handleProgrammingError = (err, req, res) => { // Don't leak error details in production const message = process.env.NODE_ENV === 'production' ? 'Something went wrong' : err.message; const error = new AppError(message, 500, 'INTERNAL_SERVER_ERROR'); return sendErrorResponse(res, error); }; /** * Send error response */ const sendErrorResponse = (res, err) => { const response = { success: false, error: err.message, code: err.code, errorId: err.errorId, timestamp: err.timestamp || new Date().toISOString() }; // Add details for client errors if (err.statusCode < 500 && err.details) { response.details = err.details; } // Add stack trace in development if (process.env.NODE_ENV === 'development' && err.stack) { response.stack = err.stack; } res.status(err.statusCode || 500).json(response); }; /** * Async error wrapper */ const asyncHandler = (fn) => { return (req, res, next) => { Promise.resolve(fn(req, res, next)).catch(next); }; }; /** * 404 handler */ const notFoundHandler = (req, res, next) => { const error = new NotFoundError(`Route ${req.originalUrl} not found`); next(error); }; /** * Unhandled rejection handler */ const handleUnhandledRejection = () => { process.on('unhandledRejection', (reason, promise) => { logger.error('Unhandled Rejection:', { reason: reason.message || reason, stack: reason.stack, promise }); // Graceful shutdown process.exit(1); }); }; /** * Uncaught exception handler */ const handleUncaughtException = () => { process.on('uncaughtException', (err) => { logger.error('Uncaught Exception:', { message: err.message, stack: err.stack }); // Graceful shutdown process.exit(1); }); }; /** * Send to external error tracking service */ const sendToErrorTracking = (errorDetails) => { // Implement integration with services like Sentry, Bugsnag, etc. // This is a placeholder for the actual implementation try { // Example: Sentry.captureException(errorDetails); logger.info('Error sent to tracking service:', { errorId: errorDetails.errorId }); } catch (trackingError) { logger.error('Failed to send error to tracking service:', trackingError); } }; /** * Health check error handler */ const healthCheckErrorHandler = (err, req, res, next) => { if (req.path === '/health' || req.path === '/api/health') { return res.status(503).json({ success: false, status: 'unhealthy', error: 'Service unavailable', timestamp: new Date().toISOString() }); } next(err); }; /** * Rate limit error handler */ const rateLimitErrorHandler = (err, req, res, next) => { if (err.type === 'rate-limit') { const error = new RateLimitError(err.message, err.retryAfter); return sendErrorResponse(res, error); } next(err); }; /** * CORS error handler */ const corsErrorHandler = (err, req, res, next) => { if (err.message && err.message.includes('CORS')) { const error = new AuthorizationError('CORS policy violation'); return sendErrorResponse(res, error); } next(err); }; /** * Initialize error handling */ const initializeErrorHandling = () => { handleUnhandledRejection(); handleUncaughtException(); }; /** * Error middleware chain */ const errorMiddlewareChain = [ healthCheckErrorHandler, rateLimitErrorHandler, corsErrorHandler, errorHandler ]; module.exports = { // Error classes AppError, ValidationError, AuthenticationError, AuthorizationError, NotFoundError, ConflictError, RateLimitError, DatabaseError, ExternalServiceError, // Middleware errorHandler, asyncHandler, notFoundHandler, errorMiddlewareChain, // Utilities initializeErrorHandling, sendErrorResponse, logError };