const rateLimit = require('express-rate-limit'); const { RedisStore } = require('rate-limit-redis'); const Redis = require('ioredis'); const logger = require('../config/logger'); // Initialize Redis client for rate limiting const redis = new Redis({ host: process.env.REDIS_HOST || 'localhost', port: process.env.REDIS_PORT || 6379, password: process.env.REDIS_PASSWORD, retryDelayOnFailover: 100, maxRetriesPerRequest: 3, lazyConnect: true }); redis.on('error', (err) => { logger.error('Redis rate limit store error:', err); }); // Create Redis store for rate limiting const redisStore = new RedisStore({ sendCommand: (...args) => redis.call(...args), }); // Rate limit for creating support tickets const createTicketLimiter = rateLimit({ store: redisStore, windowMs: 15 * 60 * 1000, // 15 minutes max: 5, // Limit each user to 5 ticket creation requests per windowMs message: { success: false, message: 'Too many tickets created. Please wait before creating another ticket.', retryAfter: '15 minutes' }, standardHeaders: true, legacyHeaders: false, keyGenerator: (req) => { // Use user ID for authenticated requests return req.user ? `ticket_create_${req.user._id}` : `ticket_create_${req.ip}`; }, skip: (req) => { // Skip rate limiting for admin users return req.user && req.user.role === 'admin'; } }); // Rate limit for adding messages to tickets const addMessageLimiter = rateLimit({ store: redisStore, windowMs: 5 * 60 * 1000, // 5 minutes max: 20, // Limit each user to 20 messages per windowMs message: { success: false, message: 'Too many messages sent. Please wait before sending another message.', retryAfter: '5 minutes' }, standardHeaders: true, legacyHeaders: false, keyGenerator: (req) => { return req.user ? `message_add_${req.user._id}` : `message_add_${req.ip}`; }, skip: (req) => { // Skip rate limiting for admin and agent users return req.user && ['admin', 'agent'].includes(req.user.role); } }); // Rate limit for general API requests const generalApiLimiter = rateLimit({ store: redisStore, windowMs: 15 * 60 * 1000, // 15 minutes max: 1000, // Limit each user to 1000 requests per windowMs message: { success: false, message: 'Too many requests. Please try again later.', retryAfter: '15 minutes' }, standardHeaders: true, legacyHeaders: false, keyGenerator: (req) => { return req.user ? `api_general_${req.user._id}` : `api_general_${req.ip}`; }, skip: (req) => { // Skip rate limiting for admin users return req.user && req.user.role === 'admin'; } }); // Rate limit for authentication endpoints const authLimiter = rateLimit({ store: redisStore, windowMs: 15 * 60 * 1000, // 15 minutes max: 10, // Limit each IP to 10 auth requests per windowMs message: { success: false, message: 'Too many authentication attempts. Please try again later.', retryAfter: '15 minutes' }, standardHeaders: true, legacyHeaders: false, keyGenerator: (req) => `auth_${req.ip}` }); // Rate limit for file uploads const uploadLimiter = rateLimit({ store: redisStore, windowMs: 10 * 60 * 1000, // 10 minutes max: 50, // Limit each user to 50 file uploads per windowMs message: { success: false, message: 'Too many file uploads. Please wait before uploading more files.', retryAfter: '10 minutes' }, standardHeaders: true, legacyHeaders: false, keyGenerator: (req) => { return req.user ? `upload_${req.user._id}` : `upload_${req.ip}`; } }); // Rate limit for search/query operations const searchLimiter = rateLimit({ store: redisStore, windowMs: 1 * 60 * 1000, // 1 minute max: 60, // Limit each user to 60 search requests per minute message: { success: false, message: 'Too many search requests. Please slow down.', retryAfter: '1 minute' }, standardHeaders: true, legacyHeaders: false, keyGenerator: (req) => { return req.user ? `search_${req.user._id}` : `search_${req.ip}`; } }); // Strict rate limit for sensitive operations const strictLimiter = rateLimit({ store: redisStore, windowMs: 60 * 60 * 1000, // 1 hour max: 5, // Limit each user to 5 sensitive operations per hour message: { success: false, message: 'Too many sensitive operations. Please wait before trying again.', retryAfter: '1 hour' }, standardHeaders: true, legacyHeaders: false, keyGenerator: (req) => { return req.user ? `strict_${req.user._id}` : `strict_${req.ip}`; }, skip: (req) => { // Skip rate limiting for admin users return req.user && req.user.role === 'admin'; } }); module.exports = { createTicket: createTicketLimiter, addMessage: addMessageLimiter, general: generalApiLimiter, auth: authLimiter, upload: uploadLimiter, search: searchLimiter, strict: strictLimiter, redis };