/** * ============================================= * [COMPLETE] ENHANCED VALIDATION MIDDLEWARE * Comprehensive input validation and sanitization * Custom validators for business logic * ============================================= */ const { body, param, query, validationResult } = require('express-validator'); const mongoose = require('mongoose'); const logger = require('../config/logger'); /** * Handle validation errors */ const handleValidationErrors = (req, res, next) => { const errors = validationResult(req); if (!errors.isEmpty()) { const formattedErrors = errors.array().map(error => ({ field: error.param, message: error.msg, value: error.value, location: error.location })); logger.warn('Validation errors:', { path: req.path, method: req.method, errors: formattedErrors, userId: req.user?.id }); return res.status(400).json({ success: false, error: 'Validation failed', details: formattedErrors }); } next(); }; /** * Custom validators */ const customValidators = { // Validate MongoDB ObjectId isObjectId: (value) => { return mongoose.Types.ObjectId.isValid(value); }, // Validate UUID isUUID: (value) => { const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i; return uuidRegex.test(value); }, // Validate phone number (international format) isPhoneNumber: (value) => { const phoneRegex = /^\+[1-9]\d{1,14}$/; return phoneRegex.test(value); }, // Validate strong password isStrongPassword: (value) => { // At least 8 characters, 1 uppercase, 1 lowercase, 1 number, 1 special character const strongPasswordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/; return strongPasswordRegex.test(value); }, // Validate sentiment score (-1 to 1) isSentimentScore: (value) => { const score = parseFloat(value); return !isNaN(score) && score >= -1 && score <= 1; }, // Validate probability (0 to 1) isProbability: (value) => { const prob = parseFloat(value); return !isNaN(prob) && prob >= 0 && prob <= 1; }, // Validate percentage (0 to 100) isPercentage: (value) => { const percent = parseFloat(value); return !isNaN(percent) && percent >= 0 && percent <= 100; }, // Validate time range format isTimeRange: (value) => { const timeRangeRegex = /^(\d+)(h|d|w|m|y)$/; return timeRangeRegex.test(value); }, // Validate JSON string isJSONString: (value) => { try { JSON.parse(value); return true; } catch { return false; } }, // Validate array of strings isStringArray: (value) => { return Array.isArray(value) && value.every(item => typeof item === 'string'); }, // Validate customer tier isCustomerTier: (value) => { const validTiers = ['standard', 'bronze', 'silver', 'gold', 'platinum']; return validTiers.includes(value); }, // Validate agent role isAgentRole: (value) => { const validRoles = ['agent', 'supervisor', 'admin', 'analyst']; return validRoles.includes(value); }, // Validate simulation difficulty isSimulationDifficulty: (value) => { const validDifficulties = ['beginner', 'intermediate', 'expert']; return validDifficulties.includes(value); }, // Validate risk level isRiskLevel: (value) => { const validLevels = ['low', 'medium', 'high', 'critical']; return validLevels.includes(value); }, // Validate platform isPlatform: (value) => { const validPlatforms = ['whatsapp', 'email', 'chat', 'phone', 'sms']; return validPlatforms.includes(value); } }; /** * Validation schemas for different endpoints */ const validationSchemas = { // User authentication login: [ body('email') .isEmail() .normalizeEmail() .withMessage('Valid email is required'), body('password') .isLength({ min: 6 }) .withMessage('Password must be at least 6 characters long'), handleValidationErrors ], register: [ body('email') .isEmail() .normalizeEmail() .withMessage('Valid email is required'), body('password') .custom(customValidators.isStrongPassword) .withMessage('Password must be at least 8 characters with uppercase, lowercase, number, and special character'), body('firstName') .trim() .isLength({ min: 2, max: 50 }) .withMessage('First name must be 2-50 characters'), body('lastName') .trim() .isLength({ min: 2, max: 50 }) .withMessage('Last name must be 2-50 characters'), body('role') .optional() .custom(customValidators.isAgentRole) .withMessage('Invalid role'), handleValidationErrors ], // Simulation validation createSimulationSession: [ body('scenarioId') .custom(customValidators.isObjectId) .withMessage('Valid scenario ID is required'), body('settings') .optional() .isObject() .withMessage('Settings must be an object'), body('settings.difficulty_adjustment') .optional() .isBoolean() .withMessage('Difficulty adjustment must be boolean'), body('settings.ai_coaching_enabled') .optional() .isBoolean() .withMessage('AI coaching enabled must be boolean'), handleValidationErrors ], addSimulationMessage: [ param('id') .notEmpty() .withMessage('Session ID is required'), body('message') .trim() .isLength({ min: 1, max: 2000 }) .withMessage('Message must be 1-2000 characters'), body('timestamp') .optional() .isISO8601() .withMessage('Invalid timestamp format'), handleValidationErrors ], // Predictive analytics validation churnPrediction: [ query('risk_level') .optional() .custom(customValidators.isRiskLevel) .withMessage('Invalid risk level'), query('time_range') .optional() .custom(customValidators.isTimeRange) .withMessage('Invalid time range format'), query('limit') .optional() .isInt({ min: 1, max: 1000 }) .toInt() .withMessage('Limit must be between 1 and 1000'), handleValidationErrors ], // AI suggestions validation generateSuggestions: [ body('conversation_context') .isObject() .withMessage('Conversation context is required'), body('conversation_context.conversation_history') .isArray() .withMessage('Conversation history must be an array'), body('urgency_level') .optional() .isIn(['low', 'medium', 'high', 'critical']) .withMessage('Invalid urgency level'), body('platform') .optional() .custom(customValidators.isPlatform) .withMessage('Invalid platform'), handleValidationErrors ], // Sentiment analysis validation analyzeSentiment: [ body('messages') .isArray({ min: 1 }) .withMessage('Messages array is required'), body('messages.*.content') .trim() .isLength({ min: 1 }) .withMessage('Message content is required'), body('messages.*.sender') .isIn(['agent', 'customer']) .withMessage('Invalid sender type'), body('conversation_id') .optional() .isString() .withMessage('Conversation ID must be a string'), handleValidationErrors ], // Template validation createTemplate: [ body('name') .trim() .isLength({ min: 1, max: 100 }) .withMessage('Template name must be 1-100 characters'), body('category') .trim() .isLength({ min: 1, max: 50 }) .withMessage('Category is required'), body('content') .trim() .isLength({ min: 1, max: 2000 }) .withMessage('Content must be 1-2000 characters'), body('customization') .optional() .isString() .withMessage('Customization must be a string'), handleValidationErrors ], // Feedback validation submitFeedback: [ body('suggestion_id') .notEmpty() .withMessage('Suggestion ID is required'), body('feedback.rating') .isInt({ min: 1, max: 5 }) .withMessage('Rating must be between 1 and 5'), body('feedback.helpful') .isBoolean() .withMessage('Helpful must be boolean'), body('feedback.used_as_is') .isBoolean() .withMessage('Used as is must be boolean'), body('feedback.outcome') .optional() .isIn(['successful', 'unsuccessful', 'neutral']) .withMessage('Invalid outcome'), body('feedback.notes') .optional() .isLength({ max: 500 }) .withMessage('Notes must be less than 500 characters'), handleValidationErrors ], // Analytics validation analyticsQuery: [ query('time_range') .optional() .custom(customValidators.isTimeRange) .withMessage('Invalid time range format'), query('agent_id') .optional() .custom(customValidators.isObjectId) .withMessage('Invalid agent ID'), query('limit') .optional() .isInt({ min: 1, max: 1000 }) .toInt() .withMessage('Limit must be between 1 and 1000'), query('offset') .optional() .isInt({ min: 0 }) .toInt() .withMessage('Offset must be non-negative'), handleValidationErrors ], // File upload validation fileUpload: [ body('file_type') .optional() .isIn(['image', 'document', 'audio', 'video']) .withMessage('Invalid file type'), body('max_size') .optional() .isInt({ min: 1 }) .withMessage('Max size must be positive integer'), handleValidationErrors ] }; /** * Dynamic validation based on user role */ const createRoleBasedValidation = (validationRules) => { return (req, res, next) => { const userRole = req.user?.role || 'guest'; const rules = validationRules[userRole] || validationRules.default || []; // Apply validation rules Promise.all(rules.map(rule => rule.run(req))) .then(() => { const errors = validationResult(req); if (!errors.isEmpty()) { return res.status(400).json({ success: false, error: 'Validation failed', details: errors.array() }); } next(); }) .catch(next); }; }; /** * Sanitization middleware */ const sanitizeInput = (req, res, next) => { // Sanitize string inputs const sanitizeString = (str) => { if (typeof str !== 'string') return str; // Remove potential XSS return str .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') .replace(/javascript:/gi, '') .replace(/on\w+\s*=/gi, '') .trim(); }; // Recursively sanitize object const sanitizeObject = (obj) => { if (obj === null || typeof obj !== 'object') { return typeof obj === 'string' ? sanitizeString(obj) : obj; } if (Array.isArray(obj)) { return obj.map(sanitizeObject); } const sanitized = {}; for (const [key, value] of Object.entries(obj)) { sanitized[key] = sanitizeObject(value); } return sanitized; }; // Sanitize request body, query, and params req.body = sanitizeObject(req.body); req.query = sanitizeObject(req.query); req.params = sanitizeObject(req.params); next(); }; /** * Content type validation */ const validateContentType = (allowedTypes = ['application/json']) => { return (req, res, next) => { if (req.method === 'GET' || req.method === 'DELETE') { return next(); } const contentType = req.get('Content-Type'); if (!contentType || !allowedTypes.some(type => contentType.includes(type))) { return res.status(415).json({ success: false, error: 'Unsupported content type', allowed: allowedTypes }); } next(); }; }; /** * Request size validation */ const validateRequestSize = (maxSize = 1024 * 1024) => { // 1MB default return (req, res, next) => { const contentLength = parseInt(req.get('Content-Length') || '0'); if (contentLength > maxSize) { return res.status(413).json({ success: false, error: 'Request too large', maxSize: maxSize, receivedSize: contentLength }); } next(); }; }; module.exports = { handleValidationErrors, customValidators, validationSchemas, createRoleBasedValidation, sanitizeInput, validateContentType, validateRequestSize };