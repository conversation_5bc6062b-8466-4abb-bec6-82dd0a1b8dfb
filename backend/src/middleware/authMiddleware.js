const jwt = require('jsonwebtoken'); const User = require('../models/User'); const logger = require('../config/logger'); exports.authenticate = async (req, res, next) => { try { const token = req.header('Authorization')?.replace('Bearer ', ''); if (!token) { logger.warn('Authentication failed - no token provided', { ip: req.ip, userAgent: req.get('User-Agent'), path: req.path }); return res.status(401).json({ error: 'Authentication required', code: 'AUTH_TOKEN_MISSING' }); } // Validate JWT_SECRET exists if (!process.env.JWT_SECRET) { logger.error('JWT_SECRET environment variable not configured'); return res.status(500).json({ error: 'Server configuration error', code: 'AUTH_CONFIG_ERROR' }); } const decoded = jwt.verify(token, process.env.JWT_SECRET); // Validate token structure if (!decoded.userId || !decoded.exp) { logger.warn('Invalid token structure', { tokenPayload: decoded }); return res.status(401).json({ error: 'Invalid token format', code: 'AUTH_TOKEN_INVALID' }); } // Check token expiration with buffer const now = Math.floor(Date.now() / 1000); if (decoded.exp <= now) { logger.warn('Token expired', { userId: decoded.userId, expiredAt: new Date(decoded.exp * 1000) }); return res.status(401).json({ error: 'Token expired', code: 'AUTH_TOKEN_EXPIRED' }); } const user = await User.findById(decoded.userId).select('-password'); if (!user) { logger.warn(`Authentication failed - user not found: ${decoded.userId}`, { ip: req.ip, userAgent: req.get('User-Agent') }); return res.status(401).json({ error: 'User not found', code: 'AUTH_USER_NOT_FOUND' }); } // Check if user account is active if (user.status === 'suspended' || user.status === 'deleted') { logger.warn(`Authentication failed - user account inactive: ${user.id}`, { status: user.status, ip: req.ip }); return res.status(401).json({ error: 'Account inactive', code: 'AUTH_ACCOUNT_INACTIVE' }); } req.user = user; req.token = token; req.tokenPayload = decoded; next(); } catch (error) { if (error.name === 'JsonWebTokenError') { logger.warn('JWT verification failed', { error: error.message, ip: req.ip, userAgent: req.get('User-Agent') }); return res.status(401).json({ error: 'Invalid token', code: 'AUTH_TOKEN_INVALID' }); } else if (error.name === 'TokenExpiredError') { logger.warn('JWT token expired', { expiredAt: error.expiredAt, ip: req.ip }); return res.status(401).json({ error: 'Token expired', code: 'AUTH_TOKEN_EXPIRED' }); } else { logger.error('Authentication error:', error); return res.status(500).json({ error: 'Authentication service error', code: 'AUTH_SERVICE_ERROR' }); } } }; exports.authorize = (...roles) => { return (req, res, next) => { if (!roles.includes(req.user.role)) { logger.warn(`Authorization failed - insufficient role: ${req.user.role}`); return res.status(403).json({ error: 'Insufficient permissions' }); } next(); }; };