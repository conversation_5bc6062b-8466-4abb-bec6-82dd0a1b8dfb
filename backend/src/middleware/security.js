/** * ============================================= * ENHANCED SECURITY MIDDLEWARE * Comprehensive security measures and protection * OWASP compliance and threat mitigation * ============================================= */ const helmet = require('helmet'); const cors = require('cors'); const mongoSanitize = require('express-mongo-sanitize'); const xss = require('xss-clean'); const hpp = require('hpp'); const crypto = require('crypto'); const rateLimit = require('express-rate-limit'); const slowDown = require('express-slow-down'); const logger = require('../utils/logger'); /** * CORS configuration */ const corsOptions = { origin: function (origin, callback) { const allowedOrigins = [ process.env.FRONTEND_URL || 'http://localhost:3001', 'http://localhost:3000', // Development 'https://chatbot.freemobile.fr', // Production 'https://admin.freemobile.fr' // Admin panel ]; // Allow requests with no origin (mobile apps, etc.) if (!origin) return callback(null, true); if (allowedOrigins.includes(origin)) { callback(null, true); } else { logger.warn('CORS violation:', { origin, userAgent: origin }); callback(new Error('Not allowed by CORS')); } }, credentials: true, methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'], allowedHeaders: [ 'Origin', 'X-Requested-With', 'Content-Type', 'Accept', 'Authorization', 'X-API-Key', 'X-Request-ID', 'X-Forwarded-For' ], exposedHeaders: [ 'X-RateLimit-Limit', 'X-RateLimit-Remaining', 'X-RateLimit-Reset', 'X-Request-ID' ], maxAge: 86400 // 24 hours }; /** * Helmet security configuration */ const helmetOptions = { contentSecurityPolicy: { directives: { defaultSrc: ["'self'"], styleSrc: ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'], fontSrc: ["'self'", 'https://fonts.gstatic.com'], imgSrc: ["'self'", 'data:', 'https:'], scriptSrc: ["'self'"], connectSrc: ["'self'", 'wss:', 'ws:'], frameSrc: ["'none'"], objectSrc: ["'none'"], upgradeInsecureRequests: [] } }, crossOriginEmbedderPolicy: false, // Disable for WebSocket compatibility hsts: { maxAge: 31536000, // 1 year includeSubDomains: true, preload: true }, noSniff: true, frameguard: { action: 'deny' }, xssFilter: true, referrerPolicy: { policy: 'strict-origin-when-cross-origin' } }; /** * Request ID middleware */ const requestId = (req, res, next) => { const requestId = req.headers['x-request-id'] || crypto.randomUUID(); req.requestId = requestId; res.setHeader('X-Request-ID', requestId); next(); }; /** * Security headers middleware */ const securityHeaders = (req, res, next) => { // Additional security headers res.setHeader('X-Content-Type-Options', 'nosniff'); res.setHeader('X-Frame-Options', 'DENY'); res.setHeader('X-XSS-Protection', '1; mode=block'); res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload'); res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()'); res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin'); // Remove server information res.removeHeader('X-Powered-By'); res.removeHeader('Server'); next(); }; /** * IP whitelist middleware */ const ipWhitelist = (whitelist = []) => { return (req, res, next) => { if (whitelist.length === 0) { return next(); } const clientIP = req.ip || req.connection.remoteAddress; if (!whitelist.includes(clientIP)) { logger.warn('IP not whitelisted:', { ip: clientIP, path: req.path }); return res.status(403).json({ success: false, error: 'Access denied from this IP address' }); } next(); }; }; /** * API key validation middleware */ const apiKeyValidation = (req, res, next) => { const apiKey = req.headers['x-api-key']; // Skip API key validation for certain routes const skipRoutes = ['/health', '/api/health', '/api/auth/login']; if (skipRoutes.includes(req.path)) { return next(); } if (!apiKey) { return res.status(401).json({ success: false, error: 'API key required' }); } // Validate API key format const apiKeyRegex = /^[a-zA-Z0-9]{32,64}$/; if (!apiKeyRegex.test(apiKey)) { return res.status(401).json({ success: false, error: 'Invalid API key format' }); } // In production, validate against database const validApiKeys = process.env.VALID_API_KEYS?.split(',') || []; if (process.env.NODE_ENV === 'production' && !validApiKeys.includes(apiKey)) { logger.warn('Invalid API key used:', { apiKey: apiKey.substring(0, 8) + '...', ip: req.ip }); return res.status(401).json({ success: false, error: 'Invalid API key' }); } next(); }; /** * Request size limiter */ const requestSizeLimiter = (maxSize = '10mb') => { return (req, res, next) => { const contentLength = parseInt(req.headers['content-length'] || '0'); const maxSizeBytes = parseSize(maxSize); if (contentLength > maxSizeBytes) { return res.status(413).json({ success: false, error: 'Request entity too large', maxSize: maxSize, receivedSize: contentLength }); } next(); }; }; /** * Brute force protection */ const bruteForceProtection = rateLimit({ windowMs: 15 * 60 * 1000, // 15 minutes max: 5, // limit each IP to 5 requests per windowMs for sensitive endpoints message: { success: false, error: 'Too many attempts, please try again later' }, standardHeaders: true, legacyHeaders: false, skip: (req) => { // Apply only to sensitive endpoints const sensitiveEndpoints = ['/api/auth/login', '/api/auth/register', '/api/auth/reset-password']; return !sensitiveEndpoints.some(endpoint => req.path.includes(endpoint)); } }); /** * Slow down middleware for suspicious activity */ const slowDownMiddleware = slowDown({ windowMs: 15 * 60 * 1000, // 15 minutes delayAfter: 10, // allow 10 requests per windowMs without delay delayMs: 500, // add 500ms delay per request after delayAfter maxDelayMs: 20000, // max delay of 20 seconds skip: (req) => { // Skip for health checks return req.path === '/health' || req.path === '/api/health'; } }); /** * Content type validation */ const contentTypeValidation = (req, res, next) => { if (req.method === 'POST' || req.method === 'PUT' || req.method === 'PATCH') { const contentType = req.headers['content-type']; if (!contentType) { return res.status(400).json({ success: false, error: 'Content-Type header required' }); } const allowedTypes = [ 'application/json', 'multipart/form-data', 'application/x-www-form-urlencoded' ]; if (!allowedTypes.some(type => contentType.includes(type))) { return res.status(415).json({ success: false, error: 'Unsupported content type', allowed: allowedTypes }); } } next(); }; /** * SQL injection protection */ const sqlInjectionProtection = (req, res, next) => { const sqlPatterns = [ /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/i, /(\'|\"|;|--|\*|\|)/, /(\b(OR|AND)\b.*=.*)/i ]; const checkForSQLInjection = (obj) => { if (typeof obj === 'string') { return sqlPatterns.some(pattern => pattern.test(obj)); } if (typeof obj === 'object' && obj !== null) { return Object.values(obj).some(checkForSQLInjection); } return false; }; if (checkForSQLInjection(req.body) || checkForSQLInjection(req.query) || checkForSQLInjection(req.params)) { logger.warn('SQL injection attempt detected:', { ip: req.ip, path: req.path, body: req.body, query: req.query, params: req.params }); return res.status(400).json({ success: false, error: 'Invalid request parameters' }); } next(); }; /** * File upload security */ const fileUploadSecurity = (options = {}) => { const { allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'], maxFileSize = 5 * 1024 * 1024, // 5MB allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.pdf'] } = options; return (req, res, next) => { if (!req.files || Object.keys(req.files).length === 0) { return next(); } const files = Array.isArray(req.files) ? req.files : Object.values(req.files); for (const file of files) { // Check file size if (file.size > maxFileSize) { return res.status(413).json({ success: false, error: 'File too large', maxSize: maxFileSize }); } // Check MIME type if (!allowedMimeTypes.includes(file.mimetype)) { return res.status(400).json({ success: false, error: 'Invalid file type', allowed: allowedMimeTypes }); } // Check file extension const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.')); if (!allowedExtensions.includes(fileExtension)) { return res.status(400).json({ success: false, error: 'Invalid file extension', allowed: allowedExtensions }); } } next(); }; }; /** * Parse size string to bytes */ const parseSize = (size) => { const units = { b: 1, kb: 1024, mb: 1024 * 1024, gb: 1024 * 1024 * 1024 }; const match = size.toString().toLowerCase().match(/^(\d+(?:\.\d+)?)\s*(b|kb|mb|gb)?$/); if (!match) return 0; const value = parseFloat(match[1]); const unit = match[2] || 'b'; return Math.floor(value * units[unit]); }; /** * Security middleware chain */ const securityMiddleware = [ requestId, helmet(helmetOptions), cors(corsOptions), securityHeaders, mongoSanitize(), xss(), hpp(), contentTypeValidation, sqlInjectionProtection, slowDownMiddleware, bruteForceProtection ]; module.exports = { corsOptions, helmetOptions, requestId, securityHeaders, ipWhitelist, apiKeyValidation, requestSizeLimiter, bruteForceProtection, slowDownMiddleware, contentTypeValidation, sqlInjectionProtection, fileUploadSecurity, securityMiddleware };