/** * ============================================= * [ANALYTICS] ANALYTICS API ROUTES * Comprehensive analytics and reporting endpoints * Time-series data and business intelligence * ============================================= */ const express = require('express'); const router = express.Router(); const { query, validationResult } = require('express-validator'); const auth = require('../middleware/auth'); const rateLimit = require('../middleware/rateLimit'); const AnalyticsService = require('../services/analyticsService'); const DatabaseService = require('../services/databaseService'); const realTimeAnalyticsController = require('../controllers/realTimeAnalyticsController'); const logger = require('../config/logger'); const analyticsService = new AnalyticsService(); const databaseService = new DatabaseService(); // Rate limiting for analytics endpoints const analyticsRateLimit = rateLimit({ windowMs: 15 * 60 * 1000, // 15 minutes max: 100, // limit each IP to 100 requests per windowMs message: 'Too many analytics requests from this IP' }); /** * @route GET /api/analytics/dashboard * @desc Get comprehensive dashboard analytics * @access Private (Admin, Supervisor) */ router.get('/dashboard', [ auth, analyticsRateLimit, query('time_range').optional().isIn(['1h', '4h', '24h', '7d', '30d']), query('include_predictions').optional().isBoolean() ], async (req, res) => { try { // Check permissions if (!['admin', 'supervisor'].includes(req.user.role)) { return res.status(403).json({ success: false, error: 'Insufficient permissions for dashboard analytics' }); } const errors = validationResult(req); if (!errors.isEmpty()) { return res.status(400).json({ success: false, error: 'Validation errors', details: errors.array() }); } const { time_range = '24h', include_predictions = true } = req.query; // Get core metrics const coreMetrics = await analyticsService.getCoreMetrics(time_range); // Get performance trends const performanceTrends = await analyticsService.getPerformanceTrends(time_range); // Get agent statistics const agentStats = await analyticsService.getAgentStatistics(time_range); // Get customer satisfaction metrics const satisfactionMetrics = await analyticsService.getSatisfactionMetrics(time_range); // Get system health const systemHealth = await databaseService.getSystemHealthScore(); // Get predictions if requested let predictions = null; if (include_predictions) { predictions = await analyticsService.getPredictionSummary(); } // Calculate business impact const businessImpact = await analyticsService.calculateBusinessImpact(time_range); res.json({ success: true, data: { core_metrics: coreMetrics, performance_trends: performanceTrends, agent_statistics: agentStats, satisfaction_metrics: satisfactionMetrics, system_health: systemHealth, predictions, business_impact: businessImpact, time_range, generated_at: new Date().toISOString() } }); } catch (error) { logger.error('Error getting dashboard analytics:', error); res.status(500).json({ success: false, error: 'Failed to get dashboard analytics' }); } }); /** * @route GET /api/analytics/agent-performance * @desc Get detailed agent performance analytics * @access Private */ router.get('/agent-performance', [ auth, analyticsRateLimit, query('agent_id').optional().isString(), query('time_range').optional().isIn(['1d', '7d', '30d', '90d']), query('include_comparisons').optional().isBoolean() ], async (req, res) => { try { const { agent_id, time_range = '30d', include_comparisons = false } = req.query; // For agents, only allow viewing their own performance let targetAgentId = agent_id; if (req.user.role === 'agent') { targetAgentId = req.user.id; } // Get agent performance metrics const performanceMetrics = await analyticsService.getAgentPerformanceMetrics( targetAgentId, time_range ); // Get skill development trends const skillTrends = await analyticsService.getSkillDevelopmentTrends( targetAgentId, time_range ); // Get simulation performance const simulationPerformance = await analyticsService.getSimulationPerformance( targetAgentId, time_range ); // Get customer feedback const customerFeedback = await analyticsService.getCustomerFeedback( targetAgentId, time_range ); // Get comparisons if requested and user has permission let comparisons = null; if (include_comparisons && ['admin', 'supervisor'].includes(req.user.role)) { comparisons = await analyticsService.getAgentComparisons(targetAgentId, time_range); } // Get improvement recommendations const recommendations = await analyticsService.getImprovementRecommendations( targetAgentId, performanceMetrics ); res.json({ success: true, data: { agent_id: targetAgentId, performance_metrics: performanceMetrics, skill_trends: skillTrends, simulation_performance: simulationPerformance, customer_feedback: customerFeedback, comparisons, recommendations, time_range, generated_at: new Date().toISOString() } }); } catch (error) { logger.error('Error getting agent performance analytics:', error); res.status(500).json({ success: false, error: 'Failed to get agent performance analytics' }); } }); /** * @route GET /api/analytics/customer-insights * @desc Get customer behavior and satisfaction insights * @access Private (Admin, Supervisor, Analyst) */ router.get('/customer-insights', [ auth, analyticsRateLimit, query('time_range').optional().isIn(['1d', '7d', '30d', '90d']), query('segment').optional().isString(), query('include_churn_analysis').optional().isBoolean() ], async (req, res) => { try { // Check permissions if (!['admin', 'supervisor', 'analyst'].includes(req.user.role)) { return res.status(403).json({ success: false, error: 'Insufficient permissions for customer insights' }); } const { time_range = '30d', segment, include_churn_analysis = true } = req.query; // Get customer satisfaction trends const satisfactionTrends = await analyticsService.getCustomerSatisfactionTrends( time_range, segment ); // Get interaction patterns const interactionPatterns = await analyticsService.getCustomerInteractionPatterns( time_range, segment ); // Get sentiment analysis const sentimentAnalysis = await analyticsService.getCustomerSentimentAnalysis( time_range, segment ); // Get escalation patterns const escalationPatterns = await analyticsService.getEscalationPatterns( time_range, segment ); // Get churn analysis if requested let churnAnalysis = null; if (include_churn_analysis) { churnAnalysis = await analyticsService.getChurnAnalysis(time_range, segment); } // Get customer journey insights const journeyInsights = await analyticsService.getCustomerJourneyInsights( time_range, segment ); res.json({ success: true, data: { satisfaction_trends: satisfactionTrends, interaction_patterns: interactionPatterns, sentiment_analysis: sentimentAnalysis, escalation_patterns: escalationPatterns, churn_analysis: churnAnalysis, journey_insights: journeyInsights, time_range, segment, generated_at: new Date().toISOString() } }); } catch (error) { logger.error('Error getting customer insights:', error); res.status(500).json({ success: false, error: 'Failed to get customer insights' }); } }); /** * @route GET /api/analytics/operational-efficiency * @desc Get operational efficiency metrics * @access Private (Admin, Supervisor) */ router.get('/operational-efficiency', [ auth, analyticsRateLimit, query('time_range').optional().isIn(['1d', '7d', '30d', '90d']), query('team_id').optional().isString() ], async (req, res) => { try { // Check permissions if (!['admin', 'supervisor'].includes(req.user.role)) { return res.status(403).json({ success: false, error: 'Insufficient permissions for operational efficiency metrics' }); } const { time_range = '30d', team_id } = req.query; // Get efficiency metrics const efficiencyMetrics = await analyticsService.getOperationalEfficiencyMetrics( time_range, team_id ); // Get resource utilization const resourceUtilization = await analyticsService.getResourceUtilization( time_range, team_id ); // Get cost analysis const costAnalysis = await analyticsService.getCostAnalysis(time_range, team_id); // Get productivity trends const productivityTrends = await analyticsService.getProductivityTrends( time_range, team_id ); // Get bottleneck analysis const bottleneckAnalysis = await analyticsService.getBottleneckAnalysis( time_range, team_id ); // Get optimization recommendations const optimizationRecommendations = await analyticsService.getOptimizationRecommendations( efficiencyMetrics, resourceUtilization, bottleneckAnalysis ); res.json({ success: true, data: { efficiency_metrics: efficiencyMetrics, resource_utilization: resourceUtilization, cost_analysis: costAnalysis, productivity_trends: productivityTrends, bottleneck_analysis: bottleneckAnalysis, optimization_recommendations: optimizationRecommendations, time_range, team_id, generated_at: new Date().toISOString() } }); } catch (error) { logger.error('Error getting operational efficiency metrics:', error); res.status(500).json({ success: false, error: 'Failed to get operational efficiency metrics' }); } }); /** * @route GET /api/analytics/simulation-insights * @desc Get simulation training insights and effectiveness * @access Private */ router.get('/simulation-insights', [ auth, analyticsRateLimit, query('time_range').optional().isIn(['1d', '7d', '30d', '90d']), query('agent_id').optional().isString(), query('scenario_category').optional().isString() ], async (req, res) => { try { const { time_range = '30d', agent_id, scenario_category } = req.query; // For agents, only allow viewing their own simulation insights let targetAgentId = agent_id; if (req.user.role === 'agent') { targetAgentId = req.user.id; } // Get simulation effectiveness metrics const effectivenessMetrics = await analyticsService.getSimulationEffectiveness( time_range, targetAgentId, scenario_category ); // Get learning progression const learningProgression = await analyticsService.getLearningProgression( targetAgentId, time_range ); // Get scenario performance const scenarioPerformance = await analyticsService.getScenarioPerformance( time_range, targetAgentId, scenario_category ); // Get skill development insights const skillDevelopment = await analyticsService.getSkillDevelopmentInsights( targetAgentId, time_range ); // Get training recommendations const trainingRecommendations = await analyticsService.getTrainingRecommendations( targetAgentId, effectivenessMetrics, skillDevelopment ); res.json({ success: true, data: { effectiveness_metrics: effectivenessMetrics, learning_progression: learningProgression, scenario_performance: scenarioPerformance, skill_development: skillDevelopment, training_recommendations: trainingRecommendations, time_range, agent_id: targetAgentId, scenario_category, generated_at: new Date().toISOString() } }); } catch (error) { logger.error('Error getting simulation insights:', error); res.status(500).json({ success: false, error: 'Failed to get simulation insights' }); } }); /** * @route GET /api/analytics/real-time-metrics * @desc Get real-time system and performance metrics * @access Private (Admin, Supervisor) */ router.get('/real-time-metrics', [ auth, analyticsRateLimit ], async (req, res) => { try { // Check permissions if (!['admin', 'supervisor'].includes(req.user.role)) { return res.status(403).json({ success: false, error: 'Insufficient permissions for real-time metrics' }); } // Get current system metrics const systemMetrics = await analyticsService.getCurrentSystemMetrics(); // Get active sessions const activeSessions = await analyticsService.getActiveSessions(); // Get current queue status const queueStatus = await analyticsService.getCurrentQueueStatus(); // Get agent availability const agentAvailability = await analyticsService.getAgentAvailability(); // Get real-time alerts const activeAlerts = await analyticsService.getActiveAlerts(); // Get performance indicators const performanceIndicators = await analyticsService.getRealTimePerformanceIndicators(); res.json({ success: true, data: { system_metrics: systemMetrics, active_sessions: activeSessions, queue_status: queueStatus, agent_availability: agentAvailability, active_alerts: activeAlerts, performance_indicators: performanceIndicators, timestamp: new Date().toISOString() } }); } catch (error) { logger.error('Error getting real-time metrics:', error); res.status(500).json({ success: false, error: 'Failed to get real-time metrics' }); } }); /** * @route POST /api/analytics/custom-report * @desc Generate custom analytics report * @access Private (Admin, Supervisor, Analyst) */ router.post('/custom-report', [ auth, analyticsRateLimit, body('report_type').notEmpty().isString(), body('parameters').isObject(), body('time_range').optional().isIn(['1d', '7d', '30d', '90d', '1y']), body('format').optional().isIn(['json', 'csv', 'pdf']) ], async (req, res) => { try { // Check permissions if (!['admin', 'supervisor', 'analyst'].includes(req.user.role)) { return res.status(403).json({ success: false, error: 'Insufficient permissions for custom reports' }); } const errors = validationResult(req); if (!errors.isEmpty()) { return res.status(400).json({ success: false, error: 'Validation errors', details: errors.array() }); } const { report_type, parameters, time_range = '30d', format = 'json' } = req.body; // Generate custom report const report = await analyticsService.generateCustomReport({ report_type, parameters, time_range, format, requested_by: req.user.id }); // If PDF format, return file download if (format === 'pdf') { res.setHeader('Content-Type', 'application/pdf'); res.setHeader('Content-Disposition', `attachment; filename="${report.filename}"`); return res.send(report.data); } // If CSV format, return file download if (format === 'csv') { res.setHeader('Content-Type', 'text/csv'); res.setHeader('Content-Disposition', `attachment; filename="${report.filename}"`); return res.send(report.data); } // Return JSON response res.json({ success: true, data: report, generated_at: new Date().toISOString() }); } catch (error) { logger.error('Error generating custom report:', error); res.status(500).json({ success: false, error: 'Failed to generate custom report' }); } }); /** * @route GET /api/analytics/dashboard * @desc Get comprehensive dashboard analytics with real-time data * @access Private (Admin, Supervisor, Analyst) */ router.get('/dashboard', [ auth, analyticsRateLimit, query('time_range').optional().isIn(['1d', '7d', '30d', '90d']), query('include_predictions').optional().isBoolean(), query('metrics').optional().isString() ], realTimeAnalyticsController.getDashboardAnalytics); /** * @route GET /api/analytics/realtime * @desc Get real-time metrics and system status * @access Private (Admin, Supervisor, Analyst) */ router.get('/realtime', [ auth, analyticsRateLimit ], realTimeAnalyticsController.getRealTimeMetrics); /** * @route GET /api/analytics/agent-performance-realtime * @desc Get real-time agent performance analytics * @access Private */ router.get('/agent-performance-realtime', [ auth, analyticsRateLimit, query('agent_id').optional().isString(), query('time_range').optional().isIn(['1d', '7d', '30d', '90d']), query('include_comparisons').optional().isBoolean() ], realTimeAnalyticsController.getAgentPerformance); /** * @route GET /api/analytics/customer-insights-realtime * @desc Get real-time customer behavior and satisfaction insights * @access Private (Admin, Supervisor, Analyst) */ router.get('/customer-insights-realtime', [ auth, analyticsRateLimit, query('time_range').optional().isIn(['1d', '7d', '30d', '90d']), query('segment').optional().isString(), query('include_churn_analysis').optional().isBoolean() ], realTimeAnalyticsController.getCustomerInsights); /** * @route GET /api/analytics/operational-efficiency-realtime * @desc Get real-time operational efficiency metrics * @access Private (Admin, Supervisor) */ router.get('/operational-efficiency-realtime', [ auth, analyticsRateLimit, query('time_range').optional().isIn(['1d', '7d', '30d', '90d']), query('team_id').optional().isString() ], realTimeAnalyticsController.getOperationalEfficiency); /** * @route POST /api/analytics/trigger-update * @desc Manually trigger real-time metrics update * @access Private (Admin, Supervisor) */ router.post('/trigger-update', [ auth, analyticsRateLimit ], realTimeAnalyticsController.triggerMetricsUpdate); module.exports = router;