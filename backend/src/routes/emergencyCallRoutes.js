/** * ============================================= * EMERGENCY CALL ROUTES * API endpoints for emergency call handling * Includes validation, rate limiting, and security * ============================================= */ const express = require('express'); const { body, param, query } = require('express-validator'); const rateLimit = require('express-rate-limit'); const authMiddleware = require('../middleware/authMiddleware'); const emergencyCallController = require('../controllers/emergencyCallController'); const logger = require('../config/logger'); const { emergencyCallRateLimit, validateEmergencyCallPermissions, auditEmergencyCall, frenchTelecomCompliance, sanitizeEmergencyCallData, validateEmergencyCallInput, setEmergencyCallSecurityHeaders } = require('../middleware/emergencyCallSecurity'); const router = express.Router(); // Emergency call rate limiting - more restrictive const emergencyCallLimiter = rateLimit({ windowMs: 5 * 60 * 1000, // 5 minutes max: 3, // Maximum 3 emergency calls per 5 minutes per IP message: { error: 'Too many emergency calls', message: 'Trop d\'appels d\'urgence. Veuillez patienter 5 minutes.', retryAfter: 300 }, standardHeaders: true, legacyHeaders: false, handler: (req, res) => { logger.warn(`Emergency call rate limit exceeded`, { ip: req.ip, userId: req.user?.id, userAgent: req.get('User-Agent') }); res.status(429).json({ success: false, error: 'Emergency call rate limit exceeded', message: 'Trop d\'appels d\'urgence récents. Veuillez patienter.', retryAfter: 300 }); } }); // General rate limiting for emergency endpoints const generalEmergencyLimiter = rateLimit({ windowMs: 1 * 60 * 1000, // 1 minute max: 10, // Maximum 10 requests per minute message: { error: 'Too many requests', message: 'Trop de requêtes. Veuillez patienter.', retryAfter: 60 } }); // Validation schemas const initiateEmergencyCallValidation = [ body('userId') .isMongoId() .withMessage('Invalid user ID format'), body('urgencyLevel') .optional() .isIn(['low', 'normal', 'medium', 'high', 'urgent', 'critical']) .withMessage('Invalid urgency level'), body('description') .optional() .isLength({ min: 10, max: 1000 }) .withMessage('Description must be between 10 and 1000 characters') .trim() .escape(), body('conversationHistory') .optional() .isArray({ max: 50 }) .withMessage('Conversation history must be an array with maximum 50 messages'), body('conversationHistory.*.content') .optional() .isLength({ max: 500 }) .withMessage('Message content must not exceed 500 characters') .trim() .escape(), body('conversationHistory.*.sender') .optional() .isIn(['user', 'bot', 'agent']) .withMessage('Invalid sender type') ]; const escalateCallValidation = [ param('emergencyCallId') .isUUID(4) .withMessage('Invalid emergency call ID format'), body('reason') .optional() .isLength({ min: 5, max: 200 }) .withMessage('Reason must be between 5 and 200 characters') .trim() .escape(), body('agentPreference') .optional() .isMongoId() .withMessage('Invalid agent ID format') ]; const connectToAgentValidation = [ param('emergencyCallId') .isUUID(4) .withMessage('Invalid emergency call ID format'), body('agentId') .isMongoId() .withMessage('Invalid agent ID format') ]; // Middleware to log emergency call attempts const logEmergencyCallAttempt = (req, res, next) => { logger.info('Emergency call attempt', { ip: req.ip, userId: req.user?.id, userAgent: req.get('User-Agent'), endpoint: req.path, method: req.method, timestamp: new Date() }); next(); }; /** * @route POST /api/emergency-calls/initiate * @desc Initiate emergency call * @access Private */ router.post('/initiate', setEmergencyCallSecurityHeaders, emergencyCallRateLimit, authMiddleware, validateEmergencyCallPermissions, auditEmergencyCall('emergency_call_initiate'), frenchTelecomCompliance, sanitizeEmergencyCallData, validateEmergencyCallInput, logEmergencyCallAttempt, emergencyCallController.initiateEmergencyCall ); /** * @route POST /api/emergency-calls/:emergencyCallId/escalate * @desc Escalate emergency call to human agent * @access Private */ router.post('/:emergencyCallId/escalate', setEmergencyCallSecurityHeaders, generalEmergencyLimiter, authMiddleware, validateEmergencyCallPermissions, auditEmergencyCall('emergency_call_escalate'), sanitizeEmergencyCallData, logEmergencyCallAttempt, escalateCallValidation, emergencyCallController.escalateToHumanAgent ); /** * @route POST /api/emergency-calls/:emergencyCallId/connect-agent * @desc Connect emergency call to specific human agent * @access Private (Agent only) */ router.post('/:emergencyCallId/connect-agent', generalEmergencyLimiter, authMiddleware, logEmergencyCallAttempt, connectToAgentValidation, // Additional middleware to check if user is an agent (req, res, next) => { if (req.user.role !== 'agent' && req.user.role !== 'admin') { return res.status(403).json({ success: false, error: 'Access denied', message: 'Only agents can connect to emergency calls' }); } next(); }, emergencyCallController.connectToHumanAgent ); /** * @route GET /api/emergency-calls/:emergencyCallId/status * @desc Get emergency call status * @access Private */ router.get('/:emergencyCallId/status', generalEmergencyLimiter, authMiddleware, [ param('emergencyCallId') .isUUID(4) .withMessage('Invalid emergency call ID format') ], emergencyCallController.getEmergencyCallStatus ); /** * @route GET /api/emergency-calls/queue/stats * @desc Get emergency call queue statistics * @access Private (Agent/Admin only) */ router.get('/queue/stats', generalEmergencyLimiter, authMiddleware, (req, res, next) => { if (req.user.role !== 'agent' && req.user.role !== 'admin') { return res.status(403).json({ success: false, error: 'Access denied', message: 'Only agents and admins can view queue statistics' }); } next(); }, async (req, res) => { try { const EmergencyCall = require('../models/EmergencyCall'); const [activeStats, queueStats] = await Promise.all([ EmergencyCall.getActiveEmergencyCalls(), EmergencyCall.getQueueStatistics() ]); res.json({ success: true, stats: { activeCalls: activeStats.length, queueBreakdown: queueStats, timestamp: new Date() } }); } catch (error) { logger.error('Failed to get queue statistics:', error); res.status(500).json({ success: false, error: 'Failed to get queue statistics' }); } } ); /** * @route GET /api/emergency-calls/health * @desc Emergency call service health check * @access Public */ router.get('/health', (req, res) => { res.json({ success: true, service: 'Emergency Call Service', status: 'operational', timestamp: new Date(), endpoints: { emergencyHotline: '9198', humanSupportLine: '**********' }, compliance: { frenchTelecomRegulations: 'compliant', gdpr: 'compliant', dataRetention: '90 days' } }); }); // Error handling middleware specific to emergency calls router.use((error, req, res, next) => { logger.error('Emergency call route error:', { error: error.message, stack: error.stack, path: req.path, method: req.method, userId: req.user?.id, ip: req.ip }); // Always provide a response for emergency calls if (!res.headersSent) { res.status(500).json({ success: false, error: 'Emergency call service error', message: 'Une erreur est survenue. Veuillez réessayer ou contacter le support.', emergencyContact: '9198' }); } }); module.exports = router;