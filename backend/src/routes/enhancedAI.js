/** * ============================================= * [FEATURE] ENHANCED AI API ROUTES * Contextual suggestions and sentiment analysis endpoints * Real-time AI assistance for customer service agents * ============================================= */ const express = require('express'); const router = express.Router(); const { body, param, query, validationResult } = require('express-validator'); const auth = require('../middleware/auth'); const rateLimit = require('../middleware/rateLimit'); const AIService = require('../services/aiService'); const SentimentService = require('../services/sentimentService'); const TemplateService = require('../services/templateService'); const logger = require('../config/logger'); const aiService = new AIService(); const sentimentService = new SentimentService(); const templateService = new TemplateService(); // Rate limiting for AI endpoints const aiRateLimit = rateLimit({ windowMs: 15 * 60 * 1000, // 15 minutes max: 200, // limit each IP to 200 requests per windowMs message: 'Too many AI requests from this IP' }); /** * @route POST /api/enhanced-ai/suggestions/contextual * @desc Generate contextual response suggestions * @access Private */ router.post('/suggestions/contextual', [ auth, aiRateLimit, body('ticket_id').optional().isString(), body('customer_id').optional().isString(), body('conversation_context').isObject(), body('urgency_level').optional().isIn(['low', 'medium', 'high', 'critical']), body('platform').optional().isIn(['whatsapp', 'email', 'chat', 'phone']) ], async (req, res) => { try { const errors = validationResult(req); if (!errors.isEmpty()) { return res.status(400).json({ success: false, error: 'Validation errors', details: errors.array() }); } const { ticket_id, customer_id, conversation_context, urgency_level = 'medium', platform = 'whatsapp' } = req.body; const agentId = req.user.id; // Get customer and agent profiles const customerProfile = customer_id ? await aiService.getCustomerProfile(customer_id) : {}; const agentProfile = await aiService.getAgentProfile(agentId); // Generate contextual suggestions const suggestions = await aiService.generateContextualSuggestions({ ticket_id, customer_profile: customerProfile, agent_profile: agentProfile, conversation_context, urgency_level, platform }); // Log suggestion generation for analytics await aiService.logSuggestionGeneration(agentId, { suggestion_count: suggestions.length, context_type: 'contextual', urgency_level, platform }); res.json({ success: true, data: { suggestions, context: { urgency_level, platform, agent_skill_level: agentProfile.skill_level, customer_tier: customerProfile.tier }, generated_at: new Date().toISOString() } }); } catch (error) { logger.error('Error generating contextual suggestions:', error); res.status(500).json({ success: false, error: 'Failed to generate contextual suggestions' }); } }); /** * @route POST /api/enhanced-ai/sentiment/analyze * @desc Analyze sentiment of conversation messages * @access Private */ router.post('/sentiment/analyze', [ auth, aiRateLimit, body('messages').isArray().notEmpty(), body('conversation_id').optional().isString(), body('real_time').optional().isBoolean() ], async (req, res) => { try { const errors = validationResult(req); if (!errors.isEmpty()) { return res.status(400).json({ success: false, error: 'Validation errors', details: errors.array() }); } const { messages, conversation_id, real_time = false } = req.body; // Analyze sentiment const sentimentAnalysis = await sentimentService.analyzeSentiment(messages); // Calculate escalation risk const escalationRisk = await sentimentService.calculateEscalationRisk( sentimentAnalysis, messages ); // Generate sentiment trends if conversation_id provided let sentimentTrends = null; if (conversation_id) { sentimentTrends = await sentimentService.getSentimentTrends(conversation_id); } // Generate recommendations based on sentiment const recommendations = await sentimentService.generateSentimentRecommendations( sentimentAnalysis, escalationRisk ); // Save analysis for real-time monitoring if (real_time && conversation_id) { await sentimentService.saveSentimentAnalysis({ conversation_id, agent_id: req.user.id, sentiment_analysis: sentimentAnalysis, escalation_risk: escalationRisk }); } res.json({ success: true, data: { sentiment_analysis: sentimentAnalysis, escalation_risk: escalationRisk, sentiment_trends: sentimentTrends, recommendations, analyzed_at: new Date().toISOString() } }); } catch (error) { logger.error('Error analyzing sentiment:', error); res.status(500).json({ success: false, error: 'Failed to analyze sentiment' }); } }); /** * @route POST /api/enhanced-ai/escalation/analyze * @desc Analyze escalation risk and provide recommendations * @access Private */ router.post('/escalation/analyze', [ auth, aiRateLimit, body('ticket_id').notEmpty().isString(), body('conversation_history').isArray(), body('customer_profile').optional().isObject(), body('current_sentiment').optional().isNumeric() ], async (req, res) => { try { const errors = validationResult(req); if (!errors.isEmpty()) { return res.status(400).json({ success: false, error: 'Validation errors', details: errors.array() }); } const { ticket_id, conversation_history, customer_profile = {}, current_sentiment } = req.body; const agentId = req.user.id; const agentProfile = await aiService.getAgentProfile(agentId); // Generate escalation analysis const escalationAnalysis = await aiService.generateEscalationRecommendation({ ticket_id, conversation_history, customer_profile, current_sentiment, agent_skill_level: agentProfile.skill_level }); // Get prevention strategies const preventionStrategies = await aiService.getEscalationPreventionStrategies( escalationAnalysis.risk_level, customer_profile, agentProfile ); // Generate immediate action items const actionItems = await aiService.generateImmediateActionItems( escalationAnalysis, conversation_history ); res.json({ success: true, data: { escalation_analysis: escalationAnalysis, prevention_strategies: preventionStrategies, action_items: actionItems, analyzed_at: new Date().toISOString() } }); } catch (error) { logger.error('Error analyzing escalation risk:', error); res.status(500).json({ success: false, error: 'Failed to analyze escalation risk' }); } }); /** * @route GET /api/enhanced-ai/templates/personalized * @desc Get personalized templates for agent * @access Private */ router.get('/templates/personalized', [ auth, query('category').optional().isString(), query('limit').optional().isInt({ min: 1, max: 100 }).toInt() ], async (req, res) => { try { const errors = validationResult(req); if (!errors.isEmpty()) { return res.status(400).json({ success: false, error: 'Validation errors', details: errors.array() }); } const { category, limit = 50 } = req.query; const agentId = req.user.id; // Get personalized templates const templates = await templateService.getPersonalizedTemplates(agentId, category); // Get template usage statistics const usageStats = await templateService.getTemplateUsageStats(agentId); // Get recommended templates based on agent performance const recommendedTemplates = await templateService.getRecommendedTemplates(agentId, category); res.json({ success: true, data: { templates: templates.slice(0, limit), usage_stats: usageStats, recommended_templates: recommendedTemplates, total_count: templates.length } }); } catch (error) { logger.error('Error getting personalized templates:', error); res.status(500).json({ success: false, error: 'Failed to get personalized templates' }); } }); /** * @route POST /api/enhanced-ai/templates/customize * @desc Customize template for agent * @access Private */ router.post('/templates/customize', [ auth, body('base_template_id').optional().isString(), body('name').notEmpty().isString().isLength({ min: 1, max: 100 }), body('category').notEmpty().isString(), body('content').notEmpty().isString().isLength({ min: 1, max: 2000 }), body('customization').optional().isString() ], async (req, res) => { try { const errors = validationResult(req); if (!errors.isEmpty()) { return res.status(400).json({ success: false, error: 'Validation errors', details: errors.array() }); } const { base_template_id, name, category, content, customization = '' } = req.body; const agentId = req.user.id; // Create customized template const customTemplate = await templateService.createCustomTemplate({ agent_id: agentId, base_template_id, name, category, content, customization }); res.status(201).json({ success: true, data: customTemplate }); } catch (error) { logger.error('Error customizing template:', error); res.status(500).json({ success: false, error: 'Failed to customize template' }); } }); /** * @route POST /api/enhanced-ai/feedback/suggestion * @desc Submit feedback on AI suggestion * @access Private */ router.post('/feedback/suggestion', [ auth, body('suggestion_id').notEmpty().isString(), body('feedback').isObject(), body('feedback.rating').isInt({ min: 1, max: 5 }), body('feedback.helpful').isBoolean(), body('feedback.used_as_is').isBoolean(), body('feedback.outcome').optional().isIn(['successful', 'unsuccessful', 'neutral']), body('feedback.notes').optional().isString().isLength({ max: 500 }) ], async (req, res) => { try { const errors = validationResult(req); if (!errors.isEmpty()) { return res.status(400).json({ success: false, error: 'Validation errors', details: errors.array() }); } const { suggestion_id, feedback } = req.body; const agentId = req.user.id; // Submit feedback const feedbackResult = await aiService.submitSuggestionFeedback( suggestion_id, agentId, feedback ); // Update learning model const learningUpdate = await aiService.updateLearningModel(agentId, { suggestion_id, feedback }); res.json({ success: true, data: { feedback_id: feedbackResult.feedback_id, learning_update: learningUpdate, submitted_at: new Date().toISOString() } }); } catch (error) { logger.error('Error submitting suggestion feedback:', error); res.status(500).json({ success: false, error: 'Failed to submit suggestion feedback' }); } }); /** * @route GET /api/enhanced-ai/performance/insights * @desc Get AI performance insights for agent * @access Private */ router.get('/performance/insights', [ auth, query('time_range').optional().isIn(['1d', '7d', '30d']), query('include_trends').optional().isBoolean() ], async (req, res) => { try { const { time_range = '7d', include_trends = true } = req.query; const agentId = req.user.id; // Get suggestion performance const suggestionPerformance = await aiService.getSuggestionPerformance(agentId, time_range); // Get sentiment analysis accuracy const sentimentAccuracy = await sentimentService.getSentimentAccuracy(agentId, time_range); // Get escalation prediction accuracy const escalationAccuracy = await aiService.getEscalationPredictionAccuracy(agentId, time_range); // Get performance trends let trends = null; if (include_trends) { trends = await aiService.getPerformanceTrends(agentId, time_range); } // Generate improvement recommendations const improvementRecommendations = await aiService.generateImprovementRecommendations( agentId, suggestionPerformance, sentimentAccuracy, escalationAccuracy ); res.json({ success: true, data: { suggestion_performance: suggestionPerformance, sentiment_accuracy: sentimentAccuracy, escalation_accuracy: escalationAccuracy, trends, improvement_recommendations: improvementRecommendations, time_range, generated_at: new Date().toISOString() } }); } catch (error) { logger.error('Error getting AI performance insights:', error); res.status(500).json({ success: false, error: 'Failed to get AI performance insights' }); } }); /** * @route PUT /api/enhanced-ai/preferences * @desc Update AI preferences for agent * @access Private */ router.put('/preferences', [ auth, body('suggestion_frequency').optional().isIn(['low', 'medium', 'high']), body('auto_sentiment_analysis').optional().isBoolean(), body('escalation_alerts').optional().isBoolean(), body('template_suggestions').optional().isBoolean(), body('learning_mode').optional().isIn(['passive', 'active', 'aggressive']) ], async (req, res) => { try { const errors = validationResult(req); if (!errors.isEmpty()) { return res.status(400).json({ success: false, error: 'Validation errors', details: errors.array() }); } const preferences = req.body; const agentId = req.user.id; // Update preferences const updatedPreferences = await aiService.updateAgentPreferences(agentId, preferences); res.json({ success: true, data: { preferences: updatedPreferences, updated_at: new Date().toISOString() } }); } catch (error) { logger.error('Error updating AI preferences:', error); res.status(500).json({ success: false, error: 'Failed to update AI preferences' }); } }); /** * @route GET /api/enhanced-ai/conversation/:id/summary * @desc Get AI-generated conversation summary * @access Private */ router.get('/conversation/:id/summary', [ auth, param('id').notEmpty().isString() ], async (req, res) => { try { const { id } = req.params; const agentId = req.user.id; // Get conversation details const conversation = await aiService.getConversationDetails(id); // Verify access if (conversation.agent_id !== agentId && req.user.role !== 'supervisor') { return res.status(403).json({ success: false, error: 'Access denied' }); } // Generate AI summary const summary = await aiService.generateConversationSummary(conversation); // Get key insights const insights = await aiService.extractConversationInsights(conversation); // Calculate performance metrics const performanceMetrics = await aiService.calculateConversationPerformance(conversation); res.json({ success: true, data: { conversation_id: id, summary, insights, performance_metrics: performanceMetrics, generated_at: new Date().toISOString() } }); } catch (error) { logger.error('Error generating conversation summary:', error); res.status(500).json({ success: false, error: 'Failed to generate conversation summary' }); } }); module.exports = router;