/** * ============================================= * SIMULATION API ROUTES * Agent training simulation endpoints * Integrates with existing ML and AI services * ============================================= */ const express = require('express'); const router = express.Router(); const { body, param, query, validationResult } = require('express-validator'); const auth = require('../middleware/auth'); const SimulationService = require('../services/simulationService'); const AIService = require('../services/aiService'); const logger = require('../config/logger'); // Initialize services const simulationService = new SimulationService(); const aiService = new AIService(); /** * @route POST /api/simulation/scenarios * @desc Fetch training scenarios with filtering * @access Private */ router.post('/scenarios', [ auth, body('difficulty').optional().isIn(['beginner', 'intermediate', 'expert']), body('category').optional().isIn(['billing', 'technical', 'sales', 'retention', 'complaint']), body('tags').optional().isArray(), body('limit').optional().isInt({ min: 1, max: 100 }).toInt(), body('offset').optional().isInt({ min: 0 }).toInt() ], async (req, res) => { try { // Validate request const errors = validationResult(req); if (!errors.isEmpty()) { return res.status(400).json({ success: false, message: 'Validation errors', errors: errors.array() }); } const { difficulty, category, tags, limit = 20, offset = 0 } = req.body; const agentId = req.user.id; // Get agent skill level for scenario recommendation const agentProfile = await simulationService.getAgentProfile(agentId); // Fetch scenarios with filtering const scenarios = await simulationService.getScenarios({ difficulty, category, tags, agentSkillLevel: agentProfile.skillLevel, limit, offset }); // Add personalized recommendations const recommendedScenarios = await simulationService.getRecommendedScenarios(agentId, 5); logger.info(`Fetched ${scenarios.length} scenarios for agent ${agentId}`, { agentId, filters: { difficulty, category, tags }, resultCount: scenarios.length }); res.json({ success: true, data: { scenarios, recommended: recommendedScenarios, total: scenarios.length, filters: { difficulty, category, tags }, pagination: { limit, offset } } }); } catch (error) { logger.error('Error fetching scenarios:', error); res.status(500).json({ success: false, message: 'Failed to fetch scenarios', error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error' }); } }); /** * @route POST /api/simulation/start * @desc Initialize new simulation session * @access Private */ router.post('/start', [ auth, body('scenario_id').notEmpty().isString(), body('settings').optional().isObject() ], async (req, res) => { try { const errors = validationResult(req); if (!errors.isEmpty()) { return res.status(400).json({ success: false, message: 'Validation errors', errors: errors.array() }); } const { scenario_id, settings = {} } = req.body; const agentId = req.user.id; // Check if agent has active simulation const activeSession = await simulationService.getActiveSession(agentId); if (activeSession) { return res.status(409).json({ success: false, message: 'Agent already has an active simulation session', activeSessionId: activeSession.id }); } // Validate scenario exists const scenario = await simulationService.getScenario(scenario_id); if (!scenario) { return res.status(404).json({ success: false, message: 'Scenario not found' }); } // Create simulation session const session = await simulationService.createSession({ scenarioId: scenario_id, agentId, settings, scenario }); // Initialize AI customer persona const customerPersona = await aiService.generateCustomerPersona(scenario.customer_profile); // Send initial customer message const initialMessage = await aiService.generateInitialMessage(customerPersona, scenario); await simulationService.addMessage(session.id, { sender: 'customer', content: initialMessage, timestamp: new Date().toISOString(), metadata: { persona: customerPersona, scenario_context: scenario.context } }); logger.info(`Started simulation session for agent ${agentId}`, { sessionId: session.id, scenarioId: scenario_id, agentId }); res.status(201).json({ success: true, data: { session: { ...session, initial_message: initialMessage, customer_persona: customerPersona } } }); } catch (error) { logger.error('Error starting simulation:', error); res.status(500).json({ success: false, message: 'Failed to start simulation', error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error' }); } }); /** * @route POST /api/simulation/message * @desc Handle agent-customer message exchange * @access Private */ router.post('/message', [ auth, body('session_id').notEmpty().isString(), body('message').notEmpty().isString().isLength({ min: 1, max: 2000 }) ], async (req, res) => { try { const errors = validationResult(req); if (!errors.isEmpty()) { return res.status(400).json({ success: false, message: 'Validation errors', errors: errors.array() }); } const { session_id, message } = req.body; const agentId = req.user.id; // Validate session ownership const session = await simulationService.getSession(session_id); if (!session || session.agent_id !== agentId) { return res.status(404).json({ success: false, message: 'Session not found or access denied' }); } if (session.status !== 'active') { return res.status(400).json({ success: false, message: 'Session is not active' }); } const startTime = Date.now(); // Add agent message const agentMessage = await simulationService.addMessage(session_id, { sender: 'agent', content: message, timestamp: new Date().toISOString(), metadata: { response_time: startTime - new Date(session.last_customer_message || session.created_at).getTime() } }); // Analyze agent response for performance metrics const responseAnalysis = await aiService.analyzeAgentResponse({ message, context: session.context, conversation_history: session.messages, customer_profile: session.customer_profile }); // Generate AI suggestions for improvement const suggestions = await aiService.generateSuggestions({ agent_message: message, session_context: session, analysis: responseAnalysis }); // Generate customer response const customerResponse = await aiService.generateCustomerResponse({ agent_message: message, customer_persona: session.customer_persona, conversation_history: session.messages, scenario: session.scenario }); // Add customer response const customerMessage = await simulationService.addMessage(session_id, { sender: 'customer', content: customerResponse.message, timestamp: new Date().toISOString(), metadata: { sentiment: customerResponse.sentiment, satisfaction: customerResponse.satisfaction, escalation_risk: customerResponse.escalation_risk } }); // Update performance metrics const updatedMetrics = await simulationService.updatePerformanceMetrics(session_id, { response_analysis: responseAnalysis, customer_feedback: customerResponse, response_time: agentMessage.metadata.response_time }); // Generate real-time feedback const feedback = await aiService.generateRealTimeFeedback({ agent_performance: responseAnalysis, customer_reaction: customerResponse, session_progress: updatedMetrics }); logger.info(`Processed message exchange in session ${session_id}`, { sessionId: session_id, agentId, responseTime: Date.now() - startTime, customerSatisfaction: customerResponse.satisfaction }); res.json({ success: true, data: { messages: [agentMessage, customerMessage], performance_metrics: updatedMetrics, ai_suggestions: suggestions, feedback: feedback, customer_state: { sentiment: customerResponse.sentiment, satisfaction: customerResponse.satisfaction, escalation_risk: customerResponse.escalation_risk } } }); } catch (error) { logger.error('Error processing message:', error); res.status(500).json({ success: false, message: 'Failed to process message', error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error' }); } }); /** * @route POST /api/simulation/end * @desc Complete simulation and calculate performance metrics * @access Private */ router.post('/end', [ auth, body('session_id').notEmpty().isString(), body('reason').optional().isIn(['completed', 'abandoned', 'escalated', 'timeout']) ], async (req, res) => { try { const errors = validationResult(req); if (!errors.isEmpty()) { return res.status(400).json({ success: false, message: 'Validation errors', errors: errors.array() }); } const { session_id, reason = 'completed' } = req.body; const agentId = req.user.id; // Validate session const session = await simulationService.getSession(session_id); if (!session || session.agent_id !== agentId) { return res.status(404).json({ success: false, message: 'Session not found or access denied' }); } // Calculate final performance metrics const finalMetrics = await simulationService.calculateFinalMetrics(session_id); // Generate comprehensive feedback const comprehensiveFeedback = await aiService.generateComprehensiveFeedback({ session, final_metrics: finalMetrics, completion_reason: reason }); // Update agent progress const updatedProgress = await simulationService.updateAgentProgress(agentId, { session_completed: true, final_metrics: finalMetrics, scenario_id: session.scenario_id, completion_reason: reason }); // Check for new badges/achievements const newAchievements = await simulationService.checkAchievements(agentId, finalMetrics); // End session await simulationService.endSession(session_id, reason, finalMetrics); logger.info(`Ended simulation session ${session_id}`, { sessionId: session_id, agentId, reason, finalScore: finalMetrics.overall_score, newAchievements: newAchievements.length }); res.json({ success: true, data: { final_metrics: finalMetrics, comprehensive_feedback: comprehensiveFeedback, agent_progress: updatedProgress, new_achievements: newAchievements, session_summary: { duration: finalMetrics.total_duration, messages_exchanged: session.messages.length, completion_reason: reason } } }); } catch (error) { logger.error('Error ending simulation:', error); res.status(500).json({ success: false, message: 'Failed to end simulation', error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error' }); } }); /** * @route GET /api/simulation/progress/:agent_id * @desc Retrieve agent progress and statistics * @access Private */ router.get('/progress/:agent_id', [ auth, param('agent_id').notEmpty().isString() ], async (req, res) => { try { const errors = validationResult(req); if (!errors.isEmpty()) { return res.status(400).json({ success: false, message: 'Validation errors', errors: errors.array() }); } const { agent_id } = req.params; const requestingAgentId = req.user.id; // Check permissions (agents can only view their own progress, supervisors can view all) if (agent_id !== requestingAgentId && !req.user.roles.includes('supervisor')) { return res.status(403).json({ success: false, message: 'Access denied' }); } // Get agent progress const progress = await simulationService.getAgentProgress(agent_id); // Get session history const sessionHistory = await simulationService.getSessionHistory(agent_id, { limit: 50 }); // Get leaderboard position const leaderboard = await simulationService.getLeaderboard({ limit: 100 }); const agentRank = leaderboard.findIndex(entry => entry.agent_id === agent_id) + 1; // Get skill development trends const skillTrends = await simulationService.getSkillTrends(agent_id, { days: 30 }); logger.info(`Retrieved progress for agent ${agent_id}`, { agentId: agent_id, requestedBy: requestingAgentId, sessionsCompleted: progress.completed_sessions, currentRank: agentRank }); res.json({ success: true, data: { progress, session_history: sessionHistory, leaderboard_position: agentRank, skill_trends: skillTrends, statistics: { total_sessions: progress.total_sessions, completed_sessions: progress.completed_sessions, average_score: progress.average_score, badges_earned: progress.badges_earned.length, current_streak: progress.current_streak } } }); } catch (error) { logger.error('Error retrieving agent progress:', error); res.status(500).json({ success: false, message: 'Failed to retrieve progress', error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error' }); } }); /** * @route GET /api/simulation/leaderboard * @desc Get simulation leaderboard * @access Private */ router.get('/leaderboard', [ auth, query('limit').optional().isInt({ min: 1, max: 100 }).toInt(), query('timeframe').optional().isIn(['daily', 'weekly', 'monthly', 'all']) ], async (req, res) => { try { const { limit = 50, timeframe = 'monthly' } = req.query; const leaderboard = await simulationService.getLeaderboard({ limit, timeframe }); res.json({ success: true, data: { leaderboard, timeframe, generated_at: new Date().toISOString() } }); } catch (error) { logger.error('Error retrieving leaderboard:', error); res.status(500).json({ success: false, message: 'Failed to retrieve leaderboard', error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error' }); } }); module.exports = router;