const express = require('express'); const router = express.Router(); const chatController = require('../controllers/chatController'); const { authenticate } = require('../middleware/authMiddleware'); router.use(authenticate); router.post('/conversations/start', chatController.startConversation); router.post('/messages/send', chatController.sendMessage); router.get('/conversations/:conversationId', chatController.getConversationHistory); module.exports = router;