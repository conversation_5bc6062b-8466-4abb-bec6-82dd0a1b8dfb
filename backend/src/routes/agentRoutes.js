const express = require('express'); const router = express.Router(); const agentController = require('../controllers/agentController'); const { authenticate, authorize } = require('../middleware/authMiddleware'); // Middleware : Seuls les agents et admins peuvent accéder router.use(authenticate); router.use(authorize('agent', 'admin')); /** * ROUTES INTERFACE AGENT - CO-PILOTE */ // Tableau de bord agent router.get('/dashboard', agentController.getAgentDashboard); // Gestion des conversations router.post('/conversations/:conversationId/take-control', agentController.takeControl); router.post('/conversations/:conversationId/messages', agentController.sendAgentMessage); router.get('/conversations/:conversationId/suggestions', agentController.getAISuggestions); router.post('/conversations/:conversationId/close', agentController.closeConversation); router.post('/conversations/:conversationId/transfer', agentController.transferConversation); module.exports = router;