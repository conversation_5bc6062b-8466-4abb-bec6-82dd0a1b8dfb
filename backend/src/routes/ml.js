/** * Routes ML pour les APIs de classification intelligente * Free Mobile Chatbot Dashboard - Phase 2 Backend Integration */ const express = require('express'); const router = express.Router(); const mlController = require('../controllers/mlController'); const { authenticate } = require('../middleware/authMiddleware'); const { body, param, query, validationResult } = require('express-validator'); const rateLimit = require('express-rate-limit'); // Alias pour l'authentification const auth = authenticate; // Rate limiting pour les APIs ML const mlRateLimit = rateLimit({ windowMs: 15 * 60 * 1000, // 15 minutes max: 100, // limite de 100 requêtes par fenêtre par IP message: { success: false, error: 'Too many ML requests, please try again later' }, standardHeaders: true, legacyHeaders: false }); // Rate limiting plus strict pour la classification const classificationRateLimit = rateLimit({ windowMs: 1 * 60 * 1000, // 1 minute max: 10, // limite de 10 classifications par minute par IP message: { success: false, error: 'Too many classification requests, please try again later' } }); // Middleware de validation des erreurs const handleValidationErrors = (req, res, next) => { const errors = validationResult(req); if (!errors.isEmpty()) { return res.status(400).json({ success: false, error: 'Validation failed', details: errors.array() }); } next(); }; // Middleware pour vérifier les permissions admin/agent const requireAdminOrAgent = (req, res, next) => { if (!req.user || !['admin', 'agent'].includes(req.user.role)) { return res.status(403).json({ success: false, error: 'Admin or agent role required' }); } next(); }; /** * @route POST /api/v2/ml/classify * @desc Classifier une conversation avec ML * @access Private (Admin/Agent) */ router.post('/classify', classificationRateLimit, auth, requireAdminOrAgent, [ body('conversationId') .isMongoId() .withMessage('Valid conversation ID is required'), body('forceReprocess') .optional() .isBoolean() .withMessage('forceReprocess must be a boolean') ], handleValidationErrors, mlController.classifyConversation ); /** * @route GET /api/v2/ml/queue/priority * @desc Récupérer la queue de priorité des conversations * @access Private (Admin/Agent) */ router.get('/queue/priority', mlRateLimit, auth, requireAdminOrAgent, [ query('limit') .optional() .isInt({ min: 1, max: 100 }) .withMessage('Limit must be between 1 and 100'), query('minPriority') .optional() .isInt({ min: 0, max: 100 }) .withMessage('minPriority must be between 0 and 100'), query('category') .optional() .isIn(['VENTE_OPPORTUNITE', 'RESILIATION_CRITIQUE', 'SUPPORT_URGENT', 'RECLAMATION', 'INFO_SIMPLE']) .withMessage('Invalid category') ], handleValidationErrors, mlController.getPriorityQueue ); /** * @route GET /api/v2/ml/metrics/performance * @desc Récupérer les métriques de performance ML * @access Private (Admin) */ router.get('/metrics/performance', mlRateLimit, auth, (req, res, next) => { if (!req.user || req.user.role !== 'admin') { return res.status(403).json({ success: false, error: 'Admin role required' }); } next(); }, [ query('timeRange') .optional() .isIn(['1h', '24h', '7d', '30d']) .withMessage('Invalid time range') ], handleValidationErrors, mlController.getPerformanceMetrics ); /** * @route GET /api/v2/ml/classifications/:conversationId * @desc Récupérer les classifications d'une conversation * @access Private (Admin/Agent) */ router.get('/classifications/:conversationId', mlRateLimit, auth, requireAdminOrAgent, [ param('conversationId') .isMongoId() .withMessage('Valid conversation ID is required'), query('includeHistory') .optional() .isBoolean() .withMessage('includeHistory must be a boolean') ], handleValidationErrors, mlController.getClassificationsByConversation ); /** * @route PUT /api/v2/ml/classifications/:id/validate * @desc Valider une classification avec feedback humain * @access Private (Admin/Agent) */ router.put('/classifications/:id/validate', mlRateLimit, auth, requireAdminOrAgent, [ param('id') .isMongoId() .withMessage('Valid classification ID is required'), body('isCorrect') .isBoolean() .withMessage('isCorrect is required and must be boolean'), body('correctedCategory') .optional() .isIn(['VENTE_OPPORTUNITE', 'RESILIATION_CRITIQUE', 'SUPPORT_URGENT', 'RECLAMATION', 'INFO_SIMPLE']) .withMessage('Invalid corrected category'), body('feedback') .optional() .isString() .isLength({ max: 500 }) .withMessage('Feedback must be a string with max 500 characters'), body('confidence') .optional() .isFloat({ min: 0, max: 1 }) .withMessage('Confidence must be between 0 and 1') ], handleValidationErrors, mlController.validateClassification ); /** * @route GET /api/v2/ml/stats * @desc Récupérer les statistiques des classifications * @access Private (Admin) */ router.get('/stats', mlRateLimit, auth, (req, res, next) => { if (!req.user || req.user.role !== 'admin') { return res.status(403).json({ success: false, error: 'Admin role required' }); } next(); }, [ query('startDate') .optional() .isISO8601() .withMessage('startDate must be a valid ISO date'), query('endDate') .optional() .isISO8601() .withMessage('endDate must be a valid ISO date'), query('groupBy') .optional() .isIn(['hour', 'day', 'week', 'month']) .withMessage('groupBy must be hour, day, week, or month'), query('category') .optional() .isIn(['VENTE_OPPORTUNITE', 'RESILIATION_CRITIQUE', 'SUPPORT_URGENT', 'RECLAMATION', 'INFO_SIMPLE']) .withMessage('Invalid category') ], handleValidationErrors, mlController.getClassificationStats ); /** * @route DELETE /api/v2/ml/cache/:conversationId * @desc Invalider le cache ML pour une conversation * @access Private (Admin) */ router.delete('/cache/:conversationId', mlRateLimit, auth, (req, res, next) => { if (!req.user || req.user.role !== 'admin') { return res.status(403).json({ success: false, error: 'Admin role required' }); } next(); }, [ param('conversationId') .isMongoId() .withMessage('Valid conversation ID is required') ], handleValidationErrors, mlController.invalidateCache ); /** * @route GET /api/v2/ml/health * @desc Vérifier la santé du service ML * @access Private (Admin) */ router.get('/health', auth, (req, res, next) => { if (!req.user || req.user.role !== 'admin') { return res.status(403).json({ success: false, error: 'Admin role required' }); } next(); }, mlController.healthCheck ); // Routes pour les alertes admin (intégration avec le système ML) /** * @route GET /api/v2/ml/alerts * @desc Récupérer les alertes générées par ML * @access Private (Admin/Agent) */ router.get('/alerts', mlRateLimit, auth, requireAdminOrAgent, [ query('status') .optional() .isIn(['ACTIVE', 'ACKNOWLEDGED', 'IN_PROGRESS', 'RESOLVED', 'DISMISSED']) .withMessage('Invalid status'), query('severity') .optional() .isIn(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']) .withMessage('Invalid severity'), query('type') .optional() .isIn(['VENTE_OPPORTUNITY', 'CHURN_RISK', 'ESCALATION_NEEDED', 'TECHNICAL_ISSUE', 'BILLING_DISPUTE', 'SATISFACTION_CRITICAL']) .withMessage('Invalid alert type'), query('limit') .optional() .isInt({ min: 1, max: 100 }) .withMessage('Limit must be between 1 and 100') ], handleValidationErrors, async (req, res) => { try { const AdminAlert = require('../models/AdminAlert'); const { status, severity, type, limit = 50, assignedTo } = req.query; const query = {}; if (status) query.status = status; if (severity) query.severity = severity; if (type) query.type = type; if (assignedTo) query.assignedTo = assignedTo; // Filtrer par utilisateur si agent (non admin) if (req.user.role === 'agent') { query.assignedTo = req.user.id; } const alerts = await AdminAlert.find(query) .sort({ priority: -1, createdAt: -1 }) .limit(parseInt(limit)) .populate('assignedTo', 'name email') .populate('customerId', 'name email planType') .populate('conversationId', 'channel status') .populate('classificationId'); res.json({ success: true, alerts: alerts, totalCount: alerts.length }); } catch (error) { res.status(500).json({ success: false, error: 'Failed to fetch alerts', details: error.message }); } } ); /** * @route PUT /api/v2/ml/alerts/:id/acknowledge * @desc Acquitter une alerte * @access Private (Admin/Agent) */ router.put('/alerts/:id/acknowledge', mlRateLimit, auth, requireAdminOrAgent, [ param('id') .isMongoId() .withMessage('Valid alert ID is required') ], handleValidationErrors, async (req, res) => { try { const AdminAlert = require('../models/AdminAlert'); const alert = await AdminAlert.findById(req.params.id); if (!alert) { return res.status(404).json({ success: false, error: 'Alert not found' }); } // Vérifier les permissions if (req.user.role === 'agent' && alert.assignedTo?.toString() !== req.user.id) { return res.status(403).json({ success: false, error: 'Not authorized to acknowledge this alert' }); } await alert.acknowledge(req.user.id); res.json({ success: true, alert: alert, message: 'Alert acknowledged successfully' }); } catch (error) { res.status(500).json({ success: false, error: 'Failed to acknowledge alert', details: error.message }); } } ); module.exports = router;