/** * ============================================= * [AI] AI CALL ROUTES * API routes for AI-powered call management system * Handles conversation analysis, AI suggestions, and smart escalation * ============================================= */ const express = require('express'); const router = express.Router(); const aiCallController = require('../controllers/aiCallController'); const { authenticate } = require('../middleware/authMiddleware'); const { validateRequest } = require('../middleware/validationMiddleware'); const rateLimit = require('express-rate-limit'); // Rate limiting for AI call endpoints const aiCallLimiter = rateLimit({ windowMs: 5 * 60 * 1000, // 5 minutes max: 20, // 20 AI call operations per 5 minutes message: { error: 'Too many AI call requests', message: 'Trop de demandes d\'appels IA. Veuillez patienter.', retryAfter: 300 }, standardHeaders: true, legacyHeaders: false }); // Validation schemas const conversationAnalysisSchema = { params: { conversationId: { type: 'string', required: true, pattern: '^[0-9a-fA-F]{24}$' // MongoDB ObjectId pattern } } }; const initiateAICallSchema = { body: { conversationId: { type: 'string', required: true, pattern: '^[0-9a-fA-F]{24}$' }, forceCall: { type: 'boolean', required: false, default: false } } }; const aiSuggestionsSchema = { params: { callId: { type: 'string', required: true, minLength: 1 } }, body: { userInput: { type: 'string', required: false, maxLength: 2000 }, context: { type: 'object', required: false, properties: { sessionId: { type: 'string' }, conversationHistory: { type: 'array' } } } } }; const escalationEvaluationSchema = { params: { callId: { type: 'string', required: true, minLength: 1 } }, body: { userFeedback: { type: 'string', required: false, maxLength: 1000 }, aiAttempts: { type: 'number', required: false, minimum: 0, maximum: 10 }, conversationHistory: { type: 'array', required: false } } }; const escalateToHumanSchema = { params: { callId: { type: 'string', required: true, minLength: 1 } }, body: { escalationReason: { type: 'string', required: true, enum: [ 'max_ai_attempts_reached', 'high_user_frustration', 'complex_technical_issue', 'user_request', 'critical_urgency', 'multiple_failed_solutions' ] }, agentRequirements: { type: 'object', required: false, properties: { specialization: { type: 'string' }, experience: { type: 'string' }, skills: { type: 'array' }, language: { type: 'string' } } }, conversationHistory: { type: 'array', required: false }, aiAttempts: { type: 'number', required: false, minimum: 0 } } }; const analyticsSchema = { query: { timeframe: { type: 'string', required: false, enum: ['1h', '24h', '7d', '30d'], default: '24h' } } }; /** * @route POST /api/ai-calls/analyze-conversation/:conversationId * @desc Analyze conversation to determine if a call is needed * @access Private */ router.post( '/analyze-conversation/:conversationId', authenticate, aiCallLimiter, validateRequest(conversationAnalysisSchema), aiCallController.analyzeConversation ); /** * @route POST /api/ai-calls/initiate * @desc Initiate AI-powered call based on conversation analysis * @access Private */ router.post( '/initiate', authenticate, aiCallLimiter, validateRequest(initiateAICallSchema), aiCallController.initiateAICall ); /** * @route POST /api/ai-calls/suggestions/:callId * @desc Get AI suggestions for current call context * @access Private */ router.post( '/suggestions/:callId', authenticate, aiCallLimiter, validateRequest(aiSuggestionsSchema), aiCallController.getAISuggestions ); /** * @route POST /api/ai-calls/evaluate-escalation/:callId * @desc Evaluate if escalation to human agent is needed * @access Private */ router.post( '/evaluate-escalation/:callId', authenticate, aiCallLimiter, validateRequest(escalationEvaluationSchema), aiCallController.evaluateEscalation ); /** * @route POST /api/ai-calls/escalate/:callId * @desc Escalate call to human agent with context preservation * @access Private */ router.post( '/escalate/:callId', authenticate, aiCallLimiter, validateRequest(escalateToHumanSchema), aiCallController.escalateToHuman ); /** * @route GET /api/ai-calls/analytics * @desc Get call analytics and performance metrics * @access Private */ router.get( '/analytics', authenticate, validateRequest(analyticsSchema), aiCallController.getCallAnalytics ); /** * @route GET /api/ai-calls/health * @desc Health check for AI call services * @access Public */ router.get('/health', aiCallController.healthCheck); // Error handling middleware for AI call routes router.use((error, req, res, next) => { if (error.type === 'validation') { return res.status(400).json({ success: false, error: 'Validation error', details: error.details, code: 'AI_CALL_VALIDATION_ERROR' }); } if (error.type === 'rate_limit') { return res.status(429).json({ success: false, error: 'Too many AI call requests', message: 'Trop de demandes d\'appels IA. Veuillez patienter.', retryAfter: error.retryAfter, code: 'AI_CALL_RATE_LIMIT' }); } // Log unexpected errors console.error('AI Call Route Error:', error); res.status(500).json({ success: false, error: 'Internal server error', message: 'Une erreur interne s\'est produite', code: 'AI_CALL_INTERNAL_ERROR' }); }); module.exports = router;