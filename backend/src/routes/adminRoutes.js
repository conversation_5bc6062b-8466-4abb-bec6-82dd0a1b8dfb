const express = require('express'); const router = express.Router(); const adminController = require('../controllers/adminController'); const { authenticate, authorize } = require('../middleware/authMiddleware'); // Authentification requise pour toutes les routes admin router.use(authenticate); // Dashboard - accessible aux agents et admins router.get('/dashboard', authorize('agent', 'admin'), adminController.getDashboard); // Gestion des conversations - accessible aux agents et admins router.get('/conversations', authorize('agent', 'admin'), adminController.getAllConversations); router.get('/conversations/:conversationId', authorize('agent', 'admin'), adminController.getConversationDetails); router.put('/conversations/:conversationId/assign', authorize('agent', 'admin'), adminController.assignAgent); router.put('/conversations/:conversationId/close', authorize('agent', 'admin'), adminController.closeConversation); // Analytics - accessible uniquement aux admins router.get('/analytics', authorize('admin'), adminController.getAnalytics); module.exports = router;