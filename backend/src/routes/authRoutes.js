const express = require('express'); const router = express.Router(); const authController = require('../controllers/authController'); const { body, validationResult } = require('express-validator'); const validate = (req, res, next) => { const errors = validationResult(req); if (!errors.isEmpty()) { return res.status(400).json({ errors: errors.array() }); } next(); }; router.post( '/register', [ body('email').isEmail().normalizeEmail(), body('password').isLength({ min: 6 }), body('firstName').notEmpty().trim(), body('lastName').notEmpty().trim(), ], validate, authController.register ); router.post( '/login', [ body('email').isEmail().normalizeEmail(), body('password').notEmpty(), ], validate, authController.login ); module.exports = router;