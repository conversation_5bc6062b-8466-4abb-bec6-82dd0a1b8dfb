const express = require('express'); const router = express.Router(); const securityController = require('../controllers/securityController'); const { authenticate } = require('../middleware/authMiddleware'); /** * ROUTES DE SÉCURITÉ * Authentification 2FA et sécurité avancée */ // Toutes les routes nécessitent une authentification router.use(authenticate); // [SECURITY] Routes 2FA router.get('/2fa/generate', securityController.generateTwoFactorSecret); router.post('/2fa/enable', securityController.enableTwoFactor); router.post('/2fa/verify', securityController.verifyTwoFactor); router.post('/2fa/disable', securityController.disableTwoFactor); router.post('/2fa/regenerate-backup-codes', securityController.regenerateBackupCodes); // [ANALYTICS] Statut et analyse de sécurité router.get('/status', securityController.getSecurityStatus); router.post('/analyze-login', securityController.analyzeLoginAttempt); router.get('/logs', securityController.getSecurityLogs); module.exports = router;