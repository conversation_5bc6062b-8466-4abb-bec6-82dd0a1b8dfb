/** * ============================================= * [FEATURE] ENHANCED AI SUGGESTIONS API ROUTES * Advanced AI-powered suggestions and contextual intelligence * Integrates with existing AI services for personalization * ============================================= */ const express = require('express'); const router = express.Router(); const { body, param, query, validationResult } = require('express-validator'); const auth = require('../middleware/auth'); const AIService = require('../services/aiService'); const SentimentService = require('../services/sentimentService'); const TemplateService = require('../services/templateService'); const logger = require('../utils/logger'); // Initialize services const aiService = new AIService(); const sentimentService = new SentimentService(); const templateService = new TemplateService(); /** * @route POST /api/ai/suggestions/contextual * @desc Generate context-aware response suggestions * @access Private */ router.post('/suggestions/contextual', [ auth, body('ticket_id').notEmpty().isString(), body('customer_id').notEmpty().isString(), body('conversation_history').isArray(), body('current_message').optional().isString(), body('urgency_level').optional().isIn(['low', 'medium', 'high', 'critical']), body('platform').optional().isIn(['whatsapp', 'facebook', 'instagram', 'twitter', 'call', 'email']) ], async (req, res) => { try { const errors = validationResult(req); if (!errors.isEmpty()) { return res.status(400).json({ success: false, message: 'Validation errors', errors: errors.array() }); } const { ticket_id, customer_id, conversation_history, current_message, urgency_level = 'medium', platform = 'whatsapp' } = req.body; const agentId = req.user.id; // Get customer profile and context const customerProfile = await aiService.getCustomerProfile(customer_id); const agentProfile = await aiService.getAgentProfile(agentId); // Analyze conversation context const conversationContext = await aiService.analyzeConversationContext({ conversation_history, customer_profile: customerProfile, current_message, platform }); // Generate contextual suggestions const suggestions = await aiService.generateContextualSuggestions({ ticket_id, customer_profile: customerProfile, agent_profile: agentProfile, conversation_context: conversationContext, urgency_level, platform }); // Get similar resolved cases for reference const similarCases = await aiService.findSimilarCases({ customer_profile: customerProfile, conversation_context: conversationContext, limit: 3 }); // Calculate confidence scores const confidenceScores = await aiService.calculateSuggestionConfidence(suggestions, conversationContext); logger.info(`Generated contextual suggestions`, { ticketId: ticket_id, agentId, suggestionsCount: suggestions.length, averageConfidence: confidenceScores.average, platform }); res.json({ success: true, data: { suggestions: suggestions.map((suggestion, index) => ({ ...suggestion, confidence: confidenceScores.individual[index] })), conversation_context: conversationContext, similar_cases: similarCases, metadata: { generated_at: new Date().toISOString(), agent_id: agentId, customer_tier: customerProfile.tier, urgency_level, platform } } }); } catch (error) { logger.error('Error generating contextual suggestions:', error); res.status(500).json({ success: false, message: 'Failed to generate contextual suggestions', error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error' }); } }); /** * @route POST /api/ai/sentiment/analyze * @desc Real-time sentiment analysis * @access Private */ router.post('/sentiment/analyze', [ auth, body('conversation').isArray().notEmpty(), body('include_trends').optional().isBoolean(), body('include_emotions').optional().isBoolean() ], async (req, res) => { try { const errors = validationResult(req); if (!errors.isEmpty()) { return res.status(400).json({ success: false, message: 'Validation errors', errors: errors.array() }); } const { conversation, include_trends = true, include_emotions = true } = req.body; // Analyze current sentiment const sentimentAnalysis = await sentimentService.analyzeSentiment(conversation); // Calculate sentiment trends if requested let sentimentTrends = null; if (include_trends) { sentimentTrends = await sentimentService.calculateSentimentTrends(conversation); } // Analyze emotions if requested let emotionBreakdown = null; if (include_emotions) { emotionBreakdown = await sentimentService.analyzeEmotions(conversation); } // Calculate escalation risk const escalationRisk = await sentimentService.calculateEscalationRisk(sentimentAnalysis, conversation); // Generate recommended approach const recommendedApproach = await sentimentService.getRecommendedApproach(sentimentAnalysis, emotionBreakdown); logger.info(`Analyzed sentiment`, { conversationLength: conversation.length, currentSentiment: sentimentAnalysis.current_sentiment, escalationRisk: escalationRisk, recommendedApproach }); res.json({ success: true, data: { sentiment_analysis: { current_sentiment: sentimentAnalysis.current_sentiment, sentiment_trend: sentimentTrends, emotion_breakdown: emotionBreakdown, escalation_risk: escalationRisk, recommended_approach: recommendedApproach, confidence: sentimentAnalysis.confidence }, recommendations: await sentimentService.getSentimentRecommendations(sentimentAnalysis), metadata: { analyzed_at: new Date().toISOString(), messages_analyzed: conversation.length, analysis_version: '2.1' } } }); } catch (error) { logger.error('Error analyzing sentiment:', error); res.status(500).json({ success: false, message: 'Failed to analyze sentiment', error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error' }); } }); /** * @route POST /api/ai/escalation/recommend * @desc Intelligent escalation recommendations * @access Private */ router.post('/escalation/recommend', [ auth, body('ticket_id').notEmpty().isString(), body('conversation_history').isArray(), body('customer_profile').isObject(), body('current_sentiment').optional().isNumeric(), body('agent_skill_level').optional().isNumeric() ], async (req, res) => { try { const errors = validationResult(req); if (!errors.isEmpty()) { return res.status(400).json({ success: false, message: 'Validation errors', errors: errors.array() }); } const { ticket_id, conversation_history, customer_profile, current_sentiment, agent_skill_level } = req.body; const agentId = req.user.id; // Analyze escalation factors const escalationFactors = await aiService.analyzeEscalationFactors({ conversation_history, customer_profile, current_sentiment, agent_skill_level: agent_skill_level || req.user.skill_level }); // Generate escalation recommendation const escalationRecommendation = await aiService.generateEscalationRecommendation({ ticket_id, escalation_factors, agent_id: agentId, customer_profile }); // Get preparation steps if escalation is recommended let preparationSteps = []; let handoffNotes = ''; if (escalationRecommendation.should_escalate) { preparationSteps = await aiService.generatePreparationSteps(escalationRecommendation); handoffNotes = await aiService.generateHandoffNotes({ conversation_history, customer_profile, escalation_reason: escalationRecommendation.reasoning }); } // Get alternative solutions before escalation const alternativeSolutions = await aiService.getAlternativeSolutions({ escalation_factors, customer_profile, conversation_history }); logger.info(`Generated escalation recommendation`, { ticketId: ticket_id, agentId, shouldEscalate: escalationRecommendation.should_escalate, confidence: escalationRecommendation.confidence, urgency: escalationRecommendation.urgency }); res.json({ success: true, data: { recommendation: { ...escalationRecommendation, preparation_steps: preparationSteps, handoff_notes: handoffNotes }, escalation_factors: escalationFactors, alternative_solutions: alternativeSolutions, metadata: { analyzed_at: new Date().toISOString(), agent_id: agentId, ticket_id, analysis_confidence: escalationRecommendation.confidence } } }); } catch (error) { logger.error('Error generating escalation recommendation:', error); res.status(500).json({ success: false, message: 'Failed to generate escalation recommendation', error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error' }); } }); /** * @route GET /api/ai/templates/:agent_id * @desc Personalized response template management * @access Private */ router.get('/templates/:agent_id', [ auth, param('agent_id').notEmpty().isString(), query('category').optional().isString(), query('include_personalized').optional().isBoolean() ], async (req, res) => { try { const errors = validationResult(req); if (!errors.isEmpty()) { return res.status(400).json({ success: false, message: 'Validation errors', errors: errors.array() }); } const { agent_id } = req.params; const { category, include_personalized = true } = req.query; // Check permissions if (agent_id !== req.user.id && !req.user.roles.includes('supervisor')) { return res.status(403).json({ success: false, message: 'Access denied' }); } // Get standard templates const standardTemplates = await templateService.getStandardTemplates({ category, agent_skill_level: req.user.skill_level }); // Get personalized templates if requested let personalizedTemplates = []; if (include_personalized) { personalizedTemplates = await templateService.getPersonalizedTemplates(agent_id, category); } // Get template categories const categories = await templateService.getTemplateCategories(); // Get template performance metrics const performanceMetrics = await templateService.getTemplatePerformance(agent_id); logger.info(`Retrieved templates for agent`, { agentId: agent_id, standardCount: standardTemplates.length, personalizedCount: personalizedTemplates.length, category }); res.json({ success: true, data: { templates: standardTemplates, personalized_templates: personalizedTemplates, categories, performance_metrics: performanceMetrics, metadata: { agent_id, total_templates: standardTemplates.length + personalizedTemplates.length, last_updated: new Date().toISOString() } } }); } catch (error) { logger.error('Error retrieving templates:', error); res.status(500).json({ success: false, message: 'Failed to retrieve templates', error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error' }); } }); /** * @route POST /api/ai/templates/personalize * @desc Create personalized template * @access Private */ router.post('/templates/personalize', [ auth, body('template_id').notEmpty().isString(), body('customization').notEmpty().isString(), body('category').notEmpty().isString(), body('name').notEmpty().isString() ], async (req, res) => { try { const errors = validationResult(req); if (!errors.isEmpty()) { return res.status(400).json({ success: false, message: 'Validation errors', errors: errors.array() }); } const { template_id, customization, category, name } = req.body; const agentId = req.user.id; // Create personalized template const personalizedTemplate = await templateService.createPersonalizedTemplate({ base_template_id: template_id, agent_id: agentId, customization, category, name }); // Analyze template effectiveness const effectivenessAnalysis = await templateService.analyzeTemplateEffectiveness(customization); logger.info(`Created personalized template`, { templateId: personalizedTemplate.id, agentId, baseTemplateId: template_id, category }); res.status(201).json({ success: true, data: { personalized_template: personalizedTemplate, effectiveness_analysis: effectivenessAnalysis, metadata: { created_at: new Date().toISOString(), agent_id: agentId, base_template_id: template_id } } }); } catch (error) { logger.error('Error creating personalized template:', error); res.status(500).json({ success: false, message: 'Failed to create personalized template', error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error' }); } }); /** * @route POST /api/ai/suggestions/feedback * @desc Submit suggestion feedback for learning * @access Private */ router.post('/suggestions/feedback', [ auth, body('suggestion_id').notEmpty().isString(), body('feedback').isObject(), body('feedback.rating').isInt({ min: 1, max: 5 }), body('feedback.helpful').isBoolean(), body('feedback.used_as_is').isBoolean(), body('feedback.outcome').isIn(['successful', 'unsuccessful', 'neutral']) ], async (req, res) => { try { const errors = validationResult(req); if (!errors.isEmpty()) { return res.status(400).json({ success: false, message: 'Validation errors', errors: errors.array() }); } const { suggestion_id, feedback } = req.body; const agentId = req.user.id; // Submit feedback await aiService.submitSuggestionFeedback(suggestion_id, agentId, feedback); // Update learning model const learningUpdate = await aiService.updateLearningModel(agentId, { suggestion_id, feedback }); // Get updated performance metrics const performanceUpdate = await aiService.getSuggestionPerformance(agentId); logger.info(`Submitted suggestion feedback`, { suggestionId: suggestion_id, agentId, rating: feedback.rating, helpful: feedback.helpful, outcome: feedback.outcome }); res.json({ success: true, data: { learning_update: learningUpdate, performance: performanceUpdate, metadata: { feedback_submitted_at: new Date().toISOString(), agent_id: agentId, suggestion_id } } }); } catch (error) { logger.error('Error submitting suggestion feedback:', error); res.status(500).json({ success: false, message: 'Failed to submit suggestion feedback', error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error' }); } }); module.exports = router;