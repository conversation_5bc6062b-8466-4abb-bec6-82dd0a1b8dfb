const express = require('express'); const router = express.Router(); const customerController = require('../controllers/customerController'); const { authenticate } = require('../middleware/authMiddleware'); // Toutes les routes nécessitent une authentification router.use(authenticate); // Profil client router.get('/profile', customerController.getCustomerProfile); router.put('/profile', customerController.updateCustomerProfile); router.post('/profile/create', customerController.createCustomerProfile); // Factures router.get('/invoices', customerController.getInvoices); router.get('/invoices/:invoiceId', customerController.getInvoiceDetails); router.post('/invoices/:invoiceId/pay', customerController.payInvoice); // Notifications router.get('/notifications', customerController.getNotifications); router.put('/notifications/:notificationId/read', customerController.markNotificationAsRead); // Consommation router.get('/consumption', customerController.getConsumption); router.get('/consumption/history', customerController.getConsumptionHistory); // Support router.post('/support/ticket', customerController.createSupportTicket); router.get('/support/tickets', customerController.getSupportTickets); router.get('/support/tickets/:ticketId', customerController.getSupportTicketDetails); module.exports = router;