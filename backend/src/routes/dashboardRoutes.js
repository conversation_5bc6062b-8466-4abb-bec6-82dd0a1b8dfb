/** * ============================================= * [TARGET] DASHBOARD API ROUTES * Backend endpoints for simulation dashboard functionality * Integrates with existing authentication and database systems * ============================================= */ const express = require('express'); const router = express.Router(); const dashboardController = require('../controllers/dashboardController'); const { authenticate, authorize } = require('../middleware/authMiddleware'); // Apply authentication middleware to all dashboard routes router.use(authenticate); /** * @route GET /api/dashboard/simulations * @desc Get all available simulation scenarios with filtering * @access Private (All authenticated users) * @query difficulty - Filter by difficulty level (beginner, intermediate, expert) * @query category - Filter by category (billing, technical, sales, retention, complaint) * @query tags - Filter by tags (comma-separated) * @query limit - Number of scenarios to return (default: 50) * @query offset - Number of scenarios to skip (default: 0) */ router.get('/simulations', dashboardController.getSimulations); /** * @route GET /api/dashboard/metrics/:agentId * @desc Get performance metrics for a specific agent * @access Private (Agent can view own metrics, Admin can view all) * @param agentId - The ID of the agent to get metrics for */ router.get('/metrics/:agentId', dashboardController.getAgentMetrics); /** * @route GET /api/dashboard/leaderboard * @desc Get ranked agent performance data * @access Private (All authenticated users) * @query limit - Number of entries to return (default: 10) * @query timeframe - Time period for leaderboard (week, month, all) */ router.get('/leaderboard', dashboardController.getLeaderboard); /** * @route GET /api/dashboard/progress/:sessionId * @desc Get real-time simulation progress data * @access Private (Session owner or Admin) * @param sessionId - The ID of the simulation session */ router.get('/progress/:sessionId', dashboardController.getSessionProgress); /** * @route GET /api/dashboard/sessions * @desc Get simulation session history for current user * @access Private (User sees own sessions, Admin sees all) * @query status - Filter by session status (active, completed, paused, abandoned) * @query limit - Number of sessions to return (default: 20) * @query offset - Number of sessions to skip (default: 0) */ router.get('/sessions', dashboardController.getUserSessions); /** * @route POST /api/dashboard/simulations/start * @desc Start a new simulation session * @access Private (All authenticated users) * @body scenarioId - The ID of the scenario to start * @body settings - Optional simulation settings (speed, difficulty adjustments) */ router.post('/simulations/start', dashboardController.startSimulation); /** * @route POST /api/dashboard/simulations/stop/:sessionId * @desc Stop an active simulation session * @access Private (Session owner or Admin) * @param sessionId - The ID of the simulation session to stop */ router.post('/simulations/stop/:sessionId', dashboardController.stopSimulation); /** * @route GET /api/dashboard/analytics * @desc Get dashboard analytics and summary statistics * @access Private (Admin and Agent roles) * @query timeframe - Time period for analytics (day, week, month) */ router.get('/analytics', dashboardController.getDashboardAnalytics); /** * @route GET /api/dashboard/scenarios/:scenarioId * @desc Get detailed information about a specific scenario * @access Private (All authenticated users) * @param scenarioId - The ID of the scenario to retrieve */ router.get('/scenarios/:scenarioId', dashboardController.getScenarioDetails); module.exports = router;