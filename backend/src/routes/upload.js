const express = require('express'); const multer = require('multer'); const path = require('path'); const fs = require('fs'); const { body, validationResult } = require('express-validator'); const auth = require('../middleware/auth'); const rateLimit = require('express-rate-limit'); const router = express.Router(); // Rate limiting for uploads const uploadRateLimit = rateLimit({ windowMs: 15 * 60 * 1000, // 15 minutes max: 20, // Limit each IP to 20 upload requests per windowMs message: { success: false, message: 'Too many upload requests, please try again later.' } }); // Ensure upload directories exist const ensureUploadDirs = () => { const dirs = [ 'uploads/chat', 'uploads/tickets', 'uploads/profiles', 'uploads/temp' ]; dirs.forEach(dir => { if (!fs.existsSync(dir)) { fs.mkdirSync(dir, { recursive: true }); } }); }; ensureUploadDirs(); // Storage configuration for chat files const chatStorage = multer.diskStorage({ destination: (req, file, cb) => { cb(null, 'uploads/chat/'); }, filename: (req, file, cb) => { const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9); const ext = path.extname(file.originalname); cb(null, `chat-${uniqueSuffix}${ext}`); } }); // Storage configuration for ticket files const ticketStorage = multer.diskStorage({ destination: (req, file, cb) => { cb(null, 'uploads/tickets/'); }, filename: (req, file, cb) => { const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9); const ext = path.extname(file.originalname); cb(null, `ticket-${uniqueSuffix}${ext}`); } }); // File filter for chat uploads const chatFileFilter = (req, file, cb) => { const allowedTypes = /jpeg|jpg|png|gif|pdf|txt|doc|docx/; const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase()); const mimetype = allowedTypes.test(file.mimetype); if (mimetype && extname) { return cb(null, true); } else { cb(new Error('Invalid file type. Only images, PDFs, and documents are allowed.')); } }; // File filter for ticket uploads const ticketFileFilter = (req, file, cb) => { const allowedTypes = /jpeg|jpg|png|gif|pdf|txt|doc|docx|xls|xlsx|zip/; const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase()); const mimetype = allowedTypes.test(file.mimetype); if (mimetype && extname) { return cb(null, true); } else { cb(new Error('Invalid file type for tickets.')); } }; // Chat upload configuration const chatUpload = multer({ storage: chatStorage, limits: { fileSize: 10 * 1024 * 1024, // 10MB limit files: 5 // Maximum 5 files }, fileFilter: chatFileFilter }); // Ticket upload configuration const ticketUpload = multer({ storage: ticketStorage, limits: { fileSize: 25 * 1024 * 1024, // 25MB limit for tickets files: 10 // Maximum 10 files for tickets }, fileFilter: ticketFileFilter }); /** * @route POST /api/upload/chat * @desc Upload files for chat messages * @access Private */ router.post('/chat', auth, uploadRateLimit, chatUpload.array('files', 5), async (req, res) => { try { if (!req.files || req.files.length === 0) { return res.status(400).json({ success: false, message: 'No files uploaded' }); } const fileUrls = req.files.map(file => ({ filename: file.filename, originalName: file.originalname, mimetype: file.mimetype, size: file.size, url: `/uploads/chat/${file.filename}`, uploadedBy: req.user._id, uploadedAt: new Date() })); res.json({ success: true, message: 'Files uploaded successfully', files: fileUrls, fileUrls: fileUrls.map(f => f.url) }); } catch (error) { console.error('Chat upload error:', error); res.status(500).json({ success: false, message: 'Error uploading files', error: error.message }); } } ); /** * @route POST /api/upload/ticket * @desc Upload files for support tickets * @access Private */ router.post('/ticket', auth, uploadRateLimit, ticketUpload.array('files', 10), async (req, res) => { try { if (!req.files || req.files.length === 0) { return res.status(400).json({ success: false, message: 'No files uploaded' }); } const fileUrls = req.files.map(file => ({ filename: file.filename, originalName: file.originalname, mimetype: file.mimetype, size: file.size, url: `/uploads/tickets/${file.filename}`, uploadedBy: req.user._id, uploadedAt: new Date() })); res.json({ success: true, message: 'Files uploaded successfully', files: fileUrls, fileUrls: fileUrls.map(f => f.url) }); } catch (error) { console.error('Ticket upload error:', error); res.status(500).json({ success: false, message: 'Error uploading files', error: error.message }); } } ); /** * @route DELETE /api/upload/:type/:filename * @desc Delete uploaded file * @access Private */ router.delete('/:type/:filename', auth, async (req, res) => { try { const { type, filename } = req.params; if (!['chat', 'ticket'].includes(type)) { return res.status(400).json({ success: false, message: 'Invalid upload type' }); } const filePath = path.join(__dirname, '../../uploads', type, filename); if (fs.existsSync(filePath)) { fs.unlinkSync(filePath); res.json({ success: true, message: 'File deleted successfully' }); } else { res.status(404).json({ success: false, message: 'File not found' }); } } catch (error) { console.error('File deletion error:', error); res.status(500).json({ success: false, message: 'Error deleting file', error: error.message }); } } ); // Error handling middleware for multer router.use((error, req, res, next) => { if (error instanceof multer.MulterError) { if (error.code === 'LIMIT_FILE_SIZE') { return res.status(400).json({ success: false, message: 'File too large. Check size limits for your upload type.' }); } if (error.code === 'LIMIT_FILE_COUNT') { return res.status(400).json({ success: false, message: 'Too many files. Check file count limits for your upload type.' }); } } if (error.message.includes('Invalid file type')) { return res.status(400).json({ success: false, message: error.message }); } next(error); }); module.exports = router;