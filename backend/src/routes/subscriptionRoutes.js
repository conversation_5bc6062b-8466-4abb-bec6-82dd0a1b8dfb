const express = require('express'); const router = express.Router(); const subscriptionController = require('../controllers/subscriptionController'); const { authenticate } = require('../middleware/authMiddleware'); // Toutes les routes nécessitent une authentification router.use(authenticate); // Forfaits router.get('/plans/current', subscriptionController.getCurrentPlan); router.get('/plans/available', subscriptionController.getAvailablePlans); router.post('/plans/simulate', subscriptionController.simulatePlanChange); router.post('/plans/change', subscriptionController.changePlan); // Options router.get('/options/available', subscriptionController.getAvailableOptions); router.post('/options/activate', subscriptionController.activateOption); router.post('/options/deactivate', subscriptionController.deactivateOption); // Consommation et facturation router.get('/consumption', subscriptionController.getConsumption); router.get('/billing/history', subscriptionController.getBillingHistory); module.exports = router;