const express = require('express'); const router = express.Router(); const { body, param, query } = require('express-validator'); const supportTicketController = require('../controllers/supportTicketController'); const { authenticate } = require('../middleware/authMiddleware'); const rateLimitMiddleware = require('../middleware/rateLimitMiddleware'); // Apply authentication to all routes router.use(authenticate); // Validation middleware const createTicketValidation = [ body('subject') .trim() .isLength({ min: 5, max: 200 }) .withMessage('Subject must be between 5 and 200 characters'), body('description') .trim() .isLength({ min: 10, max: 2000 }) .withMessage('Description must be between 10 and 2000 characters'), body('category') .isIn(['technique', 'billing', 'account', 'mobile', 'internet', 'forfait', 'options', 'equipment', 'network', 'other']) .withMessage('Invalid category'), body('priority') .optional() .isIn(['low', 'medium', 'high', 'urgent']) .withMessage('Invalid priority'), body('tags') .optional() .isArray() .withMessage('Tags must be an array'), body('tags.*') .optional() .isLength({ max: 50 }) .withMessage('Each tag must be maximum 50 characters') ]; const updateTicketValidation = [ param('ticketId') .isMongoId() .withMessage('Invalid ticket ID'), body('status') .optional() .isIn(['open', 'in_progress', 'waiting_customer', 'resolved', 'closed']) .withMessage('Invalid status'), body('assignedTo') .optional() .isMongoId() .withMessage('Invalid assigned user ID'), body('internalNote') .optional() .trim() .isLength({ max: 1000 }) .withMessage('Internal note must be maximum 1000 characters') ]; const addMessageValidation = [ param('ticketId') .isMongoId() .withMessage('Invalid ticket ID'), body('content') .trim() .isLength({ min: 1, max: 2000 }) .withMessage('Message content must be between 1 and 2000 characters'), body('isInternal') .optional() .isBoolean() .withMessage('isInternal must be a boolean') ]; const paginationValidation = [ query('page') .optional() .isInt({ min: 1 }) .withMessage('Page must be a positive integer'), query('limit') .optional() .isInt({ min: 1, max: 100 }) .withMessage('Limit must be between 1 and 100'), query('sortBy') .optional() .isIn(['createdAt', 'updatedAt', 'priority', 'status', 'subject']) .withMessage('Invalid sort field'), query('sortOrder') .optional() .isIn(['asc', 'desc']) .withMessage('Sort order must be asc or desc') ]; // Routes /** * @route POST /api/support/tickets * @desc Create a new support ticket * @access Private (authenticated users) */ router.post('/tickets', rateLimitMiddleware.createTicket, supportTicketController.uploadMiddleware, createTicketValidation, supportTicketController.createTicket ); /** * @route GET /api/support/tickets * @desc Get tickets for current user * @access Private (authenticated users) */ router.get('/tickets', paginationValidation, supportTicketController.getUserTickets ); /** * @route GET /api/support/tickets/all * @desc Get all tickets (admin/agent only) * @access Private (admin/agent) */ router.get('/tickets/all', paginationValidation, supportTicketController.getAllTickets ); /** * @route GET /api/support/tickets/:ticketId * @desc Get ticket by ID * @access Private (owner, assigned agent, or admin) */ router.get('/tickets/:ticketId', param('ticketId').isMongoId().withMessage('Invalid ticket ID'), supportTicketController.getTicketById ); /** * @route PUT /api/support/tickets/:ticketId * @desc Update ticket status (admin/agent only) * @access Private (admin/agent) */ router.put('/tickets/:ticketId', updateTicketValidation, supportTicketController.updateTicketStatus ); /** * @route POST /api/support/tickets/:ticketId/messages * @desc Add message to ticket * @access Private (owner, assigned agent, or admin) */ router.post('/tickets/:ticketId/messages', supportTicketController.uploadMiddleware, addMessageValidation, supportTicketController.addMessage ); /** * @route GET /api/support/metrics * @desc Get ticket metrics (admin only) * @access Private (admin) */ router.get('/metrics', query('period') .optional() .isIn(['7d', '30d', '90d']) .withMessage('Period must be 7d, 30d, or 90d'), supportTicketController.getTicketMetrics ); // Error handling middleware for multer router.use((error, req, res, next) => { if (error instanceof multer.MulterError) { if (error.code === 'LIMIT_FILE_SIZE') { return res.status(400).json({ success: false, message: 'File too large. Maximum size is 10MB per file.' }); } if (error.code === 'LIMIT_FILE_COUNT') { return res.status(400).json({ success: false, message: 'Too many files. Maximum 5 files allowed.' }); } } if (error.message.includes('Invalid file type')) { return res.status(400).json({ success: false, message: error.message }); } next(error); }); module.exports = router;