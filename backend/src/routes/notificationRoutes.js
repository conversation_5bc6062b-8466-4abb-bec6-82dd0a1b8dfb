const express = require('express'); const router = express.Router(); const notificationController = require('../controllers/notificationController'); const { authenticate, authorize } = require('../middleware/authMiddleware'); // Toutes les routes nécessitent une authentification router.use(authenticate); // Routes utilisateur router.get('/', notificationController.getUserNotifications); router.get('/stats', notificationController.getNotificationStats); router.post('/trigger-proactive', notificationController.triggerProactiveNotifications); router.put('/:notificationId/read', notificationController.markAsRead); router.put('/read-all', notificationController.markAllAsRead); router.post('/:notificationId/action', notificationController.executeAction); // Routes admin router.post('/create', authorize('admin'), notificationController.createNotification); module.exports = router;