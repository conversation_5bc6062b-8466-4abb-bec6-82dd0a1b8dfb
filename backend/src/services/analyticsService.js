const Conversation = require('../models/Conversation'); const Message = require('../models/Message'); const SupportTicket = require('../models/SupportTicket'); const User = require('../models/User'); const logger = require('../utils/logger'); class AnalyticsService { constructor() { this.cache = new Map(); this.cacheTimeout = 5 * 60 * 1000; // 5 minutes } // Get comprehensive dashboard analytics async getDashboardAnalytics(timeRange = '30d', filters = {}) { try { const cacheKey = `dashboard_${timeRange}_${JSON.stringify(filters)}`; const cached = this.getFromCache(cacheKey); if (cached) return cached; const dateRange = this.getDateRange(timeRange); const [ kpis, timeSeriesData, categoryDistribution, agentPerformance, realTimeMetrics, insights ] = await Promise.all([ this.getKPIs(dateRange), this.getTimeSeriesData(dateRange), this.getCategoryDistribution(dateRange), this.getAgentPerformance(dateRange), this.getRealTimeMetrics(), this.getInsights(dateRange) ]); const result = { kpis, timeSeriesData, categoryDistribution, agentPerformance, realTimeMetrics, insights, metadata: { timeRange, generatedAt: new Date(), dataPoints: timeSeriesData.length } }; this.setCache(cacheKey, result); return result; } catch (error) { logger.error('Error getting dashboard analytics:', error); throw error; } } // Get Key Performance Indicators async getKPIs(dateRange) { try { const [ totalConversations, totalTickets, activeAgents, avgResponseTime, customerSatisfaction, resolutionRate, escalationRate ] = await Promise.all([ Conversation.countDocuments({ createdAt: { $gte: dateRange.start, $lte: dateRange.end } }), SupportTicket.countDocuments({ createdAt: { $gte: dateRange.start, $lte: dateRange.end } }), User.countDocuments({ role: 'agent', isActive: true }), this.calculateAverageResponseTime(dateRange), this.calculateCustomerSatisfaction(dateRange), this.calculateResolutionRate(dateRange), this.calculateEscalationRate(dateRange) ]); // Calculate growth rates const previousRange = this.getPreviousDateRange(dateRange); const [ previousConversations, previousTickets ] = await Promise.all([ Conversation.countDocuments({ createdAt: { $gte: previousRange.start, $lte: previousRange.end } }), SupportTicket.countDocuments({ createdAt: { $gte: previousRange.start, $lte: previousRange.end } }) ]); const conversationGrowth = this.calculateGrowthRate(totalConversations, previousConversations); const ticketGrowth = this.calculateGrowthRate(totalTickets, previousTickets); return { totalConversations, totalTickets, activeAgents, averageResponseTime: avgResponseTime, customerSatisfaction: customerSatisfaction, resolutionRate: resolutionRate, escalationRate: escalationRate, conversationGrowth, ticketGrowth, dailyAverage: Math.round(totalConversations / this.getDaysDifference(dateRange)), weeklyAverage: Math.round(totalConversations / (this.getDaysDifference(dateRange) / 7)) }; } catch (error) { logger.error('Error calculating KPIs:', error); throw error; } } // Get time series data for charts async getTimeSeriesData(dateRange) { try { const conversations = await Conversation.aggregate([ { $match: { createdAt: { $gte: dateRange.start, $lte: dateRange.end } } }, { $group: { _id: { year: { $year: '$createdAt' }, month: { $month: '$createdAt' }, day: { $dayOfMonth: '$createdAt' } }, conversations: { $sum: 1 }, resolved: { $sum: { $cond: [{ $eq: ['$status', 'resolved'] }, 1, 0] } }, escalated: { $sum: { $cond: [{ $eq: ['$status', 'escalated'] }, 1, 0] } } } }, { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } } ]); const tickets = await SupportTicket.aggregate([ { $match: { createdAt: { $gte: dateRange.start, $lte: dateRange.end } } }, { $group: { _id: { year: { $year: '$createdAt' }, month: { $month: '$createdAt' }, day: { $dayOfMonth: '$createdAt' } }, tickets: { $sum: 1 }, resolved: { $sum: { $cond: [{ $eq: ['$status', 'resolved'] }, 1, 0] } }, avgResponseTime: { $avg: '$sla.responseTime.actual' } } }, { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } } ]); // Merge conversation and ticket data by date const timeSeriesMap = new Map(); conversations.forEach(item => { const dateKey = `${item._id.year}-${item._id.month}-${item._id.day}`; timeSeriesMap.set(dateKey, { date: new Date(item._id.year, item._id.month - 1, item._id.day), conversations: item.conversations, conversationsResolved: item.resolved, conversationsEscalated: item.escalated, tickets: 0, ticketsResolved: 0, avgResponseTime: 0 }); }); tickets.forEach(item => { const dateKey = `${item._id.year}-${item._id.month}-${item._id.day}`; const existing = timeSeriesMap.get(dateKey) || { date: new Date(item._id.year, item._id.month - 1, item._id.day), conversations: 0, conversationsResolved: 0, conversationsEscalated: 0 }; timeSeriesMap.set(dateKey, { ...existing, tickets: item.tickets, ticketsResolved: item.resolved, avgResponseTime: item.avgResponseTime || 0 }); }); return Array.from(timeSeriesMap.values()).sort((a, b) => a.date - b.date); } catch (error) { logger.error('Error getting time series data:', error); throw error; } } // Get category distribution async getCategoryDistribution(dateRange) { try { const [conversationsByChannel, ticketsByCategory, ticketsByPriority] = await Promise.all([ Conversation.aggregate([ { $match: { createdAt: { $gte: dateRange.start, $lte: dateRange.end } } }, { $group: { _id: '$channel', count: { $sum: 1 }, avgDuration: { $avg: '$duration' } } } ]), SupportTicket.aggregate([ { $match: { createdAt: { $gte: dateRange.start, $lte: dateRange.end } } }, { $group: { _id: '$category', count: { $sum: 1 }, avgResolutionTime: { $avg: '$resolutionTime' } } } ]), SupportTicket.aggregate([ { $match: { createdAt: { $gte: dateRange.start, $lte: dateRange.end } } }, { $group: { _id: '$priority', count: { $sum: 1 }, avgResponseTime: { $avg: '$sla.responseTime.actual' } } } ]) ]); return { conversationsByChannel, ticketsByCategory, ticketsByPriority }; } catch (error) { logger.error('Error getting category distribution:', error); throw error; } } // Get agent performance metrics async getAgentPerformance(dateRange) { try { const agentStats = await Conversation.aggregate([ { $match: { agentId: { $exists: true }, createdAt: { $gte: dateRange.start, $lte: dateRange.end } } }, { $lookup: { from: 'users', localField: 'agentId', foreignField: '_id', as: 'agent' } }, { $unwind: '$agent' }, { $group: { _id: '$agentId', agentName: { $first: '$agent.profile.firstName' }, agentEmail: { $first: '$agent.email' }, totalConversations: { $sum: 1 }, resolvedConversations: { $sum: { $cond: [{ $eq: ['$status', 'resolved'] }, 1, 0] } }, avgDuration: { $avg: '$duration' }, avgSatisfaction: { $avg: '$satisfaction.rating' } } }, { $addFields: { resolutionRate: { $multiply: [ { $divide: ['$resolvedConversations', '$totalConversations'] }, 100 ] } } }, { $sort: { totalConversations: -1 } } ]); return agentStats; } catch (error) { logger.error('Error getting agent performance:', error); throw error; } } // Get real-time metrics async getRealTimeMetrics() { try { const now = new Date(); const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000); const [ activeConversations, waitingQueue, todayTickets, overdueTickets, onlineAgents, systemHealth ] = await Promise.all([ Conversation.countDocuments({ status: 'active' }), Conversation.countDocuments({ status: 'waiting' }), SupportTicket.countDocuments({ createdAt: { $gte: last24Hours } }), SupportTicket.countDocuments({ status: { $in: ['open', 'in_progress'] }, 'sla.responseTime.breached': true }), User.countDocuments({ role: 'agent', isOnline: true }), this.getSystemHealth() ]); const avgWaitTime = await this.calculateAverageWaitTime(); const peakHourPrediction = await this.predictPeakHours(); return { activeConversations, waitingQueue, todayTickets, overdueTickets, onlineAgents, avgWaitTime, peakHourPrediction, systemHealth, lastUpdated: new Date() }; } catch (error) { logger.error('Error getting real-time metrics:', error); throw error; } } // Get insights and recommendations async getInsights(dateRange) { try { const insights = []; // Performance insights const kpis = await this.getKPIs(dateRange); if (kpis.conversationGrowth > 15) { insights.push({ type: 'performance', title: 'Forte croissance d\'activité', description: `Augmentation de ${kpis.conversationGrowth.toFixed(1)}% des conversations`, recommendation: 'Considérer l\'ajout d\'agents pour maintenir la qualité de service', priority: 'high', impact: 'positive' }); } if (kpis.customerSatisfaction >= 4.0) { insights.push({ type: 'quality', title: 'Satisfaction client élevée', description: `Score de satisfaction de ${kpis.customerSatisfaction.toFixed(1)}/5`, recommendation: 'Documenter les bonnes pratiques pour maintenir ce niveau', priority: 'low', impact: 'positive' }); } if (kpis.escalationRate > 10) { insights.push({ type: 'efficiency', title: 'Taux d\'escalade élevé', description: `${kpis.escalationRate.toFixed(1)}% des conversations sont escaladées`, recommendation: 'Analyser les causes et améliorer la formation des agents', priority: 'medium', impact: 'negative' }); } return insights; } catch (error) { logger.error('Error getting insights:', error); throw error; } } // Helper methods getDateRange(timeRange) { const end = new Date(); const start = new Date(); switch (timeRange) { case '1d': start.setDate(start.getDate() - 1); break; case '7d': start.setDate(start.getDate() - 7); break; case '30d': start.setDate(start.getDate() - 30); break; case '90d': start.setDate(start.getDate() - 90); break; default: start.setDate(start.getDate() - 30); } return { start, end }; } getPreviousDateRange(currentRange) { const duration = currentRange.end.getTime() - currentRange.start.getTime(); return { start: new Date(currentRange.start.getTime() - duration), end: new Date(currentRange.start.getTime()) }; } getDaysDifference(dateRange) { return Math.ceil((dateRange.end - dateRange.start) / (1000 * 60 * 60 * 24)); } calculateGrowthRate(current, previous) { if (previous === 0) return current > 0 ? 100 : 0; return ((current - previous) / previous) * 100; } async calculateAverageResponseTime(dateRange) { const result = await SupportTicket.aggregate([ { $match: { createdAt: { $gte: dateRange.start, $lte: dateRange.end }, 'sla.responseTime.actual': { $exists: true } } }, { $group: { _id: null, avgResponseTime: { $avg: '$sla.responseTime.actual' } } } ]); return result[0]?.avgResponseTime || 0; } async calculateCustomerSatisfaction(dateRange) { const result = await Conversation.aggregate([ { $match: { createdAt: { $gte: dateRange.start, $lte: dateRange.end }, 'satisfaction.rating': { $exists: true } } }, { $group: { _id: null, avgSatisfaction: { $avg: '$satisfaction.rating' } } } ]); return result[0]?.avgSatisfaction || 0; } async calculateResolutionRate(dateRange) { const [total, resolved] = await Promise.all([ Conversation.countDocuments({ createdAt: { $gte: dateRange.start, $lte: dateRange.end } }), Conversation.countDocuments({ createdAt: { $gte: dateRange.start, $lte: dateRange.end }, status: 'resolved' }) ]); return total > 0 ? (resolved / total) * 100 : 0; } async calculateEscalationRate(dateRange) { const [total, escalated] = await Promise.all([ Conversation.countDocuments({ createdAt: { $gte: dateRange.start, $lte: dateRange.end } }), Conversation.countDocuments({ createdAt: { $gte: dateRange.start, $lte: dateRange.end }, status: 'escalated' }) ]); return total > 0 ? (escalated / total) * 100 : 0; } async calculateAverageWaitTime() { // Simplified calculation - in real implementation, track wait times return Math.random() * 2 + 0.5; // 0.5-2.5 minutes } async predictPeakHours() { // Simplified prediction - in real implementation, use ML return '14:00-16:00'; } async getSystemHealth() { return { cpu: Math.random() * 30 + 40, // 40-70% memory: Math.random() * 20 + 60, // 60-80% database: Math.random() * 10 + 85, // 85-95% api: Math.random() * 50 + 100 // 100-150ms }; } // Cache management getFromCache(key) { const cached = this.cache.get(key); if (cached && Date.now() - cached.timestamp < this.cacheTimeout) { return cached.data; } return null; } setCache(key, data) { this.cache.set(key, { data, timestamp: Date.now() }); } clearCache() { this.cache.clear(); } } module.exports = new AnalyticsService();