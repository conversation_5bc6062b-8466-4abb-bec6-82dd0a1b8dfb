/** * ============================================= * [AI] ML SERVICE * Integration with existing ML service on port 5001 * Handles all machine learning predictions and model management * ============================================= */ const axios = require('axios'); const logger = require('../utils/logger'); const config = require('../config/config'); class MLService { constructor() { this.mlServiceUrl = process.env.ML_SERVICE_URL || 'http://localhost:5001'; this.timeout = 30000; // 30 seconds timeout this.retryAttempts = 3; } /** * Predict customer churn */ async predictChurn(params) { try { const { risk_level, time_range, customer_segment, limit } = params; const response = await this.makeRequest('/api/ml/churn/predict', { method: 'POST', data: { filters: { risk_level, time_range, customer_segment }, options: { limit, include_factors: true, include_recommendations: true } } }); return response.data.predictions.map(prediction => ({ customer_id: prediction.customer_id, churn_probability: prediction.churn_probability, risk_level: this.calculateRiskLevel(prediction.churn_probability), risk_factors: prediction.risk_factors || [], customer_value: prediction.customer_value || 'medium', customer_tier: prediction.customer_tier || 'standard', predicted_churn_date: prediction.predicted_churn_date, confidence: prediction.confidence || 0.85, last_interaction: prediction.last_interaction, account_age: prediction.account_age })); } catch (error) { logger.error('Error predicting churn:', error); throw new Error('Failed to predict customer churn'); } } /** * Forecast demand for staffing */ async forecastDemand(params) { try { const { hours_ahead, granularity, include_confidence } = params; const response = await this.makeRequest('/api/ml/demand/forecast', { method: 'POST', data: { forecast_params: { hours_ahead, granularity, include_confidence, include_peak_detection: true }, historical_data: await this.getHistoricalDemandData() } }); return response.data.forecast.map((point, index) => ({ hour: new Date().getHours() + index, predicted_volume: point.predicted_volume, confidence: point.confidence || 0.85, peak_probability: point.peak_probability || 0, factors: point.contributing_factors || [], variance: point.variance || 0 })); } catch (error) { logger.error('Error forecasting demand:', error); throw new Error('Failed to forecast demand'); } } /** * Predict ticket escalations */ async predictEscalations(params) { try { const { risk_level, time_range, ticket_ids, agent_id } = params; const response = await this.makeRequest('/api/ml/escalation/predict', { method: 'POST', data: { filters: { risk_level, time_range, ticket_ids, agent_id }, context: await this.getEscalationContext() } }); return response.data.predictions.map(prediction => ({ ticket_id: prediction.ticket_id, escalation_probability: prediction.escalation_probability, risk_level: this.calculateRiskLevel(prediction.escalation_probability), risk_factors: prediction.risk_factors || [], predicted_escalation_time: prediction.predicted_escalation_time, confidence: prediction.confidence || 0.91, customer_sentiment: prediction.customer_sentiment || 'neutral', agent_experience: prediction.agent_experience || 'medium', ticket_complexity: prediction.ticket_complexity || 'medium' })); } catch (error) { logger.error('Error predicting escalations:', error); throw new Error('Failed to predict escalations'); } } /** * Detect system anomalies */ async detectAnomalies(params) { try { const { time_range, severity_filter, type_filter } = params; const response = await this.makeRequest('/api/ml/anomaly/detect', { method: 'POST', data: { detection_params: { time_range, severity_filter, type_filter, include_predictions: true }, system_metrics: await this.getCurrentSystemMetrics() } }); return response.data.anomalies.map(anomaly => ({ id: anomaly.id, type: anomaly.type, severity: anomaly.severity, description: anomaly.description, detected_at: anomaly.detected_at, confidence: anomaly.confidence || 0.88, affected_metrics: anomaly.affected_metrics || [], root_cause_probability: anomaly.root_cause_probability || {}, estimated_impact: anomaly.estimated_impact || 'medium', auto_resolution_possible: anomaly.auto_resolution_possible || false })); } catch (error) { logger.error('Error detecting anomalies:', error); throw new Error('Failed to detect anomalies'); } } /** * Get model performance metrics */ async getModelMetrics() { try { const response = await this.makeRequest('/api/ml/models/metrics', { method: 'GET' }); return { accuracy: response.data.overall_accuracy, models_count: response.data.active_models, last_update: response.data.last_training_update, daily_predictions: response.data.predictions_today, overall_performance: response.data.performance_score, accuracy_trend: response.data.accuracy_trend, model_details: response.data.model_performance || {} }; } catch (error) { logger.error('Error getting model metrics:', error); throw new Error('Failed to retrieve model metrics'); } } /** * Train models with new data */ async trainModels(modelType, trainingData) { try { const response = await this.makeRequest('/api/ml/models/train', { method: 'POST', data: { model_type: modelType, training_data: trainingData, training_params: { validation_split: 0.2, epochs: 100, early_stopping: true } }, timeout: 300000 // 5 minutes for training }); return { training_id: response.data.training_id, status: response.data.status, estimated_completion: response.data.estimated_completion, model_version: response.data.model_version }; } catch (error) { logger.error('Error training models:', error); throw new Error('Failed to train models'); } } /** * Get training status */ async getTrainingStatus(trainingId) { try { const response = await this.makeRequest(`/api/ml/models/training/${trainingId}`, { method: 'GET' }); return { training_id: trainingId, status: response.data.status, progress: response.data.progress, current_epoch: response.data.current_epoch, loss: response.data.current_loss, accuracy: response.data.current_accuracy, estimated_completion: response.data.estimated_completion, error_message: response.data.error_message }; } catch (error) { logger.error('Error getting training status:', error); throw new Error('Failed to get training status'); } } /** * Deploy trained model */ async deployModel(modelId, version) { try { const response = await this.makeRequest('/api/ml/models/deploy', { method: 'POST', data: { model_id: modelId, version: version, deployment_config: { auto_rollback: true, health_check_interval: 60, performance_threshold: 0.85 } } }); return { deployment_id: response.data.deployment_id, status: response.data.status, endpoint: response.data.endpoint, health_check_url: response.data.health_check_url }; } catch (error) { logger.error('Error deploying model:', error); throw new Error('Failed to deploy model'); } } /** * Rollback model deployment */ async rollbackModel(modelId, previousVersion) { try { const response = await this.makeRequest('/api/ml/models/rollback', { method: 'POST', data: { model_id: modelId, target_version: previousVersion, rollback_reason: 'Performance degradation' } }); return { rollback_id: response.data.rollback_id, status: response.data.status, active_version: response.data.active_version }; } catch (error) { logger.error('Error rolling back model:', error); throw new Error('Failed to rollback model'); } } /** * Get model health status */ async getModelHealth() { try { const response = await this.makeRequest('/api/ml/models/health', { method: 'GET' }); return { overall_health: response.data.overall_health, models: response.data.model_status.map(model => ({ model_id: model.id, name: model.name, status: model.status, accuracy: model.current_accuracy, last_prediction: model.last_prediction_time, error_rate: model.error_rate, response_time: model.avg_response_time })), system_resources: response.data.system_resources, alerts: response.data.active_alerts || [] }; } catch (error) { logger.error('Error getting model health:', error); throw new Error('Failed to get model health'); } } /** * Submit prediction feedback for model improvement */ async submitPredictionFeedback(predictionId, actualOutcome, feedback) { try { const response = await this.makeRequest('/api/ml/feedback/submit', { method: 'POST', data: { prediction_id: predictionId, actual_outcome: actualOutcome, feedback: feedback, timestamp: new Date().toISOString() } }); return { feedback_id: response.data.feedback_id, status: response.data.status, model_update_scheduled: response.data.model_update_scheduled }; } catch (error) { logger.error('Error submitting prediction feedback:', error); throw new Error('Failed to submit prediction feedback'); } } /** * Get prediction explanations */ async getPredictionExplanation(predictionId) { try { const response = await this.makeRequest(`/api/ml/predictions/${predictionId}/explain`, { method: 'GET' }); return { prediction_id: predictionId, feature_importance: response.data.feature_importance, decision_path: response.data.decision_path, confidence_factors: response.data.confidence_factors, similar_cases: response.data.similar_cases || [], explanation_text: response.data.human_readable_explanation }; } catch (error) { logger.error('Error getting prediction explanation:', error); throw new Error('Failed to get prediction explanation'); } } /** * Make HTTP request to ML service with retry logic */ async makeRequest(endpoint, options = {}) { const config = { url: `${this.mlServiceUrl}${endpoint}`, timeout: options.timeout || this.timeout, headers: { 'Content-Type': 'application/json', 'X-Service-Name': 'chatbot-backend', 'X-Request-ID': this.generateRequestId(), ...options.headers }, ...options }; let lastError; for (let attempt = 1; attempt <= this.retryAttempts; attempt++) { try { logger.info(`ML Service request attempt ${attempt}`, { endpoint, method: options.method || 'GET', attempt }); const response = await axios(config); logger.info('ML Service request successful', { endpoint, status: response.status, responseTime: response.headers['x-response-time'] }); return response; } catch (error) { lastError = error; logger.warn(`ML Service request failed (attempt ${attempt})`, { endpoint, error: error.message, status: error.response?.status, attempt }); // Don't retry on client errors (4xx) if (error.response?.status >= 400 && error.response?.status < 500) { break; } // Wait before retry (exponential backoff) if (attempt < this.retryAttempts) { const delay = Math.pow(2, attempt) * 1000; // 2s, 4s, 8s await this.sleep(delay); } } } // All attempts failed logger.error('ML Service request failed after all attempts', { endpoint, error: lastError.message, attempts: this.retryAttempts }); throw lastError; } /** * Helper methods */ calculateRiskLevel(probability) { if (probability >= 0.8) return 'critical'; if (probability >= 0.6) return 'high'; if (probability >= 0.4) return 'medium'; return 'low'; } async getHistoricalDemandData() { // Get historical demand data for better forecasting const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); try { // This would typically come from your database return { time_range: '30d', data_points: 720, // 30 days * 24 hours last_updated: new Date().toISOString(), seasonal_patterns: ['weekday_peak', 'weekend_low'], special_events: [] }; } catch (error) { logger.warn('Could not fetch historical demand data, using defaults'); return {}; } } async getEscalationContext() { // Get context for escalation predictions try { return { current_agent_workload: 'medium', team_performance: 'good', recent_escalation_rate: 0.12, customer_satisfaction_trend: 'stable' }; } catch (error) { logger.warn('Could not fetch escalation context, using defaults'); return {}; } } async getCurrentSystemMetrics() { // Get current system metrics for anomaly detection try { return { cpu_usage: 65, memory_usage: 72, disk_usage: 45, network_latency: 12, error_rate: 0.1, response_time: 1.2, active_connections: 1250, queue_length: 15 }; } catch (error) { logger.warn('Could not fetch system metrics, using defaults'); return {}; } } generateRequestId() { return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`; } sleep(ms) { return new Promise(resolve => setTimeout(resolve, ms)); } /** * Health check for ML service */ async healthCheck() { try { const response = await axios.get(`${this.mlServiceUrl}/health`, { timeout: 5000 }); return { status: 'healthy', response_time: response.headers['x-response-time'], version: response.data.version, uptime: response.data.uptime }; } catch (error) { logger.error('ML Service health check failed:', error); return { status: 'unhealthy', error: error.message, last_check: new Date().toISOString() }; } } /** * Get available models */ async getAvailableModels() { try { const response = await this.makeRequest('/api/ml/models', { method: 'GET' }); return response.data.models.map(model => ({ id: model.id, name: model.name, type: model.type, version: model.version, status: model.status, accuracy: model.accuracy, created_at: model.created_at, last_trained: model.last_trained, description: model.description })); } catch (error) { logger.error('Error getting available models:', error); throw new Error('Failed to get available models'); } } /** * Update model configuration */ async updateModelConfig(modelId, config) { try { const response = await this.makeRequest(`/api/ml/models/${modelId}/config`, { method: 'PUT', data: config }); return { model_id: modelId, status: response.data.status, updated_config: response.data.config, restart_required: response.data.restart_required }; } catch (error) { logger.error('Error updating model config:', error); throw new Error('Failed to update model configuration'); } } /** * Get model predictions history */ async getPredictionHistory(modelId, timeRange = '24h') { try { const response = await this.makeRequest(`/api/ml/models/${modelId}/predictions`, { method: 'GET', params: { time_range: timeRange } }); return { model_id: modelId, time_range: timeRange, total_predictions: response.data.total_predictions, accuracy_rate: response.data.accuracy_rate, predictions: response.data.predictions.map(pred => ({ id: pred.id, input: pred.input, output: pred.output, confidence: pred.confidence, timestamp: pred.timestamp, actual_outcome: pred.actual_outcome, feedback_score: pred.feedback_score })) }; } catch (error) { logger.error('Error getting prediction history:', error); throw new Error('Failed to get prediction history'); } } } module.exports = MLService;