const CustomerProfile = require('../models/CustomerProfile'); const logger = require('../config/logger'); class SubscriptionService { // Catalogue des forfaits Free Mobile static PLANS = { '4G_2H': { id: 'free_4g_2h', name: 'Forfait 2€', price: 2, data: 0.05, // 50MB minutes: 120, sms: 'illimités', type: '4G' }, '4G_100GB': { id: 'free_4g_100', name: 'Forfait Free 100Go', price: 19.99, data: 100, minutes: 'illimités', sms: 'illimités', type: '4G', roaming: 25 // 25GB en Europe }, '5G_200GB': { id: 'free_5g_200', name: 'Forfait Free 5G', price: 29.99, data: 200, minutes: 'illimités', sms: 'illimités', type: '5G', roaming: 35 } }; // Catalogue des options static OPTIONS = { 'INTERNATIONAL': { id: 'intl_calls', name: 'Appels International', price: 5, description: 'Appels illimités vers 100 destinations' }, 'MAGHREB': { id: 'maghreb_special', name: 'Option Maghreb', price: 5, description: 'Appels illimités vers Algérie, Maroc, Tunisie' }, 'DATA_EXTRA': { id: 'data_20gb', name: 'Data Extra +20Go', price: 3, description: '20Go supplémentaires' }, 'ROAMING_WORLD': { id: 'roaming_world', name: 'Voyage Monde', price: 25, description: '20Go + appels dans 70 pays' } }; // Vérifier l'éligibilité pour un changement de forfait async checkPlanEligibility(userId, newPlanId) { try { const profile = await CustomerProfile.findOne({ userId }).populate('userId'); if (!profile) { return { eligible: false, reason: 'Profil client non trouvé' }; } const currentPlan = profile.subscription.planType; const newPlan = this.constructor.PLANS[newPlanId]; // Règles d'éligibilité if (currentPlan === '5G' && newPlan.type === '4G') { return { eligible: true, warning: 'Vous allez passer d\'un forfait 5G à 4G. Vous perdrez l\'accès au réseau 5G.' }; } // Vérifier si le client a des impayés if (profile.billing.outstandingBalance > 0) { return { eligible: false, reason: `Vous avez un solde impayé de ${profile.billing.outstandingBalance}€. Veuillez régulariser avant de changer de forfait.` }; } return { eligible: true, newPlan: newPlan, currentPlan: profile.subscription, priceChange: newPlan.price - profile.subscription.monthlyPrice }; } catch (error) { logger.error('Error checking plan eligibility:', error); throw error; } } // Changer de forfait async changePlan(userId, newPlanId) { try { const eligibility = await this.checkPlanEligibility(userId, newPlanId); if (!eligibility.eligible) { return { success: false, message: eligibility.reason }; } const newPlan = this.constructor.PLANS[newPlanId]; const profile = await CustomerProfile.findOne({ userId }); // Mettre à jour le forfait profile.subscription = { ...profile.subscription, planName: newPlan.name, planType: newPlan.type, monthlyPrice: newPlan.price, dataLimit: newPlan.data, callMinutes: newPlan.minutes, smsLimit: newPlan.sms, activatedDate: new Date() }; await profile.save(); logger.info(`Plan changed for user ${userId} to ${newPlanId}`); return { success: true, message: `Votre forfait a été changé avec succès pour ${newPlan.name}`, newPlan: newPlan, effectiveDate: new Date() }; } catch (error) { logger.error('Error changing plan:', error); throw error; } } // Activer une option async activateOption(userId, optionId) { try { const option = this.constructor.OPTIONS[optionId]; if (!option) { return { success: false, message: 'Option non trouvée' }; } const profile = await CustomerProfile.findOne({ userId }); // Vérifier si l'option est déjà active const existingOption = profile.activeOptions.find(opt => opt.name === option.name); if (existingOption) { return { success: false, message: 'Cette option est déjà active sur votre ligne' }; } // Ajouter l'option profile.activeOptions.push({ name: option.name, price: option.price, description: option.description, activatedDate: new Date() }); await profile.save(); return { success: true, message: `L'option ${option.name} a été activée avec succès`, option: option, newMonthlyTotal: profile.subscription.monthlyPrice + profile.activeOptions.reduce((sum, opt) => sum + opt.price, 0) }; } catch (error) { logger.error('Error activating option:', error); throw error; } } // Obtenir la consommation actuelle async getConsumption(userId) { try { const profile = await CustomerProfile.findOne({ userId }); if (!profile) { throw new Error('Profil client non trouvé'); } const dataPercentage = (profile.subscription.dataUsed / profile.subscription.dataLimit) * 100; const daysInMonth = 30; const daysPassed = new Date().getDate(); const expectedUsage = (daysPassed / daysInMonth) * 100; return { data: { used: profile.subscription.dataUsed, limit: profile.subscription.dataLimit, percentage: dataPercentage, remaining: profile.subscription.dataLimit - profile.subscription.dataUsed, unit: 'GB' }, calls: { used: profile.subscription.callMinutesUsed, limit: profile.subscription.callMinutes, remaining: profile.subscription.callMinutes === 'illimités' ? 'illimités' : profile.subscription.callMinutes - profile.subscription.callMinutesUsed }, analysis: { isOverusing: dataPercentage > expectedUsage + 20, projectedMonthEnd: (profile.subscription.dataUsed / daysPassed) * daysInMonth, recommendation: dataPercentage > 80 ? 'Attention, vous avez consommé plus de 80% de votre forfait' : null }, renewalDate: profile.subscription.renewalDate }; } catch (error) { logger.error('Error getting consumption:', error); throw error; } } // Simuler un changement de forfait async simulatePlanChange(userId, newPlanId) { try { const profile = await CustomerProfile.findOne({ userId }); const newPlan = this.constructor.PLANS[newPlanId]; const currentTotal = profile.subscription.monthlyPrice + profile.activeOptions.reduce((sum, opt) => sum + opt.price, 0); const newTotal = newPlan.price + profile.activeOptions.reduce((sum, opt) => sum + opt.price, 0); return { current: { plan: profile.subscription.planName, price: profile.subscription.monthlyPrice, total: currentTotal }, proposed: { plan: newPlan.name, price: newPlan.price, total: newTotal }, difference: newTotal - currentTotal, benefits: this.comparePlans(profile.subscription, newPlan) }; } catch (error) { logger.error('Error simulating plan change:', error); throw error; } } // Comparer deux forfaits comparePlans(currentPlan, newPlan) { const benefits = []; const drawbacks = []; if (newPlan.data > currentPlan.dataLimit) { benefits.push(`+${newPlan.data - currentPlan.dataLimit}GB de data`); } else if (newPlan.data < currentPlan.dataLimit) { drawbacks.push(`-${currentPlan.dataLimit - newPlan.data}GB de data`); } if (newPlan.type === '5G' && currentPlan.planType === '4G') { benefits.push('Accès au réseau 5G ultra-rapide'); } if (newPlan.roaming && (!currentPlan.roaming || newPlan.roaming > currentPlan.roaming)) { benefits.push(`${newPlan.roaming}GB en roaming Europe/DOM`); } return { benefits, drawbacks }; } // Obtenir l'historique de facturation async getBillingHistory(userId, limit = 12) { try { const profile = await CustomerProfile.findOne({ userId }); if (!profile || !profile.billing.paymentHistory) { return []; } return profile.billing.paymentHistory .sort((a, b) => b.date - a.date) .slice(0, limit) .map(payment => ({ date: payment.date, amount: payment.amount, status: payment.status, method: payment.method, invoiceUrl: `/api/billing/invoice/${payment._id}` // URL factice })); } catch (error) { logger.error('Error getting billing history:', error); throw error; } } } module.exports = new SubscriptionService();