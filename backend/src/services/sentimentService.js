const logger = require('../config/logger'); class SentimentService { // Mots-clés pour détecter les émotions static EMOTION_KEYWORDS = { angry: [ 'énervé', 'fâché', 'colère', 'furieux', 'marre', 'ras le bol', 'inadmissible', 'scandaleux', 'honteux', 'nul', 'pourri' ], frustrated: [ 'frustré', 'agacé', 'irrité', 'embêté', 'ennuyé', 'pénible', 'compliqué', 'galère', 'problème', 'bug', 'marche pas' ], sad: [ 'triste', 'déçu', 'malheureux', 'déprimé', 'découragé', 'abandonner', 'partir', 'résilier', 'changer' ], happy: [ 'content', 'heureux', 'satisfait', 'super', 'génial', 'parfait', 'merci', 'top', 'excellent', 'formidable', 'bien' ], neutral: [] }; // Analyser le sentiment d'un message async analyzeSentiment(text) { try { const lowerText = text.toLowerCase(); // Calcul du score de sentiment (-1 à 1) let sentimentScore = 0; let emotionScores = { angry: 0, frustrated: 0, sad: 0, happy: 0, neutral: 0 }; // Analyser les mots-clés émotionnels for (const [emotion, keywords] of Object.entries(this.constructor.EMOTION_KEYWORDS)) { for (const keyword of keywords) { if (lowerText.includes(keyword)) { emotionScores[emotion]++; // Ajuster le score de sentiment switch(emotion) { case 'angry': sentimentScore -= 0.8; break; case 'frustrated': sentimentScore -= 0.5; break; case 'sad': sentimentScore -= 0.6; break; case 'happy': sentimentScore += 0.7; break; } } } } // Détection de patterns supplémentaires const exclamationCount = (text.match(/!/g) || []).length; const capsRatio = (text.match(/[A-Z]/g) || []).length / text.length; if (exclamationCount > 2) sentimentScore -= 0.2; if (capsRatio > 0.5) sentimentScore -= 0.3; // Points d'interrogation multiples = frustration const questionMarkCount = (text.match(/\?/g) || []).length; if (questionMarkCount > 2) { emotionScores.frustrated++; sentimentScore -= 0.2; } // Normaliser le score entre -1 et 1 sentimentScore = Math.max(-1, Math.min(1, sentimentScore)); // Déterminer l'émotion dominante let dominantEmotion = 'neutral'; let maxScore = 0; for (const [emotion, score] of Object.entries(emotionScores)) { if (score > maxScore) { maxScore = score; dominantEmotion = emotion; } } // Si aucune émotion forte n'est détectée if (maxScore === 0) { dominantEmotion = sentimentScore > 0.2 ? 'happy' : sentimentScore < -0.2 ? 'frustrated' : 'neutral'; } // Calculer la magnitude (force du sentiment) const magnitude = Math.abs(sentimentScore) * (1 + maxScore * 0.2); return { score: sentimentScore, magnitude: Math.min(1, magnitude), emotion: dominantEmotion, // Juste la string, pas un objet analysis: { emotionScores, exclamationCount, capsRatio, questionMarkCount, textLength: text.length } }; } catch (error) { logger.error('Error analyzing sentiment:', error); return { score: 0, magnitude: 0, emotion: 'neutral' }; } } // Analyser le contexte émotionnel d'une conversation async analyzeConversationMood(messages) { try { if (!messages || messages.length === 0) { return { mood: 'neutral', trend: 'stable' }; } // Analyser les derniers messages const recentMessages = messages.slice(-5); const sentiments = []; for (const message of recentMessages) { if (message.sender === 'user') { const sentiment = await this.analyzeSentiment(message.content.text); sentiments.push(sentiment); } } if (sentiments.length === 0) { return { mood: 'neutral', trend: 'stable' }; } // Calculer la moyenne const avgScore = sentiments.reduce((sum, s) => sum + s.score, 0) / sentiments.length; // Déterminer la tendance let trend = 'stable'; if (sentiments.length >= 2) { const firstHalf = sentiments.slice(0, Math.floor(sentiments.length / 2)); const secondHalf = sentiments.slice(Math.floor(sentiments.length / 2)); const firstAvg = firstHalf.reduce((sum, s) => sum + s.score, 0) / firstHalf.length; const secondAvg = secondHalf.reduce((sum, s) => sum + s.score, 0) / secondHalf.length; if (secondAvg - firstAvg > 0.3) trend = 'improving'; else if (firstAvg - secondAvg > 0.3) trend = 'degrading'; } // Déterminer l'humeur globale let mood = 'neutral'; if (avgScore < -0.5) mood = 'very_negative'; else if (avgScore < -0.2) mood = 'negative'; else if (avgScore > 0.5) mood = 'very_positive'; else if (avgScore > 0.2) mood = 'positive'; return { mood, trend, averageScore: avgScore, sentiments: sentiments.map(s => ({ score: s.score, emotion: s.emotion.type })) }; } catch (error) { logger.error('Error analyzing conversation mood:', error); return { mood: 'neutral', trend: 'stable' }; } } // Adapter la réponse selon l'émotion détectée adaptResponseToEmotion(baseResponse, emotion) { const adaptations = { angry: { prefix: "Je comprends ta frustration. ", suffix: " Je suis là pour t'aider à résoudre ça rapidement.", tone: 'empathique' }, frustrated: { prefix: "Je vois que c'est embêtant. ", suffix: " On va trouver une solution ensemble.", tone: 'rassurant' }, sad: { prefix: "Je suis désolé que tu rencontres ces difficultés. ", suffix: " Je vais faire mon maximum pour t'aider.", tone: 'compatissant' }, happy: { prefix: "Super ! ", suffix: " ", tone: 'enthousiaste' }, neutral: { prefix: "", suffix: "", tone: 'professionnel' } }; const adaptation = adaptations[emotion] || adaptations.neutral; return { response: adaptation.prefix + baseResponse + adaptation.suffix, tone: adaptation.tone, shouldEscalate: emotion === 'angry' || emotion === 'very_negative' }; } // Détecter les signaux d'alerte nécessitant une escalade detectEscalationSignals(text, sentiment) { const escalationKeywords = [ 'avocat', 'justice', 'tribunal', 'plainte', 'UFC', 'que choisir', 'résilier', 'partir', 'concurrent', 'orange', 'SFR', 'bouygues', 'arnaque', 'escroquerie', 'voleur', 'inadmissible' ]; const lowerText = text.toLowerCase(); // Vérifier les mots-clés d'escalade for (const keyword of escalationKeywords) { if (lowerText.includes(keyword)) { return { shouldEscalate: true, reason: 'Mots-clés sensibles détectés', urgency: 'high', suggestedAction: 'transfer_to_agent' }; } } // Vérifier le sentiment très négatif if (sentiment.score < -0.7 || sentiment.emotion.type === 'angry') { return { shouldEscalate: true, reason: 'Client très mécontent', urgency: 'medium', suggestedAction: 'agent_review' }; } return { shouldEscalate: false }; } // Générer des suggestions d'empathie pour l'agent generateEmpathySuggestions(emotion, context) { const suggestions = { angry: [ "Reconnaître la frustration du client", "S'excuser sincèrement pour le désagrément", "Proposer une solution immédiate", "Offrir un geste commercial si justifié" ], frustrated: [ "Valider les sentiments du client", "Reformuler le problème pour montrer la compréhension", "Proposer plusieurs alternatives", "Donner un délai clair de résolution" ], sad: [ "Montrer de la compassion", "Rassurer sur la prise en charge", "Proposer un suivi personnalisé", "Mettre en avant les solutions positives" ] }; return suggestions[emotion] || []; } } module.exports = new SentimentService();