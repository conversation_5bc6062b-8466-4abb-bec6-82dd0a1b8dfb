/** * ============================================= * DATABASE SERVICE * Unified database access layer for MongoDB and TimescaleDB * Handles connections, queries, and data synchronization * ============================================= */ const mongoose = require('mongoose'); const { Pool } = require('pg'); const logger = require('../utils/logger'); const mongoSchemas = require('../database/mongoSchemas'); class DatabaseService { constructor() { this.mongoConnection = null; this.timescalePool = null; this.isConnected = false; this.schemas = mongoSchemas; // Connection retry configuration this.retryConfig = { maxRetries: 5, retryDelay: 5000, backoffMultiplier: 2 }; } /** * Initialize database connections */ async initialize() { try { await Promise.all([ this.connectMongoDB(), this.connectTimescaleDB() ]); this.isConnected = true; logger.info('Database service initialized successfully'); } catch (error) { logger.error('Failed to initialize database service:', error); throw error; } } /** * Connect to MongoDB */ async connectMongoDB() { try { const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/freemobile_chatbot'; await mongoose.connect(mongoUri, { useNewUrlParser: true, useUnifiedTopology: true, maxPoolSize: 10, serverSelectionTimeoutMS: 5000, socketTimeoutMS: 45000, bufferCommands: false, bufferMaxEntries: 0 }); this.mongoConnection = mongoose.connection; // Set up event handlers this.mongoConnection.on('error', (error) => { logger.error('MongoDB connection error:', error); }); this.mongoConnection.on('disconnected', () => { logger.warn('MongoDB disconnected'); this.isConnected = false; }); this.mongoConnection.on('reconnected', () => { logger.info('MongoDB reconnected'); this.isConnected = true; }); logger.info('Connected to MongoDB successfully'); } catch (error) { logger.error('MongoDB connection failed:', error); throw error; } } /** * Connect to TimescaleDB */ async connectTimescaleDB() { try { const timescaleConfig = { host: process.env.TIMESCALE_HOST || 'localhost', port: process.env.TIMESCALE_PORT || 5432, database: process.env.TIMESCALE_DB || 'freemobile_analytics', user: process.env.TIMESCALE_USER || 'postgres', password: process.env.TIMESCALE_PASSWORD || 'password', max: 20, idleTimeoutMillis: 30000, connectionTimeoutMillis: 5000, }; this.timescalePool = new Pool(timescaleConfig); // Test connection const client = await this.timescalePool.connect(); await client.query('SELECT NOW()'); client.release(); logger.info('Connected to TimescaleDB successfully'); } catch (error) { logger.error('TimescaleDB connection failed:', error); throw error; } } /** * MongoDB Operations */ // Get MongoDB collection collection(collectionName) { if (!this.isConnected) { throw new Error('Database not connected'); } return this.mongoConnection.db.collection(collectionName); } // Get MongoDB model model(modelName) { if (!this.schemas[modelName]) { throw new Error(`Model ${modelName} not found`); } return this.schemas[modelName]; } // Create ObjectId objectId(id) { return new mongoose.Types.ObjectId(id); } /** * TimescaleDB Operations */ // Execute TimescaleDB query async executeQuery(query, params = []) { try { if (!this.timescalePool) { throw new Error('TimescaleDB not connected'); } const client = await this.timescalePool.connect(); try { const result = await client.query(query, params); return result; } finally { client.release(); } } catch (error) { logger.error('TimescaleDB query error:', error); throw error; } } // Insert time-series data async insertTimeSeriesData(table, data) { try { if (!Array.isArray(data)) { data = [data]; } if (data.length === 0) { return; } // Build insert query const columns = Object.keys(data[0]); const placeholders = data.map((_, index) => `(${columns.map((_, colIndex) => `$${index * columns.length + colIndex + 1}`).join(', ')})` ).join(', '); const query = ` INSERT INTO ${table} (${columns.join(', ')}) VALUES ${placeholders} `; const values = data.flatMap(row => columns.map(col => row[col])); await this.executeQuery(query, values); } catch (error) { logger.error('Error inserting time-series data:', error); throw error; } } // Get time-series data with aggregation async getTimeSeriesData(table, options = {}) { try { const { startTime, endTime, interval = '1 hour', aggregation = 'AVG', columns = ['*'], groupBy = [], orderBy = 'time DESC', limit } = options; let query = ` SELECT time_bucket('${interval}', time) AS bucket, ${columns.map(col => col === '*' ? '*' : `${aggregation}(${col}) AS ${col}`).join(', ')} FROM ${table} `; const params = []; const conditions = []; if (startTime) { params.push(startTime); conditions.push(`time >= $${params.length}`); } if (endTime) { params.push(endTime); conditions.push(`time <= $${params.length}`); } if (conditions.length > 0) { query += ` WHERE ${conditions.join(' AND ')}`; } if (groupBy.length > 0) { query += ` GROUP BY bucket, ${groupBy.join(', ')}`; } else { query += ` GROUP BY bucket`; } if (orderBy) { query += ` ORDER BY ${orderBy}`; } if (limit) { query += ` LIMIT ${limit}`; } const result = await this.executeQuery(query, params); return result.rows; } catch (error) { logger.error('Error getting time-series data:', error); throw error; } } /** * Specialized Methods for Application Features */ // Save agent performance metrics async saveAgentPerformanceMetrics(metrics) { try { await this.insertTimeSeriesData('agent_performance_metrics', { time: new Date(), agent_id: metrics.agent_id, session_id: metrics.session_id, empathy_score: metrics.empathy_score, efficiency_score: metrics.efficiency_score, accuracy_score: metrics.accuracy_score, overall_score: metrics.overall_score, customer_satisfaction: metrics.customer_satisfaction, response_time_ms: metrics.response_time_ms, messages_count: metrics.messages_count, scenario_difficulty: metrics.scenario_difficulty, scenario_category: metrics.scenario_category, completion_status: metrics.completion_status }); } catch (error) { logger.error('Error saving agent performance metrics:', error); throw error; } } // Save churn prediction async saveChurnPrediction(prediction) { try { // Save to MongoDB for detailed storage const churnPrediction = new this.schemas.ChurnPrediction(prediction); await churnPrediction.save(); // Save to TimescaleDB for time-series analysis await this.insertTimeSeriesData('churn_prediction_metrics', { time: new Date(), customer_id: prediction.customer_id, churn_probability: prediction.churn_probability, risk_level: prediction.risk_level, customer_tier: prediction.customer_tier, customer_value: prediction.customer_value, model_version: prediction.model_version, confidence_score: prediction.confidence, risk_factors: prediction.risk_factors, intervention_applied: prediction.intervention_applied || false }); return churnPrediction; } catch (error) { logger.error('Error saving churn prediction:', error); throw error; } } // Save escalation prediction async saveEscalationPrediction(prediction) { try { // Save to MongoDB const escalationPrediction = new this.schemas.EscalationPrediction(prediction); await escalationPrediction.save(); // Save to TimescaleDB await this.insertTimeSeriesData('escalation_prediction_metrics', { time: new Date(), ticket_id: prediction.ticket_id, agent_id: prediction.agent_id, escalation_probability: prediction.escalation_probability, risk_level: prediction.risk_level, predicted_escalation_time: prediction.predicted_escalation_time, model_version: prediction.model_version, confidence_score: prediction.confidence, risk_factors: prediction.risk_factors, prevention_applied: prediction.prevention_applied || false }); return escalationPrediction; } catch (error) { logger.error('Error saving escalation prediction:', error); throw error; } } // Save system performance metrics async saveSystemPerformanceMetrics(metrics) { try { await this.insertTimeSeriesData('system_performance_metrics', { time: new Date(), service_name: metrics.service_name, cpu_usage_percent: metrics.cpu_usage_percent, memory_usage_percent: metrics.memory_usage_percent, disk_usage_percent: metrics.disk_usage_percent, network_latency_ms: metrics.network_latency_ms, response_time_ms: metrics.response_time_ms, error_rate_percent: metrics.error_rate_percent, throughput_requests_per_minute: metrics.throughput_requests_per_minute, active_connections: metrics.active_connections, queue_length: metrics.queue_length, uptime_seconds: metrics.uptime_seconds }); } catch (error) { logger.error('Error saving system performance metrics:', error); throw error; } } // Save sentiment analysis async saveSentimentAnalysis(analysis) { try { await this.insertTimeSeriesData('sentiment_analysis_metrics', { time: new Date(), conversation_id: analysis.conversation_id, agent_id: analysis.agent_id, customer_id: analysis.customer_id, message_id: analysis.message_id, sentiment_score: analysis.sentiment_score, emotion_breakdown: JSON.stringify(analysis.emotion_breakdown), escalation_risk: analysis.escalation_risk, confidence_score: analysis.confidence_score, platform: analysis.platform, message_length: analysis.message_length, response_time_ms: analysis.response_time_ms }); } catch (error) { logger.error('Error saving sentiment analysis:', error); throw error; } } // Get agent performance trends async getAgentPerformanceTrends(agentId, days = 30) { try { const result = await this.executeQuery( 'SELECT * FROM get_agent_performance_trend($1, $2)', [agentId, days] ); return result.rows; } catch (error) { logger.error('Error getting agent performance trends:', error); throw error; } } // Get system health score async getSystemHealthScore(serviceName = null, hours = 1) { try { const result = await this.executeQuery( 'SELECT get_system_health_score($1, $2) as health_score', [serviceName, hours] ); return result.rows[0]?.health_score || 0; } catch (error) { logger.error('Error getting system health score:', error); throw error; } } /** * Health Check and Monitoring */ async healthCheck() { try { const health = { mongodb: false, timescaledb: false, overall: false }; // Check MongoDB try { await this.mongoConnection.db.admin().ping(); health.mongodb = true; } catch (error) { logger.error('MongoDB health check failed:', error); } // Check TimescaleDB try { await this.executeQuery('SELECT 1'); health.timescaledb = true; } catch (error) { logger.error('TimescaleDB health check failed:', error); } health.overall = health.mongodb && health.timescaledb; return health; } catch (error) { logger.error('Database health check failed:', error); return { mongodb: false, timescaledb: false, overall: false }; } } /** * Cleanup and Shutdown */ async close() { try { if (this.mongoConnection) { await mongoose.connection.close(); } if (this.timescalePool) { await this.timescalePool.end(); } this.isConnected = false; logger.info('Database connections closed'); } catch (error) { logger.error('Error closing database connections:', error); throw error; } } /** * Transaction Support */ async withTransaction(callback) { const session = await mongoose.startSession(); try { session.startTransaction(); const result = await callback(session); await session.commitTransaction(); return result; } catch (error) { await session.abortTransaction(); throw error; } finally { session.endSession(); } } async withTimescaleTransaction(callback) { const client = await this.timescalePool.connect(); try { await client.query('BEGIN'); const result = await callback(client); await client.query('COMMIT'); return result; } catch (error) { await client.query('ROLLBACK'); throw error; } finally { client.release(); } } } module.exports = DatabaseService;