const crypto = require('crypto'); const jwt = require('jsonwebtoken'); const speakeasy = require('speakeasy'); const QRCode = require('qrcode'); const User = require('../models/User'); const logger = require('../config/logger'); /** * SERVICE DE SÉCURITÉ AVANCÉ * Authentification renforcée, 2FA, validation continue */ class SecurityService { constructor() { this.suspiciousActivities = new Map(); this.rateLimitStore = new Map(); this.sessionValidations = new Map(); } /** * [SECURITY] AUTHENTIFICATION 2FA */ /** * Générer un secret 2FA pour un utilisateur */ async generateTwoFactorSecret(userId, userEmail) { try { const secret = speakeasy.generateSecret({ name: `Free Mobile Chatbot (${userEmail})`, issuer: 'Free Mobile', length: 32 }); // Sauvegarder le secret temporaire (non activé) await User.findByIdAndUpdate(userId, { 'security.twoFactor.tempSecret': secret.base32, 'security.twoFactor.enabled': false }); // Générer le QR code const qrCodeDataURL = await QRCode.toDataURL(secret.otpauth_url); logger.info(`2FA secret generated for user: ${userId}`); return { secret: secret.base32, qrCode: qrCodeDataURL, backupCodes: this.generateBackupCodes(), setupUrl: secret.otpauth_url }; } catch (error) { logger.error('Error generating 2FA secret:', error); throw new Error('Impossible de générer le secret 2FA'); } } /** * Activer la 2FA avec vérification du code */ async enableTwoFactor(userId, verificationCode) { try { const user = await User.findById(userId); if (!user || !user.security?.twoFactor?.tempSecret) { throw new Error('Secret 2FA non trouvé'); } // Vérifier le code de confirmation const verified = speakeasy.totp.verify({ secret: user.security.twoFactor.tempSecret, encoding: 'base32', token: verificationCode, window: 2 // Permettre 2 périodes de tolérance }); if (!verified) { logger.warn(`Failed 2FA activation for user: ${userId}`); throw new Error('Code de vérification invalide'); } // Activer la 2FA const backupCodes = this.generateBackupCodes(); await User.findByIdAndUpdate(userId, { 'security.twoFactor.enabled': true, 'security.twoFactor.secret': user.security.twoFactor.tempSecret, 'security.twoFactor.backupCodes': backupCodes.map(code => ({ code: this.hashBackupCode(code), used: false })), 'security.twoFactor.activatedAt': new Date(), $unset: { 'security.twoFactor.tempSecret': 1 } }); logger.info(`2FA enabled for user: ${userId}`); return { success: true, backupCodes, message: 'Authentification à deux facteurs activée avec succès' }; } catch (error) { logger.error('Error enabling 2FA:', error); throw error; } } /** * Vérifier un code 2FA */ async verifyTwoFactorCode(userId, code) { try { const user = await User.findById(userId); if (!user?.security?.twoFactor?.enabled) { throw new Error('2FA non activée'); } // Vérifier le code TOTP const verified = speakeasy.totp.verify({ secret: user.security.twoFactor.secret, encoding: 'base32', token: code, window: 2 }); if (verified) { logger.info(`2FA verification successful for user: ${userId}`); return { success: true, method: 'totp' }; } // Vérifier les codes de backup const backupCodeVerified = await this.verifyBackupCode(userId, code); if (backupCodeVerified) { logger.info(`2FA backup code used for user: ${userId}`); return { success: true, method: 'backup' }; } logger.warn(`2FA verification failed for user: ${userId}`); return { success: false, error: 'Code invalide' }; } catch (error) { logger.error('Error verifying 2FA:', error); throw error; } } /** * Vérifier un code de backup */ async verifyBackupCode(userId, code) { try { const user = await User.findById(userId); const hashedCode = this.hashBackupCode(code); const backupCode = user.security.twoFactor.backupCodes.find( bc => bc.code === hashedCode && !bc.used ); if (backupCode) { // Marquer le code comme utilisé await User.findOneAndUpdate( { _id: userId, 'security.twoFactor.backupCodes.code': hashedCode }, { $set: { 'security.twoFactor.backupCodes.$.used': true } } ); return true; } return false; } catch (error) { logger.error('Error verifying backup code:', error); return false; } } /** * [SEARCH] VALIDATION CONTINUE DE SÉCURITÉ */ /** * Analyser l'activité suspecte */ async analyzeSuspiciousActivity(userId, activity) { const suspiciousFactors = []; // Vérifications de sécurité if (activity.ipAddress && await this.isIPSuspicious(activity.ipAddress)) { suspiciousFactors.push('suspicious_ip'); } if (activity.userAgent && this.isUserAgentSuspicious(activity.userAgent)) { suspiciousFactors.push('suspicious_user_agent'); } if (activity.location && await this.isLocationAnomalous(userId, activity.location)) { suspiciousFactors.push('anomalous_location'); } if (await this.hasUnusualActivityPattern(userId, activity)) { suspiciousFactors.push('unusual_pattern'); } // Calculer le score de risque const riskScore = this.calculateRiskScore(suspiciousFactors); // Stocker l'analyse this.suspiciousActivities.set(userId, { timestamp: new Date(), factors: suspiciousFactors, riskScore, activity }); logger.info(`Security analysis for user ${userId}: risk score ${riskScore}`); return { riskScore, factors: suspiciousFactors, requiresAdditionalAuth: riskScore >= 0.7, requiresManualReview: riskScore >= 0.9 }; } /** * Validation de session continue */ async validateSessionContinuously(userId, sessionToken, currentActivity) { try { // Vérifier la validité du token const tokenValid = this.verifySessionToken(sessionToken); if (!tokenValid) { return { valid: false, reason: 'invalid_token' }; } // Analyser l'activité actuelle const securityAnalysis = await this.analyzeSuspiciousActivity(userId, currentActivity); // Vérifier si une validation additionnelle est requise if (securityAnalysis.requiresAdditionalAuth) { return { valid: false, reason: 'additional_auth_required', riskScore: securityAnalysis.riskScore, factors: securityAnalysis.factors }; } // Mettre à jour la validation de session this.sessionValidations.set(userId, { lastValidated: new Date(), riskScore: securityAnalysis.riskScore, sessionToken }); return { valid: true, riskScore: securityAnalysis.riskScore, nextValidationIn: this.getNextValidationInterval(securityAnalysis.riskScore) }; } catch (error) { logger.error('Error in continuous session validation:', error); return { valid: false, reason: 'validation_error' }; } } /** * PROTECTION CONTRE LES ATTAQUES */ /** * Rate limiting avancé */ async checkRateLimit(identifier, action, limits = {}) { const defaultLimits = { login: { count: 5, window: 15 * 60 * 1000 }, // 5 tentatives en 15 min message: { count: 100, window: 60 * 1000 }, // 100 messages par minute api: { count: 1000, window: 60 * 60 * 1000 } // 1000 appels API par heure }; const limit = limits[action] || defaultLimits[action] || defaultLimits.api; const key = `${identifier}:${action}`; const now = Date.now(); let attempts = this.rateLimitStore.get(key) || []; // Nettoyer les anciennes tentatives attempts = attempts.filter(timestamp => (now - timestamp) < limit.window); if (attempts.length >= limit.count) { logger.warn(`Rate limit exceeded for ${identifier} on action ${action}`); return { allowed: false, resetIn: limit.window - (now - attempts[0]), attemptsRemaining: 0 }; } // Ajouter la tentative actuelle attempts.push(now); this.rateLimitStore.set(key, attempts); return { allowed: true, attemptsRemaining: limit.count - attempts.length, resetIn: limit.window }; } /** * Détection d'injection et d'attaques */ detectSecurityThreats(input) { const threats = []; // Patterns d'injection SQL/NoSQL const sqlPatterns = [ /(\s*(select|insert|update|delete|drop|create|alter)\s+)/gi, /(\$where|\$ne|\$gt|\$lt|\$regex)/gi, /(union\s+select|or\s+1=1|and\s+1=1)/gi ]; // Patterns XSS const xssPatterns = [ /<script.*?>.*?<\/script>/gi, /javascript:/gi, /on\w+\s*=/gi, /<iframe.*?>/gi ]; // Vérifications sqlPatterns.forEach(pattern => { if (pattern.test(input)) { threats.push('sql_injection'); } }); xssPatterns.forEach(pattern => { if (pattern.test(input)) { threats.push('xss_attempt'); } }); if (threats.length > 0) { logger.warn(`Security threats detected: ${threats.join(', ')}`); } return { threats, isSafe: threats.length === 0, sanitizedInput: this.sanitizeInput(input) }; } /** * [CONFIG] MÉTHODES UTILITAIRES */ /** * Générer des codes de backup */ generateBackupCodes() { const codes = []; for (let i = 0; i < 10; i++) { codes.push(crypto.randomBytes(4).toString('hex').toUpperCase()); } return codes; } /** * Hasher un code de backup */ hashBackupCode(code) { return crypto.createHash('sha256').update(code).digest('hex'); } /** * Vérifier si une IP est suspecte */ async isIPSuspicious(ipAddress) { // Liste noire d'IPs connues const blacklistedIPs = new Set([ // IPs de test malveillantes ]); // Vérifier les ranges d'IPs suspectes const suspiciousRanges = [ /^10\./, // IPs privées utilisées dans des attaques /^192\.168\./, // IPs locales suspectes en production ]; if (blacklistedIPs.has(ipAddress)) { return true; } return suspiciousRanges.some(range => range.test(ipAddress)); } /** * Vérifier si le User-Agent est suspect */ isUserAgentSuspicious(userAgent) { const suspiciousPatterns = [ /bot|crawler|spider/i, /curl|wget|python/i, /^$/, /automated|script/i ]; return suspiciousPatterns.some(pattern => pattern.test(userAgent)); } /** * Vérifier si la localisation est anormale */ async isLocationAnomalous(userId, location) { // Logique simplifiée - en production, utiliser des services de géolocalisation const user = await User.findById(userId); const lastKnownLocation = user.security?.lastKnownLocation; if (!lastKnownLocation) { // Première connexion, sauvegarder la location await User.findByIdAndUpdate(userId, { 'security.lastKnownLocation': location }); return false; } // Calculer la distance (formule simplifiée) const distance = this.calculateDistance(lastKnownLocation, location); // Anomalie si distance > 1000km et connexion récente return distance > 1000; } /** * Détecter des patterns d'activité inhabituels */ async hasUnusualActivityPattern(userId, activity) { // Vérifier l'heure de connexion const hour = new Date().getHours(); const isUnusualHour = hour < 6 || hour > 23; // Vérifier la fréquence des connexions const recentConnections = await this.getRecentConnections(userId); const tooManyConnections = recentConnections.length > 10; return isUnusualHour || tooManyConnections; } /** * Calculer le score de risque */ calculateRiskScore(factors) { const factorWeights = { suspicious_ip: 0.3, suspicious_user_agent: 0.2, anomalous_location: 0.4, unusual_pattern: 0.2 }; let score = 0; factors.forEach(factor => { score += factorWeights[factor] || 0.1; }); return Math.min(score, 1.0); } /** * Vérifier la validité d'un token de session */ verifySessionToken(token) { try { jwt.verify(token, process.env.JWT_SECRET); return true; } catch { return false; } } /** * Calculer l'intervalle de la prochaine validation */ getNextValidationInterval(riskScore) { if (riskScore > 0.8) return 5 * 60 * 1000; // 5 minutes if (riskScore > 0.5) return 30 * 60 * 1000; // 30 minutes return 60 * 60 * 1000; // 1 heure } /** * Calculer la distance entre deux points */ calculateDistance(point1, point2) { // Formule de Haversine simplifiée const R = 6371; // Rayon de la Terre en km const dLat = (point2.latitude - point1.latitude) * Math.PI / 180; const dLon = (point2.longitude - point1.longitude) * Math.PI / 180; const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(point1.latitude * Math.PI / 180) * Math.cos(point2.latitude * Math.PI / 180) * Math.sin(dLon / 2) * Math.sin(dLon / 2); const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a)); return R * c; } /** * Obtenir les connexions récentes */ async getRecentConnections(userId) { // Simulation - en production, utiliser une vraie base de données de logs return []; } /** * Nettoyer les entrées suspectes */ sanitizeInput(input) { return input .replace(/<script.*?>.*?<\/script>/gi, '') .replace(/javascript:/gi, '') .replace(/on\w+\s*=/gi, '') .replace(/<iframe.*?>/gi, ''); } /** * Générer un token de session sécurisé */ generateSecureSessionToken(userId, additionalClaims = {}) { const payload = { userId, sessionId: crypto.randomUUID(), issuedAt: Date.now(), securityLevel: additionalClaims.twoFactorVerified ? 'high' : 'standard', ...additionalClaims }; return jwt.sign(payload, process.env.JWT_SECRET, { expiresIn: additionalClaims.twoFactorVerified ? '24h' : '1h' }); } /** * Nettoyer les données expirées */ cleanup() { const now = Date.now(); const maxAge = 24 * 60 * 60 * 1000; // 24 heures // Nettoyer les activités suspectes for (const [userId, data] of this.suspiciousActivities.entries()) { if (now - data.timestamp.getTime() > maxAge) { this.suspiciousActivities.delete(userId); } } // Nettoyer le rate limiting for (const [key, attempts] of this.rateLimitStore.entries()) { const validAttempts = attempts.filter(timestamp => (now - timestamp) < maxAge); if (validAttempts.length === 0) { this.rateLimitStore.delete(key); } else { this.rateLimitStore.set(key, validAttempts); } } logger.info('Security service cleanup completed'); } } module.exports = new SecurityService();