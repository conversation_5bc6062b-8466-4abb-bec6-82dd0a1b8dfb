/** * ============================================= * [AI] ENHANCED AI SERVICE * Advanced AI-powered suggestions and contextual intelligence * Integrates with existing AI services and OpenAI * ============================================= */ const axios = require('axios'); const logger = require('../utils/logger'); const DatabaseService = require('./databaseService'); class AIService { constructor() { this.db = new DatabaseService(); this.openaiApiKey = process.env.OPENAI_API_KEY; this.openaiBaseUrl = 'https://api.openai.com/v1'; this.timeout = 30000; } /** * Generate customer persona for simulation */ async generateCustomerPersona(customerProfile) { try { const prompt = this.buildPersonaPrompt(customerProfile); const response = await this.callOpenAI({ model: 'gpt-4', messages: [ { role: 'system', content: 'You are an expert at creating realistic customer personas for training simulations. Create detailed, believable customer personalities.' }, { role: 'user', content: prompt } ], temperature: 0.8, max_tokens: 500 }); const persona = this.parsePersonaResponse(response.choices[0].message.content); return { id: this.generateId(), name: persona.name, age: persona.age, personality_traits: persona.personality_traits, communication_style: persona.communication_style, frustration_triggers: persona.frustration_triggers, satisfaction_drivers: persona.satisfaction_drivers, background: persona.background, current_mood: persona.current_mood, patience_level: persona.patience_level }; } catch (error) { logger.error('Error generating customer persona:', error); throw error; } } /** * Generate initial customer message for simulation */ async generateInitialMessage(customerPersona, scenario) { try { const prompt = ` Customer Persona: ${JSON.stringify(customerPersona)} Scenario: ${JSON.stringify(scenario)} Generate a realistic initial message from this customer based on their persona and the scenario context. The message should reflect their communication style, current mood, and the specific issue they're facing. Keep it natural and conversational, as if they're really contacting customer support. `; const response = await this.callOpenAI({ model: 'gpt-4', messages: [ { role: 'system', content: 'You are simulating a customer contacting support. Be authentic and match the persona exactly.' }, { role: 'user', content: prompt } ], temperature: 0.7, max_tokens: 200 }); return response.choices[0].message.content.trim(); } catch (error) { logger.error('Error generating initial message:', error); throw error; } } /** * Analyze agent response for performance metrics */ async analyzeAgentResponse(analysisData) { try { const { message, context, conversation_history, customer_profile } = analysisData; const prompt = ` Analyze this customer service agent response: Agent Message: "${message}" Customer Profile: ${JSON.stringify(customer_profile)} Conversation Context: ${JSON.stringify(context)} Rate the response on: 1. Empathy (0-100): How well does it show understanding and care? 2. Accuracy (0-100): How accurate and helpful is the information? 3. Professionalism (0-100): How professional and appropriate is the tone? 4. Problem-solving (0-100): How well does it address the customer's issue? Provide scores and brief explanations. `; const response = await this.callOpenAI({ model: 'gpt-4', messages: [ { role: 'system', content: 'You are an expert customer service trainer. Analyze agent responses objectively and provide constructive feedback.' }, { role: 'user', content: prompt } ], temperature: 0.3, max_tokens: 400 }); return this.parseAnalysisResponse(response.choices[0].message.content); } catch (error) { logger.error('Error analyzing agent response:', error); throw error; } } /** * Generate AI suggestions for agent improvement */ async generateSuggestions(suggestionData) { try { const { agent_message, session_context, analysis } = suggestionData; const prompt = ` Based on this agent response analysis: Agent Message: "${agent_message}" Performance Analysis: ${JSON.stringify(analysis)} Session Context: ${JSON.stringify(session_context)} Generate 3-5 specific suggestions to improve the agent's response. Focus on actionable advice that would make the response more effective. Include alternative phrasings where appropriate. `; const response = await this.callOpenAI({ model: 'gpt-4', messages: [ { role: 'system', content: 'You are a customer service coach providing specific, actionable improvement suggestions.' }, { role: 'user', content: prompt } ], temperature: 0.6, max_tokens: 600 }); return this.parseSuggestionsResponse(response.choices[0].message.content); } catch (error) { logger.error('Error generating suggestions:', error); throw error; } } /** * Generate customer response in simulation */ async generateCustomerResponse(responseData) { try { const { agent_message, customer_persona, conversation_history, scenario } = responseData; const prompt = ` Customer Persona: ${JSON.stringify(customer_persona)} Scenario: ${JSON.stringify(scenario)} Agent's Message: "${agent_message}" Conversation History: ${JSON.stringify(conversation_history)} Generate the customer's response based on: 1. Their personality and communication style 2. How the agent's message would affect their mood 3. Whether their issue is being addressed effectively 4. Their patience level and frustration triggers Also provide: - Updated sentiment score (-1 to 1) - Satisfaction level (1-10) - Escalation risk (0-1) `; const response = await this.callOpenAI({ model: 'gpt-4', messages: [ { role: 'system', content: 'You are simulating a customer response. Be realistic and consistent with the persona.' }, { role: 'user', content: prompt } ], temperature: 0.7, max_tokens: 300 }); return this.parseCustomerResponse(response.choices[0].message.content); } catch (error) { logger.error('Error generating customer response:', error); throw error; } } /** * Generate real-time feedback during simulation */ async generateRealTimeFeedback(feedbackData) { try { const { agent_performance, customer_reaction, session_progress } = feedbackData; const prompt = ` Agent Performance: ${JSON.stringify(agent_performance)} Customer Reaction: ${JSON.stringify(customer_reaction)} Session Progress: ${JSON.stringify(session_progress)} Provide brief, encouraging real-time feedback for the agent. Focus on what they're doing well and one key area for immediate improvement. Keep it concise and actionable. `; const response = await this.callOpenAI({ model: 'gpt-3.5-turbo', messages: [ { role: 'system', content: 'You are a supportive AI coach providing real-time feedback during training.' }, { role: 'user', content: prompt } ], temperature: 0.5, max_tokens: 150 }); return { type: this.determineFeedbackType(agent_performance), message: response.choices[0].message.content.trim(), category: this.categorizeFeedback(agent_performance), timestamp: new Date().toISOString() }; } catch (error) { logger.error('Error generating real-time feedback:', error); throw error; } } /** * Generate comprehensive feedback at simulation end */ async generateComprehensiveFeedback(feedbackData) { try { const { session, final_metrics, completion_reason } = feedbackData; const prompt = ` Simulation Session Summary: - Duration: ${final_metrics.total_duration} minutes - Messages: ${final_metrics.messages_count} - Overall Score: ${final_metrics.overall_score}% - Empathy: ${final_metrics.empathy_score}% - Efficiency: ${final_metrics.efficiency_score}% - Accuracy: ${final_metrics.accuracy_score}% - Customer Satisfaction: ${final_metrics.customer_satisfaction}/10 - Completion: ${completion_reason} Provide comprehensive feedback including: 1. Overall performance summary 2. Key strengths demonstrated 3. Areas for improvement 4. Specific recommendations for skill development 5. Next steps for continued learning `; const response = await this.callOpenAI({ model: 'gpt-4', messages: [ { role: 'system', content: 'You are an expert customer service trainer providing detailed performance feedback.' }, { role: 'user', content: prompt } ], temperature: 0.4, max_tokens: 800 }); return this.parseComprehensiveFeedback(response.choices[0].message.content); } catch (error) { logger.error('Error generating comprehensive feedback:', error); throw error; } } /** * Generate contextual suggestions for live conversations */ async generateContextualSuggestions(suggestionData) { try { const { ticket_id, customer_profile, agent_profile, conversation_context, urgency_level, platform } = suggestionData; const prompt = ` Generate contextual response suggestions for this customer service interaction: Customer Profile: ${JSON.stringify(customer_profile)} Agent Profile: ${JSON.stringify(agent_profile)} Conversation Context: ${JSON.stringify(conversation_context)} Urgency Level: ${urgency_level} Platform: ${platform} Provide 3-5 response suggestions that are: 1. Contextually appropriate 2. Personalized to the customer 3. Suitable for the platform 4. Matched to the urgency level Include reasoning for each suggestion. `; const response = await this.callOpenAI({ model: 'gpt-4', messages: [ { role: 'system', content: 'You are an AI assistant helping customer service agents with contextual response suggestions.' }, { role: 'user', content: prompt } ], temperature: 0.6, max_tokens: 800 }); return this.parseContextualSuggestions(response.choices[0].message.content); } catch (error) { logger.error('Error generating contextual suggestions:', error); throw error; } } /** * Call OpenAI API with error handling and retries */ async callOpenAI(requestData) { try { const response = await axios.post( `${this.openaiBaseUrl}/chat/completions`, requestData, { headers: { 'Authorization': `Bearer ${this.openaiApiKey}`, 'Content-Type': 'application/json' }, timeout: this.timeout } ); return response.data; } catch (error) { logger.error('OpenAI API call failed:', error); // Fallback to simulated responses in case of API failure return this.generateFallbackResponse(requestData); } } /** * Helper methods for parsing responses */ buildPersonaPrompt(customerProfile) { return ` Create a realistic customer persona based on this profile: ${JSON.stringify(customerProfile)} Include: name, age, personality traits, communication style, frustration triggers, satisfaction drivers, background, current mood, and patience level. Make it realistic and detailed for training purposes. `; } parsePersonaResponse(content) { // Parse the AI response into structured persona data // This is a simplified version - in production, you'd use more sophisticated parsing return { name: this.extractField(content, 'name') || 'Customer', age: this.extractField(content, 'age') || 35, personality_traits: ['patient', 'detail-oriented'], communication_style: 'direct', frustration_triggers: ['long wait times', 'unclear answers'], satisfaction_drivers: ['quick resolution', 'empathy'], background: 'Long-time customer', current_mood: 'neutral', patience_level: 'medium' }; } parseAnalysisResponse(content) { return { empathy_score: this.extractScore(content, 'empathy') || 75, accuracy_score: this.extractScore(content, 'accuracy') || 80, professionalism_score: this.extractScore(content, 'professionalism') || 85, problem_solving_score: this.extractScore(content, 'problem') || 70, overall_feedback: content, improvement_areas: ['empathy', 'clarity'] }; } parseSuggestionsResponse(content) { // Parse suggestions from AI response const suggestions = content.split('\n').filter(line => line.trim().length > 0); return suggestions.slice(0, 5).map((suggestion, index) => ({ id: this.generateId(), type: 'response', content: suggestion.replace(/^\d+\.?\s*/, '').trim(), confidence: 0.8 + (Math.random() * 0.15), reasoning: 'AI-generated suggestion based on performance analysis', alternatives: [] })); } parseCustomerResponse(content) { return { message: content.split('\n')[0] || content, sentiment: this.extractSentiment(content) || 0, satisfaction: this.extractSatisfaction(content) || 5, escalation_risk: this.extractEscalationRisk(content) || 0.3, empathy_rating: Math.floor(Math.random() * 10) + 1 }; } parseComprehensiveFeedback(content) { return { overall_summary: content.substring(0, 200), strengths: ['Good communication', 'Professional tone'], improvement_areas: ['Response time', 'Technical knowledge'], recommendations: ['Practice active listening', 'Study product knowledge'], next_steps: ['Complete advanced training module'], detailed_feedback: content }; } parseContextualSuggestions(content) { const suggestions = content.split('\n').filter(line => line.trim().length > 0); return suggestions.slice(0, 5).map((suggestion, index) => ({ id: this.generateId(), type: 'response', content: suggestion.replace(/^\d+\.?\s*/, '').trim(), confidence: 0.85 + (Math.random() * 0.1), reasoning: 'Contextually appropriate based on conversation analysis', alternatives: [], personalization: { customer_tier: 'standard', adaptation_score: 0.8 } })); } generateFallbackResponse(requestData) { // Generate fallback responses when OpenAI is unavailable logger.warn('Using fallback AI response due to API failure'); return { choices: [{ message: { content: 'Thank you for contacting us. I understand your concern and I\'m here to help you resolve this issue.' } }] }; } // Utility methods extractField(content, fieldName) { const regex = new RegExp(`${fieldName}:?\\s*([^\\n]+)`, 'i'); const match = content.match(regex); return match ? match[1].trim() : null; } extractScore(content, scoreName) { const regex = new RegExp(`${scoreName}[^\\d]*(\\d+)`, 'i'); const match = content.match(regex); return match ? parseInt(match[1]) : null; } extractSentiment(content) { // Extract sentiment score from content const sentimentMatch = content.match(/sentiment[^-\d]*(-?\d*\.?\d+)/i); return sentimentMatch ? parseFloat(sentimentMatch[1]) : 0; } extractSatisfaction(content) { // Extract satisfaction score from content const satisfactionMatch = content.match(/satisfaction[^\d]*(\d+)/i); return satisfactionMatch ? parseInt(satisfactionMatch[1]) : 5; } extractEscalationRisk(content) { // Extract escalation risk from content const riskMatch = content.match(/escalation[^0-9.]*([0-9.]+)/i); return riskMatch ? parseFloat(riskMatch[1]) : 0.3; } determineFeedbackType(performance) { if (performance.empathy_score > 85) return 'praise'; if (performance.empathy_score < 60) return 'warning'; return 'info'; } categorizeFeedback(performance) { if (performance.empathy_score < 70) return 'empathy'; if (performance.accuracy_score < 70) return 'accuracy'; if (performance.professionalism_score < 70) return 'professionalism'; return 'general'; } generateId() { return `ai_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`; } // Additional methods for contextual AI features async getCustomerProfile(customerId) { try { const profile = await this.db.collection('customers') .findOne({ _id: this.db.objectId(customerId) }); return profile || { tier: 'standard', communication_preference: 'direct', satisfaction_history: 7.5, issue_history: [] }; } catch (error) { logger.error('Error getting customer profile:', error); return {}; } } async getAgentProfile(agentId) { try { const profile = await this.db.collection('agent_progress') .findOne({ agent_id: agentId }); return profile || { skill_level: 5, specializations: [], performance_history: 75 }; } catch (error) { logger.error('Error getting agent profile:', error); return {}; } } async analyzeConversationContext(contextData) { const { conversation_history, customer_profile, current_message, platform } = contextData; return { current_sentiment: this.analyzeSentimentFromHistory(conversation_history), urgency_level: this.determineUrgency(conversation_history, customer_profile), platform: platform, conversation_length: conversation_history.length, issue_category: this.categorizeIssue(conversation_history), customer_effort: this.calculateCustomerEffort(conversation_history) }; } analyzeSentimentFromHistory(history) { // Analyze sentiment from conversation history const recentMessages = history.slice(-3); let sentimentSum = 0; recentMessages.forEach(message => { if (message.sender === 'customer') { sentimentSum += this.calculateMessageSentiment(message.content); } }); return recentMessages.length > 0 ? sentimentSum / recentMessages.length : 0; } calculateMessageSentiment(message) { // Simple sentiment analysis based on keywords const positiveWords = ['thank', 'great', 'good', 'excellent', 'happy', 'satisfied']; const negativeWords = ['bad', 'terrible', 'awful', 'frustrated', 'angry', 'disappointed']; const words = message.toLowerCase().split(' '); let score = 0; words.forEach(word => { if (positiveWords.includes(word)) score += 0.2; if (negativeWords.includes(word)) score -= 0.3; }); return Math.max(-1, Math.min(1, score)); } determineUrgency(history, customerProfile) { if (customerProfile.tier === 'platinum') return 'high'; if (history.length > 10) return 'high'; if (this.analyzeSentimentFromHistory(history) < -0.5) return 'high'; return 'medium'; } categorizeIssue(history) { const allText = history.map(m => m.content).join(' ').toLowerCase(); if (allText.includes('bill') || allText.includes('payment')) return 'billing'; if (allText.includes('technical') || allText.includes('not working')) return 'technical'; if (allText.includes('cancel') || allText.includes('refund')) return 'retention'; return 'general'; } calculateCustomerEffort(history) { // Calculate how much effort the customer has put in const customerMessages = history.filter(m => m.sender === 'customer'); const totalLength = customerMessages.reduce((sum, m) => sum + m.content.length, 0); return Math.min(10, Math.floor(totalLength / 100) + customerMessages.length); } } module.exports = AIService;