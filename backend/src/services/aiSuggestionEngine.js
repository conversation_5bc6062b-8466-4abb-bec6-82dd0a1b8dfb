/** * ============================================= * AI SUGGESTION ENGINE * Real-time AI suggestions during calls and conversations * Contextual problem-solving and adaptive recommendations * ============================================= */ const logger = require('../config/logger'); const User = require('../models/User'); const CustomerProfile = require('../models/CustomerProfile'); class AISuggestionEngine { constructor() { this.suggestionCategories = { technical: { weight: 0.8, keywords: ['réseau', 'connexion', 'wifi', 'signal', 'débit', 'configuration'] }, billing: { weight: 0.7, keywords: ['facture', 'paiement', 'forfait', 'prix', 'coût', 'abonnement'] }, service: { weight: 0.6, keywords: ['service', 'option', 'activation', 'désactivation', 'modification'] }, support: { weight: 0.9, keywords: ['problème', 'aide', 'assistance', 'dépannage', 'réparation'] } }; this.commonSolutions = { network_issues: [ { title: "Redémarrage du téléphone", description: "Éteindre et rallumer le téléphone pour réinitialiser la connexion réseau", steps: [ "Maintenez le bouton d'alimentation enfoncé", "Sélectionnez 'Éteindre'", "Attendez 30 secondes", "Rallumez le téléphone" ], effectiveness: 0.7, timeEstimate: "2 minutes" }, { title: "Vérification des paramètres APN", description: "Configurer correctement les paramètres d'accès au réseau mobile", steps: [ "Allez dans Paramètres > Réseau mobile", "Sélectionnez 'Noms des points d'accès'", "Vérifiez que l'APN Free est configuré", "Redémarrez si nécessaire" ], effectiveness: 0.8, timeEstimate: "5 minutes" } ], billing_issues: [ { title: "Consultation de facture en ligne", description: "Accéder à votre espace client pour consulter vos factures", steps: [ "Connectez-vous à votre espace client Free", "Allez dans la section 'Mes factures'", "Téléchargez la facture concernée", "Vérifiez les détails de facturation" ], effectiveness: 0.9, timeEstimate: "3 minutes" } ], service_activation: [ { title: "Activation d'option via l'espace client", description: "Activer ou désactiver des options depuis votre compte", steps: [ "Connectez-vous à votre espace client", "Allez dans 'Gérer mon compte'", "Sélectionnez 'Options et services'", "Activez/désactivez l'option souhaitée" ], effectiveness: 0.85, timeEstimate: "4 minutes" } ] }; } /** * Generate live suggestions during a call or conversation */ async provideLiveSuggestions(callContext, userInput, conversationHistory = []) { try { logger.info(` Generating live suggestions`, { callId: callContext.callId, userId: callContext.userId, inputLength: userInput?.length || 0 }); // Analyze user input and context const analysis = await this.analyzeUserInput(userInput, conversationHistory); const userProfile = await this.getUserProfile(callContext.userId); // Generate contextual suggestions const suggestions = await this.generateContextualSuggestions( analysis, userProfile, callContext ); // Rank and filter suggestions const rankedSuggestions = this.rankSuggestions(suggestions, analysis, userProfile); // Adapt suggestions based on user preferences and history const adaptedSuggestions = await this.adaptSuggestionsToUser( rankedSuggestions, userProfile, conversationHistory ); logger.info(`[COMPLETE] Generated ${adaptedSuggestions.length} live suggestions`, { callId: callContext.callId, userId: callContext.userId, topSuggestionType: adaptedSuggestions[0]?.type }); return { suggestions: adaptedSuggestions.slice(0, 5), // Top 5 suggestions analysis, confidence: this.calculateOverallConfidence(adaptedSuggestions), generatedAt: new Date(), context: { callId: callContext.callId, userId: callContext.userId, sessionId: callContext.sessionId } }; } catch (error) { logger.error('Failed to provide live suggestions:', error); return { suggestions: [], error: error.message, generatedAt: new Date() }; } } /** * Analyze user input to understand intent and context */ async analyzeUserInput(userInput, conversationHistory) { const text = userInput?.toLowerCase() || ''; // Detect issue category const detectedCategory = this.detectIssueCategory(text); // Extract key entities (phone numbers, dates, amounts, etc.) const entities = this.extractEntities(text); // Analyze sentiment and urgency const sentiment = this.analyzeSentiment(text); const urgency = this.analyzeUrgency(text, conversationHistory); // Detect specific problem patterns const problemPatterns = this.detectProblemPatterns(text); return { category: detectedCategory, entities, sentiment, urgency, problemPatterns, keywords: this.extractKeywords(text), intent: this.detectIntent(text), complexity: this.assessComplexity(text, conversationHistory) }; } /** * Generate contextual suggestions based on analysis */ async generateContextualSuggestions(analysis, userProfile, callContext) { const suggestions = []; // Get category-specific solutions const categorySolutions = this.getCategorySolutions(analysis.category); suggestions.push(...categorySolutions); // Generate personalized suggestions based on user profile const personalizedSuggestions = this.generatePersonalizedSuggestions( userProfile, analysis ); suggestions.push(...personalizedSuggestions); // Generate problem-specific suggestions const problemSuggestions = this.generateProblemSpecificSuggestions( analysis.problemPatterns, analysis.entities ); suggestions.push(...problemSuggestions); // Generate escalation suggestions if needed if (analysis.urgency.level === 'high' || analysis.complexity.level === 'high') { const escalationSuggestions = this.generateEscalationSuggestions(analysis); suggestions.push(...escalationSuggestions); } return suggestions; } /** * Detect issue category from user input */ detectIssueCategory(text) { let bestMatch = { category: 'general', score: 0 }; Object.entries(this.suggestionCategories).forEach(([category, config]) => { const score = config.keywords.reduce((acc, keyword) => { return acc + (text.includes(keyword) ? config.weight : 0); }, 0); if (score > bestMatch.score) { bestMatch = { category, score }; } }); return bestMatch; } /** * Extract entities from text (phone numbers, amounts, dates, etc.) */ extractEntities(text) { const entities = {}; // Phone numbers const phoneRegex = /(?:\+33|0)[1-9](?:[0-9]{8})/g; const phones = text.match(phoneRegex); if (phones) entities.phoneNumbers = phones; // Amounts (euros) const amountRegex = /(\d+(?:[.,]\d{2})?)\s*(?:€|euros?)/gi; const amounts = text.match(amountRegex); if (amounts) entities.amounts = amounts; // Dates const dateRegex = /(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{2,4})/g; const dates = text.match(dateRegex); if (dates) entities.dates = dates; // Error codes const errorCodeRegex = /(?:erreur|error|code)\s*:?\s*(\w+\d+|\d+)/gi; const errorCodes = text.match(errorCodeRegex); if (errorCodes) entities.errorCodes = errorCodes; return entities; } /** * Analyze sentiment from text */ analyzeSentiment(text) { const positiveWords = ['bien', 'bon', 'parfait', 'excellent', 'merci', 'content']; const negativeWords = ['problème', 'bug', 'erreur', 'frustré', 'énervé', 'marre']; const positiveCount = positiveWords.reduce((count, word) => count + (text.includes(word) ? 1 : 0), 0); const negativeCount = negativeWords.reduce((count, word) => count + (text.includes(word) ? 1 : 0), 0); let sentiment = 'neutral'; let score = 0; if (positiveCount > negativeCount) { sentiment = 'positive'; score = positiveCount / (positiveCount + negativeCount); } else if (negativeCount > positiveCount) { sentiment = 'negative'; score = negativeCount / (positiveCount + negativeCount); } return { sentiment, score, positiveCount, negativeCount }; } /** * Analyze urgency level */ analyzeUrgency(text, conversationHistory) { const urgencyKeywords = { high: ['urgent', 'immédiatement', 'tout de suite', 'rapidement', 'maintenant'], medium: ['bientôt', 'assez vite', 'dans la journée'], low: ['quand possible', 'pas pressé', 'plus tard'] }; let urgencyLevel = 'low'; let urgencyScore = 0; Object.entries(urgencyKeywords).forEach(([level, keywords]) => { const matches = keywords.reduce((count, keyword) => count + (text.includes(keyword) ? 1 : 0), 0); if (matches > 0) { urgencyLevel = level; urgencyScore = matches / keywords.length; } }); // Consider conversation length as urgency indicator if (conversationHistory.length > 10) { urgencyLevel = urgencyLevel === 'low' ? 'medium' : 'high'; urgencyScore += 0.2; } return { level: urgencyLevel, score: urgencyScore }; } /** * Get solutions for specific category */ getCategorySolutions(categoryMatch) { const { category } = categoryMatch; const solutions = []; switch (category) { case 'technical': solutions.push(...this.commonSolutions.network_issues); break; case 'billing': solutions.push(...this.commonSolutions.billing_issues); break; case 'service': solutions.push(...this.commonSolutions.service_activation); break; default: // General solutions solutions.push({ title: "Consultation de l'aide en ligne", description: "Accéder à la documentation et FAQ Free Mobile", steps: [ "Visitez le site Free Mobile", "Consultez la section 'Aide'", "Recherchez votre problème spécifique", "Suivez les instructions détaillées" ], effectiveness: 0.6, timeEstimate: "5 minutes" }); } return solutions.map(solution => ({ ...solution, type: 'solution', category, confidence: solution.effectiveness })); } /** * Generate personalized suggestions based on user profile */ generatePersonalizedSuggestions(userProfile, analysis) { const suggestions = []; if (userProfile?.subscription) { const { planType, dataUsed, dataLimit } = userProfile.subscription; // Data usage suggestions if (dataUsed / dataLimit > 0.8) { suggestions.push({ type: 'alert', title: "Attention à votre consommation data", description: `Vous avez utilisé ${Math.round((dataUsed / dataLimit) * 100)}% de votre forfait data`, action: "Consultez vos options pour éviter le hors-forfait", confidence: 0.9, category: 'billing' }); } // Plan-specific suggestions if (planType === '4G' && analysis.keywords.includes('lent')) { suggestions.push({ type: 'upgrade', title: "Améliorer votre débit avec la 5G", description: "Votre forfait 4G pourrait être la cause de lenteur", action: "Découvrez nos offres 5G pour un débit plus rapide", confidence: 0.7, category: 'service' }); } } return suggestions; } /** * Rank suggestions by relevance and effectiveness */ rankSuggestions(suggestions, analysis, userProfile) { return suggestions .map(suggestion => ({ ...suggestion, relevanceScore: this.calculateRelevanceScore(suggestion, analysis, userProfile) })) .sort((a, b) => b.relevanceScore - a.relevanceScore); } /** * Calculate relevance score for a suggestion */ calculateRelevanceScore(suggestion, analysis, userProfile) { let score = suggestion.confidence || 0.5; // Category match bonus if (suggestion.category === analysis.category.category) { score += 0.3; } // Urgency match bonus if (analysis.urgency.level === 'high' && suggestion.type === 'escalation') { score += 0.4; } // User profile match bonus if (userProfile?.preferences?.interactionMode === 'guided' && suggestion.steps) { score += 0.2; } // Effectiveness bonus if (suggestion.effectiveness) { score += suggestion.effectiveness * 0.3; } return Math.min(score, 1); } /** * Adapt suggestions to user preferences and history */ async adaptSuggestionsToUser(suggestions, userProfile, conversationHistory) { return suggestions.map(suggestion => { const adapted = { ...suggestion }; // Adapt language complexity based on user's technical proficiency if (userProfile?.technicalProficiency === 'low') { adapted.description = this.simplifyLanguage(adapted.description); if (adapted.steps) { adapted.steps = adapted.steps.map(step => this.simplifyLanguage(step)); } } // Add personalized context if (userProfile?.profile?.firstName) { adapted.personalizedMessage = `${userProfile.profile.firstName}, ${adapted.description}`; } return adapted; }); } /** * Helper methods */ detectProblemPatterns(text) { const patterns = []; if (text.includes('ne fonctionne pas') || text.includes('en panne')) { patterns.push('service_outage'); } if (text.includes('lent') || text.includes('débit')) { patterns.push('slow_connection'); } if (text.includes('facture') && text.includes('erreur')) { patterns.push('billing_error'); } return patterns; } extractKeywords(text) { const stopWords = ['le', 'la', 'les', 'un', 'une', 'des', 'et', 'ou', 'mais', 'donc']; return text .split(/\s+/) .filter(word => word.length > 3 && !stopWords.includes(word)) .slice(0, 10); } detectIntent(text) { if (text.includes('comment') || text.includes('aide')) return 'help_request'; if (text.includes('problème') || text.includes('bug')) return 'problem_report'; if (text.includes('facture') || text.includes('paiement')) return 'billing_inquiry'; return 'general_inquiry'; } assessComplexity(text, conversationHistory) { const technicalTerms = ['ip', 'dns', 'apn', 'routeur', 'configuration']; const technicalCount = technicalTerms.reduce((count, term) => count + (text.includes(term) ? 1 : 0), 0); const level = technicalCount > 2 ? 'high' : technicalCount > 0 ? 'medium' : 'low'; return { level, technicalTermCount: technicalCount }; } async getUserProfile(userId) { try { const user = await User.findById(userId); const customerProfile = await CustomerProfile.findOne({ userId }); return { ...user?.toObject(), subscription: customerProfile?.subscription, technicalProficiency: this.assessUserTechnicalProficiency(user) }; } catch (error) { logger.error('Failed to get user profile:', error); return null; } } assessUserTechnicalProficiency(user) { // Simple heuristic based on user data if (user?.profile?.occupation?.includes('informatique')) return 'high'; if (user?.preferences?.interactionMode === 'guided') return 'low'; return 'medium'; } simplifyLanguage(text) { return text .replace(/configuration/g, 'réglages') .replace(/paramètres/g, 'réglages') .replace(/initialiser/g, 'remettre à zéro') .replace(/redémarrer/g, 'éteindre et rallumer'); } calculateOverallConfidence(suggestions) { if (suggestions.length === 0) return 0; const avgConfidence = suggestions.reduce((sum, s) => sum + (s.confidence || 0), 0) / suggestions.length; return Math.round(avgConfidence * 100) / 100; } generateEscalationSuggestions(analysis) { return [{ type: 'escalation', title: "Transfert vers un spécialiste", description: "Ce problème nécessite l'intervention d'un expert technique", action: "Transférer vers un conseiller spécialisé", confidence: 0.8, category: 'support', urgency: analysis.urgency.level }]; } generateProblemSpecificSuggestions(patterns, entities) { const suggestions = []; patterns.forEach(pattern => { switch (pattern) { case 'service_outage': suggestions.push({ type: 'diagnostic', title: "Vérification de l'état du réseau", description: "Vérifier s'il y a une panne dans votre zone", confidence: 0.8 }); break; case 'slow_connection': suggestions.push({ type: 'optimization', title: "Optimisation de la connexion", description: "Améliorer la vitesse de votre connexion mobile", confidence: 0.7 }); break; } }); return suggestions; } } module.exports = AISuggestionEngine;