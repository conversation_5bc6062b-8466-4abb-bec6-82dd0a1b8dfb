/** * ============================================= * EMERGENCY CALL SERVICE * Core business logic for emergency call handling * Integrates with WebRTC, queue management, and AI services * ============================================= */ const logger = require('../config/logger'); const EmergencyCall = require('../models/EmergencyCall'); const User = require('../models/User'); const ConversationAnalysisService = require('./conversationAnalysisService'); const AISuggestionEngine = require('./aiSuggestionEngine'); const SmartEscalationService = require('./smartEscalationService'); const { v4: uuidv4 } = require('uuid'); class EmergencyCallService { constructor() { this.emergencyHotline = '9198'; this.humanSupportLine = '0745303145'; this.maxConcurrentEmergencyCalls = 50; this.queueTimeoutThreshold = 120000; // 2 minutes this.activeEmergencyCalls = new Map(); this.emergencyQueue = new Map(); // Initialize AI services this.conversationAnalysis = new ConversationAnalysisService(); this.aiSuggestionEngine = new AISuggestionEngine(); this.smartEscalation = new SmartEscalationService(); } /** * Initialize emergency call service */ async initialize(websocketService, callService) { try { this.websocketService = websocketService; this.callService = callService; // Set up periodic queue processing this.startQueueProcessor(); // Set up emergency call monitoring this.startEmergencyCallMonitoring(); logger.info('[COMPLETE] Emergency Call Service initialized successfully'); } catch (error) { logger.error('[FAILED] Emergency Call Service initialization failed:', error); throw error; } } /** * Process emergency call initiation with AI assessment */ async processEmergencyCallInitiation(emergencyCallData) { try { const { emergencyCallId, userId, urgencyLevel, description, conversationHistory } = emergencyCallData; // Perform AI-powered urgency assessment const aiAssessment = await this.performAIUrgencyAssessment({ urgencyLevel, description, conversationHistory }); // Determine routing strategy const routingStrategy = this.determineRoutingStrategy(aiAssessment); // Update emergency call record const emergencyCall = await EmergencyCall.findOne({ emergencyCallId }); if (emergencyCall) { emergencyCall.routing.currentRoute = routingStrategy.route; emergencyCall.aiAssessment = aiAssessment; await emergencyCall.save(); } // Execute routing strategy const routingResult = await this.executeRoutingStrategy(emergencyCallId, routingStrategy); logger.info(` Emergency call processed`, { emergencyCallId, aiAssessment, routingStrategy, routingResult }); return { emergencyCallId, aiAssessment, routingStrategy, routingResult }; } catch (error) { logger.error('Failed to process emergency call initiation:', error); throw error; } } /** * Perform AI-powered urgency assessment */ async performAIUrgencyAssessment(data) { try { const { urgencyLevel, description, conversationHistory } = data; // Calculate base urgency score let urgencyScore = this.calculateBaseUrgencyScore(urgencyLevel); // Analyze description for emergency indicators if (description) { const descriptionScore = this.analyzeDescriptionUrgency(description); urgencyScore += descriptionScore; } // Analyze conversation history for frustration/escalation patterns if (conversationHistory && conversationHistory.length > 0) { const conversationScore = this.analyzeConversationUrgency(conversationHistory); urgencyScore += conversationScore; } // Determine AI confidence level const confidence = this.calculateAssessmentConfidence(urgencyScore, data); // Generate recommendations const recommendations = this.generateAIRecommendations(urgencyScore, confidence); return { urgencyScore: Math.min(Math.max(urgencyScore, 1), 10), confidence, recommendations, assessmentTimestamp: new Date(), factors: { baseUrgency: this.calculateBaseUrgencyScore(urgencyLevel), descriptionAnalysis: description ? this.analyzeDescriptionUrgency(description) : 0, conversationAnalysis: conversationHistory ? this.analyzeConversationUrgency(conversationHistory) : 0 } }; } catch (error) { logger.error('Failed to perform AI urgency assessment:', error); // Return default assessment on error return { urgencyScore: 7, // Default to high urgency confidence: 0.5, recommendations: ['escalate_to_human'], assessmentTimestamp: new Date(), error: 'AI assessment failed, using default values' }; } } /** * Calculate base urgency score from user-provided level */ calculateBaseUrgencyScore(urgencyLevel) { const urgencyScores = { 'low': 2, 'normal': 4, 'medium': 5, 'high': 7, 'urgent': 8, 'critical': 10 }; return urgencyScores[urgencyLevel] || 5; } /** * Analyze description text for urgency indicators */ analyzeDescriptionUrgency(description) { const emergencyKeywords = { critical: ['panne totale', 'plus de réseau', 'urgence absolue', 'critique'], high: ['panne', 'coupure', 'ne fonctionne plus', 'bloqué', 'urgent'], medium: ['problème', 'dysfonctionnement', 'lent', 'intermittent'], billing: ['facture incorrecte', 'surfacturation', 'prélèvement', 'remboursement'], security: ['piratage', 'fraude', 'compte compromis', 'sécurité'] }; let score = 0; const descriptionLower = description.toLowerCase(); // Check for critical keywords if (emergencyKeywords.critical.some(keyword => descriptionLower.includes(keyword))) { score += 3; } // Check for high priority keywords if (emergencyKeywords.high.some(keyword => descriptionLower.includes(keyword))) { score += 2; } // Check for medium priority keywords if (emergencyKeywords.medium.some(keyword => descriptionLower.includes(keyword))) { score += 1; } // Check for billing issues (often urgent for customers) if (emergencyKeywords.billing.some(keyword => descriptionLower.includes(keyword))) { score += 1.5; } // Check for security issues (always high priority) if (emergencyKeywords.security.some(keyword => descriptionLower.includes(keyword))) { score += 2.5; } return Math.min(score, 3); // Cap at 3 points } /** * Analyze conversation history for urgency patterns */ analyzeConversationUrgency(conversationHistory) { let score = 0; const recentMessages = conversationHistory.slice(-10); // Last 10 messages const frustrationIndicators = [ 'pas de réponse', 'ça ne marche pas', 'toujours pas résolu', 'depuis des heures', 'inacceptable', 'je veux parler à quelqu\'un', 'agent humain', 'superviseur', 'réclamation' ]; const escalationIndicators = [ 'deuxième fois', 'troisième fois', 'déjà appelé', 'toujours le même problème', 'aucune solution' ]; // Check for frustration patterns recentMessages.forEach(message => { if (message.content) { const contentLower = message.content.toLowerCase(); frustrationIndicators.forEach(indicator => { if (contentLower.includes(indicator)) { score += 0.3; } }); escalationIndicators.forEach(indicator => { if (contentLower.includes(indicator)) { score += 0.5; } }); } }); // Check conversation length (longer conversations may indicate complexity) if (conversationHistory.length > 20) { score += 0.5; } // Check for repeated bot responses (indicates bot failure) const botResponses = recentMessages.filter(msg => msg.sender === 'bot'); if (botResponses.length > 5) { score += 1; } return Math.min(score, 2); // Cap at 2 points } /** * Calculate assessment confidence level */ calculateAssessmentConfidence(urgencyScore, data) { let confidence = 0.7; // Base confidence // Higher confidence with more data if (data.description && data.description.length > 50) { confidence += 0.1; } if (data.conversationHistory && data.conversationHistory.length > 5) { confidence += 0.1; } // Lower confidence for edge cases if (urgencyScore <= 2 || urgencyScore >= 9) { confidence -= 0.1; } return Math.min(Math.max(confidence, 0.3), 0.95); } /** * Generate AI recommendations based on assessment */ generateAIRecommendendations(urgencyScore, confidence) { const recommendations = []; if (urgencyScore >= 9) { recommendations.push('immediate_human_transfer'); recommendations.push('notify_supervisor'); } else if (urgencyScore >= 7) { recommendations.push('priority_queue'); recommendations.push('enhanced_ai_assistance'); } else if (urgencyScore >= 5) { recommendations.push('enhanced_ai_assistance'); recommendations.push('prepare_human_fallback'); } else { recommendations.push('standard_ai_assistance'); recommendations.push('monitor_satisfaction'); } if (confidence < 0.6) { recommendations.push('request_additional_info'); } return recommendations; } /** * Determine routing strategy based on AI assessment */ determineRoutingStrategy(aiAssessment) { const { urgencyScore, confidence, recommendations } = aiAssessment; if (recommendations.includes('immediate_human_transfer')) { return { route: 'immediate_human_transfer', priority: 'critical', estimatedWaitTime: 5000, requiresHumanAgent: true, targetNumber: this.humanSupportLine }; } if (recommendations.includes('priority_queue')) { return { route: 'priority_queue', priority: 'high', estimatedWaitTime: 30000, requiresHumanAgent: true, targetNumber: this.humanSupportLine }; } if (recommendations.includes('enhanced_ai_assistance')) { return { route: 'enhanced_ai_assistance', priority: 'medium', estimatedWaitTime: 15000, requiresHumanAgent: false, fallbackToHuman: true }; } return { route: 'standard_ai_assistance', priority: 'normal', estimatedWaitTime: 60000, requiresHumanAgent: false, fallbackToHuman: true }; } /** * Execute routing strategy */ async executeRoutingStrategy(emergencyCallId, routingStrategy) { try { const { route, requiresHumanAgent, targetNumber } = routingStrategy; switch (route) { case 'immediate_human_transfer': return await this.initiateImmediateHumanTransfer(emergencyCallId, targetNumber); case 'priority_queue': return await this.addToPriorityQueue(emergencyCallId, 'high'); case 'enhanced_ai_assistance': return await this.initiateEnhancedAIAssistance(emergencyCallId); case 'standard_ai_assistance': default: return await this.initiateStandardAIAssistance(emergencyCallId); } } catch (error) { logger.error('Failed to execute routing strategy:', error); // Fallback to human transfer on error return await this.initiateImmediateHumanTransfer(emergencyCallId, this.humanSupportLine); } } /** * Start queue processor for emergency calls */ startQueueProcessor() { setInterval(async () => { try { await this.processEmergencyQueue(); } catch (error) { logger.error('Emergency queue processing error:', error); } }, 10000); // Process every 10 seconds } /** * Start emergency call monitoring */ startEmergencyCallMonitoring() { setInterval(async () => { try { await this.monitorEmergencyCallHealth(); } catch (error) { logger.error('Emergency call monitoring error:', error); } }, 30000); // Monitor every 30 seconds } } /** * Initiate immediate human transfer */ async initiateImmediateHumanTransfer(emergencyCallId, targetNumber) { try { const emergencyCall = await EmergencyCall.findOne({ emergencyCallId }); if (!emergencyCall) { throw new Error('Emergency call not found'); } // Find available agent const availableAgent = await this.findAvailableEmergencyAgent(); if (availableAgent) { // Initiate WebRTC call directly const webrtcSession = await this.callService.initiateEmergencyCall({ emergencyCallId, customerId: emergencyCall.userId, agentId: availableAgent._id, priority: 'emergency' }); // Update emergency call emergencyCall.status = 'connected_to_agent'; emergencyCall.agentInfo = { agentId: availableAgent._id, agentName: `${availableAgent.profile.firstName} ${availableAgent.profile.lastName}`, connectedAt: new Date() }; emergencyCall.webrtcSessionId = webrtcSession.sessionId; await emergencyCall.save(); // Notify both parties this.websocketService.emitToUser(emergencyCall.userId, 'emergency-call-connected', { emergencyCallId, agentInfo: emergencyCall.agentInfo, webrtcSessionId: webrtcSession.sessionId }); this.websocketService.emitToUser(availableAgent._id, 'emergency-call-assigned', { emergencyCallId, customerInfo: { userId: emergencyCall.userId, urgencyLevel: emergencyCall.urgencyLevel, description: emergencyCall.description }, webrtcSessionId: webrtcSession.sessionId }); return { success: true, status: 'connected_to_agent', agentInfo: emergencyCall.agentInfo, webrtcSessionId: webrtcSession.sessionId }; } else { // No agents available, add to priority queue return await this.addToPriorityQueue(emergencyCallId, 'critical'); } } catch (error) { logger.error('Failed to initiate immediate human transfer:', error); throw error; } } /** * Add emergency call to priority queue */ async addToPriorityQueue(emergencyCallId, priority = 'high') { try { const emergencyCall = await EmergencyCall.findOne({ emergencyCallId }); if (!emergencyCall) { throw new Error('Emergency call not found'); } // Calculate queue position const queuePosition = await this.calculateQueuePosition(priority); const estimatedWaitTime = queuePosition * 45000; // 45 seconds per position // Update emergency call emergencyCall.status = 'in_queue'; emergencyCall.queueInfo = { position: queuePosition, estimatedWaitTime, addedAt: new Date(), priority }; await emergencyCall.save(); // Add to internal queue this.emergencyQueue.set(emergencyCallId, { emergencyCallId, priority, addedAt: new Date(), estimatedWaitTime }); // Notify user this.websocketService.emitToUser(emergencyCall.userId, 'emergency-call-queued', { emergencyCallId, queuePosition, estimatedWaitTime, priority }); // Notify available agents this.websocketService.emitToRole('agent', 'emergency-call-in-queue', { emergencyCallId, priority, queuePosition, urgencyLevel: emergencyCall.urgencyLevel }); return { success: true, status: 'in_queue', queuePosition, estimatedWaitTime, priority }; } catch (error) { logger.error('Failed to add to priority queue:', error); throw error; } } /** * Initiate enhanced AI assistance */ async initiateEnhancedAIAssistance(emergencyCallId) { try { const emergencyCall = await EmergencyCall.findOne({ emergencyCallId }); if (!emergencyCall) { throw new Error('Emergency call not found'); } // Update status emergencyCall.status = 'ai_assistance'; emergencyCall.routing.currentRoute = 'enhanced_ai_assistance'; await emergencyCall.save(); // Notify user about enhanced AI assistance this.websocketService.emitToUser(emergencyCall.userId, 'emergency-ai-assistance-started', { emergencyCallId, message: 'Assistant IA avancé activé pour votre urgence', capabilities: [ 'Analyse contextuelle avancée', 'Résolution guidée étape par étape', 'Escalade automatique si nécessaire' ] }); return { success: true, status: 'ai_assistance', assistanceType: 'enhanced', fallbackAvailable: true }; } catch (error) { logger.error('Failed to initiate enhanced AI assistance:', error); throw error; } } /** * Initiate standard AI assistance */ async initiateStandardAIAssistance(emergencyCallId) { try { const emergencyCall = await EmergencyCall.findOne({ emergencyCallId }); if (!emergencyCall) { throw new Error('Emergency call not found'); } // Update status emergencyCall.status = 'ai_assistance'; emergencyCall.routing.currentRoute = 'standard_ai_assistance'; await emergencyCall.save(); // Notify user this.websocketService.emitToUser(emergencyCall.userId, 'emergency-ai-assistance-started', { emergencyCallId, message: 'Assistant IA activé pour votre demande', escalationAvailable: true }); return { success: true, status: 'ai_assistance', assistanceType: 'standard', escalationAvailable: true }; } catch (error) { logger.error('Failed to initiate standard AI assistance:', error); throw error; } } /** * Find available emergency agent */ async findAvailableEmergencyAgent() { try { const availableAgents = await User.find({ role: 'agent', 'profile.status': 'available', 'profile.skills': { $in: ['emergency_support', 'priority_support'] } }).limit(1); return availableAgents.length > 0 ? availableAgents[0] : null; } catch (error) { logger.error('Failed to find available emergency agent:', error); return null; } } /** * Calculate queue position based on priority */ async calculateQueuePosition(priority) { try { const priorityWeights = { 'critical': 1, 'high': 2, 'medium': 3, 'normal': 4 }; const currentQueueSize = await EmergencyCall.countDocuments({ status: 'in_queue', 'queueInfo.priority': { $in: ['critical', 'high', 'medium', 'normal'] } }); // Calculate position based on priority const higherPriorityCalls = await EmergencyCall.countDocuments({ status: 'in_queue', 'queueInfo.priority': priority === 'critical' ? 'critical' : priority === 'high' ? { $in: ['critical', 'high'] } : priority === 'medium' ? { $in: ['critical', 'high', 'medium'] } : { $in: ['critical', 'high', 'medium', 'normal'] } }); return higherPriorityCalls + 1; } catch (error) { logger.error('Failed to calculate queue position:', error); return 1; // Default to first position } } /** * Process emergency queue */ async processEmergencyQueue() { try { // Get queued emergency calls const queuedCalls = await EmergencyCall.find({ status: 'in_queue' }).sort({ 'queueInfo.priority': 1, 'queueInfo.addedAt': 1 }); for (const call of queuedCalls) { // Check if call has been waiting too long const waitTime = Date.now() - call.queueInfo.addedAt.getTime(); if (waitTime > this.queueTimeoutThreshold) { // Escalate due to timeout await this.escalateDueToTimeout(call.emergencyCallId); continue; } // Try to assign to available agent const availableAgent = await this.findAvailableEmergencyAgent(); if (availableAgent) { await this.assignEmergencyCallToAgent(call.emergencyCallId, availableAgent._id); } } } catch (error) { logger.error('Failed to process emergency queue:', error); } } /** * Monitor emergency call health */ async monitorEmergencyCallHealth() { try { const activeCalls = await EmergencyCall.find({ status: { $in: ['initiated', 'in_progress', 'connected_to_agent'] } }); for (const call of activeCalls) { const callDuration = Date.now() - call.createdAt.getTime(); // Check for calls that have been active too long if (callDuration > 1800000) { // 30 minutes logger.warn(`Long-running emergency call detected`, { emergencyCallId: call.emergencyCallId, duration: callDuration, status: call.status }); // Notify supervisors this.websocketService.emitToRole('admin', 'long-emergency-call-alert', { emergencyCallId: call.emergencyCallId, duration: callDuration, status: call.status }); } } } catch (error) { logger.error('Failed to monitor emergency call health:', error); } } /** * AI-powered call initiation based on conversation analysis */ async initiateAICall(userId, conversationId, conversationHistory = []) { try { logger.info(`[AI] Initiating AI-powered call`, { userId, conversationId, historyLength: conversationHistory.length }); // Analyze conversation to determine if call is needed const callNeedAnalysis = await this.conversationAnalysis.analyzeConversationForCallNeed( conversationId, userId ); if (!callNeedAnalysis.needsCall) { logger.info(`AI determined call not needed`, { userId, conversationId, confidence: callNeedAnalysis.confidence }); return { success: false, reason: 'call_not_needed', recommendation: callNeedAnalysis.recommendation, confidence: callNeedAnalysis.confidence }; } // Generate AI suggestions for the call const aiSuggestions = await this.aiSuggestionEngine.provideLiveSuggestions( { callId: `ai_call_${Date.now()}`, userId }, conversationHistory[conversationHistory.length - 1]?.content || '', conversationHistory ); // Create AI call session const aiCallSession = { callId: `ai_call_${Date.now()}`, userId, conversationId, type: 'ai_initiated', status: 'ai_assistance', urgencyLevel: this.determineUrgencyFromAnalysis(callNeedAnalysis), description: this.generateCallDescription(callNeedAnalysis, conversationHistory), conversationHistory, aiAnalysis: callNeedAnalysis, aiSuggestions: aiSuggestions.suggestions, createdAt: new Date() }; // Store AI call session this.activeEmergencyCalls.set(aiCallSession.callId, aiCallSession); // Provide AI assistance const aiAssistanceResult = await this.provideAIAssistance( aiCallSession, aiSuggestions ); logger.info(`[COMPLETE] AI call initiated successfully`, { callId: aiCallSession.callId, userId, urgencyLevel: aiCallSession.urgencyLevel, suggestionsCount: aiSuggestions.suggestions.length }); return { success: true, callSession: aiCallSession, aiAssistance: aiAssistanceResult, recommendations: callNeedAnalysis.recommendation }; } catch (error) { logger.error('Failed to initiate AI call:', error); throw error; } } /** * Provide AI assistance during call */ async provideAIAssistance(callSession, aiSuggestions) { try { logger.info(` Providing AI assistance`, { callId: callSession.callId, suggestionsCount: aiSuggestions.suggestions.length }); // Select top suggestions based on context const topSuggestions = aiSuggestions.suggestions.slice(0, 3); // Generate personalized assistance message const assistanceMessage = this.generateAIAssistanceMessage( callSession, topSuggestions ); // Track AI assistance attempt callSession.aiAttempts = (callSession.aiAttempts || 0) + 1; callSession.lastAIAssistance = { timestamp: new Date(), suggestions: topSuggestions, message: assistanceMessage }; // Emit real-time update to user if (this.websocketService) { this.websocketService.emitToUser(callSession.userId, 'ai-assistance-provided', { callId: callSession.callId, message: assistanceMessage, suggestions: topSuggestions, confidence: aiSuggestions.confidence }); } return { message: assistanceMessage, suggestions: topSuggestions, confidence: aiSuggestions.confidence, attemptNumber: callSession.aiAttempts }; } catch (error) { logger.error('Failed to provide AI assistance:', error); throw error; } } /** * Evaluate if escalation to human agent is needed */ async evaluateEscalationNeed(callSession, userFeedback = null) { try { logger.info(`[SEARCH] Evaluating escalation need`, { callId: callSession.callId, aiAttempts: callSession.aiAttempts, userFeedback }); // Use smart escalation service const escalationEvaluation = await this.smartEscalation.evaluateEscalationNeed( callSession, callSession.aiAttempts, callSession.conversationHistory ); if (escalationEvaluation.shouldEscalate) { logger.info(` Escalation recommended`, { callId: callSession.callId, confidence: escalationEvaluation.confidence, reasons: escalationEvaluation.reasons }); // Initiate transfer to human agent const transferResult = await this.smartEscalation.transferToHumanAgent( callSession, escalationEvaluation.reasons[0], escalationEvaluation.agentRequirements ); // Update call session status callSession.status = 'escalated_to_human'; callSession.escalationInfo = { escalatedAt: new Date(), reasons: escalationEvaluation.reasons, confidence: escalationEvaluation.confidence, transferResult }; // Emit escalation notification if (this.websocketService) { this.websocketService.emitToUser(callSession.userId, 'call-escalated', { callId: callSession.callId, agent: transferResult.agent, estimatedResponseTime: transferResult.estimatedResponseTime }); } return { escalated: true, agent: transferResult.agent, briefing: transferResult.briefing, estimatedResponseTime: transferResult.estimatedResponseTime }; } return { escalated: false, continueAI: true, confidence: escalationEvaluation.confidence }; } catch (error) { logger.error('Failed to evaluate escalation need:', error); throw error; } } /** * Helper methods for AI call management */ determineUrgencyFromAnalysis(analysis) { if (analysis.callScore >= 0.8) return 'high'; if (analysis.callScore >= 0.6) return 'medium'; return 'normal'; } generateCallDescription(analysis, conversationHistory) { const lastUserMessage = conversationHistory .filter(msg => msg.sender === 'user') .pop(); const baseDescription = lastUserMessage?.content || 'Problème nécessitant une assistance'; const reasons = analysis.reasons.join(', '); return `${baseDescription} (Raisons: ${reasons})`; } generateAIAssistanceMessage(callSession, suggestions) { const userName = callSession.userProfile?.firstName || 'Client'; let message = `Bonjour ${userName}, je vais vous aider à résoudre votre problème. `; if (suggestions.length > 0) { message += `Voici ${suggestions.length} solution${suggestions.length > 1 ? 's' : ''} que je recommande :\n\n`; suggestions.forEach((suggestion, index) => { message += `${index + 1}. **${suggestion.title}**\n`; message += ` ${suggestion.description}\n`; if (suggestion.timeEstimate) { message += ` ⏱ Temps estimé: ${suggestion.timeEstimate}\n`; } message += '\n'; }); message += 'Souhaitez-vous essayer une de ces solutions, ou préférez-vous que je vous mette en relation avec un conseiller ?'; } else { message += 'Je vais analyser votre situation et vous proposer les meilleures solutions.'; } return message; } } module.exports = new EmergencyCallService();