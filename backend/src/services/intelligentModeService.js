const Message = require('../models/Message'); const Conversation = require('../models/Conversation'); const CustomerProfile = require('../models/CustomerProfile'); const logger = require('../config/logger'); const openaiService = require('./openaiService'); const nlpService = require('./nlpService'); /** * SERVICE DES MODES DE FONCTIONNEMENT INTELLIGENTS * Implémentation des 4 modes selon les spécifications : * 1. Mode Conversationnel Libre * 2. Mode Guidé par Menus * 3. Mode Hybride Intelligent * 4. Mode Proactif */ class IntelligentModeService { constructor() { this.modeStrategies = { conversational: this.handleConversationalMode.bind(this), guided: this.handleGuidedMode.bind(this), hybrid: this.handleHybridMode.bind(this), proactive: this.handleProactiveMode.bind(this) }; } /** * Point d'entrée principal - Détermine le mode approprié */ async processMessage(conversationId, userMessage, userProfile) { try { const conversation = await Conversation.findById(conversationId); const messageHistory = await Message.find({ conversationId }) .sort({ timestamp: -1 }) .limit(10); // Déterminer le mode optimal const optimalMode = await this.determineOptimalMode( userMessage, userProfile, conversation, messageHistory ); logger.info(`Using ${optimalMode} mode for conversation ${conversationId}`); // Exécuter la stratégie du mode choisi const response = await this.modeStrategies[optimalMode]( userMessage, userProfile, conversation, messageHistory ); return { ...response, mode: optimalMode, timestamp: new Date() }; } catch (error) { logger.error('Error in intelligent mode processing:', error); // Fallback vers mode conversationnel simple return this.handleConversationalMode(userMessage, userProfile); } } /** * Détermine le mode optimal selon le contexte */ async determineOptimalMode(userMessage, userProfile, conversation, messageHistory) { // Analyse NLP du message const nlpAnalysis = await nlpService.parseMessage(userMessage, userProfile.userId); // Facteurs de décision const factors = { // Complexité du message messageComplexity: this.analyzeMessageComplexity(userMessage), // Intention claire vs ambiguë intentConfidence: nlpAnalysis.intent?.confidence || 0, // Préférences utilisateur userPreference: userProfile.preferences?.interactionMode || 'auto', // Historique de la conversation conversationContext: this.analyzeConversationContext(messageHistory), // Canal utilisé channel: conversation.channel, // Profil utilisateur (tech-savvy, senior, etc.) userSegment: this.getUserSegment(userProfile) }; // Logique de décision if (factors.userPreference !== 'auto') { return factors.userPreference; } // Mode guidé pour utilisateurs seniors ou canal mobile if (factors.userSegment === 'senior' || factors.channel === 'mobile') { return 'guided'; } // Mode conversationnel pour messages complexes ou intentions floues if (factors.messageComplexity > 0.7 || factors.intentConfidence < 0.5) { return 'conversational'; } // Mode proactif si opportunité détectée if (await this.shouldActivateProactiveMode(userProfile, messageHistory)) { return 'proactive'; } // Mode hybride par défaut return 'hybrid'; } /** * MODE 1: CONVERSATIONNEL LIBRE * Langage naturel et décontracté, gestion des émotions */ async handleConversationalMode(userMessage, userProfile, conversation, messageHistory) { try { // Analyse émotionnelle du message const sentiment = await this.analyzeSentiment(userMessage); // Adaptation du ton selon l'émotion détectée let systemPrompt = this.buildConversationalPrompt(sentiment, userProfile); // Contexte de la conversation const conversationContext = messageHistory .slice(0, 5) .reverse() .map(msg => `${msg.sender}: ${msg.content.text}`) .join('\n'); // Génération de réponse avec OpenAI const response = await openaiService.generateResponse({ previousMessages: conversationContext, userProfile, sentiment, mode: 'conversational' }, userMessage); return { text: response, type: 'conversational', sentiment: sentiment, tone: this.getToneFromSentiment(sentiment), quickReplies: this.generateContextualQuickReplies(userMessage), metadata: { emotionDetected: sentiment.emotion, confidence: sentiment.confidence, adaptedTone: true } }; } catch (error) { logger.error('Error in conversational mode:', error); return { text: "Je comprends, laisse-moi t'aider avec ça. Peux-tu me donner un peu plus de détails ?", type: 'conversational' }; } } /** * [TARGET] MODE 2: GUIDÉ PAR MENUS * Navigation structurée avec boutons et actions */ async handleGuidedMode(userMessage, userProfile, conversation, messageHistory) { try { // Analyser l'intention pour proposer le bon menu const nlpAnalysis = await nlpService.parseMessage(userMessage, userProfile.userId); const intent = nlpAnalysis.intent?.name; // Menus contextuels selon l'intention const menuStructure = this.buildMenuStructure(intent, userProfile); return { text: menuStructure.message, type: 'guided', buttons: menuStructure.buttons, quickActions: menuStructure.quickActions, metadata: { menuLevel: menuStructure.level, intent: intent, navigationPath: menuStructure.path } }; } catch (error) { logger.error('Error in guided mode:', error); return this.getDefaultMenu(); } } /** * MODE 3: HYBRIDE INTELLIGENT * Combinaison adaptative des modes selon le contexte */ async handleHybridMode(userMessage, userProfile, conversation, messageHistory) { try { const nlpAnalysis = await nlpService.parseMessage(userMessage, userProfile.userId); const intentConfidence = nlpAnalysis.intent?.confidence || 0; if (intentConfidence > 0.8) { // Haute confiance : Réponse directe + options const directResponse = await this.generateDirectResponse(nlpAnalysis, userProfile); const relatedActions = this.getRelatedActions(nlpAnalysis.intent.name); return { text: directResponse, type: 'hybrid', buttons: relatedActions, quickReplies: this.generateSmartQuickReplies(nlpAnalysis), metadata: { responseType: 'direct_with_options', intentConfidence: intentConfidence } }; } else { // Faible confiance : Questions clarifiantes + suggestions const clarificationQuestions = this.generateClarificationQuestions(userMessage); const suggestions = this.generateSuggestions(userProfile); return { text: "Je veux m'assurer de bien comprendre ta demande. Peux-tu me dire plus précisément ce que tu cherches ?", type: 'hybrid', quickReplies: clarificationQuestions, suggestions: suggestions, metadata: { responseType: 'clarification_with_suggestions', intentConfidence: intentConfidence } }; } } catch (error) { logger.error('Error in hybrid mode:', error); return this.handleConversationalMode(userMessage, userProfile); } } /** * [DEPLOY] MODE 4: PROACTIF * Anticipation et suggestions avant que l'utilisateur demande */ async handleProactiveMode(userMessage, userProfile, conversation, messageHistory) { try { // Analyser les opportunités proactives const opportunities = await this.detectProactiveOpportunities(userProfile, messageHistory); // Répondre d'abord au message actuel const primaryResponse = await this.generatePrimaryResponse(userMessage, userProfile); // Ajouter les suggestions proactives const proactiveInsights = this.generateProactiveInsights(opportunities, userProfile); return { text: primaryResponse, type: 'proactive', proactiveInsights: proactiveInsights, recommendations: opportunities.recommendations, notifications: opportunities.notifications, metadata: { opportunitiesDetected: opportunities.count, proactiveType: opportunities.type } }; } catch (error) { logger.error('Error in proactive mode:', error); return this.handleHybridMode(userMessage, userProfile, conversation, messageHistory); } } /** * Analyse du sentiment émotionnel */ async analyzeSentiment(message) { // Patterns simples pour détecter les émotions const patterns = { frustration: /frustré|énervé|ras le bol|en colère|agacé|irrité/i, satisfaction: /merci|parfait|génial|excellent|super/i, urgence: /urgent|rapidement|vite|immédiatement|pressé/i, confusion: /comprends pas|confus|perdu|compliqué/i, politesse: /s'il vous plaît|svp|merci|bonjour|bonsoir/i }; let detectedEmotion = 'neutral'; let confidence = 0.5; for (const [emotion, pattern] of Object.entries(patterns)) { if (pattern.test(message)) { detectedEmotion = emotion; confidence = 0.8; break; } } return { emotion: detectedEmotion, confidence: confidence, polarity: this.getEmotionPolarity(detectedEmotion) }; } /** * Construction des menus contextuels */ buildMenuStructure(intent, userProfile) { const baseMenus = { greeting: { message: "Salut ! Comment puis-je t'aider aujourd'hui ?", buttons: [ { text: " Mon Forfait", action: "check_plan" }, { text: "[MOBILE] Ma Facture", action: "check_bill" }, { text: " Assistance", action: "technical_help" }, { text: " Autre Question", action: "other_question" } ], level: 'main', path: ['main'] }, check_balance: { message: "Voici les actions possibles concernant ton forfait :", buttons: [ { text: "[ANALYTICS] Voir ma conso", action: "view_consumption" }, { text: " Ajouter des Go", action: "add_data" }, { text: " Changer d'offre", action: "change_plan" }, { text: "↩ Retour menu", action: "main_menu" } ], level: 'submenu', path: ['main', 'forfait'] }, technical_help: { message: "Quel type de problème rencontres-tu ?", buttons: [ { text: " Réseau/Connexion", action: "network_issue" }, { text: " Email/MMS", action: "email_issue" }, { text: "[CONFIG] Config Téléphone", action: "phone_config" }, { text: " Autre Problème", action: "other_technical" } ], level: 'submenu', path: ['main', 'assistance'] } }; return baseMenus[intent] || baseMenus.greeting; } /** * Détection des opportunités proactives */ async detectProactiveOpportunities(userProfile, messageHistory) { const opportunities = { count: 0, type: 'general', recommendations: [], notifications: [] }; try { // Récupérer le profil client complet const customerProfile = await CustomerProfile.findOne({ userId: userProfile.userId }); if (!customerProfile) { return opportunities; } // Vérifier consommation proche du dépassement if (customerProfile.usage?.data?.percentage > 85) { opportunities.recommendations.push({ type: 'data_limit', title: " Attention au dépassement", message: `Tu as consommé ${customerProfile.usage.data.percentage}% de ton forfait data.`, actions: [ { text: "Ajouter 20Go", action: "add_data_20gb" }, { text: "Voir options", action: "view_data_options" } ] }); opportunities.count++; } // Suggestions d'amélioration de forfait if (customerProfile.usage?.data?.percentage < 30) { opportunities.recommendations.push({ type: 'plan_optimization', title: "[FEATURE] Optimise ton forfait", message: "Tu utilises peu de data. Un forfait moins cher pourrait te convenir.", actions: [ { text: "Voir forfaits", action: "view_smaller_plans" }, { text: "Pas maintenant", action: "dismiss" } ] }); opportunities.count++; } // Rappel de facture const now = new Date(); const billDay = customerProfile.billing?.dueDate || 5; const currentDay = now.getDate(); if (currentDay >= billDay - 3 && currentDay <= billDay) { opportunities.notifications.push({ type: 'bill_reminder', title: " Prélèvement à venir", message: `Ton prélèvement mensuel aura lieu le ${billDay}/${now.getMonth() + 1}.`, priority: 'medium' }); opportunities.count++; } opportunities.type = opportunities.count > 0 ? 'personalized' : 'general'; } catch (error) { logger.error('Error detecting proactive opportunities:', error); } return opportunities; } /** * Génération de réponses intelligentes contextuelles */ generateSmartQuickReplies(nlpAnalysis) { const intentBasedReplies = { check_balance: [ "Voir ma conso", "Ajouter des Go", "Changer de forfait" ], report_issue: [ "Problème réseau", "Problème facturation", "Autre problème" ], change_plan: [ "Voir les forfaits", "Calculer mes besoins", "Parler à un conseiller" ] }; const intent = nlpAnalysis.intent?.name; return intentBasedReplies[intent] || [ "Oui", "Non", "Plus d'infos", "Parler à un agent" ]; } /** * Méthodes utilitaires */ analyzeMessageComplexity(message) { const factors = [ message.length > 100, (message.match(/\?/g) || []).length > 1, /\b(et|ou|mais|donc|car|parce que)\b/i.test(message), message.split(' ').length > 20 ]; return factors.filter(Boolean).length / factors.length; } analyzeConversationContext(messageHistory) { return { messageCount: messageHistory.length, lastUserIntent: messageHistory.find(m => m.sender === 'user')?.intent?.name, averageResponseTime: this.calculateAverageResponseTime(messageHistory), hasEscalationIndicators: messageHistory.some(m => /agent|conseiller|humain/i.test(m.content.text) ) }; } getUserSegment(userProfile) { // Logique de segmentation basée sur le profil if (userProfile.profile?.age > 65) return 'senior'; if (userProfile.profile?.techSavvy) return 'tech'; if (userProfile.role === 'business') return 'business'; return 'standard'; } async shouldActivateProactiveMode(userProfile, messageHistory) { // Conditions pour déclencher le mode proactif const conditions = [ messageHistory.length > 3, // Conversation établie messageHistory.some(m => /aide|besoin|problème/i.test(m.content.text)), await this.hasProactiveOpportunities(userProfile) ]; return conditions.filter(Boolean).length >= 2; } async hasProactiveOpportunities(userProfile) { try { const customerProfile = await CustomerProfile.findOne({ userId: userProfile.userId }); return customerProfile?.usage?.data?.percentage > 85 || customerProfile?.billing?.overdueAmount > 0; } catch { return false; } } buildConversationalPrompt(sentiment, userProfile) { let basePrompt = `Tu es l'assistant virtuel Free Mobile. Tu utilises le tutoiement comme Free Mobile. Sois professionnel, concis et empathique.`; switch (sentiment.emotion) { case 'frustration': basePrompt += ` L'utilisateur semble frustré. Sois particulièrement empathique et propose des solutions concrètes rapidement.`; break; case 'urgence': basePrompt += ` L'utilisateur est pressé. Va droit au but et propose une aide immédiate.`; break; case 'confusion': basePrompt += ` L'utilisateur semble perdu. Explique clairement et propose d'être guidé étape par étape.`; break; } return basePrompt; } getToneFromSentiment(sentiment) { const toneMap = { frustration: 'empathetic', satisfaction: 'positive', urgence: 'efficient', confusion: 'patient', neutral: 'friendly' }; return toneMap[sentiment.emotion] || 'friendly'; } getEmotionPolarity(emotion) { const polarityMap = { frustration: 'negative', satisfaction: 'positive', urgence: 'neutral', confusion: 'neutral', politesse: 'positive' }; return polarityMap[emotion] || 'neutral'; } generateContextualQuickReplies(userMessage) { if (/problème|bug|marche pas/i.test(userMessage)) { return ["Problème réseau", "Problème app", "Autre souci"]; } if (/facture|paiement|prix/i.test(userMessage)) { return ["Voir ma facture", "Modes de paiement", "Contester facture"]; } return ["Oui", "Non", "Plus d'infos"]; } getDefaultMenu() { return { text: "Comment puis-je t'aider ?", type: 'guided', buttons: [ { text: " Mon Forfait", action: "check_plan" }, { text: "[MOBILE] Ma Facture", action: "check_bill" }, { text: " Assistance", action: "technical_help" }, { text: " Parler à un agent", action: "contact_agent" } ] }; } calculateAverageResponseTime(messageHistory) { // Calcul simplifié du temps de réponse moyen return messageHistory.length > 1 ? 2000 : 0; // 2 secondes par défaut } async generateDirectResponse(nlpAnalysis, userProfile) { // Génération de réponse directe basée sur l'intention const responses = { check_balance: "Voici ton solde et ta consommation actuelle :", check_invoice: "Je récupère ta dernière facture :", change_plan: "Regardons les forfaits disponibles pour toi :", report_issue: "Je vais t'aider à résoudre ce problème." }; return responses[nlpAnalysis.intent?.name] || "Je vais t'aider avec ça."; } getRelatedActions(intent) { const actions = { check_balance: [ { text: "Ajouter des Go", action: "add_data" }, { text: "Voir détails", action: "view_details" } ], change_plan: [ { text: "Simuler changement", action: "simulate_change" }, { text: "Voir toutes les offres", action: "view_all_plans" } ] }; return actions[intent] || [ { text: "Plus d'infos", action: "more_info" }, { text: "Autre question", action: "other_question" } ]; } generateClarificationQuestions(userMessage) { return [ "Mon forfait", "Ma facture", "Un problème technique", "Changer d'offre", "Autre chose" ]; } generateSuggestions(userProfile) { return [ "Consulter ma consommation", "Voir mes factures", "Options disponibles", "Contacter un conseiller" ]; } async generatePrimaryResponse(userMessage, userProfile) { // Réponse simple au message principal return "D'accord, je vais t'aider avec ça."; } generateProactiveInsights(opportunities, userProfile) { if (opportunities.count === 0) { return []; } return opportunities.recommendations.map(rec => ({ type: rec.type, title: rec.title, message: rec.message, priority: rec.priority || 'medium', actions: rec.actions || [] })); } } module.exports = new IntelligentModeService();