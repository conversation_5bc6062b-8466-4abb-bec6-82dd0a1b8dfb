const axios = require('axios'); const logger = require('../config/logger'); class NLPService { constructor() { this.rasaUrl = process.env.RASA_URL || 'http://localhost:5005'; } async parseMessage(message, senderId) { try { logger.debug(`Parsing message: "${message}" for sender: ${senderId}`); const response = await axios.post(`${this.rasaUrl}/model/parse`, { text: message, message_id: Date.now().toString(), }); logger.debug(`NLP response:`, response.data); return { intent: response.data.intent, entities: response.data.entities, intentRanking: response.data.intent_ranking, }; } catch (error) { logger.error('NLP parsing error:', error); return { intent: { name: 'unknown', confidence: 0 }, entities: [], error: true, }; } } async getResponse(message, senderId, context = {}) { try { const response = await axios.post(`${this.rasaUrl}/webhooks/rest/webhook`, { sender: senderId, message: message, metadata: context, }); return response.data; } catch (error) { logger.error('NLP response error:', error); return [{ text: "Désolé, je rencontre un problème technique. Puis-je vous aider autrement?", }]; } } } module.exports = new NLPService();