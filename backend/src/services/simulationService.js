/** * ============================================= * SIMULATION SERVICE * Agent training simulation business logic * Integrates with existing database and AI services * ============================================= */ const { v4: uuidv4 } = require('uuid'); const logger = require('../utils/logger'); const DatabaseService = require('./databaseService'); const AIService = require('./aiService'); class SimulationService { constructor() { this.db = new DatabaseService(); this.aiService = new AIService(); } /** * Get training scenarios with filtering */ async getScenarios(filters = {}) { try { const { difficulty, category, tags, agentSkillLevel, limit = 20, offset = 0 } = filters; // Build query const query = {}; if (difficulty) query.difficulty = difficulty; if (category) query.category = category; if (tags && tags.length > 0) query.tags = { $in: tags }; // Adjust scenarios based on agent skill level if (agentSkillLevel) { if (agentSkillLevel < 3) { query.difficulty = { $in: ['beginner', 'intermediate'] }; } else if (agentSkillLevel < 7) { query.difficulty = { $in: ['intermediate', 'expert'] }; } } const scenarios = await this.db.collection('simulation_scenarios') .find(query) .sort({ popularity: -1, created_at: -1 }) .limit(limit) .skip(offset) .toArray(); return scenarios.map(scenario => ({ id: scenario._id.toString(), title: scenario.title, description: scenario.description, difficulty: scenario.difficulty, category: scenario.category, tags: scenario.tags || [], expected_resolution_time: scenario.expected_resolution_time, customer_profile: scenario.customer_profile, learning_objectives: scenario.learning_objectives, success_criteria: scenario.success_criteria, popularity: scenario.popularity || 0, created_at: scenario.created_at })); } catch (error) { logger.error('Error fetching scenarios:', error); throw error; } } /** * Get recommended scenarios for agent */ async getRecommendedScenarios(agentId, limit = 5) { try { // Get agent progress and weak areas const agentProgress = await this.getAgentProgress(agentId); const weakAreas = this.identifyWeakAreas(agentProgress); // Find scenarios that target weak areas const recommendedScenarios = await this.db.collection('simulation_scenarios') .find({ $or: [ { category: { $in: weakAreas } }, { learning_objectives: { $in: weakAreas } } ] }) .sort({ effectiveness_score: -1 }) .limit(limit) .toArray(); return recommendedScenarios.map(scenario => ({ ...scenario, id: scenario._id.toString(), recommendation_reason: this.getRecommendationReason(scenario, weakAreas) })); } catch (error) { logger.error('Error getting recommended scenarios:', error); throw error; } } /** * Get single scenario by ID */ async getScenario(scenarioId) { try { const scenario = await this.db.collection('simulation_scenarios') .findOne({ _id: this.db.objectId(scenarioId) }); if (!scenario) return null; return { id: scenario._id.toString(), title: scenario.title, description: scenario.description, difficulty: scenario.difficulty, category: scenario.category, customer_profile: scenario.customer_profile, context: scenario.context, learning_objectives: scenario.learning_objectives, success_criteria: scenario.success_criteria, expected_resolution_time: scenario.expected_resolution_time }; } catch (error) { logger.error('Error fetching scenario:', error); throw error; } } /** * Create new simulation session */ async createSession(sessionData) { try { const { scenarioId, agentId, settings, scenario } = sessionData; const session = { _id: uuidv4(), scenario_id: scenarioId, agent_id: agentId, status: 'active', settings, scenario, customer_persona: null, // Will be set by AI service messages: [], performance_metrics: { overall_score: 0, empathy_score: 0, efficiency_score: 0, accuracy_score: 0, customer_satisfaction: 0, resolution_time: 0, response_times: [] }, created_at: new Date(), updated_at: new Date(), last_customer_message: null }; await this.db.collection('simulation_sessions').insertOne(session); return { id: session._id, scenario_id: scenarioId, agent_id: agentId, status: session.status, performance_metrics: session.performance_metrics, created_at: session.created_at }; } catch (error) { logger.error('Error creating simulation session:', error); throw error; } } /** * Get active session for agent */ async getActiveSession(agentId) { try { const session = await this.db.collection('simulation_sessions') .findOne({ agent_id: agentId, status: 'active' }); return session ? { id: session._id, scenario_id: session.scenario_id, status: session.status, created_at: session.created_at } : null; } catch (error) { logger.error('Error getting active session:', error); throw error; } } /** * Get session by ID */ async getSession(sessionId) { try { const session = await this.db.collection('simulation_sessions') .findOne({ _id: sessionId }); if (!session) return null; return { id: session._id, scenario_id: session.scenario_id, agent_id: session.agent_id, status: session.status, messages: session.messages || [], performance_metrics: session.performance_metrics, customer_profile: session.scenario?.customer_profile, customer_persona: session.customer_persona, context: session.scenario?.context, created_at: session.created_at, last_customer_message: session.last_customer_message }; } catch (error) { logger.error('Error getting session:', error); throw error; } } /** * Add message to session */ async addMessage(sessionId, messageData) { try { const message = { id: uuidv4(), ...messageData }; const updateData = { $push: { messages: message }, $set: { updated_at: new Date() } }; if (messageData.sender === 'customer') { updateData.$set.last_customer_message = new Date(); } await this.db.collection('simulation_sessions') .updateOne({ _id: sessionId }, updateData); return message; } catch (error) { logger.error('Error adding message:', error); throw error; } } /** * Update performance metrics */ async updatePerformanceMetrics(sessionId, updateData) { try { const { response_analysis, customer_feedback, response_time } = updateData; // Calculate updated metrics const session = await this.getSession(sessionId); const currentMetrics = session.performance_metrics; const updatedMetrics = { empathy_score: this.calculateEmpathyScore(response_analysis, customer_feedback), efficiency_score: this.calculateEfficiencyScore(response_time, session.messages.length), accuracy_score: this.calculateAccuracyScore(response_analysis), customer_satisfaction: customer_feedback.satisfaction, resolution_time: (Date.now() - new Date(session.created_at).getTime()) / 60000, // minutes response_times: [...currentMetrics.response_times, response_time] }; // Calculate overall score updatedMetrics.overall_score = ( updatedMetrics.empathy_score * 0.25 + updatedMetrics.efficiency_score * 0.25 + updatedMetrics.accuracy_score * 0.25 + updatedMetrics.customer_satisfaction * 10 * 0.25 ); await this.db.collection('simulation_sessions') .updateOne( { _id: sessionId }, { $set: { performance_metrics: updatedMetrics, updated_at: new Date() } } ); return updatedMetrics; } catch (error) { logger.error('Error updating performance metrics:', error); throw error; } } /** * Calculate final metrics and end session */ async calculateFinalMetrics(sessionId) { try { const session = await this.getSession(sessionId); const metrics = session.performance_metrics; // Calculate additional final metrics const finalMetrics = { ...metrics, total_duration: (Date.now() - new Date(session.created_at).getTime()) / 60000, messages_count: session.messages.length, average_response_time: metrics.response_times.reduce((a, b) => a + b, 0) / metrics.response_times.length || 0, completion_rate: 100, // Completed successfully skill_improvements: await this.calculateSkillImprovements(session) }; return finalMetrics; } catch (error) { logger.error('Error calculating final metrics:', error); throw error; } } /** * End simulation session */ async endSession(sessionId, reason, finalMetrics) { try { await this.db.collection('simulation_sessions') .updateOne( { _id: sessionId }, { $set: { status: 'completed', completion_reason: reason, final_metrics: finalMetrics, ended_at: new Date(), updated_at: new Date() } } ); return true; } catch (error) { logger.error('Error ending session:', error); throw error; } } /** * Get agent progress */ async getAgentProgress(agentId) { try { // Get existing progress or create new let progress = await this.db.collection('agent_progress') .findOne({ agent_id: agentId }); if (!progress) { progress = { agent_id: agentId, total_sessions: 0, completed_sessions: 0, average_score: 0, skill_levels: { empathy: 0, efficiency: 0, accuracy: 0, communication: 0, problem_solving: 0 }, badges_earned: [], current_streak: 0, best_streak: 0, created_at: new Date(), updated_at: new Date() }; await this.db.collection('agent_progress').insertOne(progress); } return progress; } catch (error) { logger.error('Error getting agent progress:', error); throw error; } } /** * Update agent progress after session completion */ async updateAgentProgress(agentId, updateData) { try { const { final_metrics, scenario_id, completion_reason } = updateData; const progress = await this.getAgentProgress(agentId); // Update statistics const updatedProgress = { total_sessions: progress.total_sessions + 1, completed_sessions: completion_reason === 'completed' ? progress.completed_sessions + 1 : progress.completed_sessions, average_score: this.calculateNewAverage(progress.average_score, final_metrics.overall_score, progress.completed_sessions), skill_levels: this.updateSkillLevels(progress.skill_levels, final_metrics), current_streak: completion_reason === 'completed' ? progress.current_streak + 1 : 0, updated_at: new Date() }; updatedProgress.best_streak = Math.max(updatedProgress.current_streak, progress.best_streak); await this.db.collection('agent_progress') .updateOne( { agent_id: agentId }, { $set: updatedProgress } ); return { ...progress, ...updatedProgress }; } catch (error) { logger.error('Error updating agent progress:', error); throw error; } } /** * Check for new achievements/badges */ async checkAchievements(agentId, finalMetrics) { try { const progress = await this.getAgentProgress(agentId); const newBadges = []; // Define achievement criteria const achievements = [ { id: 'first_completion', name: 'First Steps', description: 'Complete your first simulation', criteria: () => progress.completed_sessions === 1 }, { id: 'perfect_score', name: 'Perfect Performance', description: 'Achieve a perfect score of 100%', criteria: () => finalMetrics.overall_score >= 100 }, { id: 'empathy_master', name: 'Empathy Master', description: 'Achieve empathy score above 90%', criteria: () => finalMetrics.empathy_score >= 90 }, { id: 'speed_demon', name: 'Speed Demon', description: 'Complete simulation in under 5 minutes', criteria: () => finalMetrics.total_duration < 5 }, { id: 'streak_5', name: 'On Fire', description: 'Complete 5 simulations in a row', criteria: () => progress.current_streak >= 5 } ]; // Check each achievement for (const achievement of achievements) { if (!progress.badges_earned.some(badge => badge.id === achievement.id) && achievement.criteria()) { const badge = { ...achievement, earned_at: new Date(), rarity: this.calculateBadgeRarity(achievement.id) }; newBadges.push(badge); // Add to agent progress await this.db.collection('agent_progress') .updateOne( { agent_id: agentId }, { $push: { badges_earned: badge } } ); } } return newBadges; } catch (error) { logger.error('Error checking achievements:', error); throw error; } } /** * Get session history for agent */ async getSessionHistory(agentId, options = {}) { try { const { limit = 50, offset = 0 } = options; const sessions = await this.db.collection('simulation_sessions') .find({ agent_id: agentId, status: { $in: ['completed', 'abandoned'] } }) .sort({ ended_at: -1 }) .limit(limit) .skip(offset) .toArray(); return sessions.map(session => ({ id: session._id, scenario_id: session.scenario_id, status: session.status, completion_reason: session.completion_reason, performance_metrics: session.final_metrics || session.performance_metrics, created_at: session.created_at, ended_at: session.ended_at })); } catch (error) { logger.error('Error getting session history:', error); throw error; } } /** * Get leaderboard */ async getLeaderboard(options = {}) { try { const { limit = 50, timeframe = 'monthly' } = options; // Calculate date range based on timeframe const dateRange = this.getDateRange(timeframe); const leaderboard = await this.db.collection('agent_progress') .aggregate([ { $lookup: { from: 'users', localField: 'agent_id', foreignField: '_id', as: 'agent_info' } }, { $match: { updated_at: { $gte: dateRange.start } } }, { $sort: { average_score: -1, completed_sessions: -1 } }, { $limit: limit }, { $project: { agent_id: 1, agent_name: { $arrayElemAt: ['$agent_info.name', 0] }, average_score: 1, completed_sessions: 1, badges_earned: { $size: '$badges_earned' }, current_streak: 1 } } ]) .toArray(); return leaderboard.map((entry, index) => ({ rank: index + 1, ...entry })); } catch (error) { logger.error('Error getting leaderboard:', error); throw error; } } // Helper methods identifyWeakAreas(agentProgress) { const skillLevels = agentProgress.skill_levels || {}; const weakAreas = []; Object.entries(skillLevels).forEach(([skill, level]) => { if (level < 70) { // Below 70% is considered weak weakAreas.push(skill); } }); return weakAreas; } getRecommendationReason(scenario, weakAreas) { const matchingAreas = weakAreas.filter(area => scenario.learning_objectives.includes(area) || scenario.category === area ); if (matchingAreas.length > 0) { return `Targets your weak areas: ${matchingAreas.join(', ')}`; } return 'Recommended based on your progress'; } calculateEmpathyScore(responseAnalysis, customerFeedback) { // Combine AI analysis with customer feedback return (responseAnalysis.empathy_score * 0.7 + customerFeedback.empathy_rating * 10 * 0.3); } calculateEfficiencyScore(responseTime, messageCount) { // Score based on response time and conversation length const timeScore = Math.max(0, 100 - (responseTime / 1000)); // Penalty for slow responses const lengthScore = Math.max(0, 100 - (messageCount * 2)); // Penalty for long conversations return (timeScore + lengthScore) / 2; } calculateAccuracyScore(responseAnalysis) { return responseAnalysis.accuracy_score || 75; // Default if not available } calculateNewAverage(currentAverage, newScore, sessionCount) { if (sessionCount === 0) return newScore; return ((currentAverage * sessionCount) + newScore) / (sessionCount + 1); } updateSkillLevels(currentLevels, finalMetrics) { return { empathy: Math.min(100, currentLevels.empathy + (finalMetrics.empathy_score - currentLevels.empathy) * 0.1), efficiency: Math.min(100, currentLevels.efficiency + (finalMetrics.efficiency_score - currentLevels.efficiency) * 0.1), accuracy: Math.min(100, currentLevels.accuracy + (finalMetrics.accuracy_score - currentLevels.accuracy) * 0.1), communication: Math.min(100, currentLevels.communication + (finalMetrics.overall_score - currentLevels.communication) * 0.05), problem_solving: Math.min(100, currentLevels.problem_solving + (finalMetrics.overall_score - currentLevels.problem_solving) * 0.05) }; } calculateBadgeRarity(achievementId) { const rarityMap = { 'first_completion': 'common', 'perfect_score': 'legendary', 'empathy_master': 'epic', 'speed_demon': 'rare', 'streak_5': 'epic' }; return rarityMap[achievementId] || 'common'; } getDateRange(timeframe) { const now = new Date(); const ranges = { daily: new Date(now.getTime() - 24 * 60 * 60 * 1000), weekly: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000), monthly: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000), all: new Date(0) }; return { start: ranges[timeframe] || ranges.monthly, end: now }; } async calculateSkillImprovements(session) { // Calculate what skills were improved during this session const improvements = {}; const metrics = session.performance_metrics; if (metrics.empathy_score > 80) improvements.empathy = 5; if (metrics.efficiency_score > 80) improvements.efficiency = 5; if (metrics.accuracy_score > 80) improvements.accuracy = 5; return improvements; } } module.exports = SimulationService;