/** * Service d'intégration ML pour le backend Node.js * Free Mobile Chatbot Dashboard - Phase 2 Backend Integration * Communication avec le service ML FastAPI (port 5001) */ const axios = require('axios'); const logger = require('../config/logger'); const ConversationClassification = require('../models/ConversationClassification'); const AdminAlert = require('../models/AdminAlert'); class MLIntegrationService { constructor() { this.mlServiceUrl = process.env.ML_SERVICE_URL || 'http://localhost:5001'; this.apiKey = process.env.ML_API_KEY || 'dev-api-key'; this.timeout = parseInt(process.env.ML_TIMEOUT) || 30000; // 30 secondes // Configuration axios pour le service ML this.mlClient = axios.create({ baseURL: this.mlServiceUrl, timeout: this.timeout, headers: { 'Content-Type': 'application/json', 'X-API-Key': this.apiKey } }); // Intercepteurs pour logging et gestion d'erreurs this.mlClient.interceptors.request.use( (config) => { logger.info(`ML Service Request: ${config.method.toUpperCase()} ${config.url}`); return config; }, (error) => { logger.error('ML Service Request Error:', error); return Promise.reject(error); } ); this.mlClient.interceptors.response.use( (response) => { logger.info(`ML Service Response: ${response.status} - ${response.config.url}`); return response; }, (error) => { logger.error('ML Service Response Error:', { url: error.config?.url, status: error.response?.status, message: error.message }); return Promise.reject(error); } ); } /** * Classification complète d'une conversation * @param {Object} conversationData - Données de la conversation * @returns {Promise<Object>} - Résultat de classification */ async classifyConversation(conversationData) { try { const { conversationId, messages, customerData, metadata } = conversationData; // Validation des données d'entrée if (!conversationId || !messages || !customerData) { throw new Error('Missing required data for ML classification'); } // Préparation de la requête ML const mlRequest = { conversation_id: conversationId, messages: messages.map(msg => ({ id: msg._id || msg.id, sender: msg.sender, content: msg.content, timestamp: msg.timestamp || msg.createdAt })), customer_data: { customer_id: customerData._id || customerData.customerId, plan_type: customerData.planType || customerData.plan_type, monthly_revenue: customerData.monthlyRevenue || customerData.monthly_revenue || 0, tenure_months: customerData.tenureMonths || customerData.tenure_months || 0, support_tickets_count: customerData.supportTicketsCount || customerData.support_tickets_count || 0, satisfaction_score: customerData.satisfactionScore || customerData.satisfaction_score, lifetime_value: customerData.lifetimeValue || customerData.lifetime_value || 0 }, conversation_metadata: { conversation_id: conversationId, channel: metadata?.channel || 'web', duration_minutes: metadata?.durationMinutes || metadata?.duration_minutes, agent_id: metadata?.agentId || metadata?.agent_id, previous_interactions: metadata?.previousInteractions || metadata?.previous_interactions || 0 }, force_reprocess: metadata?.forceReprocess || false }; // Appel au service ML const response = await this.mlClient.post('/api/v1/classify', mlRequest); if (!response.data.success) { throw new Error(`ML Classification failed: ${response.data.error}`); } const classification = response.data.classification; // Sauvegarde en base de données const savedClassification = await this.saveClassification(classification, customerData._id); // Génération d'alertes si nécessaire await this.generateAlertsFromClassification(savedClassification); logger.info(`Conversation classified successfully: ${conversationId}`, { category: classification.category, priority: classification.priority_score, confidence: classification.confidence }); return { success: true, classification: savedClassification, cached: response.data.cached || false }; } catch (error) { logger.error('ML Classification Error:', { conversationId: conversationData.conversationId, error: error.message, stack: error.stack }); // Fallback classification en cas d'erreur const fallbackClassification = await this.createFallbackClassification( conversationData.conversationId, conversationData.customerData._id, error.message ); return { success: false, classification: fallbackClassification, error: error.message, fallback: true }; } } /** * Sauvegarde d'une classification en base de données * @param {Object} classification - Classification ML * @param {String} customerId - ID du client * @returns {Promise<Object>} - Classification sauvegardée */ async saveClassification(classification, customerId) { try { const classificationDoc = new ConversationClassification({ conversationId: classification.conversation_id, customerId: customerId, category: classification.category, priorityScore: classification.priority_score, confidence: classification.confidence, businessImpact: { revenueAtRisk: classification.business_impact.revenue_at_risk || 0, opportunityValue: classification.business_impact.opportunity_value || 0, retentionProbability: classification.business_impact.retention_probability || 0.5, lifetimeValueImpact: classification.business_impact.lifetime_value_impact || 0 }, sentiment: { score: classification.sentiment.score, trend: classification.sentiment.trend, confidence: classification.sentiment.confidence, keyEmotions: classification.sentiment.key_emotions || [] }, recommendedActions: classification.recommended_actions.map(action => ({ type: action.type, priority: action.priority, script: action.script, expectedOutcome: action.expected_outcome, confidence: action.confidence, estimatedRevenueImpact: action.estimated_revenue_impact || 0 })), mlModelVersion: classification.ml_model_version, processingTimeMs: classification.processing_time_ms, featuresUsed: classification.features_used || [], modelLatencyMs: classification.model_latency_ms || 0, processedAt: new Date(classification.processed_at) }); const saved = await classificationDoc.save(); logger.info(`Classification saved to database: ${saved._id}`); return saved; } catch (error) { logger.error('Error saving classification:', error); throw error; } } /** * Génération d'alertes basées sur la classification * @param {Object} classification - Classification sauvegardée */ async generateAlertsFromClassification(classification) { try { const alerts = []; // Alerte pour résiliation critique if (classification.category === 'RESILIATION_CRITIQUE') { alerts.push({ type: 'CHURN_RISK', severity: 'CRITICAL', title: 'Risque de résiliation détecté', description: `Client à risque de résiliation avec un score de priorité de ${classification.priorityScore}`, priority: Math.max(90, classification.priorityScore) }); } // Alerte pour opportunité de vente if (classification.category === 'VENTE_OPPORTUNITE' && classification.businessImpact.opportunityValue > 20) { alerts.push({ type: 'VENTE_OPPORTUNITY', severity: 'HIGH', title: 'Opportunité de vente identifiée', description: `Opportunité de revenus additionnels: ${classification.businessImpact.opportunityValue}€`, priority: Math.min(80, classification.priorityScore) }); } // Alerte pour support urgent if (classification.category === 'SUPPORT_URGENT') { alerts.push({ type: 'ESCALATION_NEEDED', severity: 'HIGH', title: 'Escalade technique requise', description: 'Problème technique nécessitant une intervention rapide', priority: classification.priorityScore }); } // Alerte pour sentiment très négatif if (classification.sentiment.score < -0.7) { alerts.push({ type: 'SATISFACTION_CRITICAL', severity: 'HIGH', title: 'Satisfaction client critique', description: `Sentiment très négatif détecté (score: ${classification.sentiment.score.toFixed(2)})`, priority: Math.max(70, classification.priorityScore) }); } // Création des alertes en base for (const alertData of alerts) { const alert = new AdminAlert({ ...alertData, conversationId: classification.conversationId, customerId: classification.customerId, classificationId: classification._id, triggeredBy: { rule: 'ML_CLASSIFICATION', conditions: { category: classification.category, priorityScore: classification.priorityScore, confidence: classification.confidence }, mlScore: classification.confidence }, contextData: { businessImpact: classification.businessImpact, sentiment: classification.sentiment, recommendedActions: classification.recommendedActions.slice(0, 3) // Top 3 actions } }); await alert.save(); logger.info(`Alert created: ${alert._id} (${alert.type})`); } } catch (error) { logger.error('Error generating alerts:', error); // Ne pas faire échouer la classification si les alertes échouent } } /** * Création d'une classification de fallback en cas d'erreur ML * @param {String} conversationId - ID de la conversation * @param {String} customerId - ID du client * @param {String} errorMessage - Message d'erreur * @returns {Promise<Object>} - Classification de fallback */ async createFallbackClassification(conversationId, customerId, errorMessage) { try { const fallbackClassification = new ConversationClassification({ conversationId: conversationId, customerId: customerId, category: 'INFO_SIMPLE', priorityScore: 30, confidence: 0.3, businessImpact: { revenueAtRisk: 0, opportunityValue: 0, retentionProbability: 0.7, lifetimeValueImpact: 0 }, sentiment: { score: 0, trend: 'stable', confidence: 0.3, keyEmotions: [] }, recommendedActions: [{ type: 'ESCALATE_TO_AGENT', priority: 5, script: 'Transférer vers un agent humain pour traitement manuel', expectedOutcome: 'Traitement personnalisé de la demande', confidence: 0.8 }], mlModelVersion: 'fallback-1.0.0', processingTimeMs: 0, featuresUsed: ['fallback'], humanValidated: false }); const saved = await fallbackClassification.save(); // Créer une alerte système pour l'erreur ML const systemAlert = new AdminAlert({ type: 'SYSTEM_ANOMALY', severity: 'MEDIUM', title: 'Erreur de classification ML', description: `Échec de la classification ML: ${errorMessage}`, conversationId: conversationId, customerId: customerId, classificationId: saved._id, triggeredBy: { rule: 'ML_SERVICE_ERROR', conditions: { error: errorMessage } }, priority: 60 }); await systemAlert.save(); return saved; } catch (error) { logger.error('Error creating fallback classification:', error); throw error; } } /** * Récupération de la queue de priorité depuis le service ML * @param {Object} options - Options de filtrage * @returns {Promise<Array>} - Queue de priorité */ async getPriorityQueue(options = {}) { try { const params = { limit: options.limit || 50, min_priority: options.minPriority || 0 }; if (options.category) { params.category = options.category; } const response = await this.mlClient.get('/api/v1/queue/priority', { params }); if (!response.data.success) { throw new Error('Failed to fetch priority queue'); } return response.data.queue; } catch (error) { logger.error('Error fetching priority queue:', error); // Fallback: récupérer depuis la base de données locale const localQueue = await ConversationClassification.findHighPriority( options.minPriority || 80, options.limit || 50 ); return localQueue.map(item => ({ conversation_id: item.conversationId, priority_score: item.priorityScore, category: item.category, timestamp: item.processedAt.toISOString(), customer_id: item.customerId, business_impact: item.businessImpact })); } } /** * Récupération des métriques de performance ML * @returns {Promise<Object>} - Métriques de performance */ async getPerformanceMetrics() { try { const response = await this.mlClient.get('/api/v1/metrics/performance'); if (!response.data.success) { throw new Error('Failed to fetch performance metrics'); } return response.data.metrics; } catch (error) { logger.error('Error fetching ML performance metrics:', error); // Fallback: métriques basiques depuis la base de données const now = new Date(); const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000); const recentClassifications = await ConversationClassification.countDocuments({ processedAt: { $gte: oneHourAgo } }); const avgProcessingTime = await ConversationClassification.aggregate([ { $match: { processedAt: { $gte: oneHourAgo } } }, { $group: { _id: null, avgTime: { $avg: '$processingTimeMs' } } } ]); return { total_classifications: recentClassifications, average_processing_time_ms: avgProcessingTime[0]?.avgTime || 0, throughput_per_second: recentClassifications / 3600, cache_hit_rate: 0.0, fallback: true }; } } /** * Invalidation du cache ML pour une conversation * @param {String} conversationId - ID de la conversation * @returns {Promise<Boolean>} - Succès de l'invalidation */ async invalidateCache(conversationId) { try { const response = await this.mlClient.delete(`/api/v1/cache/classification/${conversationId}`); return response.data.success; } catch (error) { logger.error('Error invalidating ML cache:', error); return false; } } /** * Vérification de la santé du service ML * @returns {Promise<Object>} - Statut de santé */ async healthCheck() { try { const response = await this.mlClient.get('/health'); return { status: 'healthy', ml_service: response.data, timestamp: new Date().toISOString() }; } catch (error) { logger.error('ML Service health check failed:', error); return { status: 'unhealthy', error: error.message, timestamp: new Date().toISOString() }; } } } module.exports = new MLIntegrationService();