/** * ============================================= * [AI] CONVERSATION ANALYSIS SERVICE * AI-powered conversation analysis for intelligent call management * Determines when text chat is insufficient and calls are needed * ============================================= */ const logger = require('../config/logger'); const Message = require('../models/Message'); const Conversation = require('../models/Conversation'); const User = require('../models/User'); class ConversationAnalysisService { constructor() { this.frustrationKeywords = [ 'frustré', 'énervé', 'agacé', 'marre', 'impossible', 'n\'arrive pas', 'ne fonctionne pas', 'problème', 'bug', 'erreur', 'panne', 'cassé', 'urgent', 'rapidement', 'immédiatement', 'tout de suite', 'maintenant' ]; this.complexityIndicators = [ 'plusieurs problèmes', 'depuis longtemps', 'déjà essayé', 'toujours pas', 'configuration', 'paramètres', 'technique', 'réseau', 'connexion', 'installation', 'mise à jour', 'synchronisation', 'compatibilité' ]; this.callTriggerThresholds = { frustrationScore: 0.6, complexityScore: 0.7, messageCount: 8, timeSpent: 300000, // 5 minutes failedAttempts: 3 }; } /** * Analyze conversation to determine if a call is needed */ async analyzeConversationForCallNeed(conversationId, userId) { try { logger.info(`[SEARCH] Analyzing conversation for call need`, { conversationId, userId }); // Get conversation and messages const conversation = await Conversation.findById(conversationId); const messages = await Message.find({ conversationId }) .sort({ timestamp: 1 }) .limit(50); // Analyze last 50 messages if (!conversation || messages.length === 0) { return { needsCall: false, reason: 'insufficient_data', confidence: 0 }; } // Perform analysis const analysis = { frustrationScore: this.calculateFrustrationScore(messages), complexityScore: this.calculateComplexityScore(messages), conversationMetrics: this.calculateConversationMetrics(messages, conversation), userBehaviorAnalysis: await this.analyzeUserBehavior(userId, messages), issueResolutionProgress: this.analyzeResolutionProgress(messages) }; // Determine if call is needed const callRecommendation = this.determineCallNeed(analysis); logger.info(`[ANALYTICS] Conversation analysis completed`, { conversationId, userId, needsCall: callRecommendation.needsCall, confidence: callRecommendation.confidence, primaryReason: callRecommendation.primaryReason }); return { ...callRecommendation, analysis, conversationId, userId, analyzedAt: new Date() }; } catch (error) { logger.error('Failed to analyze conversation for call need:', error); throw error; } } /** * Calculate frustration score based on message content */ calculateFrustrationScore(messages) { let frustrationScore = 0; let totalMessages = 0; messages.forEach(message => { if (message.sender === 'user' && message.content?.text) { totalMessages++; const text = message.content.text.toLowerCase(); // Count frustration keywords const frustrationCount = this.frustrationKeywords.reduce((count, keyword) => { return count + (text.includes(keyword) ? 1 : 0); }, 0); // Check for repeated messages (indicating frustration) const isRepeated = messages.some(otherMsg => otherMsg !== message && otherMsg.sender === 'user' && this.calculateSimilarity(text, otherMsg.content?.text?.toLowerCase() || '') > 0.8 ); // Check for caps lock usage (indicating frustration) const capsRatio = (text.match(/[A-Z]/g) || []).length / text.length; // Calculate message frustration score let messageScore = 0; messageScore += frustrationCount * 0.3; messageScore += isRepeated ? 0.4 : 0; messageScore += capsRatio > 0.5 ? 0.3 : 0; frustrationScore += Math.min(messageScore, 1); } }); return totalMessages > 0 ? frustrationScore / totalMessages : 0; } /** * Calculate complexity score based on issue type and technical terms */ calculateComplexityScore(messages) { let complexityScore = 0; let totalMessages = 0; messages.forEach(message => { if (message.sender === 'user' && message.content?.text) { totalMessages++; const text = message.content.text.toLowerCase(); // Count complexity indicators const complexityCount = this.complexityIndicators.reduce((count, indicator) => { return count + (text.includes(indicator) ? 1 : 0); }, 0); // Check for technical terms const technicalTerms = [ 'ip', 'dns', 'wifi', 'bluetooth', 'apn', 'vpn', 'ssl', 'https', 'routeur', 'modem', 'antenne', 'signal', 'débit', 'latence' ]; const technicalCount = technicalTerms.reduce((count, term) => { return count + (text.includes(term) ? 1 : 0); }, 0); // Calculate message complexity score const messageScore = Math.min((complexityCount * 0.4) + (technicalCount * 0.3), 1); complexityScore += messageScore; } }); return totalMessages > 0 ? complexityScore / totalMessages : 0; } /** * Calculate conversation metrics */ calculateConversationMetrics(messages, conversation) { const userMessages = messages.filter(msg => msg.sender === 'user'); const botMessages = messages.filter(msg => msg.sender === 'bot'); const conversationDuration = conversation.updatedAt - conversation.createdAt; const averageResponseTime = this.calculateAverageResponseTime(messages); const backAndForthCount = Math.min(userMessages.length, botMessages.length); return { totalMessages: messages.length, userMessages: userMessages.length, botMessages: botMessages.length, conversationDuration, averageResponseTime, backAndForthCount, messagesPerMinute: messages.length / (conversationDuration / 60000) }; } /** * Analyze user behavior patterns */ async analyzeUserBehavior(userId, messages) { try { // Get user's conversation history const user = await User.findById(userId); const recentConversations = await Conversation.find({ userId }) .sort({ createdAt: -1 }) .limit(10); // Analyze patterns const behaviorPatterns = { isFrequentUser: recentConversations.length > 5, hasRecentIssues: recentConversations.some(conv => Date.now() - conv.createdAt < 24 * 60 * 60 * 1000 // Last 24 hours ), preferredCommunicationStyle: this.determinePreferredStyle(messages), technicalProficiency: this.assessTechnicalProficiency(messages) }; return behaviorPatterns; } catch (error) { logger.error('Failed to analyze user behavior:', error); return { isFrequentUser: false, hasRecentIssues: false, preferredCommunicationStyle: 'unknown', technicalProficiency: 'medium' }; } } /** * Analyze issue resolution progress */ analyzeResolutionProgress(messages) { const botSolutions = messages.filter(msg => msg.sender === 'bot' && msg.content?.text?.includes('essayez') || msg.content?.text?.includes('solution') || msg.content?.text?.includes('résoudre') ); const userFeedback = messages.filter(msg => msg.sender === 'user' && ( msg.content?.text?.includes('ça marche') || msg.content?.text?.includes('résolu') || msg.content?.text?.includes('fonctionne') || msg.content?.text?.includes('toujours pas') || msg.content?.text?.includes('ne marche pas') ) ); return { solutionsOffered: botSolutions.length, userFeedbackCount: userFeedback.length, resolutionAttempts: botSolutions.length, successfulResolutions: userFeedback.filter(msg => msg.content?.text?.includes('ça marche') || msg.content?.text?.includes('résolu') || msg.content?.text?.includes('fonctionne') ).length, failedResolutions: userFeedback.filter(msg => msg.content?.text?.includes('toujours pas') || msg.content?.text?.includes('ne marche pas') ).length }; } /** * Determine if a call is needed based on analysis */ determineCallNeed(analysis) { const { frustrationScore, complexityScore, conversationMetrics, userBehaviorAnalysis, issueResolutionProgress } = analysis; let callScore = 0; const reasons = []; // Frustration-based scoring if (frustrationScore >= this.callTriggerThresholds.frustrationScore) { callScore += 0.4; reasons.push('high_user_frustration'); } // Complexity-based scoring if (complexityScore >= this.callTriggerThresholds.complexityScore) { callScore += 0.3; reasons.push('complex_technical_issue'); } // Conversation length scoring if (conversationMetrics.userMessages >= this.callTriggerThresholds.messageCount) { callScore += 0.2; reasons.push('extended_conversation'); } // Time spent scoring if (conversationMetrics.conversationDuration >= this.callTriggerThresholds.timeSpent) { callScore += 0.2; reasons.push('extended_time_spent'); } // Failed resolution attempts if (issueResolutionProgress.failedResolutions >= this.callTriggerThresholds.failedAttempts) { callScore += 0.3; reasons.push('multiple_failed_attempts'); } // User behavior modifiers if (userBehaviorAnalysis.hasRecentIssues) { callScore += 0.1; reasons.push('recent_issues_pattern'); } if (userBehaviorAnalysis.technicalProficiency === 'low' && complexityScore > 0.5) { callScore += 0.2; reasons.push('technical_complexity_mismatch'); } const needsCall = callScore >= 0.6; const confidence = Math.min(callScore, 1); const primaryReason = reasons[0] || 'general_assessment'; return { needsCall, confidence, callScore, primaryReason, reasons, recommendation: this.generateCallRecommendation(needsCall, confidence, reasons) }; } /** * Generate call recommendation message */ generateCallRecommendation(needsCall, confidence, reasons) { if (!needsCall) { return { message: "La conversation peut continuer en mode texte.", action: "continue_chat", priority: "low" }; } const highConfidence = confidence >= 0.8; const priority = highConfidence ? "high" : "medium"; let message = "Un appel téléphonique pourrait mieux résoudre votre problème. "; if (reasons.includes('high_user_frustration')) { message += "Je remarque que vous semblez frustré, un conseiller peut vous aider plus efficacement. "; } if (reasons.includes('complex_technical_issue')) { message += "Votre problème semble technique et complexe, un appel permettra un diagnostic plus précis. "; } if (reasons.includes('multiple_failed_attempts')) { message += "Plusieurs solutions ont été tentées sans succès, un expert peut vous accompagner directement. "; } return { message: message.trim(), action: "suggest_call", priority, confidence }; } /** * Helper methods */ calculateSimilarity(str1, str2) { if (!str1 || !str2) return 0; const longer = str1.length > str2.length ? str1 : str2; const shorter = str1.length > str2.length ? str2 : str1; const editDistance = this.levenshteinDistance(longer, shorter); return (longer.length - editDistance) / longer.length; } levenshteinDistance(str1, str2) { const matrix = []; for (let i = 0; i <= str2.length; i++) { matrix[i] = [i]; } for (let j = 0; j <= str1.length; j++) { matrix[0][j] = j; } for (let i = 1; i <= str2.length; i++) { for (let j = 1; j <= str1.length; j++) { if (str2.charAt(i - 1) === str1.charAt(j - 1)) { matrix[i][j] = matrix[i - 1][j - 1]; } else { matrix[i][j] = Math.min( matrix[i - 1][j - 1] + 1, matrix[i][j - 1] + 1, matrix[i - 1][j] + 1 ); } } } return matrix[str2.length][str1.length]; } calculateAverageResponseTime(messages) { let totalResponseTime = 0; let responseCount = 0; for (let i = 1; i < messages.length; i++) { const currentMsg = messages[i]; const previousMsg = messages[i - 1]; if (currentMsg.sender !== previousMsg.sender) { const responseTime = currentMsg.timestamp - previousMsg.timestamp; totalResponseTime += responseTime; responseCount++; } } return responseCount > 0 ? totalResponseTime / responseCount : 0; } determinePreferredStyle(messages) { const userMessages = messages.filter(msg => msg.sender === 'user'); if (userMessages.length === 0) return 'unknown'; const avgLength = userMessages.reduce((sum, msg) => sum + (msg.content?.text?.length || 0), 0) / userMessages.length; if (avgLength > 100) return 'detailed'; if (avgLength < 30) return 'concise'; return 'balanced'; } assessTechnicalProficiency(messages) { const userMessages = messages.filter(msg => msg.sender === 'user'); const technicalTermCount = userMessages.reduce((count, msg) => { const text = msg.content?.text?.toLowerCase() || ''; const technicalTerms = ['ip', 'dns', 'wifi', 'bluetooth', 'apn', 'routeur', 'modem']; return count + technicalTerms.reduce((termCount, term) => termCount + (text.includes(term) ? 1 : 0), 0); }, 0); const ratio = technicalTermCount / userMessages.length; if (ratio > 0.3) return 'high'; if (ratio > 0.1) return 'medium'; return 'low'; } } module.exports = ConversationAnalysisService;