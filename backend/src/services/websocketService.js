/** * Service WebSocket pour les événements temps réel ML * Free Mobile Chatbot Dashboard - Phase 2 Backend Integration */ const { Server } = require('socket.io'); const jwt = require('jsonwebtoken'); const logger = require('../config/logger'); const mlIntegrationService = require('./mlIntegrationService'); const ConversationClassification = require('../models/ConversationClassification'); const AdminAlert = require('../models/AdminAlert'); class WebSocketService { constructor() { this.io = null; this.connectedUsers = new Map(); // userId -> socket info this.adminSockets = new Set(); // sockets des admins this.agentSockets = new Set(); // sockets des agents // Métriques temps réel this.metricsInterval = null; this.metricsUpdateFrequency = 5000; // 5 secondes } /** * Initialisation du service WebSocket * @param {Object} server - Serveur HTTP */ initialize(server) { this.io = new Server(server, { cors: { origin: process.env.FRONTEND_URL || "http://localhost:3001", methods: ["GET", "POST"], credentials: true }, transports: ['websocket', 'polling'] }); // Middleware d'authentification WebSocket this.io.use(async (socket, next) => { try { const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', ''); if (!token) { return next(new Error('Authentication token required')); } const decoded = jwt.verify(token, process.env.JWT_SECRET); const user = await this.getUserById(decoded.id); if (!user) { return next(new Error('User not found')); } socket.user = user; next(); } catch (error) { logger.error('WebSocket authentication error:', error); next(new Error('Authentication failed')); } }); // Gestion des connexions this.io.on('connection', (socket) => { this.handleConnection(socket); }); // Démarrage des métriques temps réel this.startMetricsUpdates(); logger.info('WebSocket service initialized'); } /** * Gestion d'une nouvelle connexion * @param {Object} socket - Socket client */ handleConnection(socket) { const user = socket.user; logger.info(`User connected via WebSocket: ${user.email} (${user.role})`); // Enregistrement de la connexion this.connectedUsers.set(user.id, { socket: socket, user: user, connectedAt: new Date(), subscriptions: new Set() }); // Ajout aux groupes selon le rôle if (user.role === 'admin') { this.adminSockets.add(socket); socket.join('admins'); } else if (user.role === 'agent') { this.agentSockets.add(socket); socket.join('agents'); } // Événements du socket this.setupSocketEvents(socket); // Envoi de l'état initial this.sendInitialState(socket); } /** * Configuration des événements du socket * @param {Object} socket - Socket client */ setupSocketEvents(socket) { const user = socket.user; // Souscription aux événements ML socket.on('subscribe:ml-events', (eventTypes) => { const userConnection = this.connectedUsers.get(user.id); if (userConnection) { eventTypes.forEach(eventType => { userConnection.subscriptions.add(eventType); socket.join(`ml:${eventType}`); }); logger.info(`User ${user.email} subscribed to ML events:`, eventTypes); } }); // Désouscription socket.on('unsubscribe:ml-events', (eventTypes) => { const userConnection = this.connectedUsers.get(user.id); if (userConnection) { eventTypes.forEach(eventType => { userConnection.subscriptions.delete(eventType); socket.leave(`ml:${eventType}`); }); } }); // Demande de métriques en temps réel socket.on('request:metrics', async (options = {}) => { try { const metrics = await this.getCurrentMetrics(options); socket.emit('metrics:update', metrics); } catch (error) { logger.error('Error sending metrics:', error); socket.emit('error', { message: 'Failed to fetch metrics' }); } }); // Demande de queue de priorité socket.on('request:priority-queue', async (options = {}) => { try { const queue = await mlIntegrationService.getPriorityQueue(options); socket.emit('priority-queue:update', queue); } catch (error) { logger.error('Error sending priority queue:', error); socket.emit('error', { message: 'Failed to fetch priority queue' }); } }); // Gestion de la déconnexion socket.on('disconnect', (reason) => { this.handleDisconnection(socket, reason); }); // Ping/Pong pour maintenir la connexion socket.on('ping', () => { socket.emit('pong'); }); } /** * Envoi de l'état initial au client * @param {Object} socket - Socket client */ async sendInitialState(socket) { try { const user = socket.user; // Métriques de base const metrics = await this.getCurrentMetrics(); socket.emit('initial:metrics', metrics); // Alertes actives pour l'utilisateur let alertsQuery = { status: { $in: ['ACTIVE', 'ACKNOWLEDGED', 'IN_PROGRESS'] } }; // Filtrer par utilisateur si agent if (user.role === 'agent') { alertsQuery.assignedTo = user.id; } const alerts = await AdminAlert.find(alertsQuery) .sort({ priority: -1, createdAt: -1 }) .limit(10) .populate('customerId', 'name email') .populate('conversationId', 'channel'); socket.emit('initial:alerts', alerts); // Queue de priorité (admin/agent seulement) if (['admin', 'agent'].includes(user.role)) { const priorityQueue = await mlIntegrationService.getPriorityQueue({ limit: 20 }); socket.emit('initial:priority-queue', priorityQueue); } } catch (error) { logger.error('Error sending initial state:', error); socket.emit('error', { message: 'Failed to load initial state' }); } } /** * Gestion de la déconnexion * @param {Object} socket - Socket client * @param {String} reason - Raison de la déconnexion */ handleDisconnection(socket, reason) { const user = socket.user; logger.info(`User disconnected: ${user.email} (reason: ${reason})`); // Nettoyage des références this.connectedUsers.delete(user.id); this.adminSockets.delete(socket); this.agentSockets.delete(socket); } /** * Émission d'événement de classification * @param {Object} classification - Classification ML */ emitClassificationEvent(classification) { const event = { type: 'classification:new', data: { id: classification._id, conversationId: classification.conversationId, customerId: classification.customerId, category: classification.category, priorityScore: classification.priorityScore, confidence: classification.confidence, businessImpact: classification.businessImpact, sentiment: classification.sentiment, processedAt: classification.processedAt }, timestamp: new Date().toISOString() }; // Émission vers les admins et agents this.io.to('admins').emit('ml:classification', event); this.io.to('agents').emit('ml:classification', event); // Émission spécifique si haute priorité if (classification.priorityScore >= 80) { this.io.to('ml:high-priority').emit('ml:high-priority-classification', event); } logger.info(`Classification event emitted: ${classification._id}`); } /** * Émission d'événement d'alerte * @param {Object} alert - Alerte admin */ emitAlertEvent(alert) { const event = { type: 'alert:new', data: { id: alert._id, type: alert.type, severity: alert.severity, title: alert.title, description: alert.description, conversationId: alert.conversationId, customerId: alert.customerId, priority: alert.priority, assignedTo: alert.assignedTo, createdAt: alert.createdAt }, timestamp: new Date().toISOString() }; // Émission vers les admins this.io.to('admins').emit('ml:alert', event); // Émission vers l'agent assigné si spécifié if (alert.assignedTo) { const assignedConnection = this.connectedUsers.get(alert.assignedTo.toString()); if (assignedConnection) { assignedConnection.socket.emit('ml:alert:assigned', event); } } // Émission spécifique si critique if (alert.severity === 'CRITICAL') { this.io.to('ml:critical-alerts').emit('ml:critical-alert', event); } logger.info(`Alert event emitted: ${alert._id} (${alert.type})`); } /** * Émission de mise à jour de métriques * @param {Object} metrics - Métriques actuelles */ emitMetricsUpdate(metrics) { const event = { type: 'metrics:update', data: metrics, timestamp: new Date().toISOString() }; // Émission vers tous les utilisateurs connectés this.io.emit('ml:metrics', event); } /** * Émission de mise à jour de la queue de priorité * @param {Array} queue - Queue de priorité */ emitPriorityQueueUpdate(queue) { const event = { type: 'priority-queue:update', data: queue, timestamp: new Date().toISOString() }; // Émission vers les admins et agents this.io.to('admins').emit('ml:priority-queue', event); this.io.to('agents').emit('ml:priority-queue', event); } /** * Récupération des métriques actuelles * @param {Object} options - Options de filtrage * @returns {Promise<Object>} - Métriques */ async getCurrentMetrics(options = {}) { try { // Métriques ML const mlMetrics = await mlIntegrationService.getPerformanceMetrics(); // Métriques locales const now = new Date(); const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000); const [ recentClassifications, activeAlerts, highPriorityConversations ] = await Promise.all([ ConversationClassification.countDocuments({ processedAt: { $gte: oneHourAgo } }), AdminAlert.countDocuments({ status: { $in: ['ACTIVE', 'ACKNOWLEDGED', 'IN_PROGRESS'] } }), ConversationClassification.countDocuments({ priorityScore: { $gte: 80 }, processedAt: { $gte: oneHourAgo } }) ]); return { ml: mlMetrics, dashboard: { recentClassifications, activeAlerts, highPriorityConversations, connectedUsers: this.connectedUsers.size, timestamp: now.toISOString() } }; } catch (error) { logger.error('Error getting current metrics:', error); throw error; } } /** * Démarrage des mises à jour de métriques périodiques */ startMetricsUpdates() { if (this.metricsInterval) { clearInterval(this.metricsInterval); } this.metricsInterval = setInterval(async () => { try { if (this.connectedUsers.size > 0) { const metrics = await this.getCurrentMetrics(); this.emitMetricsUpdate(metrics); } } catch (error) { logger.error('Error in metrics update interval:', error); } }, this.metricsUpdateFrequency); logger.info(`Metrics updates started (every ${this.metricsUpdateFrequency}ms)`); } /** * Arrêt des mises à jour de métriques */ stopMetricsUpdates() { if (this.metricsInterval) { clearInterval(this.metricsInterval); this.metricsInterval = null; logger.info('Metrics updates stopped'); } } /** * Récupération d'un utilisateur par ID (à adapter selon votre modèle User) * @param {String} userId - ID de l'utilisateur * @returns {Promise<Object>} - Utilisateur */ async getUserById(userId) { try { // À adapter selon votre modèle User const User = require('../models/User'); return await User.findById(userId).select('-password'); } catch (error) { logger.error('Error fetching user:', error); return null; } } /** * Fermeture du service WebSocket */ close() { this.stopMetricsUpdates(); if (this.io) { this.io.close(); logger.info('WebSocket service closed'); } } /** * Statistiques du service WebSocket * @returns {Object} - Statistiques */ getStats() { return { connectedUsers: this.connectedUsers.size, adminSockets: this.adminSockets.size, agentSockets: this.agentSockets.size, totalRooms: this.io ? this.io.sockets.adapter.rooms.size : 0, metricsUpdateActive: !!this.metricsInterval }; } } module.exports = new WebSocketService();