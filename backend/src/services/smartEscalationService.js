/** * ============================================= * SMART ESCALATION SERVICE * Intelligent escalation logic for call management * Automated agent assignment and context preservation * ============================================= */ const logger = require('../config/logger'); const User = require('../models/User'); const EmergencyCall = require('../models/EmergencyCall'); const ConversationAnalysisService = require('./conversationAnalysisService'); const AISuggestionEngine = require('./aiSuggestionEngine'); class SmartEscalationService { constructor() { this.conversationAnalysis = new ConversationAnalysisService(); this.aiSuggestionEngine = new AISuggestionEngine(); this.escalationThresholds = { aiAttempts: 3, conversationDuration: 600000, // 10 minutes userFrustrationScore: 0.7, complexityScore: 0.8, failedSolutions: 2 }; this.agentSpecializations = { technical: ['network', 'device', 'configuration', 'troubleshooting'], billing: ['payment', 'subscription', 'pricing', 'refund'], service: ['activation', 'modification', 'upgrade', 'cancellation'], support: ['general', 'complaint', 'information', 'guidance'] }; } /** * Evaluate if escalation to human agent is needed */ async evaluateEscalationNeed(callSession, aiAttempts = 0, conversationHistory = []) { try { logger.info(`[SEARCH] Evaluating escalation need`, { callId: callSession.callId, userId: callSession.userId, aiAttempts, conversationLength: conversationHistory.length }); // Get conversation analysis const conversationAnalysis = await this.conversationAnalysis.analyzeConversationForCallNeed( callSession.conversationId, callSession.userId ); // Analyze current call session const sessionAnalysis = this.analyzeCallSession(callSession, aiAttempts); // Evaluate escalation criteria const escalationEvaluation = this.evaluateEscalationCriteria( conversationAnalysis, sessionAnalysis, aiAttempts ); // Determine optimal agent type if escalation is needed let agentRequirements = null; if (escalationEvaluation.shouldEscalate) { agentRequirements = await this.determineAgentRequirements( conversationAnalysis, sessionAnalysis, callSession ); } const result = { shouldEscalate: escalationEvaluation.shouldEscalate, confidence: escalationEvaluation.confidence, reasons: escalationEvaluation.reasons, urgency: escalationEvaluation.urgency, agentRequirements, analysis: { conversation: conversationAnalysis, session: sessionAnalysis }, evaluatedAt: new Date() }; logger.info(`[ANALYTICS] Escalation evaluation completed`, { callId: callSession.callId, shouldEscalate: result.shouldEscalate, confidence: result.confidence, urgency: result.urgency }); return result; } catch (error) { logger.error('Failed to evaluate escalation need:', error); throw error; } } /** * Transfer call to human agent with full context preservation */ async transferToHumanAgent(callSession, escalationReason, agentRequirements = null) { try { logger.info(` Initiating transfer to human agent`, { callId: callSession.callId, userId: callSession.userId, escalationReason, agentRequirements }); // Find best available agent const selectedAgent = await this.findBestAvailableAgent( agentRequirements, callSession ); if (!selectedAgent) { logger.warn('No available agents found, adding to priority queue'); return await this.addToPriorityQueue(callSession, escalationReason); } // Prepare context for agent const agentContext = await this.prepareAgentContext( callSession, escalationReason, selectedAgent ); // Create agent briefing const agentBriefing = await this.createAgentBriefing( callSession, agentContext, escalationReason ); // Initiate transfer const transferResult = await this.initiateAgentTransfer( callSession, selectedAgent, agentBriefing ); // Update call session await this.updateCallSessionForTransfer( callSession, selectedAgent, transferResult ); logger.info(`[COMPLETE] Successfully transferred to human agent`, { callId: callSession.callId, agentId: selectedAgent._id, agentName: `${selectedAgent.profile.firstName} ${selectedAgent.profile.lastName}` }); return { success: true, agent: { id: selectedAgent._id, name: `${selectedAgent.profile.firstName} ${selectedAgent.profile.lastName}`, specialization: selectedAgent.specialization, experience: selectedAgent.experience }, briefing: agentBriefing, transferredAt: new Date(), estimatedResponseTime: this.calculateEstimatedResponseTime(selectedAgent) }; } catch (error) { logger.error('Failed to transfer to human agent:', error); throw error; } } /** * Analyze current call session */ analyzeCallSession(callSession, aiAttempts) { const sessionDuration = Date.now() - new Date(callSession.createdAt).getTime(); return { duration: sessionDuration, aiAttempts, status: callSession.status, urgencyLevel: callSession.urgencyLevel, hasWebRTCSession: !!callSession.webrtcSessionId, queuePosition: callSession.queueInfo?.position || 0, estimatedWaitTime: callSession.queueInfo?.estimatedWaitTime || 0 }; } /** * Evaluate escalation criteria */ evaluateEscalationCriteria(conversationAnalysis, sessionAnalysis, aiAttempts) { let escalationScore = 0; const reasons = []; // AI attempts threshold if (aiAttempts >= this.escalationThresholds.aiAttempts) { escalationScore += 0.4; reasons.push('max_ai_attempts_reached'); } // Session duration threshold if (sessionAnalysis.duration >= this.escalationThresholds.conversationDuration) { escalationScore += 0.3; reasons.push('extended_session_duration'); } // User frustration threshold if (conversationAnalysis.analysis?.frustrationScore >= this.escalationThresholds.userFrustrationScore) { escalationScore += 0.4; reasons.push('high_user_frustration'); } // Complexity threshold if (conversationAnalysis.analysis?.complexityScore >= this.escalationThresholds.complexityScore) { escalationScore += 0.3; reasons.push('high_complexity_issue'); } // Critical urgency level if (sessionAnalysis.urgencyLevel === 'critical') { escalationScore += 0.5; reasons.push('critical_urgency_level'); } // Failed solutions threshold const failedSolutions = conversationAnalysis.analysis?.issueResolutionProgress?.failedResolutions || 0; if (failedSolutions >= this.escalationThresholds.failedSolutions) { escalationScore += 0.3; reasons.push('multiple_failed_solutions'); } const shouldEscalate = escalationScore >= 0.6; const confidence = Math.min(escalationScore, 1); // Determine urgency level for escalation let urgency = 'medium'; if (escalationScore >= 0.8 || sessionAnalysis.urgencyLevel === 'critical') { urgency = 'high'; } else if (escalationScore < 0.4) { urgency = 'low'; } return { shouldEscalate, confidence, escalationScore, reasons, urgency }; } /** * Determine agent requirements based on analysis */ async determineAgentRequirements(conversationAnalysis, sessionAnalysis, callSession) { const requirements = { specialization: 'support', // default experience: 'intermediate', skills: [], language: 'fr', availability: 'immediate' }; // Determine specialization based on conversation analysis const category = conversationAnalysis.analysis?.category?.category; if (category && this.agentSpecializations[category]) { requirements.specialization = category; requirements.skills = this.agentSpecializations[category]; } // Adjust experience level based on complexity const complexityScore = conversationAnalysis.analysis?.complexityScore || 0; if (complexityScore >= 0.8) { requirements.experience = 'senior'; } else if (complexityScore <= 0.3) { requirements.experience = 'junior'; } // Add urgency-based requirements if (sessionAnalysis.urgencyLevel === 'critical') { requirements.experience = 'senior'; requirements.availability = 'immediate'; requirements.skills.push('emergency_handling'); } // Add user-specific requirements try { const user = await User.findById(callSession.userId); if (user?.preferences?.language) { requirements.language = user.preferences.language; } // VIP customer handling if (user?.profile?.customerType === 'vip') { requirements.experience = 'senior'; requirements.skills.push('vip_handling'); } } catch (error) { logger.warn('Failed to get user-specific requirements:', error); } return requirements; } /** * Find best available agent based on requirements */ async findBestAvailableAgent(requirements, callSession) { try { const query = { role: 'agent', status: 'active', 'agentProfile.availability': 'available' }; // Add specialization filter if (requirements?.specialization) { query['agentProfile.specialization'] = requirements.specialization; } // Add experience filter if (requirements?.experience) { query['agentProfile.experience'] = { $gte: this.getExperienceLevel(requirements.experience) }; } const availableAgents = await User.find(query) .sort({ 'agentProfile.lastCallEndTime': 1 }) // Prioritize agents who finished calls earlier .limit(10); if (availableAgents.length === 0) { return null; } // Score agents based on requirements match const scoredAgents = availableAgents.map(agent => ({ agent, score: this.calculateAgentScore(agent, requirements, callSession) })); // Return best matching agent scoredAgents.sort((a, b) => b.score - a.score); return scoredAgents[0].agent; } catch (error) { logger.error('Failed to find available agent:', error); return null; } } /** * Prepare comprehensive context for agent */ async prepareAgentContext(callSession, escalationReason, agent) { try { // Get user information const user = await User.findById(callSession.userId); // Get conversation history const conversationHistory = callSession.conversationHistory || []; // Get AI suggestions that were attempted const aiSuggestions = await this.aiSuggestionEngine.provideLiveSuggestions( callSession, conversationHistory[conversationHistory.length - 1]?.content || '', conversationHistory ); return { customer: { id: user._id, name: `${user.profile?.firstName || ''} ${user.profile?.lastName || ''}`.trim(), email: user.email, phone: user.profile?.phoneNumber, customerType: user.profile?.customerType || 'standard', language: user.preferences?.language || 'fr' }, callSession: { id: callSession.callId, urgencyLevel: callSession.urgencyLevel, description: callSession.description, duration: Date.now() - new Date(callSession.createdAt).getTime(), status: callSession.status }, conversationHistory: conversationHistory.slice(-20), // Last 20 messages escalation: { reason: escalationReason, aiAttempts: callSession.aiAttempts || 0, previousSolutions: aiSuggestions.suggestions || [] }, agent: { id: agent._id, name: `${agent.profile.firstName} ${agent.profile.lastName}`, specialization: agent.agentProfile?.specialization } }; } catch (error) { logger.error('Failed to prepare agent context:', error); throw error; } } /** * Create comprehensive agent briefing */ async createAgentBriefing(callSession, agentContext, escalationReason) { const briefing = { summary: this.generateCallSummary(agentContext), customerProfile: this.generateCustomerProfile(agentContext.customer), issueAnalysis: this.generateIssueAnalysis(agentContext), previousAttempts: this.generatePreviousAttempts(agentContext.escalation), recommendations: this.generateAgentRecommendations(agentContext), urgencyIndicators: this.generateUrgencyIndicators(agentContext), contextualInformation: this.generateContextualInformation(agentContext) }; return briefing; } /** * Helper methods for briefing generation */ generateCallSummary(context) { const duration = Math.round(context.callSession.duration / 60000); // minutes return `Appel ${context.callSession.urgencyLevel} de ${context.customer.name} (${duration} min). ${context.callSession.description}`; } generateCustomerProfile(customer) { return { name: customer.name, type: customer.customerType, language: customer.language, contactInfo: { email: customer.email, phone: customer.phone } }; } generateIssueAnalysis(context) { const lastMessage = context.conversationHistory[context.conversationHistory.length - 1]; return { primaryIssue: context.callSession.description, lastUserMessage: lastMessage?.content || 'N/A', conversationLength: context.conversationHistory.length, estimatedComplexity: context.callSession.urgencyLevel }; } generatePreviousAttempts(escalation) { return { aiAttempts: escalation.aiAttempts, suggestedSolutions: escalation.previousSolutions.map(solution => ({ title: solution.title, tried: true, result: 'unsuccessful' })) }; } generateAgentRecommendations(context) { const recommendations = []; if (context.callSession.urgencyLevel === 'critical') { recommendations.push('Traiter en priorité absolue'); } if (context.customer.customerType === 'vip') { recommendations.push('Client VIP - Service premium requis'); } if (context.escalation.aiAttempts >= 3) { recommendations.push('Solutions automatiques épuisées - Approche manuelle nécessaire'); } return recommendations; } generateUrgencyIndicators(context) { return { level: context.callSession.urgencyLevel, duration: context.callSession.duration, escalationReason: context.escalation.reason, customerFrustration: context.escalation.aiAttempts >= 2 ? 'high' : 'medium' }; } generateContextualInformation(context) { return { timeOfDay: new Date().getHours(), dayOfWeek: new Date().getDay(), customerTimezone: 'Europe/Paris', preferredLanguage: context.customer.language }; } /** * Helper methods */ getExperienceLevel(experience) { const levels = { junior: 1, intermediate: 2, senior: 3 }; return levels[experience] || 2; } calculateAgentScore(agent, requirements, callSession) { let score = 0; // Specialization match if (agent.agentProfile?.specialization === requirements.specialization) { score += 0.4; } // Experience match const agentExp = this.getExperienceLevel(agent.agentProfile?.experience); const reqExp = this.getExperienceLevel(requirements.experience); if (agentExp >= reqExp) { score += 0.3; } // Skills match const agentSkills = agent.agentProfile?.skills || []; const matchingSkills = requirements.skills?.filter(skill => agentSkills.includes(skill) ).length || 0; score += (matchingSkills / (requirements.skills?.length || 1)) * 0.2; // Availability bonus if (agent.agentProfile?.availability === 'available') { score += 0.1; } return score; } calculateEstimatedResponseTime(agent) { // Base response time on agent experience and current workload const baseTime = 30; // 30 seconds base const experienceMultiplier = { junior: 1.5, intermediate: 1.0, senior: 0.8 }; const multiplier = experienceMultiplier[agent.agentProfile?.experience] || 1.0; return Math.round(baseTime * multiplier); } async addToPriorityQueue(callSession, escalationReason) { // Implementation for adding to priority queue when no agents available logger.info(`Adding call to priority queue`, { callId: callSession.callId, reason: escalationReason }); return { success: true, queuePosition: 1, estimatedWaitTime: 120000, // 2 minutes priority: 'high' }; } async initiateAgentTransfer(callSession, agent, briefing) { // Implementation for initiating the actual transfer logger.info(`Initiating agent transfer`, { callId: callSession.callId, agentId: agent._id }); return { transferId: `transfer_${Date.now()}`, status: 'initiated', transferredAt: new Date() }; } async updateCallSessionForTransfer(callSession, agent, transferResult) { // Update call session with transfer information try { if (callSession.emergencyCallId) { await EmergencyCall.findOneAndUpdate( { emergencyCallId: callSession.emergencyCallId }, { status: 'connected_to_agent', agentInfo: { agentId: agent._id, agentName: `${agent.profile.firstName} ${agent.profile.lastName}`, connectedAt: new Date() }, transferInfo: transferResult } ); } } catch (error) { logger.error('Failed to update call session for transfer:', error); } } } module.exports = SmartEscalationService;