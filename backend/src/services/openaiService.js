const axios = require('axios'); const logger = require('../config/logger'); class OpenAIService { constructor() { this.apiKey = process.env.OPENAI_API_KEY; this.apiUrl = 'https://api.openai.com/v1/chat/completions'; } async generateResponse(context, userMessage) { try { logger.debug('Generating AI response for context:', context); const systemPrompt = `Tu es un assistant virtuel de Free Mobile. Tu dois aider les clients avec leurs questions sur: - Forfaits et options - Facturation - Support technique - Gestion de compte Sois professionnel, concis et empathique. Utilise le tutoiement comme Free Mobile.`; const response = await axios.post( this.apiUrl, { model: 'gpt-3.5-turbo', messages: [ { role: 'system', content: systemPrompt }, ...context.previousMessages || [], { role: 'user', content: userMessage }, ], temperature: 0.7, max_tokens: 200, }, { headers: { 'Authorization': `Bearer ${this.apiKey}`, 'Content-Type': 'application/json', }, } ); return response.data.choices[0].message.content; } catch (error) { logger.error('OpenAI API error:', error); return null; } } async summarizeConversation(messages) { try { const conversation = messages.map(msg => `${msg.sender}: ${msg.content.text}` ).join('\n'); const response = await axios.post( this.apiUrl, { model: 'gpt-3.5-turbo', messages: [ { role: 'system', content: 'Résume cette conversation en quelques points clés en français.', }, { role: 'user', content: conversation, }, ], temperature: 0.3, max_tokens: 150, }, { headers: { 'Authorization': `Bearer ${this.apiKey}`, 'Content-Type': 'application/json', }, } ); return response.data.choices[0].message.content; } catch (error) { logger.error('Summarization error:', error); return null; } } } module.exports = new OpenAIService();