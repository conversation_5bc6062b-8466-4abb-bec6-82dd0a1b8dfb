/** * ============================================= * WEBSOCKET MANAGER * Centralized WebSocket management and coordination * Extends existing Socket.IO infrastructure * ============================================= */ const { Server } = require('socket.io'); const logger = require('../utils/logger'); const SimulationNamespace = require('./simulationNamespace'); const PredictiveNamespace = require('./predictiveNamespace'); const EnhancedAINamespace = require('./enhancedAINamespace'); const SupportTicketNamespace = require('./supportTicketNamespace'); const ChatNamespace = require('./chatNamespace'); const AnalyticsNamespace = require('./analyticsNamespace'); class WebSocketManager { constructor(server) { this.server = server; this.io = null; this.namespaces = {}; this.connectionPool = new Map(); // Track all connections this.metrics = { totalConnections: 0, activeConnections: 0, messagesSent: 0, messagesReceived: 0, errors: 0 }; this.initialize(); } initialize() { try { // Initialize Socket.IO server this.io = new Server(this.server, { cors: { origin: process.env.FRONTEND_URL || "http://localhost:3001", methods: ["GET", "POST"], credentials: true }, transports: ['websocket', 'polling'], pingTimeout: 60000, pingInterval: 25000, maxHttpBufferSize: 1e6, // 1MB allowEIO3: true }); // Initialize namespaces this.initializeNamespaces(); // Setup global event handlers this.setupGlobalHandlers(); // Start monitoring this.startMonitoring(); logger.info('WebSocket Manager initialized successfully'); } catch (error) { logger.error('Error initializing WebSocket Manager:', error); throw error; } } initializeNamespaces() { try { // Initialize simulation namespace this.namespaces.simulation = new SimulationNamespace(this.io); // Initialize predictive analytics namespace this.namespaces.predictive = new PredictiveNamespace(this.io); // Initialize enhanced AI namespace this.namespaces.enhancedAI = new EnhancedAINamespace(this.io); // Initialize support ticket namespace this.namespaces.support = new SupportTicketNamespace(this.io); // Initialize chat namespace this.namespaces.chat = new ChatNamespace(this.io); // Initialize analytics namespace this.namespaces.analytics = new AnalyticsNamespace(this.io); logger.info('All WebSocket namespaces initialized'); } catch (error) { logger.error('Error initializing namespaces:', error); throw error; } } setupGlobalHandlers() { // Global connection handler this.io.on('connection', (socket) => { this.metrics.totalConnections++; this.metrics.activeConnections++; // Track connection this.connectionPool.set(socket.id, { socket, connectedAt: new Date(), namespace: 'main', userId: null, lastActivity: new Date() }); logger.info(`Global client connected: ${socket.id} (Total: ${this.metrics.activeConnections})`); // Handle authentication for main namespace socket.on('authenticate', async (data) => { try { const { token, userId, role } = data; // Verify token const isValid = await this.verifyToken(token); if (isValid) { const connection = this.connectionPool.get(socket.id); if (connection) { connection.userId = userId; connection.role = role; connection.authenticated = true; } socket.emit('authenticated', { success: true, available_namespaces: this.getAvailableNamespaces(role) }); logger.info(`User ${userId} authenticated on main namespace`); } else { socket.emit('authentication_failed', { error: 'Invalid token' }); } } catch (error) { logger.error('Global authentication error:', error); socket.emit('authentication_failed', { error: 'Authentication failed' }); } }); // Handle namespace discovery socket.on('discover_namespaces', (data) => { const connection = this.connectionPool.get(socket.id); const role = connection?.role || 'guest'; socket.emit('namespaces_discovered', { available_namespaces: this.getAvailableNamespaces(role), namespace_info: this.getNamespaceInfo() }); }); // Handle health check socket.on('health_check', () => { socket.emit('health_response', { status: 'healthy', timestamp: new Date().toISOString(), server_time: Date.now() }); }); // Track activity socket.onAny(() => { this.metrics.messagesReceived++; const connection = this.connectionPool.get(socket.id); if (connection) { connection.lastActivity = new Date(); } }); // Handle disconnection socket.on('disconnect', (reason) => { this.metrics.activeConnections--; this.connectionPool.delete(socket.id); logger.info(`Global client disconnected: ${socket.id}, reason: ${reason} (Remaining: ${this.metrics.activeConnections})`); }); // Error handling socket.on('error', (error) => { this.metrics.errors++; logger.error(`Global socket error for ${socket.id}:`, error); }); }); // Global error handler this.io.engine.on('connection_error', (err) => { this.metrics.errors++; logger.error('Socket.IO connection error:', err); }); } startMonitoring() { // Monitor connections every 30 seconds setInterval(() => { this.performHealthCheck(); }, 30 * 1000); // Clean up stale connections every 5 minutes setInterval(() => { this.cleanupStaleConnections(); }, 5 * 60 * 1000); // Log metrics every 10 minutes setInterval(() => { this.logMetrics(); }, 10 * 60 * 1000); } performHealthCheck() { try { const health = this.getSystemHealth(); // Broadcast health status to monitoring clients this.io.emit('system_health', health); // Log warnings if needed if (health.overall_status !== 'healthy') { logger.warn('WebSocket system health degraded:', health); } } catch (error) { logger.error('Error performing health check:', error); } } cleanupStaleConnections() { try { const staleThreshold = 10 * 60 * 1000; // 10 minutes const now = new Date(); let cleanedCount = 0; for (const [socketId, connection] of this.connectionPool) { const timeSinceActivity = now - connection.lastActivity; if (timeSinceActivity > staleThreshold) { connection.socket.disconnect(true); this.connectionPool.delete(socketId); cleanedCount++; } } if (cleanedCount > 0) { logger.info(`Cleaned up ${cleanedCount} stale connections`); } } catch (error) { logger.error('Error cleaning up stale connections:', error); } } logMetrics() { logger.info('WebSocket Metrics:', { totalConnections: this.metrics.totalConnections, activeConnections: this.metrics.activeConnections, messagesSent: this.metrics.messagesSent, messagesReceived: this.metrics.messagesReceived, errors: this.metrics.errors, namespaceStats: this.getNamespaceStatistics() }); } getSystemHealth() { const namespaceHealth = {}; let unhealthyCount = 0; // Check each namespace health for (const [name, namespace] of Object.entries(this.namespaces)) { const health = namespace.getNamespaceHealth(); namespaceHealth[name] = health; if (health.status !== 'healthy') { unhealthyCount++; } } // Determine overall status let overallStatus = 'healthy'; if (unhealthyCount > 0) { overallStatus = unhealthyCount >= Object.keys(this.namespaces).length / 2 ? 'unhealthy' : 'degraded'; } return { overall_status: overallStatus, active_connections: this.metrics.activeConnections, total_connections: this.metrics.totalConnections, error_rate: this.metrics.errors / Math.max(this.metrics.messagesReceived, 1), namespaces: namespaceHealth, uptime: process.uptime(), memory_usage: process.memoryUsage(), timestamp: new Date().toISOString() }; } getNamespaceStatistics() { const stats = {}; for (const [name, namespace] of Object.entries(this.namespaces)) { if (typeof namespace.getStatistics === 'function') { stats[name] = namespace.getStatistics(); } } return stats; } getAvailableNamespaces(role) { const namespacePermissions = { admin: ['simulation', 'predictive', 'enhanced-ai'], supervisor: ['simulation', 'predictive', 'enhanced-ai'], agent: ['simulation', 'enhanced-ai'], analyst: ['predictive'], guest: [] }; return namespacePermissions[role] || []; } getNamespaceInfo() { return { simulation: { path: '/simulation', description: 'Agent training simulations with real-time AI feedback', features: ['real_time_coaching', 'performance_tracking', 'scenario_management'] }, predictive: { path: '/predictive', description: 'ML-powered predictions and anomaly alerts', features: ['churn_prediction', 'demand_forecasting', 'anomaly_detection'] }, 'enhanced-ai': { path: '/enhanced-ai', description: 'Contextual AI suggestions and sentiment analysis', features: ['contextual_suggestions', 'sentiment_analysis', 'escalation_detection'] } }; } async verifyToken(token) { try { // Integrate with existing authentication system // This would typically verify JWT token return true; // Simplified for now } catch (error) { logger.error('Token verification error:', error); return false; } } // Public methods for external use broadcastToNamespace(namespaceName, event, data) { if (this.namespaces[namespaceName]) { this.io.of(`/${namespaceName}`).emit(event, data); this.metrics.messagesSent++; } } broadcastToAll(event, data) { this.io.emit(event, data); this.metrics.messagesSent++; } getConnectionCount() { return this.metrics.activeConnections; } getMetrics() { return { ...this.metrics, namespaces: this.getNamespaceStatistics() }; } // Graceful shutdown async shutdown() { try { logger.info('Shutting down WebSocket Manager...'); // Notify all clients about shutdown this.io.emit('server_shutdown', { message: 'Server is shutting down', timestamp: new Date().toISOString() }); // Wait a bit for messages to be sent await new Promise(resolve => setTimeout(resolve, 1000)); // Close all connections this.io.close(); logger.info('WebSocket Manager shutdown complete'); } catch (error) { logger.error('Error during WebSocket Manager shutdown:', error); } } // Connection management disconnectUser(userId) { for (const [socketId, connection] of this.connectionPool) { if (connection.userId === userId) { connection.socket.disconnect(true); this.connectionPool.delete(socketId); } } } getUserConnections(userId) { const userConnections = []; for (const [socketId, connection] of this.connectionPool) { if (connection.userId === userId) { userConnections.push({ socketId, namespace: connection.namespace, connectedAt: connection.connectedAt, lastActivity: connection.lastActivity }); } } return userConnections; } // Rate limiting isRateLimited(socketId) { // Implement rate limiting logic return false; // Simplified for now } // Security validateOrigin(origin) { const allowedOrigins = [ process.env.FRONTEND_URL || 'http://localhost:3001', 'http://localhost:3000', // Development 'https://chatbot.freemobile.fr' // Production ]; return allowedOrigins.includes(origin); } } module.exports = WebSocketManager;