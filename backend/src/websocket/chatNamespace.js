const logger = require('../utils/logger'); const Conversation = require('../models/Conversation'); const Message = require('../models/Message'); const User = require('../models/User'); const nlpService = require('../services/nlpService'); const openaiService = require('../services/openaiService'); const intelligentModeService = require('../services/intelligentModeService'); class ChatNamespace { constructor(io) { this.io = io; this.namespace = io.of('/chat'); this.connectedUsers = new Map(); // userId -> socket mapping this.activeConversations = new Map(); // conversationId -> participants this.typingUsers = new Map(); // conversationId -> Set of typing users this.setupEventHandlers(); logger.info('Chat WebSocket namespace initialized'); } setupEventHandlers() { this.namespace.on('connection', (socket) => { logger.info(`Chat client connected: ${socket.id}`); // Handle authentication socket.on('authenticate', async (data) => { try { const { token, userId } = data; // Verify token and get user (simplified for demo) const user = await User.findById(userId); if (!user) { socket.emit('auth_error', { message: 'User not found' }); return; } socket.userId = userId; socket.user = user; // Store socket reference this.connectedUsers.set(userId, socket); // Join user to their personal room socket.join(`user_${userId}`); socket.emit('authenticated', { success: true, userId, user: { id: user._id, email: user.email, profile: user.profile, role: user.role } }); logger.info(`Chat user authenticated: ${userId}`); } catch (error) { logger.error('Chat authentication error:', error); socket.emit('auth_error', { message: 'Authentication failed' }); } }); // Handle joining conversation socket.on('join_conversation', async (data) => { try { const { conversationId } = data; if (!socket.userId) { socket.emit('error', { message: 'Not authenticated' }); return; } const conversation = await Conversation.findById(conversationId); if (!conversation) { socket.emit('error', { message: 'Conversation not found' }); return; } // Check if user owns the conversation or is an agent/admin const isOwner = conversation.userId.toString() === socket.userId; const isAgent = conversation.agentId && conversation.agentId.toString() === socket.userId; const isStaff = ['admin', 'agent'].includes(socket.user.role); if (!isOwner && !isAgent && !isStaff) { socket.emit('error', { message: 'Access denied' }); return; } socket.join(`conversation_${conversationId}`); socket.currentConversation = conversationId; // Track active participants if (!this.activeConversations.has(conversationId)) { this.activeConversations.set(conversationId, new Set()); } this.activeConversations.get(conversationId).add(socket.userId); socket.emit('conversation_joined', { conversationId, status: conversation.status, participants: Array.from(this.activeConversations.get(conversationId)) }); // Notify others in the conversation socket.to(`conversation_${conversationId}`).emit('user_joined', { userId: socket.userId, user: { id: socket.user._id, email: socket.user.email, profile: socket.user.profile, role: socket.user.role }, timestamp: new Date() }); } catch (error) { logger.error('Error joining conversation:', error); socket.emit('error', { message: 'Failed to join conversation' }); } }); // Handle leaving conversation socket.on('leave_conversation', (data) => { const { conversationId } = data; if (socket.currentConversation === conversationId) { socket.leave(`conversation_${conversationId}`); // Remove from active participants if (this.activeConversations.has(conversationId)) { this.activeConversations.get(conversationId).delete(socket.userId); if (this.activeConversations.get(conversationId).size === 0) { this.activeConversations.delete(conversationId); } } // Stop typing if user was typing this.handleTypingStop(conversationId, socket.userId); socket.currentConversation = null; socket.to(`conversation_${conversationId}`).emit('user_left', { userId: socket.userId, timestamp: new Date() }); } }); // Handle real-time message sending socket.on('send_message', async (data) => { try { const { conversationId, message, type = 'text' } = data; if (!socket.userId || socket.currentConversation !== conversationId) { socket.emit('error', { message: 'Not in conversation' }); return; } // Stop typing indicator this.handleTypingStop(conversationId, socket.userId); // Save user message const userMessage = new Message({ conversationId, sender: 'user', content: { text: message, type: type, }, metadata: { realTime: true, socketId: socket.id } }); await userMessage.save(); // Broadcast user message immediately this.namespace.to(`conversation_${conversationId}`).emit('message_received', { message: userMessage.toJSON(), timestamp: new Date() }); // Process AI response asynchronously this.processAIResponse(conversationId, message, socket.user); } catch (error) { logger.error('Error sending real-time message:', error); socket.emit('error', { message: 'Failed to send message' }); } }); // Handle typing indicators socket.on('typing_start', (data) => { const { conversationId } = data; if (socket.currentConversation === conversationId) { this.handleTypingStart(conversationId, socket.userId); } }); socket.on('typing_stop', (data) => { const { conversationId } = data; if (socket.currentConversation === conversationId) { this.handleTypingStop(conversationId, socket.userId); } }); // Handle agent takeover socket.on('agent_takeover', async (data) => { try { const { conversationId } = data; if (!['admin', 'agent'].includes(socket.user.role)) { socket.emit('error', { message: 'Access denied' }); return; } const conversation = await Conversation.findById(conversationId); if (!conversation) { socket.emit('error', { message: 'Conversation not found' }); return; } conversation.agentId = socket.userId; conversation.status = 'escalated'; await conversation.save(); // Notify all participants this.namespace.to(`conversation_${conversationId}`).emit('agent_joined', { agentId: socket.userId, agent: { id: socket.user._id, email: socket.user.email, profile: socket.user.profile }, timestamp: new Date() }); // Send system message const systemMessage = new Message({ conversationId, sender: 'bot', content: { text: `Un agent ${socket.user.profile?.firstName || 'support'} a rejoint la conversation.`, type: 'system' } }); await systemMessage.save(); this.namespace.to(`conversation_${conversationId}`).emit('message_received', { message: systemMessage.toJSON(), timestamp: new Date() }); } catch (error) { logger.error('Error in agent takeover:', error); socket.emit('error', { message: 'Failed to take over conversation' }); } }); // Handle disconnection socket.on('disconnect', () => { logger.info(`Chat client disconnected: ${socket.id}`); if (socket.userId) { this.connectedUsers.delete(socket.userId); // Clean up conversation participation if (socket.currentConversation) { const conversationId = socket.currentConversation; // Remove from active participants if (this.activeConversations.has(conversationId)) { this.activeConversations.get(conversationId).delete(socket.userId); if (this.activeConversations.get(conversationId).size === 0) { this.activeConversations.delete(conversationId); } } // Stop typing this.handleTypingStop(conversationId, socket.userId); // Notify others socket.to(`conversation_${conversationId}`).emit('user_left', { userId: socket.userId, timestamp: new Date() }); } } }); }); } // Handle typing start handleTypingStart(conversationId, userId) { if (!this.typingUsers.has(conversationId)) { this.typingUsers.set(conversationId, new Set()); } const typingSet = this.typingUsers.get(conversationId); if (!typingSet.has(userId)) { typingSet.add(userId); this.namespace.to(`conversation_${conversationId}`).emit('user_typing', { userId, typingUsers: Array.from(typingSet), timestamp: new Date() }); } } // Handle typing stop handleTypingStop(conversationId, userId) { if (this.typingUsers.has(conversationId)) { const typingSet = this.typingUsers.get(conversationId); if (typingSet.has(userId)) { typingSet.delete(userId); this.namespace.to(`conversation_${conversationId}`).emit('user_stopped_typing', { userId, typingUsers: Array.from(typingSet), timestamp: new Date() }); if (typingSet.size === 0) { this.typingUsers.delete(conversationId); } } } } // Process AI response asynchronously async processAIResponse(conversationId, message, user) { try { // Use intelligent mode service for better responses const intelligentResponse = await intelligentModeService.processMessage( conversationId, message, { userId: user._id, profile: user.profile, preferences: user.preferences } ); let response = intelligentResponse.response; // Fallback to NLP/OpenAI if intelligent mode fails if (!response) { const nlpResult = await nlpService.parseMessage(message, user._id); if (nlpResult.intent?.confidence > 0.7) { const rasaResponses = await nlpService.getResponse( message, user._id.toString(), { conversationId } ); response = rasaResponses[0]?.text || "Je n'ai pas compris ta demande."; } else { const previousMessages = await Message.find({ conversationId }) .sort({ timestamp: -1 }) .limit(5) .lean(); response = await openaiService.generateResponse( { previousMessages: previousMessages.slice(0, 5) }, message ); } } // Create bot message const botMessage = new Message({ conversationId, sender: 'bot', content: { text: response || "Désolé, je n'ai pas bien compris. Peux-tu reformuler ta question?", type: 'text', buttons: intelligentResponse?.buttons || [], quickReplies: intelligentResponse?.quickReplies || [] }, intent: intelligentResponse?.intent, entities: intelligentResponse?.entities || [], metadata: { mode: intelligentResponse?.mode || 'fallback', processingTime: Date.now() - Date.now(), nlpProvider: intelligentResponse?.mode || 'openai', realTime: true } }); await botMessage.save(); // Broadcast bot response this.namespace.to(`conversation_${conversationId}`).emit('message_received', { message: botMessage.toJSON(), intelligentFeatures: { mode: intelligentResponse?.mode, hasButtons: (intelligentResponse?.buttons || []).length > 0, hasQuickReplies: (intelligentResponse?.quickReplies || []).length > 0, hasProactiveInsights: !!intelligentResponse?.proactiveInsights, sentiment: intelligentResponse?.sentiment }, timestamp: new Date() }); } catch (error) { logger.error('Error processing AI response:', error); // Send error message const errorMessage = new Message({ conversationId, sender: 'bot', content: { text: "Désolé, une erreur s'est produite. Veuillez réessayer.", type: 'text' }, metadata: { error: true, realTime: true } }); await errorMessage.save(); this.namespace.to(`conversation_${conversationId}`).emit('message_received', { message: errorMessage.toJSON(), timestamp: new Date() }); } } // Get connected users count getConnectedUsersCount() { return this.connectedUsers.size; } // Get active conversations count getActiveConversationsCount() { return this.activeConversations.size; } // Broadcast message to conversation broadcastToConversation(conversationId, event, data) { this.namespace.to(`conversation_${conversationId}`).emit(event, { ...data, timestamp: new Date() }); } } module.exports = ChatNamespace;