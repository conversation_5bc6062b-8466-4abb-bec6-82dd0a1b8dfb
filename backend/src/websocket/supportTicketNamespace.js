const logger = require('../utils/logger'); const SupportTicket = require('../models/SupportTicket'); const User = require('../models/User'); class SupportTicketNamespace { constructor(io) { this.io = io; this.namespace = io.of('/support'); this.connectedUsers = new Map(); // userId -> socket mapping this.agentSockets = new Map(); // agentId -> socket mapping this.adminSockets = new Set(); // admin sockets this.setupEventHandlers(); this.startMetricsUpdates(); logger.info('Support Ticket WebSocket namespace initialized'); } setupEventHandlers() { this.namespace.on('connection', (socket) => { logger.info(`Support client connected: ${socket.id}`); // Handle authentication socket.on('authenticate', async (data) => { try { const { token, userId, role } = data; // Verify token and get user (simplified for demo) const user = await User.findById(userId); if (!user) { socket.emit('auth_error', { message: 'User not found' }); return; } socket.userId = userId; socket.userRole = role; // Store socket reference this.connectedUsers.set(userId, socket); if (role === 'agent') { this.agentSockets.set(userId, socket); } else if (role === 'admin') { this.adminSockets.add(socket); } // Join user to their personal room socket.join(`user_${userId}`); // Join agents/admins to global rooms if (['agent', 'admin'].includes(role)) { socket.join('agents'); socket.join('support_staff'); } socket.emit('authenticated', { success: true, userId, role, rooms: Array.from(socket.rooms) }); logger.info(`Support user authenticated: ${userId} (${role})`); } catch (error) { logger.error('Support authentication error:', error); socket.emit('auth_error', { message: 'Authentication failed' }); } }); // Handle joining ticket room socket.on('join_ticket', async (data) => { try { const { ticketId } = data; if (!socket.userId) { socket.emit('error', { message: 'Not authenticated' }); return; } const ticket = await SupportTicket.findById(ticketId); if (!ticket) { socket.emit('error', { message: 'Ticket not found' }); return; } // Check permissions const isOwner = ticket.userId.toString() === socket.userId; const isAssigned = ticket.assignedTo && ticket.assignedTo.toString() === socket.userId; const isStaff = ['admin', 'agent'].includes(socket.userRole); if (!isOwner && !isAssigned && !isStaff) { socket.emit('error', { message: 'Access denied' }); return; } socket.join(`ticket_${ticketId}`); socket.currentTicket = ticketId; socket.emit('ticket_joined', { ticketId, ticketNumber: ticket.ticketNumber, status: ticket.status }); // Notify others in the ticket room socket.to(`ticket_${ticketId}`).emit('user_joined_ticket', { userId: socket.userId, userRole: socket.userRole, timestamp: new Date() }); } catch (error) { logger.error('Error joining ticket room:', error); socket.emit('error', { message: 'Failed to join ticket' }); } }); // Handle leaving ticket room socket.on('leave_ticket', (data) => { const { ticketId } = data; if (socket.currentTicket === ticketId) { socket.leave(`ticket_${ticketId}`); socket.currentTicket = null; socket.to(`ticket_${ticketId}`).emit('user_left_ticket', { userId: socket.userId, userRole: socket.userRole, timestamp: new Date() }); } }); // Handle typing indicators socket.on('typing_start', (data) => { const { ticketId } = data; if (socket.currentTicket === ticketId) { socket.to(`ticket_${ticketId}`).emit('user_typing', { userId: socket.userId, userRole: socket.userRole, timestamp: new Date() }); } }); socket.on('typing_stop', (data) => { const { ticketId } = data; if (socket.currentTicket === ticketId) { socket.to(`ticket_${ticketId}`).emit('user_stopped_typing', { userId: socket.userId, timestamp: new Date() }); } }); // Handle agent status updates socket.on('agent_status_update', (data) => { if (socket.userRole === 'agent') { const { status, availableForAssignment } = data; socket.to('support_staff').emit('agent_status_changed', { agentId: socket.userId, status, availableForAssignment, timestamp: new Date() }); } }); // Handle disconnection socket.on('disconnect', () => { logger.info(`Support client disconnected: ${socket.id}`); if (socket.userId) { this.connectedUsers.delete(socket.userId); if (socket.userRole === 'agent') { this.agentSockets.delete(socket.userId); } else if (socket.userRole === 'admin') { this.adminSockets.delete(socket); } // Notify ticket room if user was in one if (socket.currentTicket) { socket.to(`ticket_${socket.currentTicket}`).emit('user_left_ticket', { userId: socket.userId, userRole: socket.userRole, timestamp: new Date() }); } // Notify support staff of agent disconnection if (socket.userRole === 'agent') { socket.to('support_staff').emit('agent_disconnected', { agentId: socket.userId, timestamp: new Date() }); } } }); }); } // Broadcast ticket creation to support staff broadcastTicketCreated(ticket) { this.namespace.to('support_staff').emit('ticket_created', { ticket: ticket.toJSON(), timestamp: new Date() }); } // Broadcast ticket update to relevant users broadcastTicketUpdated(ticketId, updates, updatedBy) { this.namespace.to(`ticket_${ticketId}`).emit('ticket_updated', { ticketId, updates, updatedBy, timestamp: new Date() }); // Also notify support staff this.namespace.to('support_staff').emit('ticket_status_changed', { ticketId, updates, timestamp: new Date() }); } // Broadcast new message to ticket participants broadcastTicketMessage(ticketId, message) { this.namespace.to(`ticket_${ticketId}`).emit('ticket_message', { ticketId, message, timestamp: new Date() }); } // Send notification to specific user sendUserNotification(userId, notification) { this.namespace.to(`user_${userId}`).emit('notification', { ...notification, timestamp: new Date() }); } // Broadcast metrics update to admins broadcastMetricsUpdate(metrics) { this.adminSockets.forEach(socket => { socket.emit('metrics_update', { metrics, timestamp: new Date() }); }); } // Start periodic metrics updates startMetricsUpdates() { setInterval(async () => { try { if (this.adminSockets.size > 0) { const metrics = await this.getRealtimeMetrics(); this.broadcastMetricsUpdate(metrics); } } catch (error) { logger.error('Error updating support metrics:', error); } }, 30000); // Update every 30 seconds } // Get real-time support metrics async getRealtimeMetrics() { try { const now = new Date(); const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()); const [ totalOpen, totalToday, highPriority, overdue, avgResponseTime ] = await Promise.all([ SupportTicket.countDocuments({ status: { $in: ['open', 'in_progress'] } }), SupportTicket.countDocuments({ createdAt: { $gte: today } }), SupportTicket.countDocuments({ status: { $in: ['open', 'in_progress'] }, priority: { $in: ['high', 'urgent'] } }), SupportTicket.countDocuments({ status: { $in: ['open', 'in_progress'] }, 'sla.responseTime.breached': true }), SupportTicket.aggregate([ { $match: { 'sla.responseTime.actual': { $exists: true } } }, { $group: { _id: null, avg: { $avg: '$sla.responseTime.actual' } } } ]) ]); return { totalOpenTickets: totalOpen, ticketsCreatedToday: totalToday, highPriorityTickets: highPriority, overdueTickets: overdue, averageResponseTime: avgResponseTime[0]?.avg || 0, connectedAgents: this.agentSockets.size, connectedUsers: this.connectedUsers.size }; } catch (error) { logger.error('Error calculating support metrics:', error); return {}; } } // Get connected users count getConnectedUsersCount() { return this.connectedUsers.size; } // Get connected agents count getConnectedAgentsCount() { return this.agentSockets.size; } } module.exports = SupportTicketNamespace;