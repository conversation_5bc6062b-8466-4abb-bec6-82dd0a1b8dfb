const jwt = require('jsonwebtoken'); const User = require('../models/User'); const logger = require('../utils/logger'); const analyticsService = require('../services/analyticsService'); class AnalyticsNamespace { constructor(io) { this.namespace = io.of('/analytics'); this.connectedUsers = new Map(); this.adminSockets = new Set(); this.supervisorSockets = new Set(); this.analystSockets = new Set(); this.agentSockets = new Map(); this.setupEventHandlers(); logger.info('Analytics WebSocket namespace initialized'); } setupEventHandlers() { this.namespace.use(this.authenticateSocket.bind(this)); this.namespace.on('connection', (socket) => { this.handleConnection(socket); }); } async authenticateSocket(socket, next) { try { const token = socket.handshake.auth.token || socket.handshake.query.token; if (!token) { return next(new Error('Authentication token required')); } const decoded = jwt.verify(token, process.env.JWT_SECRET); const user = await User.findById(decoded.id).select('-password'); if (!user) { return next(new Error('User not found')); } // Check if user has analytics access if (!['admin', 'supervisor', 'analyst', 'agent'].includes(user.role)) { return next(new Error('Insufficient permissions for analytics')); } socket.user = user; next(); } catch (error) { logger.error('Analytics socket authentication error:', error); next(new Error('Authentication failed')); } } handleConnection(socket) { const user = socket.user; logger.info(`Analytics connection established for user: ${user.email} (${user.role})`); // Store connection by role this.connectedUsers.set(socket.id, { socket, user, connectedAt: new Date() }); // Add to role-specific collections switch (user.role) { case 'admin': this.adminSockets.add(socket); break; case 'supervisor': this.supervisorSockets.add(socket); break; case 'analyst': this.analystSockets.add(socket); break; case 'agent': this.agentSockets.set(socket.id, socket); break; } // Send initial analytics data this.sendInitialData(socket, user); // Set up event handlers this.setupSocketEventHandlers(socket, user); // Handle disconnection socket.on('disconnect', () => { this.handleDisconnection(socket, user); }); } async sendInitialData(socket, user) { try { // Send appropriate data based on user role if (['admin', 'supervisor', 'analyst'].includes(user.role)) { const [dashboardData, realTimeMetrics] = await Promise.all([ analyticsService.getDashboardAnalytics('7d'), analyticsService.getRealTimeMetrics() ]); socket.emit('initial_analytics_data', { dashboard: dashboardData, realTime: realTimeMetrics, permissions: this.getUserPermissions(user.role) }); } else if (user.role === 'agent') { // Send agent-specific analytics const agentPerformance = await analyticsService.getAgentPerformance( analyticsService.getDateRange('30d') ); const agentData = agentPerformance.find( agent => agent._id.toString() === user._id.toString() ); socket.emit('initial_analytics_data', { agentPerformance: agentData, permissions: this.getUserPermissions(user.role) }); } } catch (error) { logger.error('Error sending initial analytics data:', error); socket.emit('analytics_error', { message: 'Failed to load initial analytics data' }); } } setupSocketEventHandlers(socket, user) { // Request dashboard data socket.on('request_dashboard_data', async (params) => { try { if (!['admin', 'supervisor', 'analyst'].includes(user.role)) { socket.emit('analytics_error', { message: 'Insufficient permissions for dashboard data' }); return; } const { timeRange = '30d', metrics = 'all' } = params; const dashboardData = await analyticsService.getDashboardAnalytics(timeRange, { metrics: metrics.split(',') }); socket.emit('dashboard_data', { data: dashboardData, timeRange, requestId: params.requestId }); } catch (error) { logger.error('Error handling dashboard data request:', error); socket.emit('analytics_error', { message: 'Failed to get dashboard data', requestId: params.requestId }); } }); // Request real-time metrics socket.on('request_realtime_metrics', async (params) => { try { if (!['admin', 'supervisor', 'analyst'].includes(user.role)) { socket.emit('analytics_error', { message: 'Insufficient permissions for real-time metrics' }); return; } const realTimeMetrics = await analyticsService.getRealTimeMetrics(); socket.emit('realtime_metrics', { data: realTimeMetrics, requestId: params.requestId }); } catch (error) { logger.error('Error handling real-time metrics request:', error); socket.emit('analytics_error', { message: 'Failed to get real-time metrics', requestId: params.requestId }); } }); // Request agent performance socket.on('request_agent_performance', async (params) => { try { const { agentId, timeRange = '30d' } = params; // Agents can only view their own performance let targetAgentId = agentId; if (user.role === 'agent') { targetAgentId = user._id.toString(); } const dateRange = analyticsService.getDateRange(timeRange); const agentPerformance = await analyticsService.getAgentPerformance(dateRange); let filteredPerformance = agentPerformance; if (targetAgentId) { filteredPerformance = agentPerformance.filter( agent => agent._id.toString() === targetAgentId ); } socket.emit('agent_performance', { data: filteredPerformance, agentId: targetAgentId, timeRange, requestId: params.requestId }); } catch (error) { logger.error('Error handling agent performance request:', error); socket.emit('analytics_error', { message: 'Failed to get agent performance', requestId: params.requestId }); } }); // Subscribe to real-time updates socket.on('subscribe_realtime_updates', (params) => { try { if (!['admin', 'supervisor', 'analyst'].includes(user.role)) { socket.emit('analytics_error', { message: 'Insufficient permissions for real-time updates' }); return; } socket.join('realtime_updates'); socket.emit('subscription_confirmed', { type: 'realtime_updates', requestId: params.requestId }); } catch (error) { logger.error('Error handling real-time updates subscription:', error); socket.emit('analytics_error', { message: 'Failed to subscribe to real-time updates', requestId: params.requestId }); } }); // Unsubscribe from real-time updates socket.on('unsubscribe_realtime_updates', (params) => { socket.leave('realtime_updates'); socket.emit('subscription_cancelled', { type: 'realtime_updates', requestId: params.requestId }); }); // Request custom analytics socket.on('request_custom_analytics', async (params) => { try { if (!['admin', 'supervisor', 'analyst'].includes(user.role)) { socket.emit('analytics_error', { message: 'Insufficient permissions for custom analytics' }); return; } const { query, timeRange, filters } = params; // Process custom analytics request const customData = await this.processCustomAnalyticsRequest(query, timeRange, filters); socket.emit('custom_analytics', { data: customData, query, requestId: params.requestId }); } catch (error) { logger.error('Error handling custom analytics request:', error); socket.emit('analytics_error', { message: 'Failed to process custom analytics request', requestId: params.requestId }); } }); } handleDisconnection(socket, user) { logger.info(`Analytics disconnection for user: ${user.email} (${user.role})`); // Remove from collections this.connectedUsers.delete(socket.id); switch (user.role) { case 'admin': this.adminSockets.delete(socket); break; case 'supervisor': this.supervisorSockets.delete(socket); break; case 'analyst': this.analystSockets.delete(socket); break; case 'agent': this.agentSockets.delete(socket.id); break; } } // Broadcast methods broadcastToRole(role, data) { let sockets; switch (role) { case 'admin': sockets = this.adminSockets; break; case 'supervisor': sockets = this.supervisorSockets; break; case 'analyst': sockets = this.analystSockets; break; case 'agent': sockets = Array.from(this.agentSockets.values()); break; default: return; } if (Array.isArray(sockets)) { sockets.forEach(socket => { if (socket.connected) { socket.emit('analytics_broadcast', data); } }); } else { sockets.forEach(socket => { if (socket.connected) { socket.emit('analytics_broadcast', data); } }); } } broadcastToAll(data) { this.namespace.emit('analytics_broadcast', data); } broadcastToRoom(room, data) { this.namespace.to(room).emit('analytics_broadcast', data); } // Utility methods getUserPermissions(role) { const permissions = { admin: ['dashboard', 'realtime', 'agents', 'customers', 'operations', 'custom'], supervisor: ['dashboard', 'realtime', 'agents', 'customers', 'operations'], analyst: ['dashboard', 'realtime', 'customers', 'custom'], agent: ['personal_performance'] }; return permissions[role] || []; } async processCustomAnalyticsRequest(query, timeRange, filters) { // Simplified custom analytics processing // In a real implementation, this would parse the query and generate appropriate analytics return { query, timeRange, filters, results: [], processedAt: new Date() }; } // Get connection statistics getConnectionStats() { return { total: this.connectedUsers.size, admins: this.adminSockets.size, supervisors: this.supervisorSockets.size, analysts: this.analystSockets.size, agents: this.agentSockets.size }; } // Cleanup method cleanup() { this.connectedUsers.clear(); this.adminSockets.clear(); this.supervisorSockets.clear(); this.analystSockets.clear(); this.agentSockets.clear(); logger.info('Analytics namespace cleaned up'); } } module.exports = AnalyticsNamespace;