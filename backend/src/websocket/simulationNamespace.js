/** * ============================================= * SIMULATION WEBSOCKET NAMESPACE * Real-time simulation updates and AI feedback * Extends existing Socket.IO infrastructure * ============================================= */ const logger = require('../utils/logger'); const SimulationService = require('../services/simulationService'); const AIService = require('../services/aiService'); class SimulationNamespace { constructor(io) { this.io = io; this.simulationService = new SimulationService(); this.aiService = new AIService(); this.namespace = io.of('/simulation'); this.activeSessions = new Map(); // sessionId -> socket mapping this.agentSockets = new Map(); // agentId -> socket mapping this.setupEventHandlers(); logger.info('Simulation WebSocket namespace initialized'); } setupEventHandlers() { this.namespace.on('connection', (socket) => { logger.info(`Simulation client connected: ${socket.id}`); // Authentication socket.on('authenticate', async (data) => { try { const { token, agentId } = data; // Verify token (integrate with existing auth) const isValid = await this.verifyToken(token); if (isValid) { socket.agentId = agentId; socket.authenticated = true; this.agentSockets.set(agentId, socket); socket.emit('authenticated', { success: true }); logger.info(`Agent ${agentId} authenticated for simulation`); } else { socket.emit('authentication_failed', { error: 'Invalid token' }); socket.disconnect(); } } catch (error) { logger.error('Authentication error:', error); socket.emit('authentication_failed', { error: 'Authentication failed' }); socket.disconnect(); } }); // Join simulation session socket.on('join_session', async (data) => { try { if (!socket.authenticated) { socket.emit('error', { message: 'Not authenticated' }); return; } const { sessionId } = data; // Verify session ownership const session = await this.simulationService.getSession(sessionId); if (!session || session.agent_id !== socket.agentId) { socket.emit('error', { message: 'Session not found or access denied' }); return; } // Join session room socket.join(`session_${sessionId}`); socket.sessionId = sessionId; this.activeSessions.set(sessionId, socket); // Send session state const sessionState = await this.getSessionState(sessionId); socket.emit('session_joined', sessionState); logger.info(`Agent ${socket.agentId} joined simulation session ${sessionId}`); } catch (error) { logger.error('Error joining session:', error); socket.emit('error', { message: 'Failed to join session' }); } }); // Handle agent message socket.on('agent_message', async (data) => { try { if (!socket.authenticated || !socket.sessionId) { socket.emit('error', { message: 'Not in a session' }); return; } const { message, timestamp } = data; const sessionId = socket.sessionId; // Process message through simulation service const result = await this.processAgentMessage(sessionId, message, timestamp); // Emit real-time updates this.namespace.to(`session_${sessionId}`).emit('message_processed', { agent_message: result.agentMessage, customer_response: result.customerMessage, performance_metrics: result.performanceMetrics, ai_feedback: result.feedback, suggestions: result.suggestions }); // Send AI coaching if needed if (result.coaching) { socket.emit('ai_coaching', result.coaching); } } catch (error) { logger.error('Error processing agent message:', error); socket.emit('error', { message: 'Failed to process message' }); } }); // Request AI suggestions socket.on('request_suggestions', async (data) => { try { if (!socket.authenticated || !socket.sessionId) { socket.emit('error', { message: 'Not in a session' }); return; } const suggestions = await this.generateRealTimeSuggestions(socket.sessionId, data); socket.emit('suggestions_generated', suggestions); } catch (error) { logger.error('Error generating suggestions:', error); socket.emit('error', { message: 'Failed to generate suggestions' }); } }); // End simulation session socket.on('end_session', async (data) => { try { if (!socket.authenticated || !socket.sessionId) { socket.emit('error', { message: 'Not in a session' }); return; } const { reason = 'completed' } = data; const sessionId = socket.sessionId; // End session through service const finalResults = await this.endSimulationSession(sessionId, reason); // Emit final results this.namespace.to(`session_${sessionId}`).emit('session_ended', finalResults); // Clean up socket.leave(`session_${sessionId}`); this.activeSessions.delete(sessionId); socket.sessionId = null; } catch (error) { logger.error('Error ending session:', error); socket.emit('error', { message: 'Failed to end session' }); } }); // Handle typing indicators socket.on('typing_start', () => { if (socket.sessionId) { socket.to(`session_${socket.sessionId}`).emit('agent_typing', { agentId: socket.agentId }); } }); socket.on('typing_stop', () => { if (socket.sessionId) { socket.to(`session_${socket.sessionId}`).emit('agent_stopped_typing', { agentId: socket.agentId }); } }); // Handle disconnection socket.on('disconnect', () => { logger.info(`Simulation client disconnected: ${socket.id}`); if (socket.agentId) { this.agentSockets.delete(socket.agentId); } if (socket.sessionId) { this.activeSessions.delete(socket.sessionId); // Notify about disconnection this.namespace.to(`session_${socket.sessionId}`).emit('agent_disconnected', { agentId: socket.agentId, timestamp: new Date().toISOString() }); } }); // Error handling socket.on('error', (error) => { logger.error(`Socket error for ${socket.id}:`, error); }); }); } async processAgentMessage(sessionId, message, timestamp) { try { // Add agent message to session const agentMessage = await this.simulationService.addMessage(sessionId, { sender: 'agent', content: message, timestamp: timestamp || new Date().toISOString() }); // Get session context const session = await this.simulationService.getSession(sessionId); // Analyze agent response const responseAnalysis = await this.aiService.analyzeAgentResponse({ message, context: session.context, conversation_history: session.messages, customer_profile: session.customer_profile }); // Generate AI suggestions const suggestions = await this.aiService.generateSuggestions({ agent_message: message, session_context: session, analysis: responseAnalysis }); // Generate customer response const customerResponse = await this.aiService.generateCustomerResponse({ agent_message: message, customer_persona: session.customer_persona, conversation_history: session.messages, scenario: session.scenario }); // Add customer response const customerMessage = await this.simulationService.addMessage(sessionId, { sender: 'customer', content: customerResponse.message, timestamp: new Date().toISOString(), metadata: { sentiment: customerResponse.sentiment, satisfaction: customerResponse.satisfaction, escalation_risk: customerResponse.escalation_risk } }); // Update performance metrics const performanceMetrics = await this.simulationService.updatePerformanceMetrics(sessionId, { response_analysis: responseAnalysis, customer_feedback: customerResponse, response_time: Date.now() - new Date(timestamp).getTime() }); // Generate real-time feedback const feedback = await this.aiService.generateRealTimeFeedback({ agent_performance: responseAnalysis, customer_reaction: customerResponse, session_progress: performanceMetrics }); // Check if coaching is needed let coaching = null; if (responseAnalysis.empathy_score < 60 || customerResponse.escalation_risk > 0.7) { coaching = await this.generateCoaching(responseAnalysis, customerResponse); } return { agentMessage, customerMessage, performanceMetrics, feedback, suggestions, coaching }; } catch (error) { logger.error('Error processing agent message:', error); throw error; } } async generateRealTimeSuggestions(sessionId, context) { try { const session = await this.simulationService.getSession(sessionId); const suggestions = await this.aiService.generateSuggestions({ agent_message: context.currentMessage || '', session_context: session, analysis: context.analysis || {} }); return { suggestions, timestamp: new Date().toISOString(), context: 'real_time_request' }; } catch (error) { logger.error('Error generating real-time suggestions:', error); throw error; } } async endSimulationSession(sessionId, reason) { try { // Calculate final metrics const finalMetrics = await this.simulationService.calculateFinalMetrics(sessionId); // Generate comprehensive feedback const session = await this.simulationService.getSession(sessionId); const comprehensiveFeedback = await this.aiService.generateComprehensiveFeedback({ session, final_metrics: finalMetrics, completion_reason: reason }); // Update agent progress const updatedProgress = await this.simulationService.updateAgentProgress(session.agent_id, { session_completed: true, final_metrics: finalMetrics, scenario_id: session.scenario_id, completion_reason: reason }); // Check for new achievements const newAchievements = await this.simulationService.checkAchievements(session.agent_id, finalMetrics); // End session await this.simulationService.endSession(sessionId, reason, finalMetrics); return { final_metrics: finalMetrics, comprehensive_feedback: comprehensiveFeedback, agent_progress: updatedProgress, new_achievements: newAchievements, session_summary: { duration: finalMetrics.total_duration, messages_exchanged: session.messages.length, completion_reason: reason } }; } catch (error) { logger.error('Error ending simulation session:', error); throw error; } } async generateCoaching(responseAnalysis, customerResponse) { try { const coaching = await this.aiService.generateRealTimeFeedback({ agent_performance: responseAnalysis, customer_reaction: customerResponse, coaching_mode: true }); return { type: 'coaching', urgency: customerResponse.escalation_risk > 0.8 ? 'high' : 'medium', message: coaching.message, suggestions: coaching.suggestions || [], timestamp: new Date().toISOString() }; } catch (error) { logger.error('Error generating coaching:', error); return null; } } async getSessionState(sessionId) { try { const session = await this.simulationService.getSession(sessionId); return { session_id: sessionId, status: session.status, scenario: session.scenario, customer_persona: session.customer_persona, messages: session.messages, performance_metrics: session.performance_metrics, created_at: session.created_at }; } catch (error) { logger.error('Error getting session state:', error); throw error; } } async verifyToken(token) { try { // Integrate with existing authentication system // This would typically verify JWT token return true; // Simplified for now } catch (error) { logger.error('Token verification error:', error); return false; } } // Broadcast methods for external use broadcastToAgent(agentId, event, data) { const socket = this.agentSockets.get(agentId); if (socket) { socket.emit(event, data); } } broadcastToSession(sessionId, event, data) { this.namespace.to(`session_${sessionId}`).emit(event, data); } broadcastToAllSessions(event, data) { this.namespace.emit(event, data); } // Get active sessions count getActiveSessionsCount() { return this.activeSessions.size; } // Get connected agents count getConnectedAgentsCount() { return this.agentSockets.size; } // Health check getNamespaceHealth() { return { namespace: '/simulation', active_sessions: this.activeSessions.size, connected_agents: this.agentSockets.size, status: 'healthy' }; } } module.exports = SimulationNamespace;