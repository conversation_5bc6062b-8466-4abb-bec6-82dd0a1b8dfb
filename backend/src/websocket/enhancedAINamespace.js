/** * ============================================= * [FEATURE] ENHANCED AI WEBSOCKET NAMESPACE * Contextual suggestions and sentiment analysis streams * Real-time AI assistance for customer service agents * ============================================= */ const logger = require('../utils/logger'); const AIService = require('../services/aiService'); const SentimentService = require('../services/sentimentService'); const TemplateService = require('../services/templateService'); class EnhancedAINamespace { constructor(io) { this.io = io; this.aiService = new AIService(); this.sentimentService = new SentimentService(); this.templateService = new TemplateService(); this.namespace = io.of('/enhanced-ai'); this.activeAgents = new Map(); // agentId -> socket mapping this.conversationContexts = new Map(); // conversationId -> context this.suggestionCache = new Map(); // Cache for recent suggestions this.setupEventHandlers(); this.startContextualUpdates(); logger.info('Enhanced AI WebSocket namespace initialized'); } setupEventHandlers() { this.namespace.on('connection', (socket) => { logger.info(`Enhanced AI client connected: ${socket.id}`); // Authentication socket.on('authenticate', async (data) => { try { const { token, agentId, preferences = {} } = data; // Verify token and agent permissions const isValid = await this.verifyToken(token); const agentProfile = await this.aiService.getAgentProfile(agentId); if (isValid && agentProfile) { socket.agentId = agentId; socket.agentProfile = agentProfile; socket.preferences = preferences; socket.authenticated = true; this.activeAgents.set(agentId, socket); // Send initial AI configuration const aiConfig = await this.getAgentAIConfiguration(agentId); socket.emit('authenticated', { success: true, agent_profile: agentProfile, ai_configuration: aiConfig }); logger.info(`Agent ${agentId} authenticated for enhanced AI`); } else { socket.emit('authentication_failed', { error: 'Invalid credentials' }); socket.disconnect(); } } catch (error) { logger.error('Enhanced AI authentication error:', error); socket.emit('authentication_failed', { error: 'Authentication failed' }); socket.disconnect(); } }); // Start conversation context socket.on('start_conversation', async (data) => { try { if (!socket.authenticated) { socket.emit('error', { message: 'Not authenticated' }); return; } const { conversation_id, customer_id, ticket_id, platform = 'whatsapp' } = data; // Get customer profile and context const customerProfile = await this.aiService.getCustomerProfile(customer_id); // Initialize conversation context const context = { conversation_id, customer_id, ticket_id, platform, agent_id: socket.agentId, customer_profile: customerProfile, conversation_history: [], sentiment_history: [], suggestion_history: [], started_at: new Date().toISOString() }; this.conversationContexts.set(conversation_id, context); socket.conversationId = conversation_id; socket.join(`conversation_${conversation_id}`); // Generate initial suggestions const initialSuggestions = await this.generateInitialSuggestions(context); socket.emit('conversation_started', { conversation_id, customer_profile: customerProfile, initial_suggestions: initialSuggestions, context }); } catch (error) { logger.error('Error starting conversation:', error); socket.emit('error', { message: 'Failed to start conversation' }); } }); // Analyze message sentiment in real-time socket.on('analyze_message', async (data) => { try { if (!socket.authenticated || !socket.conversationId) { socket.emit('error', { message: 'Not in a conversation' }); return; } const { message, sender, timestamp } = data; const conversationId = socket.conversationId; const context = this.conversationContexts.get(conversationId); if (!context) { socket.emit('error', { message: 'Conversation context not found' }); return; } // Add message to conversation history const messageData = { message, sender, timestamp: timestamp || new Date().toISOString() }; context.conversation_history.push(messageData); // Analyze sentiment if customer message if (sender === 'customer') { const sentimentAnalysis = await this.sentimentService.analyzeSentiment([messageData]); context.sentiment_history.push(sentimentAnalysis); // Calculate escalation risk const escalationRisk = await this.sentimentService.calculateEscalationRisk( sentimentAnalysis, context.conversation_history ); // Emit real-time sentiment update this.namespace.to(`conversation_${conversationId}`).emit('sentiment_update', { sentiment: sentimentAnalysis, escalation_risk: escalationRisk, timestamp: new Date().toISOString() }); // Generate contextual suggestions if needed if (escalationRisk > 0.6 || sentimentAnalysis.current_sentiment < -0.5) { const urgentSuggestions = await this.generateUrgentSuggestions(context, sentimentAnalysis); socket.emit('urgent_suggestions', urgentSuggestions); } } // Update conversation context this.conversationContexts.set(conversationId, context); } catch (error) { logger.error('Error analyzing message:', error); socket.emit('error', { message: 'Failed to analyze message' }); } }); // Request contextual suggestions socket.on('request_suggestions', async (data) => { try { if (!socket.authenticated || !socket.conversationId) { socket.emit('error', { message: 'Not in a conversation' }); return; } const { current_message = '', urgency_level = 'medium', suggestion_type = 'response' } = data; const conversationId = socket.conversationId; const context = this.conversationContexts.get(conversationId); if (!context) { socket.emit('error', { message: 'Conversation context not found' }); return; } // Generate contextual suggestions const suggestions = await this.aiService.generateContextualSuggestions({ ticket_id: context.ticket_id, customer_profile: context.customer_profile, agent_profile: socket.agentProfile, conversation_context: { conversation_history: context.conversation_history, current_message, platform: context.platform }, urgency_level, platform: context.platform }); // Cache suggestions const suggestionId = this.generateSuggestionId(); this.suggestionCache.set(suggestionId, { suggestions, context: context.conversation_id, generated_at: new Date().toISOString() }); socket.emit('suggestions_generated', { suggestion_id: suggestionId, suggestions, suggestion_type, generated_at: new Date().toISOString() }); } catch (error) { logger.error('Error generating suggestions:', error); socket.emit('error', { message: 'Failed to generate suggestions' }); } }); // Get escalation recommendation socket.on('request_escalation_analysis', async (data) => { try { if (!socket.authenticated || !socket.conversationId) { socket.emit('error', { message: 'Not in a conversation' }); return; } const conversationId = socket.conversationId; const context = this.conversationContexts.get(conversationId); if (!context) { socket.emit('error', { message: 'Conversation context not found' }); return; } // Get current sentiment const latestSentiment = context.sentiment_history[context.sentiment_history.length - 1]; // Generate escalation recommendation const escalationRecommendation = await this.aiService.generateEscalationRecommendation({ ticket_id: context.ticket_id, conversation_history: context.conversation_history, customer_profile: context.customer_profile, current_sentiment: latestSentiment?.current_sentiment, agent_skill_level: socket.agentProfile.skill_level }); socket.emit('escalation_analysis', { recommendation: escalationRecommendation, analyzed_at: new Date().toISOString() }); } catch (error) { logger.error('Error generating escalation analysis:', error); socket.emit('error', { message: 'Failed to generate escalation analysis' }); } }); // Get personalized templates socket.on('request_templates', async (data) => { try { if (!socket.authenticated) { socket.emit('error', { message: 'Not authenticated' }); return; } const { category, include_personalized = true } = data; const templates = await this.templateService.getPersonalizedTemplates( socket.agentId, category ); socket.emit('templates_loaded', { templates, category, agent_id: socket.agentId }); } catch (error) { logger.error('Error loading templates:', error); socket.emit('error', { message: 'Failed to load templates' }); } }); // Submit suggestion feedback socket.on('suggestion_feedback', async (data) => { try { if (!socket.authenticated) { socket.emit('error', { message: 'Not authenticated' }); return; } const { suggestion_id, feedback } = data; // Submit feedback to AI service for learning await this.aiService.submitSuggestionFeedback(suggestion_id, socket.agentId, feedback); // Update agent learning model const learningUpdate = await this.aiService.updateLearningModel(socket.agentId, { suggestion_id, feedback }); socket.emit('feedback_submitted', { suggestion_id, learning_update, submitted_at: new Date().toISOString() }); } catch (error) { logger.error('Error submitting feedback:', error); socket.emit('error', { message: 'Failed to submit feedback' }); } }); // Update AI preferences socket.on('update_preferences', async (data) => { try { if (!socket.authenticated) { socket.emit('error', { message: 'Not authenticated' }); return; } const { preferences } = data; // Update agent preferences socket.preferences = { ...socket.preferences, ...preferences }; // Save preferences to database await this.saveAgentPreferences(socket.agentId, socket.preferences); socket.emit('preferences_updated', { preferences: socket.preferences, updated_at: new Date().toISOString() }); } catch (error) { logger.error('Error updating preferences:', error); socket.emit('error', { message: 'Failed to update preferences' }); } }); // End conversation socket.on('end_conversation', async (data) => { try { if (!socket.authenticated || !socket.conversationId) { socket.emit('error', { message: 'Not in a conversation' }); return; } const conversationId = socket.conversationId; const context = this.conversationContexts.get(conversationId); if (context) { // Generate conversation summary const summary = await this.generateConversationSummary(context); // Clean up this.conversationContexts.delete(conversationId); socket.leave(`conversation_${conversationId}`); socket.conversationId = null; socket.emit('conversation_ended', { conversation_id: conversationId, summary, ended_at: new Date().toISOString() }); } } catch (error) { logger.error('Error ending conversation:', error); socket.emit('error', { message: 'Failed to end conversation' }); } }); // Handle disconnection socket.on('disconnect', () => { logger.info(`Enhanced AI client disconnected: ${socket.id}`); if (socket.agentId) { this.activeAgents.delete(socket.agentId); } if (socket.conversationId) { // Clean up conversation context this.conversationContexts.delete(socket.conversationId); } }); // Error handling socket.on('error', (error) => { logger.error(`Enhanced AI socket error for ${socket.id}:`, error); }); }); } startContextualUpdates() { // Update agent performance insights every 10 minutes setInterval(async () => { try { await this.broadcastPerformanceInsights(); } catch (error) { logger.error('Error in performance insights updates:', error); } }, 10 * 60 * 1000); // Clean up old suggestion cache every hour setInterval(() => { this.cleanupSuggestionCache(); }, 60 * 60 * 1000); } async generateInitialSuggestions(context) { try { // Generate greeting suggestions based on customer profile const greetingSuggestions = await this.aiService.generateContextualSuggestions({ ticket_id: context.ticket_id, customer_profile: context.customer_profile, agent_profile: { skill_level: 75 }, // Default conversation_context: { conversation_history: [], current_message: '', platform: context.platform }, urgency_level: 'medium', platform: context.platform }); return { type: 'initial', suggestions: greetingSuggestions.slice(0, 3), // Limit to 3 initial suggestions generated_at: new Date().toISOString() }; } catch (error) { logger.error('Error generating initial suggestions:', error); return { type: 'initial', suggestions: [], generated_at: new Date().toISOString() }; } } async generateUrgentSuggestions(context, sentimentAnalysis) { try { const urgentSuggestions = await this.aiService.generateContextualSuggestions({ ticket_id: context.ticket_id, customer_profile: context.customer_profile, agent_profile: { skill_level: 75 }, conversation_context: { conversation_history: context.conversation_history, current_message: '', platform: context.platform, sentiment: sentimentAnalysis }, urgency_level: 'high', platform: context.platform }); return { type: 'urgent', suggestions: urgentSuggestions, reason: 'negative_sentiment_detected', generated_at: new Date().toISOString() }; } catch (error) { logger.error('Error generating urgent suggestions:', error); return { type: 'urgent', suggestions: [], generated_at: new Date().toISOString() }; } } async generateConversationSummary(context) { try { const summary = { conversation_id: context.conversation_id, duration: new Date() - new Date(context.started_at), message_count: context.conversation_history.length, sentiment_trend: this.calculateSentimentTrend(context.sentiment_history), escalation_risk: this.calculateAverageEscalationRisk(context.sentiment_history), suggestions_used: context.suggestion_history.length, customer_satisfaction_estimate: this.estimateCustomerSatisfaction(context) }; return summary; } catch (error) { logger.error('Error generating conversation summary:', error); return {}; } } async broadcastPerformanceInsights() { try { for (const [agentId, socket] of this.activeAgents) { const insights = await this.generatePerformanceInsights(agentId); socket.emit('performance_insights', insights); } } catch (error) { logger.error('Error broadcasting performance insights:', error); } } async generatePerformanceInsights(agentId) { try { // Get agent performance data const performance = await this.aiService.getSuggestionPerformance(agentId); return { agent_id: agentId, suggestion_acceptance_rate: performance.acceptance_rate || 0.75, average_response_time: performance.avg_response_time || 45, customer_satisfaction_trend: performance.satisfaction_trend || 'stable', improvement_areas: performance.improvement_areas || [], generated_at: new Date().toISOString() }; } catch (error) { logger.error('Error generating performance insights:', error); return {}; } } cleanupSuggestionCache() { const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000); for (const [suggestionId, data] of this.suggestionCache) { if (new Date(data.generated_at) < oneHourAgo) { this.suggestionCache.delete(suggestionId); } } } calculateSentimentTrend(sentimentHistory) { if (sentimentHistory.length < 2) return 'stable'; const recent = sentimentHistory.slice(-3); const older = sentimentHistory.slice(-6, -3); const recentAvg = recent.reduce((sum, s) => sum + s.current_sentiment, 0) / recent.length; const olderAvg = older.reduce((sum, s) => sum + s.current_sentiment, 0) / older.length; if (recentAvg > olderAvg + 0.2) return 'improving'; if (recentAvg < olderAvg - 0.2) return 'declining'; return 'stable'; } calculateAverageEscalationRisk(sentimentHistory) { if (sentimentHistory.length === 0) return 0; const totalRisk = sentimentHistory.reduce((sum, s) => sum + (s.escalation_risk || 0), 0); return totalRisk / sentimentHistory.length; } estimateCustomerSatisfaction(context) { // Simple estimation based on sentiment and conversation length const avgSentiment = context.sentiment_history.reduce((sum, s) => sum + s.current_sentiment, 0) / context.sentiment_history.length; const messageCount = context.conversation_history.length; let satisfaction = 5; // Base satisfaction // Adjust based on sentiment satisfaction += avgSentiment * 2; // Adjust based on conversation length (longer = potentially more complex/frustrating) if (messageCount > 20) satisfaction -= 1; if (messageCount > 40) satisfaction -= 1; return Math.max(1, Math.min(10, Math.round(satisfaction))); } generateSuggestionId() { return `suggestion_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`; } async getAgentAIConfiguration(agentId) { try { // Get agent-specific AI configuration return { suggestion_frequency: 'medium', auto_sentiment_analysis: true, escalation_alerts: true, template_suggestions: true, learning_mode: 'active' }; } catch (error) { logger.error('Error getting AI configuration:', error); return {}; } } async saveAgentPreferences(agentId, preferences) { try { // Save preferences to database logger.info(`Saving preferences for agent ${agentId}`); } catch (error) { logger.error('Error saving agent preferences:', error); } } async verifyToken(token) { try { // Integrate with existing authentication system return true; // Simplified for now } catch (error) { logger.error('Token verification error:', error); return false; } } // External broadcast methods broadcastToAgent(agentId, event, data) { const socket = this.activeAgents.get(agentId); if (socket) { socket.emit(event, data); } } broadcastToConversation(conversationId, event, data) { this.namespace.to(`conversation_${conversationId}`).emit(event, data); } // Health check getNamespaceHealth() { return { namespace: '/enhanced-ai', active_agents: this.activeAgents.size, active_conversations: this.conversationContexts.size, cached_suggestions: this.suggestionCache.size, status: 'healthy' }; } // Get statistics getStatistics() { return { active_agents: this.activeAgents.size, active_conversations: this.conversationContexts.size, cached_suggestions: this.suggestionCache.size, average_conversation_length: this.getAverageConversationLength() }; } getAverageConversationLength() { if (this.conversationContexts.size === 0) return 0; const totalMessages = Array.from(this.conversationContexts.values()) .reduce((sum, context) => sum + context.conversation_history.length, 0); return totalMessages / this.conversationContexts.size; } } module.exports = EnhancedAINamespace;