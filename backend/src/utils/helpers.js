/** * ============================================= * UTILITY HELPER FUNCTIONS * Common utility functions for the application * Data manipulation, formatting, and validation * ============================================= */ const crypto = require('crypto'); const bcrypt = require('bcryptjs'); const jwt = require('jsonwebtoken'); const moment = require('moment'); const { v4: uuidv4 } = require('uuid'); /** * Data formatting utilities */ const formatters = { /** * Format phone number to international format */ formatPhoneNumber: (phone) => { if (!phone) return null; // Remove all non-digit characters const cleaned = phone.replace(/\D/g, ''); // Add country code if missing (assuming French numbers) if (cleaned.length === 10 && cleaned.startsWith('0')) { return `+33${cleaned.substring(1)}`; } if (cleaned.length === 9) { return `+33${cleaned}`; } if (!cleaned.startsWith('+')) { return `+${cleaned}`; } return cleaned; }, /** * Format currency amount */ formatCurrency: (amount, currency = 'EUR', locale = 'fr-FR') => { if (amount === null || amount === undefined) return null; return new Intl.NumberFormat(locale, { style: 'currency', currency: currency, minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(amount); }, /** * Format percentage */ formatPercentage: (value, decimals = 2) => { if (value === null || value === undefined) return null; return `${(value * 100).toFixed(decimals)}%`; }, /** * Format file size */ formatFileSize: (bytes) => { if (bytes === 0) return '0 Bytes'; const k = 1024; const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']; const i = Math.floor(Math.log(bytes) / Math.log(k)); return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]; }, /** * Format duration in human readable format */ formatDuration: (milliseconds) => { const duration = moment.duration(milliseconds); if (duration.asHours() >= 1) { return `${Math.floor(duration.asHours())}h ${duration.minutes()}m`; } if (duration.asMinutes() >= 1) { return `${duration.minutes()}m ${duration.seconds()}s`; } return `${duration.seconds()}s`; }, /** * Format relative time */ formatRelativeTime: (date, locale = 'fr') => { return moment(date).locale(locale).fromNow(); } }; /** * Validation utilities */ const validators = { /** * Validate email format */ isValidEmail: (email) => { const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/; return emailRegex.test(email); }, /** * Validate phone number */ isValidPhoneNumber: (phone) => { const phoneRegex = /^\+[1-9]\d{1,14}$/; return phoneRegex.test(phone); }, /** * Validate UUID */ isValidUUID: (uuid) => { const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i; return uuidRegex.test(uuid); }, /** * Validate MongoDB ObjectId */ isValidObjectId: (id) => { const objectIdRegex = /^[0-9a-fA-F]{24}$/; return objectIdRegex.test(id); }, /** * Validate password strength */ isStrongPassword: (password) => { // At least 8 characters, 1 uppercase, 1 lowercase, 1 number, 1 special character const strongPasswordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/; return strongPasswordRegex.test(password); }, /** * Validate URL */ isValidURL: (url) => { try { new URL(url); return true; } catch { return false; } }, /** * Validate JSON string */ isValidJSON: (str) => { try { JSON.parse(str); return true; } catch { return false; } } }; /** * Encryption utilities */ const encryption = { /** * Hash password */ hashPassword: async (password) => { const saltRounds = 12; return await bcrypt.hash(password, saltRounds); }, /** * Compare password with hash */ comparePassword: async (password, hash) => { return await bcrypt.compare(password, hash); }, /** * Generate JWT token */ generateJWT: (payload, expiresIn = '24h') => { return jwt.sign(payload, process.env.JWT_SECRET, { expiresIn }); }, /** * Verify JWT token */ verifyJWT: (token) => { return jwt.verify(token, process.env.JWT_SECRET); }, /** * Generate random string */ generateRandomString: (length = 32) => { return crypto.randomBytes(length).toString('hex'); }, /** * Generate secure random number */ generateRandomNumber: (min = 100000, max = 999999) => { return crypto.randomInt(min, max + 1); }, /** * Encrypt sensitive data */ encryptData: (data, key = process.env.ENCRYPTION_KEY) => { const algorithm = 'aes-256-gcm'; const iv = crypto.randomBytes(16); const cipher = crypto.createCipher(algorithm, key); let encrypted = cipher.update(data, 'utf8', 'hex'); encrypted += cipher.final('hex'); const authTag = cipher.getAuthTag(); return { encrypted, iv: iv.toString('hex'), authTag: authTag.toString('hex') }; }, /** * Decrypt sensitive data */ decryptData: (encryptedData, key = process.env.ENCRYPTION_KEY) => { const algorithm = 'aes-256-gcm'; const decipher = crypto.createDecipher(algorithm, key); decipher.setAuthTag(Buffer.from(encryptedData.authTag, 'hex')); let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8'); decrypted += decipher.final('utf8'); return decrypted; } }; /** * Array utilities */ const arrays = { /** * Remove duplicates from array */ removeDuplicates: (arr) => { return [...new Set(arr)]; }, /** * Chunk array into smaller arrays */ chunk: (arr, size) => { const chunks = []; for (let i = 0; i < arr.length; i += size) { chunks.push(arr.slice(i, i + size)); } return chunks; }, /** * Shuffle array */ shuffle: (arr) => { const shuffled = [...arr]; for (let i = shuffled.length - 1; i > 0; i--) { const j = Math.floor(Math.random() * (i + 1)); [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]; } return shuffled; }, /** * Group array by key */ groupBy: (arr, key) => { return arr.reduce((groups, item) => { const group = item[key]; groups[group] = groups[group] || []; groups[group].push(item); return groups; }, {}); }, /** * Sort array by multiple keys */ sortBy: (arr, keys) => { return arr.sort((a, b) => { for (const key of keys) { const direction = key.startsWith('-') ? -1 : 1; const prop = key.replace(/^-/, ''); if (a[prop] < b[prop]) return -1 * direction; if (a[prop] > b[prop]) return 1 * direction; } return 0; }); } }; /** * Object utilities */ const objects = { /** * Deep clone object */ deepClone: (obj) => { return JSON.parse(JSON.stringify(obj)); }, /** * Merge objects deeply */ deepMerge: (target, source) => { const result = { ...target }; for (const key in source) { if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) { result[key] = objects.deepMerge(result[key] || {}, source[key]); } else { result[key] = source[key]; } } return result; }, /** * Pick specific keys from object */ pick: (obj, keys) => { const result = {}; for (const key of keys) { if (key in obj) { result[key] = obj[key]; } } return result; }, /** * Omit specific keys from object */ omit: (obj, keys) => { const result = { ...obj }; for (const key of keys) { delete result[key]; } return result; }, /** * Flatten nested object */ flatten: (obj, prefix = '') => { const flattened = {}; for (const key in obj) { const newKey = prefix ? `${prefix}.${key}` : key; if (obj[key] && typeof obj[key] === 'object' && !Array.isArray(obj[key])) { Object.assign(flattened, objects.flatten(obj[key], newKey)); } else { flattened[newKey] = obj[key]; } } return flattened; } }; /** * String utilities */ const strings = { /** * Convert to camelCase */ toCamelCase: (str) => { return str.replace(/([-_][a-z])/g, (group) => group.toUpperCase().replace('-', '').replace('_', '') ); }, /** * Convert to snake_case */ toSnakeCase: (str) => { return str.replace(/([A-Z])/g, '_$1').toLowerCase().replace(/^_/, ''); }, /** * Convert to kebab-case */ toKebabCase: (str) => { return str.replace(/([A-Z])/g, '-$1').toLowerCase().replace(/^-/, ''); }, /** * Capitalize first letter */ capitalize: (str) => { return str.charAt(0).toUpperCase() + str.slice(1); }, /** * Truncate string */ truncate: (str, length, suffix = '...') => { if (str.length <= length) return str; return str.substring(0, length) + suffix; }, /** * Generate slug from string */ slugify: (str) => { return str .toLowerCase() .trim() .replace(/[^\w\s-]/g, '') .replace(/[\s_-]+/g, '-') .replace(/^-+|-+$/g, ''); }, /** * Escape HTML */ escapeHtml: (str) => { const htmlEscapes = { '&': '&amp;', '<': '&lt;', '>': '&gt;', '"': '&quot;', "'": '&#x27;' }; return str.replace(/[&<>"']/g, (match) => htmlEscapes[match]); } }; /** * Date utilities */ const dates = { /** * Get start of day */ startOfDay: (date = new Date()) => { return moment(date).startOf('day').toDate(); }, /** * Get end of day */ endOfDay: (date = new Date()) => { return moment(date).endOf('day').toDate(); }, /** * Add time to date */ addTime: (date, amount, unit) => { return moment(date).add(amount, unit).toDate(); }, /** * Subtract time from date */ subtractTime: (date, amount, unit) => { return moment(date).subtract(amount, unit).toDate(); }, /** * Check if date is between two dates */ isBetween: (date, start, end) => { return moment(date).isBetween(start, end, null, '[]'); }, /** * Get business days between dates */ getBusinessDays: (start, end) => { let count = 0; const current = moment(start); const endDate = moment(end); while (current.isSameOrBefore(endDate)) { if (current.day() !== 0 && current.day() !== 6) { // Not Sunday or Saturday count++; } current.add(1, 'day'); } return count; } }; /** * Performance utilities */ const performance = { /** * Debounce function */ debounce: (func, wait) => { let timeout; return function executedFunction(...args) { const later = () => { clearTimeout(timeout); func(...args); }; clearTimeout(timeout); timeout = setTimeout(later, wait); }; }, /** * Throttle function */ throttle: (func, limit) => { let inThrottle; return function executedFunction(...args) { if (!inThrottle) { func.apply(this, args); inThrottle = true; setTimeout(() => inThrottle = false, limit); } }; }, /** * Memoize function */ memoize: (func) => { const cache = new Map(); return function memoized(...args) { const key = JSON.stringify(args); if (cache.has(key)) { return cache.get(key); } const result = func.apply(this, args); cache.set(key, result); return result; }; } }; /** * Generate unique identifiers */ const generateId = { uuid: () => uuidv4(), shortId: () => crypto.randomBytes(8).toString('hex'), timestamp: () => Date.now().toString(36), sessionId: () => `session_${Date.now()}_${crypto.randomBytes(4).toString('hex')}`, requestId: () => `req_${Date.now()}_${crypto.randomBytes(6).toString('hex')}` }; module.exports = { formatters, validators, encryption, arrays, objects, strings, dates, performance, generateId };