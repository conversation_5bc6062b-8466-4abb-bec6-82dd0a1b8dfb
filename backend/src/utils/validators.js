const { ObjectId } = require('mongoose').Types; const validator = require('validator'); /** * Validation des IDs MongoDB */ exports.validateObjectId = (id, fieldName = 'ID') => { if (!id) { throw new Error(`${fieldName} est requis`); } if (!ObjectId.isValid(id)) { throw new Error(`${fieldName} invalide`); } return true; }; /** * Validation des données de conversation */ exports.validateConversationData = (data) => { const errors = []; if (data.channel && !['web', 'mobile', 'voice', 'whatsapp', 'messenger'].includes(data.channel)) { errors.push('Canal de communication invalide'); } return { isValid: errors.length === 0, errors }; }; /** * Validation des messages */ exports.validateMessage = (message) => { const errors = []; if (!message || typeof message !== 'string') { errors.push('Le message est requis et doit être une chaîne de caractères'); } else { // Nettoyer et valider le contenu const cleanMessage = validator.escape(message.trim()); if (cleanMessage.length === 0) { errors.push('Le message ne peut pas être vide'); } if (cleanMessage.length > 2000) { errors.push('Le message ne peut pas dépasser 2000 caractères'); } // Détecter les tentatives d'injection const suspiciousPatterns = [ /<script.*?>.*?<\/script>/gi, /javascript:/gi, /on\w+\s*=/gi, /eval\s*\(/gi, /alert\s*\(/gi ]; const hasSuspiciousContent = suspiciousPatterns.some(pattern => pattern.test(message) ); if (hasSuspiciousContent) { errors.push('Contenu suspect détecté'); } } return { isValid: errors.length === 0, errors, cleanMessage: message ? validator.escape(message.trim()) : '' }; }; /** * Validation des données utilisateur */ exports.validateUserData = (userData) => { const errors = []; // Email if (!userData.email) { errors.push('Email requis'); } else if (!validator.isEmail(userData.email)) { errors.push('Format email invalide'); } // Mot de passe if (!userData.password) { errors.push('Mot de passe requis'); } else if (userData.password.length < 8) { errors.push('Le mot de passe doit contenir au moins 8 caractères'); } else if (!validator.isStrongPassword(userData.password, { minLength: 8, minLowercase: 1, minUppercase: 1, minNumbers: 1, minSymbols: 1 })) { errors.push('Mot de passe trop faible (requis: 8+ caractères, majuscule, minuscule, chiffre, symbole)'); } // Nom et prénom if (userData.firstName && !validator.isAlpha(userData.firstName.replace(/\s/g, ''), 'fr-FR')) { errors.push('Prénom invalide'); } if (userData.lastName && !validator.isAlpha(userData.lastName.replace(/\s/g, ''), 'fr-FR')) { errors.push('Nom invalide'); } // Numéro de téléphone if (userData.phoneNumber && !validator.isMobilePhone(userData.phoneNumber, 'fr-FR')) { errors.push('Numéro de téléphone invalide'); } return { isValid: errors.length === 0, errors, sanitizedData: { email: userData.email ? validator.normalizeEmail(userData.email) : '', firstName: userData.firstName ? validator.escape(userData.firstName.trim()) : '', lastName: userData.lastName ? validator.escape(userData.lastName.trim()) : '', phoneNumber: userData.phoneNumber ? userData.phoneNumber.replace(/\s/g, '') : '' } }; }; /** * Validation des paramètres de requête */ exports.validateQueryParams = (params) => { const errors = []; const validated = {}; // Page if (params.page) { const page = parseInt(params.page); if (isNaN(page) || page < 1) { errors.push('Numéro de page invalide'); } else { validated.page = page; } } // Limit if (params.limit) { const limit = parseInt(params.limit); if (isNaN(limit) || limit < 1 || limit > 100) { errors.push('Limite invalide (1-100)'); } else { validated.limit = limit; } } // Status if (params.status && !['active', 'resolved', 'escalated', 'abandoned'].includes(params.status)) { errors.push('Statut invalide'); } else if (params.status) { validated.status = params.status; } // Search if (params.search) { const cleanSearch = validator.escape(params.search.trim()); if (cleanSearch.length > 100) { errors.push('Terme de recherche trop long'); } else { validated.search = cleanSearch; } } return { isValid: errors.length === 0, errors, validated }; }; /** * Middleware de validation des erreurs */ exports.handleValidationErrors = (errors) => { if (errors && errors.length > 0) { const error = new Error('Données invalides'); error.status = 400; error.details = errors; throw error; } }; /** * Sanitisation générale des données */ exports.sanitizeInput = (input) => { if (typeof input === 'string') { return validator.escape(input.trim()); } if (typeof input === 'object' && input !== null) { const sanitized = {}; for (const [key, value] of Object.entries(input)) { sanitized[key] = exports.sanitizeInput(value); } return sanitized; } return input; }; /** * Validation des fichiers uploadés */ exports.validateFile = (file) => { const errors = []; const allowedTypes = [ 'image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'text/plain' ]; if (!file) { errors.push('Fichier requis'); return { isValid: false, errors }; } if (!allowedTypes.includes(file.mimetype)) { errors.push('Type de fichier non autorisé'); } if (file.size > 5 * 1024 * 1024) { // 5MB errors.push('Fichier trop volumineux (max 5MB)'); } return { isValid: errors.length === 0, errors }; };