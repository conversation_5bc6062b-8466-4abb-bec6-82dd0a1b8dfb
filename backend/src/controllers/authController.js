const User = require('../models/User'); const jwt = require('jsonwebtoken'); const logger = require('../config/logger'); const { validateUserData, handleValidationErrors } = require('../utils/validators'); const generateToken = (userId) => { return jwt.sign({ userId }, process.env.JWT_SECRET, { expiresIn: '7d' }); }; exports.register = async (req, res) => { try { const userData = req.body; // Validation des données const validation = validateUserData(userData); if (!validation.isValid) { logger.warn(`Registration validation failed: ${validation.errors.join(', ')}`); return res.status(400).json({ error: 'Données invalides', details: validation.errors }); } const { email, password, firstName, lastName } = validation.sanitizedData; logger.info(`Registration attempt for email: ${email}`); // Vérifier si l'utilisateur existe déjà const existingUser = await User.findOne({ email }); if (existingUser) { logger.warn(`Registration failed - email already exists: ${email}`); return res.status(409).json({ error: 'Un compte avec cet email existe déjà' }); } // Créer le nouvel utilisateur const user = new User({ email, password: userData.password, // Mot de passe non échappé pour le hachage profile: { firstName, lastName }, }); await user.save(); const token = generateToken(user._id); logger.info(`User registered successfully: ${email}`); res.status(201).json({ success: true, user: { id: user._id, email: user.email, role: user.role, profile: user.profile, }, token, }); } catch (error) { logger.error('Registration error:', error); // Gestion spécifique des erreurs de base de données if (error.name === 'ValidationError') { const mongoErrors = Object.values(error.errors).map(err => err.message); return res.status(400).json({ error: 'Erreur de validation', details: mongoErrors }); } res.status(500).json({ error: 'Erreur serveur lors de la création du compte' }); } }; exports.login = async (req, res) => { try { const { email, password } = req.body; // Validation de base des champs requis if (!email || !password) { logger.warn('Login attempt with missing credentials'); return res.status(400).json({ error: 'Email et mot de passe requis', details: ['Email et mot de passe sont obligatoires'] }); } // Validation du format email const validator = require('validator'); if (!validator.isEmail(email)) { logger.warn(`Login attempt with invalid email format: ${email}`); return res.status(400).json({ error: 'Format email invalide', details: ['Veuillez saisir un email valide'] }); } const normalizedEmail = validator.normalizeEmail(email); logger.info(`Login attempt for email: ${normalizedEmail}`); // Recherche de l'utilisateur const user = await User.findOne({ email: normalizedEmail }); if (!user) { logger.warn(`Login failed - user not found: ${normalizedEmail}`); return res.status(401).json({ error: 'Identifiants incorrects' }); } // Vérification du mot de passe const isValidPassword = await user.comparePassword(password); if (!isValidPassword) { logger.warn(`Login failed - invalid password for: ${normalizedEmail}`); return res.status(401).json({ error: 'Identifiants incorrects' }); } // Mise à jour de la dernière connexion user.lastLogin = new Date(); await user.save(); const token = generateToken(user._id); logger.info(`User logged in successfully: ${normalizedEmail}`); res.json({ success: true, user: { id: user._id, email: user.email, role: user.role, profile: user.profile, lastLogin: user.lastLogin }, token, expiresIn: '7d' }); } catch (error) { logger.error('Login error:', error); res.status(500).json({ error: 'Erreur serveur lors de la connexion' }); } };