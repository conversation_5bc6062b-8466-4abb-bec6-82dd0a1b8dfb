const subscriptionService = require('../services/subscriptionService'); const CustomerProfile = require('../models/CustomerProfile'); const logger = require('../config/logger'); // Obtenir les détails du forfait actuel exports.getCurrentPlan = async (req, res) => { try { const profile = await CustomerProfile.findOne({ userId: req.user._id }); if (!profile) { return res.status(404).json({ error: 'Profil client non trouvé' }); } res.json({ subscription: profile.subscription, activeOptions: profile.activeOptions, totalMonthly: profile.subscription.monthlyPrice + profile.activeOptions.reduce((sum, opt) => sum + opt.price, 0) }); } catch (error) { logger.error('Error getting current plan:', error); res.status(500).json({ error: 'Erreur lors de la récupération du forfait' }); } }; // Simuler un changement de forfait exports.simulatePlanChange = async (req, res) => { try { const { planId } = req.body; const simulation = await subscriptionService.simulatePlanChange( req.user._id, planId ); res.json(simulation); } catch (error) { logger.error('Error simulating plan change:', error); res.status(500).json({ error: 'Erreur lors de la simulation' }); } }; // Changer de forfait exports.changePlan = async (req, res) => { try { const { planId } = req.body; const result = await subscriptionService.changePlan(req.user._id, planId); if (!result.success) { return res.status(400).json({ error: result.message }); } // Envoyer notification via Socket.IO const io = req.app.get('io'); if (io) { io.to(`user_${req.user._id}`).emit('plan_changed', { newPlan: result.newPlan, effectiveDate: result.effectiveDate }); } res.json(result); } catch (error) { logger.error('Error changing plan:', error); res.status(500).json({ error: 'Erreur lors du changement de forfait' }); } }; // Activer une option exports.activateOption = async (req, res) => { try { const { optionId } = req.body; const result = await subscriptionService.activateOption(req.user._id, optionId); if (!result.success) { return res.status(400).json({ error: result.message }); } res.json(result); } catch (error) { logger.error('Error activating option:', error); res.status(500).json({ error: 'Erreur lors de l\'activation de l\'option' }); } }; // Désactiver une option exports.deactivateOption = async (req, res) => { try { const { optionName } = req.body; const profile = await CustomerProfile.findOne({ userId: req.user._id }); if (!profile) { return res.status(404).json({ error: 'Profil non trouvé' }); } const optionIndex = profile.activeOptions.findIndex(opt => opt.name === optionName); if (optionIndex === -1) { return res.status(400).json({ error: 'Option non trouvée' }); } const removedOption = profile.activeOptions.splice(optionIndex, 1)[0]; await profile.save(); res.json({ success: true, message: `L'option ${removedOption.name} a été désactivée`, removedOption }); } catch (error) { logger.error('Error deactivating option:', error); res.status(500).json({ error: 'Erreur lors de la désactivation' }); } }; // Obtenir la consommation actuelle exports.getConsumption = async (req, res) => { try { const consumption = await subscriptionService.getConsumption(req.user._id); res.json(consumption); } catch (error) { logger.error('Error getting consumption:', error); res.status(500).json({ error: 'Erreur lors de la récupération de la consommation' }); } }; // Obtenir l'historique de facturation exports.getBillingHistory = async (req, res) => { try { const { limit = 12 } = req.query; const history = await subscriptionService.getBillingHistory( req.user._id, parseInt(limit) ); res.json(history); } catch (error) { logger.error('Error getting billing history:', error); res.status(500).json({ error: 'Erreur lors de la récupération de l\'historique' }); } }; // Obtenir les forfaits disponibles exports.getAvailablePlans = async (req, res) => { try { const plans = Object.values(subscriptionService.constructor.PLANS); const profile = await CustomerProfile.findOne({ userId: req.user._id }); // Marquer le forfait actuel const plansWithCurrent = plans.map(plan => ({ ...plan, isCurrent: profile && profile.subscription.planName === plan.name })); res.json(plansWithCurrent); } catch (error) { logger.error('Error getting available plans:', error); res.status(500).json({ error: 'Erreur lors de la récupération des forfaits' }); } }; // Obtenir les options disponibles exports.getAvailableOptions = async (req, res) => { try { const options = Object.values(subscriptionService.constructor.OPTIONS); const profile = await CustomerProfile.findOne({ userId: req.user._id }); // Marquer les options actives const optionsWithStatus = options.map(option => ({ ...option, isActive: profile && profile.activeOptions.some(opt => opt.name === option.name) })); res.json(optionsWithStatus); } catch (error) { logger.error('Error getting available options:', error); res.status(500).json({ error: 'Erreur lors de la récupération des options' }); } };