/** * Contrôleur ML pour les APIs de classification intelligente * Free Mobile Chatbot Dashboard - Phase 2 Backend Integration */ const mlIntegrationService = require('../services/mlIntegrationService'); const ConversationClassification = require('../models/ConversationClassification'); const AdminAlert = require('../models/AdminAlert'); const Conversation = require('../models/Conversation'); const CustomerProfile = require('../models/CustomerProfile'); const logger = require('../config/logger'); class MLController { /** * Classification d'une conversation * POST /api/v2/ml/classify */ async classifyConversation(req, res) { try { const { conversationId, forceReprocess = false } = req.body; if (!conversationId) { return res.status(400).json({ success: false, error: 'conversationId is required' }); } // Récupération des données de conversation const conversation = await Conversation.findById(conversationId) .populate('messages') .populate('customerId'); if (!conversation) { return res.status(404).json({ success: false, error: 'Conversation not found' }); } // Vérification si déjà classifiée (sauf si forceReprocess) if (!forceReprocess) { const existingClassification = await ConversationClassification.findOne({ conversationId: conversationId }).sort({ processedAt: -1 }); if (existingClassification) { return res.json({ success: true, classification: existingClassification, cached: true, message: 'Classification already exists' }); } } // Préparation des données pour le ML const conversationData = { conversationId: conversationId, messages: conversation.messages || [], customerData: conversation.customerId, metadata: { channel: conversation.channel, durationMinutes: conversation.duration, agentId: conversation.assignedAgent, previousInteractions: conversation.previousInteractions || 0, forceReprocess: forceReprocess } }; // Classification ML const result = await mlIntegrationService.classifyConversation(conversationData); // Log de l'activité logger.info('Conversation classified via API', { conversationId: conversationId, category: result.classification.category, priority: result.classification.priorityScore, userId: req.user?.id }); res.json(result); } catch (error) { logger.error('Classification API Error:', error); res.status(500).json({ success: false, error: 'Classification failed', details: error.message }); } } /** * Récupération de la queue de priorité * GET /api/v2/ml/queue/priority */ async getPriorityQueue(req, res) { try { const { limit = 50, minPriority = 0, category, status = 'active' } = req.query; const options = { limit: parseInt(limit), minPriority: parseInt(minPriority), category: category }; // Récupération depuis le service ML const mlQueue = await mlIntegrationService.getPriorityQueue(options); // Enrichissement avec les données locales const enrichedQueue = await Promise.all( mlQueue.map(async (item) => { try { const classification = await ConversationClassification.findOne({ conversationId: item.conversation_id }) .populate('conversationId') .populate('customerId'); if (classification) { return { ...item, classification: classification, conversation: classification.conversationId, customer: classification.customerId, alerts: await AdminAlert.countDocuments({ conversationId: item.conversation_id, status: { $in: ['ACTIVE', 'ACKNOWLEDGED'] } }) }; } return item; } catch (error) { logger.warn(`Error enriching queue item ${item.conversation_id}:`, error); return item; } }) ); res.json({ success: true, queue: enrichedQueue, totalCount: enrichedQueue.length, filters: { limit: options.limit, minPriority: options.minPriority, category: options.category } }); } catch (error) { logger.error('Priority Queue API Error:', error); res.status(500).json({ success: false, error: 'Failed to fetch priority queue', details: error.message }); } } /** * Métriques de performance ML * GET /api/v2/ml/metrics/performance */ async getPerformanceMetrics(req, res) { try { const { timeRange = '1h' } = req.query; // Métriques du service ML const mlMetrics = await mlIntegrationService.getPerformanceMetrics(); // Métriques locales complémentaires const now = new Date(); let startDate; switch (timeRange) { case '1h': startDate = new Date(now.getTime() - 60 * 60 * 1000); break; case '24h': startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000); break; case '7d': startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000); break; case '30d': startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000); break; default: startDate = new Date(now.getTime() - 60 * 60 * 1000); } // Statistiques des classifications const classificationStats = await ConversationClassification.getClassificationStats( startDate, now ); // Statistiques des alertes const alertStats = await AdminAlert.getAlertStats(startDate, now); // Métriques de validation humaine const validationStats = await ConversationClassification.aggregate([ { $match: { processedAt: { $gte: startDate, $lte: now } } }, { $group: { _id: null, totalClassifications: { $sum: 1 }, humanValidated: { $sum: { $cond: ['$humanValidated', 1, 0] } }, avgConfidence: { $avg: '$confidence' }, highPriorityCount: { $sum: { $cond: [{ $gte: ['$priorityScore', 80] }, 1, 0] } } } } ]); const validation = validationStats[0] || { totalClassifications: 0, humanValidated: 0, avgConfidence: 0, highPriorityCount: 0 }; res.json({ success: true, timeRange: timeRange, timestamp: now.toISOString(), mlService: mlMetrics, classifications: { total: validation.totalClassifications, humanValidated: validation.humanValidated, validationRate: validation.totalClassifications > 0 ? validation.humanValidated / validation.totalClassifications : 0, avgConfidence: validation.avgConfidence, highPriorityCount: validation.highPriorityCount, categoryBreakdown: classificationStats }, alerts: { breakdown: alertStats, totalActive: await AdminAlert.countDocuments({ status: { $in: ['ACTIVE', 'ACKNOWLEDGED', 'IN_PROGRESS'] } }) } }); } catch (error) { logger.error('Performance Metrics API Error:', error); res.status(500).json({ success: false, error: 'Failed to fetch performance metrics', details: error.message }); } } /** * Récupération des classifications par conversation * GET /api/v2/ml/classifications/:conversationId */ async getClassificationsByConversation(req, res) { try { const { conversationId } = req.params; const { includeHistory = false } = req.query; let query = { conversationId: conversationId }; const classifications = await ConversationClassification.find(query) .sort({ processedAt: -1 }) .populate('conversationId') .populate('customerId') .limit(includeHistory ? 10 : 1); if (classifications.length === 0) { return res.status(404).json({ success: false, error: 'No classifications found for this conversation' }); } // Récupération des alertes associées const alerts = await AdminAlert.find({ conversationId: conversationId }).sort({ createdAt: -1 }); res.json({ success: true, classifications: classifications, alerts: alerts, latest: classifications[0] }); } catch (error) { logger.error('Get Classifications API Error:', error); res.status(500).json({ success: false, error: 'Failed to fetch classifications', details: error.message }); } } /** * Validation humaine d'une classification * PUT /api/v2/ml/classifications/:id/validate */ async validateClassification(req, res) { try { const { id } = req.params; const { isCorrect, correctedCategory, feedback, confidence } = req.body; const classification = await ConversationClassification.findById(id); if (!classification) { return res.status(404).json({ success: false, error: 'Classification not found' }); } // Mise à jour de la validation classification.humanValidated = true; classification.validatedBy = req.user.id; classification.validatedAt = new Date(); classification.validationFeedback = feedback; // Correction si nécessaire if (!isCorrect && correctedCategory) { classification.category = correctedCategory; if (confidence) { classification.confidence = confidence; } } await classification.save(); logger.info('Classification validated', { classificationId: id, isCorrect: isCorrect, correctedCategory: correctedCategory, validatedBy: req.user.id }); res.json({ success: true, classification: classification, message: 'Classification validated successfully' }); } catch (error) { logger.error('Validate Classification API Error:', error); res.status(500).json({ success: false, error: 'Failed to validate classification', details: error.message }); } } /** * Statistiques des classifications * GET /api/v2/ml/stats */ async getClassificationStats(req, res) { try { const { startDate, endDate, groupBy = 'day', category } = req.query; const matchStage = {}; if (startDate || endDate) { matchStage.processedAt = {}; if (startDate) matchStage.processedAt.$gte = new Date(startDate); if (endDate) matchStage.processedAt.$lte = new Date(endDate); } if (category) { matchStage.category = category; } // Groupement temporel let dateGrouping; switch (groupBy) { case 'hour': dateGrouping = { $dateToString: { format: "%Y-%m-%d %H:00", date: "$processedAt" } }; break; case 'day': dateGrouping = { $dateToString: { format: "%Y-%m-%d", date: "$processedAt" } }; break; case 'week': dateGrouping = { $dateToString: { format: "%Y-W%U", date: "$processedAt" } }; break; case 'month': dateGrouping = { $dateToString: { format: "%Y-%m", date: "$processedAt" } }; break; default: dateGrouping = { $dateToString: { format: "%Y-%m-%d", date: "$processedAt" } }; } const stats = await ConversationClassification.aggregate([ { $match: matchStage }, { $group: { _id: { period: dateGrouping, category: '$category' }, count: { $sum: 1 }, avgPriority: { $avg: '$priorityScore' }, avgConfidence: { $avg: '$confidence' }, avgProcessingTime: { $avg: '$processingTimeMs' }, totalRevenueAtRisk: { $sum: '$businessImpact.revenueAtRisk' }, totalOpportunityValue: { $sum: '$businessImpact.opportunityValue' }, highPriorityCount: { $sum: { $cond: [{ $gte: ['$priorityScore', 80] }, 1, 0] } } } }, { $sort: { '_id.period': 1, '_id.category': 1 } } ]); res.json({ success: true, stats: stats, filters: { startDate: startDate, endDate: endDate, groupBy: groupBy, category: category } }); } catch (error) { logger.error('Classification Stats API Error:', error); res.status(500).json({ success: false, error: 'Failed to fetch classification stats', details: error.message }); } } /** * Invalidation du cache ML * DELETE /api/v2/ml/cache/:conversationId */ async invalidateCache(req, res) { try { const { conversationId } = req.params; const success = await mlIntegrationService.invalidateCache(conversationId); res.json({ success: success, conversationId: conversationId, message: success ? 'Cache invalidated successfully' : 'Cache invalidation failed' }); } catch (error) { logger.error('Cache Invalidation API Error:', error); res.status(500).json({ success: false, error: 'Failed to invalidate cache', details: error.message }); } } /** * Health check du service ML * GET /api/v2/ml/health */ async healthCheck(req, res) { try { const health = await mlIntegrationService.healthCheck(); const statusCode = health.status === 'healthy' ? 200 : 503; res.status(statusCode).json({ success: health.status === 'healthy', health: health }); } catch (error) { logger.error('ML Health Check API Error:', error); res.status(503).json({ success: false, error: 'Health check failed', details: error.message }); } } } module.exports = new MLController();