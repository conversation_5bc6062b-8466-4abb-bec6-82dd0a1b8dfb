/** * ============================================= * EMERGENCY CALL CONTROLLER * Handles emergency call routing, escalation, and human agent transfer * Complies with French telecommunications regulations * ============================================= */ const logger = require('../config/logger'); const EmergencyCall = require('../models/EmergencyCall'); const User = require('../models/User'); const { validationResult } = require('express-validator'); const { v4: uuidv4 } = require('uuid'); class EmergencyCallController { constructor() { this.emergencyHotline = '9198'; this.humanSupportLine = '**********'; this.maxConcurrentEmergencyCalls = 50; this.emergencyCallTimeout = 30000; // 30 seconds this.queueTimeoutThreshold = 120000; // 2 minutes } /** * Initiate emergency call */ async initiateEmergencyCall(req, res) { try { const errors = validationResult(req); if (!errors.isEmpty()) { return res.status(400).json({ success: false, error: 'Validation failed', details: errors.array() }); } const { userId, urgencyLevel, description, conversationHistory } = req.body; const userAgent = req.get('User-Agent'); const clientIP = req.ip; // Validate user const user = await User.findById(userId); if (!user) { return res.status(404).json({ success: false, error: 'User not found' }); } // Check rate limiting for emergency calls const recentCalls = await EmergencyCall.countDocuments({ userId, createdAt: { $gte: new Date(Date.now() - 300000) }, // Last 5 minutes status: { $in: ['initiated', 'in_progress', 'escalated'] } }); if (recentCalls >= 3) { logger.warn(`Emergency call rate limit exceeded for user ${userId}`, { userId, recentCalls, clientIP }); return res.status(429).json({ success: false, error: 'Emergency call rate limit exceeded', message: 'Trop d\'appels d\'urgence récents. Veuillez patienter.' }); } // Generate emergency call ID const emergencyCallId = uuidv4(); // Create emergency call record const emergencyCall = new EmergencyCall({ emergencyCallId, userId, urgencyLevel: urgencyLevel || 'high', description, conversationHistory: conversationHistory || [], status: 'initiated', clientInfo: { ip: clientIP, userAgent, timestamp: new Date() }, routing: { attemptedRoutes: [], currentRoute: 'ai_assessment', escalationLevel: 0 }, compliance: { recordingConsent: true, dataRetentionNotified: true, emergencyServiceNotification: urgencyLevel === 'critical' } }); await emergencyCall.save(); // Log emergency call initiation logger.info(` Emergency call initiated`, { emergencyCallId, userId, urgencyLevel, clientIP }); // Assess urgency and determine routing const routingDecision = await this.assessUrgencyAndRoute(emergencyCall); // Update call with routing decision emergencyCall.routing.currentRoute = routingDecision.route; emergencyCall.routing.attemptedRoutes.push({ route: 'ai_assessment', timestamp: new Date(), result: 'completed', nextRoute: routingDecision.route }); await emergencyCall.save(); // Emit real-time event const websocketService = req.app.get('websocketService'); if (websocketService) { websocketService.emitToUser(userId, 'emergency-call-initiated', { emergencyCallId, status: 'initiated', routing: routingDecision, estimatedWaitTime: routingDecision.estimatedWaitTime }); } res.status(201).json({ success: true, emergencyCallId, status: 'initiated', routing: routingDecision, message: 'Appel d\'urgence initié avec succès' }); } catch (error) { logger.error('Failed to initiate emergency call:', error); res.status(500).json({ success: false, error: 'Failed to initiate emergency call', message: 'Erreur lors de l\'initiation de l\'appel d\'urgence' }); } } /** * Assess urgency level and determine routing strategy */ async assessUrgencyAndRoute(emergencyCall) { try { const { urgencyLevel, description, conversationHistory } = emergencyCall; // AI-powered urgency assessment const urgencyScore = await this.calculateUrgencyScore( urgencyLevel, description, conversationHistory ); let routingDecision = { route: 'ai_assistance', priority: 'normal', estimatedWaitTime: 60000, // 1 minute requiresHumanAgent: false }; // Determine routing based on urgency score if (urgencyScore >= 9 || urgencyLevel === 'critical') { routingDecision = { route: 'immediate_human_transfer', priority: 'critical', estimatedWaitTime: 5000, // 5 seconds requiresHumanAgent: true, targetNumber: this.humanSupportLine }; } else if (urgencyScore >= 7 || urgencyLevel === 'high') { routingDecision = { route: 'priority_queue', priority: 'high', estimatedWaitTime: 30000, // 30 seconds requiresHumanAgent: true, targetNumber: this.humanSupportLine }; } else if (urgencyScore >= 5) { routingDecision = { route: 'enhanced_ai_assistance', priority: 'medium', estimatedWaitTime: 15000, // 15 seconds requiresHumanAgent: false, fallbackToHuman: true }; } logger.info(`Emergency call routing decision`, { emergencyCallId: emergencyCall.emergencyCallId, urgencyScore, routingDecision }); return routingDecision; } catch (error) { logger.error('Failed to assess urgency and route:', error); // Default to human agent transfer on error return { route: 'immediate_human_transfer', priority: 'high', estimatedWaitTime: 10000, requiresHumanAgent: true, targetNumber: this.humanSupportLine }; } } /** * Calculate urgency score based on multiple factors */ async calculateUrgencyScore(urgencyLevel, description, conversationHistory) { try { let score = 0; // Base score from urgency level const urgencyScores = { 'low': 2, 'normal': 4, 'medium': 5, 'high': 7, 'urgent': 8, 'critical': 10 }; score += urgencyScores[urgencyLevel] || 4; // Analyze description for emergency keywords if (description) { const emergencyKeywords = [ 'urgence', 'urgent', 'critique', 'panne', 'coupure', 'problème grave', 'ne fonctionne plus', 'bloqué', 'facture incorrecte', 'surfacturation', 'piratage' ]; const descriptionLower = description.toLowerCase(); const keywordMatches = emergencyKeywords.filter(keyword => descriptionLower.includes(keyword) ).length; score += Math.min(keywordMatches * 0.5, 2); } // Analyze conversation history if (conversationHistory && conversationHistory.length > 0) { const recentMessages = conversationHistory.slice(-5); const frustrationIndicators = [ 'pas de réponse', 'ça ne marche pas', 'toujours pas résolu', 'depuis des heures', 'inacceptable', 'remboursement' ]; let frustrationScore = 0; recentMessages.forEach(message => { if (message.content) { const contentLower = message.content.toLowerCase(); frustrationIndicators.forEach(indicator => { if (contentLower.includes(indicator)) { frustrationScore += 0.3; } }); } }); score += Math.min(frustrationScore, 1.5); } return Math.min(Math.max(score, 1), 10); // Clamp between 1-10 } catch (error) { logger.error('Failed to calculate urgency score:', error); return 5; // Default medium urgency } } } /** * Escalate to human agent */ async escalateToHumanAgent(req, res) { try { const { emergencyCallId } = req.params; const { reason, agentPreference } = req.body; const emergencyCall = await EmergencyCall.findOne({ emergencyCallId }); if (!emergencyCall) { return res.status(404).json({ success: false, error: 'Emergency call not found' }); } // Update call status emergencyCall.status = 'escalated'; emergencyCall.routing.escalationLevel += 1; emergencyCall.routing.currentRoute = 'human_agent_queue'; emergencyCall.routing.attemptedRoutes.push({ route: 'escalation', timestamp: new Date(), reason, agentPreference }); // Add to priority queue const queuePosition = await this.addToEmergencyQueue(emergencyCall, 'high'); emergencyCall.queueInfo = { position: queuePosition, estimatedWaitTime: queuePosition * 45000, // 45 seconds per position addedAt: new Date() }; await emergencyCall.save(); // Notify available agents const websocketService = req.app.get('websocketService'); if (websocketService) { websocketService.emitToRole('agent', 'emergency-call-escalated', { emergencyCallId, urgencyLevel: emergencyCall.urgencyLevel, queuePosition, userInfo: { userId: emergencyCall.userId, conversationHistory: emergencyCall.conversationHistory } }); // Notify user websocketService.emitToUser(emergencyCall.userId, 'emergency-call-escalated', { emergencyCallId, status: 'escalated', queuePosition, estimatedWaitTime: emergencyCall.queueInfo.estimatedWaitTime }); } logger.info(` Emergency call escalated to human agent`, { emergencyCallId, reason, queuePosition }); res.json({ success: true, status: 'escalated', queuePosition, estimatedWaitTime: emergencyCall.queueInfo.estimatedWaitTime, message: 'Appel transféré vers un agent humain' }); } catch (error) { logger.error('Failed to escalate emergency call:', error); res.status(500).json({ success: false, error: 'Failed to escalate emergency call' }); } } /** * Connect to human agent */ async connectToHumanAgent(req, res) { try { const { emergencyCallId } = req.params; const { agentId } = req.body; const emergencyCall = await EmergencyCall.findOne({ emergencyCallId }); if (!emergencyCall) { return res.status(404).json({ success: false, error: 'Emergency call not found' }); } // Validate agent const agent = await User.findById(agentId); if (!agent || agent.role !== 'agent') { return res.status(400).json({ success: false, error: 'Invalid agent' }); } // Update call status emergencyCall.status = 'connected_to_agent'; emergencyCall.agentInfo = { agentId, agentName: agent.profile.firstName + ' ' + agent.profile.lastName, connectedAt: new Date() }; emergencyCall.routing.currentRoute = 'active_call'; await emergencyCall.save(); // Initiate WebRTC call const callService = req.app.get('callService'); if (callService) { const webrtcSession = await callService.initiateEmergencyCall({ emergencyCallId, customerId: emergencyCall.userId, agentId, priority: 'emergency' }); emergencyCall.webrtcSessionId = webrtcSession.sessionId; await emergencyCall.save(); } // Notify both parties const websocketService = req.app.get('websocketService'); if (websocketService) { // Notify customer websocketService.emitToUser(emergencyCall.userId, 'emergency-call-connected', { emergencyCallId, agentInfo: emergencyCall.agentInfo, webrtcSessionId: emergencyCall.webrtcSessionId }); // Notify agent websocketService.emitToUser(agentId, 'emergency-call-assigned', { emergencyCallId, customerInfo: { userId: emergencyCall.userId, urgencyLevel: emergencyCall.urgencyLevel, description: emergencyCall.description, conversationHistory: emergencyCall.conversationHistory }, webrtcSessionId: emergencyCall.webrtcSessionId }); } logger.info(` Emergency call connected to human agent`, { emergencyCallId, agentId, agentName: emergencyCall.agentInfo.agentName }); res.json({ success: true, status: 'connected_to_agent', agentInfo: emergencyCall.agentInfo, webrtcSessionId: emergencyCall.webrtcSessionId, message: 'Connecté à un agent humain' }); } catch (error) { logger.error('Failed to connect to human agent:', error); res.status(500).json({ success: false, error: 'Failed to connect to human agent' }); } } /** * Get emergency call status */ async getEmergencyCallStatus(req, res) { try { const { emergencyCallId } = req.params; const emergencyCall = await EmergencyCall.findOne({ emergencyCallId }) .populate('userId', 'profile.firstName profile.lastName email') .populate('agentInfo.agentId', 'profile.firstName profile.lastName'); if (!emergencyCall) { return res.status(404).json({ success: false, error: 'Emergency call not found' }); } res.json({ success: true, emergencyCall: { emergencyCallId: emergencyCall.emergencyCallId, status: emergencyCall.status, urgencyLevel: emergencyCall.urgencyLevel, description: emergencyCall.description, routing: emergencyCall.routing, queueInfo: emergencyCall.queueInfo, agentInfo: emergencyCall.agentInfo, createdAt: emergencyCall.createdAt, updatedAt: emergencyCall.updatedAt } }); } catch (error) { logger.error('Failed to get emergency call status:', error); res.status(500).json({ success: false, error: 'Failed to get emergency call status' }); } } /** * Add emergency call to priority queue */ async addToEmergencyQueue(emergencyCall, priority = 'high') { try { // Implementation would integrate with existing queue system // For now, return a simulated queue position const currentQueueSize = await EmergencyCall.countDocuments({ status: { $in: ['escalated', 'in_queue'] }, 'queueInfo.addedAt': { $exists: true } }); return currentQueueSize + 1; } catch (error) { logger.error('Failed to add to emergency queue:', error); return 1; // Default to first position on error } } } module.exports = new EmergencyCallController();