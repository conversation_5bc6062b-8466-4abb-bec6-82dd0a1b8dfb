const analyticsService = require('../services/analyticsService'); const logger = require('../utils/logger'); const { validationResult } = require('express-validator'); class RealTimeAnalyticsController { constructor() { this.activeConnections = new Map(); this.metricsUpdateInterval = null; this.updateFrequency = 30000; // 30 seconds } // Initialize real-time analytics system initialize(websocketManager) { this.websocketManager = websocketManager; this.startMetricsUpdates(); logger.info('Real-time analytics controller initialized'); } // Start periodic metrics updates startMetricsUpdates() { if (this.metricsUpdateInterval) { clearInterval(this.metricsUpdateInterval); } this.metricsUpdateInterval = setInterval(async () => { try { await this.broadcastMetricsUpdate(); } catch (error) { logger.error('Error broadcasting metrics update:', error); } }, this.updateFrequency); } // Stop metrics updates stopMetricsUpdates() { if (this.metricsUpdateInterval) { clearInterval(this.metricsUpdateInterval); this.metricsUpdateInterval = null; } } // Broadcast real-time metrics to connected clients async broadcastMetricsUpdate() { try { const realTimeMetrics = await analyticsService.getRealTimeMetrics(); const dashboardData = await analyticsService.getDashboardAnalytics('1d'); const updatePayload = { type: 'metrics_update', data: { realTimeMetrics, kpis: dashboardData.kpis, timestamp: new Date().toISOString() } }; // Broadcast to analytics namespace if (this.websocketManager && this.websocketManager.namespaces.analytics) { this.websocketManager.namespaces.analytics.broadcastToRole('admin', updatePayload); this.websocketManager.namespaces.analytics.broadcastToRole('supervisor', updatePayload); this.websocketManager.namespaces.analytics.broadcastToRole('analyst', updatePayload); } logger.debug('Broadcasted real-time metrics update'); } catch (error) { logger.error('Error broadcasting metrics update:', error); } } // Get current dashboard analytics async getDashboardAnalytics(req, res) { try { const errors = validationResult(req); if (!errors.isEmpty()) { return res.status(400).json({ success: false, error: 'Validation failed', details: errors.array() }); } const { time_range = '30d', include_predictions = false, metrics = 'all' } = req.query; // Check permissions if (!['admin', 'supervisor', 'analyst'].includes(req.user.role)) { return res.status(403).json({ success: false, error: 'Insufficient permissions for analytics dashboard' }); } const dashboardData = await analyticsService.getDashboardAnalytics(time_range, { includePredictions: include_predictions, metrics: metrics.split(',') }); res.json({ success: true, data: dashboardData, meta: { time_range, include_predictions, generated_at: new Date().toISOString(), cache_status: 'fresh' } }); } catch (error) { logger.error('Error getting dashboard analytics:', error); res.status(500).json({ success: false, error: 'Failed to get dashboard analytics' }); } } // Get real-time metrics async getRealTimeMetrics(req, res) { try { // Check permissions if (!['admin', 'supervisor', 'analyst'].includes(req.user.role)) { return res.status(403).json({ success: false, error: 'Insufficient permissions for real-time metrics' }); } const realTimeMetrics = await analyticsService.getRealTimeMetrics(); res.json({ success: true, data: realTimeMetrics, meta: { update_frequency: `${this.updateFrequency / 1000}s`, next_update: new Date(Date.now() + this.updateFrequency).toISOString() } }); } catch (error) { logger.error('Error getting real-time metrics:', error); res.status(500).json({ success: false, error: 'Failed to get real-time metrics' }); } } // Get agent performance analytics async getAgentPerformance(req, res) { try { const errors = validationResult(req); if (!errors.isEmpty()) { return res.status(400).json({ success: false, error: 'Validation failed', details: errors.array() }); } const { agent_id, time_range = '30d', include_comparisons = false } = req.query; // For agents, only allow viewing their own performance let targetAgentId = agent_id; if (req.user.role === 'agent') { targetAgentId = req.user.id; } const dateRange = analyticsService.getDateRange(time_range); const agentPerformance = await analyticsService.getAgentPerformance(dateRange); // Filter for specific agent if requested let filteredPerformance = agentPerformance; if (targetAgentId) { filteredPerformance = agentPerformance.filter( agent => agent._id.toString() === targetAgentId ); } // Add comparisons if requested let comparisons = null; if (include_comparisons && targetAgentId) { const previousRange = analyticsService.getPreviousDateRange(dateRange); const previousPerformance = await analyticsService.getAgentPerformance(previousRange); const currentAgent = filteredPerformance[0]; const previousAgent = previousPerformance.find( agent => agent._id.toString() === targetAgentId ); if (currentAgent && previousAgent) { comparisons = { conversationsChange: analyticsService.calculateGrowthRate( currentAgent.totalConversations, previousAgent.totalConversations ), resolutionRateChange: currentAgent.resolutionRate - previousAgent.resolutionRate, satisfactionChange: currentAgent.avgSatisfaction - previousAgent.avgSatisfaction }; } } res.json({ success: true, data: { performance: filteredPerformance, comparisons, time_range, agent_id: targetAgentId } }); } catch (error) { logger.error('Error getting agent performance:', error); res.status(500).json({ success: false, error: 'Failed to get agent performance' }); } } // Get customer insights async getCustomerInsights(req, res) { try { const errors = validationResult(req); if (!errors.isEmpty()) { return res.status(400).json({ success: false, error: 'Validation failed', details: errors.array() }); } // Check permissions if (!['admin', 'supervisor', 'analyst'].includes(req.user.role)) { return res.status(403).json({ success: false, error: 'Insufficient permissions for customer insights' }); } const { time_range = '30d', segment, include_churn_analysis = true } = req.query; const dateRange = analyticsService.getDateRange(time_range); // Get customer satisfaction trends const satisfactionTrends = await this.getCustomerSatisfactionTrends(dateRange); // Get interaction patterns const interactionPatterns = await this.getInteractionPatterns(dateRange); // Get sentiment analysis const sentimentAnalysis = await this.getSentimentAnalysis(dateRange); res.json({ success: true, data: { satisfaction_trends: satisfactionTrends, interaction_patterns: interactionPatterns, sentiment_analysis: sentimentAnalysis, time_range, segment, generated_at: new Date().toISOString() } }); } catch (error) { logger.error('Error getting customer insights:', error); res.status(500).json({ success: false, error: 'Failed to get customer insights' }); } } // Get operational efficiency metrics async getOperationalEfficiency(req, res) { try { const errors = validationResult(req); if (!errors.isEmpty()) { return res.status(400).json({ success: false, error: 'Validation failed', details: errors.array() }); } // Check permissions if (!['admin', 'supervisor'].includes(req.user.role)) { return res.status(403).json({ success: false, error: 'Insufficient permissions for operational efficiency metrics' }); } const { time_range = '30d', team_id } = req.query; const dateRange = analyticsService.getDateRange(time_range); // Get efficiency metrics const efficiencyMetrics = await this.getEfficiencyMetrics(dateRange, team_id); // Get resource utilization const resourceUtilization = await this.getResourceUtilization(dateRange); // Get cost analysis const costAnalysis = await this.getCostAnalysis(dateRange); res.json({ success: true, data: { efficiency_metrics: efficiencyMetrics, resource_utilization: resourceUtilization, cost_analysis: costAnalysis, time_range, team_id, generated_at: new Date().toISOString() } }); } catch (error) { logger.error('Error getting operational efficiency:', error); res.status(500).json({ success: false, error: 'Failed to get operational efficiency' }); } } // Trigger manual metrics update async triggerMetricsUpdate(req, res) { try { // Check permissions if (!['admin', 'supervisor'].includes(req.user.role)) { return res.status(403).json({ success: false, error: 'Insufficient permissions to trigger metrics update' }); } await this.broadcastMetricsUpdate(); res.json({ success: true, message: 'Metrics update triggered successfully', timestamp: new Date().toISOString() }); } catch (error) { logger.error('Error triggering metrics update:', error); res.status(500).json({ success: false, error: 'Failed to trigger metrics update' }); } } // Helper methods for customer insights async getCustomerSatisfactionTrends(dateRange) { // Implementation for satisfaction trends return { overall_trend: 'improving', average_rating: 4.2, trend_data: [] }; } async getInteractionPatterns(dateRange) { // Implementation for interaction patterns return { peak_hours: ['14:00-16:00', '20:00-22:00'], preferred_channels: ['chat', 'email'], session_duration: 8.5 }; } async getSentimentAnalysis(dateRange) { // Implementation for sentiment analysis return { positive: 65, neutral: 25, negative: 10, trending: 'positive' }; } async getEfficiencyMetrics(dateRange, teamId) { // Implementation for efficiency metrics return { first_contact_resolution: 78.5, average_handle_time: 6.2, agent_utilization: 85.3 }; } async getResourceUtilization(dateRange) { // Implementation for resource utilization return { agent_capacity: 92, system_load: 67, peak_utilization: 98 }; } async getCostAnalysis(dateRange) { // Implementation for cost analysis return { cost_per_interaction: 2.45, efficiency_savings: 15.2, roi: 245 }; } // Cleanup on shutdown cleanup() { this.stopMetricsUpdates(); this.activeConnections.clear(); logger.info('Real-time analytics controller cleaned up'); } } module.exports = new RealTimeAnalyticsController();