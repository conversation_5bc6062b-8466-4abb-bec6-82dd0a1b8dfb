const CustomerProfile = require('../models/CustomerProfile'); const Notification = require('../models/Notification'); const logger = require('../config/logger'); const subscriptionService = require('../services/subscriptionService'); /** * [USER] CONTRÔLEUR CLIENT * Gestion des profils clients et services associés */ // Obtenir le profil client exports.getCustomerProfile = async (req, res) => { try { let profile = await CustomerProfile.findOne({ userId: req.user._id }); if (!profile) { // Créer un profil par défaut si il n'existe pas profile = await CustomerProfile.create({ userId: req.user._id, subscription: { planName: 'Forfait 2€', planType: '4G', monthlyPrice: 2, dataLimit: 0.05, // 50MB dataUsed: 0, callMinutes: 0, callMinutesUsed: 0, smsLimit: 0, activatedDate: new Date(), renewalDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // +30 jours isActive: true }, activeOptions: [], billingHistory: [], behavioralProfile: { type: 'standard', preferredLanguage: 'fr', communicationPreferences: { tone: 'décontracté', emojis: true, detailLevel: 'simple' } }, interactionHistory: { totalContacts: 0, preferredChannel: 'web', averageResolutionTime: 0, satisfactionScore: 0, frequentIssues: [] }, metrics: { lifetimeValue: 0, nps: 0, csat: 0, monthlyDataUsageTrend: new Array(12).fill(0), supportTicketsCount: 0 } }); logger.info(`Created default customer profile for user: ${req.user._id}`); } res.json(profile); } catch (error) { logger.error('Error getting customer profile:', error); res.status(500).json({ error: 'Erreur lors de la récupération du profil' }); } }; // Mettre à jour le profil client exports.updateCustomerProfile = async (req, res) => { try { const updates = req.body; // Filtrer les champs autorisés à la mise à jour const allowedUpdates = { 'behavioralProfile.communicationPreferences': updates.behavioralProfile?.communicationPreferences, 'location.homeAddress': updates.location?.homeAddress, 'interactionHistory.preferredChannel': updates.interactionHistory?.preferredChannel }; // Nettoyer les valeurs undefined Object.keys(allowedUpdates).forEach(key => { if (allowedUpdates[key] === undefined) { delete allowedUpdates[key]; } }); const profile = await CustomerProfile.findOneAndUpdate( { userId: req.user._id }, { $set: allowedUpdates }, { new: true, runValidators: true } ); if (!profile) { return res.status(404).json({ error: 'Profil client non trouvé' }); } logger.info(`Customer profile updated for user: ${req.user._id}`); res.json(profile); } catch (error) { logger.error('Error updating customer profile:', error); res.status(500).json({ error: 'Erreur lors de la mise à jour du profil' }); } }; // Créer un profil client exports.createCustomerProfile = async (req, res) => { try { const existingProfile = await CustomerProfile.findOne({ userId: req.user._id }); if (existingProfile) { return res.status(409).json({ error: 'Un profil client existe déjà pour cet utilisateur' }); } const profileData = { userId: req.user._id, ...req.body }; const profile = await CustomerProfile.create(profileData); logger.info(`Customer profile created for user: ${req.user._id}`); res.status(201).json(profile); } catch (error) { logger.error('Error creating customer profile:', error); res.status(500).json({ error: 'Erreur lors de la création du profil' }); } }; // Obtenir les factures exports.getInvoices = async (req, res) => { try { const { limit = 12 } = req.query; const profile = await CustomerProfile.findOne({ userId: req.user._id }); if (!profile) { return res.status(404).json({ error: 'Profil client non trouvé' }); } // Simuler des factures si aucune n'existe if (!profile.billingHistory || profile.billingHistory.length === 0) { const mockInvoices = []; const currentDate = new Date(); for (let i = 0; i < Math.min(limit, 6); i++) { const invoiceDate = new Date(currentDate); invoiceDate.setMonth(currentDate.getMonth() - i); mockInvoices.push({ _id: `invoice_${i}`, amount: profile.subscription.monthlyPrice || 2, dueDate: invoiceDate, status: i === 0 ? 'pending' : 'paid', items: [ { description: profile.subscription.planName || 'Forfait 2€', amount: profile.subscription.monthlyPrice || 2 } ] }); } return res.json(mockInvoices); } const invoices = profile.billingHistory .sort((a, b) => new Date(b.dueDate) - new Date(a.dueDate)) .slice(0, parseInt(limit)); res.json(invoices); } catch (error) { logger.error('Error getting invoices:', error); res.status(500).json({ error: 'Erreur lors de la récupération des factures' }); } }; // Obtenir les détails d'une facture exports.getInvoiceDetails = async (req, res) => { try { const { invoiceId } = req.params; const profile = await CustomerProfile.findOne({ userId: req.user._id }); if (!profile) { return res.status(404).json({ error: 'Profil client non trouvé' }); } const invoice = profile.billingHistory.find(inv => inv._id.toString() === invoiceId); if (!invoice) { return res.status(404).json({ error: 'Facture non trouvée' }); } res.json(invoice); } catch (error) { logger.error('Error getting invoice details:', error); res.status(500).json({ error: 'Erreur lors de la récupération de la facture' }); } }; // Payer une facture exports.payInvoice = async (req, res) => { try { const { invoiceId } = req.params; const { paymentMethod } = req.body; const profile = await CustomerProfile.findOne({ userId: req.user._id }); if (!profile) { return res.status(404).json({ error: 'Profil client non trouvé' }); } const invoiceIndex = profile.billingHistory.findIndex(inv => inv._id.toString() === invoiceId); if (invoiceIndex === -1) { return res.status(404).json({ error: 'Facture non trouvée' }); } // Simuler le paiement profile.billingHistory[invoiceIndex].status = 'paid'; profile.billingHistory[invoiceIndex].paidDate = new Date(); profile.billingHistory[invoiceIndex].paymentMethod = paymentMethod; await profile.save(); logger.info(`Invoice ${invoiceId} paid for user: ${req.user._id}`); res.json({ success: true, message: 'Paiement effectué avec succès', invoice: profile.billingHistory[invoiceIndex] }); } catch (error) { logger.error('Error paying invoice:', error); res.status(500).json({ error: 'Erreur lors du paiement' }); } }; // Obtenir les notifications exports.getNotifications = async (req, res) => { try { const { limit = 20, unreadOnly = false } = req.query; const query = { userId: req.user._id }; if (unreadOnly === 'true') { query.status = { $in: ['pending', 'sent'] }; } const notifications = await Notification.find(query) .sort({ createdAt: -1 }) .limit(parseInt(limit)); res.json(notifications); } catch (error) { logger.error('Error getting notifications:', error); res.status(500).json({ error: 'Erreur lors de la récupération des notifications' }); } }; // Marquer une notification comme lue exports.markNotificationAsRead = async (req, res) => { try { const { notificationId } = req.params; const notification = await Notification.findOneAndUpdate( { _id: notificationId, userId: req.user._id }, { status: 'read', readAt: new Date() }, { new: true } ); if (!notification) { return res.status(404).json({ error: 'Notification non trouvée' }); } res.json(notification); } catch (error) { logger.error('Error marking notification as read:', error); res.status(500).json({ error: 'Erreur lors de la mise à jour de la notification' }); } }; // Obtenir la consommation exports.getConsumption = async (req, res) => { try { const consumption = await subscriptionService.getConsumption(req.user._id); res.json(consumption); } catch (error) { logger.error('Error getting consumption:', error); res.status(500).json({ error: 'Erreur lors de la récupération de la consommation' }); } }; // Obtenir l'historique de consommation exports.getConsumptionHistory = async (req, res) => { try { const { months = 12 } = req.query; const profile = await CustomerProfile.findOne({ userId: req.user._id }); if (!profile) { return res.status(404).json({ error: 'Profil client non trouvé' }); } // Simuler un historique de consommation const history = []; const currentDate = new Date(); for (let i = 0; i < parseInt(months); i++) { const date = new Date(currentDate); date.setMonth(currentDate.getMonth() - i); history.push({ month: date.toISOString().substring(0, 7), // YYYY-MM dataUsed: Math.random() * (profile.subscription.dataLimit || 1), callMinutes: Math.random() * (profile.subscription.callMinutes || 60), smsCount: Math.floor(Math.random() * 100) }); } res.json(history.reverse()); } catch (error) { logger.error('Error getting consumption history:', error); res.status(500).json({ error: 'Erreur lors de la récupération de l\'historique' }); } }; // Créer un ticket de support exports.createSupportTicket = async (req, res) => { try { const { subject, description, category, priority = 'medium' } = req.body; // Simuler la création d'un ticket const ticket = { _id: `ticket_${Date.now()}`, userId: req.user._id, subject, description, category, priority, status: 'open', createdAt: new Date(), updatedAt: new Date() }; // Mettre à jour le compteur de tickets dans le profil await CustomerProfile.findOneAndUpdate( { userId: req.user._id }, { $inc: { 'metrics.supportTicketsCount': 1 } } ); logger.info(`Support ticket created for user: ${req.user._id}`); res.status(201).json(ticket); } catch (error) { logger.error('Error creating support ticket:', error); res.status(500).json({ error: 'Erreur lors de la création du ticket' }); } }; // Obtenir les tickets de support exports.getSupportTickets = async (req, res) => { try { const { limit = 10, status } = req.query; // Simuler des tickets de support const mockTickets = [ { _id: 'ticket_1', subject: 'Problème de connexion', category: 'technique', priority: 'high', status: 'open', createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000) }, { _id: 'ticket_2', subject: 'Question sur ma facture', category: 'billing', priority: 'medium', status: 'resolved', createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } ]; let tickets = mockTickets; if (status) { tickets = tickets.filter(ticket => ticket.status === status); } res.json(tickets.slice(0, parseInt(limit))); } catch (error) { logger.error('Error getting support tickets:', error); res.status(500).json({ error: 'Erreur lors de la récupération des tickets' }); } }; // Obtenir les détails d'un ticket exports.getSupportTicketDetails = async (req, res) => { try { const { ticketId } = req.params; // Simuler les détails d'un ticket const ticketDetails = { _id: ticketId, userId: req.user._id, subject: 'Problème de connexion', description: 'Je n\'arrive pas à me connecter au réseau depuis ce matin.', category: 'technique', priority: 'high', status: 'open', createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), messages: [ { sender: 'user', message: 'Je n\'arrive pas à me connecter au réseau depuis ce matin.', timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000) }, { sender: 'agent', message: 'Bonjour, nous allons vérifier votre connexion. Pouvez-vous redémarrer votre téléphone ?', timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000) } ] }; res.json(ticketDetails); } catch (error) { logger.error('Error getting support ticket details:', error); res.status(500).json({ error: 'Erreur lors de la récupération du ticket' }); } };