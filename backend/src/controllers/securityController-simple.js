const User = require('../models/User');
const logger = require('../config/logger');
const { validateObjectId } = require('../utils/validators');

/**
 * CONTRÔLEUR DE SÉCURITÉ - VERSION SIMPLIFIÉE
 * Gestion de base de la sécurité
 */

/**
 * Obtenir le statut de sécurité du compte
 */
exports.getSecurityStatus = async (req, res) => {
  try {
    const userId = req.user._id;
    const user = await User.findById(userId).select('security preferences email');

    const securityStatus = {
      twoFactorEnabled: user.security?.twoFactor?.enabled || false,
      twoFactorActivatedAt: user.security?.twoFactor?.activatedAt,
      remainingBackupCodes: user.security?.twoFactor?.backupCodes?.filter(code => !code.used).length || 0,
      riskScore: user.security?.riskScore || 0,
      lastKnownLocation: user.security?.lastKnownLocation,
      securityRecommendations: []
    };

    // Générer des recommandations
    if (!securityStatus.twoFactorEnabled) {
      securityStatus.securityRecommendations.push({
        type: 'enable_2fa',
        priority: 'high',
        title: 'Activez la double authentification',
        description: 'Sécurisez votre compte avec la 2FA',
        action: 'generate_2fa_secret'
      });
    }

    if (securityStatus.remainingBackupCodes < 3 && securityStatus.twoFactorEnabled) {
      securityStatus.securityRecommendations.push({
        type: 'regenerate_backup_codes',
        priority: 'medium',
        title: 'Régénérez vos codes de récupération',
        description: 'Il vous reste peu de codes de récupération',
        action: 'regenerate_backup_codes'
      });
    }

    if (securityStatus.riskScore > 0.5) {
      securityStatus.securityRecommendations.push({
        type: 'review_activity',
        priority: 'high',
        title: 'Activité suspecte détectée',
        description: 'Vérifiez les connexions récentes à votre compte',
        action: 'review_security_logs'
      });
    }

    res.json({
      success: true,
      securityStatus,
      lastChecked: new Date()
    });
  } catch (error) {
    logger.error('Error getting security status:', error);
    res.status(500).json({
      error: 'Erreur lors de la récupération du statut de sécurité',
      details: [error.message]
    });
  }
};

/**
 * Analyser une tentative de connexion pour détecter les risques
 */
exports.analyzeLoginAttempt = async (req, res) => {
  try {
    const { userId } = req.body;
    const clientInfo = {
      ipAddress: req.ip || req.connection?.remoteAddress,
      userAgent: req.headers['user-agent'],
      location: req.body.location // Optionnel, fourni par le client
    };

    validateObjectId(userId, 'ID utilisateur');

    // Analyse simplifiée
    const analysis = {
      riskScore: 0.1, // Score de risque faible par défaut
      requiresAdditionalAuth: false,
      requiresManualReview: false,
      factors: []
    };

    res.json({
      success: true,
      analysis: {
        riskScore: analysis.riskScore,
        riskLevel: analysis.riskScore < 0.3 ? 'low' : analysis.riskScore < 0.7 ? 'medium' : 'high',
        requiresAdditionalAuth: analysis.requiresAdditionalAuth,
        requiresManualReview: analysis.requiresManualReview,
        detectedFactors: analysis.factors,
        recommendations: analysis.requiresAdditionalAuth ? [
          'Vérification par code 2FA requis',
          'Validation d\'identité supplémentaire'
        ] : []
      },
      clientInfo: {
        ipAddress: clientInfo.ipAddress,
        location: clientInfo.location?.city || 'Non déterminé'
      }
    });
  } catch (error) {
    logger.error('Error analyzing login attempt:', error);
    res.status(500).json({
      error: 'Erreur lors de l\'analyse de sécurité',
      details: [error.message]
    });
  }
};

/**
 * Obtenir les logs de sécurité (activités récentes)
 */
exports.getSecurityLogs = async (req, res) => {
  try {
    const userId = req.user._id;
    const { limit = 20 } = req.query;

    // En production, récupérer depuis une vraie base de données de logs
    const securityLogs = [
      {
        id: '1',
        action: 'login',
        timestamp: new Date(),
        ipAddress: req.ip,
        userAgent: req.headers['user-agent'],
        location: 'Paris, France',
        riskScore: 0.1,
        status: 'success'
      }
      // Plus de logs...
    ];

    res.json({
      success: true,
      logs: securityLogs.slice(0, parseInt(limit)),
      total: securityLogs.length,
      summary: {
        totalEvents: securityLogs.length,
        suspiciousEvents: securityLogs.filter(log => log.riskScore > 0.5).length,
        lastActivity: securityLogs[0]?.timestamp
      }
    });
  } catch (error) {
    logger.error('Error getting security logs:', error);
    res.status(500).json({
      error: 'Erreur lors de la récupération des logs',
      details: [error.message]
    });
  }
};

// Placeholder methods for 2FA functionality
exports.generateTwoFactorSecret = async (req, res) => {
  res.status(501).json({ error: 'Fonctionnalité 2FA en cours de développement' });
};

exports.enableTwoFactor = async (req, res) => {
  res.status(501).json({ error: 'Fonctionnalité 2FA en cours de développement' });
};

exports.verifyTwoFactor = async (req, res) => {
  res.status(501).json({ error: 'Fonctionnalité 2FA en cours de développement' });
};

exports.disableTwoFactor = async (req, res) => {
  res.status(501).json({ error: 'Fonctionnalité 2FA en cours de développement' });
};

exports.regenerateBackupCodes = async (req, res) => {
  res.status(501).json({ error: 'Fonctionnalité 2FA en cours de développement' });
};

exports.requireSecurityVerification = async (req, res, next) => {
  // Middleware simplifié - passer directement
  next();
};
