/** * ============================================= * [TARGET] DASHBOARD CONTROLLER * Handles all dashboard-related API endpoints * Integrates with simulation database schemas and authentication * ============================================= */ const mongoose = require('mongoose'); const { SimulationScenario, SimulationSession, AgentProgress } = require('../database/mongoSchemas'); const logger = require('../config/logger'); /** * @desc Get all available simulation scenarios with filtering * @route GET /api/dashboard/simulations * @access Private */ exports.getSimulations = async (req, res) => { try { const { difficulty, category, tags, limit = 50, offset = 0 } = req.query; // Build filter object const filter = {}; if (difficulty) { filter.difficulty = difficulty; } if (category) { filter.category = category; } if (tags) { const tagArray = tags.split(',').map(tag => tag.trim()); filter.tags = { $in: tagArray }; } // Execute query with pagination const scenarios = await SimulationScenario .find(filter) .select('title description difficulty category tags customer_profile context learning_objectives success_criteria expected_resolution_time popularity effectiveness_score created_at') .sort({ popularity: -1, effectiveness_score: -1, created_at: -1 }) .limit(parseInt(limit)) .skip(parseInt(offset)) .lean(); // Get total count for pagination const totalCount = await SimulationScenario.countDocuments(filter); // Transform data to match frontend expectations const transformedScenarios = scenarios.map(scenario => ({ id: scenario._id.toString(), title: scenario.title, description: scenario.description, difficulty: scenario.difficulty, category: scenario.category, tags: scenario.tags || [], customer_profile: { id: `customer_${scenario._id}`, name: `Customer for ${scenario.title}`, age: Math.floor(Math.random() * 40) + 25, // Random age 25-65 personality: mapEmotionalStateToPersonality(scenario.customer_profile?.emotional_state || 'calm'), issue_type: scenario.category, complexity: scenario.customer_profile?.issue_complexity || 'moderate', platform: 'whatsapp', // Default platform history: [], satisfaction_threshold: scenario.success_criteria?.customer_satisfaction_target || 8 }, expected_resolution_time: scenario.expected_resolution_time || 15, success_criteria: { min_satisfaction: scenario.success_criteria?.customer_satisfaction_target || 8, max_resolution_time: scenario.success_criteria?.max_resolution_time || 30, required_actions: ['active_listening', 'problem_identification', 'solution_proposal'], avoid_actions: ['interrupting', 'dismissive_language', 'premature_escalation'] }, learning_objectives: scenario.learning_objectives || [], popularity: scenario.popularity || 0, effectiveness_score: scenario.effectiveness_score || 0, created_at: scenario.created_at })); // Response with pagination metadata res.json({ success: true, data: { scenarios: transformedScenarios, pagination: { total: totalCount, limit: parseInt(limit), offset: parseInt(offset), hasMore: (parseInt(offset) + parseInt(limit)) < totalCount }, filters: { difficulty: difficulty || null, category: category || null, tags: tags ? tags.split(',').map(tag => tag.trim()) : [] } } }); logger.info(`Dashboard simulations retrieved: ${transformedScenarios.length} scenarios for user ${req.user.id}`); } catch (error) { logger.error('Error fetching dashboard simulations:', error); res.status(500).json({ success: false, error: 'Failed to fetch simulation scenarios', message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error' }); } }; /** * Helper function to map emotional state to personality */ function mapEmotionalStateToPersonality(emotionalState) { const mapping = { 'calm': 'friendly', 'frustrated': 'frustrated', 'angry': 'demanding', 'confused': 'confused', 'urgent': 'demanding' }; return mapping[emotionalState] || 'calm'; } /** * @desc Get performance metrics for a specific agent * @route GET /api/dashboard/metrics/:agentId * @access Private */ exports.getAgentMetrics = async (req, res) => { try { const { agentId } = req.params; // Authorization check: users can only view their own metrics unless admin if (req.user.role !== 'admin' && req.user.id !== agentId) { return res.status(403).json({ success: false, error: 'Access denied. You can only view your own metrics.' }); } // Get agent progress data let agentProgress; try { // Try to find by ObjectId if it's a valid ObjectId, otherwise treat as string if (mongoose.Types.ObjectId.isValid(agentId)) { agentProgress = await AgentProgress.findOne({ agent_id: agentId }).lean(); } else { // For non-ObjectId agent IDs, return default metrics agentProgress = null; } } catch (error) { agentProgress = null; } if (!agentProgress) { // Return default metrics if no progress found return res.json({ success: true, data: { agent_id: agentId, total_sessions: 0, completed_sessions: 0, average_score: 0, skill_levels: { empathy: 0, efficiency: 0, accuracy: 0, communication: 0 }, badges_earned: [], next_milestone: { name: 'First Simulation', description: 'Complete your first simulation session', progress: 0, target: 1 } } }); } res.json({ success: true, data: agentProgress }); logger.info(`Agent metrics retrieved for agent ${agentId} by user ${req.user.id}`); } catch (error) { logger.error('Error fetching agent metrics:', error); res.status(500).json({ success: false, error: 'Failed to fetch agent metrics', message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error' }); } }; /** * @desc Get ranked agent performance data (leaderboard) * @route GET /api/dashboard/leaderboard * @access Private */ exports.getLeaderboard = async (req, res) => { try { const { limit = 10, timeframe = 'month' } = req.query; // Build date filter based on timeframe let dateFilter = {}; const now = new Date(); switch (timeframe) { case 'week': dateFilter = { updated_at: { $gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) } }; break; case 'month': dateFilter = { updated_at: { $gte: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) } }; break; case 'all': default: dateFilter = {}; break; } // Get top agents by average score const leaderboard = await AgentProgress .find(dateFilter) .select('agent_id total_sessions completed_sessions average_score skill_levels badges_earned updated_at') .sort({ average_score: -1, completed_sessions: -1 }) .limit(parseInt(limit)) .lean(); // Transform data for frontend const transformedLeaderboard = leaderboard.map((entry, index) => ({ rank: index + 1, agent_id: entry.agent_id.toString(), agent_name: `Agent ${entry.agent_id.toString().slice(-4)}`, // Use last 4 chars of ID as name total_sessions: entry.total_sessions || 0, completed_sessions: entry.completed_sessions || 0, average_score: Math.round(entry.average_score || 0), skill_levels: entry.skill_levels || { empathy: 0, efficiency: 0, accuracy: 0, communication: 0 }, badges_count: entry.badges_earned?.length || 0, last_active: entry.updated_at })); res.json({ success: true, data: { leaderboard: transformedLeaderboard, timeframe, total_agents: transformedLeaderboard.length, generated_at: new Date() } }); logger.info(`Leaderboard retrieved: ${transformedLeaderboard.length} entries for timeframe ${timeframe}`); } catch (error) { logger.error('Error fetching leaderboard:', error); res.status(500).json({ success: false, error: 'Failed to fetch leaderboard', message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error' }); } }; /** * @desc Get real-time simulation progress data * @route GET /api/dashboard/progress/:sessionId * @access Private (Session owner or Admin) */ exports.getSessionProgress = async (req, res) => { try { const { sessionId } = req.params; // Find the session const session = await SimulationSession.findById(sessionId) .populate('scenario_id', 'title difficulty category expected_resolution_time') .lean(); if (!session) { return res.status(404).json({ success: false, error: 'Simulation session not found' }); } // Authorization check: users can only view their own sessions unless admin if (req.user.role !== 'admin' && session.agent_id.toString() !== req.user.id) { return res.status(403).json({ success: false, error: 'Access denied. You can only view your own sessions.' }); } // Calculate progress percentage const expectedTime = session.scenario_id?.expected_resolution_time || 600; // Default 10 minutes const elapsedTime = session.duration || 0; const progressPercentage = Math.min(Math.round((elapsedTime / expectedTime) * 100), 100); // Transform data for frontend const progressData = { session_id: session._id.toString(), scenario: { id: session.scenario_id?._id?.toString() || session.scenario_id, title: session.scenario_id?.title || 'Unknown Scenario', difficulty: session.scenario_id?.difficulty || 'unknown', category: session.scenario_id?.category || 'general', expected_time: expectedTime }, status: session.status, progress_percentage: progressPercentage, elapsed_time: elapsedTime, messages_count: session.messages_count || 0, current_score: session.score || 0, customer_satisfaction: session.customer_satisfaction || 0, started_at: session.started_at, last_message_at: session.last_message_at || session.updated_at, is_active: session.status === 'active' }; res.json({ success: true, data: progressData }); logger.info(`Session progress retrieved for session ${sessionId} by user ${req.user.id}`); } catch (error) { logger.error('Error fetching session progress:', error); res.status(500).json({ success: false, error: 'Failed to fetch session progress', message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error' }); } }; /** * @desc Get user session history with status filtering * @route GET /api/dashboard/sessions * @access Private */ exports.getUserSessions = async (req, res) => { try { const { status, agent_id, page = 1, limit = 20, sortBy = 'created_at', sortOrder = 'desc' } = req.query; // Build filter object const filter = {}; // Role-based filtering: non-admin users can only see their own sessions if (req.user.role !== 'admin') { filter.agent_id = req.user.id; } else if (agent_id) { filter.agent_id = agent_id; } if (status) { filter.status = status; } // Build sort object const sort = {}; sort[sortBy] = sortOrder === 'desc' ? -1 : 1; // Execute database query const sessions = await SimulationSession.find(filter) .populate('scenario_id', 'title difficulty category') .sort(sort) .limit(limit * 1) .skip((page - 1) * limit) .lean(); // Get total count for pagination const totalCount = await SimulationSession.countDocuments(filter); // Transform data for frontend const transformedSessions = sessions.map(session => ({ id: session._id.toString(), scenario: { id: session.scenario_id?._id?.toString() || session.scenario_id, title: session.scenario_id?.title || 'Unknown Scenario', difficulty: session.scenario_id?.difficulty || 'unknown', category: session.scenario_id?.category || 'general' }, agent_id: session.agent_id.toString(), status: session.status, score: session.score || 0, duration: session.duration || 0, messages_count: session.messages_count || 0, customer_satisfaction: session.customer_satisfaction || 0, started_at: session.started_at, completed_at: session.completed_at, created_at: session.created_at, updated_at: session.updated_at })); res.json({ success: true, data: { sessions: transformedSessions, pagination: { current_page: parseInt(page), total_pages: Math.ceil(totalCount / limit), total_count: totalCount, per_page: parseInt(limit) }, filters: { status: status || null, agent_id: agent_id || null } } }); logger.info(`User sessions retrieved: ${transformedSessions.length} sessions for user ${req.user.id}`); } catch (error) { logger.error('Error fetching user sessions:', error); res.status(500).json({ success: false, error: 'Failed to fetch user sessions', message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error' }); } }; exports.startSimulation = async (req, res) => { res.status(501).json({ success: false, error: 'Endpoint not yet implemented' }); }; exports.stopSimulation = async (req, res) => { res.status(501).json({ success: false, error: 'Endpoint not yet implemented' }); }; /** * @desc Get dashboard summary statistics for admin users * @route GET /api/dashboard/analytics * @access Private (Admin only) */ exports.getDashboardAnalytics = async (req, res) => { try { // Admin-only access if (req.user.role !== 'admin') { return res.status(403).json({ success: false, error: 'Access denied. Admin privileges required.' }); } const { timeframe = 'month' } = req.query; // Build date filter based on timeframe let dateFilter = {}; const now = new Date(); switch (timeframe) { case 'week': dateFilter = { created_at: { $gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) } }; break; case 'month': dateFilter = { created_at: { $gte: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) } }; break; case 'year': dateFilter = { created_at: { $gte: new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000) } }; break; case 'all': default: dateFilter = {}; break; } // Parallel queries for analytics data const [ totalScenarios, activeScenarios, totalSessions, completedSessions, totalAgents, activeAgents, averageScore, topCategories, recentActivity ] = await Promise.all([ // Total scenarios SimulationScenario.countDocuments({}), // Active scenarios SimulationScenario.countDocuments({ is_active: true }), // Total sessions in timeframe SimulationSession.countDocuments(dateFilter), // Completed sessions in timeframe SimulationSession.countDocuments({ ...dateFilter, status: 'completed' }), // Total agents AgentProgress.countDocuments({}), // Active agents (with sessions in timeframe) SimulationSession.distinct('agent_id', dateFilter).then(agents => agents.length), // Average score across all completed sessions SimulationSession.aggregate([ { $match: { ...dateFilter, status: 'completed', score: { $exists: true } } }, { $group: { _id: null, avgScore: { $avg: '$score' } } } ]).then(result => result[0]?.avgScore || 0), // Top categories by session count SimulationSession.aggregate([ { $match: dateFilter }, { $lookup: { from: 'simulationscenarios', localField: 'scenario_id', foreignField: '_id', as: 'scenario' } }, { $unwind: '$scenario' }, { $group: { _id: '$scenario.category', count: { $sum: 1 } } }, { $sort: { count: -1 } }, { $limit: 5 } ]), // Recent activity (last 10 sessions) SimulationSession.find(dateFilter) .populate('scenario_id', 'title category') .sort({ created_at: -1 }) .limit(10) .lean() ]); // Calculate completion rate const completionRate = totalSessions > 0 ? Math.round((completedSessions / totalSessions) * 100) : 0; // Transform recent activity const transformedActivity = recentActivity.map(session => ({ id: session._id.toString(), scenario_title: session.scenario_id?.title || 'Unknown', category: session.scenario_id?.category || 'general', agent_id: session.agent_id.toString(), status: session.status, score: session.score || 0, created_at: session.created_at })); res.json({ success: true, data: { overview: { total_scenarios: totalScenarios, active_scenarios: activeScenarios, total_sessions: totalSessions, completed_sessions: completedSessions, completion_rate: completionRate, total_agents: totalAgents, active_agents: activeAgents, average_score: Math.round(averageScore) }, categories: topCategories.map(cat => ({ name: cat._id || 'Unknown', sessions: cat.count })), recent_activity: transformedActivity, timeframe, generated_at: new Date() } }); logger.info(`Dashboard analytics retrieved for timeframe ${timeframe} by admin ${req.user.id}`); } catch (error) { logger.error('Error fetching dashboard analytics:', error); res.status(500).json({ success: false, error: 'Failed to fetch dashboard analytics', message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error' }); } }; /** * @desc Get detailed information about a specific scenario * @route GET /api/dashboard/scenarios/:scenarioId * @access Private (All authenticated users) */ exports.getScenarioDetails = async (req, res) => { try { const { scenarioId } = req.params; // Find the scenario const scenario = await SimulationScenario.findById(scenarioId).lean(); if (!scenario) { return res.status(404).json({ success: false, error: 'Simulation scenario not found' }); } // Get session statistics for this scenario const [totalSessions, completedSessions, averageScore, averageDuration] = await Promise.all([ SimulationSession.countDocuments({ scenario_id: scenarioId }), SimulationSession.countDocuments({ scenario_id: scenarioId, status: 'completed' }), SimulationSession.aggregate([ { $match: { scenario_id: scenario._id, status: 'completed', score: { $exists: true } } }, { $group: { _id: null, avgScore: { $avg: '$score' } } } ]).then(result => result[0]?.avgScore || 0), SimulationSession.aggregate([ { $match: { scenario_id: scenario._id, status: 'completed', duration: { $exists: true } } }, { $group: { _id: null, avgDuration: { $avg: '$duration' } } } ]).then(result => result[0]?.avgDuration || 0) ]); // Transform data for frontend const scenarioDetails = { id: scenario._id.toString(), title: scenario.title, description: scenario.description, difficulty: scenario.difficulty, category: scenario.category, tags: scenario.tags || [], customer_profile: { id: scenario.customer_profile.id, name: scenario.customer_profile.name, age: scenario.customer_profile.age, personality: scenario.customer_profile.personality, issue_type: scenario.customer_profile.issue_type, complexity: scenario.customer_profile.complexity, platform: scenario.customer_profile.platform, history: scenario.customer_profile.history || [], satisfaction_threshold: scenario.customer_profile.satisfaction_threshold }, context: scenario.context, learning_objectives: scenario.learning_objectives, success_criteria: scenario.success_criteria, expected_resolution_time: scenario.expected_resolution_time, popularity: scenario.popularity, effectiveness_score: scenario.effectiveness_score, is_active: scenario.is_active !== false, statistics: { total_sessions: totalSessions, completed_sessions: completedSessions, completion_rate: totalSessions > 0 ? Math.round((completedSessions / totalSessions) * 100) : 0, average_score: Math.round(averageScore), average_duration: Math.round(averageDuration) }, created_at: scenario.created_at, updated_at: scenario.updated_at }; res.json({ success: true, data: scenarioDetails }); logger.info(`Scenario details retrieved for scenario ${scenarioId} by user ${req.user.id}`); } catch (error) { logger.error('Error fetching scenario details:', error); res.status(500).json({ success: false, error: 'Failed to fetch scenario details', message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error' }); } };