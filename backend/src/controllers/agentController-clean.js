const Conversation = require('../models/Conversation');
const Message = require('../models/Message');
const User = require('../models/User');
const logger = require('../config/logger');
const { validateObjectId, validateMessage } = require('../utils/validators');

/**
 * INTERFACE AGENT - CO-PILOTE
 * Système de prise de contrôle par les agents humains
 */

/**
 * Obtenir le tableau de bord agent en temps réel
 */
exports.getAgentDashboard = async (req, res) => {
  try {
    const agentId = req.user._id;

    // Statistiques de l'agent
    const assignedConversations = await Conversation.countDocuments({
      agentId,
      status: { $in: ['escalated', 'active'] }
    });

    const resolvedToday = await Conversation.countDocuments({
      agentId,
      status: 'resolved',
      endedAt: { $gte: new Date(new Date().setHours(0, 0, 0, 0)) }
    });

    // Conversations en attente d'escalade
    const pendingEscalation = await Conversation.find({
      status: 'active',
      agentId: null,
      // Critères d'escalade automatique
      $or: [
        { startedAt: { $lt: new Date(Date.now() - 10 * 60 * 1000) } }, // > 10 min
        { 'metadata.escalationRequested': true }
      ]
    })
      .populate('userId', 'email profile')
      .sort({ startedAt: 1 })
      .limit(10);

    // Messages récents nécessitant attention
    const recentMessages = await Message.aggregate([
      {
        $lookup: {
          from: 'conversations',
          localField: 'conversationId',
          foreignField: '_id',
          as: 'conversation'
        }
      },
      {
        $match: {
          sender: 'user',
          timestamp: { $gte: new Date(Date.now() - 30 * 60 * 1000) }, // 30 min
          'conversation.agentId': agentId
        }
      },
      { $sort: { timestamp: -1 } },
      { $limit: 20 }
    ]);

    // Métriques de performance
    const performanceStats = await Conversation.aggregate([
      {
        $match: {
          agentId,
          endedAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
        }
      },
      {
        $group: {
          _id: null,
          avgResolutionTime: { $avg: { $subtract: ['$endedAt', '$startedAt'] } },
          avgSatisfaction: { $avg: '$satisfaction.rating' },
          totalResolved: { $sum: 1 }
        }
      }
    ]);

    res.json({
      success: true,
      dashboard: {
        agent: {
          id: agentId,
          name: `${req.user.profile?.firstName} ${req.user.profile?.lastName}`,
          email: req.user.email
        },
        statistics: {
          activeConversations: assignedConversations,
          resolvedToday,
          pendingEscalation: pendingEscalation.length,
          avgResolutionTime: performanceStats[0]?.avgResolutionTime || 0,
          avgSatisfaction: performanceStats[0]?.avgSatisfaction || 0
        },
        pendingEscalation,
        recentMessages: recentMessages.slice(0, 10),
        lastUpdated: new Date()
      }
    });
  } catch (error) {
    logger.error('Error fetching agent dashboard:', error);
    res.status(500).json({ error: 'Erreur lors du chargement du tableau de bord' });
  }
};

/**
 * Prendre le contrôle d'une conversation (escalade)
 */
exports.takeControl = async (req, res) => {
  try {
    const { conversationId } = req.params;
    const agentId = req.user._id;

    // Validation
    validateObjectId(conversationId, 'ID de conversation');

    const conversation = await Conversation.findById(conversationId);
    if (!conversation) {
      return res.status(404).json({ error: 'Conversation non trouvée' });
    }

    // Vérifier que la conversation peut être escaladée
    if (conversation.status === 'resolved' || conversation.status === 'abandoned') {
      return res.status(400).json({
        error: 'Conversation fermée',
        details: ['Cette conversation est déjà terminée']
      });
    }

    // Vérifier si un autre agent a déjà pris le contrôle
    if (conversation.agentId && conversation.agentId.toString() !== agentId.toString()) {
      const existingAgent = await User.findById(conversation.agentId);
      return res.status(409).json({
        error: 'Conversation déjà assignée',
        details: [`Cette conversation est assignée à ${existingAgent?.email}`]
      });
    }

    // Prendre le contrôle
    conversation.agentId = agentId;
    conversation.status = 'escalated';
    await conversation.save();

    // Message automatique d'escalade
    const escalationMessage = new Message({
      conversationId,
      sender: 'agent',
      content: {
        text: `Un agent Free Mobile a pris le contrôle de cette conversation. ${req.user.profile?.firstName || 'L\'agent'} va vous aider personnellement.`,
        type: 'text'
      },
      metadata: {
        systemMessage: true,
        escalation: true,
        agentId
      }
    });

    await escalationMessage.save();

    // Notifier via WebSocket
    const io = req.app.get('io');
    if (io) {
      io.to(`conversation_${conversationId}`).emit('agent_joined', {
        agent: {
          id: agentId,
          name: `${req.user.profile?.firstName} ${req.user.profile?.lastName}`,
          email: req.user.email
        },
        message: escalationMessage
      });
    }

    logger.info(`Agent ${agentId} took control of conversation ${conversationId}`);

    res.json({
      success: true,
      message: 'Contrôle de la conversation pris avec succès',
      conversation: {
        id: conversation._id,
        status: conversation.status,
        agentId: conversation.agentId
      }
    });
  } catch (error) {
    logger.error('Error taking control:', error);
    res.status(500).json({ error: 'Erreur lors de la prise de contrôle' });
  }
};

/**
 * Envoyer un message en tant qu'agent
 */
exports.sendAgentMessage = async (req, res) => {
  try {
    const { conversationId } = req.params;
    const { message, messageType = 'text', quickActions } = req.body;
    const agentId = req.user._id;

    // Validations
    validateObjectId(conversationId, 'ID de conversation');
    const messageValidation = validateMessage(message);
    if (!messageValidation.isValid) {
      return res.status(400).json({
        error: 'Message invalide',
        details: messageValidation.errors
      });
    }

    // Vérifier que l'agent contrôle cette conversation
    const conversation = await Conversation.findById(conversationId);
    if (!conversation) {
      return res.status(404).json({ error: 'Conversation non trouvée' });
    }

    if (!conversation.agentId || conversation.agentId.toString() !== agentId.toString()) {
      return res.status(403).json({
        error: 'Accès refusé',
        details: ['Vous ne contrôlez pas cette conversation']
      });
    }

    // Créer le message agent
    const agentMessage = new Message({
      conversationId,
      sender: 'agent',
      content: {
        text: messageValidation.cleanMessage,
        type: messageType,
        quickActions: quickActions || []
      },
      metadata: {
        agentId,
        agentName: `${req.user.profile?.firstName} ${req.user.profile?.lastName}`,
        responseTime: Date.now() - new Date(conversation.startedAt).getTime()
      }
    });

    await agentMessage.save();

    // Notifier via WebSocket
    const io = req.app.get('io');
    if (io) {
      io.to(`conversation_${conversationId}`).emit('agent_message', {
        message: agentMessage,
        agent: {
          id: agentId,
          name: agentMessage.metadata.agentName
        }
      });
    }

    logger.info(`Agent message sent: ${conversationId} by ${agentId}`);

    res.json({
      success: true,
      message: agentMessage,
      timestamp: agentMessage.timestamp
    });
  } catch (error) {
    logger.error('Error sending agent message:', error);
    res.status(500).json({ error: 'Erreur lors de l\'envoi du message' });
  }
};
