const Conversation = require('../models/Conversation'); const Message = require('../models/Message'); const User = require('../models/User'); const logger = require('../config/logger'); exports.getDashboard = async (req, res) => { try { const today = new Date(); const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000); // Get dashboard statistics const totalConversations = await Conversation.countDocuments(); const activeConversations = await Conversation.countDocuments({ status: 'active' }); const resolvedConversations = await Conversation.countDocuments({ status: 'resolved' }); const conversationsToday = await Conversation.countDocuments({ startedAt: { $gte: new Date(today.setHours(0, 0, 0, 0)) } }); // Get satisfaction scores const satisfactionData = await Conversation.aggregate([ { $match: { 'satisfaction.rating': { $exists: true } } }, { $group: { _id: null, avgRating: { $avg: '$satisfaction.rating' } } } ]); // Get conversations by status const conversationsByStatus = await Conversation.aggregate([ { $group: { _id: '$status', count: { $sum: 1 } } } ]); // Get recent conversations const recentConversations = await Conversation.find() .populate('userId', 'email profile') .populate('agentId', 'email profile') .sort({ startedAt: -1 }) .limit(10) .lean(); res.json({ statistics: { totalConversations, activeConversations, resolvedConversations, conversationsToday, avgSatisfaction: satisfactionData[0]?.avgRating || 0, }, conversationsByStatus, recentConversations, }); } catch (error) { logger.error('Error fetching dashboard:', error); res.status(500).json({ error: 'Failed to fetch dashboard' }); } }; exports.getAllConversations = async (req, res) => { try { const page = parseInt(req.query.page) || 1; const limit = parseInt(req.query.limit) || 20; const status = req.query.status; const search = req.query.search; const filter = {}; if (status) filter.status = status; if (search) { filter.$or = [ { sessionId: { $regex: search, $options: 'i' } }, { 'userId.email': { $regex: search, $options: 'i' } } ]; } const conversations = await Conversation.find(filter) .populate('userId', 'email profile') .populate('agentId', 'email profile') .sort({ startedAt: -1 }) .skip((page - 1) * limit) .limit(limit) .lean(); const total = await Conversation.countDocuments(filter); res.json({ conversations, pagination: { current: page, pages: Math.ceil(total / limit), total, }, }); } catch (error) { logger.error('Error fetching conversations:', error); res.status(500).json({ error: 'Failed to fetch conversations' }); } }; exports.getConversationDetails = async (req, res) => { try { const { conversationId } = req.params; const conversation = await Conversation.findById(conversationId) .populate('userId', 'email profile') .populate('agentId', 'email profile') .lean(); if (!conversation) { return res.status(404).json({ error: 'Conversation not found' }); } const messages = await Message.find({ conversationId }) .sort({ timestamp: 1 }) .lean(); res.json({ conversation, messages, }); } catch (error) { logger.error('Error fetching conversation details:', error); res.status(500).json({ error: 'Failed to fetch conversation details' }); } }; exports.assignAgent = async (req, res) => { try { const { conversationId } = req.params; const { agentId } = req.body; const conversation = await Conversation.findById(conversationId); if (!conversation) { return res.status(404).json({ error: 'Conversation not found' }); } const agent = await User.findOne({ _id: agentId, role: { $in: ['agent', 'admin'] } }); if (!agent) { return res.status(400).json({ error: 'Invalid agent' }); } conversation.agentId = agentId; conversation.status = 'escalated'; await conversation.save(); logger.info(`Conversation ${conversationId} assigned to agent ${agentId}`); res.json({ message: 'Agent assigned successfully' }); } catch (error) { logger.error('Error assigning agent:', error); res.status(500).json({ error: 'Failed to assign agent' }); } }; exports.closeConversation = async (req, res) => { try { const { conversationId } = req.params; const { satisfaction } = req.body; const conversation = await Conversation.findById(conversationId); if (!conversation) { return res.status(404).json({ error: 'Conversation not found' }); } conversation.status = 'resolved'; conversation.endedAt = new Date(); if (satisfaction) { conversation.satisfaction = satisfaction; } await conversation.save(); logger.info(`Conversation ${conversationId} closed`); res.json({ message: 'Conversation closed successfully' }); } catch (error) { logger.error('Error closing conversation:', error); res.status(500).json({ error: 'Failed to close conversation' }); } }; exports.getAnalytics = async (req, res) => { try { const { startDate, endDate } = req.query; const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); const end = endDate ? new Date(endDate) : new Date(); // Conversations over time const conversationsOverTime = await Conversation.aggregate([ { $match: { startedAt: { $gte: start, $lte: end } } }, { $group: { _id: { year: { $year: '$startedAt' }, month: { $month: '$startedAt' }, day: { $dayOfMonth: '$startedAt' } }, count: { $sum: 1 } } }, { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } } ]); // Message analysis const messageStats = await Message.aggregate([ { $lookup: { from: 'conversations', localField: 'conversationId', foreignField: '_id', as: 'conversation' } }, { $match: { 'conversation.startedAt': { $gte: start, $lte: end } } }, { $group: { _id: '$sender', count: { $sum: 1 }, avgLength: { $avg: { $strLenCP: '$content.text' } } } } ]); // Intent analysis const intentStats = await Message.aggregate([ { $lookup: { from: 'conversations', localField: 'conversationId', foreignField: '_id', as: 'conversation' } }, { $match: { 'conversation.startedAt': { $gte: start, $lte: end }, 'intent.name': { $exists: true } } }, { $group: { _id: '$intent.name', count: { $sum: 1 }, avgConfidence: { $avg: '$intent.confidence' } } }, { $sort: { count: -1 } } ]); res.json({ conversationsOverTime, messageStats, intentStats, dateRange: { start, end }, }); } catch (error) { logger.error('Error fetching analytics:', error); res.status(500).json({ error: 'Failed to fetch analytics' }); } };