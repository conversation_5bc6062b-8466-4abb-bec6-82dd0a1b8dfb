const Conversation = require('../models/Conversation'); const Message = require('../models/Message'); const nlpService = require('../services/nlpService'); const openaiService = require('../services/openaiService'); const logger = require('../config/logger'); const { v4: uuidv4 } = require('uuid'); const { validateObjectId, validateMessage, validateConversationData, handleValidationErrors } = require('../utils/validators'); const intelligentModeService = require('../services/intelligentModeService'); exports.startConversation = async (req, res) => { try { // Validation des données de conversation const conversationData = req.body; const validation = validateConversationData(conversationData); if (!validation.isValid) { logger.warn(`Conversation validation failed: ${validation.errors.join(', ')}`); return res.status(400).json({ error: 'Données de conversation invalides', details: validation.errors }); } const sessionId = uuidv4(); const conversation = new Conversation({ userId: req.user._id, sessionId, channel: conversationData.channel || 'web', metadata: { userAgent: req.headers['user-agent'], ipAddress: req.ip || req.connection?.remoteAddress, location: conversationData.location || null, }, }); await conversation.save(); logger.info(`New conversation started: ${sessionId} for user: ${req.user._id}`); // Message de bienvenue personnalisé selon le canal let welcomeMessage = "Bonjour! Je suis l'assistant virtuel Free Mobile. Comment puis-je t'aider aujourd'hui?"; if (conversationData.channel === 'mobile') { welcomeMessage = " Salut! Je suis ton assistant Free Mobile. Comment puis-je t'aider?"; } else if (conversationData.channel === 'voice') { welcomeMessage = "Bonjour et bienvenue au service client Free Mobile. Je suis votre assistant virtuel."; } res.json({ success: true, conversationId: conversation._id, sessionId, welcomeMessage, channel: conversation.channel, startedAt: conversation.startedAt }); } catch (error) { logger.error('Error starting conversation:', error); res.status(500).json({ error: 'Erreur lors du démarrage de la conversation' }); } }; exports.sendMessage = async (req, res) => { try { const { conversationId, message } = req.body; // Validation des paramètres d'entrée if (!conversationId || !message) { logger.warn('Missing required parameters for sending message'); return res.status(400).json({ error: 'Paramètres manquants', details: ['conversationId et message sont requis'] }); } // Validation de l'ID de conversation try { validateObjectId(conversationId, 'ID de conversation'); } catch (validationError) { logger.warn(`Invalid conversation ID: ${conversationId}`); return res.status(400).json({ error: validationError.message, details: ['ID de conversation invalide'] }); } // Validation du message const messageValidation = validateMessage(message); if (!messageValidation.isValid) { logger.warn(`Message validation failed: ${messageValidation.errors.join(', ')}`); return res.status(400).json({ error: 'Message invalide', details: messageValidation.errors }); } // Vérifier que la conversation appartient à l'utilisateur const conversation = await Conversation.findById(conversationId); if (!conversation) { logger.warn(`Conversation not found: ${conversationId}`); return res.status(404).json({ error: 'Conversation non trouvée' }); } if (conversation.userId.toString() !== req.user._id.toString()) { logger.warn(`Unauthorized access to conversation: ${conversationId} by user: ${req.user._id}`); return res.status(403).json({ error: 'Accès non autorisé à cette conversation' }); } // Vérifier si la conversation est active if (conversation.status === 'resolved' || conversation.status === 'abandoned') { logger.warn(`Attempt to send message to closed conversation: ${conversationId}`); return res.status(400).json({ error: 'Conversation fermée', details: ['Cette conversation est terminée. Veuillez démarrer une nouvelle conversation.'] }); } logger.info(`Message received for conversation: ${conversationId} from user: ${req.user._id}`); // Save user message const userMessage = new Message({ conversationId, sender: 'user', content: { text: messageValidation.cleanMessage, type: 'text', }, metadata: { originalMessage: message !== messageValidation.cleanMessage ? message : undefined, sanitized: message !== messageValidation.cleanMessage } }); await userMessage.save(); // Utiliser le service intelligent pour déterminer la meilleure réponse const intelligentResponse = await intelligentModeService.processMessage( conversationId, messageValidation.cleanMessage, { userId: req.user._id, profile: req.user.profile, preferences: req.user.preferences } ); // Fallback vers NLP classique si erreur let response = intelligentResponse.text; let responseMetadata = intelligentResponse.metadata || {}; let nlpResult = { intent: null, entities: [] }; if (!response) { // Get NLP analysis traditionnel nlpResult = await nlpService.parseMessage(messageValidation.cleanMessage, req.user._id); if (nlpResult.intent?.confidence > 0.7) { // High confidence - use Rasa response const rasaResponses = await nlpService.getResponse( messageValidation.cleanMessage, req.user._id.toString(), { conversationId } ); response = rasaResponses[0]?.text || "Je n'ai pas compris ta demande."; } else { // Low confidence - use OpenAI for better understanding const previousMessages = await Message.find({ conversationId }) .sort({ timestamp: -1 }) .limit(5) .lean(); response = await openaiService.generateResponse( { previousMessages: previousMessages.slice(0, 5) }, messageValidation.cleanMessage ); if (!response) { response = "Désolé, je n'ai pas bien compris. Peux-tu reformuler ta question?"; } } responseMetadata = { fallbackUsed: true, originalError: 'intelligent_mode_failed' }; } // Préparer le contenu du message bot avec les fonctionnalités intelligentes const botMessageContent = { text: response, type: intelligentResponse.type || 'text', buttons: intelligentResponse.buttons || [], quickReplies: intelligentResponse.quickReplies || [], quickActions: intelligentResponse.quickActions || [], suggestions: intelligentResponse.suggestions || [] }; // Ajouter les insights proactifs si présents if (intelligentResponse.proactiveInsights) { botMessageContent.proactiveInsights = intelligentResponse.proactiveInsights; } // Save bot response avec toutes les données intelligentes const botMessage = new Message({ conversationId, sender: 'bot', content: botMessageContent, intent: intelligentResponse.intent || nlpResult.intent, entities: intelligentResponse.entities || nlpResult.entities, metadata: { ...responseMetadata, mode: intelligentResponse.mode || 'fallback', sentiment: intelligentResponse.sentiment || null, tone: intelligentResponse.tone || 'neutral', processingTime: Date.now() - userMessage.timestamp.getTime(), intelligentModeUsed: !!intelligentResponse.mode, nlpProvider: responseMetadata.fallbackUsed ? (nlpResult.intent?.confidence > 0.7 ? 'rasa' : 'openai') : 'intelligent_mode' }, }); await botMessage.save(); // Notifier via WebSocket pour les mises à jour temps réel const wsManager = req.app.get('websocketManager'); if (wsManager && wsManager.namespaces.chat) { wsManager.namespaces.chat.broadcastToConversation(conversationId, 'message_received', { message: botMessage.toJSON(), intelligentFeatures: { mode: intelligentResponse.mode, hasButtons: botMessageContent.buttons.length > 0, hasQuickReplies: botMessageContent.quickReplies.length > 0, hasProactiveInsights: !!intelligentResponse.proactiveInsights, sentiment: intelligentResponse.sentiment } }); } logger.info(`Bot response sent for conversation: ${conversationId} using mode: ${intelligentResponse.mode || 'fallback'}`); res.json({ success: true, response: { text: response, type: intelligentResponse.type || 'text', mode: intelligentResponse.mode || 'fallback', buttons: botMessageContent.buttons, quickReplies: botMessageContent.quickReplies, suggestions: botMessageContent.suggestions, proactiveInsights: intelligentResponse.proactiveInsights || [], sentiment: intelligentResponse.sentiment, tone: intelligentResponse.tone }, intent: intelligentResponse.intent || nlpResult.intent, messageId: botMessage._id, timestamp: botMessage.timestamp, metadata: { processingTime: botMessage.metadata.processingTime, mode: intelligentResponse.mode, intelligentFeaturesUsed: !!intelligentResponse.mode } }); } catch (error) { logger.error('Error sending message:', error); res.status(500).json({ error: 'Failed to process message' }); } }; exports.getConversationHistory = async (req, res) => { try { const { conversationId } = req.params; const conversation = await Conversation.findById(conversationId); if (!conversation || conversation.userId.toString() !== req.user._id.toString()) { return res.status(404).json({ error: 'Conversation not found' }); } const messages = await Message.find({ conversationId }) .sort({ timestamp: 1 }) .lean(); res.json({ conversation, messages, }); } catch (error) { logger.error('Error fetching conversation history:', error); res.status(500).json({ error: 'Failed to fetch conversation' }); } };