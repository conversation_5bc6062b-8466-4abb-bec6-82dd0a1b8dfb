const SupportTicket = require('../models/SupportTicket'); const User = require('../models/User'); const logger = require('../config/logger'); const { validationResult } = require('express-validator'); const multer = require('multer'); const path = require('path'); const fs = require('fs').promises; // Configure multer for file uploads const storage = multer.diskStorage({ destination: async (req, file, cb) => { const uploadDir = path.join(__dirname, '../../uploads/tickets'); try { await fs.mkdir(uploadDir, { recursive: true }); cb(null, uploadDir); } catch (error) { cb(error); } }, filename: (req, file, cb) => { const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9); cb(null, `${uniqueSuffix}-${file.originalname}`); } }); const upload = multer({ storage, limits: { fileSize: 10 * 1024 * 1024, // 10MB limit files: 5 // Maximum 5 files }, fileFilter: (req, file, cb) => { const allowedTypes = /jpeg|jpg|png|gif|pdf|doc|docx|txt/; const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase()); const mimetype = allowedTypes.test(file.mimetype); if (mimetype && extname) { return cb(null, true); } else { cb(new Error('Invalid file type. Only images, PDFs, and documents are allowed.')); } } }); // Create a new support ticket exports.createTicket = async (req, res) => { try { const errors = validationResult(req); if (!errors.isEmpty()) { return res.status(400).json({ success: false, message: 'Validation errors', errors: errors.array() }); } const { subject, description, category, priority = 'medium', tags = [] } = req.body; // Get user info for metadata const user = await User.findById(req.user._id); if (!user) { return res.status(404).json({ success: false, message: 'User not found' }); } // Process attachments if any const attachments = []; if (req.files && req.files.length > 0) { for (const file of req.files) { attachments.push({ filename: file.filename, originalName: file.originalname, mimetype: file.mimetype, size: file.size, url: `/uploads/tickets/${file.filename}` }); } } // Create the ticket const ticket = new SupportTicket({ userId: req.user._id, subject, description, category, priority, tags, attachments, metadata: { source: 'web', customerInfo: { phoneNumber: user.profile?.phoneNumber, planType: user.profile?.planType || 'standard' }, systemInfo: { userAgent: req.get('User-Agent'), ipAddress: req.ip, sessionId: req.sessionID } } }); await ticket.save(); // Populate user information await ticket.populate('userId', 'email profile'); logger.info(`Support ticket created: ${ticket.ticketNumber} by user ${req.user._id}`); // Emit real-time event for admin dashboard if (req.app.get('websocketManager')) { const wsManager = req.app.get('websocketManager'); if (wsManager.namespaces.support) { wsManager.namespaces.support.broadcastTicketCreated(ticket); } } res.status(201).json({ success: true, message: 'Support ticket created successfully', data: { ticket: ticket.toJSON() } }); } catch (error) { logger.error('Error creating support ticket:', error); res.status(500).json({ success: false, message: 'Internal server error', error: process.env.NODE_ENV === 'development' ? error.message : undefined }); } }; // Get tickets for current user exports.getUserTickets = async (req, res) => { try { const { page = 1, limit = 10, status, category, priority, sortBy = 'createdAt', sortOrder = 'desc' } = req.query; const filter = { userId: req.user._id }; if (status) filter.status = status; if (category) filter.category = category; if (priority) filter.priority = priority; const sort = {}; sort[sortBy] = sortOrder === 'desc' ? -1 : 1; const tickets = await SupportTicket.find(filter) .populate('assignedTo', 'email profile') .sort(sort) .limit(limit * 1) .skip((page - 1) * limit) .lean(); const total = await SupportTicket.countDocuments(filter); res.json({ success: true, data: { tickets, pagination: { current: parseInt(page), pages: Math.ceil(total / limit), total, limit: parseInt(limit) } } }); } catch (error) { logger.error('Error fetching user tickets:', error); res.status(500).json({ success: false, message: 'Internal server error' }); } }; // Get all tickets (admin/agent only) exports.getAllTickets = async (req, res) => { try { // Check if user has admin/agent role if (!['admin', 'agent'].includes(req.user.role)) { return res.status(403).json({ success: false, message: 'Access denied. Admin or agent role required.' }); } const { page = 1, limit = 20, status, category, priority, assignedTo, search, sortBy = 'createdAt', sortOrder = 'desc' } = req.query; const filter = {}; if (status) filter.status = status; if (category) filter.category = category; if (priority) filter.priority = priority; if (assignedTo) filter.assignedTo = assignedTo; if (search) { filter.$or = [ { subject: { $regex: search, $options: 'i' } }, { description: { $regex: search, $options: 'i' } }, { ticketNumber: { $regex: search, $options: 'i' } } ]; } const sort = {}; sort[sortBy] = sortOrder === 'desc' ? -1 : 1; const tickets = await SupportTicket.find(filter) .populate('userId', 'email profile') .populate('assignedTo', 'email profile') .sort(sort) .limit(limit * 1) .skip((page - 1) * limit) .lean(); const total = await SupportTicket.countDocuments(filter); res.json({ success: true, data: { tickets, pagination: { current: parseInt(page), pages: Math.ceil(total / limit), total, limit: parseInt(limit) } } }); } catch (error) { logger.error('Error fetching all tickets:', error); res.status(500).json({ success: false, message: 'Internal server error' }); } }; // Get ticket by ID exports.getTicketById = async (req, res) => { try { const { ticketId } = req.params; const ticket = await SupportTicket.findById(ticketId) .populate('userId', 'email profile') .populate('assignedTo', 'email profile') .populate('messages.sender', 'email profile'); if (!ticket) { return res.status(404).json({ success: false, message: 'Ticket not found' }); } // Check access permissions const isOwner = ticket.userId._id.toString() === req.user._id.toString(); const isAssigned = ticket.assignedTo && ticket.assignedTo._id.toString() === req.user._id.toString(); const isAdminOrAgent = ['admin', 'agent'].includes(req.user.role); if (!isOwner && !isAssigned && !isAdminOrAgent) { return res.status(403).json({ success: false, message: 'Access denied' }); } res.json({ success: true, data: { ticket: ticket.toJSON() } }); } catch (error) { logger.error('Error fetching ticket:', error); res.status(500).json({ success: false, message: 'Internal server error' }); } }; // Update ticket status exports.updateTicketStatus = async (req, res) => { try { const { ticketId } = req.params; const { status, assignedTo, internalNote } = req.body; // Check if user has permission to update tickets if (!['admin', 'agent'].includes(req.user.role)) { return res.status(403).json({ success: false, message: 'Access denied. Admin or agent role required.' }); } const ticket = await SupportTicket.findById(ticketId); if (!ticket) { return res.status(404).json({ success: false, message: 'Ticket not found' }); } const updates = {}; if (status) updates.status = status; if (assignedTo) updates.assignedTo = assignedTo; // Add system message for status change if (status && status !== ticket.status) { ticket.messages.push({ sender: req.user._id, senderType: 'system', content: `Status changed from ${ticket.status} to ${status}`, isInternal: true }); } // Add internal note if provided if (internalNote) { ticket.messages.push({ sender: req.user._id, senderType: 'agent', content: internalNote, isInternal: true }); } Object.assign(ticket, updates); await ticket.save(); await ticket.populate('userId assignedTo'); // Emit real-time update if (req.app.get('websocketManager')) { const wsManager = req.app.get('websocketManager'); if (wsManager.namespaces.support) { wsManager.namespaces.support.broadcastTicketUpdated( ticket._id, updates, req.user._id ); } } logger.info(`Ticket ${ticket.ticketNumber} updated by ${req.user.email}`); res.json({ success: true, message: 'Ticket updated successfully', data: { ticket: ticket.toJSON() } }); } catch (error) { logger.error('Error updating ticket:', error); res.status(500).json({ success: false, message: 'Internal server error' }); } }; // Add message to ticket exports.addMessage = async (req, res) => { try { const { ticketId } = req.params; const { content, isInternal = false } = req.body; const ticket = await SupportTicket.findById(ticketId); if (!ticket) { return res.status(404).json({ success: false, message: 'Ticket not found' }); } // Check access permissions const isOwner = ticket.userId.toString() === req.user._id.toString(); const isAssigned = ticket.assignedTo && ticket.assignedTo.toString() === req.user._id.toString(); const isAdminOrAgent = ['admin', 'agent'].includes(req.user.role); if (!isOwner && !isAssigned && !isAdminOrAgent) { return res.status(403).json({ success: false, message: 'Access denied' }); } // Determine sender type let senderType = 'customer'; if (req.user.role === 'agent') senderType = 'agent'; if (req.user.role === 'admin') senderType = 'agent'; // Process attachments if any const attachments = []; if (req.files && req.files.length > 0) { for (const file of req.files) { attachments.push({ filename: file.filename, originalName: file.originalname, mimetype: file.mimetype, size: file.size, url: `/uploads/tickets/${file.filename}` }); } } const message = { sender: req.user._id, senderType, content, isInternal: isInternal && ['admin', 'agent'].includes(req.user.role), attachments }; ticket.messages.push(message); // Update ticket status if customer responds if (senderType === 'customer' && ticket.status === 'waiting_customer') { ticket.status = 'in_progress'; } await ticket.save(); await ticket.populate('messages.sender', 'email profile'); const addedMessage = ticket.messages[ticket.messages.length - 1]; // Emit real-time update if (req.app.get('websocketManager')) { const wsManager = req.app.get('websocketManager'); if (wsManager.namespaces.support) { wsManager.namespaces.support.broadcastTicketMessage( ticket._id, addedMessage ); } } res.json({ success: true, message: 'Message added successfully', data: { message: addedMessage } }); } catch (error) { logger.error('Error adding message to ticket:', error); res.status(500).json({ success: false, message: 'Internal server error' }); } }; // Get ticket metrics (admin only) exports.getTicketMetrics = async (req, res) => { try { if (req.user.role !== 'admin') { return res.status(403).json({ success: false, message: 'Access denied. Admin role required.' }); } const { period = '30d' } = req.query; // Calculate date range const now = new Date(); let startDate; switch (period) { case '7d': startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000); break; case '30d': startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000); break; case '90d': startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000); break; default: startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000); } const metrics = await SupportTicket.getMetrics({ startDate, endDate: now }); // Get additional metrics const statusDistribution = await SupportTicket.aggregate([ { $match: { createdAt: { $gte: startDate, $lte: now } } }, { $group: { _id: '$status', count: { $sum: 1 } } } ]); const categoryDistribution = await SupportTicket.aggregate([ { $match: { createdAt: { $gte: startDate, $lte: now } } }, { $group: { _id: '$category', count: { $sum: 1 } } } ]); const priorityDistribution = await SupportTicket.aggregate([ { $match: { createdAt: { $gte: startDate, $lte: now } } }, { $group: { _id: '$priority', count: { $sum: 1 } } } ]); res.json({ success: true, data: { metrics, distributions: { status: statusDistribution, category: categoryDistribution, priority: priorityDistribution }, period, dateRange: { startDate, endDate: now } } }); } catch (error) { logger.error('Error fetching ticket metrics:', error); res.status(500).json({ success: false, message: 'Internal server error' }); } }; // Export multer upload middleware exports.uploadMiddleware = upload.array('attachments', 5);