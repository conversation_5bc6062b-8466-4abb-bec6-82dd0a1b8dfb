const notificationService = require('../services/notificationService'); const logger = require('../config/logger'); // Obtenir les notifications de l'utilisateur exports.getUserNotifications = async (req, res) => { try { const { status, type, unreadOnly, limit } = req.query; const filters = { status, type, unreadOnly: unreadOnly === 'true', limit: parseInt(limit) || 50 }; const notifications = await notificationService.getUserNotifications( req.user._id, filters ); res.json(notifications); } catch (error) { logger.error('Error getting notifications:', error); res.status(500).json({ error: 'Erreur lors de la récupération des notifications' }); } }; // Marquer une notification comme lue exports.markAsRead = async (req, res) => { try { const { notificationId } = req.params; const notification = await notificationService.markAsRead( notificationId, req.user._id ); res.json({ success: true, notification }); } catch (error) { logger.error('Error marking notification as read:', error); res.status(500).json({ error: 'Erreur lors du marquage de la notification' }); } }; // Marquer toutes les notifications comme lues exports.markAllAsRead = async (req, res) => { try { const notifications = await notificationService.getUserNotifications( req.user._id, { unreadOnly: true } ); const promises = notifications.map(notification => notificationService.markAsRead(notification._id, req.user._id) ); await Promise.all(promises); res.json({ success: true, count: notifications.length }); } catch (error) { logger.error('Error marking all notifications as read:', error); res.status(500).json({ error: 'Erreur lors du marquage des notifications' }); } }; // Exécuter une action de notification exports.executeAction = async (req, res) => { try { const { notificationId } = req.params; const { actionIndex } = req.body; const actionResult = await notificationService.executeNotificationAction( notificationId, actionIndex, req.user._id ); // Traiter l'action selon son type let result = { success: true }; switch (actionResult.action) { case 'activate_option': // Rediriger vers le service d'options const subscriptionService = require('../services/subscriptionService'); result = await subscriptionService.activateOption( req.user._id, actionResult.payload.optionId ); break; case 'view_plan': case 'view_offer': // Simplement retourner les détails result.redirect = `/plans/${actionResult.payload.planId || actionResult.payload.offerId}`; break; default: result.action = actionResult; } res.json(result); } catch (error) { logger.error('Error executing notification action:', error); res.status(500).json({ error: 'Erreur lors de l\'exécution de l\'action' }); } }; // Créer une notification manuelle (admin) exports.createNotification = async (req, res) => { try { const { userId, type, data } = req.body; // Vérifier les permissions admin if (req.user.role !== 'admin') { return res.status(403).json({ error: 'Permission refusée' }); } const notification = await notificationService.createNotification( userId, type, data ); res.status(201).json({ success: true, notification }); } catch (error) { logger.error('Error creating notification:', error); res.status(500).json({ error: 'Erreur lors de la création de la notification' }); } }; // Déclencher les notifications proactives pour un utilisateur exports.triggerProactiveNotifications = async (req, res) => { try { const notifications = await notificationService.checkAndCreateProactiveNotifications( req.user._id ); res.json({ success: true, count: notifications.length, notifications: notifications.map(n => ({ id: n._id, type: n.type, title: n.title })) }); } catch (error) { logger.error('Error triggering proactive notifications:', error); res.status(500).json({ error: 'Erreur lors de la création des notifications' }); } }; // Obtenir les statistiques de notifications exports.getNotificationStats = async (req, res) => { try { const notifications = await notificationService.getUserNotifications( req.user._id, {} ); const stats = { total: notifications.length, unread: notifications.filter(n => n.status === 'pending' || n.status === 'sent').length, byType: {}, byStatus: {}, recentActivity: [] }; // Compter par type notifications.forEach(notification => { stats.byType[notification.type] = (stats.byType[notification.type] || 0) + 1; stats.byStatus[notification.status] = (stats.byStatus[notification.status] || 0) + 1; }); // Activité récente stats.recentActivity = notifications .slice(0, 5) .map(n => ({ id: n._id, type: n.type, title: n.title, createdAt: n.createdAt, status: n.status })); res.json(stats); } catch (error) { logger.error('Error getting notification stats:', error); res.status(500).json({ error: 'Erreur lors de la récupération des statistiques' }); } };