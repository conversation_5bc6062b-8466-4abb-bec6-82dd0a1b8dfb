const express = require('express'); const cors = require('cors'); const helmet = require('helmet'); const morgan = require('morgan'); const compression = require('compression'); const rateLimit = require('express-rate-limit'); const logger = require('./config/logger'); const authRoutes = require('./routes/authRoutes'); const chatRoutes = require('./routes/chatRoutes'); const adminRoutes = require('./routes/adminRoutes'); const agentRoutes = require('./routes/agentRoutes'); const securityRoutes = require('./routes/securityRoutes'); const notificationRoutes = require('./routes/notificationRoutes'); const subscriptionRoutes = require('./routes/subscriptionRoutes'); const customerRoutes = require('./routes/customerRoutes'); const supportTicketRoutes = require('./routes/supportTicketRoutes'); const analyticsRoutes = require('./routes/analytics'); const uploadRoutes = require('./routes/upload'); const mlRoutes = require('./routes/ml'); const dashboardRoutes = require('./routes/dashboardRoutes'); const emergencyCallRoutes = require('./routes/emergencyCallRoutes'); const aiCallRoutes = require('./routes/aiCallRoutes'); const app = express(); // Security middleware app.use(helmet({ contentSecurityPolicy: { directives: { defaultSrc: ["'self'"], styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"], fontSrc: ["'self'", "https://fonts.gstatic.com"], imgSrc: ["'self'", "data:", "https:"], scriptSrc: ["'self'"], connectSrc: ["'self'", "wss:", "ws:"] } }, crossOriginEmbedderPolicy: false })); // Enhanced CORS configuration const corsOptions = { origin: (origin, callback) => { // Allow requests with no origin (mobile apps, Postman, etc.) if (!origin) return callback(null, true); const allowedOrigins = process.env.NODE_ENV === 'production' ? (process.env.ALLOWED_ORIGINS || process.env.FRONTEND_URL || '').split(',').map(url => url.trim()) : [ 'http://localhost:3000', 'http://localhost:3001', 'http://127.0.0.1:3000', 'http://127.0.0.1:3001' ]; if (allowedOrigins.includes(origin)) { callback(null, true); } else { logger.warn(`CORS blocked origin: ${origin}`); callback(new Error('Not allowed by CORS')); } }, credentials: true, methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'], allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept'], exposedHeaders: ['X-Total-Count', 'X-Page-Count'], maxAge: 86400 // 24 hours }; app.use(cors(corsOptions)); // Enhanced Rate limiting with different tiers const createRateLimiter = (windowMs, max, message) => rateLimit({ windowMs, max, message: { error: 'Too many requests', message, retryAfter: Math.ceil(windowMs / 1000) }, standardHeaders: true, legacyHeaders: false, skip: (req) => { // Skip rate limiting for health checks return req.path === '/health' || req.path === '/api/health'; }, keyGenerator: (req) => { // Use user ID if authenticated, otherwise IP return req.user?.id || req.ip; } }); // General API rate limiting const generalLimiter = createRateLimiter( 15 * 60 * 1000, // 15 minutes 100, // 100 requests per 15 minutes 'Too many API requests. Please try again later.' ); // Strict rate limiting for authentication endpoints const authLimiter = createRateLimiter( 15 * 60 * 1000, // 15 minutes 10, // 10 login attempts per 15 minutes 'Too many authentication attempts. Please try again later.' ); // Emergency call rate limiting (separate from general) const emergencyLimiter = createRateLimiter( 5 * 60 * 1000, // 5 minutes 3, // 3 emergency calls per 5 minutes 'Too many emergency calls. Please wait before making another emergency call.' ); // Apply rate limiters app.use('/api/', generalLimiter); app.use('/api/auth', authLimiter); app.use('/api/emergency-calls', emergencyLimiter); // Body parsing middleware app.use(express.json()); app.use(express.urlencoded({ extended: true })); // Compression app.use(compression()); // Logging app.use(morgan('combined', { stream: { write: (message) => logger.info(message.trim()), }, })); // Routes app.use('/api/auth', authRoutes); app.use('/api/chat', chatRoutes); app.use('/api/admin', adminRoutes); app.use('/api/agent', agentRoutes); app.use('/api/security', securityRoutes); app.use('/api/notifications', notificationRoutes); app.use('/api/subscription', subscriptionRoutes); app.use('/api/customer', customerRoutes); app.use('/api/support', supportTicketRoutes); app.use('/api/analytics', analyticsRoutes); app.use('/api/upload', uploadRoutes); app.use('/api/v2/ml', mlRoutes); app.use('/api/dashboard', dashboardRoutes); app.use('/api/emergency-calls', emergencyCallRoutes); app.use('/api/ai-calls', aiCallRoutes); // Serve static files for uploads app.use('/uploads', express.static('uploads')); // Health check endpoints app.get('/health', (req, res) => { res.json({ status: 'ok', timestamp: new Date().toISOString(), environment: process.env.NODE_ENV || 'development', uptime: process.uptime() }); }); app.get('/api/status', (req, res) => { res.json({ message: 'Backend API is running', status: 'operational', timestamp: new Date().toISOString(), version: '1.0.0' }); }); // 404 handler app.use('*', (req, res) => { res.status(404).json({ error: 'Route not found', path: req.originalUrl, method: req.method }); }); // Error handling middleware app.use((err, req, res, next) => { logger.error('Unhandled error:', { error: err.message, stack: err.stack, url: req.url, method: req.method, ip: req.ip, userAgent: req.get('User-Agent') }); // Handle specific error types if (err.name === 'ValidationError') { return res.status(400).json({ error: 'Validation Error', details: Object.values(err.errors).map(e => e.message) }); } if (err.name === 'CastError') { return res.status(400).json({ error: 'Invalid ID format', details: ['The provided ID is not valid'] }); } if (err.code === 11000) { return res.status(409).json({ error: 'Duplicate entry', details: ['A record with this information already exists'] }); } res.status(err.status || 500).json({ error: process.env.NODE_ENV === 'production' ? 'Internal server error' : err.message, ...(process.env.NODE_ENV !== 'production' && { stack: err.stack }) }); }); module.exports = app;