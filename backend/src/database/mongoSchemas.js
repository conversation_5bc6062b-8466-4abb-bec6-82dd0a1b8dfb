/** * ============================================= * [ANALYTICS] MONGODB SCHEMA EXTENSIONS * Extended schemas for simulation, prediction, and AI features * Maintains compatibility with existing collections * ============================================= */ const mongoose = require('mongoose'); // Simulation Scenarios Schema const simulationScenarioSchema = new mongoose.Schema({ title: { type: String, required: true, index: true }, description: { type: String, required: true }, difficulty: { type: String, enum: ['beginner', 'intermediate', 'expert'], required: true, index: true }, category: { type: String, enum: ['billing', 'technical', 'sales', 'retention', 'complaint'], required: true, index: true }, tags: [{ type: String, index: true }], customer_profile: { id: { type: String, required: true }, name: { type: String, required: true }, age: { type: Number, required: true, min: 18, max: 100 }, personality: { type: String, enum: ['calm', 'frustrated', 'confused', 'demanding', 'friendly'], default: 'calm' }, issue_type: { type: String, enum: ['billing', 'technical', 'account', 'service', 'complaint'], required: true }, complexity: { type: String, enum: ['simple', 'moderate', 'complex'], default: 'moderate' }, platform: { type: String, enum: ['whatsapp', 'facebook', 'instagram', 'twitter', 'call'], default: 'whatsapp' }, history: [{ timestamp: { type: Date, required: true }, message: { type: String, required: true }, sentiment: { type: Number, min: -1, max: 1, default: 0 }, satisfaction: { type: Number, min: 0, max: 10, default: 5 } }], satisfaction_threshold: { type: Number, min: 0, max: 10, default: 7 }, // Legacy fields for backward compatibility tier: { type: String, enum: ['standard', 'bronze', 'silver', 'gold', 'platinum'], default: 'standard' }, communication_style: { type: String, enum: ['direct', 'polite', 'aggressive', 'confused', 'technical'], default: 'polite' }, emotional_state: { type: String, enum: ['calm', 'frustrated', 'angry', 'confused', 'urgent'], default: 'calm' } }, context: { situation: String, background: String, desired_outcome: String, escalation_triggers: [String] }, learning_objectives: [{ type: String, enum: ['empathy', 'efficiency', 'accuracy', 'communication', 'problem_solving'] }], success_criteria: { min_empathy_score: { type: Number, default: 70 }, min_efficiency_score: { type: Number, default: 70 }, min_accuracy_score: { type: Number, default: 70 }, max_resolution_time: { type: Number, default: 30 }, // minutes customer_satisfaction_target: { type: Number, default: 8 } }, expected_resolution_time: { type: Number, // minutes default: 15 }, popularity: { type: Number, default: 0 }, effectiveness_score: { type: Number, default: 0 }, created_by: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }, created_at: { type: Date, default: Date.now, index: true }, updated_at: { type: Date, default: Date.now }, is_active: { type: Boolean, default: true } }); // Simulation Sessions Schema const simulationSessionSchema = new mongoose.Schema({ _id: { type: String, // UUID required: true }, scenario_id: { type: mongoose.Schema.Types.ObjectId, ref: 'SimulationScenario', required: true, index: true }, agent_id: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true, index: true }, status: { type: String, enum: ['active', 'completed', 'abandoned', 'paused'], default: 'active', index: true }, settings: { difficulty_adjustment: { type: Boolean, default: true }, ai_coaching_enabled: { type: Boolean, default: true }, real_time_feedback: { type: Boolean, default: true } }, scenario: { type: mongoose.Schema.Types.Mixed // Snapshot of scenario at session start }, customer_persona: { id: String, name: String, age: Number, personality_traits: [String], communication_style: String, frustration_triggers: [String], satisfaction_drivers: [String], background: String, current_mood: String, patience_level: String }, messages: [{ id: String, sender: { type: String, enum: ['agent', 'customer'], required: true }, content: { type: String, required: true }, timestamp: { type: Date, required: true }, metadata: { sentiment: Number, satisfaction: Number, escalation_risk: Number, empathy_rating: Number, response_time: Number } }], performance_metrics: { overall_score: { type: Number, default: 0 }, empathy_score: { type: Number, default: 0 }, efficiency_score: { type: Number, default: 0 }, accuracy_score: { type: Number, default: 0 }, customer_satisfaction: { type: Number, default: 0 }, resolution_time: { type: Number, default: 0 }, response_times: [Number] }, final_metrics: { total_duration: Number, messages_count: Number, average_response_time: Number, completion_rate: Number, skill_improvements: { empathy: Number, efficiency: Number, accuracy: Number, communication: Number, problem_solving: Number } }, created_at: { type: Date, default: Date.now, index: true }, updated_at: { type: Date, default: Date.now }, ended_at: Date, completion_reason: { type: String, enum: ['completed', 'abandoned', 'escalated', 'timeout'] }, last_customer_message: Date }); // Agent Progress Schema const agentProgressSchema = new mongoose.Schema({ agent_id: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true, unique: true, index: true }, total_sessions: { type: Number, default: 0 }, completed_sessions: { type: Number, default: 0 }, average_score: { type: Number, default: 0 }, skill_levels: { empathy: { type: Number, default: 0, min: 0, max: 100 }, efficiency: { type: Number, default: 0, min: 0, max: 100 }, accuracy: { type: Number, default: 0, min: 0, max: 100 }, communication: { type: Number, default: 0, min: 0, max: 100 }, problem_solving: { type: Number, default: 0, min: 0, max: 100 } }, badges_earned: [{ id: { type: String, required: true }, name: { type: String, required: true }, description: { type: String, required: true }, icon: { type: String, required: true }, earned_date: { type: Date, required: true }, rarity: { type: String, enum: ['common', 'rare', 'epic', 'legendary'], default: 'common' }, // Legacy field for backward compatibility earned_at: Date }], current_streak: { type: Number, default: 0 }, best_streak: { type: Number, default: 0 }, next_milestone: { id: { type: String, default: 'first_completion' }, name: { type: String, default: 'First Completion' }, description: { type: String, default: 'Complete your first simulation' }, progress: { type: Number, min: 0, max: 100, default: 0 }, target: { type: Number, default: 1 }, reward: { type: String, default: 'Beginner Badge' } }, created_at: { type: Date, default: Date.now }, updated_at: { type: Date, default: Date.now } }); // Churn Predictions Schema const churnPredictionSchema = new mongoose.Schema({ customer_id: { type: mongoose.Schema.Types.ObjectId, ref: 'Customer', required: true, index: true }, churn_probability: { type: Number, required: true, min: 0, max: 1 }, risk_level: { type: String, enum: ['low', 'medium', 'high', 'critical'], required: true, index: true }, risk_factors: [{ type: String, enum: ['billing_issues', 'service_complaints', 'declining_satisfaction', 'decreasing_usage', 'frequent_support_contact', 'competitor_interest', 'price_concerns'] }], customer_value: { type: String, enum: ['low', 'medium', 'high'], default: 'medium' }, customer_tier: { type: String, enum: ['standard', 'bronze', 'silver', 'gold', 'platinum'], default: 'standard' }, predicted_churn_date: Date, confidence: { type: Number, min: 0, max: 1 }, model_version: String, created_at: { type: Date, default: Date.now, index: true }, intervention_applied: { type: Boolean, default: false }, intervention_outcome: { type: String, enum: ['successful', 'unsuccessful', 'pending'] } }); // Escalation Predictions Schema const escalationPredictionSchema = new mongoose.Schema({ ticket_id: { type: mongoose.Schema.Types.ObjectId, ref: 'Ticket', required: true, index: true }, escalation_probability: { type: Number, required: true, min: 0, max: 1 }, risk_level: { type: String, enum: ['low', 'medium', 'high', 'critical'], required: true, index: true }, risk_factors: [{ type: String, enum: ['long_resolution_time', 'slow_initial_response', 'stale_ticket', 'negative_customer_sentiment', 'customer_escalation_history', 'high_value_customer', 'inexperienced_agent', 'agent_overload', 'extended_conversation', 'customer_frustration', 'concurrent_system_issues', 'sla_breach_risk'] }], predicted_escalation_time: Date, confidence: { type: Number, min: 0, max: 1 }, customer_sentiment: { type: String, enum: ['positive', 'neutral', 'negative'], default: 'neutral' }, agent_experience: { type: String, enum: ['novice', 'intermediate', 'experienced', 'expert'], default: 'intermediate' }, ticket_complexity: { type: String, enum: ['low', 'medium', 'high'], default: 'medium' }, model_version: String, created_at: { type: Date, default: Date.now, index: true }, actual_escalated: { type: Boolean, default: null }, prevention_applied: { type: Boolean, default: false }, prevention_outcome: { type: String, enum: ['successful', 'unsuccessful', 'pending'] } }); // Anomalies Schema const anomalySchema = new mongoose.Schema({ type: { type: String, enum: ['performance', 'volume', 'satisfaction', 'system'], required: true, index: true }, severity: { type: String, enum: ['low', 'medium', 'high', 'critical'], required: true, index: true }, description: { type: String, required: true }, detected_at: { type: Date, default: Date.now, index: true }, confidence: { type: Number, min: 0, max: 1 }, affected_metrics: [String], root_cause_probability: { type: Map, of: Number }, estimated_impact: { type: String, enum: ['minimal', 'low', 'medium', 'high'], default: 'medium' }, auto_resolution_possible: { type: Boolean, default: false }, anomaly_score: Number, threshold: Number, status: { type: String, enum: ['active', 'acknowledged', 'resolved', 'false_positive'], default: 'active', index: true }, acknowledged_by: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }, acknowledged_at: Date, acknowledgment_notes: String, resolved_at: Date, resolution_notes: String, updated_at: { type: Date, default: Date.now } }); // AI Suggestions Schema const aiSuggestionSchema = new mongoose.Schema({ suggestion_id: { type: String, required: true, unique: true, index: true }, agent_id: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true, index: true }, conversation_id: String, ticket_id: { type: mongoose.Schema.Types.ObjectId, ref: 'Ticket' }, suggestion_type: { type: String, enum: ['response', 'escalation', 'template', 'action'], required: true }, content: { type: String, required: true }, confidence: { type: Number, min: 0, max: 1 }, reasoning: String, alternatives: [String], context: { customer_sentiment: Number, conversation_length: Number, urgency_level: String, platform: String }, feedback: { rating: { type: Number, min: 1, max: 5 }, helpful: Boolean, used_as_is: Boolean, outcome: { type: String, enum: ['successful', 'unsuccessful', 'neutral'] }, notes: String, submitted_at: Date }, created_at: { type: Date, default: Date.now, index: true } }); // Personalized Templates Schema const personalizedTemplateSchema = new mongoose.Schema({ agent_id: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true, index: true }, base_template_id: { type: mongoose.Schema.Types.ObjectId, ref: 'Template' }, name: { type: String, required: true }, category: { type: String, required: true, index: true }, content: { type: String, required: true }, customization: String, usage_count: { type: Number, default: 0 }, effectiveness_score: { type: Number, default: 0 }, customer_satisfaction_avg: { type: Number, default: 0 }, created_at: { type: Date, default: Date.now }, updated_at: { type: Date, default: Date.now }, is_active: { type: Boolean, default: true } }); // Create indexes for performance simulationScenarioSchema.index({ difficulty: 1, category: 1 }); simulationScenarioSchema.index({ tags: 1 }); simulationScenarioSchema.index({ popularity: -1 }); simulationScenarioSchema.index({ effectiveness_score: -1 }); simulationScenarioSchema.index({ created_at: -1 }); simulationScenarioSchema.index({ is_active: 1 }); simulationSessionSchema.index({ agent_id: 1, status: 1 }); simulationSessionSchema.index({ created_at: -1 }); simulationSessionSchema.index({ scenario_id: 1, created_at: -1 }); simulationSessionSchema.index({ agent_id: 1, created_at: -1 }); simulationSessionSchema.index({ status: 1, created_at: -1 }); agentProgressSchema.index({ 'skill_levels.empathy': -1 }); agentProgressSchema.index({ average_score: -1 }); agentProgressSchema.index({ completed_sessions: -1 }); agentProgressSchema.index({ total_sessions: -1 }); churnPredictionSchema.index({ created_at: -1, risk_level: 1 }); churnPredictionSchema.index({ customer_id: 1, created_at: -1 }); escalationPredictionSchema.index({ created_at: -1, risk_level: 1 }); escalationPredictionSchema.index({ ticket_id: 1, created_at: -1 }); anomalySchema.index({ detected_at: -1, severity: 1 }); anomalySchema.index({ type: 1, status: 1 }); aiSuggestionSchema.index({ agent_id: 1, created_at: -1 }); aiSuggestionSchema.index({ suggestion_type: 1, created_at: -1 }); personalizedTemplateSchema.index({ agent_id: 1, category: 1 }); personalizedTemplateSchema.index({ effectiveness_score: -1 }); // Export models module.exports = { SimulationScenario: mongoose.model('SimulationScenario', simulationScenarioSchema), SimulationSession: mongoose.model('SimulationSession', simulationSessionSchema), AgentProgress: mongoose.model('AgentProgress', agentProgressSchema), ChurnPrediction: mongoose.model('ChurnPrediction', churnPredictionSchema), EscalationPrediction: mongoose.model('EscalationPrediction', escalationPredictionSchema), Anomaly: mongoose.model('Anomaly', anomalySchema), AISuggestion: mongoose.model('AISuggestion', aiSuggestionSchema), PersonalizedTemplate: mongoose.model('PersonalizedTemplate', personalizedTemplateSchema) };