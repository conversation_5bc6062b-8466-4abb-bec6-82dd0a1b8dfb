-- =============================================
-- 📊 TIMESCALEDB SCHEMA EXTENSIONS
-- Time-series tables for analytics and monitoring
-- Optimized for high-frequency data ingestion
-- =============================================

-- Enable TimescaleDB extension if not already enabled
CREATE EXTENSION IF NOT EXISTS timescaledb;

-- =============================================
-- SIMULATION METRICS TIME SERIES
-- =============================================

-- Agent performance metrics over time
CREATE TABLE IF NOT EXISTS agent_performance_metrics (
    time TIMESTAMPTZ NOT NULL,
    agent_id UUID NOT NULL,
    session_id VARCHAR(255),
    empathy_score NUMERIC(5,2),
    efficiency_score NUMERIC(5,2),
    accuracy_score NUMERIC(5,2),
    overall_score NUMERIC(5,2),
    customer_satisfaction NUMERIC(3,1),
    response_time_ms INTEGER,
    messages_count INTEGER,
    scenario_difficulty VARCHAR(20),
    scenario_category VARCHAR(50),
    completion_status VARCHAR(20)
);

-- Convert to hypertable
SELECT create_hypertable('agent_performance_metrics', 'time', if_not_exists => TRUE);

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_agent_performance_agent_time 
ON agent_performance_metrics (agent_id, time DESC);

CREATE INDEX IF NOT EXISTS idx_agent_performance_scenario 
ON agent_performance_metrics (scenario_category, time DESC);

-- =============================================
-- PREDICTIVE ANALYTICS TIME SERIES
-- =============================================

-- Churn prediction metrics
CREATE TABLE IF NOT EXISTS churn_prediction_metrics (
    time TIMESTAMPTZ NOT NULL,
    customer_id UUID NOT NULL,
    churn_probability NUMERIC(5,4),
    risk_level VARCHAR(20),
    customer_tier VARCHAR(20),
    customer_value VARCHAR(20),
    model_version VARCHAR(50),
    confidence_score NUMERIC(5,4),
    risk_factors TEXT[],
    intervention_applied BOOLEAN DEFAULT FALSE,
    actual_churned BOOLEAN DEFAULT NULL
);

SELECT create_hypertable('churn_prediction_metrics', 'time', if_not_exists => TRUE);

CREATE INDEX IF NOT EXISTS idx_churn_prediction_customer_time 
ON churn_prediction_metrics (customer_id, time DESC);

CREATE INDEX IF NOT EXISTS idx_churn_prediction_risk_time 
ON churn_prediction_metrics (risk_level, time DESC);

-- Demand forecasting metrics
CREATE TABLE IF NOT EXISTS demand_forecast_metrics (
    time TIMESTAMPTZ NOT NULL,
    forecast_horizon_hours INTEGER,
    predicted_volume INTEGER,
    actual_volume INTEGER DEFAULT NULL,
    confidence_interval_lower INTEGER,
    confidence_interval_upper INTEGER,
    peak_probability NUMERIC(5,4),
    model_version VARCHAR(50),
    forecast_accuracy NUMERIC(5,4) DEFAULT NULL,
    contributing_factors TEXT[]
);

SELECT create_hypertable('demand_forecast_metrics', 'time', if_not_exists => TRUE);

CREATE INDEX IF NOT EXISTS idx_demand_forecast_time 
ON demand_forecast_metrics (time DESC);

CREATE INDEX IF NOT EXISTS idx_demand_forecast_horizon 
ON demand_forecast_metrics (forecast_horizon_hours, time DESC);

-- Escalation prediction metrics
CREATE TABLE IF NOT EXISTS escalation_prediction_metrics (
    time TIMESTAMPTZ NOT NULL,
    ticket_id UUID NOT NULL,
    agent_id UUID,
    escalation_probability NUMERIC(5,4),
    risk_level VARCHAR(20),
    predicted_escalation_time TIMESTAMPTZ,
    actual_escalated BOOLEAN DEFAULT NULL,
    escalation_time TIMESTAMPTZ DEFAULT NULL,
    model_version VARCHAR(50),
    confidence_score NUMERIC(5,4),
    risk_factors TEXT[],
    prevention_applied BOOLEAN DEFAULT FALSE,
    prevention_outcome VARCHAR(20)
);

SELECT create_hypertable('escalation_prediction_metrics', 'time', if_not_exists => TRUE);

CREATE INDEX IF NOT EXISTS idx_escalation_prediction_ticket_time 
ON escalation_prediction_metrics (ticket_id, time DESC);

CREATE INDEX IF NOT EXISTS idx_escalation_prediction_agent_time 
ON escalation_prediction_metrics (agent_id, time DESC);

-- =============================================
-- SYSTEM MONITORING TIME SERIES
-- =============================================

-- System performance metrics
CREATE TABLE IF NOT EXISTS system_performance_metrics (
    time TIMESTAMPTZ NOT NULL,
    service_name VARCHAR(100) NOT NULL,
    cpu_usage_percent NUMERIC(5,2),
    memory_usage_percent NUMERIC(5,2),
    disk_usage_percent NUMERIC(5,2),
    network_latency_ms INTEGER,
    response_time_ms INTEGER,
    error_rate_percent NUMERIC(5,4),
    throughput_requests_per_minute INTEGER,
    active_connections INTEGER,
    queue_length INTEGER,
    uptime_seconds BIGINT
);

SELECT create_hypertable('system_performance_metrics', 'time', if_not_exists => TRUE);

CREATE INDEX IF NOT EXISTS idx_system_performance_service_time 
ON system_performance_metrics (service_name, time DESC);

-- Anomaly detection metrics
CREATE TABLE IF NOT EXISTS anomaly_detection_metrics (
    time TIMESTAMPTZ NOT NULL,
    anomaly_id VARCHAR(255) NOT NULL,
    anomaly_type VARCHAR(50),
    severity VARCHAR(20),
    confidence_score NUMERIC(5,4),
    affected_metrics TEXT[],
    anomaly_score NUMERIC(10,6),
    threshold_value NUMERIC(10,6),
    detection_model VARCHAR(50),
    auto_resolved BOOLEAN DEFAULT FALSE,
    resolution_time TIMESTAMPTZ,
    false_positive BOOLEAN DEFAULT NULL
);

SELECT create_hypertable('anomaly_detection_metrics', 'time', if_not_exists => TRUE);

CREATE INDEX IF NOT EXISTS idx_anomaly_detection_type_time 
ON anomaly_detection_metrics (anomaly_type, time DESC);

CREATE INDEX IF NOT EXISTS idx_anomaly_detection_severity_time 
ON anomaly_detection_metrics (severity, time DESC);

-- =============================================
-- CUSTOMER INTERACTION TIME SERIES
-- =============================================

-- Real-time sentiment analysis
CREATE TABLE IF NOT EXISTS sentiment_analysis_metrics (
    time TIMESTAMPTZ NOT NULL,
    conversation_id VARCHAR(255) NOT NULL,
    agent_id UUID,
    customer_id UUID,
    message_id VARCHAR(255),
    sentiment_score NUMERIC(5,4),
    emotion_breakdown JSONB,
    escalation_risk NUMERIC(5,4),
    confidence_score NUMERIC(5,4),
    platform VARCHAR(50),
    message_length INTEGER,
    response_time_ms INTEGER
);

SELECT create_hypertable('sentiment_analysis_metrics', 'time', if_not_exists => TRUE);

CREATE INDEX IF NOT EXISTS idx_sentiment_analysis_conversation_time 
ON sentiment_analysis_metrics (conversation_id, time DESC);

CREATE INDEX IF NOT EXISTS idx_sentiment_analysis_agent_time 
ON sentiment_analysis_metrics (agent_id, time DESC);

-- AI suggestion performance
CREATE TABLE IF NOT EXISTS ai_suggestion_metrics (
    time TIMESTAMPTZ NOT NULL,
    suggestion_id VARCHAR(255) NOT NULL,
    agent_id UUID NOT NULL,
    suggestion_type VARCHAR(50),
    confidence_score NUMERIC(5,4),
    acceptance_rate NUMERIC(5,4),
    effectiveness_score NUMERIC(5,4),
    response_time_improvement_ms INTEGER,
    customer_satisfaction_impact NUMERIC(3,2),
    model_version VARCHAR(50),
    context_accuracy NUMERIC(5,4)
);

SELECT create_hypertable('ai_suggestion_metrics', 'time', if_not_exists => TRUE);

CREATE INDEX IF NOT EXISTS idx_ai_suggestion_agent_time 
ON ai_suggestion_metrics (agent_id, time DESC);

CREATE INDEX IF NOT EXISTS idx_ai_suggestion_type_time 
ON ai_suggestion_metrics (suggestion_type, time DESC);

-- =============================================
-- BUSINESS METRICS TIME SERIES
-- =============================================

-- Customer satisfaction trends
CREATE TABLE IF NOT EXISTS customer_satisfaction_metrics (
    time TIMESTAMPTZ NOT NULL,
    customer_id UUID,
    agent_id UUID,
    ticket_id UUID,
    satisfaction_score NUMERIC(3,1),
    nps_score INTEGER,
    resolution_time_minutes INTEGER,
    first_contact_resolution BOOLEAN,
    escalated BOOLEAN,
    channel VARCHAR(50),
    category VARCHAR(100),
    sentiment_score NUMERIC(5,4)
);

SELECT create_hypertable('customer_satisfaction_metrics', 'time', if_not_exists => TRUE);

CREATE INDEX IF NOT EXISTS idx_customer_satisfaction_customer_time 
ON customer_satisfaction_metrics (customer_id, time DESC);

CREATE INDEX IF NOT EXISTS idx_customer_satisfaction_agent_time 
ON customer_satisfaction_metrics (agent_id, time DESC);

-- Operational efficiency metrics
CREATE TABLE IF NOT EXISTS operational_efficiency_metrics (
    time TIMESTAMPTZ NOT NULL,
    team_id VARCHAR(100),
    agent_count INTEGER,
    total_tickets_handled INTEGER,
    average_resolution_time_minutes NUMERIC(8,2),
    first_contact_resolution_rate NUMERIC(5,4),
    escalation_rate NUMERIC(5,4),
    customer_satisfaction_avg NUMERIC(3,2),
    agent_utilization_rate NUMERIC(5,4),
    cost_per_ticket NUMERIC(8,2),
    revenue_impact NUMERIC(12,2)
);

SELECT create_hypertable('operational_efficiency_metrics', 'time', if_not_exists => TRUE);

CREATE INDEX IF NOT EXISTS idx_operational_efficiency_team_time 
ON operational_efficiency_metrics (team_id, time DESC);

-- =============================================
-- CONTINUOUS AGGREGATES FOR PERFORMANCE
-- =============================================

-- Hourly agent performance summary
CREATE MATERIALIZED VIEW IF NOT EXISTS agent_performance_hourly
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 hour', time) AS bucket,
    agent_id,
    AVG(empathy_score) AS avg_empathy_score,
    AVG(efficiency_score) AS avg_efficiency_score,
    AVG(accuracy_score) AS avg_accuracy_score,
    AVG(overall_score) AS avg_overall_score,
    AVG(customer_satisfaction) AS avg_customer_satisfaction,
    AVG(response_time_ms) AS avg_response_time_ms,
    COUNT(*) AS session_count,
    COUNT(CASE WHEN completion_status = 'completed' THEN 1 END) AS completed_sessions
FROM agent_performance_metrics
GROUP BY bucket, agent_id;

-- Daily system performance summary
CREATE MATERIALIZED VIEW IF NOT EXISTS system_performance_daily
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 day', time) AS bucket,
    service_name,
    AVG(cpu_usage_percent) AS avg_cpu_usage,
    MAX(cpu_usage_percent) AS max_cpu_usage,
    AVG(memory_usage_percent) AS avg_memory_usage,
    MAX(memory_usage_percent) AS max_memory_usage,
    AVG(response_time_ms) AS avg_response_time,
    MAX(response_time_ms) AS max_response_time,
    AVG(error_rate_percent) AS avg_error_rate,
    MAX(error_rate_percent) AS max_error_rate,
    AVG(throughput_requests_per_minute) AS avg_throughput
FROM system_performance_metrics
GROUP BY bucket, service_name;

-- Hourly churn prediction summary
CREATE MATERIALIZED VIEW IF NOT EXISTS churn_prediction_hourly
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 hour', time) AS bucket,
    risk_level,
    customer_tier,
    COUNT(*) AS prediction_count,
    AVG(churn_probability) AS avg_churn_probability,
    AVG(confidence_score) AS avg_confidence_score,
    COUNT(CASE WHEN intervention_applied THEN 1 END) AS interventions_applied,
    COUNT(CASE WHEN actual_churned = true THEN 1 END) AS actual_churns,
    COUNT(CASE WHEN actual_churned = false THEN 1 END) AS retained_customers
FROM churn_prediction_metrics
GROUP BY bucket, risk_level, customer_tier;

-- =============================================
-- RETENTION POLICIES
-- =============================================

-- Keep detailed metrics for 90 days, then compress
SELECT add_retention_policy('agent_performance_metrics', INTERVAL '90 days');
SELECT add_retention_policy('system_performance_metrics', INTERVAL '90 days');
SELECT add_retention_policy('sentiment_analysis_metrics', INTERVAL '90 days');

-- Keep prediction metrics for 1 year
SELECT add_retention_policy('churn_prediction_metrics', INTERVAL '1 year');
SELECT add_retention_policy('demand_forecast_metrics', INTERVAL '1 year');
SELECT add_retention_policy('escalation_prediction_metrics', INTERVAL '1 year');

-- Keep anomaly detection for 6 months
SELECT add_retention_policy('anomaly_detection_metrics', INTERVAL '6 months');

-- Keep business metrics for 2 years
SELECT add_retention_policy('customer_satisfaction_metrics', INTERVAL '2 years');
SELECT add_retention_policy('operational_efficiency_metrics', INTERVAL '2 years');

-- =============================================
-- COMPRESSION POLICIES
-- =============================================

-- Compress data older than 7 days for better storage efficiency
SELECT add_compression_policy('agent_performance_metrics', INTERVAL '7 days');
SELECT add_compression_policy('system_performance_metrics', INTERVAL '7 days');
SELECT add_compression_policy('churn_prediction_metrics', INTERVAL '7 days');
SELECT add_compression_policy('demand_forecast_metrics', INTERVAL '7 days');
SELECT add_compression_policy('escalation_prediction_metrics', INTERVAL '7 days');
SELECT add_compression_policy('sentiment_analysis_metrics', INTERVAL '7 days');
SELECT add_compression_policy('ai_suggestion_metrics', INTERVAL '7 days');
SELECT add_compression_policy('customer_satisfaction_metrics', INTERVAL '7 days');
SELECT add_compression_policy('operational_efficiency_metrics', INTERVAL '7 days');

-- =============================================
-- USEFUL FUNCTIONS FOR ANALYTICS
-- =============================================

-- Function to calculate agent performance trends
CREATE OR REPLACE FUNCTION get_agent_performance_trend(
    p_agent_id UUID,
    p_days INTEGER DEFAULT 30
)
RETURNS TABLE (
    date DATE,
    avg_score NUMERIC,
    session_count BIGINT,
    trend_direction TEXT
) AS $$
BEGIN
    RETURN QUERY
    WITH daily_performance AS (
        SELECT 
            DATE(time) as perf_date,
            AVG(overall_score) as daily_avg_score,
            COUNT(*) as daily_session_count
        FROM agent_performance_metrics
        WHERE agent_id = p_agent_id
        AND time >= NOW() - INTERVAL '1 day' * p_days
        GROUP BY DATE(time)
        ORDER BY DATE(time)
    ),
    trend_calculation AS (
        SELECT 
            perf_date,
            daily_avg_score,
            daily_session_count,
            LAG(daily_avg_score) OVER (ORDER BY perf_date) as prev_score
        FROM daily_performance
    )
    SELECT 
        perf_date,
        daily_avg_score,
        daily_session_count,
        CASE 
            WHEN prev_score IS NULL THEN 'stable'
            WHEN daily_avg_score > prev_score + 5 THEN 'improving'
            WHEN daily_avg_score < prev_score - 5 THEN 'declining'
            ELSE 'stable'
        END as trend_direction
    FROM trend_calculation;
END;
$$ LANGUAGE plpgsql;

-- Function to get system health score
CREATE OR REPLACE FUNCTION get_system_health_score(
    p_service_name VARCHAR DEFAULT NULL,
    p_hours INTEGER DEFAULT 1
)
RETURNS NUMERIC AS $$
DECLARE
    health_score NUMERIC := 100;
    avg_cpu NUMERIC;
    avg_memory NUMERIC;
    avg_error_rate NUMERIC;
    avg_response_time NUMERIC;
BEGIN
    SELECT 
        AVG(cpu_usage_percent),
        AVG(memory_usage_percent),
        AVG(error_rate_percent),
        AVG(response_time_ms)
    INTO avg_cpu, avg_memory, avg_error_rate, avg_response_time
    FROM system_performance_metrics
    WHERE time >= NOW() - INTERVAL '1 hour' * p_hours
    AND (p_service_name IS NULL OR service_name = p_service_name);
    
    -- Deduct points based on metrics
    IF avg_cpu > 90 THEN health_score := health_score - 30;
    ELSIF avg_cpu > 80 THEN health_score := health_score - 15;
    ELSIF avg_cpu > 70 THEN health_score := health_score - 5;
    END IF;
    
    IF avg_memory > 90 THEN health_score := health_score - 25;
    ELSIF avg_memory > 80 THEN health_score := health_score - 10;
    END IF;
    
    IF avg_error_rate > 5 THEN health_score := health_score - 40;
    ELSIF avg_error_rate > 1 THEN health_score := health_score - 20;
    ELSIF avg_error_rate > 0.5 THEN health_score := health_score - 10;
    END IF;
    
    IF avg_response_time > 5000 THEN health_score := health_score - 30;
    ELSIF avg_response_time > 2000 THEN health_score := health_score - 15;
    ELSIF avg_response_time > 1000 THEN health_score := health_score - 5;
    END IF;
    
    RETURN GREATEST(0, health_score);
END;
$$ LANGUAGE plpgsql;
