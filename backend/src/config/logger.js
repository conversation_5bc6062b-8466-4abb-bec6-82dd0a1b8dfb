const winston = require('winston'); const path = require('path'); // Sensitive fields to sanitize from logs const SENSITIVE_FIELDS = [ 'password', 'token', 'apiKey', 'secret', 'authorization', 'jwt', 'session', 'cookie', 'auth', 'key', 'credential' ]; /** * Sanitize sensitive data from log objects */ const sanitizeLogData = winston.format((info) => { const sanitize = (obj) => { if (typeof obj !== 'object' || obj === null) return obj; const sanitized = Array.isArray(obj) ? [] : {}; for (const [key, value] of Object.entries(obj)) { const lowerKey = key.toLowerCase(); const isSensitive = SENSITIVE_FIELDS.some(field => lowerKey.includes(field)); if (isSensitive) { sanitized[key] = '[REDACTED]'; } else if (typeof value === 'object' && value !== null) { sanitized[key] = sanitize(value); } else { sanitized[key] = value; } } return sanitized; }; return sanitize(info); }); /** * Enhanced production logger configuration */ const createLogger = () => { const logLevel = process.env.LOG_LEVEL || (process.env.NODE_ENV === 'production' ? 'info' : 'debug'); const logDir = process.env.LOG_FILE_PATH ? path.dirname(process.env.LOG_FILE_PATH) : './logs'; const maxSize = parseInt(process.env.LOG_MAX_SIZE) || 5242880; // 5MB const maxFiles = parseInt(process.env.LOG_MAX_FILES) || 10; const transports = []; // File transports for production if (process.env.NODE_ENV === 'production' || process.env.LOG_FILE_ENABLED === 'true') { transports.push( new winston.transports.File({ filename: path.join(logDir, 'error.log'), level: 'error', maxsize: maxSize, maxFiles: maxFiles, tailable: true, format: winston.format.combine( winston.format.timestamp(), sanitizeLogData(), winston.format.errors({ stack: true }), winston.format.json() ) }), new winston.transports.File({ filename: path.join(logDir, 'combined.log'), maxsize: maxSize, maxFiles: maxFiles, tailable: true, format: winston.format.combine( winston.format.timestamp(), sanitizeLogData(), winston.format.errors({ stack: true }), winston.format.json() ) }), new winston.transports.File({ filename: path.join(logDir, 'security.log'), level: 'warn', maxsize: maxSize, maxFiles: maxFiles, tailable: true, format: winston.format.combine( winston.format.timestamp(), winston.format.label({ label: 'SECURITY' }), sanitizeLogData(), winston.format.json() ) }) ); } // Console transport for development if (process.env.NODE_ENV !== 'production') { transports.push( new winston.transports.Console({ format: winston.format.combine( winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), winston.format.colorize(), winston.format.errors({ stack: true }), winston.format.printf(({ timestamp, level, message, ...meta }) => { const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : ''; return `${timestamp} [${level}]: ${message} ${metaStr}`; }) ) }) ); } return winston.createLogger({ level: logLevel, format: winston.format.combine( winston.format.timestamp(), winston.format.errors({ stack: true }), winston.format.splat() ), defaultMeta: { service: 'chatbot-backend', environment: process.env.NODE_ENV, version: process.env.DEPLOYMENT_VERSION || '1.0.0', region: process.env.DEPLOYMENT_REGION || 'unknown' }, transports, // Handle uncaught exceptions and rejections exceptionHandlers: [ new winston.transports.File({ filename: path.join(logDir, 'exceptions.log'), maxsize: maxSize, maxFiles: 5 }) ], rejectionHandlers: [ new winston.transports.File({ filename: path.join(logDir, 'rejections.log'), maxsize: maxSize, maxFiles: 5 }) ], exitOnError: false }); }; const logger = createLogger(); // Add security logging methods logger.security = (message, meta = {}) => { logger.warn(message, { ...meta, type: 'security' }); }; logger.audit = (action, userId, meta = {}) => { logger.info(`AUDIT: ${action}`, { ...meta, type: 'audit', userId, timestamp: new Date().toISOString() }); }; logger.performance = (operation, duration, meta = {}) => { logger.info(`PERFORMANCE: ${operation}`, { ...meta, type: 'performance', duration, timestamp: new Date().toISOString() }); }; module.exports = logger;