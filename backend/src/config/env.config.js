// Configuration des variables d'environnement // Pour la production, utilisez des variables d'environnement réelles module.exports = { NODE_ENV: process.env.NODE_ENV || 'development', PORT: process.env.PORT || 5000, MONGODB_URI: process.env.MONGODB_URI || 'mongodb://localhost:27017/free-mobile-chatbot', JWT_SECRET: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production', OPENAI_API_KEY: process.env.OPENAI_API_KEY || 'your-openai-api-key', RASA_URL: process.env.RASA_URL || 'http://localhost:5005', REDIS_URL: process.env.REDIS_URL || 'redis://localhost:6379' };