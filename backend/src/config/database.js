const mongoose = require('mongoose'); const logger = require('./logger'); const connectDB = async () => { try { console.log('Attempting to connect to MongoDB...'); // Don't log the full URI for security reasons const mongoUri = process.env.MONGODB_URI; if (!mongoUri) { throw new Error('MONGODB_URI environment variable is not set'); } console.log('MongoDB URI configured:', mongoUri.replace(/\/\/[^:]+:[^@]+@/, '//***:***@')); // Enhanced MongoDB connection options for production const connectionOptions = { useNewUrlParser: true, useUnifiedTopology: true, maxPoolSize: 10, // Maintain up to 10 socket connections serverSelectionTimeoutMS: 5000, // Keep trying to send operations for 5 seconds socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity bufferCommands: false, // Disable mongoose buffering bufferMaxEntries: 0, // Disable mongoose buffering // Security enhancements ssl: process.env.NODE_ENV === 'production', // Enable SSL in production sslValidate: process.env.NODE_ENV === 'production', // Validate SSL certificates authSource: 'admin', // Specify auth database // Connection monitoring heartbeatFrequencyMS: 10000, // Send heartbeat every 10 seconds // Retry logic retryWrites: true, retryReads: true, }; // Add SSL certificate paths if provided if (process.env.MONGO_SSL_CERT_PATH) { connectionOptions.sslCert = process.env.MONGO_SSL_CERT_PATH; } if (process.env.MONGO_SSL_KEY_PATH) { connectionOptions.sslKey = process.env.MONGO_SSL_KEY_PATH; } if (process.env.MONGO_SSL_CA_PATH) { connectionOptions.sslCA = process.env.MONGO_SSL_CA_PATH; } console.log('Connecting to MongoDB with options:', { ...connectionOptions, ssl: connectionOptions.ssl }); await mongoose.connect(mongoUri, connectionOptions); console.log('MongoDB connection successful!'); // Connection event handlers mongoose.connection.on('connected', () => { logger.info('MongoDB connected successfully', { host: mongoose.connection.host, port: mongoose.connection.port, database: mongoose.connection.name, ssl: connectionOptions.ssl }); }); mongoose.connection.on('error', (error) => { logger.error('MongoDB connection error:', error); }); mongoose.connection.on('disconnected', () => { logger.warn('MongoDB disconnected'); }); // Graceful shutdown process.on('SIGINT', async () => { try { await mongoose.connection.close(); logger.info('MongoDB connection closed through app termination'); process.exit(0); } catch (error) { logger.error('Error closing MongoDB connection:', error); process.exit(1); } }); logger.info('MongoDB connected successfully'); } catch (error) { logger.error('MongoDB connection error:', error); process.exit(1); } }; module.exports = connectDB;