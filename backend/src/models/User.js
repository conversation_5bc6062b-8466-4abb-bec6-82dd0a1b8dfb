const mongoose = require('mongoose'); const bcrypt = require('bcryptjs'); const userSchema = new mongoose.Schema({ email: { type: String, required: true, unique: true, lowercase: true, trim: true, }, password: { type: String, required: true, }, role: { type: String, enum: ['user', 'agent', 'admin'], default: 'user', }, profile: { firstName: String, lastName: String, phoneNumber: String, customerId: String, }, preferences: { language: { type: String, default: 'fr', }, notifications: { type: Boolean, default: true, }, interactionMode: { type: String, enum: ['auto', 'conversational', 'guided', 'hybrid'], default: 'auto' } }, security: { twoFactor: { enabled: { type: Boolean, default: false }, secret: String, tempSecret: String, backupCodes: [{ code: String, used: { type: Boolean, default: false } }], activatedAt: Date }, lastKnownLocation: { latitude: Number, longitude: Number, city: String, country: String }, securityQuestions: [{ question: String, answerHash: String }], loginAttempts: { count: { type: Number, default: 0 }, lastAttempt: Date, blockedUntil: Date }, riskScore: { type: Number, default: 0, min: 0, max: 1 } }, createdAt: { type: Date, default: Date.now, }, lastLogin: Date, }); userSchema.pre('save', async function(next) { if (!this.isModified('password')) return next(); this.password = await bcrypt.hash(this.password, 10); next(); }); userSchema.methods.comparePassword = async function(candidatePassword) { return await bcrypt.compare(candidatePassword, this.password); }; module.exports = mongoose.model('User', userSchema);