const mongoose = require('mongoose'); const notificationSchema = new mongoose.Schema({ userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true }, type: { type: String, enum: [ 'consumption_alert', // Alerte consommation 'billing_reminder', // Rappel facturation 'offer_suggestion', // Suggestion d'offre 'network_issue', // Problème réseau 'roaming_alert', // Alerte roaming 'promotion', // Promotion 'maintenance', // Maintenance prévue 'churn_prevention', // Prévention churn 'payment_issue' // Problème de paiement ], required: true }, priority: { type: String, enum: ['low', 'medium', 'high', 'critical'], default: 'medium' }, title: { type: String, required: true }, message: { type: String, required: true }, // Actions possibles actions: [{ label: String, action: String, // Type d'action (activate_option, pay_bill, etc.) payload: mongoose.Schema.Types.Mixed }], // Canaux de notification channels: [{ type: String, enum: ['push', 'sms', 'email', 'in_app'], sent: Boolean, sentAt: Date, error: String }], // Contexte et métadonnées context: { triggerEvent: String, // Événement déclencheur dataThreshold: Number, // Ex: 90% de consommation relatedEntity: String, // forfait, facture, etc. relatedEntityId: String }, // Gestion de l'état status: { type: String, enum: ['pending', 'sent', 'read', 'actioned', 'expired', 'cancelled'], default: 'pending' }, readAt: Date, actionedAt: Date, actionTaken: String, expiresAt: Date, // Règles de répétition recurrence: { enabled: Boolean, frequency: String, // daily, weekly, monthly maxOccurrences: Number, currentOccurrence: Number, nextTriggerDate: Date }, // Analytics analytics: { openRate: Number, clickRate: Number, conversionRate: Number, dismissedAt: Date } }, { timestamps: true }); // Index pour les requêtes fréquentes notificationSchema.index({ userId: 1, status: 1, createdAt: -1 }); notificationSchema.index({ expiresAt: 1 }); // Méthode pour vérifier si la notification est encore valide notificationSchema.methods.isValid = function() { if (this.status === 'cancelled' || this.status === 'expired') { return false; } if (this.expiresAt && new Date() > this.expiresAt) { this.status = 'expired'; return false; } return true; }; // Méthode pour marquer comme lue notificationSchema.methods.markAsRead = function() { this.status = 'read'; this.readAt = new Date(); return this.save(); }; module.exports = mongoose.model('Notification', notificationSchema);