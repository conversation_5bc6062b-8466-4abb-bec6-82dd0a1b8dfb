const mongoose = require('mongoose'); const messageSchema = new mongoose.Schema({ conversationId: { type: mongoose.Schema.Types.ObjectId, ref: 'Conversation', required: true, }, sender: { type: String, enum: ['user', 'bot', 'agent'], required: true, }, content: { text: String, type: { type: String, enum: ['text', 'button', 'card', 'image', 'file'], default: 'text', }, payload: Object, }, intent: { name: String, confidence: Number, }, entities: [{ entity: String, value: String, confidence: Number, }], timestamp: { type: Date, default: Date.now, }, metadata: { processingTime: Number, nlpProvider: String, fallback: Boolean, }, }); module.exports = mongoose.model('Message', messageSchema);