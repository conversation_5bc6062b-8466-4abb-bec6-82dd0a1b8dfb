const mongoose = require('mongoose'); const customerProfileSchema = new mongoose.Schema({ userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true, unique: true }, // Informations du forfait subscription: { planName: String, planType: { type: String, enum: ['4G', '5G', 'Fibre'], default: '4G' }, monthlyPrice: Number, dataLimit: Number, // en GB dataUsed: Number, callMinutes: { type: mongoose.Schema.Types.Mixed, // Support pour Number ou String ("illimités") default: 0 }, callMinutesUsed: { type: Number, default: 0 }, smsLimit: { type: mongoose.Schema.Types.Mixed, // Support pour Number ou String ("illimités") default: 0 }, activatedDate: Date, renewalDate: Date, isActive: { type: Boolean, default: true } }, // Options actives activeOptions: [{ name: String, price: Number, activatedDate: Date, description: String }], // Informations de facturation billing: { paymentMethod: { type: String, enum: ['card', 'direct_debit', 'check'], default: 'direct_debit' }, billingDay: Number, // jour du mois lastInvoiceAmount: Number, averageMonthlySpend: Number, outstandingBalance: Number, paymentHistory: [{ date: Date, amount: Number, status: String, method: String }] }, // Profil comportemental behavioralProfile: { type: { type: String, enum: ['geek', 'senior', 'business', 'student', 'family', 'standard'], default: 'standard' }, preferredLanguage: { type: String, default: 'fr' }, communicationPreferences: { tone: String, // formel, décontracté, technique emojis: Boolean, detailLevel: String // simple, détaillé, technique }, churnRisk: { score: Number, // 0-100 factors: [String], lastCalculated: Date } }, // Données techniques technical: { phoneModel: String, os: String, lastNetworkIssue: Date, averageDataSpeed: Number, coverageQuality: String, simType: String, roamingActive: Boolean }, // Historique d'interactions interactionHistory: { totalContacts: Number, lastContactDate: Date, preferredChannel: String, averageResolutionTime: Number, satisfactionScore: Number, frequentIssues: [String] }, // Géolocalisation location: { homeAddress: { street: String, city: String, postalCode: String, country: String, coordinates: { lat: Number, lng: Number } }, currentLocation: { city: String, country: String, isRoaming: Boolean, lastUpdated: Date } }, // Métriques et analytics metrics: { lifetimeValue: Number, nps: Number, csat: Number, monthlyDataUsageTrend: [Number], // 12 derniers mois supportTicketsCount: Number } }, { timestamps: true }); // Méthodes pour calculer les métriques customerProfileSchema.methods.calculateChurnRisk = function() { let riskScore = 0; const factors = []; // Facteurs de risque if (this.metrics.nps < 6) { riskScore += 20; factors.push('NPS faible'); } if (this.billing.averageMonthlySpend < 15) { riskScore += 15; factors.push('Dépense mensuelle faible'); } if (this.interactionHistory.satisfactionScore < 3) { riskScore += 25; factors.push('Satisfaction client faible'); } // Ajouter d'autres facteurs... this.behavioralProfile.churnRisk = { score: Math.min(riskScore, 100), factors, lastCalculated: new Date() }; return this.behavioralProfile.churnRisk; }; module.exports = mongoose.model('CustomerProfile', customerProfileSchema);