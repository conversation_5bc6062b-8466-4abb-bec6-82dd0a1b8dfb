/** * <PERSON><PERSON><PERSON><PERSON> MongoDB pour les classifications ML des conversations * Free Mobile Chatbot Dashboard - Phase 2 Backend Integration */ const mongoose = require('mongoose'); // Schéma pour l'impact business const businessImpactSchema = new mongoose.Schema({ revenueAtRisk: { type: Number, default: 0, min: 0 }, opportunityValue: { type: Number, default: 0, min: 0 }, retentionProbability: { type: Number, required: true, min: 0, max: 1 }, lifetimeValueImpact: { type: Number, default: 0 } }, { _id: false }); // Schéma pour l'analyse de sentiment const sentimentAnalysisSchema = new mongoose.Schema({ score: { type: Number, required: true, min: -1, max: 1 }, trend: { type: String, enum: ['improving', 'declining', 'stable'], required: true }, confidence: { type: Number, required: true, min: 0, max: 1 }, keyEmotions: [{ type: String, trim: true }], history: [{ timestamp: { type: Date, default: Date.now }, score: { type: Number, min: -1, max: 1 }, trigger: { type: String, trim: true } }] }, { _id: false }); // Schéma pour les actions recommandées const recommendedActionSchema = new mongoose.Schema({ type: { type: String, enum: [ 'ESCALATE_TO_AGENT', 'OFFER_UPGRADE', 'RETENTION_CALL', 'TECHNICAL_SUPPORT', 'BILLING_REVIEW', 'SATISFACTION_SURVEY' ], required: true }, priority: { type: Number, required: true, min: 1, max: 10 }, script: { type: String, required: true, trim: true }, expectedOutcome: { type: String, required: true, trim: true }, confidence: { type: Number, required: true, min: 0, max: 1 }, estimatedRevenueImpact: { type: Number, default: 0 } }, { _id: false }); // Schéma principal pour les classifications de conversations const conversationClassificationSchema = new mongoose.Schema({ conversationId: { type: mongoose.Schema.Types.ObjectId, ref: 'Conversation', required: true, index: true }, customerId: { type: mongoose.Schema.Types.ObjectId, ref: 'CustomerProfile', required: true, index: true }, // Classification ML category: { type: String, enum: [ 'VENTE_OPPORTUNITE', 'RESILIATION_CRITIQUE', 'SUPPORT_URGENT', 'RECLAMATION', 'INFO_SIMPLE' ], required: true, index: true }, priorityScore: { type: Number, required: true, min: 0, max: 100, index: true }, confidence: { type: Number, required: true, min: 0, max: 1 }, // Résultats ML détaillés businessImpact: { type: businessImpactSchema, required: true }, sentiment: { type: sentimentAnalysisSchema, required: true }, recommendedActions: [recommendedActionSchema], // Métadonnées ML mlModelVersion: { type: String, required: true, trim: true }, processingTimeMs: { type: Number, required: true, min: 0 }, featuresUsed: [{ type: String, trim: true }], modelLatencyMs: { type: Number, default: 0, min: 0 }, // Validation humaine humanValidated: { type: Boolean, default: false }, validatedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }, validatedAt: { type: Date }, validationFeedback: { type: String, trim: true }, // Métadonnées système cached: { type: Boolean, default: false }, processedAt: { type: Date, default: Date.now, index: true }, nextReview: { type: Date, index: true } }, { timestamps: true, collection: 'conversation_classifications' }); // Index composés pour les requêtes fréquentes conversationClassificationSchema.index({ category: 1, priorityScore: -1, processedAt: -1 }); conversationClassificationSchema.index({ customerId: 1, processedAt: -1 }); conversationClassificationSchema.index({ priorityScore: -1, processedAt: -1 }); conversationClassificationSchema.index({ humanValidated: 1, processedAt: -1 }); // Index TTL pour les données anciennes (optionnel) conversationClassificationSchema.index({ processedAt: 1 }, { expireAfterSeconds: 365 * 24 * 60 * 60 // 1 an }); // Méthodes d'instance conversationClassificationSchema.methods.isHighPriority = function() { return this.priorityScore >= 80; }; conversationClassificationSchema.methods.isCritical = function() { return this.category === 'RESILIATION_CRITIQUE' || this.category === 'SUPPORT_URGENT'; }; conversationClassificationSchema.methods.getRevenueImpact = function() { return this.businessImpact.revenueAtRisk + this.businessImpact.opportunityValue; }; conversationClassificationSchema.methods.needsHumanReview = function() { return this.confidence < 0.7 || this.priorityScore >= 90 || !this.humanValidated; }; // Méthodes statiques conversationClassificationSchema.statics.findHighPriority = function(minPriority = 80, limit = 50) { return this.find({ priorityScore: { $gte: minPriority } }) .sort({ priorityScore: -1, processedAt: -1 }) .limit(limit) .populate('conversationId customerId'); }; conversationClassificationSchema.statics.findByCategory = function(category, limit = 100) { return this.find({ category }) .sort({ processedAt: -1 }) .limit(limit) .populate('conversationId customerId'); }; conversationClassificationSchema.statics.getClassificationStats = function(startDate, endDate) { const matchStage = {}; if (startDate || endDate) { matchStage.processedAt = {}; if (startDate) matchStage.processedAt.$gte = new Date(startDate); if (endDate) matchStage.processedAt.$lte = new Date(endDate); } return this.aggregate([ { $match: matchStage }, { $group: { _id: '$category', count: { $sum: 1 }, avgPriority: { $avg: '$priorityScore' }, avgConfidence: { $avg: '$confidence' }, totalRevenueAtRisk: { $sum: '$businessImpact.revenueAtRisk' }, totalOpportunityValue: { $sum: '$businessImpact.opportunityValue' } } }, { $sort: { count: -1 } } ]); }; conversationClassificationSchema.statics.findUnvalidated = function(limit = 50) { return this.find({ humanValidated: false, $or: [ { confidence: { $lt: 0.7 } }, { priorityScore: { $gte: 80 } } ] }) .sort({ priorityScore: -1, processedAt: -1 }) .limit(limit) .populate('conversationId customerId'); }; // Middleware pre-save conversationClassificationSchema.pre('save', function(next) { // Calculer nextReview basé sur la priorité if (this.isNew) { const hoursToAdd = this.priorityScore >= 90 ? 1 : this.priorityScore >= 70 ? 4 : this.priorityScore >= 50 ? 12 : 24; this.nextReview = new Date(Date.now() + hoursToAdd * 60 * 60 * 1000); } next(); }); // Middleware post-save pour notifications conversationClassificationSchema.post('save', function(doc) { // Émettre des événements pour les classifications critiques if (doc.isHighPriority() && !doc.humanValidated) { // Ici on pourrait émettre un événement WebSocket // ou déclencher une notification console.log(`High priority classification created: ${doc._id}`); } }); module.exports = mongoose.model('ConversationClassification', conversationClassificationSchema);