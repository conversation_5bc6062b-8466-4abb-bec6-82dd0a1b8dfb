/** * ============================================= * EMERGENCY CALL MODEL * MongoDB schema for emergency call records * Complies with French telecommunications regulations * ============================================= */ const mongoose = require('mongoose'); const emergencyCallSchema = new mongoose.Schema({ emergencyCallId: { type: String, required: true, unique: true, index: true }, userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true, index: true }, urgencyLevel: { type: String, enum: ['low', 'normal', 'medium', 'high', 'urgent', 'critical'], default: 'high', required: true }, description: { type: String, maxlength: 1000 }, conversationHistory: [{ messageId: String, content: String, sender: { type: String, enum: ['user', 'bot', 'agent'] }, timestamp: { type: Date, default: Date.now }, metadata: { confidence: Number, intent: String, entities: [String] } }], status: { type: String, enum: [ 'initiated', 'ai_assessment', 'ai_assistance', 'escalated', 'in_queue', 'connected_to_agent', 'in_progress', 'resolved', 'cancelled', 'failed' ], default: 'initiated', required: true, index: true }, routing: { currentRoute: { type: String, enum: [ 'ai_assessment', 'ai_assistance', 'enhanced_ai_assistance', 'priority_queue', 'human_agent_queue', 'immediate_human_transfer', 'active_call' ] }, escalationLevel: { type: Number, default: 0, min: 0, max: 5 }, attemptedRoutes: [{ route: String, timestamp: { type: Date, default: Date.now }, result: { type: String, enum: ['completed', 'failed', 'timeout', 'escalated'] }, reason: String, nextRoute: String, agentPreference: String }] }, queueInfo: { position: Number, estimatedWaitTime: Number, // in milliseconds addedAt: Date, priority: { type: String, enum: ['low', 'normal', 'high', 'critical'], default: 'high' } }, agentInfo: { agentId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }, agentName: String, connectedAt: Date, disconnectedAt: Date, transferReason: String }, webrtcSessionId: { type: String, index: true }, callMetrics: { totalDuration: Number, // in seconds queueWaitTime: Number, // in seconds agentResponseTime: Number, // in seconds resolutionTime: Number, // in seconds customerSatisfactionScore: { type: Number, min: 1, max: 5 }, callQuality: { audioQuality: Number, connectionStability: Number, dropouts: Number } }, clientInfo: { ip: String, userAgent: String, timestamp: Date, location: { country: String, region: String, city: String }, device: { type: String, browser: String, os: String } }, compliance: { recordingConsent: { type: Boolean, default: false }, dataRetentionNotified: { type: Boolean, default: false }, emergencyServiceNotification: { type: Boolean, default: false }, gdprCompliant: { type: Boolean, default: true }, auditTrail: [{ action: String, timestamp: { type: Date, default: Date.now }, userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }, details: mongoose.Schema.Types.Mixed }] }, resolution: { resolvedAt: Date, resolutionType: { type: String, enum: ['ai_resolved', 'agent_resolved', 'escalated_external', 'cancelled'] }, resolutionSummary: String, followUpRequired: { type: Boolean, default: false }, followUpDate: Date, customerFeedback: { rating: { type: Number, min: 1, max: 5 }, comment: String, submittedAt: Date } }, emergencyFlags: { isEmergency: { type: Boolean, default: true }, requiresImmediateAttention: { type: Boolean, default: false }, escalatedToSupervisor: { type: Boolean, default: false }, notifiedEmergencyServices: { type: Boolean, default: false } } }, { timestamps: true, collection: 'emergency_calls' }); // Indexes for performance emergencyCallSchema.index({ emergencyCallId: 1 }); emergencyCallSchema.index({ userId: 1, createdAt: -1 }); emergencyCallSchema.index({ status: 1, createdAt: -1 }); emergencyCallSchema.index({ urgencyLevel: 1, status: 1 }); emergencyCallSchema.index({ 'agentInfo.agentId': 1, status: 1 }); emergencyCallSchema.index({ webrtcSessionId: 1 }); // Compound indexes for common queries emergencyCallSchema.index({ status: 1, urgencyLevel: 1, 'queueInfo.addedAt': 1 }); // TTL index for automatic cleanup (90 days retention) emergencyCallSchema.index({ createdAt: 1 }, { expireAfterSeconds: 7776000 // 90 days }); // Pre-save middleware for audit trail emergencyCallSchema.pre('save', function(next) { if (this.isModified('status')) { this.compliance.auditTrail.push({ action: `Status changed to ${this.status}`, timestamp: new Date(), details: { previousStatus: this.constructor.findOne({ _id: this._id }).status, newStatus: this.status } }); } next(); }); // Methods emergencyCallSchema.methods.addToAuditTrail = function(action, userId, details) { this.compliance.auditTrail.push({ action, userId, details, timestamp: new Date() }); return this.save(); }; emergencyCallSchema.methods.updateStatus = function(newStatus, reason) { this.status = newStatus; this.compliance.auditTrail.push({ action: `Status updated to ${newStatus}`, details: { reason }, timestamp: new Date() }); return this.save(); }; // Static methods emergencyCallSchema.statics.getActiveEmergencyCalls = function() { return this.find({ status: { $in: ['initiated', 'escalated', 'in_queue', 'connected_to_agent', 'in_progress'] } }).populate('userId', 'profile.firstName profile.lastName email'); }; emergencyCallSchema.statics.getQueueStatistics = function() { return this.aggregate([ { $match: { status: { $in: ['escalated', 'in_queue'] } } }, { $group: { _id: '$urgencyLevel', count: { $sum: 1 }, averageWaitTime: { $avg: '$queueInfo.estimatedWaitTime' } } } ]); }; module.exports = mongoose.model('EmergencyCall', emergencyCallSchema);