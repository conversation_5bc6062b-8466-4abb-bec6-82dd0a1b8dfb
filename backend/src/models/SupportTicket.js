const mongoose = require('mongoose'); const supportTicketSchema = new mongoose.Schema({ ticketNumber: { type: String, unique: true, required: true, index: true }, userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true, index: true }, subject: { type: String, required: true, maxlength: 200 }, description: { type: String, required: true, maxlength: 2000 }, category: { type: String, enum: [ 'technique', 'billing', 'account', 'mobile', 'internet', 'forfait', 'options', 'equipment', 'network', 'other' ], required: true, index: true }, priority: { type: String, enum: ['low', 'medium', 'high', 'urgent'], default: 'medium', index: true }, status: { type: String, enum: ['open', 'in_progress', 'waiting_customer', 'resolved', 'closed'], default: 'open', index: true }, assignedTo: { type: mongoose.Schema.Types.ObjectId, ref: 'User', index: true }, tags: [{ type: String, maxlength: 50 }], attachments: [{ filename: String, originalName: String, mimetype: String, size: Number, uploadedAt: { type: Date, default: Date.now }, url: String }], messages: [{ sender: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true }, senderType: { type: String, enum: ['customer', 'agent', 'system'], required: true }, content: { type: String, required: true, maxlength: 2000 }, timestamp: { type: Date, default: Date.now }, isInternal: { type: Boolean, default: false }, attachments: [{ filename: String, originalName: String, mimetype: String, size: Number, url: String }] }], resolution: { summary: String, resolvedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }, resolvedAt: Date, resolutionTime: Number, // in minutes customerSatisfaction: { rating: { type: Number, min: 1, max: 5 }, feedback: String, submittedAt: Date } }, sla: { responseTime: { target: Number, // in minutes actual: Number, breached: { type: Boolean, default: false } }, resolutionTime: { target: Number, // in minutes actual: Number, breached: { type: Boolean, default: false } } }, escalation: { level: { type: Number, default: 0, min: 0, max: 3 }, escalatedAt: Date, escalatedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }, reason: String }, metadata: { source: { type: String, enum: ['web', 'mobile', 'email', 'phone', 'chat'], default: 'web' }, customerInfo: { phoneNumber: String, planType: String, accountStatus: String }, systemInfo: { userAgent: String, ipAddress: String, sessionId: String } } }, { timestamps: true, toJSON: { virtuals: true }, toObject: { virtuals: true } }); // Indexes for performance supportTicketSchema.index({ createdAt: -1 }); supportTicketSchema.index({ updatedAt: -1 }); supportTicketSchema.index({ status: 1, priority: -1 }); supportTicketSchema.index({ assignedTo: 1, status: 1 }); supportTicketSchema.index({ userId: 1, createdAt: -1 }); // Virtual for ticket age supportTicketSchema.virtual('ageInHours').get(function() { return Math.floor((Date.now() - this.createdAt.getTime()) / (1000 * 60 * 60)); }); // Virtual for response time status supportTicketSchema.virtual('responseStatus').get(function() { if (!this.sla.responseTime.target) return 'no_sla'; if (this.sla.responseTime.breached) return 'breached'; if (this.sla.responseTime.actual) return 'met'; const elapsed = Math.floor((Date.now() - this.createdAt.getTime()) / (1000 * 60)); const remaining = this.sla.responseTime.target - elapsed; if (remaining <= 0) return 'breached'; if (remaining <= this.sla.responseTime.target * 0.2) return 'warning'; return 'on_track'; }); // Pre-save middleware to generate ticket number supportTicketSchema.pre('save', async function(next) { if (this.isNew && !this.ticketNumber) { const date = new Date(); const year = date.getFullYear(); const month = String(date.getMonth() + 1).padStart(2, '0'); const day = String(date.getDate()).padStart(2, '0'); // Count tickets created today const startOfDay = new Date(year, date.getMonth(), date.getDate()); const endOfDay = new Date(year, date.getMonth(), date.getDate() + 1); const todayCount = await this.constructor.countDocuments({ createdAt: { $gte: startOfDay, $lt: endOfDay } }); const sequence = String(todayCount + 1).padStart(4, '0'); this.ticketNumber = `FM-${year}${month}${day}-${sequence}`; } // Set SLA targets based on priority if (this.isNew || this.isModified('priority')) { const slaTargets = { urgent: { response: 15, resolution: 240 }, // 15 min, 4 hours high: { response: 60, resolution: 480 }, // 1 hour, 8 hours medium: { response: 240, resolution: 1440 }, // 4 hours, 24 hours low: { response: 480, resolution: 2880 } // 8 hours, 48 hours }; const targets = slaTargets[this.priority] || slaTargets.medium; this.sla.responseTime.target = targets.response; this.sla.resolutionTime.target = targets.resolution; } next(); }); // Static methods supportTicketSchema.statics.findByStatus = function(status, limit = 50) { return this.find({ status }) .populate('userId', 'email profile') .populate('assignedTo', 'email profile') .sort({ priority: -1, createdAt: -1 }) .limit(limit); }; supportTicketSchema.statics.findOverdue = function() { const now = new Date(); return this.find({ status: { $in: ['open', 'in_progress'] }, $or: [ { 'sla.responseTime.breached': true }, { 'sla.resolutionTime.breached': true } ] }) .populate('userId assignedTo') .sort({ priority: -1, createdAt: 1 }); }; supportTicketSchema.statics.getMetrics = async function(dateRange = {}) { const { startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), endDate = new Date() } = dateRange; const pipeline = [ { $match: { createdAt: { $gte: startDate, $lte: endDate } } }, { $group: { _id: null, totalTickets: { $sum: 1 }, openTickets: { $sum: { $cond: [{ $eq: ['$status', 'open'] }, 1, 0] } }, resolvedTickets: { $sum: { $cond: [{ $eq: ['$status', 'resolved'] }, 1, 0] } }, avgResolutionTime: { $avg: '$resolution.resolutionTime' }, slaBreaches: { $sum: { $cond: ['$sla.responseTime.breached', 1, 0] } } } } ]; const result = await this.aggregate(pipeline); return result[0] || {}; }; module.exports = mongoose.model('SupportTicket', supportTicketSchema);