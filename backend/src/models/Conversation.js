const mongoose = require('mongoose'); const conversationSchema = new mongoose.Schema({ userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true, }, sessionId: { type: String, required: true, unique: true, }, status: { type: String, enum: ['active', 'resolved', 'escalated', 'abandoned'], default: 'active', }, channel: { type: String, enum: ['web', 'mobile', 'voice'], default: 'web', }, metadata: { userAgent: String, ipAddress: String, location: Object, }, startedAt: { type: Date, default: Date.now, }, endedAt: Date, agentId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', }, satisfaction: { rating: Number, feedback: String, }, }); module.exports = mongoose.model('Conversation', conversationSchema);