/** * <PERSON><PERSON><PERSON>le MongoDB pour les alertes administrateur * Free Mobile Chatbot Dashboard - Phase 2 Backend Integration */ const mongoose = require('mongoose'); // Schéma pour les conditions de déclenchement const triggerConditionsSchema = new mongoose.Schema({ rule: { type: String, required: true, trim: true }, conditions: { type: mongoose.Schema.Types.Mixed, required: true }, mlScore: { type: Number, min: 0, max: 1 }, threshold: { type: Number } }, { _id: false }); // Schéma pour la résolution const resolutionSchema = new mongoose.Schema({ action: { type: String, required: true, trim: true }, outcome: { type: String, required: true, trim: true }, revenueImpact: { type: Number, default: 0 }, resolvedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true }, resolvedAt: { type: Date, default: Date.now }, notes: { type: String, trim: true }, followUpRequired: { type: Boolean, default: false }, followUpDate: { type: Date } }, { _id: false }); // Schéma pour l'historique d'escalade const escalationHistorySchema = new mongoose.Schema({ level: { type: Number, required: true, min: 1, max: 5 }, escalatedTo: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true }, escalatedAt: { type: Date, default: Date.now }, reason: { type: String, required: true, trim: true }, previousAssignee: { type: mongoose.Schema.Types.ObjectId, ref: 'User' } }, { _id: false }); // Schéma pour les métriques const alertMetricsSchema = new mongoose.Schema({ timeToDetect: { type: Number, // en secondes min: 0 }, timeToAcknowledge: { type: Number, // en secondes min: 0 }, timeToResolve: { type: Number, // en secondes min: 0 }, businessOutcome: { type: String, enum: ['POSITIVE', 'NEGATIVE', 'NEUTRAL', 'PENDING'], default: 'PENDING' }, customerSatisfactionImpact: { type: Number, min: -5, max: 5, default: 0 } }, { _id: false }); // Schéma principal pour les alertes admin const adminAlertSchema = new mongoose.Schema({ // Identification type: { type: String, enum: [ 'VENTE_OPPORTUNITY', 'CHURN_RISK', 'ESCALATION_NEEDED', 'TECHNICAL_ISSUE', 'BILLING_DISPUTE', 'SATISFACTION_CRITICAL', 'SYSTEM_ANOMALY', 'PERFORMANCE_DEGRADATION' ], required: true, index: true }, severity: { type: String, enum: ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'], required: true, index: true }, // Références conversationId: { type: mongoose.Schema.Types.ObjectId, ref: 'Conversation', index: true }, customerId: { type: mongoose.Schema.Types.ObjectId, ref: 'CustomerProfile', required: true, index: true }, classificationId: { type: mongoose.Schema.Types.ObjectId, ref: 'ConversationClassification', index: true }, // Détails de l'alerte title: { type: String, required: true, trim: true, maxlength: 200 }, description: { type: String, required: true, trim: true, maxlength: 1000 }, triggeredBy: { type: triggerConditionsSchema, required: true }, // Gestion status: { type: String, enum: ['ACTIVE', 'ACKNOWLEDGED', 'IN_PROGRESS', 'RESOLVED', 'DISMISSED', 'EXPIRED'], default: 'ACTIVE', index: true }, assignedTo: { type: mongoose.Schema.Types.ObjectId, ref: 'User', index: true }, assignedAt: { type: Date }, acknowledgedAt: { type: Date }, acknowledgedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }, // Résolution resolution: { type: resolutionSchema }, // Escalade escalationLevel: { type: Number, default: 1, min: 1, max: 5 }, escalationHistory: [escalationHistorySchema], autoEscalationEnabled: { type: Boolean, default: true }, escalationThresholdHours: { type: Number, default: 2, min: 0.5, max: 48 }, // Métriques metrics: { type: alertMetricsSchema, default: () => ({}) }, // Métadonnées tags: [{ type: String, trim: true, lowercase: true }], priority: { type: Number, min: 1, max: 100, default: 50 }, expiresAt: { type: Date, index: { expireAfterSeconds: 0 } }, // Notifications notificationsSent: [{ type: { type: String, enum: ['EMAIL', 'SMS', 'PUSH', 'WEBHOOK', 'SLACK'] }, recipient: String, sentAt: { type: Date, default: Date.now }, status: { type: String, enum: ['SENT', 'DELIVERED', 'FAILED', 'PENDING'] } }], // Données contextuelles contextData: { type: mongoose.Schema.Types.Mixed } }, { timestamps: true, collection: 'admin_alerts' }); // Index composés pour les requêtes fréquentes adminAlertSchema.index({ type: 1, severity: 1, status: 1, createdAt: -1 }); adminAlertSchema.index({ assignedTo: 1, status: 1, createdAt: -1 }); adminAlertSchema.index({ customerId: 1, createdAt: -1 }); adminAlertSchema.index({ status: 1, priority: -1, createdAt: -1 }); // Index pour l'auto-escalade adminAlertSchema.index({ status: 1, autoEscalationEnabled: 1, createdAt: 1 }); // Méthodes d'instance adminAlertSchema.methods.isCritical = function() { return this.severity === 'CRITICAL'; }; adminAlertSchema.methods.isOverdue = function() { if (!this.escalationThresholdHours) return false; const thresholdMs = this.escalationThresholdHours * 60 * 60 * 1000; const referenceTime = this.acknowledgedAt || this.createdAt; return Date.now() - referenceTime.getTime() > thresholdMs; }; adminAlertSchema.methods.acknowledge = function(userId) { this.status = 'ACKNOWLEDGED'; this.acknowledgedAt = new Date(); this.acknowledgedBy = userId; // Calculer time to acknowledge if (this.metrics) { this.metrics.timeToAcknowledge = Math.floor( (this.acknowledgedAt - this.createdAt) / 1000 ); } return this.save(); }; adminAlertSchema.methods.assign = function(userId) { this.assignedTo = userId; this.assignedAt = new Date(); if (this.status === 'ACTIVE') { this.status = 'ACKNOWLEDGED'; } return this.save(); }; adminAlertSchema.methods.escalate = function(toUserId, reason) { const escalationEntry = { level: this.escalationLevel + 1, escalatedTo: toUserId, reason: reason, previousAssignee: this.assignedTo }; this.escalationHistory.push(escalationEntry); this.escalationLevel += 1; this.assignedTo = toUserId; this.assignedAt = new Date(); return this.save(); }; adminAlertSchema.methods.resolve = function(resolution) { this.status = 'RESOLVED'; this.resolution = { ...resolution, resolvedAt: new Date() }; // Calculer time to resolve if (this.metrics) { this.metrics.timeToResolve = Math.floor( (Date.now() - this.createdAt.getTime()) / 1000 ); } return this.save(); }; // Méthodes statiques adminAlertSchema.statics.findActive = function(limit = 50) { return this.find({ status: { $in: ['ACTIVE', 'ACKNOWLEDGED', 'IN_PROGRESS'] } }) .sort({ priority: -1, createdAt: -1 }) .limit(limit) .populate('assignedTo customerId conversationId'); }; adminAlertSchema.statics.findCritical = function(limit = 20) { return this.find({ severity: 'CRITICAL', status: { $ne: 'RESOLVED' } }) .sort({ createdAt: -1 }) .limit(limit) .populate('assignedTo customerId conversationId'); }; adminAlertSchema.statics.findOverdue = function() { const now = new Date(); return this.find({ status: { $in: ['ACTIVE', 'ACKNOWLEDGED', 'IN_PROGRESS'] }, autoEscalationEnabled: true, $expr: { $gt: [ { $subtract: [now, { $ifNull: ['$acknowledgedAt', '$createdAt'] }] }, { $multiply: ['$escalationThresholdHours', 3600000] } // Convert hours to ms ] } }) .populate('assignedTo customerId'); }; adminAlertSchema.statics.getAlertStats = function(startDate, endDate) { const matchStage = {}; if (startDate || endDate) { matchStage.createdAt = {}; if (startDate) matchStage.createdAt.$gte = new Date(startDate); if (endDate) matchStage.createdAt.$lte = new Date(endDate); } return this.aggregate([ { $match: matchStage }, { $group: { _id: { type: '$type', severity: '$severity' }, count: { $sum: 1 }, avgTimeToResolve: { $avg: '$metrics.timeToResolve' }, avgTimeToAcknowledge: { $avg: '$metrics.timeToAcknowledge' }, resolvedCount: { $sum: { $cond: [{ $eq: ['$status', 'RESOLVED'] }, 1, 0] } } } }, { $sort: { count: -1 } } ]); }; // Middleware pre-save adminAlertSchema.pre('save', function(next) { // Définir la date d'expiration si pas définie if (this.isNew && !this.expiresAt) { const daysToExpire = this.severity === 'CRITICAL' ? 7 : this.severity === 'HIGH' ? 14 : this.severity === 'MEDIUM' ? 30 : 60; this.expiresAt = new Date(Date.now() + daysToExpire * 24 * 60 * 60 * 1000); } next(); }); module.exports = mongoose.model('AdminAlert', adminAlertSchema);