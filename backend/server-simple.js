require('dotenv').config({ path: '../.env' });
const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors({
  origin: process.env.NODE_ENV === 'production' ? process.env.FRONTEND_URL : 'http://localhost:3000',
  credentials: true
}));

app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Basic health check
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'ChatbotRNCP Backend Server is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'ChatbotRNCP Backend API is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// Basic auth endpoints for testing
app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;
  
  // Simple hardcoded admin check
  if (email === '<EMAIL>' && password === 'AdminFreeMobile2024!') {
    res.json({
      success: true,
      message: 'Login successful',
      token: 'mock-jwt-token-' + Date.now(),
      user: {
        id: 'admin-user-id',
        email: '<EMAIL>',
        role: 'admin',
        profile: {
          firstName: 'Admin',
          lastName: 'Free Mobile'
        }
      }
    });
  } else {
    res.status(401).json({
      success: false,
      error: 'Invalid credentials'
    });
  }
});

app.post('/api/auth/register', (req, res) => {
  res.json({
    success: true,
    message: 'Registration endpoint available',
    note: 'Registration functionality will be implemented'
  });
});

app.get('/api/auth/status', (req, res) => {
  res.json({
    authenticated: false,
    message: 'Authentication status endpoint'
  });
});

// Basic dashboard endpoints
app.get('/api/dashboard', (req, res) => {
  res.json({
    success: true,
    data: {
      totalConversations: 150,
      activeConversations: 12,
      resolvedConversations: 138,
      avgSatisfaction: 4.2,
      recentActivity: []
    }
  });
});

app.get('/api/dashboard/stats', (req, res) => {
  res.json({
    success: true,
    stats: {
      users: 1250,
      conversations: 3420,
      satisfaction: 4.3,
      responseTime: 2.5
    }
  });
});

// Basic user endpoints
app.get('/api/users/me', (req, res) => {
  res.json({
    success: true,
    user: {
      id: 'admin-user-id',
      email: '<EMAIL>',
      role: 'admin',
      profile: {
        firstName: 'Admin',
        lastName: 'Free Mobile'
      }
    }
  });
});

// Basic chat endpoints
app.post('/api/chat/start', (req, res) => {
  res.json({
    success: true,
    conversationId: 'conv-' + Date.now(),
    message: 'Chat session started'
  });
});

app.post('/api/chat/message', (req, res) => {
  res.json({
    success: true,
    response: {
      text: 'Bonjour! Je suis l\'assistant virtuel Free Mobile. Comment puis-je vous aider?',
      type: 'text'
    }
  });
});

// Catch all for undefined routes
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    path: req.originalUrl,
    method: req.method
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Server error:', error);
  res.status(500).json({
    error: 'Internal server error',
    message: error.message
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 ChatbotRNCP Backend Server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`🔐 API Health: http://localhost:${PORT}/api/health`);
  console.log(`👤 Admin login: <EMAIL> / AdminFreeMobile2024!`);
});

module.exports = app;
