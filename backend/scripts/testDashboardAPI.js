/** * Test Dashboard API with real database data */ const axios = require('axios'); const API_BASE_URL = 'http://localhost:5000/api'; async function testDashboardAPI() { try { console.log(' Testing Dashboard API with real database data...\n'); // Step 1: Login to get authentication token console.log('[SECURITY] Step 1: Authenticating...'); const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, { email: '<EMAIL>', password: 'admin123' }); if (loginResponse.data.success) { console.log('[COMPLETE] Login successful'); const token = loginResponse.data.token; // Step 2: Test dashboard simulations endpoint console.log('\n[ANALYTICS] Step 2: Testing dashboard simulations endpoint...'); const dashboardResponse = await axios.get(`${API_BASE_URL}/dashboard/simulations`, { headers: { 'Authorization': `Bearer ${token}`, 'Content-Type': 'application/json' } }); if (dashboardResponse.data.success) { console.log('[COMPLETE] Dashboard endpoint successful!'); console.log(`[METRICS] Found ${dashboardResponse.data.data.scenarios.length} scenarios`); // Display first scenario details if (dashboardResponse.data.data.scenarios.length > 0) { const firstScenario = dashboardResponse.data.data.scenarios[0]; console.log('\n[TARGET] First Scenario Details:'); console.log(` - ID: ${firstScenario.id}`); console.log(` - Title: ${firstScenario.title}`); console.log(` - Difficulty: ${firstScenario.difficulty}`); console.log(` - Category: ${firstScenario.category}`); console.log(` - Customer: ${firstScenario.customer_profile.name} (${firstScenario.customer_profile.personality})`); console.log(` - Effectiveness: ${firstScenario.effectiveness_score}%`); } // Test with filters console.log('\n[SEARCH] Step 3: Testing with filters...'); const filteredResponse = await axios.get(`${API_BASE_URL}/dashboard/simulations?difficulty=intermediate&category=billing`, { headers: { 'Authorization': `Bearer ${token}`, 'Content-Type': 'application/json' } }); console.log(`[COMPLETE] Filtered results: ${filteredResponse.data.data.scenarios.length} scenarios`); // Test pagination console.log('\n Step 4: Testing pagination...'); const paginatedResponse = await axios.get(`${API_BASE_URL}/dashboard/simulations?limit=2&page=1`, { headers: { 'Authorization': `Bearer ${token}`, 'Content-Type': 'application/json' } }); if (paginatedResponse.data.data.pagination) { const pagination = paginatedResponse.data.data.pagination; console.log(`[COMPLETE] Pagination working: Page ${pagination.current_page}/${pagination.total_pages} (${pagination.total_count} total)`); } console.log('\n All dashboard API tests passed!'); console.log('\n Summary:'); console.log(` - Authentication: [COMPLETE] Working`); console.log(` - Database Integration: [COMPLETE] Working`); console.log(` - Filtering: [COMPLETE] Working`); console.log(` - Pagination: [COMPLETE] Working`); console.log(` - Data Transformation: [COMPLETE] Working`); } else { console.error('[FAILED] Dashboard endpoint failed:', dashboardResponse.data); } } else { console.error('[FAILED] Login failed:', loginResponse.data); } } catch (error) { console.error('[FAILED] Test failed:', error.message); if (error.response) { console.error('Response status:', error.response.status); console.error('Response data:', error.response.data); } } } if (require.main === module) { testDashboardAPI(); } module.exports = { testDashboardAPI };