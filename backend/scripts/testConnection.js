/** * Test MongoDB connection and schema validation */ const path = require('path'); require('dotenv').config({ path: path.join(__dirname, '../../.env') }); const mongoose = require('mongoose'); const { SimulationScenario, AgentProgress } = require('../src/database/mongoSchemas'); async function testConnection() { try { console.log(' Testing MongoDB connection...'); console.log('NODE_ENV:', process.env.NODE_ENV); console.log('MONGO_USERNAME:', process.env.MONGO_USERNAME); console.log('URI:', process.env.MONGODB_URI); await mongoose.connect(process.env.MONGODB_URI, { useNewUrlParser: true, useUnifiedTopology: true, }); console.log('[COMPLETE] Connected to MongoDB successfully!'); // Test creating a simple scenario const testScenario = new SimulationScenario({ title: "Test Scenario", description: "A test scenario for schema validation", difficulty: "beginner", category: "billing", tags: ["test"], customer_profile: { id: "test-customer", name: "Test Customer", age: 30, personality: "calm", issue_type: "billing", complexity: "simple", platform: "whatsapp", history: [], satisfaction_threshold: 7 }, context: { situation: "Test situation", background: "Test background", desired_outcome: "Test outcome", escalation_triggers: ["test_trigger"] }, learning_objectives: ["empathy"], success_criteria: { min_empathy_score: 70, min_efficiency_score: 70, min_accuracy_score: 70, max_resolution_time: 30, customer_satisfaction_target: 7 }, expected_resolution_time: 25, popularity: 50, effectiveness_score: 60 }); console.log(' Testing scenario creation...'); const savedScenario = await testScenario.save(); console.log('[COMPLETE] Scenario created successfully:', savedScenario._id); // Test creating agent progress const testProgress = new AgentProgress({ agent_id: new mongoose.Types.ObjectId(), total_sessions: 1, completed_sessions: 1, average_score: 85, skill_levels: { empathy: 80, efficiency: 85, accuracy: 90, communication: 75, problem_solving: 80 }, badges_earned: [{ id: "test_badge", name: "Test Badge", description: "A test badge", icon: "[TARGET]", earned_date: new Date(), rarity: "common" }], next_milestone: { id: "test_milestone", name: "Test Milestone", description: "A test milestone", progress: 50, target: 100, reward: "Test Reward" } }); console.log(' Testing agent progress creation...'); const savedProgress = await testProgress.save(); console.log('[COMPLETE] Agent progress created successfully:', savedProgress._id); // Clean up test data await SimulationScenario.deleteOne({ _id: savedScenario._id }); await AgentProgress.deleteOne({ _id: savedProgress._id }); console.log(' Test data cleaned up'); console.log(' All tests passed! Schema is working correctly.'); } catch (error) { console.error('[FAILED] Test failed:', error.message); if (error.errors) { console.error('Validation errors:'); Object.keys(error.errors).forEach(key => { console.error(` - ${key}: ${error.errors[key].message}`); }); } } finally { await mongoose.connection.close(); console.log(' Database connection closed'); } } if (require.main === module) { testConnection(); } module.exports = { testConnection };