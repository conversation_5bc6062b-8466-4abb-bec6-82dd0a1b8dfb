/** * ============================================= * SIMULATION DATA SEEDER * Populates database with sample simulation scenarios and agent progress * Run with: node scripts/seedSimulationData.js * ============================================= */ const path = require('path'); require('dotenv').config({ path: path.join(__dirname, '../../.env') }); const mongoose = require('mongoose'); const { v4: uuidv4 } = require('uuid'); const { SimulationScenario, SimulationSession, AgentProgress } = require('../src/database/mongoSchemas'); const logger = require('../src/config/logger'); // Sample simulation scenarios const sampleScenarios = [ { title: "Billing Dispute Resolution", description: "Handle a customer complaint about unexpected charges on their mobile bill. The customer is frustrated and demands immediate resolution.", difficulty: "intermediate", category: "billing", tags: ["dispute", "charges", "customer_service", "resolution"], customer_profile: { id: "customer-1", name: "Frustrated Customer", age: 35, personality: "frustrated", issue_type: "billing", complexity: "moderate", platform: "whatsapp", history: [], satisfaction_threshold: 8, // Legacy fields tier: "gold", communication_style: "aggressive", emotional_state: "frustrated" }, context: { situation: "Customer received a bill with unexpected roaming charges", background: "Customer traveled abroad but claims they disabled roaming", desired_outcome: "Refund or explanation of charges", escalation_triggers: ["dismissive_response", "no_solution_offered", "long_wait_time"] }, learning_objectives: ["empathy", "problem_solving", "communication"], success_criteria: { min_empathy_score: 80, min_efficiency_score: 70, min_accuracy_score: 85, max_resolution_time: 25, customer_satisfaction_target: 8 }, expected_resolution_time: 20, popularity: 85, effectiveness_score: 78 }, { title: "Technical Support - Network Issues", description: "Assist a customer experiencing poor network coverage in their area. They need technical troubleshooting and potential solutions.", difficulty: "beginner", category: "technical", tags: ["network", "coverage", "troubleshooting", "technical_support"], customer_profile: { id: "customer-2", name: "Confused Customer", age: 28, personality: "confused", issue_type: "technical", complexity: "simple", platform: "whatsapp", history: [], satisfaction_threshold: 7, // Legacy fields tier: "standard", communication_style: "confused", emotional_state: "calm" }, context: { situation: "Customer has weak signal at home", background: "Recently moved to a new area", desired_outcome: "Improved network coverage or alternative solution", escalation_triggers: ["technical_jargon", "no_clear_steps", "dismissive_attitude"] }, learning_objectives: ["communication", "efficiency", "problem_solving"], success_criteria: { min_empathy_score: 70, min_efficiency_score: 80, min_accuracy_score: 90, max_resolution_time: 15, customer_satisfaction_target: 7 }, expected_resolution_time: 12, popularity: 92, effectiveness_score: 85 }, { title: "Account Security Breach", description: "Handle a critical security incident where a customer's account may have been compromised. Requires immediate action and careful communication.", difficulty: "expert", category: "technical", tags: ["security", "breach", "urgent", "account_protection"], customer_profile: { id: "customer-3", name: "Urgent Customer", age: 42, personality: "demanding", issue_type: "technical", complexity: "complex", platform: "call", history: [], satisfaction_threshold: 9, // Legacy fields tier: "platinum", communication_style: "direct", emotional_state: "urgent" }, context: { situation: "Suspicious activity detected on customer account", background: "Customer received alerts about unauthorized access attempts", desired_outcome: "Secure account and prevent further unauthorized access", escalation_triggers: ["delayed_response", "insufficient_security_measures", "data_exposure"] }, learning_objectives: ["accuracy", "efficiency", "communication", "problem_solving"], success_criteria: { min_empathy_score: 75, min_efficiency_score: 95, min_accuracy_score: 98, max_resolution_time: 10, customer_satisfaction_target: 9 }, expected_resolution_time: 8, popularity: 65, effectiveness_score: 92 }, { title: "Service Upgrade Consultation", description: "Guide a customer through available service upgrades and help them choose the best plan for their needs.", difficulty: "intermediate", category: "sales", tags: ["upgrade", "consultation", "sales", "customer_retention"], customer_profile: { id: "customer-4", name: "Friendly Customer", age: 31, personality: "friendly", issue_type: "service", complexity: "moderate", platform: "whatsapp", history: [], satisfaction_threshold: 8, // Legacy fields tier: "silver", communication_style: "polite", emotional_state: "calm" }, context: { situation: "Customer wants to upgrade their mobile plan", background: "Current plan doesn't meet their data needs", desired_outcome: "Find suitable upgrade with good value", escalation_triggers: ["pushy_sales_tactics", "unclear_pricing", "hidden_fees"] }, learning_objectives: ["communication", "empathy", "problem_solving"], success_criteria: { min_empathy_score: 85, min_efficiency_score: 75, min_accuracy_score: 80, max_resolution_time: 30, customer_satisfaction_target: 8 }, expected_resolution_time: 25, popularity: 78, effectiveness_score: 73 }, { title: "Service Cancellation Request", description: "Handle a customer who wants to cancel their service. Focus on retention while respecting their decision.", difficulty: "expert", category: "retention", tags: ["cancellation", "retention", "customer_service", "negotiation"], customer_profile: { id: "customer-5", name: "Dissatisfied Customer", age: 39, personality: "frustrated", issue_type: "complaint", complexity: "complex", platform: "call", history: [], satisfaction_threshold: 6, // Legacy fields tier: "bronze", communication_style: "direct", emotional_state: "frustrated" }, context: { situation: "Customer wants to cancel due to poor service experience", background: "Multiple previous complaints not resolved satisfactorily", desired_outcome: "Cancel service or find acceptable resolution", escalation_triggers: ["retention_pressure", "ignoring_concerns", "complicated_process"] }, learning_objectives: ["empathy", "communication", "problem_solving"], success_criteria: { min_empathy_score: 90, min_efficiency_score: 70, min_accuracy_score: 85, max_resolution_time: 35, customer_satisfaction_target: 7 }, expected_resolution_time: 30, popularity: 55, effectiveness_score: 68 } ]; // Sample agent progress data const sampleAgentProgress = [ { agent_id: new mongoose.Types.ObjectId(), total_sessions: 45, completed_sessions: 42, average_score: 87, skill_levels: { empathy: 85, efficiency: 90, accuracy: 88, communication: 86, problem_solving: 89 }, badges_earned: [ { id: "first_steps", name: "First Steps", description: "Completed first simulation", icon: "[TARGET]", earned_date: new Date(), rarity: "common", earned_at: new Date() // Legacy field }, { id: "problem_solver", name: "Problem Solver", description: "Resolved 25 simulations", icon: "", earned_date: new Date(), rarity: "rare", earned_at: new Date() // Legacy field }, { id: "empathy_expert", name: "Empathy Expert", description: "Achieved 90+ empathy score", icon: "", earned_date: new Date(), rarity: "epic", earned_at: new Date() // Legacy field } ], next_milestone: { name: "Master Communicator", description: "Complete 50 simulations with 85+ average score", progress: 42, target: 50 }, created_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago updated_at: new Date() }, { agent_id: new mongoose.Types.ObjectId(), total_sessions: 28, completed_sessions: 25, average_score: 76, skill_levels: { empathy: 78, efficiency: 75, accuracy: 80, communication: 72, problem_solving: 74 }, badges_earned: [ { id: "first_steps", name: "First Steps", description: "Completed first simulation", icon: "[TARGET]", earned_date: new Date(), rarity: "common", earned_at: new Date() // Legacy field }, { id: "quick_learner", name: "Quick Learner", description: "Completed 10 simulations", icon: "[PERFORMANCE]", earned_date: new Date(), rarity: "common", earned_at: new Date() // Legacy field } ], next_milestone: { name: "Problem Solver", description: "Resolve 25 simulations successfully", progress: 25, target: 25 }, created_at: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000), // 20 days ago updated_at: new Date() } ]; // Sample simulation sessions data const sampleSessions = [ { _id: uuidv4(), scenario_id: null, // Will be set dynamically agent_id: null, // Will be set dynamically status: 'completed', score: 85, duration: 420, // 7 minutes messages_count: 12, customer_satisfaction: 4.2, started_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago completed_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000 + 420000), last_message_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000 + 420000) }, { _id: uuidv4(), scenario_id: null, // Will be set dynamically agent_id: null, // Will be set dynamically status: 'completed', score: 92, duration: 380, // 6.3 minutes messages_count: 8, customer_satisfaction: 4.8, started_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago completed_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000 + 380000), last_message_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000 + 380000) }, { _id: uuidv4(), scenario_id: null, // Will be set dynamically agent_id: null, // Will be set dynamically status: 'active', score: 0, duration: 180, // 3 minutes so far messages_count: 5, customer_satisfaction: 0, started_at: new Date(Date.now() - 3 * 60 * 1000), // 3 minutes ago completed_at: null, last_message_at: new Date(Date.now() - 30 * 1000) // 30 seconds ago }, { _id: uuidv4(), scenario_id: null, // Will be set dynamically agent_id: null, // Will be set dynamically status: 'completed', score: 78, duration: 540, // 9 minutes messages_count: 15, customer_satisfaction: 3.9, started_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago completed_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000 + 540000), last_message_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000 + 540000) }, { _id: uuidv4(), scenario_id: null, // Will be set dynamically agent_id: null, // Will be set dynamically status: 'abandoned', score: 0, duration: 120, // 2 minutes before abandoning messages_count: 3, customer_satisfaction: 0, started_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago completed_at: null, last_message_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000 + 120000) } ]; async function seedDatabase() { try { // Connect to MongoDB await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/freemobile_chatbot', { useNewUrlParser: true, useUnifiedTopology: true, }); logger.info('Connected to MongoDB for seeding'); // Clear existing data (optional - comment out if you want to keep existing data) // await SimulationScenario.deleteMany({}); // await AgentProgress.deleteMany({}); // logger.info('Cleared existing simulation data'); // Insert sample scenarios const insertedScenarios = await SimulationScenario.insertMany(sampleScenarios); logger.info(`Inserted ${insertedScenarios.length} simulation scenarios`); // Insert sample agent progress const insertedProgress = await AgentProgress.insertMany(sampleAgentProgress); logger.info(`Inserted ${insertedProgress.length} agent progress records`); // Create sample sessions with references to inserted scenarios and agents const sessionsWithRefs = sampleSessions.map((session, index) => ({ ...session, scenario_id: insertedScenarios[index % insertedScenarios.length]._id, agent_id: insertedProgress[index % insertedProgress.length].agent_id })); const insertedSessions = await SimulationSession.insertMany(sessionsWithRefs); logger.info(`Inserted ${insertedSessions.length} simulation sessions`); logger.info('Database seeding completed successfully!'); // Display summary console.log('\n[ANALYTICS] SEEDING SUMMARY:'); console.log(`[COMPLETE] Scenarios: ${insertedScenarios.length}`); console.log(`[COMPLETE] Agent Progress: ${insertedProgress.length}`); console.log(`[COMPLETE] Sessions: ${insertedSessions.length}`); console.log('\n[TARGET] Ready to test dashboard endpoints!'); } catch (error) { logger.error('Error seeding database:', error); process.exit(1); } finally { await mongoose.connection.close(); logger.info('Database connection closed'); } } // Run the seeder if (require.main === module) { seedDatabase(); } module.exports = { seedDatabase, sampleScenarios, sampleAgentProgress };