/** * Script de test d'intégration ML * Free Mobile Chatbot Dashboard - Phase 2 Backend Integration * Test de la communication entre le backend Node.js et le service ML FastAPI */ require('dotenv').config({ path: '../../.env' }); const axios = require('axios'); const mongoose = require('mongoose'); const mlIntegrationService = require('../src/services/mlIntegrationService'); const ConversationClassification = require('../src/models/ConversationClassification'); const AdminAlert = require('../src/models/AdminAlert'); // Configuration des couleurs pour les logs const colors = { reset: '\x1b[0m', bright: '\x1b[1m', red: '\x1b[31m', green: '\x1b[32m', yellow: '\x1b[33m', blue: '\x1b[34m', magenta: '\x1b[35m', cyan: '\x1b[36m' }; function log(message, color = 'reset') { console.log(`${colors[color]}${message}${colors.reset}`); } function logSuccess(message) { log(`[COMPLETE] ${message}`, 'green'); } function logError(message) { log(`[FAILED] ${message}`, 'red'); } function logInfo(message) { log(`ℹ ${message}`, 'blue'); } function logWarning(message) { log(` ${message}`, 'yellow'); } async function connectDatabase() { try { await mongoose.connect(process.env.MONGODB_URI); logSuccess('Connected to MongoDB'); } catch (error) { logError(`MongoDB connection failed: ${error.message}`); throw error; } } async function testMLServiceHealth() { logInfo('Testing ML Service Health...'); try { const health = await mlIntegrationService.healthCheck(); if (health.status === 'healthy') { logSuccess('ML Service is healthy'); log(` Models loaded: ${Object.keys(health.ml_service.models_loaded).length}`, 'cyan'); log(` Redis connected: ${health.ml_service.redis_connected}`, 'cyan'); log(` Memory usage: ${health.ml_service.memory_usage_mb.toFixed(2)} MB`, 'cyan'); } else { logWarning('ML Service is unhealthy'); log(` Error: ${health.error}`, 'yellow'); } return health.status === 'healthy'; } catch (error) { logError(`ML Service health check failed: ${error.message}`); return false; } } async function testMLClassification() { logInfo('Testing ML Classification...'); try { // Données de test const testConversationData = { conversationId: new mongoose.Types.ObjectId(), messages: [ { _id: new mongoose.Types.ObjectId(), sender: 'user', content: 'Bonjour, je veux résilier mon abonnement car c\'est trop cher', timestamp: new Date() }, { _id: new mongoose.Types.ObjectId(), sender: 'bot', content: 'Je comprends votre demande. Puis-je connaître la raison principale?', timestamp: new Date() }, { _id: new mongoose.Types.ObjectId(), sender: 'user', content: 'Le service ne marche pas bien et c\'est trop cher pour ce que c\'est', timestamp: new Date() } ], customerData: { _id: new mongoose.Types.ObjectId(), planType: '20€', monthlyRevenue: 20.0, tenureMonths: 8, supportTicketsCount: 3, satisfactionScore: 2.5, lifetimeValue: 160.0 }, metadata: { channel: 'web', durationMinutes: 5, previousInteractions: 1 } }; // Test de classification const result = await mlIntegrationService.classifyConversation(testConversationData); if (result.success) { logSuccess('ML Classification successful'); log(` Category: ${result.classification.category}`, 'cyan'); log(` Priority Score: ${result.classification.priorityScore}`, 'cyan'); log(` Confidence: ${result.classification.confidence.toFixed(3)}`, 'cyan'); log(` Revenue at Risk: ${result.classification.businessImpact.revenueAtRisk}€`, 'cyan'); log(` Sentiment Score: ${result.classification.sentiment.score.toFixed(3)}`, 'cyan'); log(` Recommended Actions: ${result.classification.recommendedActions.length}`, 'cyan'); return result.classification; } else { logError(`ML Classification failed: ${result.error}`); return null; } } catch (error) { logError(`ML Classification test failed: ${error.message}`); return null; } } async function testPriorityQueue() { logInfo('Testing Priority Queue...'); try { const queue = await mlIntegrationService.getPriorityQueue({ limit: 10, minPriority: 50 }); logSuccess(`Priority Queue retrieved: ${queue.length} items`); if (queue.length > 0) { log(' Top priority items:', 'cyan'); queue.slice(0, 3).forEach((item, index) => { log(` ${index + 1}. Priority: ${item.priority_score}, Category: ${item.category}`, 'cyan'); }); } return queue; } catch (error) { logError(`Priority Queue test failed: ${error.message}`); return []; } } async function testPerformanceMetrics() { logInfo('Testing Performance Metrics...'); try { const metrics = await mlIntegrationService.getPerformanceMetrics(); logSuccess('Performance Metrics retrieved'); log(` Total Classifications: ${metrics.total_classifications || 0}`, 'cyan'); log(` Average Processing Time: ${(metrics.average_processing_time_ms || 0).toFixed(2)} ms`, 'cyan'); log(` Throughput: ${(metrics.throughput_per_second || 0).toFixed(2)} req/sec`, 'cyan'); log(` Cache Hit Rate: ${((metrics.cache_hit_rate || 0) * 100).toFixed(1)}%`, 'cyan'); return metrics; } catch (error) { logError(`Performance Metrics test failed: ${error.message}`); return null; } } async function testDatabaseOperations(classification) { logInfo('Testing Database Operations...'); try { // Test de recherche de classifications const classifications = await ConversationClassification.find({}) .sort({ processedAt: -1 }) .limit(5); logSuccess(`Found ${classifications.length} classifications in database`); // Test de recherche d'alertes const alerts = await AdminAlert.find({ status: { $in: ['ACTIVE', 'ACKNOWLEDGED'] } }).limit(5); logSuccess(`Found ${alerts.length} active alerts in database`); // Test des méthodes statiques const highPriorityClassifications = await ConversationClassification.findHighPriority(80, 5); logSuccess(`Found ${highPriorityClassifications.length} high priority classifications`); const unvalidatedClassifications = await ConversationClassification.findUnvalidated(5); logSuccess(`Found ${unvalidatedClassifications.length} unvalidated classifications`); // Test des statistiques const stats = await ConversationClassification.getClassificationStats(); logSuccess(`Classification stats: ${stats.length} categories`); if (stats.length > 0) { log(' Category breakdown:', 'cyan'); stats.forEach(stat => { log(` ${stat._id}: ${stat.count} classifications (avg priority: ${stat.avgPriority?.toFixed(1)})`, 'cyan'); }); } return true; } catch (error) { logError(`Database operations test failed: ${error.message}`); return false; } } async function testCacheOperations() { logInfo('Testing Cache Operations...'); try { const testConversationId = new mongoose.Types.ObjectId().toString(); // Test d'invalidation de cache const invalidated = await mlIntegrationService.invalidateCache(testConversationId); if (invalidated) { logSuccess('Cache invalidation successful'); } else { logWarning('Cache invalidation returned false (cache entry may not exist)'); } return true; } catch (error) { logError(`Cache operations test failed: ${error.message}`); return false; } } async function runIntegrationTests() { log('\n[DEPLOY] Starting ML Integration Tests', 'bright'); log('=====================================\n', 'bright'); let testResults = { database: false, mlHealth: false, classification: false, priorityQueue: false, metrics: false, databaseOps: false, cache: false }; try { // 1. Connexion à la base de données await connectDatabase(); testResults.database = true; // 2. Test de santé du service ML testResults.mlHealth = await testMLServiceHealth(); // 3. Test de classification ML if (testResults.mlHealth) { const classification = await testMLClassification(); testResults.classification = !!classification; // 4. Test de la queue de priorité testResults.priorityQueue = !!(await testPriorityQueue()).length >= 0; // 5. Test des métriques de performance testResults.metrics = !!(await testPerformanceMetrics()); // 6. Test des opérations de base de données testResults.databaseOps = await testDatabaseOperations(classification); // 7. Test des opérations de cache testResults.cache = await testCacheOperations(); } else { logWarning('Skipping ML-dependent tests due to service unavailability'); } } catch (error) { logError(`Integration tests failed: ${error.message}`); } finally { await mongoose.disconnect(); logInfo('Disconnected from MongoDB'); } // Résumé des résultats log('\n[ANALYTICS] Test Results Summary', 'bright'); log('========================\n', 'bright'); Object.entries(testResults).forEach(([test, passed]) => { if (passed) { logSuccess(`${test}: PASSED`); } else { logError(`${test}: FAILED`); } }); const passedTests = Object.values(testResults).filter(Boolean).length; const totalTests = Object.keys(testResults).length; log(`\n[METRICS] Overall: ${passedTests}/${totalTests} tests passed`, 'bright'); if (passedTests === totalTests) { logSuccess(' All integration tests passed! ML integration is working correctly.'); } else if (passedTests >= totalTests * 0.7) { logWarning(' Most tests passed, but some issues detected. Check the logs above.'); } else { logError('[FAILED] Multiple test failures detected. ML integration needs attention.'); } process.exit(passedTests === totalTests ? 0 : 1); } // Gestion des erreurs non capturées process.on('unhandledRejection', (reason, promise) => { logError(`Unhandled Rejection at: ${promise}, reason: ${reason}`); process.exit(1); }); process.on('uncaughtException', (error) => { logError(`Uncaught Exception: ${error.message}`); process.exit(1); }); // Exécution des tests if (require.main === module) { runIntegrationTests(); } module.exports = { runIntegrationTests, testMLServiceHealth, testMLClassification, testPriorityQueue, testPerformanceMetrics };