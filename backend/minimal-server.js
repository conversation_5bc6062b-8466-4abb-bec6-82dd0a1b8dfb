require('dotenv').config({ path: '../.env' }); const express = require('express'); const cors = require('cors'); const helmet = require('helmet'); const morgan = require('morgan'); const compression = require('compression'); console.log('Starting minimal server...'); const app = express(); // Basic middleware app.use(helmet()); app.use(cors()); app.use(compression()); app.use(morgan('combined')); app.use(express.json()); app.use(express.urlencoded({ extended: true })); // Health check endpoint app.get('/health', (req, res) => { res.json({ status: 'ok', timestamp: new Date().toISOString(), environment: process.env.NODE_ENV || 'development' }); }); // Basic API endpoint app.get('/api/status', (req, res) => { res.json({ message: 'ChatbotRNCP API is running', version: '1.0.0' }); }); const PORT = process.env.PORT || 5001; const server = app.listen(PORT, () => { console.log(`[COMPLETE] Minimal server running on port ${PORT}`); console.log(` Health check: http://localhost:${PORT}/health`); console.log(` API status: http://localhost:${PORT}/api/status`); }); // Graceful shutdown process.on('SIGTERM', () => { console.log('SIGTERM received, shutting down gracefully'); server.close(() => { console.log('Process terminated'); }); }); process.on('SIGINT', () => { console.log('SIGINT received, shutting down gracefully'); server.close(() => { console.log('Process terminated'); }); });