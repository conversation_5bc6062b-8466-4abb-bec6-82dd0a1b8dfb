require('dotenv').config({ path: '../.env' }); const http = require('http'); const socketIO = require('socket.io'); const app = require('./src/app'); const connectDB = require('./src/config/database'); const logger = require('./src/config/logger'); const User = require('./src/models/User'); const websocketService = require('./src/services/websocketService'); const PORT = process.env.PORT || 5000; // Create HTTP server const server = http.createServer(app); // Initialize WebSocket service for ML events websocketService.initialize(server); // Legacy Socket.IO for existing chat functionality const io = socketIO(server, { cors: { origin: process.env.NODE_ENV === 'production' ? process.env.FRONTEND_URL : 'http://localhost:3001', credentials: true, }, path: '/socket.io-legacy' // Différencier du nouveau service WebSocket }); // Legacy Socket.IO handlers (pour compatibilité) io.on('connection', (socket) => { logger.info(`Legacy socket connection: ${socket.id}`); socket.on('join-conversation', (conversationId) => { socket.join(conversationId); logger.info(`Socket ${socket.id} joined conversation: ${conversationId}`); }); socket.on('disconnect', () => { logger.info(`Legacy socket disconnected: ${socket.id}`); }); }); // Make io accessible to routes (legacy) app.set('io', io); app.set('websocketService', websocketService); // Initialize database and start server const startServer = async () => { try { console.log('Starting server initialization...'); await connectDB(); console.log('Database connected successfully'); // Create root user if not exists const rootEmail = process.env.ROOT_USER_EMAIL || '<EMAIL>'; const rootPassword = process.env.ROOT_USER_PASSWORD || 'AdminPassword123!'; const rootUser = await User.findOne({ email: rootEmail }); if (!rootUser) { await User.create({ email: rootEmail, password: rootPassword, role: 'admin', profile: { firstName: 'Admin', lastName: 'Free Mobile', }, }); logger.info('Root user created successfully'); } server.listen(PORT, () => { console.log(`Server running on port ${PORT}`); logger.info(`Server running on port ${PORT}`); }); } catch (error) { logger.error('Failed to start server:', error); process.exit(1); } }; startServer();