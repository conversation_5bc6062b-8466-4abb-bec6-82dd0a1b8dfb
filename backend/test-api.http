### Variables
@baseUrl = http://localhost:5000/api
@token = Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
@conversationId = 

### ===== AUTHENTICATION =====

### Register new user
POST {{baseUrl}}/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "Test123!",
  "firstName": "<PERSON>",
  "lastName": "Test"
}

### Login
# @name login
POST {{baseUrl}}/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "Password131"
}

### ===== CHAT =====

### Start conversation
# @name startChat
POST {{baseUrl}}/chat/conversations/start
Authorization: {{token}}
Content-Type: application/json

{
  "channel": "web"
}

### Send message - Check consumption
POST {{baseUrl}}/chat/messages/send
Authorization: {{token}}
Content-Type: application/json

{
  "conversationId": "{{conversationId}}",
  "message": "Je veux voir ma consommation"
}

### Send message - Change plan
POST {{baseUrl}}/chat/messages/send
Authorization: {{token}}
Content-Type: application/json

{
  "conversationId": "{{conversationId}}",
  "message": "Je veux passer au forfait 5G"
}

### Send message - Technical issue
POST {{baseUrl}}/chat/messages/send
Authorization: {{token}}
Content-Type: application/json

{
  "conversationId": "{{conversationId}}",
  "message": "J'ai plus internet depuis ce matin, c'est inadmissible!"
}

### Get conversation history
GET {{baseUrl}}/chat/conversations/{{conversationId}}
Authorization: {{token}}

### Handle message action
POST {{baseUrl}}/chat/messages/action
Authorization: {{token}}
Content-Type: application/json

{
  "action": "add_data_20gb",
  "payload": {}
}

### ===== SUBSCRIPTION =====

### Get current plan
GET {{baseUrl}}/subscription/plans/current
Authorization: {{token}}

### Get available plans
GET {{baseUrl}}/subscription/plans/available
Authorization: {{token}}

### Simulate plan change
POST {{baseUrl}}/subscription/plans/simulate
Authorization: {{token}}
Content-Type: application/json

{
  "planId": "5G_200GB"
}

### Change plan
POST {{baseUrl}}/subscription/plans/change
Authorization: {{token}}
Content-Type: application/json

{
  "planId": "5G_200GB"
}

### Get available options
GET {{baseUrl}}/subscription/options/available
Authorization: {{token}}

### Activate option
POST {{baseUrl}}/subscription/options/activate
Authorization: {{token}}
Content-Type: application/json

{
  "optionId": "INTERNATIONAL"
}

### Get consumption
GET {{baseUrl}}/subscription/consumption
Authorization: {{token}}

### Get billing history
GET {{baseUrl}}/subscription/billing/history?limit=6
Authorization: {{token}}

### ===== NOTIFICATIONS =====

### Get user notifications
GET {{baseUrl}}/notifications?unreadOnly=true
Authorization: {{token}}

### Trigger proactive notifications
POST {{baseUrl}}/notifications/trigger-proactive
Authorization: {{token}}

### Mark notification as read
PUT {{baseUrl}}/notifications/123456/read
Authorization: {{token}}

### Execute notification action
POST {{baseUrl}}/notifications/123456/action
Authorization: {{token}}
Content-Type: application/json

{
  "actionIndex": 0
}

### Get notification stats
GET {{baseUrl}}/notifications/stats
Authorization: {{token}}

### ===== HEALTH CHECK =====

### Health check
GET http://localhost:5000/health 