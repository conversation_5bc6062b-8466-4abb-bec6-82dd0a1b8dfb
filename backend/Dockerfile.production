# =============================================
# 🔧 BACKEND NODE.JS - PRODUCTION DOCKERFILE
# Free Mobile Chatbot Dashboard - Phase 4 Production
# Multi-stage build for optimized production image
# =============================================

# Stage 1: Base Node.js image
FROM node:18-alpine as base

# Set environment variables
ENV NODE_ENV=production \
    NPM_CONFIG_LOGLEVEL=warn \
    NPM_CONFIG_COLOR=false

# Install system dependencies
RUN apk add --no-cache \
    dumb-init \
    curl \
    && rm -rf /var/cache/apk/*

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S backend -u 1001

# Stage 2: Dependencies installation
FROM base as dependencies

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install production dependencies only
RUN npm ci --only=production --silent && \
    npm cache clean --force

# Stage 3: Build stage (if needed for TypeScript compilation)
FROM base as builder

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install all dependencies (including dev dependencies)
RUN npm ci --silent

# Copy source code
COPY . .

# Build application (if using TypeScript)
RUN npm run build 2>/dev/null || echo "No build script found"

# Stage 4: Production runtime
FROM base as production

# Set environment variables
ENV NODE_ENV=production \
    PORT=5000 \
    LOG_LEVEL=info \
    MAX_CONNECTIONS=1000 \
    CLUSTER_WORKERS=0

# Set working directory
WORKDIR /app

# Copy production dependencies
COPY --from=dependencies --chown=backend:nodejs /app/node_modules ./node_modules

# Copy application code
COPY --from=builder --chown=backend:nodejs /app/src ./src
COPY --from=builder --chown=backend:nodejs /app/package*.json ./
COPY --from=builder --chown=backend:nodejs /app/server.js ./

# Copy built files if they exist
COPY --from=builder --chown=backend:nodejs /app/dist ./dist 2>/dev/null || true

# Create necessary directories
RUN mkdir -p /app/logs /app/uploads /app/tmp \
    && chown -R backend:nodejs /app \
    && chmod -R 755 /app

# Switch to non-root user
USER backend

# Expose port
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:5000/api/health || exit 1

# Production startup with dumb-init for proper signal handling
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "server.js"]
