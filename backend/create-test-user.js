require('dotenv').config({ path: '../.env' }); const mongoose = require('mongoose'); const User = require('./src/models/User'); const logger = require('./src/config/logger'); const createTestUser = async () => { try { // Connect to MongoDB const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/freemobile-chatbot'; await mongoose.connect(mongoURI); logger.info('Connected to MongoDB'); // Check if test user already exists const existingUser = await User.findOne({ email: '<EMAIL>' }); if (existingUser) { logger.info('Test user already exists'); console.log('[COMPLETE] <NAME_EMAIL> already exists'); process.exit(0); } // Create test user const testUser = new User({ email: '<EMAIL>', password: 'AdminPassword123!', role: 'admin', profile: { firstName: 'Admin', lastName: 'User', phoneNumber: '+33123456789', customerId: 'ADMIN-001' }, preferences: { language: 'fr', notifications: true } }); await testUser.save(); logger.info('Test user created successfully'); console.log('[COMPLETE] Test user created: <EMAIL> / AdminPassword123!'); // Create additional test users const users = [ { email: '<EMAIL>', password: 'UserPassword123!', role: 'user', profile: { firstName: 'Test', lastName: 'User', phoneNumber: '+33987654321', customerId: 'USER-001' } }, { email: '<EMAIL>', password: 'AgentPassword123!', role: 'agent', profile: { firstName: 'Agent', lastName: 'Support', phoneNumber: '+***********', customerId: 'AGENT-001' } } ]; for (const userData of users) { const existingUser = await User.findOne({ email: userData.email }); if (!existingUser) { const user = new User(userData); await user.save(); console.log(`[COMPLETE] Created user: ${userData.email} / ${userData.password}`); } else { console.log(`ℹ User already exists: ${userData.email}`); } } console.log('\n Database setup complete!'); console.log('\n Available test accounts:'); console.log('[USER] Admin: <EMAIL> / AdminPassword123!'); console.log('[USER] User: <EMAIL> / UserPassword123!'); console.log('[USER] Agent: <EMAIL> / AgentPassword123!'); } catch (error) { logger.error('Error creating test user:', error); console.error('[FAILED] Error creating test user:', error.message); process.exit(1); } finally { await mongoose.connection.close(); logger.info('MongoDB connection closed'); } }; createTestUser();