/** * ============================================= * PREDICTIVE ANALYTICS TESTS * Comprehensive test suite for ML predictions * Unit and integration tests for predictive features * ============================================= */ const request = require('supertest'); const mongoose = require('mongoose'); const app = require('../src/app'); const { ChurnPrediction, EscalationPrediction } = require('../src/database/mongoSchemas'); const { generateToken } = require('../src/utils/helpers'); describe('Predictive Analytics API', () => { let authToken; let adminToken; let testAgent; let testAdmin; beforeAll(async () => { // Connect to test database await mongoose.connect(process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/freemobile_test'); // Create test users testAgent = { id: new mongoose.Types.ObjectId(), email: '<EMAIL>', role: 'agent', firstName: 'Test', lastName: 'Agent' }; testAdmin = { id: new mongoose.Types.ObjectId(), email: '<EMAIL>', role: 'admin', firstName: 'Test', lastName: 'Admin' }; authToken = generateToken(testAgent); adminToken = generateToken(testAdmin); }); beforeEach(async () => { // Clean up test data await ChurnPrediction.deleteMany({}); await EscalationPrediction.deleteMany({}); }); afterAll(async () => { await mongoose.connection.close(); }); describe('GET /api/predictive/churn', () => { beforeEach(async () => { // Create test churn predictions await ChurnPrediction.create([ { customer_id: new mongoose.Types.ObjectId(), churn_probability: 0.85, risk_level: 'high', customer_tier: 'gold', customer_value: 'high', model_version: 'v1.2.0', confidence: 0.92, risk_factors: ['billing_issues', 'support_contacts'], prediction_date: new Date() }, { customer_id: new mongoose.Types.ObjectId(), churn_probability: 0.35, risk_level: 'medium', customer_tier: 'silver', customer_value: 'medium', model_version: 'v1.2.0', confidence: 0.78, risk_factors: ['usage_decline'], prediction_date: new Date() }, { customer_id: new mongoose.Types.ObjectId(), churn_probability: 0.95, risk_level: 'critical', customer_tier: 'platinum', customer_value: 'high', model_version: 'v1.2.0', confidence: 0.96, risk_factors: ['billing_issues', 'support_contacts', 'competitor_activity'], prediction_date: new Date() } ]); }); it('should fetch churn predictions for admin users', async () => { const response = await request(app) .get('/api/predictive/churn') .set('Authorization', `Bearer ${adminToken}`) .expect(200); expect(response.body.success).toBe(true); expect(response.body.data.predictions).toHaveLength(3); expect(response.body.data.trends).toBeDefined(); expect(response.body.data.prevention_actions).toBeDefined(); }); it('should filter by risk level', async () => { const response = await request(app) .get('/api/predictive/churn?risk_level=critical') .set('Authorization', `Bearer ${adminToken}`) .expect(200); expect(response.body.success).toBe(true); expect(response.body.data.predictions).toHaveLength(1); expect(response.body.data.predictions[0].risk_level).toBe('critical'); }); it('should deny access to regular agents', async () => { const response = await request(app) .get('/api/predictive/churn') .set('Authorization', `Bearer ${authToken}`) .expect(403); expect(response.body.success).toBe(false); expect(response.body.error).toContain('Insufficient permissions'); }); it('should validate risk level filter', async () => { const response = await request(app) .get('/api/predictive/churn?risk_level=invalid') .set('Authorization', `Bearer ${adminToken}`) .expect(400); expect(response.body.success).toBe(false); expect(response.body.error).toBe('Validation errors'); }); it('should limit results based on limit parameter', async () => { const response = await request(app) .get('/api/predictive/churn?limit=2') .set('Authorization', `Bearer ${adminToken}`) .expect(200); expect(response.body.success).toBe(true); expect(response.body.data.predictions).toHaveLength(2); }); }); describe('GET /api/predictive/demand', () => { it('should fetch demand forecast for supervisors', async () => { const supervisorToken = generateToken({ ...testAgent, role: 'supervisor' }); const response = await request(app) .get('/api/predictive/demand') .set('Authorization', `Bearer ${supervisorToken}`) .expect(200); expect(response.body.success).toBe(true); expect(response.body.data.forecast).toBeDefined(); expect(response.body.data.staffing_recommendations).toBeDefined(); expect(response.body.data.capacity_metrics).toBeDefined(); }); it('should deny access to regular agents', async () => { const response = await request(app) .get('/api/predictive/demand') .set('Authorization', `Bearer ${authToken}`) .expect(403); expect(response.body.success).toBe(false); expect(response.body.error).toContain('Insufficient permissions'); }); it('should validate hours_ahead parameter', async () => { const response = await request(app) .get('/api/predictive/demand?hours_ahead=200') .set('Authorization', `Bearer ${adminToken}`) .expect(400); expect(response.body.success).toBe(false); expect(response.body.error).toBe('Validation errors'); }); }); describe('GET /api/predictive/escalation', () => { beforeEach(async () => { // Create test escalation predictions await EscalationPrediction.create([ { ticket_id: new mongoose.Types.ObjectId(), agent_id: testAgent.id, escalation_probability: 0.75, risk_level: 'high', predicted_escalation_time: new Date(Date.now() + 3600000), // 1 hour from now model_version: 'v1.1.0', confidence: 0.88, risk_factors: ['customer_frustration', 'complex_issue'], prediction_date: new Date() }, { ticket_id: new mongoose.Types.ObjectId(), agent_id: new mongoose.Types.ObjectId(), escalation_probability: 0.25, risk_level: 'low', predicted_escalation_time: null, model_version: 'v1.1.0', confidence: 0.82, risk_factors: [], prediction_date: new Date() } ]); }); it('should fetch escalation predictions for agents (own tickets only)', async () => { const response = await request(app) .get('/api/predictive/escalation') .set('Authorization', `Bearer ${authToken}`) .expect(200); expect(response.body.success).toBe(true); expect(response.body.data.predictions).toHaveLength(1); expect(response.body.data.predictions[0].agent_id).toBe(testAgent.id.toString()); }); it('should fetch all escalation predictions for admins', async () => { const response = await request(app) .get('/api/predictive/escalation') .set('Authorization', `Bearer ${adminToken}`) .expect(200); expect(response.body.success).toBe(true); expect(response.body.data.predictions).toHaveLength(2); }); it('should filter by specific agent for admins', async () => { const response = await request(app) .get(`/api/predictive/escalation?agent_id=${testAgent.id}`) .set('Authorization', `Bearer ${adminToken}`) .expect(200); expect(response.body.success).toBe(true); expect(response.body.data.predictions).toHaveLength(1); expect(response.body.data.predictions[0].agent_id).toBe(testAgent.id.toString()); }); it('should provide prevention strategies for high-risk tickets', async () => { const response = await request(app) .get('/api/predictive/escalation?risk_level=high') .set('Authorization', `Bearer ${adminToken}`) .expect(200); expect(response.body.success).toBe(true); expect(response.body.data.prevention_strategies).toBeDefined(); expect(response.body.data.intervention_recommendations).toBeDefined(); }); }); describe('GET /api/predictive/anomalies', () => { it('should fetch anomalies for admin users', async () => { const response = await request(app) .get('/api/predictive/anomalies') .set('Authorization', `Bearer ${adminToken}`) .expect(200); expect(response.body.success).toBe(true); expect(response.body.data.anomalies).toBeDefined(); expect(response.body.data.system_health_score).toBeDefined(); expect(response.body.data.resolution_recommendations).toBeDefined(); }); it('should deny access to regular agents', async () => { const response = await request(app) .get('/api/predictive/anomalies') .set('Authorization', `Bearer ${authToken}`) .expect(403); expect(response.body.success).toBe(false); expect(response.body.error).toContain('Insufficient permissions'); }); it('should filter by severity', async () => { const response = await request(app) .get('/api/predictive/anomalies?severity_filter=critical') .set('Authorization', `Bearer ${adminToken}`) .expect(200); expect(response.body.success).toBe(true); expect(response.body.data.anomalies).toBeDefined(); }); }); describe('POST /api/predictive/anomalies/:id/acknowledge', () => { let testAnomalyId; beforeEach(() => { testAnomalyId = 'anomaly_' + Date.now(); }); it('should acknowledge anomaly successfully', async () => { const response = await request(app) .post(`/api/predictive/anomalies/${testAnomalyId}/acknowledge`) .set('Authorization', `Bearer ${adminToken}`) .send({ notes: 'Investigated and resolved. False positive due to maintenance window.' }) .expect(200); expect(response.body.success).toBe(true); expect(response.body.data.anomaly_id).toBe(testAnomalyId); expect(response.body.data.acknowledged_by).toBe(testAdmin.id.toString()); expect(response.body.data.notes).toBe('Investigated and resolved. False positive due to maintenance window.'); }); it('should deny access to regular agents', async () => { const response = await request(app) .post(`/api/predictive/anomalies/${testAnomalyId}/acknowledge`) .set('Authorization', `Bearer ${authToken}`) .send({ notes: 'Test note' }) .expect(403); expect(response.body.success).toBe(false); expect(response.body.error).toContain('Insufficient permissions'); }); it('should validate notes length', async () => { const longNotes = 'x'.repeat(1001); // Exceeds 1000 character limit const response = await request(app) .post(`/api/predictive/anomalies/${testAnomalyId}/acknowledge`) .set('Authorization', `Bearer ${adminToken}`) .send({ notes: longNotes }) .expect(400); expect(response.body.success).toBe(false); expect(response.body.error).toBe('Validation errors'); }); }); describe('GET /api/predictive/workload', () => { it('should fetch workload analytics for supervisors', async () => { const supervisorToken = generateToken({ ...testAgent, role: 'supervisor' }); const response = await request(app) .get('/api/predictive/workload') .set('Authorization', `Bearer ${supervisorToken}`) .expect(200); expect(response.body.success).toBe(true); expect(response.body.data.agent_workloads).toBeDefined(); expect(response.body.data.team_efficiency).toBeDefined(); expect(response.body.data.optimal_assignments).toBeDefined(); expect(response.body.data.balancing_recommendations).toBeDefined(); }); it('should filter by team_id', async () => { const teamId = 'team_support_1'; const response = await request(app) .get(`/api/predictive/workload?team_id=${teamId}`) .set('Authorization', `Bearer ${adminToken}`) .expect(200); expect(response.body.success).toBe(true); expect(response.body.data).toBeDefined(); }); }); describe('GET /api/predictive/models/status', () => { it('should fetch model status for admin users', async () => { const response = await request(app) .get('/api/predictive/models/status') .set('Authorization', `Bearer ${adminToken}`) .expect(200); expect(response.body.success).toBe(true); expect(response.body.data.model_metrics).toBeDefined(); expect(response.body.data.model_health).toBeDefined(); expect(response.body.data.business_impact).toBeDefined(); expect(response.body.data.confidence_metrics).toBeDefined(); }); it('should deny access to non-admin users', async () => { const supervisorToken = generateToken({ ...testAgent, role: 'supervisor' }); const response = await request(app) .get('/api/predictive/models/status') .set('Authorization', `Bearer ${supervisorToken}`) .expect(403); expect(response.body.success).toBe(false); expect(response.body.error).toBe('Admin access required'); }); }); describe('POST /api/predictive/models/retrain', () => { it('should trigger model retraining for admin users', async () => { const response = await request(app) .post('/api/predictive/models/retrain') .set('Authorization', `Bearer ${adminToken}`) .send({ model_type: 'churn', force: false }) .expect(200); expect(response.body.success).toBe(true); expect(response.body.data.training_id).toBeDefined(); expect(response.body.data.model_type).toBe('churn'); expect(response.body.data.status).toBeDefined(); expect(response.body.data.initiated_by).toBe(testAdmin.id.toString()); }); it('should validate model_type parameter', async () => { const response = await request(app) .post('/api/predictive/models/retrain') .set('Authorization', `Bearer ${adminToken}`) .send({ model_type: 'invalid_model', force: false }) .expect(400); expect(response.body.success).toBe(false); expect(response.body.error).toBe('Validation errors'); }); it('should deny access to non-admin users', async () => { const response = await request(app) .post('/api/predictive/models/retrain') .set('Authorization', `Bearer ${authToken}`) .send({ model_type: 'churn', force: false }) .expect(403); expect(response.body.success).toBe(false); expect(response.body.error).toBe('Admin access required'); }); }); describe('GET /api/predictive/insights/dashboard', () => { it('should fetch dashboard insights for authorized users', async () => { const analystToken = generateToken({ ...testAgent, role: 'analyst' }); const response = await request(app) .get('/api/predictive/insights/dashboard') .set('Authorization', `Bearer ${analystToken}`) .expect(200); expect(response.body.success).toBe(true); expect(response.body.data.summary).toBeDefined(); expect(response.body.data.business_impact).toBeDefined(); expect(response.body.data.confidence_metrics).toBeDefined(); expect(response.body.data.recent_predictions).toBeDefined(); }); it('should deny access to regular agents', async () => { const response = await request(app) .get('/api/predictive/insights/dashboard') .set('Authorization', `Bearer ${authToken}`) .expect(403); expect(response.body.success).toBe(false); expect(response.body.error).toContain('Insufficient permissions'); }); }); describe('Performance and Load Tests', () => { it('should handle concurrent prediction requests', async () => { const promises = []; const requestCount = 10; // Create concurrent requests for (let i = 0; i < requestCount; i++) { promises.push( request(app) .get('/api/predictive/churn?limit=5') .set('Authorization', `Bearer ${adminToken}`) ); } const responses = await Promise.all(promises); // All should succeed responses.forEach(response => { expect(response.status).toBe(200); expect(response.body.success).toBe(true); }); }); it('should handle large datasets efficiently', async () => { // Create a large number of predictions const predictions = []; for (let i = 0; i < 100; i++) { predictions.push({ customer_id: new mongoose.Types.ObjectId(), churn_probability: Math.random(), risk_level: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)], customer_tier: ['standard', 'silver', 'gold'][Math.floor(Math.random() * 3)], customer_value: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)], model_version: 'v1.2.0', confidence: Math.random(), risk_factors: ['factor1', 'factor2'], prediction_date: new Date() }); } await ChurnPrediction.insertMany(predictions); const startTime = Date.now(); const response = await request(app) .get('/api/predictive/churn?limit=50') .set('Authorization', `Bearer ${adminToken}`) .expect(200); const responseTime = Date.now() - startTime; expect(response.body.success).toBe(true); expect(response.body.data.predictions).toHaveLength(50); expect(responseTime).toBeLessThan(2000); // Should respond within 2 seconds }); }); });