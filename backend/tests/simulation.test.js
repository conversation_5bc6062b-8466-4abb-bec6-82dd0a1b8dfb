/** * ============================================= * SIMULATION SERVICE TESTS * Comprehensive test suite for simulation features * Unit and integration tests for agent training * ============================================= */ const request = require('supertest'); const mongoose = require('mongoose'); const app = require('../src/app'); const { SimulationScenario, SimulationSession, AgentProgress } = require('../src/database/mongoSchemas'); const { generateToken } = require('../src/utils/helpers'); describe('Simulation API', () => { let authToken; let testAgent; let testScenario; let testSession; beforeAll(async () => { // Connect to test database await mongoose.connect(process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/freemobile_test'); // Create test agent testAgent = { id: new mongoose.Types.ObjectId(), email: '<EMAIL>', role: 'agent', firstName: 'Test', lastName: 'Agent' }; authToken = generateToken(testAgent); }); beforeEach(async () => { // Clean up test data await SimulationScenario.deleteMany({}); await SimulationSession.deleteMany({}); await AgentProgress.deleteMany({}); // Create test scenario testScenario = await SimulationScenario.create({ title: 'Test Billing Issue Scenario', description: 'Customer has billing dispute', difficulty: 'intermediate', category: 'billing', tags: ['billing', 'dispute'], customer_profile: { tier: 'gold', communication_style: 'direct', issue_complexity: 'moderate', emotional_state: 'frustrated' }, context: { situation: 'Customer received incorrect bill', background: 'Long-time customer with premium plan', desired_outcome: 'Bill correction and explanation' }, learning_objectives: ['empathy', 'accuracy'], success_criteria: { min_empathy_score: 75, min_efficiency_score: 70, min_accuracy_score: 80 } }); }); afterAll(async () => { await mongoose.connection.close(); }); describe('GET /api/simulation/scenarios', () => { it('should fetch scenarios successfully', async () => { const response = await request(app) .get('/api/simulation/scenarios') .set('Authorization', `Bearer ${authToken}`) .expect(200); expect(response.body.success).toBe(true); expect(response.body.data).toHaveLength(1); expect(response.body.data[0].title).toBe('Test Billing Issue Scenario'); }); it('should filter scenarios by difficulty', async () => { // Create additional scenario await SimulationScenario.create({ title: 'Expert Technical Issue', description: 'Complex technical problem', difficulty: 'expert', category: 'technical', tags: ['technical'], customer_profile: { tier: 'standard' }, context: { situation: 'Network issue' }, learning_objectives: ['problem_solving'] }); const response = await request(app) .get('/api/simulation/scenarios?difficulty=intermediate') .set('Authorization', `Bearer ${authToken}`) .expect(200); expect(response.body.data).toHaveLength(1); expect(response.body.data[0].difficulty).toBe('intermediate'); }); it('should require authentication', async () => { await request(app) .get('/api/simulation/scenarios') .expect(401); }); }); describe('POST /api/simulation/sessions', () => { it('should create simulation session successfully', async () => { const sessionData = { scenarioId: testScenario._id.toString(), settings: { difficulty_adjustment: true, ai_coaching_enabled: true, real_time_feedback: true } }; const response = await request(app) .post('/api/simulation/sessions') .set('Authorization', `Bearer ${authToken}`) .send(sessionData) .expect(201); expect(response.body.success).toBe(true); expect(response.body.data.scenario_id).toBe(testScenario._id.toString()); expect(response.body.data.agent_id).toBe(testAgent.id.toString()); expect(response.body.data.status).toBe('active'); }); it('should prevent multiple active sessions', async () => { // Create first session await SimulationSession.create({ _id: 'test-session-1', scenario_id: testScenario._id, agent_id: testAgent.id, status: 'active', scenario: testScenario.toObject() }); const sessionData = { scenarioId: testScenario._id.toString(), settings: {} }; const response = await request(app) .post('/api/simulation/sessions') .set('Authorization', `Bearer ${authToken}`) .send(sessionData) .expect(409); expect(response.body.success).toBe(false); expect(response.body.error).toContain('active simulation session'); }); it('should validate scenario exists', async () => { const sessionData = { scenarioId: new mongoose.Types.ObjectId().toString(), settings: {} }; const response = await request(app) .post('/api/simulation/sessions') .set('Authorization', `Bearer ${authToken}`) .send(sessionData) .expect(404); expect(response.body.success).toBe(false); expect(response.body.error).toBe('Scenario not found'); }); }); describe('POST /api/simulation/sessions/:id/messages', () => { beforeEach(async () => { testSession = await SimulationSession.create({ _id: 'test-session-messages', scenario_id: testScenario._id, agent_id: testAgent.id, status: 'active', scenario: testScenario.toObject(), messages: [] }); }); it('should add message to session successfully', async () => { const messageData = { message: 'Hello, I understand you have a billing concern. How can I help you today?', timestamp: new Date().toISOString() }; const response = await request(app) .post(`/api/simulation/sessions/${testSession._id}/messages`) .set('Authorization', `Bearer ${authToken}`) .send(messageData) .expect(200); expect(response.body.success).toBe(true); expect(response.body.data.message).toBeDefined(); expect(response.body.data.message.content).toBe(messageData.message); expect(response.body.data.message.sender).toBe('agent'); }); it('should validate message content', async () => { const messageData = { message: '', timestamp: new Date().toISOString() }; const response = await request(app) .post(`/api/simulation/sessions/${testSession._id}/messages`) .set('Authorization', `Bearer ${authToken}`) .send(messageData) .expect(400); expect(response.body.success).toBe(false); expect(response.body.error).toBe('Message content is required'); }); it('should reject messages for inactive sessions', async () => { await SimulationSession.findByIdAndUpdate(testSession._id, { status: 'completed' }); const messageData = { message: 'Test message', timestamp: new Date().toISOString() }; const response = await request(app) .post(`/api/simulation/sessions/${testSession._id}/messages`) .set('Authorization', `Bearer ${authToken}`) .send(messageData) .expect(400); expect(response.body.success).toBe(false); expect(response.body.error).toBe('Session is not active'); }); }); describe('PUT /api/simulation/sessions/:id/end', () => { beforeEach(async () => { testSession = await SimulationSession.create({ _id: 'test-session-end', scenario_id: testScenario._id, agent_id: testAgent.id, status: 'active', scenario: testScenario.toObject(), messages: [ { id: 'msg1', sender: 'customer', content: 'I have a billing issue', timestamp: new Date(), metadata: { sentiment: -0.3, satisfaction: 3 } }, { id: 'msg2', sender: 'agent', content: 'I understand your concern. Let me help you.', timestamp: new Date(), metadata: { empathy_rating: 8, response_time: 30 } } ] }); }); it('should end session and calculate metrics', async () => { const response = await request(app) .put(`/api/simulation/sessions/${testSession._id}/end`) .set('Authorization', `Bearer ${authToken}`) .send({ reason: 'completed' }) .expect(200); expect(response.body.success).toBe(true); expect(response.body.data.final_metrics).toBeDefined(); expect(response.body.data.comprehensive_feedback).toBeDefined(); expect(response.body.data.agent_progress).toBeDefined(); // Verify session is marked as completed const updatedSession = await SimulationSession.findById(testSession._id); expect(updatedSession.status).toBe('completed'); expect(updatedSession.completion_reason).toBe('completed'); }); it('should update agent progress', async () => { await request(app) .put(`/api/simulation/sessions/${testSession._id}/end`) .set('Authorization', `Bearer ${authToken}`) .send({ reason: 'completed' }) .expect(200); const agentProgress = await AgentProgress.findOne({ agent_id: testAgent.id }); expect(agentProgress).toBeDefined(); expect(agentProgress.total_sessions).toBe(1); expect(agentProgress.completed_sessions).toBe(1); }); }); describe('GET /api/simulation/progress', () => { beforeEach(async () => { await AgentProgress.create({ agent_id: testAgent.id, total_sessions: 5, completed_sessions: 4, average_score: 78.5, skill_levels: { empathy: 75, efficiency: 82, accuracy: 79, communication: 80, problem_solving: 73 }, badges_earned: [ { id: 'first_session', name: 'First Steps', description: 'Completed first simulation', rarity: 'common', earned_at: new Date() } ], current_streak: 3, best_streak: 5 }); }); it('should fetch agent progress successfully', async () => { const response = await request(app) .get('/api/simulation/progress') .set('Authorization', `Bearer ${authToken}`) .expect(200); expect(response.body.success).toBe(true); expect(response.body.data.total_sessions).toBe(5); expect(response.body.data.completed_sessions).toBe(4); expect(response.body.data.average_score).toBe(78.5); expect(response.body.data.skill_levels.empathy).toBe(75); expect(response.body.data.badges_earned).toHaveLength(1); }); }); describe('GET /api/simulation/history', () => { beforeEach(async () => { // Create completed sessions await SimulationSession.create([ { _id: 'history-session-1', scenario_id: testScenario._id, agent_id: testAgent.id, status: 'completed', scenario: testScenario.toObject(), final_metrics: { overall_score: 85, empathy_score: 80, efficiency_score: 90 }, created_at: new Date(Date.now() - 86400000) // 1 day ago }, { _id: 'history-session-2', scenario_id: testScenario._id, agent_id: testAgent.id, status: 'completed', scenario: testScenario.toObject(), final_metrics: { overall_score: 75, empathy_score: 70, efficiency_score: 80 }, created_at: new Date(Date.now() - 172800000) // 2 days ago } ]); }); it('should fetch session history successfully', async () => { const response = await request(app) .get('/api/simulation/history') .set('Authorization', `Bearer ${authToken}`) .expect(200); expect(response.body.success).toBe(true); expect(response.body.data).toHaveLength(2); expect(response.body.data[0]._id).toBe('history-session-1'); // Most recent first expect(response.body.data[0].final_metrics.overall_score).toBe(85); }); it('should support pagination', async () => { const response = await request(app) .get('/api/simulation/history?limit=1&offset=0') .set('Authorization', `Bearer ${authToken}`) .expect(200); expect(response.body.success).toBe(true); expect(response.body.data).toHaveLength(1); expect(response.body.pagination.limit).toBe(1); expect(response.body.pagination.offset).toBe(0); }); }); describe('WebSocket Integration', () => { // Note: WebSocket tests would require additional setup with socket.io-client // This is a placeholder for WebSocket integration tests it('should handle WebSocket connections for real-time updates', () => { // TODO: Implement WebSocket tests // - Test session joining // - Test real-time message processing // - Test AI coaching delivery // - Test session metrics updates expect(true).toBe(true); // Placeholder }); }); describe('Performance Tests', () => { it('should handle concurrent session creation', async () => { const promises = []; // Create multiple scenarios for concurrent testing const scenarios = await SimulationScenario.create([ { ...testScenario.toObject(), _id: new mongoose.Types.ObjectId(), title: 'Scenario 1' }, { ...testScenario.toObject(), _id: new mongoose.Types.ObjectId(), title: 'Scenario 2' }, { ...testScenario.toObject(), _id: new mongoose.Types.ObjectId(), title: 'Scenario 3' } ]); // Create different agents const agents = [ { ...testAgent, id: new mongoose.Types.ObjectId() }, { ...testAgent, id: new mongoose.Types.ObjectId() }, { ...testAgent, id: new mongoose.Types.ObjectId() } ]; // Attempt concurrent session creation for (let i = 0; i < 3; i++) { const token = generateToken(agents[i]); promises.push( request(app) .post('/api/simulation/sessions') .set('Authorization', `Bearer ${token}`) .send({ scenarioId: scenarios[i]._id.toString(), settings: {} }) ); } const responses = await Promise.all(promises); // All should succeed responses.forEach(response => { expect(response.status).toBe(201); expect(response.body.success).toBe(true); }); }); it('should handle large message volumes', async () => { testSession = await SimulationSession.create({ _id: 'performance-test-session', scenario_id: testScenario._id, agent_id: testAgent.id, status: 'active', scenario: testScenario.toObject(), messages: [] }); const promises = []; const messageCount = 50; // Send multiple messages rapidly for (let i = 0; i < messageCount; i++) { promises.push( request(app) .post(`/api/simulation/sessions/${testSession._id}/messages`) .set('Authorization', `Bearer ${authToken}`) .send({ message: `Test message ${i + 1}`, timestamp: new Date().toISOString() }) ); } const responses = await Promise.all(promises); // All should succeed responses.forEach(response => { expect(response.status).toBe(200); expect(response.body.success).toBe(true); }); // Verify all messages were stored const updatedSession = await SimulationSession.findById(testSession._id); expect(updatedSession.messages).toHaveLength(messageCount); }); }); });