const request = require('supertest'); const mongoose = require('mongoose'); const app = require('../../src/app'); const User = require('../../src/models/User'); const { connectDB } = require('../../src/config/database'); /** * Integration tests for API endpoints */ describe('API Integration Tests', () => { let authToken; let testUser; let server; beforeAll(async () => { // Connect to test database process.env.NODE_ENV = 'test'; process.env.MONGODB_URI = process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/chatbot_test'; await connectDB(); server = app.listen(0); // Use random port for testing }); afterAll(async () => { // Clean up test database await mongoose.connection.dropDatabase(); await mongoose.connection.close(); if (server) { server.close(); } }); beforeEach(async () => { // Clear test data before each test await User.deleteMany({}); // Create test user testUser = await User.create({ email: '<EMAIL>', password: 'TestPassword123!', profile: { firstName: 'Test', lastName: 'User' }, role: 'agent', isActive: true }); }); describe('Authentication Endpoints', () => { describe('POST /api/auth/login', () => { it('should login with valid credentials', async () => { const response = await request(app) .post('/api/auth/login') .send({ email: '<EMAIL>', password: 'TestPassword123!' }) .expect(200); expect(response.body.success).toBe(true); expect(response.body.token).toBeDefined(); expect(response.body.user.email).toBe('<EMAIL>'); authToken = response.body.token; }); it('should reject invalid credentials', async () => { const response = await request(app) .post('/api/auth/login') .send({ email: '<EMAIL>', password: 'WrongPassword' }) .expect(401); expect(response.body.success).toBe(false); expect(response.body.error).toContain('Invalid credentials'); }); it('should validate input format', async () => { const response = await request(app) .post('/api/auth/login') .send({ email: 'invalid-email', password: '123' }) .expect(400); expect(response.body.success).toBe(false); expect(response.body.details).toBeDefined(); }); }); describe('POST /api/auth/register', () => { it('should register new user with valid data', async () => { const userData = { email: '<EMAIL>', password: 'NewPassword123!', profile: { firstName: 'New', lastName: 'User' } }; const response = await request(app) .post('/api/auth/register') .send(userData) .expect(201); expect(response.body.success).toBe(true); expect(response.body.user.email).toBe(userData.email); expect(response.body.token).toBeDefined(); }); it('should reject duplicate email', async () => { const response = await request(app) .post('/api/auth/register') .send({ email: '<EMAIL>', // Already exists password: 'NewPassword123!', profile: { firstName: 'Duplicate', lastName: 'User' } }) .expect(400); expect(response.body.success).toBe(false); expect(response.body.error).toContain('already exists'); }); }); describe('GET /api/auth/me', () => { beforeEach(async () => { // Login to get auth token const loginResponse = await request(app) .post('/api/auth/login') .send({ email: '<EMAIL>', password: 'TestPassword123!' }); authToken = loginResponse.body.token; }); it('should return user profile with valid token', async () => { const response = await request(app) .get('/api/auth/me') .set('Authorization', `Bearer ${authToken}`) .expect(200); expect(response.body.success).toBe(true); expect(response.body.user.email).toBe('<EMAIL>'); expect(response.body.user.password).toBeUndefined(); }); it('should reject request without token', async () => { const response = await request(app) .get('/api/auth/me') .expect(401); expect(response.body.success).toBe(false); expect(response.body.error).toContain('token'); }); it('should reject request with invalid token', async () => { const response = await request(app) .get('/api/auth/me') .set('Authorization', 'Bearer invalid-token') .expect(401); expect(response.body.success).toBe(false); }); }); }); describe('Chat Endpoints', () => { beforeEach(async () => { // Login to get auth token const loginResponse = await request(app) .post('/api/auth/login') .send({ email: '<EMAIL>', password: 'TestPassword123!' }); authToken = loginResponse.body.token; }); describe('POST /api/chat/conversations', () => { it('should create new conversation', async () => { const conversationData = { title: 'Test Conversation', type: 'support', priority: 'medium' }; const response = await request(app) .post('/api/chat/conversations') .set('Authorization', `Bearer ${authToken}`) .send(conversationData) .expect(201); expect(response.body.success).toBe(true); expect(response.body.conversation.title).toBe(conversationData.title); expect(response.body.conversation.participants).toContain(testUser._id.toString()); }); }); describe('GET /api/chat/conversations', () => { it('should return user conversations', async () => { const response = await request(app) .get('/api/chat/conversations') .set('Authorization', `Bearer ${authToken}`) .expect(200); expect(response.body.success).toBe(true); expect(Array.isArray(response.body.conversations)).toBe(true); }); it('should support pagination', async () => { const response = await request(app) .get('/api/chat/conversations?page=1&limit=10') .set('Authorization', `Bearer ${authToken}`) .expect(200); expect(response.body.success).toBe(true); expect(response.body.pagination).toBeDefined(); expect(response.body.pagination.page).toBe(1); expect(response.body.pagination.limit).toBe(10); }); }); }); describe('Analytics Endpoints', () => { beforeEach(async () => { // Login as admin for analytics access testUser.role = 'admin'; await testUser.save(); const loginResponse = await request(app) .post('/api/auth/login') .send({ email: '<EMAIL>', password: 'TestPassword123!' }); authToken = loginResponse.body.token; }); describe('GET /api/analytics/dashboard', () => { it('should return dashboard analytics for admin', async () => { const response = await request(app) .get('/api/analytics/dashboard') .set('Authorization', `Bearer ${authToken}`) .expect(200); expect(response.body.success).toBe(true); expect(response.body.analytics).toBeDefined(); expect(response.body.analytics.overview).toBeDefined(); }); it('should reject non-admin users', async () => { // Change user role to agent testUser.role = 'agent'; await testUser.save(); const response = await request(app) .get('/api/analytics/dashboard') .set('Authorization', `Bearer ${authToken}`) .expect(403); expect(response.body.success).toBe(false); expect(response.body.error).toContain('access'); }); }); }); describe('Health Check Endpoints', () => { describe('GET /health', () => { it('should return health status', async () => { const response = await request(app) .get('/health') .expect(200); expect(response.body.status).toBeDefined(); expect(response.body.timestamp).toBeDefined(); expect(response.body.checks).toBeDefined(); }); it('should include database status', async () => { const response = await request(app) .get('/health') .expect(200); expect(response.body.checks.database).toBeDefined(); expect(response.body.checks.database.status).toBe('healthy'); }); }); }); describe('Rate Limiting', () => { it('should enforce rate limits', async () => { const endpoint = '/api/auth/login'; const requests = []; // Make multiple requests quickly for (let i = 0; i < 10; i++) { requests.push( request(app) .post(endpoint) .send({ email: '<EMAIL>', password: 'WrongPassword' }) ); } const responses = await Promise.all(requests); // Some requests should be rate limited const rateLimitedResponses = responses.filter(res => res.status === 429); expect(rateLimitedResponses.length).toBeGreaterThan(0); }); }); describe('Error Handling', () => { it('should handle malformed JSON', async () => { const response = await request(app) .post('/api/auth/login') .set('Content-Type', 'application/json') .send('{"invalid": json}') .expect(400); expect(response.body.success).toBe(false); expect(response.body.error).toContain('JSON'); }); it('should handle missing content-type', async () => { const response = await request(app) .post('/api/auth/login') .send('some data') .expect(415); expect(response.body.success).toBe(false); expect(response.body.error).toContain('content type'); }); it('should return error ID for tracking', async () => { const response = await request(app) .get('/api/nonexistent-endpoint') .expect(404); expect(response.body.success).toBe(false); expect(response.body.errorId).toBeDefined(); expect(response.body.timestamp).toBeDefined(); }); }); describe('Security Headers', () => { it('should include security headers', async () => { const response = await request(app) .get('/health') .expect(200); expect(response.headers['x-content-type-options']).toBe('nosniff'); expect(response.headers['x-frame-options']).toBe('DENY'); expect(response.headers['x-xss-protection']).toBeDefined(); }); it('should include CORS headers', async () => { const response = await request(app) .options('/api/auth/login') .set('Origin', 'https://chatbotrncp.vercel.app') .expect(200); expect(response.headers['access-control-allow-origin']).toBeDefined(); expect(response.headers['access-control-allow-methods']).toBeDefined(); }); }); });