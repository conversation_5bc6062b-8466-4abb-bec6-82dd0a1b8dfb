/** * ============================================= * [SECURITY] AUTHENTICATION-ENABLED SERVER * Full backend server with authentication endpoints * Includes login, registration, and JWT token management * ============================================= */ require('dotenv').config({ path: '../.env' }); const express = require('express'); const cors = require('cors'); const helmet = require('helmet'); const morgan = require('morgan'); const compression = require('compression'); const jwt = require('jsonwebtoken'); const bcrypt = require('bcryptjs'); const { body, validationResult } = require('express-validator'); console.log('[DEPLOY] Starting authentication-enabled server...'); const app = express(); // Basic middleware app.use(helmet({ contentSecurityPolicy: false, // Disable for development })); app.use(cors({ origin: ['http://localhost:3000', 'http://localhost:3001'], credentials: true, methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'], allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'] })); app.use(compression()); app.use(morgan('combined')); app.use(express.json({ limit: '10mb' })); app.use(express.urlencoded({ extended: true, limit: '10mb' })); // Mock user database (in production, this would be MongoDB) const mockUsers = [ { id: '1', email: '<EMAIL>', password: '$2a$10$CTMdS0Bbopjgu.tvGTYAqev9WHQZqbeGn/atQvFQkQEk/XeDvjy4O', // password: AdminPassword123! role: 'admin', profile: { firstName: 'Admin', lastName: 'Free Mobile' }, lastLogin: null }, { id: '2', email: '<EMAIL>', password: '$2a$10$CTMdS0Bbopjgu.tvGTYAqev9WHQZqbeGn/atQvFQkQEk/XeDvjy4O', // password: AdminPassword123! role: 'agent', profile: { firstName: 'Agent', lastName: 'Support' }, lastLogin: null }, { id: '3', email: '<EMAIL>', password: '$2a$10$CTMdS0Bbopjgu.tvGTYAqev9WHQZqbeGn/atQvFQkQEk/XeDvjy4O', // password: AdminPassword123! role: 'user', profile: { firstName: 'User', lastName: 'Test' }, lastLogin: null } ]; // JWT Secret (in production, this should be in environment variables) const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-for-development-only'; // Utility functions const generateToken = (userId) => { return jwt.sign({ userId }, JWT_SECRET, { expiresIn: '7d' }); }; const findUserByEmail = (email) => { return mockUsers.find(user => user.email.toLowerCase() === email.toLowerCase()); }; const findUserById = (id) => { return mockUsers.find(user => user.id === id); }; const comparePassword = async (plainPassword, hashedPassword) => { return await bcrypt.compare(plainPassword, hashedPassword); }; // Validation middleware const validate = (req, res, next) => { const errors = validationResult(req); if (!errors.isEmpty()) { return res.status(400).json({ success: false, error: 'Données invalides', details: errors.array().map(err => err.msg) }); } next(); }; // Health check endpoint app.get('/health', (req, res) => { res.json({ status: 'ok', timestamp: new Date().toISOString(), environment: process.env.NODE_ENV || 'development', services: { authentication: 'active', database: 'mock-active' } }); }); // Basic API endpoint app.get('/api/status', (req, res) => { res.json({ message: 'ChatbotRNCP Authentication API is running', version: '2.0.0', features: ['authentication', 'jwt-tokens', 'user-management'] }); }); // Authentication Routes // POST /api/auth/login app.post('/api/auth/login', [ body('email').isEmail().normalizeEmail().withMessage('Email valide requis'), body('password').notEmpty().withMessage('Mot de passe requis'), ], validate, async (req, res) => { try { const { email, password } = req.body; console.log(`[SECURITY] Login attempt for: ${email}`); // Find user const user = findUserByEmail(email); if (!user) { console.log(`[FAILED] User not found: ${email}`); return res.status(401).json({ success: false, error: 'Identifiants incorrects' }); } // Verify password const isValidPassword = await comparePassword(password, user.password); if (!isValidPassword) { console.log(`[FAILED] Invalid password for: ${email}`); return res.status(401).json({ success: false, error: 'Identifiants incorrects' }); } // Update last login user.lastLogin = new Date().toISOString(); // Generate token const token = generateToken(user.id); console.log(`[COMPLETE] Login successful for: ${email}`); res.json({ success: true, message: 'Connexion réussie', user: { id: user.id, email: user.email, role: user.role, profile: user.profile, lastLogin: user.lastLogin }, token, expiresIn: '7d' }); } catch (error) { console.error('[FAILED] Login error:', error); res.status(500).json({ success: false, error: 'Erreur serveur lors de la connexion' }); } }); // POST /api/auth/register app.post('/api/auth/register', [ body('email').isEmail().normalizeEmail().withMessage('Email valide requis'), body('password').isLength({ min: 6 }).withMessage('Mot de passe minimum 6 caractères'), body('firstName').notEmpty().trim().withMessage('Prénom requis'), body('lastName').notEmpty().trim().withMessage('Nom requis'), ], validate, async (req, res) => { try { const { email, password, firstName, lastName } = req.body; console.log(` Registration attempt for: ${email}`); // Check if user already exists const existingUser = findUserByEmail(email); if (existingUser) { console.log(`[FAILED] User already exists: ${email}`); return res.status(409).json({ success: false, error: 'Un compte avec cet email existe déjà' }); } // Hash password const hashedPassword = await bcrypt.hash(password, 10); // Create new user const newUser = { id: String(mockUsers.length + 1), email: email.toLowerCase(), password: hashedPassword, role: 'user', profile: { firstName: firstName.trim(), lastName: lastName.trim() }, lastLogin: null }; mockUsers.push(newUser); // Generate token const token = generateToken(newUser.id); console.log(`[COMPLETE] Registration successful for: ${email}`); res.status(201).json({ success: true, message: 'Compte créé avec succès', user: { id: newUser.id, email: newUser.email, role: newUser.role, profile: newUser.profile }, token, expiresIn: '7d' }); } catch (error) { console.error('[FAILED] Registration error:', error); res.status(500).json({ success: false, error: 'Erreur serveur lors de la création du compte' }); } }); // GET /api/auth/validate (verify token and get user info) - alias for /me app.get('/api/auth/validate', (req, res) => { try { const authHeader = req.headers.authorization; if (!authHeader || !authHeader.startsWith('Bearer ')) { return res.status(401).json({ success: false, error: 'Token manquant' }); } const token = authHeader.substring(7); const decoded = jwt.verify(token, JWT_SECRET); const user = findUserById(decoded.userId); if (!user) { return res.status(401).json({ success: false, error: 'Utilisateur non trouvé' }); } res.json({ success: true, user: { id: user.id, email: user.email, role: user.role, profile: user.profile, lastLogin: user.lastLogin } }); } catch (error) { console.error('[FAILED] Token verification error:', error); res.status(401).json({ success: false, error: 'Token invalide' }); } }); // GET /api/auth/me (verify token and get user info) app.get('/api/auth/me', (req, res) => { try { const authHeader = req.headers.authorization; if (!authHeader || !authHeader.startsWith('Bearer ')) { return res.status(401).json({ success: false, error: 'Token manquant' }); } const token = authHeader.substring(7); const decoded = jwt.verify(token, JWT_SECRET); const user = findUserById(decoded.userId); if (!user) { return res.status(401).json({ success: false, error: 'Utilisateur non trouvé' }); } res.json({ success: true, user: { id: user.id, email: user.email, role: user.role, profile: user.profile, lastLogin: user.lastLogin } }); } catch (error) { console.error('[FAILED] Token verification error:', error); res.status(401).json({ success: false, error: 'Token invalide' }); } }); // Mock AI Call endpoints for testing app.post('/api/ai-calls/initiate', (req, res) => { res.json({ success: true, message: 'AI call initiated successfully', callSession: { callId: 'call_' + Date.now(), status: 'initiated', urgencyLevel: req.body.urgencyLevel || 'medium', description: req.body.description || 'Test call' } }); }); // Error handling middleware app.use((err, req, res, next) => { console.error('[FAILED] Server error:', err); res.status(500).json({ success: false, error: 'Erreur serveur interne' }); }); // 404 handler app.use('*', (req, res) => { res.status(404).json({ success: false, error: 'Endpoint non trouvé', path: req.originalUrl }); }); const PORT = process.env.PORT || 5000; const server = app.listen(PORT, () => { console.log(`[COMPLETE] Authentication server running on port ${PORT}`); console.log(` Health check: http://localhost:${PORT}/health`); console.log(` API status: http://localhost:${PORT}/api/status`); console.log(`[SECURITY] Login endpoint: http://localhost:${PORT}/api/auth/login`); console.log(` Register endpoint: http://localhost:${PORT}/api/auth/register`); console.log(`[USER] Profile endpoint: http://localhost:${PORT}/api/auth/me`); console.log(''); console.log(' Test credentials:'); console.log(' Email: <EMAIL>'); console.log(' Password: AdminPassword123!'); console.log(''); }); // Graceful shutdown process.on('SIGTERM', () => { console.log('SIGTERM received, shutting down gracefully'); server.close(() => { console.log('Process terminated'); }); }); process.on('SIGINT', () => { console.log('SIGINT received, shutting down gracefully'); server.close(() => { console.log('Process terminated'); }); }); module.exports = app;