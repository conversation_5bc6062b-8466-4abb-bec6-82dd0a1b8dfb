/** * Vercel Serverless Function - Enhanced AI Assistance: Contextual Suggestions * Suggestions contextuelles IA pour Free Mobile Chatbot Dashboard */ const { MongoClient, ServerApiVersion } = require('mongodb'); // Configuration MongoDB const MONGODB_URI = process.env.MONGODB_URI; const DATABASE_NAME = process.env.DATABASE_NAME || 'freemobile_chatbot_prod'; // Cache de connexion let cachedClient = null; let cachedDb = null; async function connectToDatabase() { if (cachedClient && cachedDb) { return { client: cachedClient, db: cachedDb }; } const client = new MongoClient(MONGODB_URI, { serverApi: { version: ServerApiVersion.v1, strict: true, deprecationErrors: true, }, maxPoolSize: 10, serverSelectionTimeoutMS: 5000, socketTimeoutMS: 45000, }); await client.connect(); const db = client.db(DATABASE_NAME); cachedClient = client; cachedDb = db; return { client, db }; } // Génération de suggestions contextuelles basées sur l'IA function generateContextualSuggestions(conversationContext) { const { customerMessage, customerProfile, conversationHistory } = conversationContext; const suggestions = []; // Analyse du sentiment du message client const sentiment = analyzeSentiment(customerMessage); // Suggestions basées sur le sentiment if (sentiment.score < -0.3) { // Client frustré suggestions.push({ id: 'empathy_1', category: 'empathetic_response', content: 'Je comprends votre frustration et je vais faire tout mon possible pour résoudre votre problème rapidement.', confidence: 0.92, reasoning: 'Sentiment négatif détecté - réponse empathique recommandée', priority: 1 }); suggestions.push({ id: 'deescalation_1', category: 'de_escalation', content: 'Je vous présente mes excuses pour ce désagrément. Permettez-moi de vérifier immédiatement votre dossier.', confidence: 0.88, reasoning: 'Technique de désescalade pour client mécontent', priority: 2 }); } else if (sentiment.score > 0.3) { // Client satisfait suggestions.push({ id: 'positive_1', category: 'positive_reinforcement', content: 'Je suis ravi de pouvoir vous aider ! Voici ce que je peux faire pour vous.', confidence: 0.85, reasoning: 'Sentiment positif - renforcement de la relation client', priority: 1 }); } // Suggestions basées sur le type de problème if (customerMessage.toLowerCase().includes('facture') || customerMessage.toLowerCase().includes('facturation')) { suggestions.push({ id: 'billing_1', category: 'information_request', content: 'Pour mieux vous aider avec votre facture, puis-je avoir votre numéro de ligne ou votre identifiant client ?', confidence: 0.94, reasoning: 'Problème de facturation détecté - demande d\'informations spécifiques', priority: 1 }); suggestions.push({ id: 'billing_2', category: 'solution_offer', content: 'Je peux consulter le détail de votre facture et vous expliquer chaque élément. Souhaitez-vous que nous la passions en revue ensemble ?', confidence: 0.89, reasoning: 'Proposition de solution proactive pour problème de facturation', priority: 2 }); } if (customerMessage.toLowerCase().includes('internet') || customerMessage.toLowerCase().includes('connexion')) { suggestions.push({ id: 'tech_1', category: 'solution_offer', content: 'Pour diagnostiquer votre problème de connexion, pouvons-nous commencer par vérifier l\'état de votre Freebox ?', confidence: 0.91, reasoning: 'Problème technique détecté - approche de diagnostic structurée', priority: 1 }); } if (customerMessage.toLowerCase().includes('résilier') || customerMessage.toLowerCase().includes('arrêter')) { suggestions.push({ id: 'retention_1', category: 'retention', content: 'Je comprends votre souhait. Avant de procéder, puis-je connaître les raisons qui vous amènent à cette décision ?', confidence: 0.87, reasoning: 'Intention de résiliation détectée - stratégie de rétention', priority: 1 }); suggestions.push({ id: 'retention_2', category: 'retention', content: 'Nous avons peut-être des solutions qui pourraient répondre à vos préoccupations. Puis-je vous présenter quelques options ?', confidence: 0.83, reasoning: 'Proposition d\'alternatives pour éviter la résiliation', priority: 2 }); } // Suggestions génériques de qualité suggestions.push({ id: 'clarification_1', category: 'clarification', content: 'Pour m\'assurer de bien comprendre votre demande, pourriez-vous me donner plus de détails ?', confidence: 0.75, reasoning: 'Suggestion générique de clarification', priority: 3 }); // Trier par priorité et confiance return suggestions .sort((a, b) => { if (a.priority !== b.priority) return a.priority - b.priority; return b.confidence - a.confidence; }) .slice(0, 5); // Limiter à 5 suggestions } function analyzeSentiment(text) { // Simulation d'analyse de sentiment (en production, utiliser un vrai service NLP) const positiveWords = ['merci', 'parfait', 'excellent', 'satisfait', 'content', 'bien']; const negativeWords = ['problème', 'frustré', 'mécontent', 'nul', 'horrible', 'inacceptable']; const words = text.toLowerCase().split(/\s+/); let score = 0; words.forEach(word => { if (positiveWords.includes(word)) score += 0.3; if (negativeWords.includes(word)) score -= 0.4; }); // Normaliser le score entre -1 et 1 score = Math.max(-1, Math.min(1, score)); let label = 'neutral'; if (score > 0.2) label = 'positive'; else if (score < -0.2) label = 'negative'; return { score, label, confidence: Math.abs(score) > 0.5 ? 0.9 : 0.7 }; } module.exports = async (req, res) => { // Configuration CORS res.setHeader('Access-Control-Allow-Origin', '*'); res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS'); res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization'); // Gérer les requêtes OPTIONS if (req.method === 'OPTIONS') { res.status(200).end(); return; } try { // Vérifier si la fonctionnalité est activée if (process.env.ENABLE_AI_ASSISTANCE !== 'true') { return res.status(503).json({ success: false, error: 'AI assistance feature is disabled', message: 'Contact administrator to enable this feature' }); } if (req.method === 'POST') { const { conversationContext } = req.body; if (!conversationContext || !conversationContext.customerMessage) { return res.status(400).json({ success: false, error: 'Missing conversation context', message: 'customerMessage is required in conversationContext' }); } // Générer les suggestions contextuelles const suggestions = generateContextualSuggestions(conversationContext); // Analyser le sentiment const sentiment = analyzeSentiment(conversationContext.customerMessage); return res.status(200).json({ success: true, data: { suggestions, sentiment, conversationId: conversationContext.conversationId || `conv_${Date.now()}`, agentId: conversationContext.agentId || 'demo_agent', timestamp: new Date().toISOString(), modelInfo: { name: 'Free Mobile AI Assistant v3.2', confidence: 0.92, responseTime: '< 500ms', features: [ 'sentiment_analysis', 'contextual_suggestions', 'empathy_detection', 'escalation_prevention' ] } } }); } else if (req.method === 'GET') { // Endpoint de démonstration avec données statiques const demoSuggestions = [ { id: 'demo_1', category: 'empathetic_response', content: 'Je comprends votre préoccupation et je vais vous aider à résoudre ce problème.', confidence: 0.95, reasoning: 'Réponse empathique standard', priority: 1 }, { id: 'demo_2', category: 'information_request', content: 'Pour mieux vous assister, pourriez-vous me fournir votre numéro de ligne ?', confidence: 0.88, reasoning: 'Demande d\'informations pour personnaliser l\'aide', priority: 2 }, { id: 'demo_3', category: 'solution_offer', content: 'Je peux vous proposer plusieurs solutions. Laquelle vous intéresse le plus ?', confidence: 0.82, reasoning: 'Proposition proactive de solutions', priority: 3 } ]; return res.status(200).json({ success: true, data: { suggestions: demoSuggestions, stats: { totalSuggestions: 3, averageConfidence: 0.88, categories: ['empathetic_response', 'information_request', 'solution_offer'], lastUpdated: new Date().toISOString() }, features: { aiAssistance: true, sentimentAnalysis: true, contextualSuggestions: true, realTimeProcessing: true } }, timestamp: new Date().toISOString() }); } } catch (error) { console.error('AI suggestions error:', error); return res.status(500).json({ success: false, error: 'Failed to generate AI suggestions', message: error.message, timestamp: new Date().toISOString() }); } };