/** * Vercel Serverless Function - Comprehensive Analytics Dashboard * Tableau de bord analytique pour Free Mobile Chatbot Dashboard */ const { MongoClient, ServerApiVersion } = require('mongodb'); // Configuration MongoDB const MONGODB_URI = process.env.MONGODB_URI; const DATABASE_NAME = process.env.DATABASE_NAME || 'freemobile_chatbot_prod'; // Cache de connexion let cachedClient = null; let cachedDb = null; async function connectToDatabase() { if (cachedClient && cachedDb) { return { client: cachedClient, db: cachedDb }; } const client = new MongoClient(MONGODB_URI, { serverApi: { version: ServerApiVersion.v1, strict: true, deprecationErrors: true, }, maxPoolSize: 10, serverSelectionTimeoutMS: 5000, socketTimeoutMS: 45000, }); await client.connect(); const db = client.db(DATABASE_NAME); cachedClient = client; cachedDb = db; return { client, db }; } // Génération de données analytiques de démonstration function generateAnalyticsData() { const now = new Date(); const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()); // KPIs principaux const kpis = { totalConversations: 15847, activeAgents: 127, averageResponseTime: 1.8, // minutes customerSatisfaction: 4.3, // sur 5 resolutionRate: 89.2, // pourcentage escalationRate: 5.7, // pourcentage dailyGrowth: 3.2, // pourcentage weeklyGrowth: 12.8 // pourcentage }; // Données temporelles (derniers 7 jours) const timeSeriesData = []; for (let i = 6; i >= 0; i--) { const date = new Date(today); date.setDate(date.getDate() - i); timeSeriesData.push({ date: date.toISOString().split('T')[0], conversations: Math.floor(2000 + Math.random() * 500), resolutions: Math.floor(1700 + Math.random() * 400), escalations: Math.floor(100 + Math.random() * 50), satisfaction: Math.round((4.0 + Math.random() * 0.8) * 10) / 10, responseTime: Math.round((1.5 + Math.random() * 1.0) * 10) / 10 }); } // Répartition par catégorie const categoryDistribution = [ { category: 'Facturation', count: 4521, percentage: 28.5 }, { category: 'Technique', count: 3794, percentage: 23.9 }, { category: 'Commercial', count: 2847, percentage: 18.0 }, { category: 'Résiliation', count: 2156, percentage: 13.6 }, { category: 'Activation', count: 1689, percentage: 10.7 }, { category: 'Autres', count: 840, percentage: 5.3 } ]; // Performance des agents const agentPerformance = [ { agentId: 'AGT001', name: 'Marie Dubois', conversations: 89, satisfaction: 4.7, avgResponseTime: 1.2 }, { agentId: 'AGT002', name: 'Pierre Martin', conversations: 76, satisfaction: 4.5, avgResponseTime: 1.5 }, { agentId: 'AGT003', name: 'Sophie Leroy', conversations: 82, satisfaction: 4.6, avgResponseTime: 1.3 }, { agentId: 'AGT004', name: 'Jean Dupont', conversations: 71, satisfaction: 4.2, avgResponseTime: 1.8 }, { agentId: 'AGT005', name: 'Claire Bernard', conversations: 94, satisfaction: 4.8, avgResponseTime: 1.1 } ]; // Métriques en temps réel const realTimeMetrics = { currentActiveConversations: 234, waitingQueue: 12, averageWaitTime: 0.8, // minutes peakHourPrediction: '14:00-16:00', systemLoad: 67, // pourcentage apiResponseTime: 145, // millisecondes databaseConnections: 8, errorRate: 0.3 // pourcentage }; // Insights et recommandations const insights = [ { type: 'performance', title: 'Pic d\'activité détecté', description: 'Augmentation de 15% des conversations entre 14h et 16h', recommendation: 'Considérer l\'ajout d\'agents durant cette période', priority: 'medium', impact: 'positive' }, { type: 'quality', title: 'Satisfaction client élevée', description: 'Score de satisfaction de 4.3/5 maintenu sur 7 jours', recommendation: 'Continuer les pratiques actuelles', priority: 'low', impact: 'positive' }, { type: 'efficiency', title: 'Temps de réponse optimisé', description: 'Réduction de 12% du temps de réponse moyen', recommendation: 'Documenter les bonnes pratiques', priority: 'low', impact: 'positive' } ]; return { kpis, timeSeriesData, categoryDistribution, agentPerformance, realTimeMetrics, insights, lastUpdated: new Date().toISOString() }; } module.exports = async (req, res) => { // Configuration CORS res.setHeader('Access-Control-Allow-Origin', '*'); res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS'); res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization'); // Gérer les requêtes OPTIONS if (req.method === 'OPTIONS') { res.status(200).end(); return; } if (req.method !== 'GET') { return res.status(405).json({ error: 'Method not allowed' }); } try { // Vérifier si la fonctionnalité est activée if (process.env.ENABLE_COMPREHENSIVE_ANALYTICS !== 'true') { return res.status(503).json({ success: false, error: 'Comprehensive analytics feature is disabled', message: 'Contact administrator to enable this feature' }); } const { period = '7d', metrics = 'all', agentId, category } = req.query; // Générer les données analytiques const analyticsData = generateAnalyticsData(); // Filtrage par agent si spécifié if (agentId) { analyticsData.agentPerformance = analyticsData.agentPerformance.filter( agent => agent.agentId === agentId ); } // Filtrage par catégorie si spécifié if (category) { analyticsData.categoryDistribution = analyticsData.categoryDistribution.filter( cat => cat.category.toLowerCase() === category.toLowerCase() ); } // Filtrage des métriques si spécifié let responseData = analyticsData; if (metrics !== 'all') { const requestedMetrics = metrics.split(','); responseData = {}; requestedMetrics.forEach(metric => { if (analyticsData[metric]) { responseData[metric] = analyticsData[metric]; } }); responseData.lastUpdated = analyticsData.lastUpdated; } // Métadonnées sur le dashboard const dashboardMeta = { version: '2.1.0', features: { realTimeUpdates: true, predictiveInsights: true, customDashboards: true, dataExport: true, alerting: true }, updateFrequency: '30 seconds', dataRetention: '90 days', supportedPeriods: ['1d', '7d', '30d', '90d'], availableMetrics: [ 'kpis', 'timeSeriesData', 'categoryDistribution', 'agentPerformance', 'realTimeMetrics', 'insights' ] }; return res.status(200).json({ success: true, data: responseData, meta: dashboardMeta, query: { period, metrics, agentId: agentId || null, category: category || null }, timestamp: new Date().toISOString(), responseTime: `${Date.now() - Date.now()}ms` }); } catch (error) { console.error('Analytics dashboard error:', error); return res.status(500).json({ success: false, error: 'Failed to generate analytics data', message: error.message, timestamp: new Date().toISOString() }); } };