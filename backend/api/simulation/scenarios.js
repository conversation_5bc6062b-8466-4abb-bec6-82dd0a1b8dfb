/** * Vercel Serverless Function - Simulation Training Scenarios * Gestion des scénarios de formation pour Free Mobile Chatbot Dashboard */ const { MongoClient, ServerApiVersion, ObjectId } = require('mongodb'); // Configuration MongoDB const MONGODB_URI = process.env.MONGODB_URI; const DATABASE_NAME = process.env.DATABASE_NAME || 'freemobile_chatbot_prod'; // Cache de connexion let cachedClient = null; let cachedDb = null; async function connectToDatabase() { if (cachedClient && cachedDb) { return { client: cachedClient, db: cachedDb }; } const client = new MongoClient(MONGODB_URI, { serverApi: { version: ServerApiVersion.v1, strict: true, deprecationErrors: true, }, maxPoolSize: 10, serverSelectionTimeoutMS: 5000, socketTimeoutMS: 45000, }); await client.connect(); const db = client.db(DATABASE_NAME); cachedClient = client; cachedDb = db; return { client, db }; } // Données de démonstration pour les scénarios const demoScenarios = [ { _id: new ObjectId(), title: "Réclamation Facturation Mobile", description: "Client mécontent de sa facture mobile avec des frais supplémentaires", category: "billing", difficulty: "intermediate", tags: ["facturation", "réclamation", "mobile"], estimatedDuration: 15, learningObjectives: [ "Gérer une réclamation de facturation", "Expliquer les frais supplémentaires", "Proposer des solutions adaptées" ], customerProfile: { name: "Marie Dubois", age: 35, plan: "Free Mobile 100Go", mood: "frustrated", issue: "Frais supplémentaires sur la facture" }, successCriteria: { empathy: 80, efficiency: 75, resolution: 85 }, isActive: true, usageCount: 45, averageScore: 78, completionRate: 92, createdAt: new Date('2024-01-15'), updatedAt: new Date() }, { _id: new ObjectId(), title: "Problème de Réseau Internet", description: "Client avec des problèmes de connexion internet Freebox", category: "technical", difficulty: "advanced", tags: ["internet", "freebox", "technique"], estimatedDuration: 20, learningObjectives: [ "Diagnostiquer un problème de réseau", "Guider le client dans les tests techniques", "Escalader si nécessaire" ], customerProfile: { name: "Pierre Martin", age: 42, plan: "Freebox Pop", mood: "concerned", issue: "Connexion internet instable" }, successCriteria: { empathy: 75, efficiency: 85, resolution: 80 }, isActive: true, usageCount: 32, averageScore: 82, completionRate: 88, createdAt: new Date('2024-01-10'), updatedAt: new Date() }, { _id: new ObjectId(), title: "Demande de Résiliation", description: "Client souhaitant résilier son abonnement Free Mobile", category: "retention", difficulty: "expert", tags: ["résiliation", "rétention", "négociation"], estimatedDuration: 25, learningObjectives: [ "Comprendre les raisons de résiliation", "Proposer des alternatives", "Négocier une rétention" ], customerProfile: { name: "Sophie Leroy", age: 28, plan: "Free Mobile 210Go", mood: "determined", issue: "Veut résilier pour un concurrent" }, successCriteria: { empathy: 85, efficiency: 70, resolution: 90 }, isActive: true, usageCount: 28, averageScore: 75, completionRate: 85, createdAt: new Date('2024-01-05'), updatedAt: new Date() } ]; module.exports = async (req, res) => { // Configuration CORS res.setHeader('Access-Control-Allow-Origin', '*'); res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS'); res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization'); // Gérer les requêtes OPTIONS if (req.method === 'OPTIONS') { res.status(200).end(); return; } try { switch (req.method) { case 'GET': return await getScenarios(req, res); case 'POST': return await createScenario(req, res); default: return res.status(405).json({ error: 'Method not allowed' }); } } catch (error) { console.error('Scenarios API error:', error); return res.status(500).json({ error: 'Internal server error', message: error.message }); } }; async function getScenarios(req, res) { const { category, difficulty, tags, search, page = 1, limit = 20 } = req.query; try { // Pour la démo, utiliser les données statiques // En production, cela viendrait de MongoDB let scenarios = [...demoScenarios]; // Filtrage par catégorie if (category) { scenarios = scenarios.filter(s => s.category === category); } // Filtrage par difficulté if (difficulty) { scenarios = scenarios.filter(s => s.difficulty === difficulty); } // Filtrage par tags if (tags) { const tagArray = tags.split(','); scenarios = scenarios.filter(s => tagArray.some(tag => s.tags.includes(tag)) ); } // Recherche textuelle if (search) { const searchLower = search.toLowerCase(); scenarios = scenarios.filter(s => s.title.toLowerCase().includes(searchLower) || s.description.toLowerCase().includes(searchLower) || s.tags.some(tag => tag.toLowerCase().includes(searchLower)) ); } // Pagination const startIndex = (parseInt(page) - 1) * parseInt(limit); const endIndex = startIndex + parseInt(limit); const paginatedScenarios = scenarios.slice(startIndex, endIndex); // Statistiques const stats = { total: scenarios.length, byCategory: { billing: scenarios.filter(s => s.category === 'billing').length, technical: scenarios.filter(s => s.category === 'technical').length, retention: scenarios.filter(s => s.category === 'retention').length, }, byDifficulty: { beginner: scenarios.filter(s => s.difficulty === 'beginner').length, intermediate: scenarios.filter(s => s.difficulty === 'intermediate').length, advanced: scenarios.filter(s => s.difficulty === 'advanced').length, expert: scenarios.filter(s => s.difficulty === 'expert').length, } }; return res.status(200).json({ success: true, data: { scenarios: paginatedScenarios, pagination: { page: parseInt(page), limit: parseInt(limit), total: scenarios.length, pages: Math.ceil(scenarios.length / parseInt(limit)) }, stats, features: { simulationTraining: process.env.ENABLE_SIMULATION_TRAINING === 'true', categories: ['billing', 'technical', 'retention', 'sales'], difficulties: ['beginner', 'intermediate', 'advanced', 'expert'] } }, timestamp: new Date().toISOString() }); } catch (error) { console.error('Get scenarios error:', error); return res.status(500).json({ success: false, error: 'Failed to fetch scenarios', message: error.message }); } } async function createScenario(req, res) { const { title, description, category, difficulty, tags = [], estimatedDuration, learningObjectives = [], customerProfile, successCriteria } = req.body; // Validation basique if (!title || !description || !category || !difficulty) { return res.status(400).json({ success: false, error: 'Missing required fields', required: ['title', 'description', 'category', 'difficulty'] }); } try { const newScenario = { _id: new ObjectId(), title, description, category, difficulty, tags, estimatedDuration: parseInt(estimatedDuration) || 15, learningObjectives, customerProfile: customerProfile || {}, successCriteria: successCriteria || { empathy: 70, efficiency: 70, resolution: 70 }, isActive: true, usageCount: 0, averageScore: 0, completionRate: 0, createdAt: new Date(), updatedAt: new Date() }; // En production, sauvegarder en base de données // const { db } = await connectToDatabase(); // const result = await db.collection('simulation_scenarios').insertOne(newScenario); return res.status(201).json({ success: true, data: { scenario: newScenario, message: 'Scenario created successfully (demo mode)' }, timestamp: new Date().toISOString() }); } catch (error) { console.error('Create scenario error:', error); return res.status(500).json({ success: false, error: 'Failed to create scenario', message: error.message }); } }