/** * Vercel Serverless Function - System Health Check * Compatible avec la structure Vercel pour Free Mobile Chatbot Dashboard */ const { MongoClient, ServerApiVersion } = require('mongodb'); // Configuration MongoDB const MONGODB_URI = process.env.MONGODB_URI; const DATABASE_NAME = process.env.DATABASE_NAME || 'freemobile_chatbot_prod'; // Cache de connexion pour les fonctions serverless let cachedClient = null; let cachedDb = null; async function connectToDatabase() { if (cachedClient && cachedDb) { return { client: cachedClient, db: cachedDb }; } const client = new MongoClient(MONGODB_URI, { serverApi: { version: ServerApiVersion.v1, strict: true, deprecationErrors: true, }, maxPoolSize: 10, serverSelectionTimeoutMS: 5000, socketTimeoutMS: 45000, }); await client.connect(); const db = client.db(DATABASE_NAME); cachedClient = client; cachedDb = db; return { client, db }; } async function healthCheck() { try { const { client } = await connectToDatabase(); await client.db('admin').command({ ping: 1 }); return { status: 'healthy', timestamp: new Date().toISOString(), database: 'connected' }; } catch (error) { console.error('Database health check failed:', error); return { status: 'unhealthy', error: error.message, timestamp: new Date().toISOString(), database: 'disconnected' }; } } module.exports = async (req, res) => { // Configuration CORS res.setHeader('Access-Control-Allow-Origin', '*'); res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS'); res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization'); // Gérer les requêtes OPTIONS (preflight) if (req.method === 'OPTIONS') { res.status(200).end(); return; } // Seules les requêtes GET sont autorisées if (req.method !== 'GET') { return res.status(405).json({ error: 'Method not allowed', message: 'Only GET requests are allowed for health checks' }); } try { const startTime = Date.now(); // Vérification de la base de données const dbHealth = await healthCheck(); // Calcul du temps de réponse const responseTime = Date.now() - startTime; // Informations système const systemInfo = { timestamp: new Date().toISOString(), environment: process.env.NODE_ENV || 'development', version: process.env.npm_package_version || '1.0.0', uptime: process.uptime(), memory: process.memoryUsage(), responseTime: `${responseTime}ms`, region: process.env.VERCEL_REGION || 'unknown', }; // Statut des fonctionnalités enhanced const featuresStatus = { simulationTraining: process.env.ENABLE_SIMULATION_TRAINING === 'true', predictiveAnalytics: process.env.ENABLE_PREDICTIVE_ANALYTICS === 'true', aiAssistance: process.env.ENABLE_AI_ASSISTANCE === 'true', comprehensiveAnalytics: process.env.ENABLE_COMPREHENSIVE_ANALYTICS === 'true', }; // Statut global de santé const isHealthy = dbHealth.status === 'healthy' && responseTime < 5000; const healthStatus = { status: isHealthy ? 'healthy' : 'unhealthy', database: dbHealth, system: systemInfo, features: featuresStatus, checks: { database: dbHealth.status === 'healthy', responseTime: responseTime < 5000, memory: systemInfo.memory.heapUsed < 500 * 1024 * 1024, // 500MB limit environment: process.env.NODE_ENV === 'production', }, endpoints: { simulation: '/api/simulation/scenarios', predictive: '/api/predictive/churn', aiAssistance: '/api/enhanced-ai/suggestions', analytics: '/api/analytics/dashboard' } }; // Code de statut approprié const statusCode = isHealthy ? 200 : 503; // Headers de cache res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate'); res.setHeader('Pragma', 'no-cache'); res.setHeader('Expires', '0'); return res.status(statusCode).json(healthStatus); } catch (error) { console.error('Health check failed:', error); return res.status(503).json({ status: 'unhealthy', error: error.message, timestamp: new Date().toISOString(), database: { status: 'unhealthy', error: error.message }, system: { timestamp: new Date().toISOString(), environment: process.env.NODE_ENV || 'development', uptime: process.uptime(), region: process.env.VERCEL_REGION || 'unknown', } }); } };