# 🚫 Backend .dockerignore
# Exclure les fichiers inutiles pour optimiser le build Docker

# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment files (secrets doivent être passés via variables)
.env
.env.*

# Development files
nodemon.json
.nodemonignore

# IDE files
.vscode
.idea
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore
.gitattributes

# Testing
test
tests
*.test.js
*.spec.js
coverage
.nyc_output
*.lcov

# Logs (seront créés dans le container)
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Uploads (seront gérés par volumes)
uploads
temp
tmp

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# ESLint cache
.eslintcache

# Documentation
README.md
CHANGELOG.md
docs/

# Docker files
Dockerfile*
docker-compose*
.dockerignore

# Build outputs
dist
build

# TypeScript (si utilisé)
*.tsbuildinfo

# Monitoring et stats
.pm2

# Backup files
*.bak
*.backup 