# 🚀 TEMPLATE CONFIGURATION PRODUCTION - CHATBOT FREE MOBILE
# ===========================================================
# Copiez ce fichier vers .env.production et remplissez les valeurs

# Environnement
NODE_ENV=production
ENVIRONMENT=production

# 🌐 Configuration Serveur
PORT=5000
HOST=0.0.0.0
DOMAIN=your-domain.com
FRONTEND_URL=https://your-domain.com
BACKEND_URL=https://api.your-domain.com

# 🔐 Sécurité JWT - CHANGEZ CES VALEURS !
JWT_SECRET=CHANGEZ-MOI-PRODUCTION-SECRET-TRES-LONG-ET-COMPLEXE-2025
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=30d

# 🗄️ Base de Données MongoDB
MONGO_URI=***************************************************************************************
MONGO_USERNAME=freemobile
MONGO_PASSWORD=CHANGEZ-MOI-PASSWORD-MONGO-PRODUCTION
MONGO_DB_NAME=freemobile-chatbot

# 🔥 Cache Redis
REDIS_URL=redis://:CHANGEZ-PASSWORD@redis:6379
REDIS_PASSWORD=CHANGEZ-MOI-PASSWORD-REDIS-PRODUCTION
REDIS_HOST=redis
REDIS_PORT=6379

# 🤖 Intelligence Artificielle
OPENAI_API_KEY=sk-YOUR-REAL-OPENAI-API-KEY-HERE
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=150
OPENAI_TEMPERATURE=0.7

# 🎯 Rasa NLP
RASA_SERVER_URL=http://rasa:5005
RASA_MODEL_PATH=/app/models
RASA_WEBHOOK_URL=http://backend:5000/api/webhooks/rasa

# 📧 Configuration Email Production
EMAIL_HOST=smtp.votre-provider.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=CHANGEZ-MOI-PASSWORD-EMAIL
EMAIL_FROM=<EMAIL>
EMAIL_SECURE=true

# 📱 SMS Configuration
SMS_PROVIDER=freemobile
SMS_API_KEY=VOTRE-CLE-API-SMS
SMS_API_SECRET=VOTRE-SECRET-API-SMS

# 🔔 Webhooks et Notifications
WEBHOOK_URL=https://webhooks.votre-domaine.com/chatbot
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/VOTRE-WEBHOOK
TEAMS_WEBHOOK_URL=https://outlook.office.com/webhook/VOTRE-WEBHOOK

# 📊 Analytics et Monitoring
ANALYTICS_ENABLED=true
ANALYTICS_API_KEY=VOTRE-CLE-ANALYTICS
SENTRY_DSN=https://<EMAIL>/PROJECT-ID
PROMETHEUS_ENABLED=true
GRAFANA_ENABLED=true

# 🌍 Configuration Régionale
DEFAULT_LANGUAGE=fr
SUPPORTED_LANGUAGES=fr,en,es
TIMEZONE=Europe/Paris

# 🔒 Sécurité Avancée
TWO_FACTOR_ENABLED=true
SESSION_TIMEOUT=3600000
MAX_LOGIN_ATTEMPTS=5
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# 🎭 Interface Agent
AGENT_INTERFACE_ENABLED=true
AGENT_SESSION_TIMEOUT=7200000
MAX_CONCURRENT_CONVERSATIONS=50

# 🧠 Modes Intelligents
INTELLIGENT_MODES_ENABLED=true
SENTIMENT_ANALYSIS_ENABLED=true
PROACTIVE_INSIGHTS_ENABLED=true
CONTEXTUAL_MEMORY_ENABLED=true

# 📈 Performance
MAX_CONNECTIONS=1000
CONNECTION_TIMEOUT=30000
RESPONSE_TIMEOUT=10000
CACHE_TTL=3600

# 🔄 Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=/var/backups/chatbot

# 🏥 Monitoring Santé
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000

# 📝 Configuration Logs
LOG_LEVEL=info
LOG_FORMAT=json
LOG_FILE=/var/log/chatbot/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# 🚀 Docker et Registry
DOCKER_REGISTRY=your-registry.com/freemobile
DOCKER_TAG=latest
COMPOSE_PROJECT_NAME=freemobile-chatbot-prod

# 🌐 CDN et Statiques
CDN_URL=https://cdn.votre-domaine.com
STATIC_FILES_URL=https://static.votre-domaine.com
UPLOADS_MAX_SIZE=10485760

# 🔐 SSL/TLS
SSL_ENABLED=true
SSL_CERT_PATH=/etc/ssl/certs/chatbot.crt
SSL_KEY_PATH=/etc/ssl/private/chatbot.key
FORCE_HTTPS=true

# 🎪 Feature Flags
FEATURE_CHAT_HISTORY=true
FEATURE_FILE_UPLOAD=true
FEATURE_VOICE_MESSAGES=false
FEATURE_VIDEO_CALLS=false
FEATURE_MULTI_LANGUAGE=true
FEATURE_DARK_MODE=true

# 🔄 Sessions et Cache
SESSION_STORE=redis
SESSION_SECRET=CHANGEZ-MOI-SESSION-SECRET-PRODUCTION
SESSION_NAME=freemobile.chatbot.sid
SESSION_SECURE=true
SESSION_HTTP_ONLY=true

# 🎯 API Limiting
API_RATE_LIMIT_ENABLED=true
API_RATE_LIMIT_REQUESTS=1000
API_RATE_LIMIT_WINDOW=3600000

# 📱 Notifications Push
PUSH_NOTIFICATIONS_ENABLED=true
FCM_SERVER_KEY=VOTRE-FCM-SERVER-KEY
APNS_KEY_ID=VOTRE-APNS-KEY-ID

# 🔒 Headers de Sécurité
SECURITY_HEADERS_ENABLED=true
CSP_ENABLED=true
HSTS_ENABLED=true
X_FRAME_OPTIONS=DENY

# 📊 Monitoring Performance
APM_ENABLED=true
APM_SERVICE_NAME=freemobile-chatbot
APM_SERVICE_VERSION=1.0.0
APM_ENVIRONMENT=production

# 💾 Performance Base de Données
DB_POOL_SIZE=10
DB_CONNECTION_TIMEOUT=30000
DB_IDLE_TIMEOUT=600000
DB_MAX_CONNECTIONS=50 