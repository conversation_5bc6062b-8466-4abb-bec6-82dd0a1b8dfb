# Azure DevOps Pipeline for ChatbotRNCP # File: azure-pipelines.yml trigger: branches: include: - main - develop - feature/* paths: exclude: - docs/* - README.md pr: branches: include: - main - develop paths: exclude: - docs/* - README.md schedules: - cron: "0 2 * * *" displayName: Nightly Build branches: include: - main always: true variables: - group: ChatbotRNCP-Variables - name: nodeVersion value: '20.x' - name: dockerRegistry value: 'your-registry.azurecr.io' - name: imageName value: 'chatbot-rncp' - name: vmImageName value: 'ubuntu-latest' stages: # ========================================================================== # PREPARATION STAGE # ========================================================================== - stage: Preparation displayName: '[CONFIG] Preparation' jobs: - job: SetupMatrix displayName: 'Setup Test Matrix' pool: vmImage: $(vmImageName) steps: - checkout: self fetchDepth: 1 - task: PowerShell@2 displayName: 'Determine Test Strategy' inputs: targetType: 'inline' script: | $branch = "$(Build.SourceBranchName)" $isPR = "$(System.PullRequest.PullRequestId)" -ne "" if ($branch -eq "main" -and -not $isPR) { Write-Host "##vso[task.setvariable variable=testSuite;isOutput=true]all" Write-Host "##vso[task.setvariable variable=browsers;isOutput=true]chromium,firefox" Write-Host "##vso[task.setvariable variable=deployEnabled;isOutput=true]true" } elseif ($isPR) { Write-Host "##vso[task.setvariable variable=testSuite;isOutput=true]unit,e2e" Write-Host "##vso[task.setvariable variable=browsers;isOutput=true]chromium" Write-Host "##vso[task.setvariable variable=deployEnabled;isOutput=true]false" } else { Write-Host "##vso[task.setvariable variable=testSuite;isOutput=true]unit" Write-Host "##vso[task.setvariable variable=browsers;isOutput=true]chromium" Write-Host "##vso[task.setvariable variable=deployEnabled;isOutput=true]false" } $version = if ($branch -eq "main") { "v1.0.$(Build.BuildNumber)" } else { "$branch-$(Build.BuildNumber)" } Write-Host "##vso[task.setvariable variable=version;isOutput=true]$version" name: 'matrix' # ========================================================================== # SECURITY SCANNING STAGE # ========================================================================== - stage: SecurityScan displayName: ' Security Scan' dependsOn: Preparation condition: or(eq(variables['Build.SourceBranch'], 'refs/heads/main'), eq(variables['Build.Reason'], 'PullRequest')) jobs: - job: SecurityAnalysis displayName: 'Security Analysis' pool: vmImage: $(vmImageName) steps: - checkout: self fetchDepth: 0 - task: NodeTool@0 displayName: 'Install Node.js' inputs: versionSpec: $(nodeVersion) - task: Cache@2 displayName: 'Cache npm packages' inputs: key: 'npm | "$(Agent.OS)" | free-mobile-chatbot/frontend/package-lock.json, free-mobile-chatbot/backend/package-lock.json' restoreKeys: | npm | "$(Agent.OS)" path: ~/.npm - script: | cd free-mobile-chatbot npm ci --prefix frontend npm ci --prefix backend displayName: 'Install Dependencies' - script: | cd free-mobile-chatbot npm audit --prefix frontend --audit-level moderate npm audit --prefix backend --audit-level moderate displayName: 'Run Security Audit' continueOnError: true - task: Docker@2 displayName: 'Run Trivy Security Scan' inputs: command: 'run' arguments: '--rm -v $(Build.SourcesDirectory):/workspace aquasec/trivy:latest fs /workspace/free-mobile-chatbot --format json --output /workspace/trivy-results.json' - task: PublishBuildArtifacts@1 displayName: 'Publish Security Results' inputs: pathToPublish: 'trivy-results.json' artifactName: 'security-results' # ========================================================================== # TESTING STAGE # ========================================================================== - stage: Testing displayName: ' Testing' dependsOn: - Preparation - SecurityScan condition: succeeded() variables: testSuite: $[ stageDependencies.Preparation.SetupMatrix.outputs['matrix.testSuite'] ] browsers: $[ stageDependencies.Preparation.SetupMatrix.outputs['matrix.browsers'] ] jobs: - job: TestMatrix displayName: 'Test Matrix' strategy: matrix: Linux_Node18: imageName: 'ubuntu-latest' nodeVersion: '18.x' Linux_Node20: imageName: 'ubuntu-latest' nodeVersion: '20.x' Linux_Node22: imageName: 'ubuntu-latest' nodeVersion: '22.x' Windows_Node20: imageName: 'windows-latest' nodeVersion: '20.x' pool: vmImage: $(imageName) steps: - checkout: self fetchDepth: 1 - task: NodeTool@0 displayName: 'Install Node.js $(nodeVersion)' inputs: versionSpec: $(nodeVersion) - task: Cache@2 displayName: 'Cache npm packages' inputs: key: 'npm | "$(Agent.OS)" | "$(nodeVersion)" | free-mobile-chatbot/frontend/package-lock.json, free-mobile-chatbot/backend/package-lock.json' restoreKeys: | npm | "$(Agent.OS)" | "$(nodeVersion)" npm | "$(Agent.OS)" path: ~/.npm - task: Cache@2 displayName: 'Cache Playwright browsers' inputs: key: 'playwright | "$(Agent.OS)" | free-mobile-chatbot/frontend/package-lock.json' restoreKeys: | playwright | "$(Agent.OS)" path: | ~/.cache/ms-playwright ~/AppData/Local/ms-playwright - script: | cd free-mobile-chatbot npm ci --prefix frontend npm ci --prefix backend displayName: 'Install Dependencies' - script: | cd free-mobile-chatbot/frontend npx playwright install $(browsers) displayName: 'Install Playwright Browsers' - task: DockerCompose@0 displayName: 'Start Services' inputs: containerregistrytype: 'Container Registry' dockerComposeFile: 'free-mobile-chatbot/docker-compose.prod.yml' action: 'Run services' detached: true - script: | # Wait for services to be ready timeout 300 bash -c 'until curl -f http://localhost:5000/health; do sleep 5; done' timeout 300 bash -c 'until curl -f http://localhost:3001/; do sleep 5; done' displayName: 'Wait for Services' condition: eq(variables['Agent.OS'], 'Linux') - task: PowerShell@2 displayName: 'Wait for Services (Windows)' condition: eq(variables['Agent.OS'], 'Windows_NT') inputs: targetType: 'inline' script: | $timeout = 300 $elapsed = 0 do { try { Invoke-WebRequest -Uri "http://localhost:5000/health" -UseBasicParsing break } catch { Start-Sleep -Seconds 5 $elapsed += 5 } } while ($elapsed -lt $timeout) - script: | cd free-mobile-chatbot chmod +x scripts/automated-testing.sh ./scripts/automated-testing.sh --test-suite $(testSuite) --browser $(browsers) --report-format html,json,junit displayName: 'Run Tests (Linux)' condition: eq(variables['Agent.OS'], 'Linux') - task: PowerShell@2 displayName: 'Run Tests (Windows)' condition: eq(variables['Agent.OS'], 'Windows_NT') inputs: targetType: 'inline' script: | cd free-mobile-chatbot .\scripts\automated-testing.ps1 -TestSuite "$(testSuite)" -Browser "$(browsers)" -ReportFormat "html,json,junit" - task: PublishTestResults@2 displayName: 'Publish Test Results' condition: always() inputs: testResultsFormat: 'JUnit' testResultsFiles: 'free-mobile-chatbot/frontend/test-results/results.xml' testRunTitle: 'Test Results ($(Agent.OS)-$(nodeVersion))' - task: PublishCodeCoverageResults@1 displayName: 'Publish Coverage Results' condition: and(always(), contains(variables['testSuite'], 'unit')) inputs: codeCoverageTool: 'Cobertura' summaryFileLocation: 'free-mobile-chatbot/frontend/coverage/cobertura-coverage.xml' reportDirectory: 'free-mobile-chatbot/frontend/coverage' - task: PublishBuildArtifacts@1 displayName: 'Publish Test Artifacts' condition: always() inputs: pathToPublish: 'free-mobile-chatbot/reports' artifactName: 'test-results-$(Agent.OS)-$(nodeVersion)' # ========================================================================== # PERFORMANCE TESTING STAGE # ========================================================================== - stage: Performance displayName: '[PERFORMANCE] Performance Testing' dependsOn: - Preparation - Testing condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main')) jobs: - job: PerformanceTests displayName: 'Performance Tests' pool: vmImage: $(vmImageName) steps: - checkout: self fetchDepth: 1 - task: NodeTool@0 displayName: 'Install Node.js' inputs: versionSpec: $(nodeVersion) - script: | cd free-mobile-chatbot npm ci --prefix frontend npm ci --prefix backend displayName: 'Install Dependencies' - task: DockerCompose@0 displayName: 'Start Services' inputs: containerregistrytype: 'Container Registry' dockerComposeFile: 'free-mobile-chatbot/docker-compose.prod.yml' action: 'Run services' detached: true - script: | timeout 300 bash -c 'until curl -f http://localhost:5000/health; do sleep 5; done' displayName: 'Wait for Services' - script: | cd free-mobile-chatbot ./scripts/automated-testing.sh --test-suite performance --verbose displayName: 'Run Performance Tests' - script: | npm install -g @lhci/cli cd free-mobile-chatbot lhci autorun --config=lighthouserc.js displayName: 'Run Lighthouse CI' - task: PublishBuildArtifacts@1 displayName: 'Publish Performance Results' inputs: pathToPublish: 'free-mobile-chatbot/reports/performance-results.json' artifactName: 'performance-results' # ========================================================================== # BUILD STAGE # ========================================================================== - stage: Build displayName: '[ARCHITECTURE] Build & Push Images' dependsOn: - Preparation - Testing condition: and(succeeded(), or(eq(variables['Build.SourceBranch'], 'refs/heads/main'), eq(variables['Build.SourceBranch'], 'refs/heads/develop'))) variables: version: $[ stageDependencies.Preparation.SetupMatrix.outputs['matrix.version'] ] jobs: - job: BuildImages displayName: 'Build Docker Images' pool: vmImage: $(vmImageName) steps: - checkout: self fetchDepth: 1 - task: Docker@2 displayName: 'Login to Container Registry' inputs: containerRegistry: 'Azure Container Registry' command: 'login' - task: Docker@2 displayName: 'Build Frontend Image' inputs: containerRegistry: 'Azure Container Registry' repository: '$(imageName)-frontend' command: 'buildAndPush' Dockerfile: 'free-mobile-chatbot/frontend/Dockerfile.prod' buildContext: 'free-mobile-chatbot/frontend' tags: | $(version) latest - task: Docker@2 displayName: 'Build Backend Image' inputs: containerRegistry: 'Azure Container Registry' repository: '$(imageName)-backend' command: 'buildAndPush' Dockerfile: 'free-mobile-chatbot/backend/Dockerfile.prod' buildContext: 'free-mobile-chatbot/backend' tags: | $(version) latest # ========================================================================== # DEPLOYMENT STAGE # ========================================================================== - stage: Deploy displayName: '[DEPLOY] Deploy' dependsOn: - Preparation - Build - Performance condition: and(succeeded(), eq(stageDependencies.Preparation.SetupMatrix.outputs['matrix.deployEnabled'], 'true')) variables: version: $[ stageDependencies.Preparation.SetupMatrix.outputs['matrix.version'] ] jobs: - deployment: DeployToStaging displayName: 'Deploy to Staging' environment: 'staging' pool: vmImage: $(vmImageName) strategy: runOnce: deploy: steps: - checkout: self fetchDepth: 1 - script: | echo "[DEPLOY] Deploying version $(version) to staging" # Add your staging deployment logic here displayName: 'Deploy to Staging' - script: | # Run smoke tests curl -f https://staging.your-domain.com/health displayName: 'Run Smoke Tests' - deployment: DeployToProduction displayName: 'Deploy to Production' dependsOn: DeployToStaging environment: 'production' pool: vmImage: $(vmImageName) strategy: runOnce: deploy: steps: - checkout: self fetchDepth: 1 - script: | echo "[DEPLOY] Deploying version $(version) to production" # Add your production deployment logic here displayName: 'Deploy to Production' - script: | # Run smoke tests curl -f https://your-domain.com/health displayName: 'Run Smoke Tests'