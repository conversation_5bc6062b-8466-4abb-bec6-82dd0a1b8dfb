# GitHub Actions Workflow for ChatbotRNCP # File: .github/workflows/ci-cd.yml name: [DEPLOY] ChatbotRNCP CI/CD Pipeline on: push: branches: [ main, develop, feature/* ] pull_request: branches: [ main, develop ] release: types: [ published ] schedule: # Run nightly tests at 2 AM UTC - cron: '0 2 * * *' permissions: contents: read security-events: write env: NODE_VERSION_DEFAULT: '20.x' DOCKER_REGISTRY: ghcr.io IMAGE_NAME: chatbot-rncp jobs: # ============================================================================ # PREPARATION JOB # ============================================================================ prepare: name: [CONFIG] Prepare Environment runs-on: ubuntu-latest outputs: test-matrix: ${{ steps.matrix.outputs.matrix }} should-deploy: ${{ steps.deploy-check.outputs.should-deploy }} version: ${{ steps.version.outputs.version }} steps: - name: Checkout Code uses: actions/checkout@v4 with: fetch-depth: 0 - name: [SEARCH] Determine Test Matrix id: matrix run: | if [[ "${{ github.event_name }}" == "pull_request" ]]; then echo "matrix={\"node-version\":[\"20.x\"],\"test-suite\":[\"unit\",\"e2e\"],\"browser\":[\"chromium\"]}" >> $GITHUB_OUTPUT elif [[ "${{ github.ref }}" == "refs/heads/main" ]]; then echo "matrix={\"node-version\":[\"18.x\",\"20.x\",\"22.x\"],\"test-suite\":[\"unit\",\"e2e\",\"integration\"],\"browser\":[\"chromium\",\"firefox\"]}" >> $GITHUB_OUTPUT else echo "matrix={\"node-version\":[\"20.x\"],\"test-suite\":[\"unit\"],\"browser\":[\"chromium\"]}" >> $GITHUB_OUTPUT fi - name: [DEPLOY] Check Deployment Conditions id: deploy-check run: | if [[ "${{ github.ref }}" == "refs/heads/main" && "${{ github.event_name }}" == "push" ]]; then echo "should-deploy=true" >> $GITHUB_OUTPUT else echo "should-deploy=false" >> $GITHUB_OUTPUT fi - name: Generate Version id: version run: | if [[ "${{ github.event_name }}" == "release" ]]; then echo "version=${{ github.event.release.tag_name }}" >> $GITHUB_OUTPUT else echo "version=dev-$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT fi # ============================================================================ # SECURITY SCANNING # ============================================================================ security-scan: name: Security Scan runs-on: ubuntu-latest if: github.event_name == 'push' || github.event_name == 'pull_request' steps: - name: Checkout Code uses: actions/checkout@v4 - name: [SEARCH] Run Trivy Vulnerability Scanner uses: aquasecurity/trivy-action@0.20.0 with: scan-type: 'fs' scan-ref: '.' format: 'sarif' output: 'trivy-results.sarif' - name: Upload Trivy Results uses: github/codeql-action/upload-sarif@v3 if: always() with: sarif_file: 'trivy-results.sarif' - name: [SECURITY] Run CodeQL Analysis uses: github/codeql-action/init@v3 with: languages: javascript - name: [ARCHITECTURE] Autobuild uses: github/codeql-action/autobuild@v3 - name: [ANALYTICS] Perform CodeQL Analysis uses: github/codeql-action/analyze@v3 # ============================================================================ # TESTING MATRIX # ============================================================================ test: name: Test Suite runs-on: ${{ matrix.os }} needs: prepare if: always() && needs.prepare.result == 'success' strategy: fail-fast: false matrix: os: [ubuntu-latest, windows-latest] node-version: ${{ fromJson(needs.prepare.outputs.test-matrix).node-version }} test-suite: ${{ fromJson(needs.prepare.outputs.test-matrix).test-suite }} browser: ${{ fromJson(needs.prepare.outputs.test-matrix).browser }} exclude: # Exclude webkit on Windows (not supported) - os: windows-latest browser: webkit steps: - name: Checkout Code uses: actions/checkout@v4 - name: 🟢 Setup Node.js ${{ matrix.node-version }} uses: actions/setup-node@v4 with: node-version: ${{ matrix.node-version }} cache: 'npm' cache-dependency-path: | frontend/package-lock.json backend/package-lock.json - name: Setup Docker Buildx uses: docker/setup-buildx-action@v3 - name: [CONFIG] Install Dependencies run: | cd free-mobile-chatbot npm ci --prefix frontend npm ci --prefix backend - name: Cache Playwright Browsers uses: actions/cache@v3 with: path: | ~/.cache/ms-playwright ~/AppData/Local/ms-playwright key: ${{ runner.os }}-playwright-${{ hashFiles('**/package-lock.json') }} - name: Install Playwright Browsers run: | cd free-mobile-chatbot/frontend npx playwright install ${{ matrix.browser }} - name: [DEPLOY] Start Services run: | cd free-mobile-chatbot docker compose -f docker-compose.prod.yml up -d # Wait for services to be ready timeout 300 bash -c 'until curl -f http://localhost:5000/health; do sleep 5; done' timeout 300 bash -c 'until curl -f http://localhost:3001/; do sleep 5; done' - name: Run Tests (Linux/macOS) if: runner.os != 'Windows' run: | cd free-mobile-chatbot chmod +x scripts/automated-testing.sh ./scripts/automated-testing.sh --test-suite ${{ matrix.test-suite }} --browser ${{ matrix.browser }} --report-format html,json,junit - name: Run Tests (Windows) if: runner.os == 'Windows' shell: powershell run: | cd free-mobile-chatbot .\scripts\automated-testing.ps1 -TestSuite "${{ matrix.test-suite }}" -Browser "${{ matrix.browser }}" -ReportFormat "html,json,junit" - name: [ANALYTICS] Upload Test Results uses: actions/upload-artifact@v3 if: always() with: name: test-results-${{ matrix.os }}-node${{ matrix.node-version }}-${{ matrix.test-suite }}-${{ matrix.browser }} path: | free-mobile-chatbot/reports/ free-mobile-chatbot/frontend/test-results/ free-mobile-chatbot/frontend/coverage/ retention-days: 30 - name: [METRICS] Publish Test Results uses: dorny/test-reporter@v1 if: always() with: name: Test Results (${{ matrix.os }}-${{ matrix.node-version }}-${{ matrix.test-suite }}-${{ matrix.browser }}) path: 'free-mobile-chatbot/frontend/test-results/results.xml' reporter: jest-junit - name: [ANALYTICS] Upload Coverage to Codecov uses: codecov/codecov-action@v3 if: matrix.test-suite == 'unit' with: file: free-mobile-chatbot/frontend/coverage/lcov.info flags: frontend-${{ matrix.node-version }} name: codecov-${{ matrix.os }}-${{ matrix.node-version }} - name: Notify on Failure if: failure() uses: 8398a7/action-slack@v3 with: status: failure channel: '#ci-cd-alerts' webhook_url: ${{ secrets.SLACK_WEBHOOK }} fields: repo,message,commit,author,action,eventName,ref,workflow # ============================================================================ # PERFORMANCE TESTING # ============================================================================ performance: name: [PERFORMANCE] Performance Tests runs-on: ubuntu-latest needs: [prepare, test] if: github.ref == 'refs/heads/main' && github.event_name == 'push' steps: - name: Checkout Code uses: actions/checkout@v4 - name: 🟢 Setup Node.js uses: actions/setup-node@v4 with: node-version: ${{ env.NODE_VERSION_DEFAULT }} cache: 'npm' - name: [CONFIG] Install Dependencies run: | cd free-mobile-chatbot npm ci --prefix frontend npm ci --prefix backend - name: [DEPLOY] Start Services run: | cd free-mobile-chatbot docker compose -f docker-compose.prod.yml up -d timeout 300 bash -c 'until curl -f http://localhost:5000/health; do sleep 5; done' - name: [PERFORMANCE] Run Performance Tests run: | cd free-mobile-chatbot ./scripts/automated-testing.sh --test-suite performance --verbose - name: Run Lighthouse CI uses: treosh/lighthouse-ci-action@v10 with: configPath: './free-mobile-chatbot/lighthouserc.js' uploadArtifacts: true temporaryPublicStorage: true - name: [ANALYTICS] Upload Performance Results uses: actions/upload-artifact@v3 with: name: performance-results path: | free-mobile-chatbot/reports/performance-results.json .lighthouseci/ # ============================================================================ # BUILD AND PUSH DOCKER IMAGES # ============================================================================ build: name: [ARCHITECTURE] Build & Push Images runs-on: ubuntu-latest needs: [prepare, test] if: needs.prepare.outputs.should-deploy == 'true' outputs: image-digest: ${{ steps.build.outputs.digest }} steps: - name: Checkout Code uses: actions/checkout@v4 - name: Setup Docker Buildx uses: docker/setup-buildx-action@v3 - name: [SECURITY] Login to Container Registry uses: docker/login-action@v3 with: registry: ${{ env.DOCKER_REGISTRY }} username: ${{ github.actor }} password: ${{ secrets.GITHUB_TOKEN }} - name: Extract Metadata id: meta uses: docker/metadata-action@v5 with: images: ${{ env.DOCKER_REGISTRY }}/${{ github.repository }}/${{ env.IMAGE_NAME }} tags: | type=ref,event=branch type=ref,event=pr type=semver,pattern={{version}} type=semver,pattern={{major}}.{{minor}} type=sha,prefix={{branch}}- - name: [ARCHITECTURE] Build and Push Frontend uses: docker/build-push-action@v5 with: context: ./free-mobile-chatbot/frontend file: ./free-mobile-chatbot/frontend/Dockerfile.prod push: true tags: ${{ steps.meta.outputs.tags }}-frontend labels: ${{ steps.meta.outputs.labels }} cache-from: type=gha cache-to: type=gha,mode=max - name: [ARCHITECTURE] Build and Push Backend id: build uses: docker/build-push-action@v5 with: context: ./free-mobile-chatbot/backend file: ./free-mobile-chatbot/backend/Dockerfile.prod push: true tags: ${{ steps.meta.outputs.tags }}-backend labels: ${{ steps.meta.outputs.labels }} cache-from: type=gha cache-to: type=gha,mode=max # ============================================================================ # DEPLOYMENT # ============================================================================ deploy: name: [DEPLOY] Deploy to Production runs-on: ubuntu-latest needs: [prepare, test, performance, build] if: needs.prepare.outputs.should-deploy == 'true' environment: production steps: - name: Checkout Code uses: actions/checkout@v4 - name: [DEPLOY] Deploy to Production run: | echo "[DEPLOY] Deploying version ${{ needs.prepare.outputs.version }}" echo " Image digest: ${{ needs.build.outputs.image-digest }}" # Add your deployment logic here # Example: kubectl, helm, docker-compose, etc. - name: Run Smoke Tests run: | cd free-mobile-chatbot # Run minimal smoke tests against production curl -f https://your-production-url.com/health - name: Notify Success uses: 8398a7/action-slack@v3 with: status: success channel: '#deployments' webhook_url: ${{ secrets.SLACK_WEBHOOK }} fields: repo,message,commit,author,took # ============================================================================ # CLEANUP # ============================================================================ cleanup: name: Cleanup runs-on: ubuntu-latest needs: [test, performance, build, deploy] if: always() steps: - name: Clean up Docker run: | docker system prune -af docker volume prune -f - name: [ANALYTICS] Generate Final Report run: | echo "## [ANALYTICS] Pipeline Summary" >> $GITHUB_STEP_SUMMARY echo "- **Tests**: ${{ needs.test.result }}" >> $GITHUB_STEP_SUMMARY echo "- **Performance**: ${{ needs.performance.result }}" >> $GITHUB_STEP_SUMMARY echo "- **Build**: ${{ needs.build.result }}" >> $GITHUB_STEP_SUMMARY echo "- **Deploy**: ${{ needs.deploy.result }}" >> $GITHUB_STEP_SUMMARY