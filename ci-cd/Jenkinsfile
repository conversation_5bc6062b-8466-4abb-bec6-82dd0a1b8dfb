// Jenkins Pipeline for ChatbotRNCP
// File: Jenkinsfile

pipeline {
    agent none
    
    options {
        buildDiscarder(logRotator(numToKeepStr: '10'))
        timeout(time: 60, unit: 'MINUTES')
        timestamps()
        ansiColor('xterm')
    }
    
    environment {
        NODE_VERSION = '20'
        DOCKER_REGISTRY = 'your-registry.com'
        IMAGE_NAME = 'chatbot-rncp'
        COMPOSE_PROJECT_NAME = "chatbot-${env.BUILD_NUMBER}"
        
        // Credentials
        DOCKER_CREDENTIALS = credentials('docker-registry-credentials')
        MONGODB_URI = credentials('mongodb-uri')
        JWT_SECRET = credentials('jwt-secret')
        SLACK_WEBHOOK = credentials('slack-webhook')
    }
    
    stages {
        // ====================================================================
        // PREPARATION STAGE
        // ====================================================================
        stage('🔧 Preparation') {
            agent { label 'linux' }
            
            steps {
                script {
                    // Determine test strategy based on branch
                    if (env.BRANCH_NAME == 'main') {
                        env.TEST_SUITE = 'all'
                        env.BROWSERS = 'chromium,firefox'
                        env.DEPLOY_ENABLED = 'true'
                    } else if (env.BRANCH_NAME.startsWith('PR-')) {
                        env.TEST_SUITE = 'unit,e2e'
                        env.BROWSERS = 'chromium'
                        env.DEPLOY_ENABLED = 'false'
                    } else {
                        env.TEST_SUITE = 'unit'
                        env.BROWSERS = 'chromium'
                        env.DEPLOY_ENABLED = 'false'
                    }
                    
                    // Generate version
                    env.VERSION = env.BRANCH_NAME == 'main' ? 
                        "v1.0.${env.BUILD_NUMBER}" : 
                        "${env.BRANCH_NAME}-${env.BUILD_NUMBER}"
                }
                
                echo "🎯 Test Strategy: ${env.TEST_SUITE}"
                echo "🌐 Browsers: ${env.BROWSERS}"
                echo "🚀 Deploy: ${env.DEPLOY_ENABLED}"
                echo "📦 Version: ${env.VERSION}"
            }
        }
        
        // ====================================================================
        // SECURITY SCANNING
        // ====================================================================
        stage('🔒 Security Scan') {
            agent { label 'linux' }
            
            when {
                anyOf {
                    branch 'main'
                    changeRequest()
                }
            }
            
            parallel {
                stage('🔍 Dependency Check') {
                    steps {
                        script {
                            checkout scm
                            
                            dir('free-mobile-chatbot') {
                                sh '''
                                    # Install dependencies
                                    npm ci --prefix frontend
                                    npm ci --prefix backend
                                    
                                    # Run security audit
                                    npm audit --prefix frontend --audit-level moderate
                                    npm audit --prefix backend --audit-level moderate
                                '''
                            }
                        }
                    }
                }
                
                stage('🛡️ Container Security') {
                    steps {
                        script {
                            // Run Trivy security scanner
                            sh '''
                                docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
                                    -v $(pwd):/workspace \
                                    aquasec/trivy:latest fs /workspace/free-mobile-chatbot \
                                    --format json --output trivy-results.json
                            '''
                            
                            archiveArtifacts artifacts: 'trivy-results.json', allowEmptyArchive: true
                        }
                    }
                }
            }
        }
        
        // ====================================================================
        // TESTING MATRIX
        // ====================================================================
        stage('🧪 Testing Matrix') {
            parallel {
                stage('🐧 Linux Tests') {
                    agent { 
                        label 'linux'
                        // Use Docker agent for consistent environment
                        dockerfile {
                            filename 'ci-cd/Dockerfile.jenkins'
                            args '-v /var/run/docker.sock:/var/run/docker.sock'
                        }
                    }
                    
                    steps {
                        script {
                            runTestSuite('linux')
                        }
                    }
                    
                    post {
                        always {
                            publishTestResults()
                            archiveTestArtifacts('linux')
                        }
                    }
                }
                
                stage('🪟 Windows Tests') {
                    agent { label 'windows' }
                    
                    when {
                        branch 'main'
                    }
                    
                    steps {
                        script {
                            runTestSuite('windows')
                        }
                    }
                    
                    post {
                        always {
                            publishTestResults()
                            archiveTestArtifacts('windows')
                        }
                    }
                }
            }
        }
        
        // ====================================================================
        // PERFORMANCE TESTING
        // ====================================================================
        stage('⚡ Performance Tests') {
            agent { label 'linux' }
            
            when {
                branch 'main'
            }
            
            steps {
                script {
                    checkout scm
                    
                    dir('free-mobile-chatbot') {
                        sh '''
                            # Start services
                            docker compose -f docker-compose.prod.yml up -d
                            
                            # Wait for services
                            timeout 300 bash -c 'until curl -f http://localhost:5000/health; do sleep 5; done'
                            
                            # Run performance tests
                            chmod +x scripts/automated-testing.sh
                            ./scripts/automated-testing.sh --test-suite performance --verbose
                            
                            # Run Lighthouse
                            npm install -g @lhci/cli
                            lhci autorun --config=lighthouserc.js
                        '''
                    }
                }
            }
            
            post {
                always {
                    archiveArtifacts artifacts: 'free-mobile-chatbot/reports/performance-results.json', allowEmptyArchive: true
                    archiveArtifacts artifacts: '.lighthouseci/**/*', allowEmptyArchive: true
                }
            }
        }
        
        // ====================================================================
        // BUILD STAGE
        // ====================================================================
        stage('🏗️ Build Images') {
            agent { label 'linux' }
            
            when {
                anyOf {
                    branch 'main'
                    branch 'develop'
                }
            }
            
            steps {
                script {
                    checkout scm
                    
                    // Login to Docker registry
                    sh '''
                        echo $DOCKER_CREDENTIALS_PSW | docker login $DOCKER_REGISTRY -u $DOCKER_CREDENTIALS_USR --password-stdin
                    '''
                    
                    // Build and push images
                    dir('free-mobile-chatbot') {
                        parallel(
                            frontend: {
                                sh """
                                    docker build -f frontend/Dockerfile.prod -t ${DOCKER_REGISTRY}/${IMAGE_NAME}-frontend:${VERSION} frontend/
                                    docker push ${DOCKER_REGISTRY}/${IMAGE_NAME}-frontend:${VERSION}
                                """
                            },
                            backend: {
                                sh """
                                    docker build -f backend/Dockerfile.prod -t ${DOCKER_REGISTRY}/${IMAGE_NAME}-backend:${VERSION} backend/
                                    docker push ${DOCKER_REGISTRY}/${IMAGE_NAME}-backend:${VERSION}
                                """
                            }
                        )
                    }
                }
            }
            
            post {
                always {
                    sh 'docker logout $DOCKER_REGISTRY'
                }
            }
        }
        
        // ====================================================================
        // DEPLOYMENT STAGE
        // ====================================================================
        stage('🚀 Deploy') {
            agent { label 'linux' }
            
            when {
                allOf {
                    branch 'main'
                    environment name: 'DEPLOY_ENABLED', value: 'true'
                }
            }
            
            steps {
                script {
                    // Deploy to staging first
                    stage('🧪 Deploy to Staging') {
                        deployToEnvironment('staging')
                        runSmokeTests('staging')
                    }
                    
                    // Manual approval for production
                    input message: 'Deploy to Production?', ok: 'Deploy',
                          submitterParameter: 'DEPLOYER'
                    
                    // Deploy to production
                    stage('🏭 Deploy to Production') {
                        deployToEnvironment('production')
                        runSmokeTests('production')
                    }
                }
            }
        }
    }
    
    post {
        always {
            script {
                // Cleanup Docker resources
                sh '''
                    docker compose -f free-mobile-chatbot/docker-compose.prod.yml down -v || true
                    docker system prune -f || true
                '''
            }
        }
        
        success {
            script {
                sendSlackNotification('success', '✅ Pipeline completed successfully!')
            }
        }
        
        failure {
            script {
                sendSlackNotification('danger', '❌ Pipeline failed!')
            }
        }
        
        unstable {
            script {
                sendSlackNotification('warning', '⚠️ Pipeline completed with warnings!')
            }
        }
    }
}

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

def runTestSuite(platform) {
    checkout scm
    
    dir('free-mobile-chatbot') {
        if (platform == 'windows') {
            bat '''
                REM Install dependencies
                npm ci --prefix frontend
                npm ci --prefix backend
                
                REM Start services
                docker compose -f docker-compose.prod.yml up -d
                
                REM Wait for services
                timeout /t 60 /nobreak
                
                REM Run tests
                powershell -ExecutionPolicy Bypass -File scripts\\automated-testing.ps1 -TestSuite "%TEST_SUITE%" -Browser "%BROWSERS%" -ReportFormat "html,json,junit"
            '''
        } else {
            sh '''
                # Install dependencies
                npm ci --prefix frontend
                npm ci --prefix backend
                
                # Start services
                docker compose -f docker-compose.prod.yml up -d
                
                # Wait for services
                timeout 300 bash -c 'until curl -f http://localhost:5000/health; do sleep 5; done'
                timeout 300 bash -c 'until curl -f http://localhost:3001/; do sleep 5; done'
                
                # Run tests
                chmod +x scripts/automated-testing.sh
                ./scripts/automated-testing.sh --test-suite ${TEST_SUITE} --browser ${BROWSERS} --report-format html,json,junit
            '''
        }
    }
}

def publishTestResults() {
    // Publish JUnit test results
    publishTestResults testResultsPattern: 'free-mobile-chatbot/frontend/test-results/results.xml'
    
    // Publish HTML reports
    publishHTML([
        allowMissing: false,
        alwaysLinkToLastBuild: true,
        keepAll: true,
        reportDir: 'free-mobile-chatbot/reports',
        reportFiles: 'test-report.html',
        reportName: 'Test Report'
    ])
    
    // Publish coverage reports
    publishCoverage adapters: [
        istanbulCoberturaAdapter('free-mobile-chatbot/frontend/coverage/cobertura-coverage.xml')
    ], sourceFileResolver: sourceFiles('STORE_LAST_BUILD')
}

def archiveTestArtifacts(platform) {
    archiveArtifacts artifacts: """
        free-mobile-chatbot/reports/**/*,
        free-mobile-chatbot/frontend/test-results/**/*,
        free-mobile-chatbot/frontend/coverage/**/*
    """, allowEmptyArchive: true, fingerprint: true
}

def deployToEnvironment(environment) {
    echo "🚀 Deploying to ${environment}"
    
    // Example deployment using docker-compose
    sh """
        # Update environment-specific configuration
        export ENVIRONMENT=${environment}
        export VERSION=${VERSION}
        
        # Deploy using docker-compose
        envsubst < free-mobile-chatbot/docker-compose.${environment}.yml | docker compose -f - up -d
    """
}

def runSmokeTests(environment) {
    echo "🧪 Running smoke tests against ${environment}"
    
    sh """
        # Wait for deployment to be ready
        sleep 30
        
        # Run basic health checks
        curl -f https://${environment}.your-domain.com/health
        curl -f https://${environment}.your-domain.com/api/health
    """
}

def sendSlackNotification(color, message) {
    if (env.SLACK_WEBHOOK) {
        slackSend(
            channel: '#ci-cd-alerts',
            color: color,
            message: """
                ${message}
                
                *Job:* ${env.JOB_NAME}
                *Build:* ${env.BUILD_NUMBER}
                *Branch:* ${env.BRANCH_NAME}
                *Commit:* ${env.GIT_COMMIT?.take(8)}
                *Duration:* ${currentBuild.durationString}
                
                <${env.BUILD_URL}|View Build>
            """,
            teamDomain: 'your-team',
            token: env.SLACK_WEBHOOK
        )
    }
}
