# GitLab CI/CD Pipeline for ChatbotRNCP # File: .gitlab-ci.yml stages: - prepare - security - test - performance - build - deploy - cleanup variables: NODE_VERSION: "20" DOCKER_REGISTRY: $CI_REGISTRY IMAGE_NAME: chatbot-rncp DOCKER_DRIVER: overlay2 DOCKER_TLS_CERTDIR: "/certs" # Test configuration COMPOSE_PROJECT_NAME: "chatbot-${CI_PIPELINE_ID}" MONGODB_URI: "mongodb://localhost:27017/chatbot_test" # Cache configuration npm_config_cache: "$CI_PROJECT_DIR/.npm" CYPRESS_CACHE_FOLDER: "$CI_PROJECT_DIR/cache/Cypress" # ============================================================================ # TEMPLATES # ============================================================================ .node_template: &node_template image: node:${NODE_VERSION} before_script: - cd free-mobile-chatbot - npm ci --cache .npm --prefer-offline --prefix frontend - npm ci --cache .npm --prefer-offline --prefix backend cache: key: files: - free-mobile-chatbot/frontend/package-lock.json - free-mobile-chatbot/backend/package-lock.json paths: - .npm/ - cache/Cypress/ - free-mobile-chatbot/frontend/node_modules/ - free-mobile-chatbot/backend/node_modules/ .docker_template: &docker_template image: docker:24.0.5 services: - docker:24.0.5-dind before_script: - docker info - echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY .test_template: &test_template <<: *node_template services: - docker:24.0.5-dind - mongo:6.0 - redis:7-alpine variables: DOCKER_HOST: tcp://docker:2376 DOCKER_TLS_CERTDIR: "/certs" DOCKER_TLS_VERIFY: 1 DOCKER_CERT_PATH: "$DOCKER_TLS_CERTDIR/client" before_script: - !reference [.node_template, before_script] - cd free-mobile-chatbot/frontend && npx playwright install chromium firefox # ============================================================================ # PREPARATION STAGE # ============================================================================ prepare:matrix: stage: prepare image: alpine:latest script: - | if [ "$CI_COMMIT_REF_NAME" = "main" ] && [ "$CI_PIPELINE_SOURCE" = "push" ]; then echo "TEST_SUITE=all" >> prepare.env echo "BROWSERS=chromium,firefox" >> prepare.env echo "DEPLOY_ENABLED=true" >> prepare.env elif [ "$CI_PIPELINE_SOURCE" = "merge_request_event" ]; then echo "TEST_SUITE=unit,e2e" >> prepare.env echo "BROWSERS=chromium" >> prepare.env echo "DEPLOY_ENABLED=false" >> prepare.env else echo "TEST_SUITE=unit" >> prepare.env echo "BROWSERS=chromium" >> prepare.env echo "DEPLOY_ENABLED=false" >> prepare.env fi VERSION=$(if [ "$CI_COMMIT_REF_NAME" = "main" ]; then echo "v1.0.$CI_PIPELINE_ID"; else echo "$CI_COMMIT_REF_NAME-$CI_PIPELINE_ID"; fi) echo "VERSION=$VERSION" >> prepare.env cat prepare.env artifacts: reports: dotenv: prepare.env expire_in: 1 hour # ============================================================================ # SECURITY STAGE # ============================================================================ security:dependency-scan: stage: security <<: *node_template script: - npm audit --prefix frontend --audit-level moderate - npm audit --prefix backend --audit-level moderate allow_failure: true rules: - if: $CI_COMMIT_REF_NAME == "main" - if: $CI_PIPELINE_SOURCE == "merge_request_event" security:container-scan: stage: security image: name: aquasec/trivy:latest entrypoint: [""] script: - trivy fs --format json --output trivy-results.json free-mobile-chatbot/ artifacts: reports: container_scanning: trivy-results.json expire_in: 1 week rules: - if: $CI_COMMIT_REF_NAME == "main" - if: $CI_PIPELINE_SOURCE == "merge_request_event" security:sast: stage: security include: - template: Security/SAST.gitlab-ci.yml rules: - if: $CI_COMMIT_REF_NAME == "main" - if: $CI_PIPELINE_SOURCE == "merge_request_event" # ============================================================================ # TESTING STAGE # ============================================================================ .test_job_template: &test_job_template stage: test <<: *test_template script: - cd free-mobile-chatbot - docker compose -f docker-compose.prod.yml up -d - | timeout 300 bash -c 'until curl -f http://localhost:5000/health; do sleep 5; done' timeout 300 bash -c 'until curl -f http://localhost:3001/; do sleep 5; done' - chmod +x scripts/automated-testing.sh - ./scripts/automated-testing.sh --test-suite $TEST_SUITE --browser $BROWSERS --report-format html,json,junit after_script: - cd free-mobile-chatbot - docker compose -f docker-compose.prod.yml down -v || true artifacts: when: always reports: junit: free-mobile-chatbot/frontend/test-results/results.xml coverage_report: coverage_format: cobertura path: free-mobile-chatbot/frontend/coverage/cobertura-coverage.xml paths: - free-mobile-chatbot/reports/ - free-mobile-chatbot/frontend/test-results/ - free-mobile-chatbot/frontend/coverage/ expire_in: 1 week test:node18: <<: *test_job_template image: node:18 rules: - if: $CI_COMMIT_REF_NAME == "main" test:node20: <<: *test_job_template image: node:20 test:node22: <<: *test_job_template image: node:22 rules: - if: $CI_COMMIT_REF_NAME == "main" # ============================================================================ # PERFORMANCE STAGE # ============================================================================ performance:lighthouse: stage: performance <<: *test_template script: - cd free-mobile-chatbot - docker compose -f docker-compose.prod.yml up -d - timeout 300 bash -c 'until curl -f http://localhost:5000/health; do sleep 5; done' - ./scripts/automated-testing.sh --test-suite performance --verbose - npm install -g @lhci/cli - lhci autorun --config=lighthouserc.js artifacts: reports: performance: free-mobile-chatbot/reports/performance-results.json paths: - .lighthouseci/ expire_in: 1 week rules: - if: $CI_COMMIT_REF_NAME == "main" performance:load-test: stage: performance image: loadimpact/k6:latest script: - | cat > load-test.js << 'EOF' import http from 'k6/http'; import { check, sleep } from 'k6'; export let options = { stages: [ { duration: '2m', target: 10 }, { duration: '5m', target: 10 }, { duration: '2m', target: 0 }, ], }; export default function() { let response = http.get('http://localhost:5000/health'); check(response, { 'status is 200': (r) => r.status === 200, 'response time < 500ms': (r) => r.timings.duration < 500, }); sleep(1); } EOF - k6 run load-test.js rules: - if: $CI_COMMIT_REF_NAME == "main" - when: manual # ============================================================================ # BUILD STAGE # ============================================================================ build:frontend: stage: build <<: *docker_template script: - cd free-mobile-chatbot/frontend - docker build -f Dockerfile.prod -t $CI_REGISTRY_IMAGE/frontend:$VERSION . - docker push $CI_REGISTRY_IMAGE/frontend:$VERSION - docker tag $CI_REGISTRY_IMAGE/frontend:$VERSION $CI_REGISTRY_IMAGE/frontend:latest - docker push $CI_REGISTRY_IMAGE/frontend:latest rules: - if: $CI_COMMIT_REF_NAME == "main" - if: $CI_COMMIT_REF_NAME == "develop" build:backend: stage: build <<: *docker_template script: - cd free-mobile-chatbot/backend - docker build -f Dockerfile.prod -t $CI_REGISTRY_IMAGE/backend:$VERSION . - docker push $CI_REGISTRY_IMAGE/backend:$VERSION - docker tag $CI_REGISTRY_IMAGE/backend:$VERSION $CI_REGISTRY_IMAGE/backend:latest - docker push $CI_REGISTRY_IMAGE/backend:latest rules: - if: $CI_COMMIT_REF_NAME == "main" - if: $CI_COMMIT_REF_NAME == "develop" # ============================================================================ # DEPLOYMENT STAGE # ============================================================================ deploy:staging: stage: deploy image: alpine:latest environment: name: staging url: https://staging.your-domain.com before_script: - apk add --no-cache curl script: - echo "[DEPLOY] Deploying version $VERSION to staging" - | # Example deployment using curl to trigger deployment webhook curl -X POST \ -H "Authorization: Bearer $STAGING_DEPLOY_TOKEN" \ -H "Content-Type: application/json" \ -d "{\"version\":\"$VERSION\",\"environment\":\"staging\"}" \ $STAGING_DEPLOY_WEBHOOK - sleep 30 # Wait for deployment - curl -f https://staging.your-domain.com/health rules: - if: $CI_COMMIT_REF_NAME == "main" && $DEPLOY_ENABLED == "true" deploy:production: stage: deploy image: alpine:latest environment: name: production url: https://your-domain.com before_script: - apk add --no-cache curl script: - echo "[DEPLOY] Deploying version $VERSION to production" - | curl -X POST \ -H "Authorization: Bearer $PRODUCTION_DEPLOY_TOKEN" \ -H "Content-Type: application/json" \ -d "{\"version\":\"$VERSION\",\"environment\":\"production\"}" \ $PRODUCTION_DEPLOY_WEBHOOK - sleep 60 # Wait for deployment - curl -f https://your-domain.com/health when: manual rules: - if: $CI_COMMIT_REF_NAME == "main" && $DEPLOY_ENABLED == "true" # ============================================================================ # CLEANUP STAGE # ============================================================================ cleanup:docker: stage: cleanup <<: *docker_template script: - docker system prune -af - docker volume prune -f when: always rules: - if: $CI_PIPELINE_SOURCE == "schedule" - when: manual # ============================================================================ # NOTIFICATION JOBS # ============================================================================ notify:success: stage: .post image: alpine:latest before_script: - apk add --no-cache curl script: - | curl -X POST \ -H "Content-Type: application/json" \ -d "{ \"text\": \"[COMPLETE] Pipeline succeeded for $CI_PROJECT_NAME\", \"attachments\": [{ \"color\": \"good\", \"fields\": [ {\"title\": \"Branch\", \"value\": \"$CI_COMMIT_REF_NAME\", \"short\": true}, {\"title\": \"Commit\", \"value\": \"$CI_COMMIT_SHORT_SHA\", \"short\": true}, {\"title\": \"Pipeline\", \"value\": \"$CI_PIPELINE_URL\", \"short\": false} ] }] }" \ $SLACK_WEBHOOK rules: - if: $CI_COMMIT_REF_NAME == "main" when: on_success notify:failure: stage: .post image: alpine:latest before_script: - apk add --no-cache curl script: - | curl -X POST \ -H "Content-Type: application/json" \ -d "{ \"text\": \"[FAILED] Pipeline failed for $CI_PROJECT_NAME\", \"attachments\": [{ \"color\": \"danger\", \"fields\": [ {\"title\": \"Branch\", \"value\": \"$CI_COMMIT_REF_NAME\", \"short\": true}, {\"title\": \"Commit\", \"value\": \"$CI_COMMIT_SHORT_SHA\", \"short\": true}, {\"title\": \"Pipeline\", \"value\": \"$CI_PIPELINE_URL\", \"short\": false} ] }] }" \ $SLACK_WEBHOOK rules: - when: on_failure