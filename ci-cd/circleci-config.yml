# CircleCI Configuration for ChatbotRNCP # File: .circleci/config.yml version: 2.1 # ============================================================================ # ORBS # ============================================================================ orbs: node: circleci/node@5.1.0 docker: circleci/docker@2.4.0 slack: circleci/slack@4.12.1 codecov: codecov/codecov@3.2.4 # ============================================================================ # EXECUTORS # ============================================================================ executors: node-executor: docker: - image: cimg/node:20.9 - image: cimg/mongo:6.0 environment: MONGO_INITDB_ROOT_USERNAME: admin MONGO_INITDB_ROOT_PASSWORD: password - image: cimg/redis:7.0 working_directory: ~/project/free-mobile-chatbot environment: NODE_ENV: test MONGODB_URI: ********************************************************************** REDIS_URL: redis://localhost:6379 docker-executor: docker: - image: cimg/base:stable working_directory: ~/project # ============================================================================ # COMMANDS # ============================================================================ commands: setup-dependencies: description: "Install and cache dependencies" steps: - restore_cache: keys: - v1-dependencies-{{ checksum "frontend/package-lock.json" }}-{{ checksum "backend/package-lock.json" }} - v1-dependencies- - run: name: Install Dependencies command: | npm ci --prefix frontend npm ci --prefix backend - save_cache: key: v1-dependencies-{{ checksum "frontend/package-lock.json" }}-{{ checksum "backend/package-lock.json" }} paths: - frontend/node_modules - backend/node_modules - ~/.npm setup-playwright: description: "Install and cache Playwright browsers" parameters: browsers: type: string default: "chromium" steps: - restore_cache: keys: - v1-playwright-<< parameters.browsers >>-{{ checksum "frontend/package-lock.json" }} - v1-playwright-<< parameters.browsers >>- - run: name: Install Playwright Browsers command: | cd frontend npx playwright install << parameters.browsers >> - save_cache: key: v1-playwright-<< parameters.browsers >>-{{ checksum "frontend/package-lock.json" }} paths: - ~/.cache/ms-playwright start-services: description: "Start application services" steps: - setup_remote_docker: version: 20.10.14 docker_layer_caching: true - run: name: Start Services command: | docker compose -f docker-compose.prod.yml up -d # Wait for services to be ready timeout 300 bash -c 'until curl -f http://localhost:5000/health; do sleep 5; done' timeout 300 bash -c 'until curl -f http://localhost:3001/; do sleep 5; done' background: false run-automated-tests: description: "Run automated test suite" parameters: test-suite: type: string default: "unit" browsers: type: string default: "chromium" report-format: type: string default: "html,json,junit" steps: - run: name: Run Automated Tests command: | chmod +x scripts/automated-testing.sh ./scripts/automated-testing.sh \ --test-suite << parameters.test-suite >> \ --browser << parameters.browsers >> \ --report-format << parameters.report-format >> no_output_timeout: 30m publish-test-results: description: "Publish test results and artifacts" steps: - store_test_results: path: frontend/test-results - store_artifacts: path: reports destination: test-reports - store_artifacts: path: frontend/test-results destination: test-results - store_artifacts: path: frontend/coverage destination: coverage # ============================================================================ # JOBS # ============================================================================ jobs: # -------------------------------------------------------------------------- # PREPARATION JOB # -------------------------------------------------------------------------- prepare: executor: docker-executor steps: - checkout - run: name: Determine Test Strategy command: | if [[ "$CIRCLE_BRANCH" == "main" && "$CIRCLE_PULL_REQUEST" == "" ]]; then echo 'export TEST_SUITE="all"' >> $BASH_ENV echo 'export BROWSERS="chromium,firefox"' >> $BASH_ENV echo 'export DEPLOY_ENABLED="true"' >> $BASH_ENV elif [[ "$CIRCLE_PULL_REQUEST" != "" ]]; then echo 'export TEST_SUITE="unit,e2e"' >> $BASH_ENV echo 'export BROWSERS="chromium"' >> $BASH_ENV echo 'export DEPLOY_ENABLED="false"' >> $BASH_ENV else echo 'export TEST_SUITE="unit"' >> $BASH_ENV echo 'export BROWSERS="chromium"' >> $BASH_ENV echo 'export DEPLOY_ENABLED="false"' >> $BASH_ENV fi VERSION=$(if [[ "$CIRCLE_BRANCH" == "main" ]]; then echo "v1.0.$CIRCLE_BUILD_NUM"; else echo "$CIRCLE_BRANCH-$CIRCLE_BUILD_NUM"; fi) echo "export VERSION=\"$VERSION\"" >> $BASH_ENV source $BASH_ENV echo "Test Suite: $TEST_SUITE" echo "Browsers: $BROWSERS" echo "Deploy: $DEPLOY_ENABLED" echo "Version: $VERSION" - persist_to_workspace: root: . paths: - . # -------------------------------------------------------------------------- # SECURITY JOBS # -------------------------------------------------------------------------- security-scan: executor: node-executor steps: - attach_workspace: at: . - setup-dependencies - run: name: Security Audit command: | npm audit --prefix frontend --audit-level moderate || true npm audit --prefix backend --audit-level moderate || true - run: name: Install Trivy command: | sudo apt-get update sudo apt-get install wget apt-transport-https gnupg lsb-release wget -qO - https://aquasecurity.github.io/trivy-repo/deb/public.key | sudo apt-key add - echo "deb https://aquasecurity.github.io/trivy-repo/deb $(lsb_release -sc) main" | sudo tee -a /etc/apt/sources.list.d/trivy.list sudo apt-get update sudo apt-get install trivy - run: name: Container Security Scan command: | trivy fs --format json --output trivy-results.json . - store_artifacts: path: trivy-results.json destination: security-results # -------------------------------------------------------------------------- # TESTING JOBS # -------------------------------------------------------------------------- test-node18: docker: - image: cimg/node:18.19 - image: cimg/mongo:6.0 environment: MONGO_INITDB_ROOT_USERNAME: admin MONGO_INITDB_ROOT_PASSWORD: password - image: cimg/redis:7.0 working_directory: ~/project/free-mobile-chatbot environment: NODE_ENV: test MONGODB_URI: ********************************************************************** REDIS_URL: redis://localhost:6379 steps: - attach_workspace: at: ~/project - setup-dependencies - setup-playwright: browsers: chromium - start-services - run-automated-tests: test-suite: unit,e2e browsers: chromium - publish-test-results - codecov/upload: file: frontend/coverage/lcov.info flags: frontend-node18 test-node20: executor: node-executor steps: - attach_workspace: at: ~/project - setup-dependencies - setup-playwright: browsers: chromium,firefox - start-services - run-automated-tests: test-suite: << pipeline.parameters.test-suite >> browsers: << pipeline.parameters.browsers >> - publish-test-results - codecov/upload: file: frontend/coverage/lcov.info flags: frontend-node20 test-node22: docker: - image: cimg/node:22.0 - image: cimg/mongo:6.0 environment: MONGO_INITDB_ROOT_USERNAME: admin MONGO_INITDB_ROOT_PASSWORD: password - image: cimg/redis:7.0 working_directory: ~/project/free-mobile-chatbot environment: NODE_ENV: test MONGODB_URI: ********************************************************************** REDIS_URL: redis://localhost:6379 steps: - attach_workspace: at: ~/project - setup-dependencies - setup-playwright: browsers: chromium - start-services - run-automated-tests: test-suite: unit,e2e browsers: chromium - publish-test-results - codecov/upload: file: frontend/coverage/lcov.info flags: frontend-node22 # -------------------------------------------------------------------------- # PERFORMANCE JOB # -------------------------------------------------------------------------- performance-test: executor: node-executor steps: - attach_workspace: at: ~/project - setup-dependencies - setup-playwright: browsers: chromium - start-services - run: name: Run Performance Tests command: | ./scripts/automated-testing.sh --test-suite performance --verbose - run: name: Run Lighthouse CI command: | npm install -g @lhci/cli lhci autorun --config=lighthouserc.js - store_artifacts: path: reports/performance-results.json destination: performance-results - store_artifacts: path: .lighthouseci destination: lighthouse-results # -------------------------------------------------------------------------- # BUILD JOBS # -------------------------------------------------------------------------- build-frontend: executor: docker-executor steps: - attach_workspace: at: . - setup_remote_docker: version: 20.10.14 docker_layer_caching: true - docker/check: docker-username: DOCKER_USERNAME docker-password: DOCKER_PASSWORD - docker/build: image: $DOCKER_USERNAME/chatbot-rncp-frontend tag: $VERSION,latest dockerfile: free-mobile-chatbot/frontend/Dockerfile.prod path: free-mobile-chatbot/frontend - docker/push: image: $DOCKER_USERNAME/chatbot-rncp-frontend tag: $VERSION,latest build-backend: executor: docker-executor steps: - attach_workspace: at: . - setup_remote_docker: version: 20.10.14 docker_layer_caching: true - docker/check: docker-username: DOCKER_USERNAME docker-password: DOCKER_PASSWORD - docker/build: image: $DOCKER_USERNAME/chatbot-rncp-backend tag: $VERSION,latest dockerfile: free-mobile-chatbot/backend/Dockerfile.prod path: free-mobile-chatbot/backend - docker/push: image: $DOCKER_USERNAME/chatbot-rncp-backend tag: $VERSION,latest # -------------------------------------------------------------------------- # DEPLOYMENT JOBS # -------------------------------------------------------------------------- deploy-staging: executor: docker-executor steps: - attach_workspace: at: . - run: name: Deploy to Staging command: | echo "[DEPLOY] Deploying version $VERSION to staging" # Add your staging deployment logic here curl -f https://staging.your-domain.com/health - slack/notify: event: pass channel: '#deployments' template: success_tagged_deploy_1 deploy-production: executor: docker-executor steps: - attach_workspace: at: . - run: name: Deploy to Production command: | echo "[DEPLOY] Deploying version $VERSION to production" # Add your production deployment logic here curl -f https://your-domain.com/health - slack/notify: event: pass channel: '#deployments' template: success_tagged_deploy_1 # ============================================================================ # PARAMETERS # ============================================================================ parameters: test-suite: type: string default: "unit" browsers: type: string default: "chromium" # ============================================================================ # WORKFLOWS # ============================================================================ workflows: version: 2 # Main CI/CD workflow ci-cd: jobs: # Preparation - prepare # Security (parallel) - security-scan: requires: - prepare filters: branches: only: - main - develop - /^feature\/.*/ # Testing matrix (parallel) - test-node18: requires: - prepare filters: branches: only: main - test-node20: requires: - prepare - test-node22: requires: - prepare filters: branches: only: main # Performance testing - performance-test: requires: - test-node20 filters: branches: only: main # Build (parallel) - build-frontend: requires: - test-node20 filters: branches: only: - main - develop - build-backend: requires: - test-node20 filters: branches: only: - main - develop # Deployment - deploy-staging: requires: - build-frontend - build-backend - performance-test filters: branches: only: main - hold-for-approval: type: approval requires: - deploy-staging filters: branches: only: main - deploy-production: requires: - hold-for-approval filters: branches: only: main # Nightly workflow nightly: triggers: - schedule: cron: "0 2 * * *" filters: branches: only: main jobs: - prepare - test-node18: requires: - prepare - test-node20: requires: - prepare - test-node22: requires: - prepare - performance-test: requires: - test-node20