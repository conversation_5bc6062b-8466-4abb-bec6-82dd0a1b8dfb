# [CONFIG] CI/CD Troubleshooting Guide ## Overview This comprehensive troubleshooting guide helps resolve common issues encountered when implementing and running the ChatbotRNCP CI/CD pipeline across different platforms. ## Common Issues and Solutions ### 1. Environment Setup Issues #### Issue: Node.js Version Mismatch ```bash Error: The engine "node" is incompatible with this module ``` **Solution:** ```yaml # Ensure consistent Node.js version across all jobs - name: Setup Node.js uses: actions/setup-node@v4 with: node-version: '20.x' # Use specific version cache: 'npm' ``` **Prevention:** - Use `.nvmrc` file in repository root - Specify exact Node.js version in CI/CD configurations - Test locally with the same Node.js version used in CI/CD #### Issue: npm Cache Corruption ```bash Error: ENOENT: no such file or directory, open 'package-lock.json' ``` **Solution:** ```bash # Clear npm cache and reinstall npm cache clean --force rm -rf node_modules package-lock.json npm install ``` **CI/CD Fix:** ```yaml - name: Clear npm cache run: npm cache clean --force - name: Install dependencies run: | rm -rf node_modules package-lock.json npm ci --cache .npm --prefer-offline ``` ### 2. Docker and Service Issues #### Issue: Docker Compose Services Not Starting ```bash Error: Service 'mongodb' failed to build ``` **Diagnosis Steps:** ```bash # Check Docker daemon status docker info # Verify Docker Compose file syntax docker compose config # Check service logs docker compose logs mongodb # Test individual service docker run --rm mongo:6.0 mongod --version ``` **Solution:** ```yaml # Add service health checks services: mongodb: image: mongo:6.0 healthcheck: test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"] interval: 10s timeout: 5s retries: 5 start_period: 30s ``` #### Issue: Port Conflicts in CI/CD ```bash Error: Port 27017 is already in use ``` **Solution:** ```bash # Use dynamic ports in CI/CD export MONGODB_PORT=$(shuf -i 27018-27100 -n 1) export REDIS_PORT=$(shuf -i 6380-6400 -n 1) # Update connection strings export MONGODB_URI="mongodb://localhost:$MONGODB_PORT/chatbot_test" export REDIS_URL="redis://localhost:$REDIS_PORT" ``` ### 3. Test Execution Issues #### Issue: Playwright Browser Installation Fails ```bash Error: Failed to download Chromium ``` **Solution:** ```yaml # GitHub Actions - name: Install Playwright browsers run: | cd frontend npx playwright install chromium firefox webkit npx playwright install-deps # For restricted environments - name: Install Playwright (offline) run: | export PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=1 npm ci npx playwright install chromium --with-deps ``` #### Issue: Tests Timeout in CI/CD ```bash Error: Test timeout of 30000ms exceeded ``` **Solution:** ```javascript // playwright.config.js module.exports = { timeout: 60000, // Increase timeout for CI/CD expect: { timeout: 10000 }, use: { // Slower CI/CD environments actionTimeout: 15000, navigationTimeout: 30000 } }; ``` #### Issue: Flaky Tests in CI/CD ```bash Error: Test failed intermittently ``` **Solution:** ```javascript // Add retry logic for flaky tests test.describe('Flaky test suite', () => { test.beforeEach(async ({ page }) => { // Ensure clean state await page.goto('/'); await page.waitForLoadState('networkidle'); }); test('should handle flaky behavior', async ({ page }) => { // Add explicit waits await page.waitForSelector('[data-testid="element"]', { state: 'visible', timeout: 10000 }); // Use retry logic await expect(async () => { await page.click('[data-testid="button"]'); await expect(page.locator('[data-testid="result"]')).toBeVisible(); }).toPass({ timeout: 30000 }); }); }); ``` ### 4. Database and Backend Issues #### Issue: MongoDB Connection Refused ```bash Error: MongoNetworkError: connect ECONNREFUSED 127.0.0.1:27017 ``` **Diagnosis:** ```bash # Check MongoDB service status docker compose ps mongodb # Test connection manually mongosh mongodb://localhost:27017/chatbot_test # Check network connectivity telnet localhost 27017 ``` **Solution:** ```javascript // backend/config/database.js const mongoose = require('mongoose'); const connectDB = async (retries = 5) => { for (let i = 0; i < retries; i++) { try { await mongoose.connect(process.env.MONGODB_URI, { useNewUrlParser: true, useUnifiedTopology: true, serverSelectionTimeoutMS: 10000, socketTimeoutMS: 45000, }); console.log('[COMPLETE] MongoDB connected'); return; } catch (error) { console.log(`[FAILED] MongoDB connection attempt ${i + 1} failed:`, error.message); if (i === retries - 1) throw error; await new Promise(resolve => setTimeout(resolve, 5000)); } } }; ``` #### Issue: JWT Secret Not Set ```bash Error: JWT secret is required ``` **Solution:** ```bash # Set default for CI/CD environments export JWT_SECRET=${JWT_SECRET:-"ci-cd-test-secret-key-not-for-production"} # Validate in application if (!process.env.JWT_SECRET) { if (process.env.NODE_ENV === 'production') { throw new Error('JWT_SECRET is required in production'); } else { console.warn(' Using default JWT secret for testing'); process.env.JWT_SECRET = 'default-test-secret'; } } ``` ### 5. Performance and Resource Issues #### Issue: CI/CD Pipeline Runs Out of Memory ```bash Error: JavaScript heap out of memory ``` **Solution:** ```yaml # Increase Node.js memory limit - name: Run tests with increased memory run: | export NODE_OPTIONS="--max-old-space-size=4096" npm test env: NODE_OPTIONS: --max-old-space-size=4096 ``` #### Issue: Slow Test Execution ```bash Warning: Tests taking longer than expected ``` **Optimization:** ```javascript // jest.config.js module.exports = { // Run tests in parallel maxWorkers: '50%', // Cache test results cache: true, cacheDirectory: '.cache/jest', // Skip coverage for faster execution collectCoverage: process.env.CI !== 'true', // Optimize test patterns testPathIgnorePatterns: [ '/node_modules/', '/build/', '/dist/' ] }; ``` ### 6. Platform-Specific Issues #### GitHub Actions Issues **Issue: Workflow not triggering** ```yaml # Check trigger conditions on: push: branches: [ main, develop ] paths-ignore: - 'docs/**' - '*.md' pull_request: branches: [ main ] ``` **Issue: Secrets not accessible** ```yaml # Ensure secrets are properly referenced env: MONGODB_URI: ${{ secrets.MONGODB_URI }} JWT_SECRET: ${{ secrets.JWT_SECRET }} ``` #### Jenkins Issues **Issue: Pipeline script approval** ```groovy // Use @NonCPS for complex operations @NonCPS def parseJsonResponse(response) { return new groovy.json.JsonSlurper().parseText(response) } ``` **Issue: Docker permission denied** ```bash # Add jenkins user to docker group sudo usermod -aG docker jenkins sudo systemctl restart jenkins ``` #### Azure DevOps Issues **Issue: Variable group not found** ```yaml # Ensure variable group exists and is linked variables: - group: ChatbotRNCP-Variables # Must exist in Library ``` **Issue: Service connection authentication** ```bash # Verify service connection in Project Settings # Test connection before using in pipeline ``` ### 7. Security and Compliance Issues #### Issue: Security scan failures ```bash Error: High severity vulnerabilities found ``` **Solution:** ```bash # Update dependencies npm audit fix # For unfixable vulnerabilities, create exceptions npm audit --audit-level moderate # Use security-focused base images FROM node:20-alpine # More secure than full images ``` #### Issue: Secrets exposed in logs ```yaml # Mask sensitive values - name: Deploy application run: | echo "::add-mask::${{ secrets.DATABASE_PASSWORD }}" deploy.sh env: DB_PASSWORD: ${{ secrets.DATABASE_PASSWORD }} ``` ## [SEARCH] Debugging Techniques ### 1. Enable Debug Logging ```bash # Enable debug mode for various tools export DEBUG=* export NODE_ENV=development export PLAYWRIGHT_DEBUG=1 export JEST_VERBOSE=true ``` ### 2. Interactive Debugging ```yaml # GitHub Actions - Enable SSH debugging - name: Setup tmate session uses: mxschmitt/action-tmate@v3 if: failure() ``` ### 3. Artifact Collection ```yaml # Collect debugging artifacts - name: Upload debug artifacts uses: actions/upload-artifact@v3 if: failure() with: name: debug-artifacts path: | logs/ screenshots/ videos/ docker-compose.logs ``` ## [ANALYTICS] Monitoring and Alerting ### Health Check Script ```bash #!/bin/bash # ci-health-check.sh check_service() { local service=$1 local port=$2 local path=${3:-"/health"} if curl -f -s "http://localhost:$port$path" > /dev/null; then echo "[COMPLETE] $service is healthy" return 0 else echo "[FAILED] $service is unhealthy" return 1 fi } # Check all services FAILED=0 check_service "Frontend" 3001 "/" || FAILED=1 check_service "Backend" 5000 "/health" || FAILED=1 check_service "MongoDB" 27017 "/" || FAILED=1 check_service "Redis" 6379 "/" || FAILED=1 if [ $FAILED -eq 1 ]; then echo " Some services are unhealthy" exit 1 else echo " All services are healthy" exit 0 fi ``` ### Performance Monitoring ```javascript // performance-monitor.js const { performance } = require('perf_hooks'); class PerformanceMonitor { constructor() { this.metrics = {}; } startTimer(name) { this.metrics[name] = performance.now(); } endTimer(name) { if (this.metrics[name]) { const duration = performance.now() - this.metrics[name]; console.log(`⏱ ${name}: ${duration.toFixed(2)}ms`); return duration; } } checkMemoryUsage() { const usage = process.memoryUsage(); console.log(' Memory Usage:', { rss: `${Math.round(usage.rss / 1024 / 1024)}MB`, heapTotal: `${Math.round(usage.heapTotal / 1024 / 1024)}MB`, heapUsed: `${Math.round(usage.heapUsed / 1024 / 1024)}MB` }); } } module.exports = PerformanceMonitor; ``` ## [DEPLOY] Best Practices for Prevention ### 1. Pre-commit Hooks ```bash # .husky/pre-commit #!/bin/sh . "$(dirname "$0")/_/husky.sh" # Run linting npm run lint # Run type checking npm run type-check # Run unit tests npm run test:unit # Check for security vulnerabilities npm audit --audit-level moderate ``` ### 2. Local CI/CD Testing ```bash # Test CI/CD pipeline locally using act (GitHub Actions) act -j test # Test with Docker Compose docker compose -f docker-compose.ci.yml up --abort-on-container-exit # Validate pipeline configuration yamllint .github/workflows/ci-cd.yml ``` ### 3. Gradual Rollout Strategy ```yaml # Implement feature flags for gradual rollout deploy: script: - | # Deploy with feature flag disabled kubectl set env deployment/chatbot-app FEATURE_NEW_CHAT=false # Monitor for 10 minutes sleep 600 # Enable feature for 10% of users kubectl set env deployment/chatbot-app FEATURE_NEW_CHAT=10 ``` This troubleshooting guide covers the most common issues and provides practical solutions for maintaining a robust CI/CD pipeline for the ChatbotRNCP application.