# CI/CD Implementation Examples ## Overview This document provides practical implementation examples for integrating the ChatbotRNCP automated testing system with various CI/CD platforms. Each example includes complete configuration files, setup instructions, and best practices. ## [TARGET] GitHub Actions Implementation ### Complete Setup Process 1. **Create the workflow directory**: ```bash mkdir -p .github/workflows cp ci-cd/github-actions.yml .github/workflows/ci-cd.yml ``` 2. **Configure repository secrets**: ```bash # Navigate to Settings > Secrets and variables > Actions # Add the following secrets: MONGODB_URI=mongodb://localhost:27017/chatbot_test JWT_SECRET=your-super-secret-jwt-key-for-testing REDIS_URL=redis://localhost:6379 DOCKER_REGISTRY_TOKEN=your-docker-registry-token SLACK_WEBHOOK=your-slack-webhook-url CODECOV_TOKEN=your-codecov-token ``` 3. **Customize the workflow**: ```yaml # .github/workflows/ci-cd.yml name: [DEPLOY] ChatbotRNCP CI/CD Pipeline on: push: branches: [ main, develop, feature/* ] pull_request: branches: [ main, develop ] env: NODE_VERSION_DEFAULT: '20.x' DOCKER_REGISTRY: ghcr.io IMAGE_NAME: chatbot-rncp jobs: test: runs-on: ubuntu-latest strategy: matrix: node-version: [18.x, 20.x, 22.x] test-suite: [unit, e2e, integration] browser: [chromium, firefox] steps: - uses: actions/checkout@v4 - uses: actions/setup-node@v4 with: node-version: ${{ matrix.node-version }} cache: 'npm' - name: Run Tests run: | cd free-mobile-chatbot ./scripts/automated-testing.sh \ --test-suite ${{ matrix.test-suite }} \ --browser ${{ matrix.browser }} ``` ### Advanced GitHub Actions Features #### Matrix Strategy with Exclusions ```yaml strategy: matrix: os: [ubuntu-latest, windows-latest, macos-latest] node-version: [18.x, 20.x, 22.x] exclude: # Exclude macOS with older Node versions for cost optimization - os: macos-latest node-version: 18.x ``` #### Conditional Job Execution ```yaml jobs: deploy: if: github.ref == 'refs/heads/main' && github.event_name == 'push' needs: [test, security-scan] runs-on: ubuntu-latest ``` #### Artifact Management ```yaml - name: Upload Test Results uses: actions/upload-artifact@v3 if: always() with: name: test-results-${{ matrix.os }}-${{ matrix.node-version }} path: | free-mobile-chatbot/reports/ free-mobile-chatbot/frontend/coverage/ retention-days: 30 ``` ## [ARCHITECTURE] Jenkins Implementation ### Jenkinsfile Setup 1. **Create Jenkinsfile in repository root**: ```bash cp ci-cd/Jenkinsfile ./Jenkinsfile ``` 2. **Configure Jenkins credentials**: ```groovy // In Jenkins: Manage Jenkins > Manage Credentials // Add the following credentials: // - docker-registry-credentials (Username/Password) // - mongodb-uri (Secret text) // - jwt-secret (Secret text) // - slack-webhook (Secret text) ``` 3. **Install required Jenkins plugins**: ```bash # Required plugins: # - Docker Pipeline # - Pipeline: Stage View # - Blue Ocean (optional) # - Slack Notification # - HTML Publisher # - JUnit ``` ### Advanced Jenkins Features #### Parallel Execution ```groovy stage('Testing Matrix') { parallel { stage('Linux Tests') { agent { label 'linux' } steps { runTestSuite('linux') } } stage('Windows Tests') { agent { label 'windows' } steps { runTestSuite('windows') } } } } ``` #### Custom Functions ```groovy def runTestSuite(platform) { dir('free-mobile-chatbot') { if (platform == 'windows') { bat ''' powershell -ExecutionPolicy Bypass -File scripts\\automated-testing.ps1 ''' } else { sh ''' chmod +x scripts/automated-testing.sh ./scripts/automated-testing.sh --test-suite all ''' } } } ``` #### Pipeline Approval Gates ```groovy stage('Deploy to Production') { when { branch 'main' } steps { input message: 'Deploy to Production?', ok: 'Deploy', submitterParameter: 'DEPLOYER' script { deployToEnvironment('production') } } } ``` ## Azure DevOps Implementation ### Pipeline Setup 1. **Create azure-pipelines.yml**: ```bash cp ci-cd/azure-pipelines.yml ./azure-pipelines.yml ``` 2. **Configure variable groups**: ```yaml # In Azure DevOps: Pipelines > Library > Variable groups # Create "ChatbotRNCP-Variables" with: variables: - group: ChatbotRNCP-Variables - name: nodeVersion value: '20.x' - name: dockerRegistry value: 'your-registry.azurecr.io' ``` 3. **Set up service connections**: ```bash # In Azure DevOps: Project Settings > Service connections # Create connections for: # - Azure Container Registry # - Docker Registry # - Kubernetes (if using AKS) ``` ### Advanced Azure DevOps Features #### Multi-stage Pipeline ```yaml stages: - stage: Build displayName: 'Build Stage' jobs: - job: BuildJob displayName: 'Build Application' - stage: Test displayName: 'Test Stage' dependsOn: Build jobs: - job: TestJob displayName: 'Run Tests' - stage: Deploy displayName: 'Deploy Stage' dependsOn: Test condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main')) ``` #### Template Usage ```yaml # templates/test-template.yml parameters: - name: nodeVersion type: string - name: testSuite type: string steps: - task: NodeTool@0 inputs: versionSpec: ${{ parameters.nodeVersion }} - script: | ./scripts/automated-testing.sh --test-suite ${{ parameters.testSuite }} ``` ## GitLab CI Implementation ### .gitlab-ci.yml Setup 1. **Create GitLab CI configuration**: ```bash cp ci-cd/gitlab-ci.yml ./.gitlab-ci.yml ``` 2. **Configure CI/CD variables**: ```yaml # In GitLab: Settings > CI/CD > Variables # Add the following variables: MONGODB_URI: mongodb://localhost:27017/chatbot_test JWT_SECRET: your-super-secret-jwt-key-for-testing DOCKER_REGISTRY: $CI_REGISTRY SLACK_WEBHOOK: your-slack-webhook-url ``` ### Advanced GitLab CI Features #### Dynamic Child Pipelines ```yaml generate-pipeline: stage: prepare script: - | cat > generated-pipeline.yml << EOF test-dynamic: stage: test script: - echo "Dynamic pipeline generated" EOF artifacts: paths: - generated-pipeline.yml trigger-child: stage: test trigger: include: - artifact: generated-pipeline.yml job: generate-pipeline ``` #### Rules and Conditions ```yaml test:unit: script: - npm test rules: - if: $CI_COMMIT_REF_NAME == "main" - if: $CI_PIPELINE_SOURCE == "merge_request_event" - changes: - "frontend/**/*" - "backend/**/*" ``` ## ⭕ CircleCI Implementation ### .circleci/config.yml Setup 1. **Create CircleCI configuration**: ```bash mkdir -p .circleci cp ci-cd/circleci-config.yml ./.circleci/config.yml ``` 2. **Configure environment variables**: ```bash # In CircleCI: Project Settings > Environment Variables # Add the following variables: MONGODB_URI=mongodb://localhost:27017/chatbot_test JWT_SECRET=your-super-secret-jwt-key-for-testing DOCKER_USERNAME=your-docker-username DOCKER_PASSWORD=your-docker-password ``` ### Advanced CircleCI Features #### Orb Usage ```yaml orbs: node: circleci/node@5.1.0 docker: circleci/docker@2.4.0 slack: circleci/slack@4.12.1 jobs: test: executor: node/default steps: - node/install-packages - run: npm test - slack/notify: event: fail template: basic_fail_1 ``` #### Workflow Orchestration ```yaml workflows: version: 2 build-test-deploy: jobs: - build - test: requires: - build - deploy: requires: - test filters: branches: only: main ``` ## [CONFIG] Integration with Existing Setup ### Leveraging setup-MCP.ps1 All CI/CD implementations can leverage the existing `setup-MCP.ps1` script: ```powershell # CI/CD wrapper for setup-MCP.ps1 function Initialize-CIEnvironment { param( [string]$Environment = "test", [string]$Platform = "generic" ) # Set CI-specific environment variables $env:CI = "true" $env:NODE_ENV = $Environment $env:CI_PLATFORM = $Platform # Run existing setup script & .\setup-MCP.ps1 # Additional CI-specific setup Write-Host "[CONFIG] CI/CD environment initialized for $Platform" -ForegroundColor Green } ``` ### Database Integration ```javascript // Enhanced ci-database-setup.js const { MongoClient } = require('mongodb'); class CIDatabaseSetup { constructor() { this.uri = process.env.MONGODB_URI; this.dbName = process.env.DB_NAME || 'chatbot_test'; } async setup() { const client = new MongoClient(this.uri); try { await client.connect(); const db = client.db(this.dbName); // Create collections with proper indexes await this.createCollections(db); await this.seedTestData(db); await this.createIndexes(db); console.log('[COMPLETE] CI database setup complete'); } finally { await client.close(); } } async createCollections(db) { const collections = ['users', 'tickets', 'messages', 'analytics']; for (const collection of collections) { try { await db.createCollection(collection); console.log(`Created collection: ${collection}`); } catch (error) { // Collection might already exist console.log(`Collection ${collection} already exists`); } } } async seedTestData(db) { // Seed with test data for CI/CD const testUsers = [ { email: '<EMAIL>', role: 'admin', password: '$2b$10$hashedPassword', createdAt: new Date() }, { email: '<EMAIL>', role: 'agent', password: '$2b$10$hashedPassword', createdAt: new Date() } ]; await db.collection('users').insertMany(testUsers); console.log('[COMPLETE] Test data seeded'); } async createIndexes(db) { // Create performance indexes await db.collection('users').createIndex({ email: 1 }, { unique: true }); await db.collection('tickets').createIndex({ ticketNumber: 1 }, { unique: true }); await db.collection('messages').createIndex({ createdAt: -1 }); console.log('[COMPLETE] Database indexes created'); } } module.exports = CIDatabaseSetup; ``` ## [DEPLOY] Deployment Strategies ### Blue-Green Deployment ```yaml deploy-blue-green: script: - | # Deploy to blue environment kubectl apply -f k8s/blue-deployment.yml # Wait for blue to be ready kubectl wait --for=condition=available deployment/chatbot-blue # Run smoke tests on blue curl -f https://blue.your-domain.com/health # Switch traffic to blue kubectl patch service chatbot-service -p '{"spec":{"selector":{"version":"blue"}}}' # Cleanup green environment kubectl delete deployment chatbot-green ``` ### Canary Deployment ```yaml deploy-canary: script: - | # Deploy canary version (10% traffic) kubectl apply -f k8s/canary-deployment.yml # Monitor metrics for 5 minutes sleep 300 # Check error rate ERROR_RATE=$(curl -s https://monitoring.your-domain.com/api/error-rate) if [ "$ERROR_RATE" -lt "1" ]; then # Full rollout kubectl apply -f k8s/production-deployment.yml else # Rollback kubectl delete deployment chatbot-canary exit 1 fi ``` ### Rolling Deployment ```yaml deploy-rolling: script: - | # Update deployment with rolling strategy kubectl set image deployment/chatbot-app \ frontend=your-registry.com/chatbot-frontend:$VERSION \ backend=your-registry.com/chatbot-backend:$VERSION # Wait for rollout to complete kubectl rollout status deployment/chatbot-app # Verify deployment kubectl get pods -l app=chatbot-app ``` ## [ANALYTICS] Monitoring and Alerting ### Health Check Integration ```bash #!/bin/bash # health-check.sh SERVICES=("frontend:3001" "backend:5000" "mongodb:27017" "redis:6379") FAILED_SERVICES=() for service in "${SERVICES[@]}"; do IFS=':' read -r name port <<< "$service" if ! curl -f "http://localhost:$port/health" &>/dev/null; then FAILED_SERVICES+=("$name") fi done if [ ${#FAILED_SERVICES[@]} -gt 0 ]; then echo "[FAILED] Failed services: ${FAILED_SERVICES[*]}" exit 1 else echo "[COMPLETE] All services healthy" exit 0 fi ``` ### Slack Integration ```bash #!/bin/bash # slack-notify.sh send_slack_notification() { local status=$1 local message=$2 local color=$3 curl -X POST -H 'Content-type: application/json' \ --data "{ \"attachments\": [{ \"color\": \"$color\", \"title\": \"ChatbotRNCP CI/CD\", \"text\": \"$message\", \"fields\": [ {\"title\": \"Branch\", \"value\": \"$CI_BRANCH\", \"short\": true}, {\"title\": \"Commit\", \"value\": \"$CI_COMMIT\", \"short\": true} ] }] }" \ $SLACK_WEBHOOK } # Usage examples: # send_slack_notification "success" "[COMPLETE] Deployment successful!" "good" # send_slack_notification "failure" "[FAILED] Tests failed!" "danger" # send_slack_notification "warning" " Performance degraded!" "warning" ``` This comprehensive implementation guide provides everything needed to successfully integrate the ChatbotRNCP automated testing system with any major CI/CD platform.