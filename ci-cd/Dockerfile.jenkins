# Jenkins CI/CD Docker Image for ChatbotRNCP
# This Dockerfile creates a custom Jenkins agent with all required tools

FROM node:20-bullseye

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV NODE_ENV=test
ENV CI=true

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    git \
    python3 \
    python3-pip \
    build-essential \
    apt-transport-https \
    ca-certificates \
    gnupg \
    lsb-release \
    jq \
    unzip \
    && rm -rf /var/lib/apt/lists/*

# Install Docker CLI
RUN curl -fsSL https://download.docker.com/linux/debian/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg \
    && echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/debian $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null \
    && apt-get update \
    && apt-get install -y docker-ce-cli \
    && rm -rf /var/lib/apt/lists/*

# Install Docker Compose
RUN curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose \
    && chmod +x /usr/local/bin/docker-compose

# Install Trivy security scanner
RUN wget -qO - https://aquasecurity.github.io/trivy-repo/deb/public.key | apt-key add - \
    && echo "deb https://aquasecurity.github.io/trivy-repo/deb $(lsb_release -sc) main" | tee -a /etc/apt/sources.list.d/trivy.list \
    && apt-get update \
    && apt-get install -y trivy \
    && rm -rf /var/lib/apt/lists/*

# Install Lighthouse CI
RUN npm install -g @lhci/cli

# Install global npm packages for testing
RUN npm install -g \
    jest \
    playwright \
    @playwright/test \
    eslint \
    typescript

# Create jenkins user
RUN useradd -m -s /bin/bash jenkins \
    && usermod -aG docker jenkins

# Set working directory
WORKDIR /workspace

# Switch to jenkins user
USER jenkins

# Install Playwright browsers
RUN npx playwright install chromium firefox webkit

# Create directories for test results
RUN mkdir -p /home/<USER>/test-results \
    && mkdir -p /home/<USER>/coverage \
    && mkdir -p /home/<USER>/reports

# Set default command
CMD ["/bin/bash"]
