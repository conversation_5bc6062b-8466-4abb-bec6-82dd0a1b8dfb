// CI/CD Environment Setup Script for ChatbotRNCP // This script prepares the CI/CD environment with necessary configurations const fs = require('fs'); const path = require('path'); const { execSync } = require('child_process'); class CIEnvironmentSetup { constructor(options = {}) { this.platform = options.platform || process.env.CI_PLATFORM || 'generic'; this.environment = options.environment || process.env.NODE_ENV || 'test'; this.projectRoot = options.projectRoot || process.cwd(); this.verbose = options.verbose || false; } async setup() { console.log(`[CONFIG] Setting up CI/CD environment for ${this.platform}`); try { await this.detectPlatform(); await this.setupEnvironmentVariables(); await this.setupTestDatabase(); await this.setupCacheDirectories(); await this.setupReportDirectories(); await this.validateSetup(); console.log('[COMPLETE] CI/CD environment setup complete'); return true; } catch (error) { console.error('[FAILED] CI/CD environment setup failed:', error.message); return false; } } async detectPlatform() { const platforms = { 'GITHUB_ACTIONS': 'github-actions', 'JENKINS_URL': 'jenkins', 'AZURE_HTTP_USER_AGENT': 'azure-devops', 'GITLAB_CI': 'gitlab-ci', 'CIRCLECI': 'circleci' }; for (const [envVar, platform] of Object.entries(platforms)) { if (process.env[envVar]) { this.platform = platform; break; } } this.log(`Detected platform: ${this.platform}`); } async setupEnvironmentVariables() { this.log('Setting up environment variables...'); const defaultEnvVars = { NODE_ENV: this.environment, CI: 'true', FORCE_COLOR: '1', DISABLE_ESLINT_PLUGIN: 'true', GENERATE_SOURCEMAP: 'false', MONGODB_URI: process.env.MONGODB_URI || 'mongodb://localhost:27017/chatbot_test', REDIS_URL: process.env.REDIS_URL || 'redis://localhost:6379', JWT_SECRET: process.env.JWT_SECRET || 'test-jwt-secret-for-ci', FRONTEND_URL: process.env.FRONTEND_URL || 'http://localhost:3001', BACKEND_URL: process.env.BACKEND_URL || 'http://localhost:5000' }; // Platform-specific environment variables const platformEnvVars = this.getPlatformSpecificEnvVars(); const envVars = { ...defaultEnvVars, ...platformEnvVars }; for (const [key, value] of Object.entries(envVars)) { if (!process.env[key]) { process.env[key] = value; this.log(`Set ${key}=${value}`); } } } getPlatformSpecificEnvVars() { switch (this.platform) { case 'github-actions': return { GITHUB_WORKSPACE: process.env.GITHUB_WORKSPACE || process.cwd(), RUNNER_TEMP: process.env.RUNNER_TEMP || '/tmp' }; case 'jenkins': return { WORKSPACE: process.env.WORKSPACE || process.cwd(), BUILD_NUMBER: process.env.BUILD_NUMBER || '1' }; case 'azure-devops': return { AGENT_BUILDDIRECTORY: process.env.AGENT_BUILDDIRECTORY || process.cwd(), BUILD_BUILDNUMBER: process.env.BUILD_BUILDNUMBER || '1' }; case 'gitlab-ci': return { CI_PROJECT_DIR: process.env.CI_PROJECT_DIR || process.cwd(), CI_PIPELINE_ID: process.env.CI_PIPELINE_ID || '1' }; case 'circleci': return { CIRCLE_WORKING_DIRECTORY: process.env.CIRCLE_WORKING_DIRECTORY || process.cwd(), CIRCLE_BUILD_NUM: process.env.CIRCLE_BUILD_NUM || '1' }; default: return {}; } } async setupTestDatabase() { this.log('Setting up test database...'); const dbSetupScript = path.join(__dirname, '..', 'scripts', 'ci-database-setup.js'); if (fs.existsSync(dbSetupScript)) { try { execSync(`node ${dbSetupScript}`, { stdio: this.verbose ? 'inherit' : 'pipe', cwd: this.projectRoot }); this.log('[COMPLETE] Test database setup complete'); } catch (error) { this.log(' Test database setup failed, continuing...'); } } else { this.log(' Database setup script not found, skipping...'); } } async setupCacheDirectories() { this.log('Setting up cache directories...'); const cacheDirectories = [ '.npm', 'node_modules/.cache', '.cache/ms-playwright', '.cache/jest', '.cache/eslint' ]; for (const dir of cacheDirectories) { const fullPath = path.join(this.projectRoot, dir); if (!fs.existsSync(fullPath)) { fs.mkdirSync(fullPath, { recursive: true }); this.log(`Created cache directory: ${dir}`); } } } async setupReportDirectories() { this.log('Setting up report directories...'); const reportDirectories = [ 'reports', 'frontend/test-results', 'frontend/coverage', 'backend/test-results', 'backend/coverage', '.lighthouseci' ]; for (const dir of reportDirectories) { const fullPath = path.join(this.projectRoot, dir); if (!fs.existsSync(fullPath)) { fs.mkdirSync(fullPath, { recursive: true }); this.log(`Created report directory: ${dir}`); } } } async validateSetup() { this.log('Validating CI/CD setup...'); const validations = [ this.validateNodeVersion(), this.validateNpmCache(), this.validateEnvironmentVariables(), this.validateDirectories() ]; const results = await Promise.allSettled(validations); const failures = results.filter(result => result.status === 'rejected'); if (failures.length > 0) { throw new Error(`Validation failed: ${failures.map(f => f.reason).join(', ')}`); } this.log('[COMPLETE] All validations passed'); } validateNodeVersion() { const nodeVersion = process.version; const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]); if (majorVersion < 18) { throw new Error(`Node.js version ${nodeVersion} is not supported. Minimum version: 18.x`); } this.log(`[COMPLETE] Node.js version: ${nodeVersion}`); return true; } validateNpmCache() { try { execSync('npm config get cache', { stdio: 'pipe' }); this.log('[COMPLETE] npm cache configured'); return true; } catch (error) { throw new Error('npm cache not properly configured'); } } validateEnvironmentVariables() { const requiredVars = ['NODE_ENV', 'CI', 'MONGODB_URI']; const missing = requiredVars.filter(varName => !process.env[varName]); if (missing.length > 0) { throw new Error(`Missing required environment variables: ${missing.join(', ')}`); } this.log('[COMPLETE] Required environment variables present'); return true; } validateDirectories() { const requiredDirs = ['reports', 'frontend', 'backend']; const missing = requiredDirs.filter(dir => !fs.existsSync(path.join(this.projectRoot, dir)) ); if (missing.length > 0) { throw new Error(`Missing required directories: ${missing.join(', ')}`); } this.log('[COMPLETE] Required directories present'); return true; } log(message) { if (this.verbose) { console.log(`[CI-Setup] ${message}`); } } // Static method for easy CLI usage static async run(options = {}) { const setup = new CIEnvironmentSetup(options); return await setup.setup(); } } // CLI usage if (require.main === module) { const args = process.argv.slice(2); const options = { verbose: args.includes('--verbose') || args.includes('-v'), platform: args.find(arg => arg.startsWith('--platform='))?.split('=')[1], environment: args.find(arg => arg.startsWith('--env='))?.split('=')[1] }; CIEnvironmentSetup.run(options) .then(success => { process.exit(success ? 0 : 1); }) .catch(error => { console.error('Setup failed:', error); process.exit(1); }); } module.exports = CIEnvironmentSetup;