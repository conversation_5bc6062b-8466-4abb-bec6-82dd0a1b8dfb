{"version": 2, "alias": ["chatbotrncp.vercel.app"], "regions": ["cdg1", "fra1"], "builds": [{"src": "frontend/package.json", "use": "@vercel/static-build", "config": {"distDir": "build"}}, {"src": "backend/api/**/*.js", "use": "@vercel/node"}], "build": {"env": {"NODE_ENV": "production", "CI": "false", "GENERATE_SOURCEMAP": "false"}}, "env": {"NODE_ENV": "production", "MONGODB_URI": "@mongodb-uri", "DATABASE_NAME": "@database-name", "JWT_SECRET": "@jwt-secret", "SESSION_SECRET": "@session-secret", "OPENAI_API_KEY": "@openai-api-key", "SENTRY_DSN": "@sentry-dsn", "FRONTEND_URL": "https://chatbotrncp.vercel.app", "ALLOWED_ORIGINS": "https://chatbotrncp.vercel.app"}, "routes": [{"src": "/api/(.*)", "dest": "/backend/api/$1"}, {"src": "/dashboard/ai-suggestions", "dest": "/frontend/build/index.html"}, {"src": "/dashboard/(.*)", "dest": "/frontend/build/index.html"}, {"src": "/static/(.*)", "dest": "/frontend/build/static/$1"}, {"src": "/(.*\\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot))", "dest": "/frontend/build/$1"}, {"src": "/(.*)", "dest": "/frontend/build/index.html"}], "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "https://chatbotrncp.vercel.app"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, X-Requested-With"}, {"key": "Access-Control-Max-Age", "value": "86400"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/(.*)", "headers": [{"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload"}, {"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' https://cdn.jsdelivr.net https://unpkg.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.openai.com https://chatbotrncp.za6xmim.mongodb.net; frame-ancestors 'none';"}]}], "redirects": [{"source": "/", "destination": "/dashboard", "permanent": false}, {"source": "/admin", "destination": "/dashboard/admin", "permanent": false}], "rewrites": [{"source": "/api/health", "destination": "/api/system/health"}, {"source": "/api/status", "destination": "/api/system/status"}], "crons": [{"path": "/api/cron/cleanup", "schedule": "0 2 * * *"}, {"path": "/api/cron/analytics", "schedule": "*/15 * * * *"}, {"path": "/api/cron/ml-predictions", "schedule": "*/30 * * * *"}, {"path": "/api/cron/health-check", "schedule": "*/5 * * * *"}], "github": {"enabled": true, "autoAlias": true, "autoJobCancelation": true}, "buildCommand": "cd frontend && npm ci && npm run build", "devCommand": "cd frontend && npm start", "installCommand": "cd frontend && npm ci", "outputDirectory": "frontend/build", "framework": null}