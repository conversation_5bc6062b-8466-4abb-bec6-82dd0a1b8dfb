version: "3.1" rules: - rule: Say goodbye anytime the user says goodbye steps: - intent: goodbye - action: utter_goodbye - rule: Say 'I am a bot' anytime the user challenges steps: - intent: bot_challenge - action: utter_iamabot - rule: Activate fallback steps: - intent: nlu_fallback - action: utter_default_fallback - rule: Always greet steps: - intent: greet - action: utter_greet - rule: Handle billing issues steps: - intent: report_billing_issue - action: utter_billing_issue - rule: Handle network issues steps: - intent: report_network_issue - action: utter_network_issue - rule: Provide balance info steps: - intent: check_balance - action: utter_check_balance - rule: Provide invoice info steps: - intent: check_invoice - action: utter_check_invoice - rule: Escalate complex requests condition: - or: - intent: cancel_subscription - intent: ask_refund - intent: complain - intent: ask_support steps: - action: utter_escalate_human