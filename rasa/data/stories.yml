version: "3.1" stories: - story: greet and check balance steps: - intent: greet - action: utter_greet - intent: check_balance - action: utter_check_balance - story: greet and check invoice steps: - intent: greet - action: utter_greet - intent: check_invoice - action: utter_check_invoice - story: network issue steps: - intent: report_network_issue - action: utter_network_issue - intent: deny - action: utter_escalate_human - story: billing issue escalation steps: - intent: report_billing_issue - action: utter_billing_issue - intent: affirm - action: utter_escalate_human - story: simple goodbye steps: - intent: goodbye - action: utter_goodbye - story: bot challenge steps: - intent: bot_challenge - action: utter_iamabot - story: complex support request steps: - intent: greet - action: utter_greet - intent: ask_support - action: utter_escalate_human - story: technical help steps: - intent: ask_technical_help - action: utter_network_issue - intent: deny - action: utter_escalate_human - story: fallback story steps: - intent: nlu_fallback - action: utter_default_fallback - story: cancellation request steps: - intent: cancel_subscription - action: utter_escalate_human - story: refund request steps: - intent: ask_refund - action: utter_escalate_human - story: complaint steps: - intent: complain - action: utter_escalate_human