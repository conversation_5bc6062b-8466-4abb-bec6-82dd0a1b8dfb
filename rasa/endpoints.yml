# Rasa Endpoints Configuration # Configuration des endpoints pour actions et intégrations # Action Server Configuration action_endpoint: url: "http://localhost:5055/webhook" # Tracker Store - Redis pour persistence des conversations tracker_store: type: redis url: "redis://redis:6379" key_prefix: "rasa" record_exp: 30000 # Lock Store - Redis pour gestion des verrous lock_store: type: redis url: "redis://redis:6379" key_prefix: "rasa_lock" # Event Broker - Pour streaming des événements (optionnel) # event_broker: # type: pika # url: "amqp://guest:guest@rabbitmq:5672" # username: "guest" # password: "guest" # queue: "rasa_core_events" # Model Server (pour modèles distants - optionnel) # model_server: # url: "http://model-server:5000" # token: "your_model_server_token" # wait_time_between_pulls: 10