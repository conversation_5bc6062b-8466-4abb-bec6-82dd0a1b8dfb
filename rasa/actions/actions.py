# 🤖 Actions personnalisées pour Rasa - Chatbot Free Mobile

from typing import Any, Text, Dict, List
from rasa_sdk import Action, Tracker
from rasa_sdk.executor import CollectingDispatcher
from rasa_sdk.events import SlotSet
import requests
import logging

logger = logging.getLogger(__name__)

class ActionCheckBalance(Action):
    """Action pour vérifier le solde du compte client"""

    def name(self) -> Text:
        return "action_check_balance"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        
        # Simuler l'appel à l'API backend pour récupérer le solde
        try:
            customer_id = tracker.get_slot("customer_id")
            if not customer_id:
                dispatcher.utter_message(text="Je ne trouve pas votre numéro client. Pourriez-vous me le donner ?")
                return []

            # Simulation d'un appel API
            balance = "25.50"  # Simulation
            
            dispatcher.utter_message(
                text=f"Votre solde actuel est de {balance}€. Souhaitez-vous recharger votre compte ?"
            )
            
            return [SlotSet("balance", balance)]
            
        except Exception as e:
            logger.error(f"Erreur lors de la vérification du solde: {e}")
            dispatcher.utter_message(
                text="Désolé, je ne peux pas accéder à votre solde en ce moment. Veuillez réessayer plus tard."
            )
            return []

class ActionCheckUsage(Action):
    """Action pour vérifier la consommation (data, appels, SMS)"""

    def name(self) -> Text:
        return "action_check_usage"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        
        try:
            customer_id = tracker.get_slot("customer_id")
            if not customer_id:
                dispatcher.utter_message(text="Je ne trouve pas votre numéro client. Pourriez-vous me le donner ?")
                return []

            # Simulation des données de consommation
            usage_data = {
                "data": "5.2 Go sur 50 Go",
                "calls": "120 min sur illimité",
                "sms": "45 SMS sur illimité"
            }
            
            message = f"""📊 Voici votre consommation du mois :
            
📶 Données mobiles : {usage_data['data']}
📞 Appels : {usage_data['calls']}
💬 SMS : {usage_data['sms']}

Avez-vous d'autres questions sur votre forfait ?"""
            
            dispatcher.utter_message(text=message)
            
            return [
                SlotSet("data_usage", usage_data['data']),
                SlotSet("calls_usage", usage_data['calls']),
                SlotSet("sms_usage", usage_data['sms'])
            ]
            
        except Exception as e:
            logger.error(f"Erreur lors de la vérification de la consommation: {e}")
            dispatcher.utter_message(
                text="Désolé, je ne peux pas accéder à vos données de consommation en ce moment."
            )
            return []

class ActionReportProblem(Action):
    """Action pour signaler un problème technique"""

    def name(self) -> Text:
        return "action_report_problem"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        
        try:
            problem_type = tracker.get_slot("problem_type")
            
            if problem_type == "network":
                message = """🔧 Pour les problèmes de réseau :
                
1. Redémarrez votre téléphone
2. Vérifiez que vous êtes en zone couverte
3. Désactivez/réactivez les données mobiles

Si le problème persiste, je peux vous mettre en relation avec un technicien."""
                
            elif problem_type == "billing":
                message = """💰 Pour les questions de facturation :
                
Je peux vous aider avec :
- Explication de votre facture
- Contestation d'un montant
- Mise en place d'un échéancier

Que souhaitez-vous faire ?"""
                
            else:
                message = """🛠️ Je peux vous aider avec :
                
📶 Problèmes de réseau
💰 Questions de facturation  
📱 Configuration d'appareil
🔧 Problèmes techniques

De quel type de problème s'agit-il ?"""
            
            dispatcher.utter_message(text=message)
            
            return [SlotSet("support_step", "problem_identified")]
            
        except Exception as e:
            logger.error(f"Erreur lors du signalement de problème: {e}")
            dispatcher.utter_message(
                text="Je vais vous mettre en relation avec un agent qui pourra mieux vous aider."
            )
            return []

class ActionEscalateToAgent(Action):
    """Action pour escalader vers un agent humain"""

    def name(self) -> Text:
        return "action_escalate_to_agent"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        
        try:
            reason = tracker.get_slot("escalation_reason") or "demande du client"
            
            # Simulation de l'escalade vers un agent
            dispatcher.utter_message(
                text=f"""👨‍💼 Je vous mets en relation avec un de nos agents.

**Motif :** {reason}
**Temps d'attente estimé :** 2-3 minutes

Un agent va prendre le relais dans quelques instants. Merci de votre patience !"""
            )
            
            # Ici on pourrait déclencher l'escalade via webhook vers le backend
            
            return [
                SlotSet("escalated", True),
                SlotSet("escalation_reason", reason)
            ]
            
        except Exception as e:
            logger.error(f"Erreur lors de l'escalade: {e}")
            dispatcher.utter_message(
                text="Une erreur s'est produite. Je vous mets directement en relation avec un agent."
            )
            return [SlotSet("escalated", True)]

class ActionGetStoreLocations(Action):
    """Action pour trouver les boutiques Free Mobile"""

    def name(self) -> Text:
        return "action_get_store_locations"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        
        try:
            city = tracker.get_slot("city") or "Paris"
            
            # Simulation de boutiques
            stores = [
                {
                    "name": "Free Mobile - Châtelet",
                    "address": "2 Rue de Rivoli, 75001 Paris",
                    "hours": "10h-19h (Lun-Sam)",
                    "phone": "01 23 45 67 89"
                },
                {
                    "name": "Free Mobile - Opéra", 
                    "address": "15 Boulevard des Capucines, 75002 Paris",
                    "hours": "9h30-19h30 (Lun-Sam)",
                    "phone": "01 23 45 67 90"
                }
            ]
            
            message = f"🏪 Boutiques Free Mobile près de {city} :\n\n"
            
            for store in stores:
                message += f"""📍 **{store['name']}**
📧 {store['address']}
🕒 {store['hours']}
📞 {store['phone']}

"""
            
            message += "Souhaitez-vous l'adresse d'une boutique spécifique ?"
            
            dispatcher.utter_message(text=message)
            
            return [SlotSet("stores_found", len(stores))]
            
        except Exception as e:
            logger.error(f"Erreur lors de la recherche de boutiques: {e}")
            dispatcher.utter_message(
                text="Désolé, je ne peux pas accéder aux informations des boutiques pour le moment."
            )
            return [] 