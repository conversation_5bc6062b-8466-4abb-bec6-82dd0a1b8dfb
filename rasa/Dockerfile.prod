# Rasa Production Dockerfile
FROM python:3.9-slim AS builder

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    gcc \
    g++ \
    git \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Install Rasa
RUN pip install --no-cache-dir \
    rasa[full]==3.6.4 \
    rasa-sdk==3.6.1

# Production stage
FROM python:3.9-slim AS production

# Install runtime dependencies only
RUN apt-get update && apt-get install -y \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user
RUN groupadd -r rasa && useradd -r -g rasa rasa

# Set working directory
WORKDIR /app

# Copy from builder
COPY --from=builder /usr/local/lib/python3.9/site-packages /usr/local/lib/python3.9/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# Copy Rasa configuration and data
COPY config.yml domain.yml ./
COPY data/ ./data/
COPY actions/ ./actions/

# Create necessary directories
RUN mkdir -p models logs && \
    chown -R rasa:rasa /app

# Switch to non-root user
USER rasa

# Expose port
EXPOSE 5005

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5005/status || exit 1

# Train model and start server
CMD ["bash", "-c", "rasa train --domain domain.yml --data data --config config.yml --out models && rasa run --model models --enable-api --cors '*' --port 5005 --log-file logs/rasa.log"] 