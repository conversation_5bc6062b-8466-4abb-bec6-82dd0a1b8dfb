# [SECURITY] Rasa Credentials Configuration # Configuration des canaux de communication pour le chatbot # REST API Channel (par défaut) rest: # Pas de configuration nécessaire # Socket.IO Channel pour communication temps réel socketio: user_message_evt: user_uttered bot_message_evt: bot_uttered session_persistence: true # Configuration pour intégration avec le backend Node.js callback: url: "http://backend:5000/webhooks/rasa" # Channel personnalisé pour Free Mobile # Peut être étendu pour intégrer avec les systèmes Free Mobile existants custom: # Configuration spécifique Free Mobile à définir