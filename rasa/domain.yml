version: "3.1" intents: - greet - goodbye - affirm - deny - bot_challenge - check_balance - check_invoice - change_plan - report_network_issue - report_billing_issue - ask_support - ask_technical_help - cancel_subscription - activate_option - check_consumption - report_phone_issue - ask_store_location - ask_opening_hours - complain - ask_refund entities: - phone_number - invoice_date - plan_name - option_name - amount - date - location slots: phone_number: type: text mappings: - type: from_entity entity: phone_number issue_type: type: text mappings: - type: from_entity entity: issue_type plan_name: type: text mappings: - type: from_entity entity: plan_name responses: utter_greet: - text: "Bonjour! Je suis l'assistant virtuel Free Mobile. Comment puis-je t'aider aujourd'hui?" - text: "Salut! Je suis là pour t'aider avec tes questions Free Mobile. Que puis-je faire pour toi?" utter_goodbye: - text: "Au revoir! N'hésite pas à revenir si tu as d'autres questions." - text: "À bientôt! L'équipe Free Mobile est toujours là pour t'aider." utter_iamabot: - text: "Je suis un assistant virtuel créé pour t'aider avec tes questions Free Mobile." - text: "Je suis un chatbot conçu pour répondre à tes questions sur les services Free Mobile." utter_check_balance: - text: "Pour consulter ton solde, connecte-toi à ton Espace Abonné sur free.fr ou utilise l'app Free Mobile." - text: "Tu peux vérifier ton solde dans l'application Free Mobile ou sur ton Espace Abonné en ligne." utter_check_invoice: - text: "Tes factures sont disponibles dans ton Espace Abonné sur free.fr, section 'Mes factures'." - text: "Tu trouveras toutes tes factures dans l'application Free Mobile ou sur free.fr dans ton espace personnel." utter_network_issue: - text: "Pour un problème de réseau, vérifie d'abord que tu es dans une zone couverte. Tu peux aussi redémarrer ton téléphone." - text: "En cas de problème réseau, consulte la carte de couverture sur free.fr et redémarre ton appareil." utter_billing_issue: - text: "Pour une question de facturation, je peux t'orienter vers un conseiller. Veux-tu que je transfère ta demande?" - text: "Les questions de facturation nécessitent souvent un examen personnalisé. Puis-je te mettre en relation avec un conseiller?" utter_escalate_human: - text: "Je transfère ta demande à un conseiller humain qui pourra mieux t'aider." - text: "Un agent va prendre le relais pour traiter ta demande de façon personnalisée." utter_default_fallback: - text: "Je ne suis pas sûr de comprendre. Peux-tu reformuler ta question?" - text: "Désolé, je n'ai pas bien saisi. Peux-tu être plus précis dans ta demande?" actions: - action_check_balance - action_check_invoice - action_escalate_to_human - action_provide_store_info - action_default_fallback session_config: session_expiration_time: 60 carry_over_slots_to_new_session: true