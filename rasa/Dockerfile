# Rasa Dockerfile - AI Chatbot Multi-stage
# Optimized for production with pre-trained model

# =============================================
# STAGE 1: Base Dependencies
# =============================================
FROM python:3.9-slim AS dependencies

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    gcc \
    g++ \
    git \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Set working directory
WORKDIR /app

# Install Python dependencies
RUN pip install --no-cache-dir \
    rasa[full]==3.6.4 \
    rasa-sdk==3.6.1

# =============================================
# 🏗️ STAGE 2: Model Training
# =============================================
FROM dependencies AS trainer

# Copy Rasa configuration and training data
COPY config.yml domain.yml ./
COPY data/ ./data/
COPY actions/ ./actions/

# Create models directory
RUN mkdir -p models

# Train Rasa model
RUN rasa train \
    --domain domain.yml \
    --data data \
    --config config.yml \
    --out models \
    --fixed-model-name chatbot-model

# =============================================
# 🚀 STAGE 3: Production Runtime
# =============================================
FROM python:3.9-slim AS production

# Install minimal runtime dependencies
RUN apt-get update && apt-get install -y \
    curl \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user
RUN groupadd -r rasa && useradd -r -g rasa rasa

# Set working directory
WORKDIR /app

# Copy Python packages from dependencies stage
COPY --from=dependencies /usr/local/lib/python3.9/site-packages /usr/local/lib/python3.9/site-packages
COPY --from=dependencies /usr/local/bin /usr/local/bin

# Copy trained model and configuration
COPY --from=trainer --chown=rasa:rasa /app/models ./models
COPY --from=trainer --chown=rasa:rasa /app/domain.yml ./
COPY --from=trainer --chown=rasa:rasa /app/config.yml ./
COPY --from=trainer --chown=rasa:rasa /app/actions ./actions

# Create necessary directories
RUN mkdir -p logs credentials && \
    chown -R rasa:rasa /app

# Copy credentials (if any)
COPY --chown=rasa:rasa credentials.yml ./credentials.yml

# Create endpoints configuration
COPY --chown=rasa:rasa endpoints.yml ./endpoints.yml

# Switch to non-root user
USER rasa

# Environment variables
ENV RASA_MODEL_PATH=/app/models/chatbot-model.tar.gz
ENV RASA_CORE_DEBUG_FILE=/app/logs/rasa_core.log

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5005/status || exit 1

# Expose port
EXPOSE 5005

# Labels for metadata
LABEL maintainer="Free Mobile <<EMAIL>>"
LABEL version="1.0"
LABEL description="Chatbot Free Mobile Rasa - IA NLP Engine"

# Start Rasa server with pre-trained model
CMD ["rasa", "run", \
     "--model", "models/chatbot-model.tar.gz", \
     "--enable-api", \
     "--cors", "*", \
     "--port", "5005", \
     "--log-file", "logs/rasa.log", \
     "--debug"] 