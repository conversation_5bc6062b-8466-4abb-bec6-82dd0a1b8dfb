# Rasa .dockerignore
# Exclude unnecessary files to optimize Docker <PERSON><PERSON> build

# Python cache
__pycache__
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# IDE files
.vscode
.idea
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore
.gitattributes

# Testing
.pytest_cache
.coverage
htmlcov/
.tox/
.cache

# Logs (seront créés dans le container)
logs
*.log

# Rasa specific
.rasa

# Models (seront construits dans le container)
models/*.tar.gz

# Jupyter notebooks
.ipynb_checkpoints

# Documentation
README.md
docs/

# Docker files
Dockerfile*
docker-compose*
.dockerignore

# Environment files
.env
.env.*

# Backup files
*.bak
*.backup

# Temporary files
tmp/
temp/ 