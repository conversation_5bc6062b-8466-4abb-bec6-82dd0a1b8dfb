# 🌐 Frontend Dockerfile - React + Nginx Multi-stage
# Optimisé pour production avec cache et sécurité

# =============================================
# 📦 STAGE 1: Build Dependencies
# =============================================
FROM node:18-alpine AS dependencies

# Set working directory
WORKDIR /app

# Copy package files for better layer caching
COPY package*.json ./

# Install dependencies with npm ci for faster, reliable builds
RUN npm ci --silent && \
    npm cache clean --force

# =============================================
# 🏗️ STAGE 2: Build Application
# =============================================
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy dependencies from previous stage
COPY --from=dependencies /app/node_modules ./node_modules
COPY package*.json ./

# Install dev dependencies for build
RUN npm ci --silent

# Copy source code
COPY . .

# Build optimized production bundle
ENV NODE_ENV=production
ENV GENERATE_SOURCEMAP=false
ENV CI=false

# Build React app with optimizations
RUN npm run build && \
    # Remove source maps for security
    find build -name "*.map" -delete && \
    # Compress assets
    find build -name "*.js" -exec gzip -9 -k {} \; && \
    find build -name "*.css" -exec gzip -9 -k {} \;

# =============================================
# 🚀 STAGE 3: Production Server
# =============================================
FROM nginx:1.25-alpine AS production

# Install security updates
RUN apk update && apk upgrade && \
    apk add --no-cache curl && \
    rm -rf /var/cache/apk/*

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S reactuser -u 1001 -G nodejs

# Copy custom nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Copy built application from builder stage
COPY --from=builder --chown=reactuser:nodejs /app/build /usr/share/nginx/html

# Create nginx cache and temp directories
RUN mkdir -p /var/cache/nginx/client_temp \
             /var/cache/nginx/proxy_temp \
             /var/cache/nginx/fastcgi_temp \
             /var/cache/nginx/uwsgi_temp \
             /var/cache/nginx/scgi_temp && \
    chown -R reactuser:nodejs /var/cache/nginx && \
    chown -R reactuser:nodejs /usr/share/nginx/html && \
    chown -R reactuser:nodejs /var/log/nginx && \
    # Make nginx.pid writable
    touch /var/run/nginx.pid && \
    chown -R reactuser:nodejs /var/run/nginx.pid

# Switch to non-root user
USER reactuser

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# Expose port
EXPOSE 3000

# Labels for metadata
LABEL maintainer="Free Mobile <<EMAIL>>"
LABEL version="1.0"
LABEL description="Chatbot Free Mobile Frontend - React + Nginx"

# Start nginx
CMD ["nginx", "-g", "daemon off;"] 