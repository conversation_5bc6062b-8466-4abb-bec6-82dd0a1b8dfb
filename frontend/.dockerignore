# 🚫 Frontend .dockerignore
# Exclure les fichiers inutiles pour optimiser le build Docker

# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production build
build
dist

# Development
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode
.idea
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore
.gitattributes

# Testing
coverage
.nyc_output
*.lcov

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# ESLint cache
.eslintcache

# Storybook build outputs
.out
.storybook-out

# Documentation
README.md
CHANGELOG.md
docs/

# Docker files (already in context)
Dockerfile*
docker-compose*
.dockerignore

# Source maps
*.map 