{"name": "free-mobile-chatbot-frontend", "version": "1.0.0", "description": "Frontend React pour le chatbot Free Mobile", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.3", "@mui/material": "^5.14.5", "@mui/x-date-pickers": "^6.11.2", "@reduxjs/toolkit": "^1.9.5", "axios": "^1.11.0", "chart.js": "^4.5.0", "chartjs-adapter-date-fns": "^3.0.0", "date-fns": "^2.30.0", "dayjs": "^1.11.13", "emoji-picker-react": "^4.13.2", "framer-motion": "^12.23.9", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.263.1", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-error-boundary": "^6.0.0", "react-markdown": "^8.0.7", "react-redux": "^8.1.2", "react-router-dom": "^6.15.0", "react-scripts": "^5.0.1", "react-toastify": "^11.0.5", "remark-gfm": "^3.0.1", "socket.io-client": "^4.7.2", "typescript": "^4.9.5", "web-vitals": "^3.4.0", "xlsx": "^0.18.5"}, "devDependencies": {"@axe-core/playwright": "^4.10.2", "@playwright/test": "^1.54.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^20.5.9", "@types/react": "^18.2.21", "@types/react-dom": "^18.2.7", "playwright": "^1.54.1"}, "scripts": {"start": "react-scripts start", "start:dev": "set GENERATE_SOURCEMAP=false && react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:e2e:report": "playwright show-report", "test:e2e:install": "playwright install", "test:all": "npm test -- --coverage --watchAll=false && npm run test:e2e"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "overrides": {"nth-check": "^2.1.1", "postcss": "^8.4.31", "webpack-dev-server": "^4.15.1"}, "proxy": "http://localhost:5000"}