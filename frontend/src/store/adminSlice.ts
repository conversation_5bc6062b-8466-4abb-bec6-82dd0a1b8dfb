import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

interface AdminState {
  dashboard: any;
  conversations: any[];
  analytics: any;
  loading: boolean;
  error: string | null;
}

const initialState: AdminState = {
  dashboard: null,
  conversations: [],
  analytics: null,
  loading: false,
  error: null,
};

// Mock admin service
const mockAdminService = {
  getDashboard: async () => ({
    totalConversations: 150,
    activeConversations: 25,
    resolvedConversations: 125,
    averageResponseTime: '2.5 hours'
  }),
  getAllConversations: async () => ({
    conversations: [],
    total: 0
  }),
  getAnalytics: async () => ({
    totalTickets: 150,
    resolvedTickets: 125,
    averageResolutionTime: '2.5 hours'
  })
};

export const fetchDashboard = createAsyncThunk(
  'admin/fetchDashboard',
  async () => {
    const response = await mockAdminService.getDashboard();
    return response;
  }
);

export const fetchConversations = createAsyncThunk(
  'admin/fetchConversations',
  async (params: { page?: number; limit?: number; status?: string; search?: string }) => {
    const response = await mockAdminService.getAllConversations();
    return response;
  }
);

export const fetchAnalytics = createAsyncThunk(
  'admin/fetchAnalytics',
  async ({ startDate, endDate }: { startDate?: string; endDate?: string }) => {
    const response = await mockAdminService.getAnalytics();
    return response;
  }
);

const adminSlice = createSlice({
  name: 'admin',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateConversationStatus: (state, action) => {
      const { conversationId, status } = action.payload;
      const conversation = state.conversations.find(c => c._id === conversationId);
      if (conversation) {
        conversation.status = status;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Dashboard
      .addCase(fetchDashboard.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchDashboard.fulfilled, (state, action) => {
        state.loading = false;
        state.dashboard = action.payload;
      })
      .addCase(fetchDashboard.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch dashboard';
      })

      // Fetch Conversations
      .addCase(fetchConversations.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchConversations.fulfilled, (state, action) => {
        state.loading = false;
        state.conversations = action.payload.conversations;
      })
      .addCase(fetchConversations.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch conversations';
      })

      // Fetch Analytics
      .addCase(fetchAnalytics.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAnalytics.fulfilled, (state, action) => {
        state.loading = false;
        state.analytics = action.payload;
      })
      .addCase(fetchAnalytics.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch analytics';
      });
  },
});

export const { clearError, updateConversationStatus } = adminSlice.actions;
export default adminSlice.reducer;