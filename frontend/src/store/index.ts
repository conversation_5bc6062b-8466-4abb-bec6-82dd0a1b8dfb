import { configureStore } from '@reduxjs/toolkit';
import authReducer from './authSlice';
import chatReducer from './slices/chatSlice';
import adminReducer from './adminSlice';
import customerReducer from './slices/customerSlice';
import uiReducer from './slices/uiSlice';

export const store = configureStore({
  reducer: {
    auth: authReducer,
    chat: chatReducer,
    admin: adminReducer,
    customer: customerReducer,
    ui: uiReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;