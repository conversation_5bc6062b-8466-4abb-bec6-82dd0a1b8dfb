/**
 * =============================================
 * [FEATURE] ENHANCED AI SUGGESTIONS REDUX SLICE
 * Advanced AI-powered suggestions and recommendations
 * Extends existing AI services with contextual intelligence
 * =============================================
 */
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

// Types for enhanced AI suggestions
export interface ContextualSuggestion {
  id: string;
  type: 'response' | 'action' | 'escalation' | 'information' | 'template';
  content: string;
  confidence: number; // 0-1
  reasoning: string;
  context: SuggestionContext;
  personalization: PersonalizationData;
  timestamp: string;
  used: boolean;
  feedback?: SuggestionFeedback;
  alternatives?: string[];
}

export interface SuggestionContext {
  ticket_id: string;
  customer_id: string;
  conversation_history: ConversationMessage[];
  customer_profile: CustomerProfile;
  current_sentiment: number; // -1 to 1
  urgency_level: 'low' | 'medium' | 'high' | 'critical';
  platform: 'whatsapp' | 'facebook' | 'instagram' | 'twitter' | 'call' | 'email';
  agent_skill_level: number; // 0-100
  similar_cases: SimilarCase[];
}

export interface ConversationMessage {
  id: string;
  sender: 'customer' | 'agent' | 'system';
  content: string;
  timestamp: string;
  sentiment?: number;
  intent?: string;
  entities?: ExtractedEntity[];
}

export interface ExtractedEntity {
  type: 'person' | 'product' | 'service' | 'date' | 'amount' | 'location';
  value: string;
  confidence: number;
}

export interface CustomerProfile {
  id: string;
  name: string;
  tier: 'bronze' | 'silver' | 'gold' | 'platinum';
  communication_style: 'formal' | 'casual' | 'technical' | 'emotional';
  preferred_language: string;
  previous_issues: string[];
  satisfaction_history: number[];
  response_preferences: ResponsePreferences;
}

export interface ResponsePreferences {
  length: 'short' | 'medium' | 'detailed';
  tone: 'professional' | 'friendly' | 'empathetic' | 'technical';
  include_examples: boolean;
  include_links: boolean;
  follow_up_preference: 'immediate' | 'scheduled' | 'none';
}

export interface SimilarCase {
  case_id: string;
  similarity_score: number; // 0-1
  resolution: string;
  outcome: 'resolved' | 'escalated' | 'pending';
  customer_satisfaction: number; // 0-10
  resolution_time: number; // minutes
}

export interface PersonalizationData {
  agent_id: string;
  agent_preferences: AgentPreferences;
  learning_data: LearningData;
  adaptation_score: number; // 0-1
}

export interface AgentPreferences {
  communication_style: 'direct' | 'empathetic' | 'technical' | 'consultative';
  preferred_templates: string[];
  custom_phrases: string[];
  escalation_threshold: number; // 0-1
  response_length_preference: 'concise' | 'detailed';
}

export interface LearningData {
  successful_responses: string[];
  feedback_patterns: FeedbackPattern[];
  improvement_areas: string[];
  strengths: string[];
}

export interface FeedbackPattern {
  suggestion_type: string;
  usage_rate: number; // 0-1
  success_rate: number; // 0-1
  customer_satisfaction_impact: number; // -1 to 1
}

export interface SuggestionFeedback {
  rating: 1 | 2 | 3 | 4 | 5;
  helpful: boolean;
  used_as_is: boolean;
  modified: boolean;
  modification_reason?: string;
  outcome: 'successful' | 'unsuccessful' | 'neutral';
  customer_response?: 'positive' | 'negative' | 'neutral';
}

export interface ResponseTemplate {
  id: string;
  name: string;
  category: 'greeting' | 'resolution' | 'escalation' | 'follow_up' | 'closing';
  template: string;
  variables: TemplateVariable[];
  usage_count: number;
  success_rate: number; // 0-1
  personalized_versions: PersonalizedTemplate[];
  last_updated: string;
}

export interface TemplateVariable {
  name: string;
  type: 'text' | 'number' | 'date' | 'selection';
  required: boolean;
  default_value?: string;
  options?: string[];
}

export interface PersonalizedTemplate {
  agent_id: string;
  customized_template: string;
  performance_metrics: {
    usage_count: number;
    success_rate: number;
    customer_satisfaction: number;
  };
}

export interface SentimentAnalysis {
  current_sentiment: number; // -1 to 1
  sentiment_trend: SentimentTrend[];
  emotion_breakdown: EmotionBreakdown;
  escalation_risk: number; // 0-1
  recommended_approach: 'empathetic' | 'solution_focused' | 'escalate' | 'de_escalate';
}

export interface SentimentTrend {
  timestamp: string;
  sentiment: number;
  trigger_event?: string;
}

export interface EmotionBreakdown {
  anger: number;
  frustration: number;
  satisfaction: number;
  confusion: number;
  urgency: number;
}

export interface EscalationRecommendation {
  should_escalate: boolean;
  confidence: number; // 0-1
  urgency: 'immediate' | 'soon' | 'monitor';
  escalation_type: 'supervisor' | 'specialist' | 'technical' | 'billing';
  reasoning: string[];
  preparation_steps: string[];
  handoff_notes: string;
}

export interface EnhancedAIState {
  // Current suggestions
  active_suggestions: ContextualSuggestion[];
  suggestion_history: ContextualSuggestion[];

  // Templates
  response_templates: ResponseTemplate[];
  personalized_templates: PersonalizedTemplate[];
  template_categories: string[];

  // Sentiment analysis
  current_sentiment: SentimentAnalysis | null;
  sentiment_history: SentimentAnalysis[];

  // Escalation recommendations
  escalation_recommendation: EscalationRecommendation | null;
  escalation_history: EscalationRecommendation[];

  // Learning and adaptation
  agent_learning_data: LearningData;
  suggestion_performance: Record<string, FeedbackPattern>;

  // Real-time context
  current_context: SuggestionContext | null;
  context_updates: ContextUpdate[];

  // Settings
  ai_settings: AISettings;
  personalization_enabled: boolean;
  auto_suggestions: boolean;
  suggestion_frequency: 'low' | 'medium' | 'high';

  // UI state
  suggestions_panel_open: boolean;
  templates_panel_open: boolean;
  sentiment_panel_open: boolean;
  selected_suggestion: string | null;

  // Loading states
  loading: {
    suggestions: boolean;
    templates: boolean;
    sentiment: boolean;
    escalation: boolean;
  };
  error: string | null;
}

export interface ContextUpdate {
  timestamp: string;
  type: 'message' | 'sentiment_change' | 'escalation_risk' | 'customer_info';
  data: any;
}

export interface AISettings {
  confidence_threshold: number; // 0-1
  max_suggestions: number;
  include_alternatives: boolean;
  personalization_level: 'basic' | 'advanced' | 'expert';
  learning_mode: 'passive' | 'active' | 'aggressive';
  template_auto_update: boolean;
}

// Mock async thunks for enhanced AI operations
export const generateContextualSuggestions = createAsyncThunk(
  'enhancedAI/generateContextualSuggestions',
  async (context: SuggestionContext) => {
    // Mock implementation
    return {
      suggestions: [
        {
          id: 'suggestion-1',
          type: 'response' as const,
          content: 'Bonjour, je comprends votre problème. Laissez-moi vous aider.',
          confidence: 0.85,
          reasoning: 'Réponse empathique basée sur le contexte',
          context,
          personalization: {} as PersonalizationData,
          timestamp: new Date().toISOString(),
          used: false,
          alternatives: ['Salut, comment puis-je vous aider ?']
        }
      ]
    };
  }
);

export const analyzeSentiment = createAsyncThunk(
  'enhancedAI/analyzeSentiment',
  async (conversation: ConversationMessage[]) => {
    // Mock implementation
    return {
      sentiment_analysis: {
        current_sentiment: 0.2,
        sentiment_trend: [],
        emotion_breakdown: {
          anger: 0.1,
          frustration: 0.3,
          satisfaction: 0.2,
          confusion: 0.3,
          urgency: 0.1
        },
        escalation_risk: 0.3,
        recommended_approach: 'empathetic' as const
      }
    };
  }
);

// Initial state
const initialState: EnhancedAIState = {
  active_suggestions: [],
  suggestion_history: [],
  response_templates: [],
  personalized_templates: [],
  template_categories: [],
  current_sentiment: null,
  sentiment_history: [],
  escalation_recommendation: null,
  escalation_history: [],
  agent_learning_data: {
    successful_responses: [],
    feedback_patterns: [],
    improvement_areas: [],
    strengths: []
  },
  suggestion_performance: {},
  current_context: null,
  context_updates: [],
  ai_settings: {
    confidence_threshold: 0.7,
    max_suggestions: 5,
    include_alternatives: true,
    personalization_level: 'advanced',
    learning_mode: 'active',
    template_auto_update: true
  },
  personalization_enabled: true,
  auto_suggestions: true,
  suggestion_frequency: 'medium',
  suggestions_panel_open: true,
  templates_panel_open: false,
  sentiment_panel_open: false,
  selected_suggestion: null,
  loading: {
    suggestions: false,
    templates: false,
    sentiment: false,
    escalation: false
  },
  error: null
};

// Enhanced AI slice
const enhancedAISlice = createSlice({
  name: 'enhancedAI',
  initialState,
  reducers: {
    // UI actions
    toggleSuggestionsPanel: (state) => {
      state.suggestions_panel_open = !state.suggestions_panel_open;
    },
    toggleTemplatesPanel: (state) => {
      state.templates_panel_open = !state.templates_panel_open;
    },
    toggleSentimentPanel: (state) => {
      state.sentiment_panel_open = !state.sentiment_panel_open;
    },
    setSelectedSuggestion: (state, action: PayloadAction<string | null>) => {
      state.selected_suggestion = action.payload;
    },

    // Settings actions
    updateAISettings: (state, action: PayloadAction<Partial<AISettings>>) => {
      state.ai_settings = { ...state.ai_settings, ...action.payload };
    },
    setPersonalizationEnabled: (state, action: PayloadAction<boolean>) => {
      state.personalization_enabled = action.payload;
    },
    setAutoSuggestions: (state, action: PayloadAction<boolean>) => {
      state.auto_suggestions = action.payload;
    },
    setSuggestionFrequency: (state, action: PayloadAction<'low' | 'medium' | 'high'>) => {
      state.suggestion_frequency = action.payload;
    },

    // Context actions
    updateContext: (state, action: PayloadAction<Partial<SuggestionContext>>) => {
      if (state.current_context) {
        state.current_context = { ...state.current_context, ...action.payload };
      } else {
        state.current_context = action.payload as SuggestionContext;
      }

      // Add context update to history
      state.context_updates.unshift({
        timestamp: new Date().toISOString(),
        type: 'message',
        data: action.payload
      });

      // Keep only last 50 context updates
      if (state.context_updates.length > 50) {
        state.context_updates = state.context_updates.slice(0, 50);
      }
    },

    // Suggestion actions
    markSuggestionUsed: (state, action: PayloadAction<string>) => {
      const suggestion = state.active_suggestions.find(s => s.id === action.payload);
      if (suggestion) {
        suggestion.used = true;
        state.suggestion_history.unshift(suggestion);
      }
    },
    addSuggestionFeedback: (state, action: PayloadAction<{
      suggestion_id: string;
      feedback: SuggestionFeedback;
    }>) => {
      const suggestion = state.active_suggestions.find(s => s.id === action.payload.suggestion_id);
      if (suggestion) {
        suggestion.feedback = action.payload.feedback;
      }
    },

    // Real-time updates
    addContextualSuggestion: (state, action: PayloadAction<ContextualSuggestion>) => {
      // Add to active suggestions if confidence meets threshold
      if (action.payload.confidence >= state.ai_settings.confidence_threshold) {
        state.active_suggestions.unshift(action.payload);

        // Keep only max suggestions
        if (state.active_suggestions.length > state.ai_settings.max_suggestions) {
          state.active_suggestions = state.active_suggestions.slice(0, state.ai_settings.max_suggestions);
        }
      }
    },
    updateSentimentAnalysis: (state, action: PayloadAction<SentimentAnalysis>) => {
      state.current_sentiment = action.payload;
      state.sentiment_history.unshift(action.payload);

      // Keep only last 20 sentiment analyses
      if (state.sentiment_history.length > 20) {
        state.sentiment_history = state.sentiment_history.slice(0, 20);
      }
    },
    clearSuggestions: (state) => {
      state.active_suggestions = [];
    },
    clearError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    // Generate contextual suggestions
    builder
      .addCase(generateContextualSuggestions.pending, (state) => {
        state.loading.suggestions = true;
        state.error = null;
      })
      .addCase(generateContextualSuggestions.fulfilled, (state, action) => {
        state.loading.suggestions = false;
        state.active_suggestions = action.payload.suggestions.filter(
          (s: ContextualSuggestion) => s.confidence >= state.ai_settings.confidence_threshold
        );
      })
      .addCase(generateContextualSuggestions.rejected, (state, action) => {
        state.loading.suggestions = false;
        state.error = action.error.message || 'Failed to generate suggestions';
      });

    // Analyze sentiment
    builder
      .addCase(analyzeSentiment.pending, (state) => {
        state.loading.sentiment = true;
      })
      .addCase(analyzeSentiment.fulfilled, (state, action) => {
        state.loading.sentiment = false;
        state.current_sentiment = action.payload.sentiment_analysis;
        state.sentiment_history.unshift(action.payload.sentiment_analysis);
      })
      .addCase(analyzeSentiment.rejected, (state, action) => {
        state.loading.sentiment = false;
        state.error = action.error.message || 'Failed to analyze sentiment';
      });
  }
});

// Export actions
export const {
  toggleSuggestionsPanel,
  toggleTemplatesPanel,
  toggleSentimentPanel,
  setSelectedSuggestion,
  updateAISettings,
  setPersonalizationEnabled,
  setAutoSuggestions,
  setSuggestionFrequency,
  updateContext,
  markSuggestionUsed,
  addSuggestionFeedback,
  addContextualSuggestion,
  updateSentimentAnalysis,
  clearSuggestions,
  clearError
} = enhancedAISlice.actions;

// Export selectors
export const selectEnhancedAIState = (state: any) => state.enhancedAI;
export const selectActiveSuggestions = (state: any) => state.enhancedAI.active_suggestions;
export const selectCurrentSentiment = (state: any) => state.enhancedAI.current_sentiment;
export const selectEscalationRecommendation = (state: any) => state.enhancedAI.escalation_recommendation;
export const selectResponseTemplates = (state: any) => state.enhancedAI.response_templates;
export const selectAISettings = (state: any) => state.enhancedAI.ai_settings;
export const selectPersonalizedTemplates = (state: any) => state.enhancedAI.personalized_templates;
export const selectSuggestionHistory = (state: any) => state.enhancedAI.suggestion_history;
export const selectSentimentHistory = (state: any) => state.enhancedAI.sentiment_history;
export const selectLoading = (state: any) => state.enhancedAI.loading;
export const selectError = (state: any) => state.enhancedAI.error;

export default enhancedAISlice.reducer;