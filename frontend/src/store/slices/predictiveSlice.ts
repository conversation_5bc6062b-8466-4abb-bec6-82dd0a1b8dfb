/** * ============================================= * PREDICTIVE ANALYTICS REDUX SLICE * State management for ML-powered predictions and analytics * Integrates with existing analytics and ML services * ============================================= */ import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'; // Types for predictive analytics export interface ChurnPrediction { customer_id: string; customer_name: string; churn_probability: number; // 0-1 risk_level: 'low' | 'medium' | 'high' | 'critical'; contributing_factors: ChurnFactor[]; recommended_actions: RecommendedAction[]; prediction_confidence: number; // 0-1 last_updated: string; } export interface ChurnFactor { factor: string; impact_score: number; // 0-1 description: string; category: 'usage' | 'billing' | 'support' | 'satisfaction' | 'engagement'; } export interface RecommendedAction { action: string; priority: 'low' | 'medium' | 'high'; expected_impact: number; // 0-1 effort_required: 'low' | 'medium' | 'high'; department: 'sales' | 'support' | 'billing' | 'technical'; } export interface DemandForecast { date: string; hour: number; predicted_volume: number; confidence_interval: { lower: number; upper: number; }; peak_probability: number; // 0-1 recommended_staffing: number; historical_average: number; factors: ForecastFactor[]; } export interface ForecastFactor { factor: string; influence: number; // -1 to 1 description: string; category: 'seasonal' | 'promotional' | 'external' | 'historical'; } export interface WorkloadOptimization { agent_id: string; agent_name: string; current_workload: number; // 0-100 optimal_workload: number; // 0-100 efficiency_score: number; // 0-100 skill_match_score: number; // 0-100 recommended_assignments: TicketAssignment[]; break_recommendation: BreakRecommendation; performance_trend: 'improving' | 'stable' | 'declining'; } export interface TicketAssignment { ticket_id: string; priority: number; // 1-10 estimated_duration: number; // minutes skill_match: number; // 0-1 customer_satisfaction_impact: number; // 0-1 } export interface BreakRecommendation { recommended: boolean; urgency: 'low' | 'medium' | 'high'; optimal_duration: number; // minutes reason: string; } export interface EscalationPrediction { ticket_id: string; escalation_probability: number; // 0-1 risk_level: 'low' | 'medium' | 'high' | 'critical'; predicted_escalation_time: number; // minutes escalation_reasons: EscalationReason[]; prevention_actions: PreventionAction[]; confidence: number; // 0-1 } export interface EscalationReason { reason: string; probability: number; // 0-1 category: 'complexity' | 'customer_frustration' | 'agent_skill' | 'system_issue'; } export interface PreventionAction { action: string; effectiveness: number; // 0-1 urgency: 'immediate' | 'soon' | 'monitor'; resource_required: string; } export interface AnomalyDetection { id: string; type: 'performance' | 'volume' | 'satisfaction' | 'system'; severity: 'low' | 'medium' | 'high' | 'critical'; description: string; detected_at: string; affected_metrics: string[]; anomaly_score: number; // 0-1 baseline_value: number; current_value: number; trend: 'increasing' | 'decreasing' | 'fluctuating'; recommended_investigation: string[]; auto_resolved: boolean; } export interface PredictiveMetrics { model_accuracy: { churn_prediction: number; demand_forecast: number; escalation_prediction: number; anomaly_detection: number; }; prediction_confidence: { average: number; by_category: Record<string, number>; }; business_impact: { churn_prevented: number; cost_savings: number; efficiency_improvement: number; satisfaction_improvement: number; }; last_model_update: string; } export interface PredictiveState { // Churn prediction churn_predictions: ChurnPrediction[]; high_risk_customers: ChurnPrediction[]; churn_trends: { daily: number[]; weekly: number[]; monthly: number[]; }; // Demand forecasting demand_forecast: DemandForecast[]; peak_hours: number[]; staffing_recommendations: { current_hour: number; next_4_hours: number[]; next_24_hours: number[]; }; // Workload optimization agent_workloads: WorkloadOptimization[]; team_efficiency: number; optimal_assignments: TicketAssignment[]; // Escalation prediction escalation_predictions: EscalationPrediction[]; high_risk_tickets: EscalationPrediction[]; escalation_trends: { hourly: number[]; daily: number[]; }; // Anomaly detection active_anomalies: AnomalyDetection[]; anomaly_history: AnomalyDetection[]; system_health_score: number; // 0-100 // Model performance predictive_metrics: PredictiveMetrics; // Filters and settings filters: { time_range: '1h' | '4h' | '24h' | '7d' | '30d'; risk_levels: string[]; departments: string[]; anomaly_types: string[]; }; // UI state active_dashboard: 'churn' | 'demand' | 'workload' | 'escalation' | 'anomaly'; auto_refresh: boolean; refresh_interval: number; // seconds // Loading states loading: { churn: boolean; demand: boolean; workload: boolean; escalation: boolean; anomaly: boolean; metrics: boolean; }; error: string | null; last_updated: string; } // Async thunks for predictive analytics export const fetchChurnPredictions = createAsyncThunk( 'predictive/fetchChurnPredictions', async (filters?: { risk_level?: string; time_range?: string }) => { const response = await fetch('/api/ml/predictions/churn', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(filters || {}) }); return response.json(); } ); export const fetchDemandForecast = createAsyncThunk( 'predictive/fetchDemandForecast', async (hours_ahead: number = 24) => { const response = await fetch(`/api/ml/predictions/demand?hours=${hours_ahead}`); return response.json(); } ); export const fetchWorkloadOptimization = createAsyncThunk( 'predictive/fetchWorkloadOptimization', async () => { const response = await fetch('/api/ml/optimization/workload'); return response.json(); } ); export const fetchEscalationPredictions = createAsyncThunk( 'predictive/fetchEscalationPredictions', async (filters?: { risk_level?: string }) => { const response = await fetch('/api/ml/predictions/escalation', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(filters || {}) }); return response.json(); } ); export const fetchAnomalyDetection = createAsyncThunk( 'predictive/fetchAnomalyDetection', async (time_range: string = '24h') => { const response = await fetch(`/api/ml/anomaly/detect?range=${time_range}`); return response.json(); } ); export const fetchPredictiveMetrics = createAsyncThunk( 'predictive/fetchPredictiveMetrics', async () => { const response = await fetch('/api/ml/metrics/predictive'); return response.json(); } ); // Initial state const initialState: PredictiveState = { churn_predictions: [], high_risk_customers: [], churn_trends: { daily: [], weekly: [], monthly: [] }, demand_forecast: [], peak_hours: [], staffing_recommendations: { current_hour: 0, next_4_hours: [], next_24_hours: [] }, agent_workloads: [], team_efficiency: 0, optimal_assignments: [], escalation_predictions: [], high_risk_tickets: [], escalation_trends: { hourly: [], daily: [] }, active_anomalies: [], anomaly_history: [], system_health_score: 100, predictive_metrics: { model_accuracy: { churn_prediction: 0, demand_forecast: 0, escalation_prediction: 0, anomaly_detection: 0 }, prediction_confidence: { average: 0, by_category: {} }, business_impact: { churn_prevented: 0, cost_savings: 0, efficiency_improvement: 0, satisfaction_improvement: 0 }, last_model_update: '' }, filters: { time_range: '24h', risk_levels: [], departments: [], anomaly_types: [] }, active_dashboard: 'churn', auto_refresh: true, refresh_interval: 300, // 5 minutes loading: { churn: false, demand: false, workload: false, escalation: false, anomaly: false, metrics: false }, error: null, last_updated: '' }; // Predictive analytics slice const predictiveSlice = createSlice({ name: 'predictive', initialState, reducers: { // UI actions setActiveDashboard: (state, action: PayloadAction<PredictiveState['active_dashboard']>) => { state.active_dashboard = action.payload; }, setAutoRefresh: (state, action: PayloadAction<boolean>) => { state.auto_refresh = action.payload; }, setRefreshInterval: (state, action: PayloadAction<number>) => { state.refresh_interval = action.payload; }, // Filter actions setFilters: (state, action: PayloadAction<Partial<PredictiveState['filters']>>) => { state.filters = { ...state.filters, ...action.payload }; }, setTimeRange: (state, action: PayloadAction<PredictiveState['filters']['time_range']>) => { state.filters.time_range = action.payload; }, // Real-time updates addAnomaly: (state, action: PayloadAction<AnomalyDetection>) => { state.active_anomalies.unshift(action.payload); state.anomaly_history.unshift(action.payload); // Update system health score based on anomaly severity const severityImpact = { low: 1, medium: 3, high: 7, critical: 15 }; state.system_health_score = Math.max(0, state.system_health_score - severityImpact[action.payload.severity] ); }, resolveAnomaly: (state, action: PayloadAction<string>) => { const anomaly = state.active_anomalies.find(a => a.id === action.payload); if (anomaly) { anomaly.auto_resolved = true; state.active_anomalies = state.active_anomalies.filter(a => a.id !== action.payload); // Improve system health score when anomaly is resolved state.system_health_score = Math.min(100, state.system_health_score + 2); } }, updateChurnPrediction: (state, action: PayloadAction<ChurnPrediction>) => { const index = state.churn_predictions.findIndex(p => p.customer_id === action.payload.customer_id); if (index !== -1) { state.churn_predictions[index] = action.payload; } else { state.churn_predictions.push(action.payload); } // Update high risk customers state.high_risk_customers = state.churn_predictions.filter(p => p.risk_level === 'high' || p.risk_level === 'critical' ); }, updateEscalationPrediction: (state, action: PayloadAction<EscalationPrediction>) => { const index = state.escalation_predictions.findIndex(p => p.ticket_id === action.payload.ticket_id); if (index !== -1) { state.escalation_predictions[index] = action.payload; } else { state.escalation_predictions.push(action.payload); } // Update high risk tickets state.high_risk_tickets = state.escalation_predictions.filter(p => p.risk_level === 'high' || p.risk_level === 'critical' ); }, clearError: (state) => { state.error = null; }, updateLastRefresh: (state) => { state.last_updated = new Date().toISOString(); } }, extraReducers: (builder) => { // Churn predictions builder .addCase(fetchChurnPredictions.pending, (state) => { state.loading.churn = true; state.error = null; }) .addCase(fetchChurnPredictions.fulfilled, (state, action) => { state.loading.churn = false; state.churn_predictions = action.payload.predictions; state.high_risk_customers = action.payload.high_risk_customers; state.churn_trends = action.payload.trends; state.last_updated = new Date().toISOString(); }) .addCase(fetchChurnPredictions.rejected, (state, action) => { state.loading.churn = false; state.error = action.error.message || 'Failed to fetch churn predictions'; }); // Demand forecast builder .addCase(fetchDemandForecast.pending, (state) => { state.loading.demand = true; }) .addCase(fetchDemandForecast.fulfilled, (state, action) => { state.loading.demand = false; state.demand_forecast = action.payload.forecast; state.peak_hours = action.payload.peak_hours; state.staffing_recommendations = action.payload.staffing_recommendations; }) .addCase(fetchDemandForecast.rejected, (state, action) => { state.loading.demand = false; state.error = action.error.message || 'Failed to fetch demand forecast'; }); // Workload optimization builder .addCase(fetchWorkloadOptimization.fulfilled, (state, action) => { state.loading.workload = false; state.agent_workloads = action.payload.agent_workloads; state.team_efficiency = action.payload.team_efficiency; state.optimal_assignments = action.payload.optimal_assignments; }); // Escalation predictions builder .addCase(fetchEscalationPredictions.fulfilled, (state, action) => { state.loading.escalation = false; state.escalation_predictions = action.payload.predictions; state.high_risk_tickets = action.payload.high_risk_tickets; state.escalation_trends = action.payload.trends; }); // Anomaly detection builder .addCase(fetchAnomalyDetection.fulfilled, (state, action) => { state.loading.anomaly = false; state.active_anomalies = action.payload.active_anomalies; state.anomaly_history = action.payload.history; state.system_health_score = action.payload.system_health_score; }); // Predictive metrics builder .addCase(fetchPredictiveMetrics.fulfilled, (state, action) => { state.loading.metrics = false; state.predictive_metrics = action.payload.metrics; }); } }); // Export actions export const { setActiveDashboard, setAutoRefresh, setRefreshInterval, setFilters, setTimeRange, addAnomaly, resolveAnomaly, updateChurnPrediction, updateEscalationPrediction, clearError, updateLastRefresh } = predictiveSlice.actions; // Export selectors export const selectPredictiveState = (state: { predictive: PredictiveState }) => state.predictive; export const selectChurnPredictions = (state: { predictive: PredictiveState }) => state.predictive.churn_predictions; export const selectHighRiskCustomers = (state: { predictive: PredictiveState }) => state.predictive.high_risk_customers; export const selectDemandForecast = (state: { predictive: PredictiveState }) => state.predictive.demand_forecast; export const selectActiveAnomalies = (state: { predictive: PredictiveState }) => state.predictive.active_anomalies; export const selectSystemHealthScore = (state: { predictive: PredictiveState }) => state.predictive.system_health_score; export default predictiveSlice.reducer;