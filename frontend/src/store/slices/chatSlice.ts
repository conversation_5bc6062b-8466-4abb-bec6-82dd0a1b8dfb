import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

interface ChatState {
  conversations: any[];
  currentConversation: any;
  messages: any[];
  loading: boolean;
  error: string | null;
  isTyping: boolean;
  isAgentOnline: boolean;
  typingUsers: any[];
  connectionStatus: {
    connected: boolean;
    error: string | null;
  };
  realTimeEnabled: boolean;
}

const initialState: ChatState = {
  conversations: [],
  currentConversation: null,
  messages: [],
  loading: false,
  error: null,
  isTyping: false,
  isAgentOnline: false,
  typingUsers: [],
  connectionStatus: {
    connected: false,
    error: null
  },
  realTimeEnabled: false,
};

// Mock chat service
const mockChatService = {
  startConversation: async () => ({
    conversationId: 'conv-' + Date.now(),
    sessionId: 'session-' + Date.now(),
    welcomeMessage: 'Bonjour ! Comment puis-je vous aider aujourd\'hui ?'
  }),
  sendMessage: async (conversationId: string, message: string) => ({
    messageId: 'msg-' + Date.now(),
    response: 'Merci pour votre message. Un agent va vous répondre bientôt.'
  }),
  getUserConversations: async () => [],
  getConversationHistory: async (conversationId: string) => ({
    conversation: { _id: conversationId, status: 'active' },
    messages: []
  })
};

export const startConversation = createAsyncThunk(
  'chat/startConversation',
  async () => {
    const response = await mockChatService.startConversation();
    return response;
  }
);

export const sendMessage = createAsyncThunk(
  'chat/sendMessage',
  async ({ conversationId, message, attachments }: { conversationId: string; message: string; attachments?: string[] }) => {
    const response = await mockChatService.sendMessage(conversationId, message);
    return response;
  }
);

export const loadUserConversations = createAsyncThunk(
  'chat/loadUserConversations',
  async (_, { rejectWithValue }) => {
    try {
      const response = await mockChatService.getUserConversations();
      return response;
    } catch (error: any) {
      return rejectWithValue('Erreur de chargement des conversations');
    }
  }
);

const chatSlice = createSlice({
  name: 'chat',
  initialState,
  reducers: {
    addMessage: (state, action) => {
      state.messages.push(action.payload);
    },
    setTyping: (state, action) => {
      state.isTyping = action.payload;
    },
    setAgentOnline: (state, action) => {
      state.isAgentOnline = action.payload;
    },
    updateTypingUsers: (state, action) => {
      state.typingUsers = action.payload;
    },
    setConnectionStatus: (state, action) => {
      state.connectionStatus = action.payload;
    },
    setRealTimeEnabled: (state, action) => {
      state.realTimeEnabled = action.payload;
    },
    clearChat: (state) => {
      state.currentConversation = null;
      state.messages = [];
      state.typingUsers = [];
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Start conversation
      .addCase(startConversation.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(startConversation.fulfilled, (state, action) => {
        state.loading = false;
        state.currentConversation = {
          _id: action.payload.conversationId,
          sessionId: action.payload.sessionId,
          status: 'active',
          channel: 'web',
          startedAt: new Date(),
        };
        // Add welcome message
        state.messages = [{
          _id: Date.now().toString(),
          conversationId: action.payload.conversationId,
          sender: 'bot',
          content: {
            text: action.payload.welcomeMessage,
            type: 'text',
          },
          timestamp: new Date(),
        }];
      })
      .addCase(startConversation.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Erreur de démarrage de conversation';
      })

      // Send message
      .addCase(sendMessage.pending, (state) => {
        state.isTyping = true;
        state.error = null;
      })
      .addCase(sendMessage.fulfilled, (state, action) => {
        state.isTyping = false;
      })
      .addCase(sendMessage.rejected, (state, action) => {
        state.isTyping = false;
        state.error = action.error.message || 'Erreur d\'envoi de message';
      })

      // Load user conversations
      .addCase(loadUserConversations.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loadUserConversations.fulfilled, (state, action) => {
        state.loading = false;
        state.conversations = action.payload;
      })
      .addCase(loadUserConversations.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  addMessage,
  setTyping,
  setAgentOnline,
  updateTypingUsers,
  setConnectionStatus,
  setRealTimeEnabled,
  clearChat,
  clearError
} = chatSlice.actions;

export default chatSlice.reducer;