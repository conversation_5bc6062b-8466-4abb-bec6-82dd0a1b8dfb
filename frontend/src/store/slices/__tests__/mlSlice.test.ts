import { configureStore } from '@reduxjs/toolkit'; import mlSlice, { fetchPriorityQueue, fetchPerformanceMetrics, fetchAlerts, classifyConversation, validateClassification, acknowledgeAlert, fetchClassificationStats, updateFilters, resetFilters, addClassification, addAlert, updateMetrics, updatePriorityQueue, clearErrors, resetState, } from '../mlSlice'; import { MLDashboardState } from '../../../types/ml'; // Mock the ML service jest.mock('../../../services/ml.service', () => ({ getPriorityQueue: jest.fn(), getPerformanceMetrics: jest.fn(), getAlerts: jest.fn(), classifyConversation: jest.fn(), validateClassification: jest.fn(), acknowledgeAlert: jest.fn(), getClassificationStats: jest.fn(), })); describe('mlSlice', () => { let store: ReturnType<typeof configureStore>; beforeEach(() => { store = configureStore({ reducer: { ml: mlSlice, }, }); }); describe('initial state', () => { it('should have correct initial state', () => { const state = store.getState().ml; expect(state).toEqual({ classifications: [], alerts: [], priorityQueue: [], metrics: null, filters: { timeRange: '24h', minPriority: 0, showOnlyUnvalidated: false }, loading: { classifications: false, alerts: false, priorityQueue: false, metrics: false }, error: { classifications: null, alerts: null, priorityQueue: null, metrics: null }, lastUpdated: { classifications: null, alerts: null, priorityQueue: null, metrics: null } }); }); }); describe('synchronous actions', () => { it('should update filters', () => { const newFilters = { timeRange: '7d' as const, minPriority: 50 }; store.dispatch(updateFilters(newFilters)); const state = store.getState().ml; expect(state.filters.timeRange).toBe('7d'); expect(state.filters.minPriority).toBe(50); expect(state.filters.showOnlyUnvalidated).toBe(false); // unchanged }); it('should reset filters', () => { // First update filters store.dispatch(updateFilters({ timeRange: '7d', minPriority: 80 })); // Then reset store.dispatch(resetFilters()); const state = store.getState().ml; expect(state.filters).toEqual({ timeRange: '24h', minPriority: 0, showOnlyUnvalidated: false }); }); it('should add classification', () => { const mockClassification = { _id: 'class-1', conversationId: 'conv-1', category: 'TECHNICAL_SUPPORT' as const, confidence: 0.95, priorityScore: 85, humanValidated: false, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() }; store.dispatch(addClassification(mockClassification)); const state = store.getState().ml; expect(state.classifications).toHaveLength(1); expect(state.classifications[0]).toEqual(mockClassification); expect(state.lastUpdated.classifications).toBeTruthy(); }); it('should add alert', () => { const mockAlert = { _id: 'alert-1', type: 'PERFORMANCE_DEGRADATION' as const, severity: 'HIGH' as const, status: 'ACTIVE' as const, title: 'High Response Time', message: 'Response time exceeded threshold', metadata: {}, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() }; store.dispatch(addAlert(mockAlert)); const state = store.getState().ml; expect(state.alerts).toHaveLength(1); expect(state.alerts[0]).toEqual(mockAlert); expect(state.lastUpdated.alerts).toBeTruthy(); }); it('should update metrics', () => { const mockMetrics = { totalConversations: 1000, classifiedConversations: 950, averageConfidence: 0.87, accuracyRate: 0.92, responseTime: 150, categoriesDistribution: { TECHNICAL_SUPPORT: 45, BILLING: 30, GENERAL_INQUIRY: 25 }, hourlyStats: [], lastUpdated: new Date().toISOString() }; store.dispatch(updateMetrics(mockMetrics)); const state = store.getState().ml; expect(state.metrics).toEqual(mockMetrics); expect(state.lastUpdated.metrics).toBeTruthy(); }); it('should clear errors', () => { // First set some errors by dispatching failed actions const errorAction = { type: fetchAlerts.rejected.type, error: { message: 'Test error' }, meta: { requestId: 'test', arg: {} } }; store.dispatch(errorAction as any); // Then clear errors store.dispatch(clearErrors()); const state = store.getState().ml; expect(state.error.alerts).toBe(null); expect(state.error.classifications).toBe(null); expect(state.error.priorityQueue).toBe(null); expect(state.error.metrics).toBe(null); }); it('should reset state', () => { // First modify state store.dispatch(updateFilters({ minPriority: 80 })); store.dispatch(addAlert({ _id: 'alert-1', type: 'PERFORMANCE_DEGRADATION', severity: 'HIGH', status: 'ACTIVE', title: 'Test', message: 'Test', metadata: {}, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() })); // Then reset store.dispatch(resetState()); const state = store.getState().ml; expect(state.alerts).toEqual([]); expect(state.classifications).toEqual([]); expect(state.filters.minPriority).toBe(0); }); }); describe('async actions', () => { it('should handle fetchPriorityQueue.pending', () => { store.dispatch(fetchPriorityQueue.pending('requestId', { limit: 10 })); const state = store.getState().ml; expect(state.loading.priorityQueue).toBe(true); expect(state.error.priorityQueue).toBe(null); }); it('should handle fetchPriorityQueue.rejected', () => { const errorMessage = 'Failed to fetch priority queue'; const rejectedAction = { type: fetchPriorityQueue.rejected.type, error: { message: errorMessage }, meta: { requestId: 'requestId', arg: { limit: 10 } } }; store.dispatch(rejectedAction as any); const state = store.getState().ml; expect(state.loading.priorityQueue).toBe(false); expect(state.error.priorityQueue).toBe(errorMessage); }); it('should handle fetchPerformanceMetrics.pending', () => { store.dispatch(fetchPerformanceMetrics.pending('requestId', '24h')); const state = store.getState().ml; expect(state.loading.metrics).toBe(true); expect(state.error.metrics).toBe(null); }); it('should handle fetchAlerts.pending', () => { store.dispatch(fetchAlerts.pending('requestId', { limit: 20 })); const state = store.getState().ml; expect(state.loading.alerts).toBe(true); expect(state.error.alerts).toBe(null); }); it('should handle classifyConversation.pending', () => { store.dispatch(classifyConversation.pending('requestId', { conversationId: 'conv-1' })); const state = store.getState().ml; expect(state.loading.classifications).toBe(true); expect(state.error.classifications).toBe(null); }); }); describe('edge cases', () => { it('should limit classifications to 100 items', () => { // Add 101 classifications for (let i = 0; i < 101; i++) { store.dispatch(addClassification({ _id: `class-${i}`, conversationId: `conv-${i}`, category: 'TECHNICAL_SUPPORT', confidence: 0.9, priorityScore: 70, humanValidated: false, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() })); } const state = store.getState().ml; expect(state.classifications).toHaveLength(100); expect(state.classifications[0]._id).toBe('class-100'); // Most recent first }); it('should limit alerts to 100 items', () => { // Add 101 alerts for (let i = 0; i < 101; i++) { store.dispatch(addAlert({ _id: `alert-${i}`, type: 'PERFORMANCE_DEGRADATION', severity: 'LOW', status: 'ACTIVE', title: `Alert ${i}`, message: `Message ${i}`, metadata: {}, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() })); } const state = store.getState().ml; expect(state.alerts).toHaveLength(100); expect(state.alerts[0]._id).toBe('alert-100'); // Most recent first }); }); });