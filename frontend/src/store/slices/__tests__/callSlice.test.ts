import { configureStore } from '@reduxjs/toolkit'; import callSlice, { initiateCall, endCall, getQueueStats, startWebRTCCall, joinWebRTCCall, scheduleCallback, getCallHistory, toggleMute, toggleSpeaker, toggleVideo, setLocalStream, setRemoteStream, updateCallStatus, updateWebRTCSession, setShowCallInterface, clearError, resetCallState, } from '../callSlice'; // Mock the services jest.mock('../../../services/call.service', () => ({ callService: { initiateCall: jest.fn(), endCall: jest.fn(), getQueueStats: jest.fn(), startWebRTCCall: jest.fn(), joinWebRTCCall: jest.fn(), scheduleCallback: jest.fn(), getCallHistory: jest.fn(), }, })); jest.mock('../../../services/webrtc.service', () => ({ webrtcService: { toggleMute: jest.fn(), toggleSpeaker: jest.fn(), toggleVideo: jest.fn(), }, })); describe('callSlice', () => { let store: ReturnType<typeof configureStore>; beforeEach(() => { store = configureStore({ reducer: { call: callSlice, }, }); }); describe('initial state', () => { it('should have correct initial state', () => { const state = store.getState().call; expect(state).toEqual({ currentCall: null, isCallActive: false, callStatus: 'idle', isMuted: false, isSpeakerOn: false, isVideoEnabled: false, queuePosition: null, estimatedWaitTime: 0, availableAgents: 0, queueStats: null, webrtcSession: null, localStream: null, remoteStream: null, callHistory: [], isLoading: false, error: null, showCallInterface: false }); }); }); describe('synchronous actions', () => { it('should toggle mute', () => { store.dispatch(toggleMute('user-123')); const state = store.getState().call; expect(state.isMuted).toBe(true); // Toggle again store.dispatch(toggleMute('user-123')); expect(store.getState().call.isMuted).toBe(false); }); it('should toggle speaker', () => { store.dispatch(toggleSpeaker('user-123')); const state = store.getState().call; expect(state.isSpeakerOn).toBe(true); // Toggle again store.dispatch(toggleSpeaker('user-123')); expect(store.getState().call.isSpeakerOn).toBe(false); }); it('should toggle video', () => { store.dispatch(toggleVideo('user-123')); const state = store.getState().call; expect(state.isVideoEnabled).toBe(true); // Toggle again store.dispatch(toggleVideo('user-123')); expect(store.getState().call.isVideoEnabled).toBe(false); }); it('should set local stream', () => { const mockStream = {} as MediaStream; store.dispatch(setLocalStream(mockStream)); const state = store.getState().call; expect(state.localStream).toBe(mockStream); }); it('should set remote stream', () => { const mockStream = {} as MediaStream; store.dispatch(setRemoteStream(mockStream)); const state = store.getState().call; expect(state.remoteStream).toBe(mockStream); }); it('should update call status', () => { const statusUpdate = { status: 'connecting' as const, queuePosition: 3, estimatedWaitTime: 120 }; store.dispatch(updateCallStatus(statusUpdate)); const state = store.getState().call; expect(state.callStatus).toBe('connecting'); expect(state.queuePosition).toBe(3); expect(state.estimatedWaitTime).toBe(120); }); it('should update WebRTC session', () => { const mockSession = { id: 'session-123', participants: [], isActive: true }; store.dispatch(updateWebRTCSession(mockSession)); const state = store.getState().call; expect(state.webrtcSession).toEqual(mockSession); }); it('should set show call interface', () => { store.dispatch(setShowCallInterface(true)); const state = store.getState().call; expect(state.showCallInterface).toBe(true); }); it('should clear error', () => { // First set an error by dispatching a failed action const errorAction = { type: initiateCall.rejected.type, error: { message: 'Test error' }, meta: { requestId: 'test', arg: {} } }; store.dispatch(errorAction as any); // Then clear error store.dispatch(clearError()); const state = store.getState().call; expect(state.error).toBe(null); }); it('should reset call state', () => { // First modify state store.dispatch(toggleMute('user-123')); store.dispatch(updateCallStatus({ status: 'connected' })); store.dispatch(setShowCallInterface(true)); // Then reset store.dispatch(resetCallState()); const state = store.getState().call; expect(state.isMuted).toBe(false); expect(state.callStatus).toBe('idle'); expect(state.showCallInterface).toBe(false); expect(state.currentCall).toBe(null); }); }); describe('async actions', () => { it('should handle initiateCall.pending', () => { const callParams = { type: 'immediate' as const, userId: 'user-123', priority: 'normal' as const, phoneNumber: '+1234567890' }; store.dispatch(initiateCall.pending('requestId', callParams)); const state = store.getState().call; expect(state.isLoading).toBe(true); expect(state.error).toBe(null); }); it('should handle initiateCall.rejected', () => { const errorMessage = 'Failed to initiate call'; const rejectedAction = { type: initiateCall.rejected.type, error: { message: errorMessage }, meta: { requestId: 'requestId', arg: {} } }; store.dispatch(rejectedAction as any); const state = store.getState().call; expect(state.isLoading).toBe(false); expect(state.error).toBe(errorMessage); }); it('should handle endCall.pending', () => { store.dispatch(endCall.pending('requestId', { callId: 'call-123' })); const state = store.getState().call; expect(state.isLoading).toBe(true); }); it('should handle getQueueStats.fulfilled', () => { const mockQueueStats = { stats: { availableAgents: 5, averageWaitTime: 120, totalInQueue: 10 } }; const fulfilledAction = { type: getQueueStats.fulfilled.type, payload: mockQueueStats, meta: { requestId: 'requestId', arg: undefined } }; store.dispatch(fulfilledAction as any); const state = store.getState().call; expect(state.queueStats).toEqual(mockQueueStats.stats); expect(state.availableAgents).toBe(5); expect(state.estimatedWaitTime).toBe(120); }); it('should handle startWebRTCCall.pending', () => { const params = { participants: [] }; store.dispatch(startWebRTCCall.pending('requestId', params)); const state = store.getState().call; expect(state.isLoading).toBe(true); }); it('should handle joinWebRTCCall.fulfilled', () => { const mockSession = { session: { id: 'session-123', participants: [], isActive: true } }; const fulfilledAction = { type: joinWebRTCCall.fulfilled.type, payload: mockSession, meta: { requestId: 'requestId', arg: {} } }; store.dispatch(fulfilledAction as any); const state = store.getState().call; expect(state.webrtcSession).toEqual(mockSession.session); expect(state.callStatus).toBe('connected'); }); it('should handle scheduleCallback.pending', () => { const params = { userId: 'user-123', scheduledTime: new Date(), reason: 'Technical support', phoneNumber: '+1234567890' }; store.dispatch(scheduleCallback.pending('requestId', params)); const state = store.getState().call; expect(state.isLoading).toBe(true); }); it('should handle getCallHistory.fulfilled', () => { const mockCallHistory = [ { id: 'call-1', direction: 'outbound' as const, from: '+1234567890', to: '+0987654321', status: 'completed' as const, duration: 300, startTime: new Date().toISOString(), endTime: new Date().toISOString(), recordingUrl: null } ]; const fulfilledAction = { type: getCallHistory.fulfilled.type, payload: { calls: mockCallHistory }, meta: { requestId: 'requestId', arg: {} } }; store.dispatch(fulfilledAction as any); const state = store.getState().call; expect(state.callHistory).toEqual(mockCallHistory); }); }); describe('edge cases', () => { it('should handle null streams', () => { store.dispatch(setLocalStream(null)); store.dispatch(setRemoteStream(null)); const state = store.getState().call; expect(state.localStream).toBe(null); expect(state.remoteStream).toBe(null); }); it('should handle partial status updates', () => { store.dispatch(updateCallStatus({ status: 'ringing' })); const state = store.getState().call; expect(state.callStatus).toBe('ringing'); expect(state.queuePosition).toBe(null); // Should remain unchanged expect(state.estimatedWaitTime).toBe(0); // Should remain unchanged }); }); });