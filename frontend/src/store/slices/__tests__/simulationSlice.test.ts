import { configureStore } from '@reduxjs/toolkit'; import simulationSlice, { fetchScenarios, fetchRecommendedScenarios, createSimulationSession, startSimulation, fetchActiveSession, sendMessage, endSimulation, fetchAgentProgress, fetchSessionHistory, fetchLeaderboard, setSelectedScenario, setFilters, clearError, setSocketConnected, updateRealTimeMetrics, addAICoaching, updateSessionProgress, addSessionMessage, clearActiveSession, updateCurrentSessionData, toggleAIPanel, togglePerformancePanel, setSimulationSpeed, addAISuggestion, addRealTimeFeedback, resetSimulation, } from '../simulationSlice'; // Mock the simulation service jest.mock('../../../services/simulation.service', () => ({ getScenarios: jest.fn(), getRecommendedScenarios: jest.fn(), createSession: jest.fn(), getActiveSession: jest.fn(), sendMessage: jest.fn(), endSession: jest.fn(), getAgentProgress: jest.fn(), getSessionHistory: jest.fn(), getLeaderboard: jest.fn(), })); describe('simulationSlice', () => { let store: ReturnType<typeof configureStore>; beforeEach(() => { store = configureStore({ reducer: { simulation: simulationSlice, }, }); jest.clearAllMocks(); }); describe('initial state', () => { it('should have correct initial state', () => { const state = store.getState().simulation; expect(state).toEqual({ scenarios: [], recommendedScenarios: [], selectedScenario: null, activeSession: null, sessionHistory: [], currentSessionData: null, agentProgress: null, leaderboard: [], realTimeMetrics: null, aiCoaching: [], loading: { scenarios: false, session: false, progress: false, history: false, leaderboard: false }, error: null, socketConnected: false, filters: { difficulty: 'all', category: 'all', timeRange: '30d' } }); }); }); describe('synchronous actions', () => { it('should set selected scenario', () => { const scenario = { id: 'scenario-1', name: 'Customer Complaint' }; store.dispatch(setSelectedScenario(scenario)); const state = store.getState().simulation; expect(state.selectedScenario).toEqual(scenario); }); it('should set filters', () => { const newFilters = { difficulty: 'hard', category: 'technical' }; store.dispatch(setFilters(newFilters)); const state = store.getState().simulation; expect(state.filters.difficulty).toBe('hard'); expect(state.filters.category).toBe('technical'); expect(state.filters.timeRange).toBe('30d'); // unchanged }); it('should clear error', () => { // First set an error const errorAction = { type: fetchScenarios.rejected.type, payload: 'Test error', meta: { requestId: 'test', arg: {} } }; store.dispatch(errorAction as any); // Then clear error store.dispatch(clearError()); const state = store.getState().simulation; expect(state.error).toBe(null); }); it('should set socket connected', () => { store.dispatch(setSocketConnected(true)); const state = store.getState().simulation; expect(state.socketConnected).toBe(true); }); it('should update real time metrics', () => { const metrics = { responseTime: 2.5, accuracy: 0.85, customerSatisfaction: 4.2 }; store.dispatch(updateRealTimeMetrics(metrics)); const state = store.getState().simulation; expect(state.realTimeMetrics).toEqual(metrics); }); it('should add AI coaching', () => { const coaching = { type: 'suggestion', message: 'Try to be more empathetic', priority: 'medium' }; store.dispatch(addAICoaching(coaching)); const state = store.getState().simulation; expect(state.aiCoaching).toHaveLength(1); expect(state.aiCoaching[0]).toMatchObject(coaching); expect(state.aiCoaching[0].timestamp).toBeTruthy(); expect(state.aiCoaching[0].id).toBeTruthy(); }); it('should update session progress', () => { // First create an active session using the fulfilled action const session = { id: 'session-1', status: 'active' }; const fulfilledAction = { type: createSimulationSession.fulfilled.type, payload: { session }, meta: { requestId: 'test', arg: {} } }; store.dispatch(fulfilledAction as any); const progress = { currentStep: 3, totalSteps: 10, completionPercentage: 30 }; store.dispatch(updateSessionProgress(progress)); const state = store.getState().simulation; expect(state.activeSession).toMatchObject(progress); }); it('should add session message', () => { // First create an active session using the fulfilled action const session = { id: 'session-1', status: 'active', messages: [] }; const fulfilledAction = { type: createSimulationSession.fulfilled.type, payload: { session }, meta: { requestId: 'test', arg: {} } }; store.dispatch(fulfilledAction as any); const message = { id: 'msg-1', sender: 'agent', content: 'Hello, how can I help you?', timestamp: new Date().toISOString() }; store.dispatch(addSessionMessage(message)); const state = store.getState().simulation; expect(state.activeSession.messages).toHaveLength(1); expect(state.activeSession.messages[0]).toEqual(message); }); it('should clear active session', () => { // First set an active session const session = { id: 'session-1', status: 'active' }; store.dispatch({ type: 'simulation/setActiveSession', payload: session }); // Then clear it store.dispatch(clearActiveSession()); const state = store.getState().simulation; expect(state.activeSession).toBe(null); }); it('should update current session data', () => { // First set some initial data store.dispatch(updateCurrentSessionData({ step: 1, score: 80 })); // Then update it store.dispatch(updateCurrentSessionData({ step: 2, score: 85 })); const state = store.getState().simulation; expect(state.currentSessionData).toEqual({ step: 2, score: 85 }); }); it('should toggle AI panel', () => { // Initial state should have, aiPanelOpen as undefined/false store.dispatch(toggleAIPanel()); const state = store.getState().simulation; expect(state.aiPanelOpen).toBe(true); // Toggle again store.dispatch(toggleAIPanel()); expect(store.getState().simulation.aiPanelOpen).toBe(false); }); it('should toggle performance panel', () => { store.dispatch(togglePerformancePanel()); const state = store.getState().simulation; expect(state.performancePanelOpen).toBe(true); }); it('should set simulation speed', () => { store.dispatch(setSimulationSpeed('fast')); const state = store.getState().simulation; expect(state.simulationSpeed).toBe('fast'); }); it('should add AI suggestion', () => { const suggestion = { type: 'response', content: 'You might want to ask about their account details', confidence: 0.8 }; store.dispatch(addAISuggestion(suggestion)); const state = store.getState().simulation; expect(state.aiCoaching).toHaveLength(1); expect(state.aiCoaching[0]).toMatchObject(suggestion); expect(state.aiCoaching[0].timestamp).toBeTruthy(); }); it('should add real time feedback', () => { // First initialize the realTimeFeedback array by dispatching resetSimulation store.dispatch(resetSimulation()); const feedback = { type: 'positive', message: 'Great response time!', score: 95 }; store.dispatch(addRealTimeFeedback(feedback)); const state = store.getState().simulation; expect(state.realTimeFeedback).toHaveLength(1); expect(state.realTimeFeedback[0]).toMatchObject(feedback); expect(state.realTimeFeedback[0].timestamp).toBeTruthy(); }); it('should reset simulation', () => { // First set some data store.dispatch(setSelectedScenario({ id: 'test' })); store.dispatch(addAISuggestion({ type: 'test', content: 'test' })); store.dispatch(updateCurrentSessionData({ step: 1 })); // Then reset store.dispatch(resetSimulation()); const state = store.getState().simulation; expect(state.activeSession).toBe(null); expect(state.currentSessionData).toBe(null); expect(state.aiCoaching).toEqual([]); expect(state.realTimeFeedback).toEqual([]); expect(state.sessionProgress).toBe(null); expect(state.error).toBe(null); }); }); describe('async actions', () => { it('should handle fetchScenarios.pending', () => { store.dispatch(fetchScenarios.pending('requestId', {})); const state = store.getState().simulation; expect(state.loading.scenarios).toBe(true); expect(state.error).toBe(null); }); it('should handle fetchScenarios.rejected', () => { const errorMessage = 'Failed to fetch scenarios'; const rejectedAction = { type: fetchScenarios.rejected.type, payload: errorMessage, meta: { requestId: 'requestId', arg: {} } }; store.dispatch(rejectedAction as any); const state = store.getState().simulation; expect(state.loading.scenarios).toBe(false); expect(state.error).toBe(errorMessage); }); it('should handle createSimulationSession.pending', () => { store.dispatch(createSimulationSession.pending('requestId', { scenarioId: 'test' })); const state = store.getState().simulation; expect(state.loading.session).toBe(true); }); it('should handle startSimulation.pending', () => { store.dispatch(startSimulation.pending('requestId', { scenarioId: 'test' })); const state = store.getState().simulation; // startSimulation doesn't have a pending handler, so loading should remain false expect(state.loading.session).toBe(false); }); it('should handle fetchAgentProgress.pending', () => { store.dispatch(fetchAgentProgress.pending('requestId', 'agent-123')); const state = store.getState().simulation; expect(state.loading.progress).toBe(true); }); it('should handle fetchSessionHistory.pending', () => { store.dispatch(fetchSessionHistory.pending('requestId', {})); const state = store.getState().simulation; expect(state.loading.history).toBe(true); }); it('should handle fetchLeaderboard.pending', () => { store.dispatch(fetchLeaderboard.pending('requestId', {})); const state = store.getState().simulation; expect(state.loading.leaderboard).toBe(true); }); }); });