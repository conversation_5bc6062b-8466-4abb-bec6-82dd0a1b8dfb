import { configureStore } from '@reduxjs/toolkit'; import chatSlice, { sendMessage, loadConversationHistory, clearChat, setTyping, } from '../chatSlice'; import { ChatState } from '../../../types'; // Mock the chat service jest.mock('../../../services/chat.service', () => ({ sendMessage: jest.fn(), getChatHistory: jest.fn(), })); describe('chatSlice', () => { let store: ReturnType<typeof configureStore>; beforeEach(() => { store = configureStore({ reducer: { chat: chatSlice, }, }); }); describe('initial state', () => { it('should have correct initial state', () => { const state = store.getState().chat; expect(state).toEqual({ conversations: [], currentConversation: null, messages: [], loading: false, error: null, isTyping: false, isAgentOnline: false, }); }); }); describe('synchronous actions', () => { it('should clear chat messages', () => { // First add some messages const initialState: ChatState = { messages: [ { id: '1', content: 'Hello', sender: 'user', timestamp: new Date().toISOString(), conversationId: 'conv-1', }, { id: '2', content: 'Hi there!', sender: 'bot', timestamp: new Date().toISOString(), conversationId: 'conv-1', }, ], loading: false, error: null, typing: false, currentConversationId: 'conv-1', }; const storeWithMessages = configureStore({ reducer: { chat: chatSlice, }, preloadedState: { chat: initialState, }, }); // Clear chat storeWithMessages.dispatch(clearChat()); const state = storeWithMessages.getState().chat; expect(state.messages).toEqual([]); expect(state.currentConversation).toBe(null); expect(state.error).toBe(null); }); it('should set typing status', () => { store.dispatch(setTyping(true)); let state = store.getState().chat; expect(state.isTyping).toBe(true); store.dispatch(setTyping(false)); state = store.getState().chat; expect(state.isTyping).toBe(false); }); }); describe('async actions', () => { it('should handle sendMessage.pending', () => { const messageData = { content: 'Hello', conversationId: 'conv-1', }; store.dispatch(sendMessage.pending('requestId', messageData)); const state = store.getState().chat; expect(state.isTyping).toBe(true); expect(state.error).toBe(null); }); it('should handle sendMessage.rejected', () => { const error = { message: 'Failed to send message' }; const messageData = { content: 'Hello', conversationId: 'conv-1', }; store.dispatch(sendMessage.rejected(error as any, 'requestId', messageData)); const state = store.getState().chat; expect(state.loading).toBe(false); expect(state.error).toBe('Failed to send message'); }); it('should handle loadConversationHistory.pending', () => { store.dispatch(loadConversationHistory.pending('requestId', 'conv-1')); const state = store.getState().chat; expect(state.loading).toBe(true); expect(state.error).toBe(null); }); it('should handle loadConversationHistory.rejected', () => { const errorMessage = 'Failed to load conversation history'; const rejectedAction = { type: loadConversationHistory.rejected.type, payload: errorMessage, error: { message: errorMessage }, meta: { requestId: 'requestId', arg: 'conv-1' } }; store.dispatch(rejectedAction as any); const state = store.getState().chat; expect(state.loading).toBe(false); expect(state.error).toBe('Failed to load conversation history'); }); }); describe('edge cases', () => { it('should handle multiple typing status changes', () => { store.dispatch(setTyping(true)); store.dispatch(setTyping(true)); // Should remain true let state = store.getState().chat; expect(state.isTyping).toBe(true); store.dispatch(setTyping(false)); store.dispatch(setTyping(false)); // Should remain false state = store.getState().chat; expect(state.isTyping).toBe(false); }); it('should handle clearChat when already empty', () => { store.dispatch(clearChat()); const state = store.getState().chat; expect(state.messages).toEqual([]); expect(state.currentConversation).toBe(null); expect(state.error).toBe(null); }); }); });