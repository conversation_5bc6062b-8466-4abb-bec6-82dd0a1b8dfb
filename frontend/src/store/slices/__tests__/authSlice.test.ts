import { configureStore } from '@reduxjs/toolkit';
import authSlice, { login, logout, register } from '../../authSlice';

// Mock the auth service
jest.mock('../../../services/auth.service', () => ({
  login: jest.fn(),
  register: jest.fn(),
  updateProfile: jest.fn(),
  getCurrentUser: jest.fn(),
}));

interface AuthState {
  user: any;
  token: string | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
}

describe('authSlice', () => {
  let store: ReturnType<typeof configureStore>;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        auth: authSlice,
      },
    });
  });

  describe('initial state', () => {
    it('should have correct initial state', () => {
      const state = store.getState().auth;

      expect(state).toEqual({
        user: null,
        token: null,
        isAuthenticated: false,
        loading: false,
        error: null,
      });
    });
  });

  describe('logout action', () => {
    it('should clear user data on logout', () => {
      // First set some user data
      const initialState: AuthState = {
        user: {
          id: '1',
          email: '<EMAIL>',
          name: 'Test User',
          role: 'user',
        },
        token: 'test-token',
        isAuthenticated: true,
        loading: false,
        error: null,
      };

      // Create store with initial state
      const storeWithUser = configureStore({
        reducer: {
          auth: authSlice,
        },
        preloadedState: {
          auth: initialState,
        },
      });

      // Dispatch logout
      storeWithUser.dispatch(logout());
      const state = storeWithUser.getState().auth;

      expect(state).toEqual({
        user: null,
        token: null,
        isAuthenticated: false,
        loading: false,
        error: null,
      });
    });
  });

  describe('async actions', () => {
    it('should handle login.pending', () => {
      store.dispatch(
        login.pending('requestId', { email: '<EMAIL>', password: 'password' })
      );

      const state = store.getState().auth;
      expect(state.loading).toBe(true);
      expect(state.error).toBe(null);
    });

    it('should handle login.rejected', () => {
      const errorMessage = 'Login failed';
      const rejectedAction = {
        type: login.rejected.type,
        payload: errorMessage,
        error: { message: errorMessage },
        meta: {
          requestId: 'requestId',
          arg: { email: '<EMAIL>', password: 'password' }
        }
      };

      store.dispatch(rejectedAction as any);
      const state = store.getState().auth;

      expect(state.loading).toBe(false);
      expect(state.error).toBe('Login failed');
      expect(state.isAuthenticated).toBe(false);
    });

    it('should handle register.pending', () => {
      const registerData = {
        email: '<EMAIL>',
        password: 'password',
        name: 'Test User',
        role: 'user' as const,
      };

      store.dispatch(register.pending('requestId', registerData));
      const state = store.getState().auth;

      expect(state.loading).toBe(true);
      expect(state.error).toBe(null);
    });

    it('should handle register.rejected', () => {
      const errorMessage = 'Registration failed';
      const registerData = {
        email: '<EMAIL>',
        password: 'password',
        name: 'Test User',
        role: 'user' as const,
      };

      const rejectedAction = {
        type: register.rejected.type,
        payload: errorMessage,
        error: { message: errorMessage },
        meta: { requestId: 'requestId', arg: registerData }
      };

      store.dispatch(rejectedAction as any);
      const state = store.getState().auth;

      expect(state.loading).toBe(false);
      expect(state.error).toBe('Registration failed');
      expect(state.isAuthenticated).toBe(false);
    });
  });
});