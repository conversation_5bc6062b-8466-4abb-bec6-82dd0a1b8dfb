import { configureStore } from '@reduxjs/toolkit'; import predictiveSlice, { fetchChurnPredictions, fetchDemandForecast, fetchEscalationPredictions, fetchAnomalies, fetchWorkloadAnalytics, fetchPredictiveInsights, setFilters, clearError, setSocketConnected, addRealTimeAlert, updateSystemHealthScore, updateChurnPrediction, updateEscalationPrediction, addAnomaly, updateAnomalyStatus, updateDemandForecast, updateModelTrainingStatus, clearPredictions, clearAlerts, setActiveDashboard, setTimeRange, setAutoRefresh, updateLastRefresh, } from '../predictiveSlice'; // Mock the predictive service jest.mock('../../../services/predictive.service', () => ({ getChurnPredictions: jest.fn(), getDemandForecast: jest.fn(), getEscalationPredictions: jest.fn(), getAnomalies: jest.fn(), getWorkloadAnalytics: jest.fn(), getPredictiveInsights: jest.fn(), })); describe('predictiveSlice', () => { let store: ReturnType<typeof configureStore>; beforeEach(() => { store = configureStore({ reducer: { predictive: predictiveSlice, }, }); jest.clearAllMocks(); }); describe('initial state', () => { it('should have correct initial state', () => { const state = store.getState().predictive; expect(state).toEqual({ churnPredictions: [], demandForecast: [], escalationPredictions: [], anomalies: [], workloadAnalytics: null, modelStatus: null, insights: null, realTimeAlerts: [], systemHealthScore: 0, loading: { churn: false, demand: false, escalation: false, anomalies: false, workload: false, models: false, insights: false }, error: null, socketConnected: false, filters: { timeRange: '24h', riskLevel: 'all', showResolved: false }, trainingStatus: {} }); }); }); describe('synchronous actions', () => { it('should set filters', () => { const newFilters = { riskLevel: 'high', showResolved: true }; store.dispatch(setFilters(newFilters)); const state = store.getState().predictive; expect(state.filters.riskLevel).toBe('high'); expect(state.filters.showResolved).toBe(true); expect(state.filters.timeRange).toBe('24h'); // unchanged }); it('should set time range', () => { store.dispatch(setTimeRange('7d')); const state = store.getState().predictive; expect(state.filters.timeRange).toBe('7d'); }); it('should set socket connected', () => { store.dispatch(setSocketConnected(true)); const state = store.getState().predictive; expect(state.socketConnected).toBe(true); }); it('should add real time alert', () => { const mockAlert = { type: 'anomaly', severity: 'high', message: 'High CPU usage detected' }; store.dispatch(addRealTimeAlert(mockAlert)); const state = store.getState().predictive; expect(state.realTimeAlerts).toHaveLength(1); expect(state.realTimeAlerts[0]).toMatchObject(mockAlert); expect(state.realTimeAlerts[0].timestamp).toBeTruthy(); expect(state.realTimeAlerts[0].id).toBeTruthy(); }); it('should update system health score', () => { store.dispatch(updateSystemHealthScore(85)); const state = store.getState().predictive; expect(state.systemHealthScore).toBe(85); }); it('should add anomaly', () => { const mockAnomaly = { id: 'anomaly-1', type: 'performance', severity: 'high', description: 'High response time detected', detected_at: new Date().toISOString(), affected_systems: ['chat-service'], confidence: 0.95, status: 'active', metadata: {} }; store.dispatch(addAnomaly(mockAnomaly)); const state = store.getState().predictive; expect(state.anomalies).toHaveLength(1); expect(state.anomalies[0]).toEqual(mockAnomaly); }); it('should update anomaly status', () => { // First add an anomaly const mockAnomaly = { id: 'anomaly-1', type: 'performance', severity: 'medium', description: 'Test anomaly', detected_at: new Date().toISOString(), affected_systems: ['test-service'], confidence: 0.8, status: 'active', metadata: {} }; store.dispatch(addAnomaly(mockAnomaly)); // Then update its status store.dispatch(updateAnomalyStatus({ anomalyId: 'anomaly-1', status: 'resolved', notes: 'Fixed by restarting service' })); const state = store.getState().predictive; expect(state.anomalies[0].status).toBe('resolved'); expect(state.anomalies[0].acknowledgment_notes).toBe('Fixed by restarting service'); expect(state.anomalies[0].acknowledged_at).toBeTruthy(); }); it('should update churn prediction', () => { const mockPrediction = { customer_id: 'cust-123', customer_name: 'John Doe', churn_probability: 0.75, risk_level: 'high', contributing_factors: [], recommended_actions: [], prediction_confidence: 0.9, last_updated: new Date().toISOString() }; store.dispatch(updateChurnPrediction(mockPrediction)); const state = store.getState().predictive; expect(state.churnPredictions).toHaveLength(1); expect(state.churnPredictions[0]).toEqual(mockPrediction); }); it('should clear error', () => { // First set an error by dispatching a failed action const errorAction = { type: fetchChurnPredictions.rejected.type, payload: 'Test error', meta: { requestId: 'test', arg: {} } }; store.dispatch(errorAction as any); // Then clear error store.dispatch(clearError()); const state = store.getState().predictive; expect(state.error).toBe(null); }); it('should clear predictions', () => { // First add some data store.dispatch(updateChurnPrediction({ customer_id: 'test', churn_probability: 0.8 })); store.dispatch(addAnomaly({ id: 'test', type: 'performance' })); // Then clear store.dispatch(clearPredictions()); const state = store.getState().predictive; expect(state.churnPredictions).toEqual([]); expect(state.escalationPredictions).toEqual([]); expect(state.anomalies).toEqual([]); expect(state.demandForecast).toEqual([]); }); it('should clear alerts', () => { // First add an alert store.dispatch(addRealTimeAlert({ type: 'test', message: 'Test alert' })); // Then clear store.dispatch(clearAlerts()); const state = store.getState().predictive; expect(state.realTimeAlerts).toEqual([]); }); }); describe('async actions', () => { it('should handle fetchChurnPredictions.pending', () => { store.dispatch(fetchChurnPredictions.pending('requestId', {})); const state = store.getState().predictive; expect(state.loading.churn).toBe(true); expect(state.error).toBe(null); }); it('should handle fetchChurnPredictions.rejected', () => { const errorMessage = 'Failed to fetch churn predictions'; const rejectedAction = { type: fetchChurnPredictions.rejected.type, payload: errorMessage, meta: { requestId: 'requestId', arg: {} } }; store.dispatch(rejectedAction as any); const state = store.getState().predictive; expect(state.loading.churn).toBe(false); expect(state.error).toBe(errorMessage); }); it('should handle fetchDemandForecast.pending', () => { store.dispatch(fetchDemandForecast.pending('requestId', {})); const state = store.getState().predictive; expect(state.loading.demand).toBe(true); }); it('should handle fetchEscalationPredictions.pending', () => { store.dispatch(fetchEscalationPredictions.pending('requestId', {})); const state = store.getState().predictive; expect(state.loading.escalation).toBe(true); }); it('should handle fetchAnomalies.pending', () => { store.dispatch(fetchAnomalies.pending('requestId', {})); const state = store.getState().predictive; expect(state.loading.anomalies).toBe(true); }); it('should handle fetchWorkloadAnalytics.pending', () => { store.dispatch(fetchWorkloadAnalytics.pending('requestId', {})); const state = store.getState().predictive; expect(state.loading.workload).toBe(true); }); it('should handle fetchPredictiveInsights.pending', () => { store.dispatch(fetchPredictiveInsights.pending('requestId', undefined)); const state = store.getState().predictive; expect(state.loading.insights).toBe(true); }); }); describe('edge cases', () => { it('should handle multiple anomalies', () => { const criticalAnomaly = { id: 'anomaly-1', type: 'security', severity: 'critical', description: 'Security breach detected', detected_at: new Date().toISOString(), affected_systems: ['auth-service'], confidence: 0.99, status: 'active', metadata: {} }; const lowAnomaly = { id: 'anomaly-2', type: 'performance', severity: 'low', description: 'Minor performance issue', detected_at: new Date().toISOString(), affected_systems: ['cache-service'], confidence: 0.7, status: 'active', metadata: {} }; store.dispatch(addAnomaly(criticalAnomaly)); store.dispatch(addAnomaly(lowAnomaly)); const state = store.getState().predictive; expect(state.anomalies).toHaveLength(2); expect(state.anomalies[0]).toEqual(lowAnomaly); // Most recent first (unshift) expect(state.anomalies[1]).toEqual(criticalAnomaly); }); it('should handle partial filter updates', () => { store.dispatch(setFilters({ riskLevel: 'medium' })); const state = store.getState().predictive; expect(state.filters.riskLevel).toBe('medium'); expect(state.filters.timeRange).toBe('24h'); // Should remain unchanged expect(state.filters.showResolved).toBe(false); // Should remain unchanged }); it('should limit real-time alerts to 50', () => { // Add 55 alerts for (let i = 0; i < 55; i++) { store.dispatch(addRealTimeAlert({ type: 'test', message: `Alert ${i}`, severity: 'low' })); } const state = store.getState().predictive; expect(state.realTimeAlerts).toHaveLength(50); expect(state.realTimeAlerts[0].message).toBe('Alert 54'); // Most recent first }); it('should update existing churn prediction', () => { const initialPrediction = { customer_id: 'cust-123', churn_probability: 0.5, risk_level: 'medium' }; const updatedPrediction = { customer_id: 'cust-123', churn_probability: 0.8, risk_level: 'high' }; store.dispatch(updateChurnPrediction(initialPrediction)); store.dispatch(updateChurnPrediction(updatedPrediction)); const state = store.getState().predictive; expect(state.churnPredictions).toHaveLength(1); expect(state.churnPredictions[0].churn_probability).toBe(0.8); expect(state.churnPredictions[0].risk_level).toBe('high'); }); }); });