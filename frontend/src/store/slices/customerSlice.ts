import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

interface CustomerState {
  profile: any;
  invoices: any[];
  notifications: any[];
  loading: boolean;
  error: string | null;
}

const initialState: CustomerState = {
  profile: null,
  invoices: [],
  notifications: [],
  loading: false,
  error: null,
};

// Mock customer service
const mockCustomerService = {
  getCustomerProfile: async () => ({
    id: '1',
    name: 'Test Customer',
    email: '<EMAIL>',
    plan: 'Free Mobile 100GB',
    usage: { data: 50, calls: 120, sms: 45 }
  }),
  getInvoices: async () => [],
  getNotifications: async () => []
};

export const fetchCustomerProfile = createAsyncThunk(
  'customer/fetchProfile',
  async (_, { rejectWithValue }) => {
    try {
      const response = await mockCustomerService.getCustomerProfile();
      return response;
    } catch (error: any) {
      return rejectWithValue('Erreur de chargement du profil');
    }
  }
);

export const fetchInvoices = createAsyncThunk(
  'customer/fetchInvoices',
  async (_, { rejectWithValue }) => {
    try {
      const response = await mockCustomerService.getInvoices();
      return response;
    } catch (error: any) {
      return rejectWithValue('Erreur de chargement des factures');
    }
  }
);

export const fetchNotifications = createAsyncThunk(
  'customer/fetchNotifications',
  async (_, { rejectWithValue }) => {
    try {
      const response = await mockCustomerService.getNotifications();
      return response;
    } catch (error: any) {
      return rejectWithValue('Erreur de chargement des notifications');
    }
  }
);

const customerSlice = createSlice({
  name: 'customer',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    addNotification: (state, action) => {
      state.notifications.unshift(action.payload);
    },
    removeNotification: (state, action) => {
      state.notifications = state.notifications.filter((n: any) => n._id !== action.payload);
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch profile
      .addCase(fetchCustomerProfile.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCustomerProfile.fulfilled, (state, action) => {
        state.loading = false;
        state.profile = action.payload;
      })
      .addCase(fetchCustomerProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Fetch invoices
      .addCase(fetchInvoices.fulfilled, (state, action) => {
        state.invoices = action.payload;
      })

      // Fetch notifications
      .addCase(fetchNotifications.fulfilled, (state, action) => {
        state.notifications = action.payload;
      });
  },
});

export const { clearError, addNotification, removeNotification } = customerSlice.actions;
export default customerSlice.reducer;