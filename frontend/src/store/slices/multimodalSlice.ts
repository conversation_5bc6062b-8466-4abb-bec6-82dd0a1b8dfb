/** * ============================================= * MULTIMODAL REDUX SLICE * State management for multimodal processing * Handles text, voice, and image analysis with real-time updates * ============================================= */ import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'; import { multimodalService } from '../../services/multimodal.service'; // Types export interface MultimodalState { // Processing states isProcessing: boolean; processingType: 'text' | 'voice' | 'image' | 'multimodal' | null; processingProgress: number; // Input data textInput: string; voiceInput: { isRecording: boolean; audioBlob: Blob | null; duration: number; waveformData: number[]; audioQuality: AudioQuality | null; }; imageInput: { file: File | null; preview: string | null; metadata: ImageMetadata | null; }; // Processing results textAnalysis: TextAnalysisResult | null; voiceAnalysis: VoiceAnalysisResult | null; imageAnalysis: ImageAnalysisResult | null; fusionResult: FusionResult | null; // Real-time updates streamingResults: StreamingResult[]; // UI state activeTab: 'text' | 'voice' | 'image' | 'multimodal'; showResults: boolean; expandedSections: string[]; // WebSocket connection isConnected: boolean; connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error'; // Error handling error: string | null; retryCount: number; // History processingHistory: ProcessingHistoryItem[]; } export interface AudioQuality { score: number; rating: 'excellent' | 'good' | 'fair' | 'poor'; issues: string[]; recommendations: string[]; } export interface ImageMetadata { width: number; height: number; size: number; format: string; quality: number; } export interface TextAnalysisResult { processingId: string; intent: { primary: string; confidence: number; alternatives: Array<{ intent: string; confidence: number }>; }; sentiment: { label: 'positive' | 'negative' | 'neutral'; score: number; confidence: number; }; emotion: { dominant: string; confidence: number; emotions: Record<string, number>; }; entities: Record<string, string[]>; keywords: string[]; urgency: { level: 'low' | 'medium' | 'high' | 'urgent'; score: number; }; confidence: number; processingTime: number; } export interface VoiceAnalysisResult { processingId: string; transcription: { text: string; confidence: number; words: Array<{ text: string; confidence: number; startTime?: number; endTime?: number; }>; }; emotion: { dominant: string; confidence: number; emotions: Record<string, number>; audioCharacteristics: { pitch: number; energy: number; tempo: number; }; }; audioQuality: AudioQuality; voiceActivity: { hasVoice: boolean; voicePercentage: number; confidence: number; }; confidence: number; processingTime: number; } export interface ImageAnalysisResult { processingId: string; ocr: { text: string; confidence: number; words: Array<{ text: string; confidence: number; boundingBox: any; }>; }; objects: { objects: Array<{ name: string; confidence: number; category: string; boundingBox: any; }>; }; faces: { faces: Array<{ emotions: Record<string, number>; dominantEmotion: string; confidence: number; boundingBox: any; }>; totalFaces: number; }; products: { products: Array<{ name: string; category: string; confidence: number; source: string; }>; }; quality: { score: number; rating: string; issues: string[]; recommendations: string[]; }; confidence: number; processingTime: number; } export interface FusionResult { fusionId: string; context: string; confidence: number; intent: { primary: string; confidence: number; sources: Array<{ source: string; confidence: number }>; }; sentiment: { label: string; confidence: number; sources: Array<{ source: string; confidence: number }>; }; emotion: { dominant: string; confidence: number; sources: Array<{ source: string; confidence: number }>; }; urgency: { level: string; confidence: number; }; entities: Record<string, string[]>; products: Array<{ name: string; category: string; confidence: number; }>; keyInsights: Array<{ type: string; insight: string; confidence: number; actionable: boolean; }>; recommendations: Array<{ type: string; message: string; priority: string; }>; modalityContributions: Record<string, { weight: number; dataQuality: number; keyContributions: string[]; }>; processingTime: number; } export interface StreamingResult { id: string; type: 'text' | 'voice' | 'image'; timestamp: string; data: any; isPartial: boolean; } export interface ProcessingHistoryItem { id: string; timestamp: string; type: 'text' | 'voice' | 'image' | 'multimodal'; input: any; result: any; processingTime: number; confidence: number; } // Initial state const initialState: MultimodalState = { isProcessing: false, processingType: null, processingProgress: 0, textInput: '', voiceInput: { isRecording: false, audioBlob: null, duration: 0, waveformData: [], audioQuality: null }, imageInput: { file: null, preview: null, metadata: null }, textAnalysis: null, voiceAnalysis: null, imageAnalysis: null, fusionResult: null, streamingResults: [], activeTab: 'text', showResults: false, expandedSections: [], isConnected: false, connectionStatus: 'disconnected', error: null, retryCount: 0, processingHistory: [] }; // Async thunks export const processText = createAsyncThunk( 'multimodal/processText', async (params: { text: string; options?: any }) => { const response = await multimodalService.processText(params.text, params.options); return response; } ); export const processVoice = createAsyncThunk( 'multimodal/processVoice', async (params: { audioBlob: Blob; options?: any }) => { const response = await multimodalService.processVoice(params.audioBlob, params.options); return response; } ); export const processImage = createAsyncThunk( 'multimodal/processImage', async (params: { imageFile: File; options?: any }) => { const response = await multimodalService.processImage(params.imageFile, params.options); return response; } ); export const processMultimodal = createAsyncThunk( 'multimodal/processMultimodal', async (params: { text?: string; audioBlob?: Blob; imageFile?: File; options?: any; }) => { const response = await multimodalService.processMultimodal(params); return response; } ); export const connectWebSocket = createAsyncThunk( 'multimodal/connectWebSocket', async (userId: string) => { await multimodalService.connect(userId); return true; } ); // Slice const multimodalSlice = createSlice({ name: 'multimodal', initialState, reducers: { // Input management setTextInput: (state, action: PayloadAction<string>) => { state.textInput = action.payload; }, setVoiceRecording: (state, action: PayloadAction<boolean>) => { state.voiceInput.isRecording = action.payload; if (action.payload) { state.voiceInput.duration = 0; state.voiceInput.waveformData = []; } }, updateVoiceRecording: (state, action: PayloadAction<{ duration?: number; waveformData?: number[]; audioQuality?: AudioQuality; }>) => { if (action.payload.duration !== undefined) { state.voiceInput.duration = action.payload.duration; } if (action.payload.waveformData) { state.voiceInput.waveformData = action.payload.waveformData; } if (action.payload.audioQuality) { state.voiceInput.audioQuality = action.payload.audioQuality; } }, setVoiceBlob: (state, action: PayloadAction<Blob | null>) => { state.voiceInput.audioBlob = action.payload; }, setImageFile: (state, action: PayloadAction<{ file: File | null; preview: string | null; metadata: ImageMetadata | null; }>) => { state.imageInput = action.payload; }, // UI state setActiveTab: (state, action: PayloadAction<'text' | 'voice' | 'image' | 'multimodal'>) => { state.activeTab = action.payload; }, setShowResults: (state, action: PayloadAction<boolean>) => { state.showResults = action.payload; }, toggleExpandedSection: (state, action: PayloadAction<string>) => { const section = action.payload; const index = state.expandedSections.indexOf(section); if (index > -1) { state.expandedSections.splice(index, 1); } else { state.expandedSections.push(section); } }, // Processing state setProcessingProgress: (state, action: PayloadAction<number>) => { state.processingProgress = action.payload; }, // Streaming results addStreamingResult: (state, action: PayloadAction<StreamingResult>) => { state.streamingResults.push(action.payload); // Keep only last 50 streaming results if (state.streamingResults.length > 50) { state.streamingResults = state.streamingResults.slice(-50); } }, clearStreamingResults: (state) => { state.streamingResults = []; }, // WebSocket connection setConnectionStatus: (state, action: PayloadAction<MultimodalState['connectionStatus']>) => { state.connectionStatus = action.payload; state.isConnected = action.payload === 'connected'; }, // Error handling setError: (state, action: PayloadAction<string | null>) => { state.error = action.payload; if (action.payload) { state.retryCount += 1; } }, clearError: (state) => { state.error = null; state.retryCount = 0; }, // History management addToHistory: (state, action: PayloadAction<ProcessingHistoryItem>) => { state.processingHistory.unshift(action.payload); // Keep only last 20 items if (state.processingHistory.length > 20) { state.processingHistory = state.processingHistory.slice(0, 20); } }, clearHistory: (state) => { state.processingHistory = []; }, // Reset state resetMultimodal: (state) => { return { ...initialState, isConnected: state.isConnected, connectionStatus: state.connectionStatus }; }, clearResults: (state) => { state.textAnalysis = null; state.voiceAnalysis = null; state.imageAnalysis = null; state.fusionResult = null; state.showResults = false; state.streamingResults = []; } }, extraReducers: (builder) => { // Process text builder .addCase(processText.pending, (state) => { state.isProcessing = true; state.processingType = 'text'; state.processingProgress = 0; state.error = null; }) .addCase(processText.fulfilled, (state, action) => { state.isProcessing = false; state.processingType = null; state.processingProgress = 100; state.textAnalysis = action.payload; state.showResults = true; // Add to history const historyItem: ProcessingHistoryItem = { id: action.payload.processingId, timestamp: new Date().toISOString(), type: 'text', input: state.textInput, result: action.payload, processingTime: action.payload.processingTime, confidence: action.payload.confidence }; state.processingHistory.unshift(historyItem); }) .addCase(processText.rejected, (state, action) => { state.isProcessing = false; state.processingType = null; state.error = action.error.message || 'Text processing failed'; }); // Process voice builder .addCase(processVoice.pending, (state) => { state.isProcessing = true; state.processingType = 'voice'; state.processingProgress = 0; state.error = null; }) .addCase(processVoice.fulfilled, (state, action) => { state.isProcessing = false; state.processingType = null; state.processingProgress = 100; state.voiceAnalysis = action.payload; state.showResults = true; // Add to history const historyItem: ProcessingHistoryItem = { id: action.payload.processingId, timestamp: new Date().toISOString(), type: 'voice', input: { duration: state.voiceInput.duration }, result: action.payload, processingTime: action.payload.processingTime, confidence: action.payload.confidence }; state.processingHistory.unshift(historyItem); }) .addCase(processVoice.rejected, (state, action) => { state.isProcessing = false; state.processingType = null; state.error = action.error.message || 'Voice processing failed'; }); // Process image builder .addCase(processImage.pending, (state) => { state.isProcessing = true; state.processingType = 'image'; state.processingProgress = 0; state.error = null; }) .addCase(processImage.fulfilled, (state, action) => { state.isProcessing = false; state.processingType = null; state.processingProgress = 100; state.imageAnalysis = action.payload; state.showResults = true; // Add to history const historyItem: ProcessingHistoryItem = { id: action.payload.processingId, timestamp: new Date().toISOString(), type: 'image', input: { filename: state.imageInput.file?.name, size: state.imageInput.file?.size }, result: action.payload, processingTime: action.payload.processingTime, confidence: action.payload.confidence }; state.processingHistory.unshift(historyItem); }) .addCase(processImage.rejected, (state, action) => { state.isProcessing = false; state.processingType = null; state.error = action.error.message || 'Image processing failed'; }); // Process multimodal builder .addCase(processMultimodal.pending, (state) => { state.isProcessing = true; state.processingType = 'multimodal'; state.processingProgress = 0; state.error = null; }) .addCase(processMultimodal.fulfilled, (state, action) => { state.isProcessing = false; state.processingType = null; state.processingProgress = 100; state.fusionResult = action.payload; state.showResults = true; // Add to history const historyItem: ProcessingHistoryItem = { id: action.payload.fusionId, timestamp: new Date().toISOString(), type: 'multimodal', input: { hasText: !!state.textInput, hasVoice: !!state.voiceInput.audioBlob, hasImage: !!state.imageInput.file }, result: action.payload, processingTime: action.payload.processingTime, confidence: action.payload.confidence }; state.processingHistory.unshift(historyItem); }) .addCase(processMultimodal.rejected, (state, action) => { state.isProcessing = false; state.processingType = null; state.error = action.error.message || 'Multimodal processing failed'; }); // Connect WebSocket builder .addCase(connectWebSocket.pending, (state) => { state.connectionStatus = 'connecting'; }) .addCase(connectWebSocket.fulfilled, (state) => { state.connectionStatus = 'connected'; state.isConnected = true; state.error = null; }) .addCase(connectWebSocket.rejected, (state, action) => { state.connectionStatus = 'error'; state.isConnected = false; state.error = action.error.message || 'WebSocket connection failed'; }); } }); export const { setTextInput, setVoiceRecording, updateVoiceRecording, setVoiceBlob, setImageFile, setActiveTab, setShowResults, toggleExpandedSection, setProcessingProgress, addStreamingResult, clearStreamingResults, setConnectionStatus, setError, clearError, addToHistory, clearHistory, resetMultimodal, clearResults } = multimodalSlice.actions; export default multimodalSlice.reducer;