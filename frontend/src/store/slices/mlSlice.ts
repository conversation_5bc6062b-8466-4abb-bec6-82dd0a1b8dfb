/** * Redux Slice pour les fonctionnalités ML * Free Mobile Chatbot Dashboard - Phase 3 Frontend Implementation */ import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'; import { MLDashboardState, MLDashboardFilters, ConversationClassification, AdminAlert, PriorityQueueItem, MLMetricsResponse, ConversationCategory, AlertSeverity, AlertStatus } from '../../types/ml'; import mlService from '../../services/ml.service'; // État initial const initialState: MLDashboardState = { classifications: [], alerts: [], priorityQueue: [], metrics: null, filters: { timeRange: '24h', minPriority: 0, showOnlyUnvalidated: false }, loading: { classifications: false, alerts: false, priorityQueue: false, metrics: false }, error: { classifications: null, alerts: null, priorityQueue: null, metrics: null }, lastUpdated: { classifications: null, alerts: null, priorityQueue: null, metrics: null } }; // Actions asynchrones export const fetchPriorityQueue = createAsyncThunk( 'ml/fetchPriorityQueue', async (params: { limit?: number; minPriority?: number; category?: ConversationCategory; } = {}) => { const response = await mlService.getPriorityQueue(params); return response.queue; } ); export const fetchPerformanceMetrics = createAsyncThunk( 'ml/fetchPerformanceMetrics', async (timeRange: '1h' | '24h' | '7d' | '30d' = '24h') => { const response = await mlService.getPerformanceMetrics({ timeRange }); return response; } ); export const fetchAlerts = createAsyncThunk( 'ml/fetchAlerts', async (params: { status?: AlertStatus; severity?: AlertSeverity; limit?: number; } = {}) => { const response = await mlService.getAlerts(params); return response.alerts; } ); export const classifyConversation = createAsyncThunk( 'ml/classifyConversation', async (params: { conversationId: string; forceReprocess?: boolean }) => { const response = await mlService.classifyConversation(params); return response.classification; } ); export const validateClassification = createAsyncThunk( 'ml/validateClassification', async (params: { classificationId: string; isCorrect: boolean; correctedCategory?: ConversationCategory; feedback?: string; confidence?: number; }) => { const { classificationId, ...request } = params; const response = await mlService.validateClassification(classificationId, request); return response.classification; } ); export const acknowledgeAlert = createAsyncThunk( 'ml/acknowledgeAlert', async (alertId: string) => { const response = await mlService.acknowledgeAlert(alertId); return response.alert; } ); export const fetchClassificationStats = createAsyncThunk( 'ml/fetchClassificationStats', async (params: { startDate?: string; endDate?: string; groupBy?: 'hour' | 'day' | 'week' | 'month'; category?: string; } = {}) => { const response = await mlService.getClassificationStats( params.startDate, params.endDate, params.groupBy, params.category ); return response.stats; } ); // Slice Redux const mlSlice = createSlice({ name: 'ml', initialState, reducers: { // Mise à jour des filtres updateFilters: (state, action: PayloadAction<Partial<MLDashboardFilters>>) => { state.filters = { ...state.filters, ...action.payload }; }, // Réinitialisation des filtres resetFilters: (state) => { state.filters = initialState.filters; }, // Ajout d'une nouvelle classification (WebSocket) addClassification: (state, action: PayloadAction<ConversationClassification>) => { state.classifications.unshift(action.payload); // Garder seulement les 100 dernières if (state.classifications.length > 100) { state.classifications = state.classifications.slice(0, 100); } state.lastUpdated.classifications = new Date().toISOString(); }, // Ajout d'une nouvelle alerte (WebSocket) addAlert: (state, action: PayloadAction<AdminAlert>) => { state.alerts.unshift(action.payload); // Garder seulement les 100 dernières if (state.alerts.length > 100) { state.alerts = state.alerts.slice(0, 100); } state.lastUpdated.alerts = new Date().toISOString(); }, // Mise à jour des métriques (WebSocket) updateMetrics: (state, action: PayloadAction<MLMetricsResponse>) => { state.metrics = action.payload; state.lastUpdated.metrics = new Date().toISOString(); }, // Mise à jour de la queue de priorité (WebSocket) updatePriorityQueue: (state, action: PayloadAction<PriorityQueueItem[]>) => { state.priorityQueue = action.payload; state.lastUpdated.priorityQueue = new Date().toISOString(); }, // Mise à jour d'une classification existante updateClassification: (state, action: PayloadAction<ConversationClassification>) => { const index = state.classifications.findIndex(c => c._id === action.payload._id); if (index !== -1) { state.classifications[index] = action.payload; } }, // Mise à jour d'une alerte existante updateAlert: (state, action: PayloadAction<AdminAlert>) => { const index = state.alerts.findIndex(a => a._id === action.payload._id); if (index !== -1) { state.alerts[index] = action.payload; } }, // Suppression d'une alerte removeAlert: (state, action: PayloadAction<string>) => { state.alerts = state.alerts.filter(a => a._id !== action.payload); }, // Réinitialisation des erreurs clearErrors: (state) => { state.error = { classifications: null, alerts: null, priorityQueue: null, metrics: null }; }, // Réinitialisation de l'état resetState: (state) => { return initialState; } }, extraReducers: (builder) => { // Fetch Priority Queue builder .addCase(fetchPriorityQueue.pending, (state) => { state.loading.priorityQueue = true; state.error.priorityQueue = null; }) .addCase(fetchPriorityQueue.fulfilled, (state, action) => { state.loading.priorityQueue = false; state.priorityQueue = action.payload; state.lastUpdated.priorityQueue = new Date().toISOString(); }) .addCase(fetchPriorityQueue.rejected, (state, action) => { state.loading.priorityQueue = false; state.error.priorityQueue = action.error.message || 'Erreur lors du chargement de la queue de priorité'; }); // Fetch Performance Metrics builder .addCase(fetchPerformanceMetrics.pending, (state) => { state.loading.metrics = true; state.error.metrics = null; }) .addCase(fetchPerformanceMetrics.fulfilled, (state, action) => { state.loading.metrics = false; // Transform PerformanceMetricsResponse to MLMetricsResponse state.metrics = { ml: action.payload.mlService, dashboard: { recentClassifications: action.payload.classifications?.total || 0, activeAlerts: action.payload.alerts?.totalActive || 0, highPriorityConversations: action.payload.classifications?.highPriorityCount || 0, connectedUsers: 0, timestamp: action.payload.timestamp } }; state.lastUpdated.metrics = new Date().toISOString(); }) .addCase(fetchPerformanceMetrics.rejected, (state, action) => { state.loading.metrics = false; state.error.metrics = action.error.message || 'Erreur lors du chargement des métriques'; }); // Fetch Alerts builder .addCase(fetchAlerts.pending, (state) => { state.loading.alerts = true; state.error.alerts = null; }) .addCase(fetchAlerts.fulfilled, (state, action) => { state.loading.alerts = false; state.alerts = action.payload; state.lastUpdated.alerts = new Date().toISOString(); }) .addCase(fetchAlerts.rejected, (state, action) => { state.loading.alerts = false; state.error.alerts = action.error.message || 'Erreur lors du chargement des alertes'; }); // Classify Conversation builder .addCase(classifyConversation.pending, (state) => { state.loading.classifications = true; state.error.classifications = null; }) .addCase(classifyConversation.fulfilled, (state, action) => { state.loading.classifications = false; // Ajouter ou mettre à jour la classification const existingIndex = state.classifications.findIndex( c => c.conversationId === action.payload.conversationId ); if (existingIndex !== -1) { state.classifications[existingIndex] = action.payload; } else { state.classifications.unshift(action.payload); } state.lastUpdated.classifications = new Date().toISOString(); }) .addCase(classifyConversation.rejected, (state, action) => { state.loading.classifications = false; state.error.classifications = action.error.message || 'Erreur lors de la classification'; }); // Validate Classification builder .addCase(validateClassification.fulfilled, (state, action) => { const index = state.classifications.findIndex(c => c._id === action.payload._id); if (index !== -1) { state.classifications[index] = action.payload; } }); // Acknowledge Alert builder .addCase(acknowledgeAlert.fulfilled, (state, action) => { const index = state.alerts.findIndex(a => a._id === action.payload._id); if (index !== -1) { state.alerts[index] = action.payload; } }); } }); // Actions export const { updateFilters, resetFilters, addClassification, addAlert, updateMetrics, updatePriorityQueue, updateClassification, updateAlert, removeAlert, clearErrors, resetState } = mlSlice.actions; // Sélecteurs export const selectMLState = (state: { ml: MLDashboardState }) => state.ml; export const selectClassifications = (state: { ml: MLDashboardState }) => state.ml.classifications; export const selectAlerts = (state: { ml: MLDashboardState }) => state.ml.alerts; export const selectPriorityQueue = (state: { ml: MLDashboardState }) => state.ml.priorityQueue; export const selectMetrics = (state: { ml: MLDashboardState }) => state.ml.metrics; export const selectFilters = (state: { ml: MLDashboardState }) => state.ml.filters; export const selectLoading = (state: { ml: MLDashboardState }) => state.ml.loading; export const selectErrors = (state: { ml: MLDashboardState }) => state.ml.error; // Sélecteurs dérivés export const selectHighPriorityClassifications = (state: { ml: MLDashboardState }) => state.ml.classifications.filter(c => c.priorityScore >= 80); export const selectUnvalidatedClassifications = (state: { ml: MLDashboardState }) => state.ml.classifications.filter(c => !c.humanValidated); export const selectActiveAlerts = (state: { ml: MLDashboardState }) => state.ml.alerts.filter(a => ['ACTIVE', 'ACKNOWLEDGED', 'IN_PROGRESS'].includes(a.status)); export const selectCriticalAlerts = (state: { ml: MLDashboardState }) => state.ml.alerts.filter(a => a.severity === 'CRITICAL'); export const selectFilteredClassifications = (state: { ml: MLDashboardState }) => { const { classifications, filters } = state.ml; return classifications.filter(classification => { // Filtre par priorité minimale if (classification.priorityScore < filters.minPriority) { return false; } // Filtre par catégorie if (filters.category && classification.category !== filters.category) { return false; } // Filtre par validation humaine if (filters.showOnlyUnvalidated && classification.humanValidated) { return false; } return true; }); }; export default mlSlice.reducer;