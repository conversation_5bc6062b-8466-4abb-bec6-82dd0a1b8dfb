/** * ============================================= * [ANALYTICS] ANALYTICS REDUX SLICE * State management for analytics and metrics * Handles real-time analytics, KPIs, and reporting * ============================================= */ import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'; // Types interface AnalyticsMetrics { totalConversations: number; activeUsers: number; averageResponseTime: number; satisfactionScore: number; resolutionRate: number; escalationRate: number; } interface RealTimeMetrics { timestamp: string; activeConnections: number; messagesPerMinute: number; systemLoad: number; lastUpdated: string; } interface AnalyticsState { metrics: AnalyticsMetrics; realTimeMetrics: RealTimeMetrics; loading: boolean; error: string | null; lastUpdated: string | null; } // Initial state const initialState: AnalyticsState = { metrics: { totalConversations: 0, activeUsers: 0, averageResponseTime: 0, satisfactionScore: 0, resolutionRate: 0, escalationRate: 0, }, realTimeMetrics: { timestamp: '', activeConnections: 0, messagesPerMinute: 0, systemLoad: 0, lastUpdated: '', }, loading: false, error: null, lastUpdated: null, }; // Async thunks export const fetchAnalytics = createAsyncThunk( 'analytics/fetchAnalytics', async (timeframe: string = '24h', { rejectWithValue }) => { try { // Mock data for now - replace with actual API call const mockData = { metrics: { totalConversations: 1250, activeUsers: 89, averageResponseTime: 2.3, satisfactionScore: 4.2, resolutionRate: 87.5, escalationRate: 12.5, }, realTimeMetrics: { timestamp: new Date().toISOString(), activeConnections: 45, messagesPerMinute: 23, systemLoad: 65, lastUpdated: new Date().toISOString(), }, }; return mockData; } catch (error: unknown) { const errorMessage = error instanceof Error ? error.message : 'Failed to fetch analytics'; return rejectWithValue(errorMessage); } } ); // Analytics slice const analyticsSlice = createSlice({ name: 'analytics', initialState, reducers: { updateRealTimeMetrics: (state, action: PayloadAction<RealTimeMetrics>) => { state.realTimeMetrics = action.payload; state.lastUpdated = new Date().toISOString(); }, clearError: (state) => { state.error = null; }, resetAnalytics: () => initialState, }, extraReducers: (builder) => { builder .addCase(fetchAnalytics.pending, (state) => { state.loading = true; state.error = null; }) .addCase(fetchAnalytics.fulfilled, (state, action) => { state.loading = false; state.metrics = action.payload.metrics; state.realTimeMetrics = action.payload.realTimeMetrics; state.lastUpdated = new Date().toISOString(); }) .addCase(fetchAnalytics.rejected, (state, action) => { state.loading = false; state.error = action.payload as string; }); }, }); export const { updateRealTimeMetrics, clearError, resetAnalytics } = analyticsSlice.actions; export default analyticsSlice.reducer;