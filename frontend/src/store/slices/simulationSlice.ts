/** * ============================================= * SIMULATION REDUX SLICE * State management for agent training simulations * Integrates with existing AI services for realistic scenarios * ============================================= */ import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'; // Types for simulation system export interface CustomerProfile { id: string; name: string; age: number; personality: 'calm' | 'frustrated' | 'confused' | 'demanding' | 'friendly'; issue_type: 'billing' | 'technical' | 'account' | 'service' | 'complaint'; complexity: 'simple' | 'moderate' | 'complex'; platform: 'whatsapp' | 'facebook' | 'instagram' | 'twitter' | 'call'; history: CustomerInteraction[]; satisfaction_threshold: number; } export interface CustomerInteraction { timestamp: string; message: string; sentiment: number; // -1 to 1 satisfaction: number; // 0 to 10 } export interface SimulationScenario { id: string; title: string; description: string; difficulty: 'beginner' | 'intermediate' | 'expert'; category: 'billing' | 'technical' | 'sales' | 'retention' | 'complaint'; customer_profile: CustomerProfile; expected_resolution_time: number; // minutes success_criteria: SuccessCriteria; learning_objectives: string[]; tags: string[]; } export interface SuccessCriteria { min_satisfaction: number; max_resolution_time: number; required_actions: string[]; avoid_actions: string[]; } export interface SimulationSession { id: string; scenario_id: string; agent_id: string; start_time: string; end_time?: string; status: 'active' | 'completed' | 'paused' | 'abandoned'; messages: SimulationMessage[]; performance_metrics: PerformanceMetrics; ai_feedback: AIFeedback[]; } export interface SimulationMessage { id: string; sender: 'agent' | 'customer' | 'system'; content: string; timestamp: string; metadata?: { sentiment?: number; confidence?: number; ai_suggestion_used?: boolean; response_time?: number; }; } export interface PerformanceMetrics { empathy_score: number; // 0-100 efficiency_score: number; // 0-100 accuracy_score: number; // 0-100 overall_score: number; // 0-100 resolution_time: number; // minutes customer_satisfaction: number; // 0-10 ai_suggestions_used: number; ai_suggestions_available: number; } export interface AIFeedback { id: string; type: 'suggestion' | 'warning' | 'praise' | 'improvement'; message: string; timestamp: string; severity: 'low' | 'medium' | 'high'; category: 'empathy' | 'efficiency' | 'accuracy' | 'communication'; } export interface SimulationState { // Current session current_session: SimulationSession | null; is_active: boolean; // Available scenarios scenarios: SimulationScenario[]; filtered_scenarios: SimulationScenario[]; scenario_filters: { difficulty: string[]; category: string[]; tags: string[]; }; // Session history completed_sessions: SimulationSession[]; session_history: SimulationSession[]; // Performance tracking agent_progress: AgentProgress; leaderboard: LeaderboardEntry[]; // AI integration ai_suggestions: AISuggestion[]; real_time_feedback: AIFeedback[]; // UI state selected_scenario: string | null; show_ai_panel: boolean; show_performance_panel: boolean; simulation_speed: 'normal' | 'fast' | 'slow'; // Loading states loading: { scenarios: boolean; session: boolean; feedback: boolean; }; error: string | null; } export interface AgentProgress { agent_id: string; total_sessions: number; completed_sessions: number; average_score: number; skill_levels: { empathy: number; efficiency: number; accuracy: number; communication: number; }; badges_earned: Badge[]; next_milestone: Milestone; } export interface Badge { id: string; name: string; description: string; icon: string; earned_date: string; rarity: 'common' | 'rare' | 'epic' | 'legendary'; } export interface Milestone { id: string; name: string; description: string; progress: number; // 0-100 target: number; reward: string; } export interface LeaderboardEntry { agent_id: string; agent_name: string; total_score: number; sessions_completed: number; average_satisfaction: number; rank: number; badge_count: number; } export interface AISuggestion { id: string; type: 'response' | 'action' | 'escalation' | 'information'; content: string; confidence: number; // 0-1 reasoning: string; timestamp: string; used: boolean; } // Async thunks for simulation operations export const fetchScenarios = createAsyncThunk( 'simulation/fetchScenarios', async (filters?: { difficulty?: string; category?: string }) => { // Integration with existing AI service const response = await fetch('/api/simulation/scenarios', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(filters || {}) }); return response.json(); } ); export const startSimulation = createAsyncThunk( 'simulation/startSimulation', async (scenario_id: string) => { const response = await fetch('/api/simulation/start', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ scenario_id }) }); return response.json(); } ); export const sendMessage = createAsyncThunk( 'simulation/sendMessage', async ({ session_id, message }: { session_id: string; message: string }) => { const response = await fetch('/api/simulation/message', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ session_id, message }) }); return response.json(); } ); export const endSimulation = createAsyncThunk( 'simulation/endSimulation', async (session_id: string) => { const response = await fetch('/api/simulation/end', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ session_id }) }); return response.json(); } ); export const fetchAgentProgress = createAsyncThunk( 'simulation/fetchAgentProgress', async (agent_id: string) => { const response = await fetch(`/api/simulation/progress/${agent_id}`); return response.json(); } ); // Initial state const initialState: SimulationState = { current_session: null, is_active: false, scenarios: [], filtered_scenarios: [], scenario_filters: { difficulty: [], category: [], tags: [] }, completed_sessions: [], session_history: [], agent_progress: { agent_id: '', total_sessions: 0, completed_sessions: 0, average_score: 0, skill_levels: { empathy: 0, efficiency: 0, accuracy: 0, communication: 0 }, badges_earned: [], next_milestone: { id: '', name: '', description: '', progress: 0, target: 0, reward: '' } }, leaderboard: [], ai_suggestions: [], real_time_feedback: [], selected_scenario: null, show_ai_panel: true, show_performance_panel: false, simulation_speed: 'normal', loading: { scenarios: false, session: false, feedback: false }, error: null }; // Simulation slice const simulationSlice = createSlice({ name: 'simulation', initialState, reducers: { // UI actions setSelectedScenario: (state, action: PayloadAction<string | null>) => { state.selected_scenario = action.payload; }, toggleAIPanel: (state) => { state.show_ai_panel = !state.show_ai_panel; }, togglePerformancePanel: (state) => { state.show_performance_panel = !state.show_performance_panel; }, setSimulationSpeed: (state, action: PayloadAction<'normal' | 'fast' | 'slow'>) => { state.simulation_speed = action.payload; }, // Filter actions setScenarioFilters: (state, action: PayloadAction<Partial<SimulationState['scenario_filters']>>) => { state.scenario_filters = { ...state.scenario_filters, ...action.payload }; // Apply filters state.filtered_scenarios = state.scenarios.filter(scenario => { const { difficulty, category, tags } = state.scenario_filters; return ( (difficulty.length === 0 || difficulty.includes(scenario.difficulty)) && (category.length === 0 || category.includes(scenario.category)) && (tags.length === 0 || tags.some(tag => scenario.tags.includes(tag))) ); }); }, // Real-time updates addAISuggestion: (state, action: PayloadAction<AISuggestion>) => { state.ai_suggestions.unshift(action.payload); // Keep only last 10 suggestions if (state.ai_suggestions.length > 10) { state.ai_suggestions = state.ai_suggestions.slice(0, 10); } }, markSuggestionUsed: (state, action: PayloadAction<string>) => { const suggestion = state.ai_suggestions.find(s => s.id === action.payload); if (suggestion) { suggestion.used = true; } }, addRealTimeFeedback: (state, action: PayloadAction<AIFeedback>) => { state.real_time_feedback.unshift(action.payload); // Keep only last 20 feedback items if (state.real_time_feedback.length > 20) { state.real_time_feedback = state.real_time_feedback.slice(0, 20); } }, clearError: (state) => { state.error = null; }, resetSimulation: (state) => { state.current_session = null; state.is_active = false; state.ai_suggestions = []; state.real_time_feedback = []; state.error = null; } }, extraReducers: (builder) => { // Fetch scenarios builder .addCase(fetchScenarios.pending, (state) => { state.loading.scenarios = true; state.error = null; }) .addCase(fetchScenarios.fulfilled, (state, action) => { state.loading.scenarios = false; state.scenarios = action.payload.scenarios; state.filtered_scenarios = action.payload.scenarios; }) .addCase(fetchScenarios.rejected, (state, action) => { state.loading.scenarios = false; state.error = action.error.message || 'Failed to fetch scenarios'; }); // Start simulation builder .addCase(startSimulation.pending, (state) => { state.loading.session = true; state.error = null; }) .addCase(startSimulation.fulfilled, (state, action) => { state.loading.session = false; state.current_session = action.payload.session; state.is_active = true; }) .addCase(startSimulation.rejected, (state, action) => { state.loading.session = false; state.error = action.error.message || 'Failed to start simulation'; }); // Send message builder .addCase(sendMessage.fulfilled, (state, action) => { if (state.current_session) { state.current_session.messages.push(...action.payload.messages); state.current_session.performance_metrics = action.payload.performance_metrics; // Add AI suggestions if provided if (action.payload.ai_suggestions) { action.payload.ai_suggestions.forEach((suggestion: AISuggestion) => { state.ai_suggestions.unshift(suggestion); }); } // Add feedback if provided if (action.payload.feedback) { action.payload.feedback.forEach((feedback: AIFeedback) => { state.real_time_feedback.unshift(feedback); }); } } }); // End simulation builder .addCase(endSimulation.fulfilled, (state, action) => { if (state.current_session) { state.current_session.status = 'completed'; state.current_session.end_time = new Date().toISOString(); state.completed_sessions.unshift(state.current_session); state.session_history.unshift(state.current_session); } state.current_session = null; state.is_active = false; // Update agent progress if (action.payload.agent_progress) { state.agent_progress = action.payload.agent_progress; } }); // Fetch agent progress builder .addCase(fetchAgentProgress.fulfilled, (state, action) => { state.agent_progress = action.payload.progress; state.leaderboard = action.payload.leaderboard; }); } }); // Export actions export const { setSelectedScenario, toggleAIPanel, togglePerformancePanel, setSimulationSpeed, setScenarioFilters, addAISuggestion, markSuggestionUsed, addRealTimeFeedback, clearError, resetSimulation } = simulationSlice.actions; // Export selectors export const selectSimulationState = (state: { simulation: SimulationState }) => state.simulation; export const selectCurrentSession = (state: { simulation: SimulationState }) => state.simulation.current_session; export const selectScenarios = (state: { simulation: SimulationState }) => state.simulation.filtered_scenarios; export const selectAISuggestions = (state: { simulation: SimulationState }) => state.simulation.ai_suggestions; export const selectAgentProgress = (state: { simulation: SimulationState }) => state.simulation.agent_progress; export const selectIsSimulationActive = (state: { simulation: SimulationState }) => state.simulation.is_active; export default simulationSlice.reducer;