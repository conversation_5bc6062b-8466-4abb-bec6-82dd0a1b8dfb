/** * ============================================= * EMERGENCY CALL REDUX SLICE * State management for emergency call functionality * Handles call initiation, status updates, and WebRTC integration * ============================================= */ import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'; import { emergencyCallService } from '../../services/emergencyCall.service'; // Types export interface EmergencyCall { emergencyCallId: string; userId: string; urgencyLevel: 'low' | 'normal' | 'medium' | 'high' | 'urgent' | 'critical'; description: string; status: 'initiated' | 'ai_assistance' | 'escalated' | 'in_queue' | 'connected_to_agent' | 'resolved' | 'cancelled'; conversationHistory: Array<{ content: string; sender: 'user' | 'bot' | 'agent'; timestamp: Date; }>; routing: { currentRoute: string; escalationLevel: number; attemptedRoutes: Array<{ route: string; timestamp: Date; result: string; reason?: string; }>; }; queueInfo?: { position: number; estimatedWaitTime: number; addedAt: Date; priority: string; }; agentInfo?: { agentId: string; agentName: string; connectedAt: Date; }; webrtcSessionId?: string; createdAt: Date; updatedAt: Date; } export interface EmergencyCallState { currentCall: EmergencyCall | null; isInitiating: boolean; isConnected: boolean; isEscalating: boolean; error: string | null; queueStats: { activeCalls: number; queueBreakdown: Array<{ urgencyLevel: string; count: number; averageWaitTime: number; }>; } | null; callHistory: EmergencyCall[]; } const initialState: EmergencyCallState = { currentCall: null, isInitiating: false, isConnected: false, isEscalating: false, error: null, queueStats: null, callHistory: [] }; // Async Thunks export const initiateEmergencyCall = createAsyncThunk( 'emergencyCall/initiate', async (params: { userId: string; urgencyLevel: string; description: string; conversationHistory?: Array<{ content: string; sender: 'user' | 'bot' | 'agent'; timestamp: Date; }>; }, { rejectWithValue }) => { try { const response = await emergencyCallService.initiateEmergencyCall(params); return response.data; } catch (error: any) { return rejectWithValue(error.response?.data?.message || 'Failed to initiate emergency call'); } } ); export const escalateEmergencyCall = createAsyncThunk( 'emergencyCall/escalate', async (params: { emergencyCallId: string; reason?: string; agentPreference?: string; }, { rejectWithValue }) => { try { const response = await emergencyCallService.escalateToHumanAgent(params); return response.data; } catch (error: any) { return rejectWithValue(error.response?.data?.message || 'Failed to escalate emergency call'); } } ); export const connectToAgent = createAsyncThunk( 'emergencyCall/connectToAgent', async (params: { emergencyCallId: string; agentId: string; }, { rejectWithValue }) => { try { const response = await emergencyCallService.connectToHumanAgent(params); return response.data; } catch (error: any) { return rejectWithValue(error.response?.data?.message || 'Failed to connect to agent'); } } ); export const getEmergencyCallStatus = createAsyncThunk( 'emergencyCall/getStatus', async (emergencyCallId: string, { rejectWithValue }) => { try { const response = await emergencyCallService.getEmergencyCallStatus(emergencyCallId); return response.data.emergencyCall; } catch (error: any) { return rejectWithValue(error.response?.data?.message || 'Failed to get call status'); } } ); export const getQueueStats = createAsyncThunk( 'emergencyCall/getQueueStats', async (_, { rejectWithValue }) => { try { const response = await emergencyCallService.getQueueStats(); return response.data.stats; } catch (error: any) { return rejectWithValue(error.response?.data?.message || 'Failed to get queue stats'); } } ); // Slice const emergencyCallSlice = createSlice({ name: 'emergencyCall', initialState, reducers: { // Real-time updates from WebSocket updateCallStatus: (state, action: PayloadAction<{ emergencyCallId: string; status: string; data?: any; }>) => { if (state.currentCall?.emergencyCallId === action.payload.emergencyCallId) { state.currentCall.status = action.payload.status as any; if (action.payload.data) { Object.assign(state.currentCall, action.payload.data); } } }, updateQueuePosition: (state, action: PayloadAction<{ emergencyCallId: string; position: number; estimatedWaitTime: number; }>) => { if (state.currentCall?.emergencyCallId === action.payload.emergencyCallId) { if (state.currentCall.queueInfo) { state.currentCall.queueInfo.position = action.payload.position; state.currentCall.queueInfo.estimatedWaitTime = action.payload.estimatedWaitTime; } } }, setAgentConnected: (state, action: PayloadAction<{ emergencyCallId: string; agentInfo: { agentId: string; agentName: string; connectedAt: Date; }; webrtcSessionId: string; }>) => { if (state.currentCall?.emergencyCallId === action.payload.emergencyCallId) { state.currentCall.status = 'connected_to_agent'; state.currentCall.agentInfo = action.payload.agentInfo; state.currentCall.webrtcSessionId = action.payload.webrtcSessionId; state.isConnected = true; } }, setConnectionStatus: (state, action: PayloadAction<boolean>) => { state.isConnected = action.payload; }, clearCurrentCall: (state) => { if (state.currentCall) { state.callHistory.unshift(state.currentCall); // Keep only last 10 calls in history state.callHistory = state.callHistory.slice(0, 10); } state.currentCall = null; state.isConnected = false; state.error = null; }, clearError: (state) => { state.error = null; }, // WebRTC specific actions setWebRTCSession: (state, action: PayloadAction<{ emergencyCallId: string; sessionId: string; }>) => { if (state.currentCall?.emergencyCallId === action.payload.emergencyCallId) { state.currentCall.webrtcSessionId = action.payload.sessionId; } } }, extraReducers: (builder) => { // Initiate Emergency Call builder .addCase(initiateEmergencyCall.pending, (state) => { state.isInitiating = true; state.error = null; }) .addCase(initiateEmergencyCall.fulfilled, (state, action) => { state.isInitiating = false; state.currentCall = { emergencyCallId: action.payload.emergencyCallId, userId: action.meta.arg.userId, urgencyLevel: action.meta.arg.urgencyLevel as any, description: action.meta.arg.description, status: action.payload.status, conversationHistory: action.meta.arg.conversationHistory || [], routing: action.payload.routing || { currentRoute: 'initiated', escalationLevel: 0, attemptedRoutes: [] }, createdAt: new Date(), updatedAt: new Date() }; }) .addCase(initiateEmergencyCall.rejected, (state, action) => { state.isInitiating = false; state.error = action.payload as string; }); // Escalate Emergency Call builder .addCase(escalateEmergencyCall.pending, (state) => { state.isEscalating = true; state.error = null; }) .addCase(escalateEmergencyCall.fulfilled, (state, action) => { state.isEscalating = false; if (state.currentCall) { state.currentCall.status = 'escalated'; state.currentCall.queueInfo = { position: action.payload.queuePosition, estimatedWaitTime: action.payload.estimatedWaitTime, addedAt: new Date(), priority: 'high' }; } }) .addCase(escalateEmergencyCall.rejected, (state, action) => { state.isEscalating = false; state.error = action.payload as string; }); // Connect to Agent builder .addCase(connectToAgent.fulfilled, (state, action) => { if (state.currentCall) { state.currentCall.status = 'connected_to_agent'; state.currentCall.agentInfo = action.payload.agentInfo; state.currentCall.webrtcSessionId = action.payload.webrtcSessionId; state.isConnected = true; } }); // Get Emergency Call Status builder .addCase(getEmergencyCallStatus.fulfilled, (state, action) => { state.currentCall = action.payload; }); // Get Queue Stats builder .addCase(getQueueStats.fulfilled, (state, action) => { state.queueStats = action.payload; }); } }); export const { updateCallStatus, updateQueuePosition, setAgentConnected, setConnectionStatus, clearCurrentCall, clearError, setWebRTCSession } = emergencyCallSlice.actions; export default emergencyCallSlice.reducer;