/** * ============================================= * [AI] AI CALL REDUX SLICE * State management for AI-powered call system * Handles conversation analysis, suggestions, and escalation * ============================================= */ import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'; import { aiCallService } from '../../services/aiCall.service'; // Types interface ConversationAnalysis { needsCall: boolean; confidence: number; callScore: number; primaryReason: string; reasons: string[]; recommendation: { message: string; action: string; priority: string; }; analysis: { frustrationScore: number; complexityScore: number; conversationMetrics: { messageCount: number; averageResponseTime: number; sentimentScore: number; }; userBehaviorAnalysis: { engagementLevel: number; satisfactionIndicators: string[]; behaviorPatterns: string[]; }; issueResolutionProgress: { stepsCompleted: number; totalSteps: number; currentStage: string; }; }; analyzedAt: string; } interface AISuggestion { type: string; title: string; description: string; steps?: string[]; effectiveness?: number; timeEstimate?: string; confidence: number; category: string; userFeedback?: 'helpful' | 'not_helpful' | 'partially_helpful' | 'tried'; } interface CallSession { callId: string; status: string; urgencyLevel: string; description: string; aiAttempts?: number; createdAt: string; } interface EscalationEvaluation { shouldEscalate: boolean; confidence: number; reasons: string[]; urgency: string; } interface AgentInfo { id: string; name: string; specialization: string; experience: string; } interface EscalationInfo { escalated: boolean; agent?: AgentInfo; estimatedResponseTime?: number; transferredAt: string; } interface AICallState { // Conversation Analysis analysis: ConversationAnalysis | null; analysisLoading: boolean; // AI Call Session callSession: CallSession | null; callLoading: boolean; // AI Suggestions suggestions: AISuggestion[]; suggestionsLoading: boolean; suggestionsConfidence: number; // Escalation escalationEvaluation: EscalationEvaluation | null; escalationLoading: boolean; escalationInfo: EscalationInfo | null; // Analytics analytics: { totalCalls: number; aiResolvedCalls: number; escalatedCalls: number; averageResolutionTime: number; userSatisfactionScore: number; aiEffectivenessScore: number; } | null; // General state loading: boolean; error: string | null; } const initialState: AICallState = { analysis: null, analysisLoading: false, callSession: null, callLoading: false, suggestions: [], suggestionsLoading: false, suggestionsConfidence: 0, escalationEvaluation: null, escalationLoading: false, escalationInfo: null, analytics: null, loading: false, error: null }; // Async Thunks export const analyzeConversation = createAsyncThunk( 'aiCall/analyzeConversation', async (conversationId: string, { rejectWithValue }) => { try { const response = await aiCallService.analyzeConversation(conversationId); return response.data; } catch (error: unknown) { const errorMessage = error instanceof Error ? error.message : 'Failed to analyze conversation'; return rejectWithValue(errorMessage); } } ); export const initiateAICall = createAsyncThunk( 'aiCall/initiateAICall', async (params: { conversationId: string; forceCall?: boolean }, { rejectWithValue }) => { try { const response = await aiCallService.initiateAICall(params); return response.data; } catch (error: unknown) { const errorMessage = error instanceof Error ? error.message : 'Failed to initiate AI call'; return rejectWithValue(errorMessage); } } ); export const getAISuggestions = createAsyncThunk( 'aiCall/getAISuggestions', async (params: { callId: string; userInput?: string; context?: { conversationHistory: Array<{ sender: string; content: string; timestamp: Date; }>; userPreferences?: Record<string, unknown>; sessionData?: Record<string, unknown>; }; }, { rejectWithValue }) => { try { const response = await aiCallService.getAISuggestions(params); return response.data; } catch (error: unknown) { const errorMessage = error instanceof Error ? error.message : 'Failed to get AI suggestions'; return rejectWithValue(errorMessage); } } ); export const evaluateEscalation = createAsyncThunk( 'aiCall/evaluateEscalation', async (params: { callId: string; userFeedback?: string; aiAttempts?: number; conversationHistory?: Array<{ sender: string; content: string; timestamp: Date; }>; }, { rejectWithValue }) => { try { const response = await aiCallService.evaluateEscalation(params); return response.data; } catch (error: unknown) { const errorMessage = error instanceof Error ? error.message : 'Failed to evaluate escalation'; return rejectWithValue(errorMessage); } } ); export const escalateToHuman = createAsyncThunk( 'aiCall/escalateToHuman', async (params: { callId: string; escalationReason: string; agentRequirements?: { skills: string[]; experience: string; language: string; }; conversationHistory?: Array<{ sender: string; content: string; timestamp: Date; }>; aiAttempts?: number; }, { rejectWithValue }) => { try { const response = await aiCallService.escalateToHuman(params); return response.data; } catch (error: unknown) { const errorMessage = error instanceof Error ? error.message : 'Failed to escalate to human'; return rejectWithValue(errorMessage); } } ); export const getCallAnalytics = createAsyncThunk( 'aiCall/getCallAnalytics', async (timeframe: string = '24h', { rejectWithValue }) => { try { const response = await aiCallService.getCallAnalytics(timeframe); return response.data; } catch (error: unknown) { const errorMessage = error instanceof Error ? error.message : 'Failed to get call analytics'; return rejectWithValue(errorMessage); } } ); // Slice const aiCallSlice = createSlice({ name: 'aiCall', initialState, reducers: { clearError: (state) => { state.error = null; }, clearAnalysis: (state) => { state.analysis = null; }, clearCallSession: (state) => { state.callSession = null; state.suggestions = []; state.escalationEvaluation = null; state.escalationInfo = null; }, updateCallSession: (state, action: PayloadAction<Partial<CallSession>>) => { if (state.callSession) { state.callSession = { ...state.callSession, ...action.payload }; } }, addSuggestionFeedback: (state, action: PayloadAction<{ suggestionIndex: number; feedback: 'helpful' | 'not_helpful' | 'tried'; }>) => { const { suggestionIndex, feedback } = action.payload; if (state.suggestions[suggestionIndex]) { state.suggestions[suggestionIndex].userFeedback = feedback; } }, resetState: () => initialState }, extraReducers: (builder) => { // Analyze Conversation builder .addCase(analyzeConversation.pending, (state) => { state.analysisLoading = true; state.loading = true; state.error = null; }) .addCase(analyzeConversation.fulfilled, (state, action) => { state.analysisLoading = false; state.loading = false; state.analysis = action.payload.analysis; }) .addCase(analyzeConversation.rejected, (state, action) => { state.analysisLoading = false; state.loading = false; state.error = action.payload as string; }); // Initiate AI Call builder .addCase(initiateAICall.pending, (state) => { state.callLoading = true; state.loading = true; state.error = null; }) .addCase(initiateAICall.fulfilled, (state, action) => { state.callLoading = false; state.loading = false; if (action.payload.success && action.payload.callSession) { state.callSession = { callId: action.payload.callSession.callId || '', status: action.payload.callSession.status || 'initiated', urgencyLevel: action.payload.callSession.urgencyLevel || 'medium', description: action.payload.callSession.description || '', aiAttempts: 0, createdAt: new Date().toISOString() }; if (action.payload.aiAssistance?.suggestions) { state.suggestions = action.payload.aiAssistance.suggestions; state.suggestionsConfidence = action.payload.aiAssistance.confidence; } } }) .addCase(initiateAICall.rejected, (state, action) => { state.callLoading = false; state.loading = false; state.error = action.payload as string; }); // Get AI Suggestions builder .addCase(getAISuggestions.pending, (state) => { state.suggestionsLoading = true; state.loading = true; state.error = null; }) .addCase(getAISuggestions.fulfilled, (state, action) => { state.suggestionsLoading = false; state.loading = false; state.suggestions = action.payload.suggestions; state.suggestionsConfidence = action.payload.confidence; }) .addCase(getAISuggestions.rejected, (state, action) => { state.suggestionsLoading = false; state.loading = false; state.error = action.payload as string; }); // Evaluate Escalation builder .addCase(evaluateEscalation.pending, (state) => { state.escalationLoading = true; state.loading = true; state.error = null; }) .addCase(evaluateEscalation.fulfilled, (state, action) => { state.escalationLoading = false; state.loading = false; state.escalationEvaluation = action.payload.evaluation; }) .addCase(evaluateEscalation.rejected, (state, action) => { state.escalationLoading = false; state.loading = false; state.error = action.payload as string; }); // Escalate to Human builder .addCase(escalateToHuman.pending, (state) => { state.escalationLoading = true; state.loading = true; state.error = null; }) .addCase(escalateToHuman.fulfilled, (state, action) => { state.escalationLoading = false; state.loading = false; if (action.payload.success) { state.escalationInfo = action.payload.escalation; if (state.callSession) { state.callSession.status = 'escalated_to_human'; } } }) .addCase(escalateToHuman.rejected, (state, action) => { state.escalationLoading = false; state.loading = false; state.error = action.payload as string; }); // Get Call Analytics builder .addCase(getCallAnalytics.pending, (state) => { state.loading = true; state.error = null; }) .addCase(getCallAnalytics.fulfilled, (state, action) => { state.loading = false; state.analytics = action.payload.analytics; }) .addCase(getCallAnalytics.rejected, (state, action) => { state.loading = false; state.error = action.payload as string; }); } }); export const { clearError, clearAnalysis, clearCallSession, updateCallSession, addSuggestionFeedback, resetState } = aiCallSlice.actions; export default aiCallSlice.reducer;