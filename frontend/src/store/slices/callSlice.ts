/** * ============================================= * CALL REDUX SLICE * State management for call functionality * Handles call states, WebRTC, and queue management * ============================================= */ import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'; import { callService } from '../../services/call.service'; import { webrtcService } from '../../services/webrtc.service'; // Types export interface CallState { // Current call currentCall: Call | null; isCallActive: boolean; callStatus: 'idle' | 'connecting' | 'ringing' | 'queued' | 'connected' | 'ended' | 'failed'; // Call controls isMuted: boolean; isSpeakerOn: boolean; isVideoEnabled: boolean; // Queue information queuePosition: number | null; estimatedWaitTime: number; availableAgents: number; queueStats: QueueStats | null; // WebRTC webrtcSession: WebRTCSession | null; localStream: MediaStream | null; remoteStream: MediaStream | null; // Call history callHistory: Call[]; // UI state isLoading: boolean; error: string | null; showCallInterface: boolean; } export interface Call { id: string; twilioCallSid?: string; direction: 'inbound' | 'outbound'; from: string; to: string; status: string; startTime: string; endTime?: string; duration?: string; agentId?: string; agentName?: string; reason?: string; priority: 'low' | 'normal' | 'high' | 'urgent' | 'emergency'; recordingUrl?: string; transcriptionUrl?: string; // Emergency call specific fields isEmergencyCall?: boolean; emergencyCallId?: string; urgencyLevel?: 'low' | 'normal' | 'medium' | 'high' | 'urgent' | 'critical'; } export interface QueueStats { currentCalls: number; averageWaitTime: number; totalAgents: number; availableAgents: number; } export interface WebRTCSession { callId: string; status: string; participants: Participant[]; createdAt: string; } export interface Participant { userId: string; role: 'customer' | 'agent' | 'supervisor'; status: string; muted: boolean; joinedAt: string; socketId: string; } // Initial state const initialState: CallState = { currentCall: null, isCallActive: false, callStatus: 'idle', isMuted: false, isSpeakerOn: false, isVideoEnabled: false, queuePosition: null, estimatedWaitTime: 0, availableAgents: 0, queueStats: null, webrtcSession: null, localStream: null, remoteStream: null, callHistory: [], isLoading: false, error: null, showCallInterface: false }; // Async thunks export const initiateCall = createAsyncThunk( 'call/initiate', async (params: { type: 'immediate' | 'scheduled' | 'callback'; userId: string; priority: 'low' | 'normal' | 'high' | 'urgent'; reason?: string; scheduledTime?: Date; preferredAgent?: string; }) => { const response = await callService.initiateCall(params); return response.data; } ); export const endCall = createAsyncThunk( 'call/end', async (params: { callId: string; reason?: string }) => { const response = await callService.endCall(params.callId, params.reason); return response.data; } ); export const getQueueStats = createAsyncThunk( 'call/getQueueStats', async () => { const response = await callService.getQueueStats(); return response.data; } ); export const startWebRTCCall = createAsyncThunk( 'call/startWebRTC', async (params: { participants: Participant[] }) => { const response = await callService.startWebRTCCall(params.participants); return response.data; } ); export const joinWebRTCCall = createAsyncThunk( 'call/joinWebRTC', async (params: { callId: string; userId: string; role: string; socketId: string }) => { const response = await callService.joinWebRTCCall(params); return response.data; } ); export const scheduleCallback = createAsyncThunk( 'call/scheduleCallback', async (params: { userId: string; scheduledTime: Date; reason: string; priority: 'low' | 'normal' | 'high' | 'urgent'; preferences: any; }) => { const response = await callService.scheduleCallback(params); return response.data; } ); export const getCallHistory = createAsyncThunk( 'call/getHistory', async (params: { userId: string; limit?: number; offset?: number }) => { const response = await callService.getCallHistory(params); return response.data; } ); // Slice const callSlice = createSlice({ name: 'call', initialState, reducers: { // Call controls toggleMute: (state, action: PayloadAction<string>) => { state.isMuted = !state.isMuted; // WebRTC service will handle the actual muting if (state.webrtcSession) { webrtcService.toggleMute(action.payload, state.isMuted); } }, toggleSpeaker: (state, action: PayloadAction<string>) => { state.isSpeakerOn = !state.isSpeakerOn; // WebRTC service will handle the speaker toggle if (state.webrtcSession) { webrtcService.toggleSpeaker(action.payload, state.isSpeakerOn); } }, toggleVideo: (state, action: PayloadAction<string>) => { state.isVideoEnabled = !state.isVideoEnabled; // WebRTC service will handle the video toggle if (state.webrtcSession) { webrtcService.toggleVideo(action.payload, state.isVideoEnabled); } }, // WebRTC stream management setLocalStream: (state, action: PayloadAction<MediaStream | null>) => { state.localStream = action.payload; }, setRemoteStream: (state, action: PayloadAction<MediaStream | null>) => { state.remoteStream = action.payload; }, // Call status updates updateCallStatus: (state, action: PayloadAction<{ status: CallState['callStatus']; queuePosition?: number; estimatedWaitTime?: number; }>) => { state.callStatus = action.payload.status; if (action.payload.queuePosition !== undefined) { state.queuePosition = action.payload.queuePosition; } if (action.payload.estimatedWaitTime !== undefined) { state.estimatedWaitTime = action.payload.estimatedWaitTime; } }, // WebRTC session updates updateWebRTCSession: (state, action: PayloadAction<WebRTCSession>) => { state.webrtcSession = action.payload; }, // UI state setShowCallInterface: (state, action: PayloadAction<boolean>) => { state.showCallInterface = action.payload; }, // Error handling clearError: (state) => { state.error = null; }, // Reset call state resetCallState: (state) => { state.currentCall = null; state.isCallActive = false; state.callStatus = 'idle'; state.isMuted = false; state.isSpeakerOn = false; state.isVideoEnabled = false; state.queuePosition = null; state.webrtcSession = null; state.localStream = null; state.remoteStream = null; state.showCallInterface = false; state.error = null; } }, extraReducers: (builder) => { // Initiate call builder .addCase(initiateCall.pending, (state) => { state.isLoading = true; state.error = null; state.callStatus = 'connecting'; }) .addCase(initiateCall.fulfilled, (state, action) => { state.isLoading = false; state.currentCall = action.payload.call; state.isCallActive = true; state.callStatus = 'ringing'; state.showCallInterface = true; }) .addCase(initiateCall.rejected, (state, action) => { state.isLoading = false; state.error = action.error.message || 'Failed to initiate call'; state.callStatus = 'failed'; }); // End call builder .addCase(endCall.pending, (state) => { state.isLoading = true; }) .addCase(endCall.fulfilled, (state, action) => { state.isLoading = false; state.isCallActive = false; state.callStatus = 'ended'; if (state.currentCall) { state.currentCall.endTime = action.payload.endedAt; state.currentCall.status = 'completed'; // Add to history state.callHistory.unshift(state.currentCall); } // Reset after a delay setTimeout(() => { state.currentCall = null; state.callStatus = 'idle'; state.showCallInterface = false; }, 3000); }) .addCase(endCall.rejected, (state, action) => { state.isLoading = false; state.error = action.error.message || 'Failed to end call'; }); // Get queue stats builder .addCase(getQueueStats.fulfilled, (state, action) => { state.queueStats = action.payload.stats; state.availableAgents = action.payload.stats.availableAgents; state.estimatedWaitTime = action.payload.stats.averageWaitTime; }); // Start WebRTC call builder .addCase(startWebRTCCall.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(startWebRTCCall.fulfilled, (state, action) => { state.isLoading = false; state.webrtcSession = action.payload.session; state.isCallActive = true; state.callStatus = 'connected'; state.showCallInterface = true; }) .addCase(startWebRTCCall.rejected, (state, action) => { state.isLoading = false; state.error = action.error.message || 'Failed to start WebRTC call'; state.callStatus = 'failed'; }); // Join WebRTC call builder .addCase(joinWebRTCCall.fulfilled, (state, action) => { state.webrtcSession = action.payload.session; state.callStatus = 'connected'; }); // Schedule callback builder .addCase(scheduleCallback.pending, (state) => { state.isLoading = true; state.error = null; }) .addCase(scheduleCallback.fulfilled, (state, action) => { state.isLoading = false; // Could add scheduled calls to a separate array if needed }) .addCase(scheduleCallback.rejected, (state, action) => { state.isLoading = false; state.error = action.error.message || 'Failed to schedule callback'; }); // Get call history builder .addCase(getCallHistory.fulfilled, (state, action) => { state.callHistory = action.payload.calls; }); } }); export const { toggleMute, toggleSpeaker, toggleVideo, setLocalStream, setRemoteStream, updateCallStatus, updateWebRTCSession, setShowCallInterface, clearError, resetCallState } = callSlice.actions; export default callSlice.reducer;