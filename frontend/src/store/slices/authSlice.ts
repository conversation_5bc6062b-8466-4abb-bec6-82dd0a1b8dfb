import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'; import { AuthState, User, RegisterData } from '../../types'; import apiService from '../../services/api'; const initialState: AuthState = { user: localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user')!) : null, token: localStorage.getItem('token'), isAuthenticated: !!localStorage.getItem('token'), loading: false, error: null, }; // Async thunks export const login = createAsyncThunk( 'auth/login', async ({ email, password }: { email: string; password: string }, { rejectWithValue }) => { try { const response = await apiService.login(email, password); localStorage.setItem('token', response.token); localStorage.setItem('user', JSON.stringify(response.user)); return response; } catch (error: any) { return rejectWithValue(error.response?.data?.error || 'Erreur de connexion'); } } ); export const register = createAsyncThunk( 'auth/register', async (data: RegisterData, { rejectWithValue }) => { try { const response = await apiService.register(data); localStorage.setItem('token', response.token); localStorage.setItem('user', JSON.stringify(response.user)); return response; } catch (error: any) { return rejectWithValue(error.response?.data?.error || 'Erreur d\'inscription'); } } ); export const validateToken = createAsyncThunk( 'auth/validateToken', async (_, { rejectWithValue }) => { try { const response = await apiService.validateToken(); return response; } catch (error: any) { localStorage.removeItem('token'); localStorage.removeItem('user'); return rejectWithValue('Token invalide'); } } ); const authSlice = createSlice({ name: 'auth', initialState, reducers: { logout: (state) => { state.user = null; state.token = null; state.isAuthenticated = false; state.error = null; localStorage.removeItem('token'); localStorage.removeItem('user'); }, clearError: (state) => { state.error = null; }, updateUser: (state, action) => { if (state.user) { state.user = { ...state.user, ...action.payload }; localStorage.setItem('user', JSON.stringify(state.user)); } }, }, extraReducers: (builder) => { builder // Login .addCase(login.pending, (state) => { state.loading = true; state.error = null; }) .addCase(login.fulfilled, (state, action) => { state.loading = false; state.user = action.payload.user; state.token = action.payload.token; state.isAuthenticated = true; state.error = null; }) .addCase(login.rejected, (state, action) => { state.loading = false; state.error = action.payload as string; state.isAuthenticated = false; }) // Register .addCase(register.pending, (state) => { state.loading = true; state.error = null; }) .addCase(register.fulfilled, (state, action) => { state.loading = false; state.user = action.payload.user; state.token = action.payload.token; state.isAuthenticated = true; state.error = null; }) .addCase(register.rejected, (state, action) => { state.loading = false; state.error = action.payload as string; }) // Validate token .addCase(validateToken.pending, (state) => { state.loading = true; }) .addCase(validateToken.fulfilled, (state) => { state.loading = false; state.error = null; }) .addCase(validateToken.rejected, (state) => { state.loading = false; state.isAuthenticated = false; state.user = null; state.token = null; }); }, }); export const { logout, clearError, updateUser } = authSlice.actions; export default authSlice.reducer;