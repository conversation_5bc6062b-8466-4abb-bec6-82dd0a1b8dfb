/** * Test Script for React DOM Manipulation Fixes * This script tests the critical fixes implemented for the Free Mobile dashboard */ // Test 1: API Service Structure console.log(' Testing API Service...'); import('./services/api.js').then(module => { const apiService = module.default; console.log('[COMPLETE] API Service loaded successfully'); console.log('[COMPLETE] Login method exists:', typeof apiService.login === 'function'); console.log('[COMPLETE] Register method exists:', typeof apiService.register === 'function'); console.log('[COMPLETE] ValidateToken method exists:', typeof apiService.validateToken === 'function'); }).catch(error => { console.error('[FAILED] API Service test failed:', error); }); // Test 2: Auth Slice console.log(' Testing Auth Slice...'); import('./store/slices/authSlice.js').then(module => { console.log('[COMPLETE] Auth Slice loaded successfully'); console.log('[COMPLETE] Login action exists:', typeof module.login === 'function'); console.log('[COMPLETE] Register action exists:', typeof module.register === 'function'); console.log('[COMPLETE] ValidateToken action exists:', typeof module.validateToken === 'function'); }).catch(error => { console.error('[FAILED] Auth Slice test failed:', error); }); // Test 3: Dashboard Routes console.log(' Testing Dashboard Routes...'); import('./routes/dashboardRoutes.js').then(module => { console.log('[COMPLETE] Dashboard Routes loaded successfully'); console.log('[COMPLETE] Default export exists:', typeof module.default === 'function'); }).catch(error => { console.error('[FAILED] Dashboard Routes test failed:', error); }); // Test 4: useAuth Hook console.log(' Testing useAuth Hook...'); import('./hooks/useAuth.js').then(module => { console.log('[COMPLETE] useAuth Hook loaded successfully'); console.log('[COMPLETE] useAuth function exists:', typeof module.useAuth === 'function'); }).catch(error => { console.error('[FAILED] useAuth Hook test failed:', error); }); // Test 5: Component Loading console.log(' Testing Dashboard Components...'); const componentTests = [ './pages/Dashboard/DashboardOverview.js', './pages/Dashboard/SupportFormPage.js', './pages/Dashboard/AdminDashboard.js', './pages/Dashboard/AnalyticsDashboard.js', './components/Auth/LoginForm.js' ]; componentTests.forEach(componentPath => { import(componentPath).then(module => { const componentName = componentPath.split('/').pop().replace('.js', ''); console.log(`[COMPLETE] ${componentName} loaded successfully`); }).catch(error => { const componentName = componentPath.split('/').pop().replace('.js', ''); console.error(`[FAILED] ${componentName} test failed:`, error); }); }); console.log(' All tests completed! Check console for results.'); // Test 6: Local Storage Mock Test console.log(' Testing Local Storage Integration...'); try { // Test localStorage functionality localStorage.setItem('test-token', 'mock-token-123'); localStorage.setItem('test-user', JSON.stringify({ id: '1', email: '<EMAIL>' })); const token = localStorage.getItem('test-token'); const user = JSON.parse(localStorage.getItem('test-user') || '{}'); if (token === 'mock-token-123' && user.email === '<EMAIL>') { console.log('[COMPLETE] Local Storage integration working'); } else { console.error('[FAILED] Local Storage integration failed'); } // Cleanup localStorage.removeItem('test-token'); localStorage.removeItem('test-user'); } catch (error) { console.error('[FAILED] Local Storage test failed:', error); } // Test 7: Route Constants console.log(' Testing Route Constants...'); import('./utils/constants.js').then(module => { const { ROUTES } = module; console.log('[COMPLETE] Route constants loaded'); console.log('[COMPLETE] Dashboard routes exist:', !!ROUTES.DASHBOARD_OVERVIEW); console.log('[COMPLETE] Login route exists:', !!ROUTES.LOGIN); }).catch(error => { console.error('[FAILED] Route constants test failed:', error); }); export default { message: 'React DOM Manipulation Fixes Test Suite', version: '1.0.0', timestamp: new Date().toISOString() };