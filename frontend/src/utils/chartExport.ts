/**
 * Chart Export Utilities
 * Provides multi-format export functionality for Chart.js components
 * Supports PDF, PNG, CSV, and Excel exports
 */

import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import * as XLSX from 'xlsx';

export interface ExportOptions {
  filename?: string;
  format: 'pdf' | 'png' | 'csv' | 'excel';
  quality?: number;
  includeTitle?: boolean;
  includeDate?: boolean;
  customTitle?: string;
}

export interface ChartData {
  labels: string[];
  datasets: Array<{
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string | string[];
  }>;
}

/**
 * Export chart as PNG image
 */
export const exportChartAsPNG = async (
  chartElement: HTMLElement,
  options: ExportOptions = { format: 'png' }
): Promise<void> => {
  try {
    const canvas = await html2canvas(chartElement, {
      backgroundColor: '#ffffff',
      scale: options.quality || 2,
      useCORS: true,
      allowTaint: true,
    });

    const link = document.createElement('a');
    link.download = `${options.filename || 'chart'}.png`;
    link.href = canvas.toDataURL('image/png');
    link.click();
  } catch (error) {
    console.error('Error exporting chart as PNG:', error);
    throw new Error('Failed to export chart as PNG');
  }
};

/**
 * Export chart as PDF
 */
export const exportChartAsPDF = async (
  chartElement: HTMLElement,
  options: ExportOptions = { format: 'pdf' }
): Promise<void> => {
  try {
    const canvas = await html2canvas(chartElement, {
      backgroundColor: '#ffffff',
      scale: options.quality || 2,
      useCORS: true,
      allowTaint: true,
    });

    const imgData = canvas.toDataURL('image/png');
    const pdf = new jsPDF({
      orientation: canvas.width > canvas.height ? 'landscape' : 'portrait',
      unit: 'mm',
      format: 'a4',
    });

    const pdfWidth = pdf.internal.pageSize.getWidth();
    const pdfHeight = pdf.internal.pageSize.getHeight();
    const imgWidth = canvas.width;
    const imgHeight = canvas.height;
    const ratio = Math.min(pdfWidth / imgWidth, pdfHeight / imgHeight);
    const imgX = (pdfWidth - imgWidth * ratio) / 2;
    const imgY = 20;

    // Add title if requested
    if (options.includeTitle) {
      pdf.setFontSize(16);
      pdf.text(options.customTitle || 'Analytics Chart', pdfWidth / 2, 15, { align: 'center' });
    }

    // Add date if requested
    if (options.includeDate) {
      pdf.setFontSize(10);
      pdf.text(`Generated on: ${new Date().toLocaleDateString('fr-FR')}`, 10, pdfHeight - 10);
    }

    pdf.addImage(imgData, 'PNG', imgX, imgY, imgWidth * ratio, imgHeight * ratio);
    pdf.save(`${options.filename || 'chart'}.pdf`);
  } catch (error) {
    console.error('Error exporting chart as PDF:', error);
    throw new Error('Failed to export chart as PDF');
  }
};

/**
 * Export chart data as CSV
 */
export const exportChartAsCSV = (
  chartData: ChartData,
  options: ExportOptions = { format: 'csv' }
): void => {
  try {
    let csvContent = '';

    // Add title if requested
    if (options.includeTitle) {
      csvContent += `${options.customTitle || 'Analytics Data'}\n`;
      csvContent += `Generated on: ${new Date().toLocaleDateString('fr-FR')}\n\n`;
    }

    // Create header row
    const headers = ['Label', ...chartData.datasets.map(dataset => dataset.label)];
    csvContent += headers.join(',') + '\n';

    // Add data rows
    chartData.labels.forEach((label, index) => {
      const row = [
        `"${label}"`,
        ...chartData.datasets.map(dataset => dataset.data[index] || 0)
      ];
      csvContent += row.join(',') + '\n';
    });

    // Download CSV
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `${options.filename || 'chart-data'}.csv`;
    link.click();
    URL.revokeObjectURL(link.href);
  } catch (error) {
    console.error('Error exporting chart as CSV:', error);
    throw new Error('Failed to export chart as CSV');
  }
};

/**
 * Export chart data as Excel
 */
export const exportChartAsExcel = (
  chartData: ChartData,
  options: ExportOptions = { format: 'excel' }
): void => {
  try {
    // Create workbook
    const wb = XLSX.utils.book_new();

    // Prepare data for Excel
    const excelData: any[][] = [];

    // Add title if requested
    if (options.includeTitle) {
      excelData.push([options.customTitle || 'Analytics Data']);
      excelData.push([`Generated on: ${new Date().toLocaleDateString('fr-FR')}`]);
      excelData.push([]); // Empty row
    }

    // Add headers
    const headers = ['Label', ...chartData.datasets.map(dataset => dataset.label)];
    excelData.push(headers);

    // Add data rows
    chartData.labels.forEach((label, index) => {
      const row = [
        label,
        ...chartData.datasets.map(dataset => dataset.data[index] || 0)
      ];
      excelData.push(row);
    });

    // Create worksheet
    const ws = XLSX.utils.aoa_to_sheet(excelData);

    // Style the worksheet
    const range = XLSX.utils.decode_range(ws['!ref'] || 'A1');
    
    // Set column widths
    ws['!cols'] = [
      { width: 20 }, // Label column
      ...chartData.datasets.map(() => ({ width: 15 })) // Data columns
    ];

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(wb, ws, 'Chart Data');

    // Save file
    XLSX.writeFile(wb, `${options.filename || 'chart-data'}.xlsx`);
  } catch (error) {
    console.error('Error exporting chart as Excel:', error);
    throw new Error('Failed to export chart as Excel');
  }
};

/**
 * Universal export function that handles all formats
 */
export const exportChart = async (
  chartElement: HTMLElement,
  chartData: ChartData,
  options: ExportOptions
): Promise<void> => {
  try {
    switch (options.format) {
      case 'png':
        await exportChartAsPNG(chartElement, options);
        break;
      case 'pdf':
        await exportChartAsPDF(chartElement, options);
        break;
      case 'csv':
        exportChartAsCSV(chartData, options);
        break;
      case 'excel':
        exportChartAsExcel(chartData, options);
        break;
      default:
        throw new Error(`Unsupported export format: ${options.format}`);
    }
  } catch (error) {
    console.error('Error exporting chart:', error);
    throw error;
  }
};

/**
 * Get available export formats
 */
export const getAvailableExportFormats = () => [
  { value: 'png', label: 'PNG Image', icon: '🖼️' },
  { value: 'pdf', label: 'PDF Document', icon: '📄' },
  { value: 'csv', label: 'CSV Data', icon: '📊' },
  { value: 'excel', label: 'Excel Spreadsheet', icon: '📈' },
];

/**
 * Validate export options
 */
export const validateExportOptions = (options: ExportOptions): boolean => {
  const validFormats = ['png', 'pdf', 'csv', 'excel'];
  return validFormats.includes(options.format);
};
