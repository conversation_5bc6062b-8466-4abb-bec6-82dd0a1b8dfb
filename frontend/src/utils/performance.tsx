import React, { lazy, ComponentType, LazyExoticComponent, useState, useEffect, useRef, useMemo, useCallback } from 'react'; /** * Performance optimization utilities for React components */ /** * Enhanced lazy loading with error boundary and loading states */ export const createLazyComponent = <T extends ComponentType<any>>( importFn: () => Promise<{ default: T }>, fallback?: ComponentType ): LazyExoticComponent<T> => { return lazy(importFn); }; /** * Lazy-loaded components for code splitting */ export const LazyComponents = { // Dashboard components Dashboard: createLazyComponent(() => import('../pages/DashboardPage')), DashboardOverview: createLazyComponent(() => import('../pages/Dashboard/DashboardOverview')), // Chat components ChatPage: createLazyComponent(() => import('../pages/ChatPage')), // Analytics components AnalyticsDashboard: createLazyComponent(() => import('../pages/Dashboard/AnalyticsDashboard')), // Admin components AdminDashboard: createLazyComponent(() => import('../pages/Dashboard/AdminDashboard')), // Support components SupportFormPage: createLazyComponent(() => import('../pages/Dashboard/SupportFormPage')), // Profile components ProfilePage: createLazyComponent(() => import('../pages/ProfilePage')) }; /** * Image optimization utilities */ export const ImageOptimization = { /** * Create optimized image URL with lazy loading */ createOptimizedImageUrl: (src: string, options: { width?: number; height?: number; quality?: number; format?: 'webp' | 'jpeg' | 'png'; } = {}) => { const { width, height, quality = 80, format = 'webp' } = options; // In production, this would integrate with a CDN like Cloudinary or ImageKit if (process.env.NODE_ENV === 'production') { const params = new URLSearchParams(); if (width) params.append('w', width.toString()); if (height) params.append('h', height.toString()); params.append('q', quality.toString()); params.append('f', format); return `${process.env.REACT_APP_CDN_URL}/image/fetch/${params.toString()}/${encodeURIComponent(src)}`; } return src; }, /** * Lazy image component with intersection observer */ LazyImage: ({ src, alt, className, ...props }: { src: string; alt: string; className?: string; width?: number; height?: number; }) => { const [isLoaded, setIsLoaded] = useState(false); const [isInView, setIsInView] = useState(false); const imgRef = useRef<HTMLImageElement>(null); useEffect(() => { const observer = new IntersectionObserver( ([entry]) => { if (entry.isIntersecting) { setIsInView(true); observer.disconnect(); } }, { threshold: 0.1 } ); if (imgRef.current) { observer.observe(imgRef.current); } return () => observer.disconnect(); }, []); return ( <img ref={imgRef} src={isInView ? src : undefined} alt={alt} className={className} onLoad={() => setIsLoaded(true)} style={{ opacity: isLoaded ? 1 : 0, transition: 'opacity 0.3s ease-in-out', ...props }} /> ); } }; /** * Performance monitoring utilities */ export const PerformanceMonitoring = { /** * Measure component render time */ measureRenderTime: (componentName: string) => { return (WrappedComponent: ComponentType<any>) => { return (props: any) => { useEffect(() => { const startTime = performance.now(); return () => { const endTime = performance.now(); const renderTime = endTime - startTime; if (renderTime > 16) { // More than one frame (60fps) console.warn(`Slow render detected: ${componentName} took ${renderTime.toFixed(2)}ms`); } // Send to analytics in production if (process.env.NODE_ENV === 'production') { // Analytics.track('component_render_time', { // component: componentName, // renderTime, // timestamp: Date.now() // }); } }; }); return <WrappedComponent {...props} />; }; }; }, /** * Memory usage monitoring */ monitorMemoryUsage: () => { if ('memory' in performance) { const memory = (performance as any).memory; return { usedJSHeapSize: memory.usedJSHeapSize, totalJSHeapSize: memory.totalJSHeapSize, jsHeapSizeLimit: memory.jsHeapSizeLimit, usage: Math.round((memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100) }; } return null; }, /** * Bundle size analyzer (development only) */ analyzeBundleSize: () => { if (process.env.NODE_ENV === 'development') { // This would integrate with webpack-bundle-analyzer console.log('Bundle analysis available in development mode'); } } }; /** * Caching utilities */ export const CacheUtils = { /** * Simple in-memory cache with TTL */ createCache: <T extends any>(ttlMs: number = 300000) => { // 5 minutes default const cache = new Map<string, { data: T; expires: number }>(); return { get: (key: string): T | null => { const item = cache.get(key); if (!item) return null; if (Date.now() > item.expires) { cache.delete(key); return null; } return item.data; }, set: (key: string, data: T): void => { cache.set(key, { data, expires: Date.now() + ttlMs }); }, clear: (): void => { cache.clear(); }, size: (): number => { return cache.size; } }; }, /** * React Query configuration for API caching */ queryClientConfig: { defaultOptions: { queries: { staleTime: 5 * 60 * 1000, // 5 minutes cacheTime: 10 * 60 * 1000, // 10 minutes retry: (failureCount: number, error: any) => { // Don't retry on 4xx errors if (error?.response?.status >= 400 && error?.response?.status < 500) { return false; } return failureCount < 3; }, refetchOnWindowFocus: false, refetchOnReconnect: true }, mutations: { retry: 1 } } } }; /** * Bundle optimization utilities */ export const BundleOptimization = { /** * Dynamic imports for heavy libraries */ loadChartLibrary: async () => { const { default: Chart } = await import('chart.js/auto'); return Chart; }, loadDateLibrary: async () => { const { default: dayjs } = await import('dayjs'); return dayjs; }, loadMarkdownLibrary: async () => { const { default: ReactMarkdown } = await import('react-markdown'); return ReactMarkdown; }, /** * Preload critical resources */ preloadCriticalResources: () => { // Preload critical fonts const fontLink = document.createElement('link'); fontLink.rel = 'preload'; fontLink.href = 'https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap'; fontLink.as = 'style'; document.head.appendChild(fontLink); // Preload critical API endpoints if ('serviceWorker' in navigator) { navigator.serviceWorker.ready.then(registration => { // Cache critical API endpoints const criticalUrls = [ '/api/auth/me', '/api/dashboard/stats', '/api/chat/conversations' ]; criticalUrls.forEach(url => { fetch(url, { method: 'HEAD' }).catch(() => { // Silently fail - this is just preloading }); }); }); } } }; /** * React hooks for performance optimization */ export const useDebounce = <T extends any>(value: T, delay: number): T => { const [debouncedValue, setDebouncedValue] = useState<T>(value); useEffect(() => { const handler = setTimeout(() => { setDebouncedValue(value); }, delay); return () => { clearTimeout(handler); }; }, [value, delay]); return debouncedValue; }; export const useThrottle = <T extends (...args: any[]) => any>( callback: T, delay: number ): T => { const lastRun = useRef(Date.now()); return useCallback( ((...args) => { if (Date.now() - lastRun.current >= delay) { callback(...args); lastRun.current = Date.now(); } }) as T, [callback, delay] ); }; export const useMemoizedCallback = <T extends (...args: any[]) => any>( callback: T, deps: React.DependencyList ): T => { return useCallback(callback, deps); }; export const useMemoizedValue = <T extends any>( factory: () => T, deps: React.DependencyList ): T => { return useMemo(factory, deps); };