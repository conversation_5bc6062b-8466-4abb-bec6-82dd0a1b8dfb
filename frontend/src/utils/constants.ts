export const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';
export const SOCKET_URL = process.env.REACT_APP_SOCKET_URL || 'http://localhost:5000';

export const ROUTES = {
  HOME: '/',
  LOGIN: '/login',
  REGISTER: '/register',
  DASHBOARD: '/dashboard',
  CHAT: '/chat',
  SUPPORT: '/support',
  ADMIN: '/admin',
  ADMIN_DASHBOARD: '/admin/dashboard',
  ADMIN_CONVERSATIONS: '/admin/conversations',
  ADMIN_ANALYTICS: '/admin/analytics',
  AGENT_CHAT: '/agent/chat',

  // New Dashboard Routes
  DASHBOARD_OVERVIEW: '/dashboard/overview',
  DASHBOARD_SUPPORT_FORM: '/dashboard/support-form',
  DASHBOARD_ADMIN: '/dashboard/admin',
  DASHBOARD_ADMIN_CONVERSATIONS: '/dashboard/admin/conversations',
  DASHBOARD_ADMIN_CLIENTS: '/dashboard/admin/clients',
  DASHBOARD_ANALYTICS: '/dashboard/analytics',
  DASHBOARD_AGENT_COPILOT: '/dashboard/agent-copilot',
  DASHBOARD_MULTIMODAL: '/dashboard/multimodal',
};

export const FREE_MOBILE_COLORS = {
  PRIMARY: '#ed1c24',
  SECONDARY: '#2c3e50',
  ACCENT: '#ff6b6b',
  SUCCESS: '#27ae60',
  WARNING: '#f39c12',
  ERROR: '#e74c3c',
  INFO: '#3498db',
  TEXT_PRIMARY: '#2c3e50',
  TEXT_SECONDARY: '#7f8c8d',
};

export const USER_ROLES = {
  USER: 'user',
  AGENT: 'agent',
  ADMIN: 'admin',
} as const;

export const CONVERSATION_STATUS = {
  ACTIVE: 'active',
  RESOLVED: 'resolved',
  ESCALATED: 'escalated',
  ABANDONED: 'abandoned',
};

export const MESSAGE_TYPES = {
  TEXT: 'text',
  BUTTON: 'button',
  CARD: 'card',
  IMAGE: 'image',
  FILE: 'file',
};

export const CHANNELS = {
  WEB: 'web',
  MOBILE: 'mobile',
  VOICE: 'voice',
  WHATSAPP: 'whatsapp',
  MESSENGER: 'messenger',
  INSTAGRAM: 'instagram',
};

export const DASHBOARD_SECTIONS = {
  OVERVIEW: 'overview',
  SUPPORT_FORM: 'support-form',
  ADMIN: 'admin',
  ANALYTICS: 'analytics',
  AGENT_COPILOT: 'agent-copilot',
  MULTIMODAL: 'multimodal',
} as const;

export const TICKET_STATUS = {
  PENDING: 'pending',
  IN_PROGRESS: 'in_progress',
  RESOLVED: 'resolved',
  CLOSED: 'closed',
  ESCALATED: 'escalated',
} as const;

export const TICKET_PRIORITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  URGENT: 'urgent',
} as const;

export const SUPPORT_CATEGORIES = [
  { id: 'billing', label: 'Facturation', icon: 'receipt' },
  { id: 'technical', label: 'Problème technique', icon: 'build' },
  { id: 'subscription', label: 'Abonnement', icon: 'card_membership' },
  { id: 'network', label: 'Réseau', icon: 'signal_cellular_alt' },
  { id: 'device', label: 'Appareil', icon: 'phone_android' },
  { id: 'other', label: 'Autre', icon: 'help_outline' },
];

export const FREE_MOBILE_PLANS = [
  {
    id: 'forfait-2h',
    name: 'Forfait 2h',
    price: 2,
    data: '50 Mo',
    calls: '2h',
    sms: 'Illimités',
    description: 'Le forfait essentiel'
  },
  {
    id: 'forfait-150go',
    name: 'Forfait 150Go',
    price: 19.99,
    data: '150 Go',
    calls: 'Illimités',
    sms: 'Illimités',
    description: 'Le forfait le plus populaire'
  },
  {
    id: 'forfait-300go',
    name: 'Forfait 300Go',
    price: 25.99,
    data: '300 Go',
    calls: 'Illimités',
    sms: 'Illimités',
    description: 'Pour les gros consommateurs'
  }
];