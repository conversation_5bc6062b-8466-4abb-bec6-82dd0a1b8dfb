/** * ============================================= * [SECURITY] AUTHENTICATION UTILITIES * Helper functions for authentication and token management * Handles JWT tokens, user sessions, and auth state * ============================================= */ // Token storage keys const TOKEN_KEY = 'auth_token'; const REFRESH_TOKEN_KEY = 'refresh_token'; const USER_KEY = 'user_data'; /** * Get authentication token from localStorage */ export const getAuthToken = (): string | null => { try { return localStorage.getItem(TOKEN_KEY); } catch (error) { console.error('Error getting auth token:', error); return null; } }; /** * Set authentication token in localStorage */ export const setAuthToken = (token: string): void => { try { localStorage.setItem(TOKEN_KEY, token); } catch (error) { console.error('Error setting auth token:', error); } }; /** * Remove authentication token from localStorage */ export const removeAuthToken = (): void => { try { localStorage.removeItem(TOKEN_KEY); localStorage.removeItem(REFRESH_TOKEN_KEY); localStorage.removeItem(USER_KEY); } catch (error) { console.error('Error removing auth token:', error); } }; /** * Get refresh token from localStorage */ export const getRefreshToken = (): string | null => { try { return localStorage.getItem(REFRESH_TOKEN_KEY); } catch (error) { console.error('Error getting refresh token:', error); return null; } }; /** * Set refresh token in localStorage */ export const setRefreshToken = (token: string): void => { try { localStorage.setItem(REFRESH_TOKEN_KEY, token); } catch (error) { console.error('Error setting refresh token:', error); } }; /** * Get user data from localStorage */ export const getUserData = (): any => { try { const userData = localStorage.getItem(USER_KEY); return userData ? JSON.parse(userData) : null; } catch (error) { console.error('Error getting user data:', error); return null; } }; /** * Set user data in localStorage */ export const setUserData = (userData: any): void => { try { localStorage.setItem(USER_KEY, JSON.stringify(userData)); } catch (error) { console.error('Error setting user data:', error); } }; /** * Check if user is authenticated */ export const isAuthenticated = (): boolean => { const token = getAuthToken(); if (!token) return false; try { // Basic JWT token validation (check if not expired) const payload = JSON.parse(atob(token.split('.')[1])); const currentTime = Date.now() / 1000; return payload.exp > currentTime; } catch (error) { console.error('Error validating token:', error); return false; } }; /** * Get authorization headers for API requests */ export const getAuthHeaders = (): Record<string, string> => { const token = getAuthToken(); return token ? { 'Authorization': `Bearer ${token}`, 'Content-Type': 'application/json', } : { 'Content-Type': 'application/json', }; }; /** * Clear all authentication data */ export const clearAuthData = (): void => { removeAuthToken(); }; /** * Check if token is expired */ export const isTokenExpired = (token: string): boolean => { try { const payload = JSON.parse(atob(token.split('.')[1])); const currentTime = Date.now() / 1000; return payload.exp <= currentTime; } catch (error) { return true; } }; /** * Get token expiration time */ export const getTokenExpiration = (token: string): number | null => { try { const payload = JSON.parse(atob(token.split('.')[1])); return payload.exp; } catch (error) { return null; } };