import { useEffect, useState, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import io, { Socket } from 'socket.io-client';
import { SOCKET_URL } from '../utils/constants';
import { RootState } from '../store';
import { addMessage, setTyping, setAgentOnline, updateConversationStatus } from '../store/slices/chatSlice';

export const useSocket = () => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [connected, setConnected] = useState(false);
  const dispatch = useDispatch();
  const { isAuthenticated, token } = useSelector((state: RootState) => state.auth);
  const { currentConversation } = useSelector((state: RootState) => state.chat);
  const socketRef = useRef<Socket | null>(null);

  useEffect(() => {
    if (!isAuthenticated || !token) {
      if (socketRef.current) {
        socketRef.current.disconnect();
        socketRef.current = null;
        setSocket(null);
        setConnected(false);
      }
      return;
    }

    // Créer la connexion socket
    const newSocket = io(SOCKET_URL, {
      auth: {
        token,
      },
      autoConnect: true,
      reconnection: true,
      reconnectionDelay: 1000,
      reconnectionAttempts: 5,
      timeout: 20000,
    });

    // Event listeners
    newSocket.on('connect', () => {
      console.log('Socket connecté:', newSocket.id);
      setConnected(true);

      // Rejoindre la conversation courante si elle existe
      if (currentConversation?._id) {
        newSocket.emit('join-conversation', currentConversation._id);
      }
    });

    newSocket.on('disconnect', (reason) => {
      console.log('Socket déconnecté:', reason);
      setConnected(false);
    });

    newSocket.on('reconnect', (attemptNumber) => {
      console.log('Socket reconnecté après', attemptNumber, 'tentatives');
      setConnected(true);
    });

    newSocket.on('connect_error', (error) => {
      console.error('Erreur de connexion socket:', error);
      setConnected(false);
    });

    // Messages du chat
    newSocket.on('new-message', (message) => {
      console.log('Nouveau message reçu:', message);
      dispatch(addMessage(message));
    });

    newSocket.on('bot-typing', (data) => {
      dispatch(setTyping(data.isTyping));
    });

    newSocket.on('agent-joined', (data) => {
      console.log('Agent rejoint:', data);
      dispatch(setAgentOnline(true));
      dispatch(updateConversationStatus({
        conversationId: data.conversationId,
        status: 'escalated'
      }));
    });

    newSocket.on('agent-left', (data) => {
      console.log('Agent quitté:', data);
      dispatch(setAgentOnline(false));
    });

    newSocket.on('conversation-status-changed', (data) => {
      dispatch(updateConversationStatus({
        conversationId: data.conversationId,
        status: data.status
      }));
    });

    // Notifications système
    newSocket.on('system-notification', (notification) => {
      console.log('Notification système:', notification);
      // Ici on pourrait dispatcher une action pour afficher la notification
    });

    socketRef.current = newSocket;
    setSocket(newSocket);

    // Cleanup
    return () => {
      if (newSocket) {
        newSocket.disconnect();
      }
    };
  }, [isAuthenticated, token, dispatch]);

  // Rejoindre une conversation
  useEffect(() => {
    if (socket && connected && currentConversation?._id) {
      socket.emit('join-conversation', currentConversation._id);
    }
  }, [socket, connected, currentConversation]);

  // Méthodes utilitaires
  const joinConversation = (conversationId: string) => {
    if (socket && connected) {
      socket.emit('join-conversation', conversationId);
    }
  };

  const leaveConversation = (conversationId: string) => {
    if (socket && connected) {
      socket.emit('leave-conversation', conversationId);
    }
  };

  const sendTyping = (conversationId: string, isTyping: boolean) => {
    if (socket && connected) {
      socket.emit('user-typing', { conversationId, isTyping });
    }
  };

  const escalateToAgent = (conversationId: string, reason?: string) => {
    if (socket && connected) {
      socket.emit('escalate-to-agent', { conversationId, reason });
    }
  };

  return {
    socket,
    connected,
    joinConversation,
    leaveConversation,
    sendTyping,
    escalateToAgent,
  };
};