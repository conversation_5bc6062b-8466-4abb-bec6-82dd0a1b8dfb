/** * Hook WebSocket pour les événements ML temps réel * Free Mobile Chatbot Dashboard - Phase 3 Frontend Implementation */ import { useEffect, useRef, useCallback, useState } from 'react'; import { io, Socket } from 'socket.io-client'; import { MLWebSocketEvent, ClassificationEvent, AlertEvent, MetricsEvent, PriorityQueueEvent, MLMetricsResponse, ConversationClassification, AdminAlert, PriorityQueueItem } from '../types/ml'; interface UseMLWebSocketOptions { autoConnect?: boolean; subscriptions?: string[]; onClassification?: (event: ClassificationEvent) => void; onAlert?: (event: AlertEvent) => void; onMetrics?: (event: MetricsEvent) => void; onPriorityQueue?: (event: PriorityQueueEvent) => void; onError?: (error: Error) => void; onConnect?: () => void; onDisconnect?: () => void; } interface MLWebSocketState { connected: boolean; connecting: boolean; error: string | null; lastEvent: MLWebSocketEvent | null; metrics: MLMetricsResponse | null; classifications: ConversationClassification[]; alerts: AdminAlert[]; priorityQueue: PriorityQueueItem[]; } export const useMLWebSocket = (options: UseMLWebSocketOptions = {}) => { const { autoConnect = true, subscriptions = ['ml-events'], onClassification, onAlert, onMetrics, onPriorityQueue, onError, onConnect, onDisconnect } = options; const socketRef = useRef<Socket | null>(null); const [state, setState] = useState<MLWebSocketState>({ connected: false, connecting: false, error: null, lastEvent: null, metrics: null, classifications: [], alerts: [], priorityQueue: [] }); /** * Connexion au WebSocket */ const connect = useCallback(() => { if (socketRef.current?.connected) { return; } setState(prev => ({ ...prev, connecting: true, error: null })); const token = localStorage.getItem('token'); const serverUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000'; socketRef.current = io(serverUrl, { auth: { token: token }, transports: ['websocket', 'polling'], timeout: 10000, reconnection: true, reconnectionAttempts: 5, reconnectionDelay: 1000 }); const socket = socketRef.current; // Événements de connexion socket.on('connect', () => { console.log('ML WebSocket connected'); setState(prev => ({ ...prev, connected: true, connecting: false, error: null })); // Souscription aux événements ML if (subscriptions.length > 0) { socket.emit('subscribe:ml-events', subscriptions); } onConnect?.(); }); socket.on('disconnect', (reason) => { console.log('ML WebSocket disconnected:', reason); setState(prev => ({ ...prev, connected: false, connecting: false })); onDisconnect?.(); }); socket.on('connect_error', (error) => { console.error('ML WebSocket connection error:', error); setState(prev => ({ ...prev, connected: false, connecting: false, error: error.message })); onError?.(error); }); // Événements ML spécifiques socket.on('ml:classification', (event: ClassificationEvent) => { console.log('New ML classification:', event); setState(prev => ({ ...prev, lastEvent: event as MLWebSocketEvent, classifications: [event.data as unknown as ConversationClassification, ...prev.classifications.slice(0, 49)] // Garder les 50 dernières })); onClassification?.(event); }); socket.on('ml:high-priority-classification', (event: ClassificationEvent) => { console.log('High priority classification:', event); setState(prev => ({ ...prev, lastEvent: event as MLWebSocketEvent, classifications: [event.data as unknown as ConversationClassification, ...prev.classifications.slice(0, 49)] })); onClassification?.(event); }); socket.on('ml:alert', (event: AlertEvent) => { console.log('New ML alert:', event); setState(prev => ({ ...prev, lastEvent: event as MLWebSocketEvent, alerts: [event.data as unknown as AdminAlert, ...prev.alerts.slice(0, 49)] // Garder les 50 dernières })); onAlert?.(event); }); socket.on('ml:critical-alert', (event: AlertEvent) => { console.log('Critical ML alert:', event); setState(prev => ({ ...prev, lastEvent: event as MLWebSocketEvent, alerts: [event.data as unknown as AdminAlert, ...prev.alerts.slice(0, 49)] })); onAlert?.(event); }); socket.on('ml:metrics', (event: MetricsEvent) => { console.log('ML metrics update:', event); setState(prev => ({ ...prev, lastEvent: event as MLWebSocketEvent, metrics: event.data })); onMetrics?.(event); }); socket.on('ml:priority-queue', (event: PriorityQueueEvent) => { console.log('Priority queue update:', event); setState(prev => ({ ...prev, lastEvent: event as MLWebSocketEvent, priorityQueue: event.data })); onPriorityQueue?.(event); }); // Événements d'état initial socket.on('initial:metrics', (metrics: MLMetricsResponse) => { console.log('Initial metrics received:', metrics); setState(prev => ({ ...prev, metrics })); }); socket.on('initial:alerts', (alerts: AdminAlert[]) => { console.log('Initial alerts received:', alerts.length); setState(prev => ({ ...prev, alerts })); }); socket.on('initial:priority-queue', (queue: PriorityQueueItem[]) => { console.log('Initial priority queue received:', queue.length); setState(prev => ({ ...prev, priorityQueue: queue })); }); // Gestion des erreurs génériques socket.on('error', (error: any) => { console.error('ML WebSocket error:', error); setState(prev => ({ ...prev, error: error.message || 'WebSocket error' })); onError?.(new Error(error.message || 'WebSocket error')); }); }, [subscriptions, onClassification, onAlert, onMetrics, onPriorityQueue, onError, onConnect, onDisconnect]); /** * Déconnexion du WebSocket */ const disconnect = useCallback(() => { if (socketRef.current) { socketRef.current.disconnect(); socketRef.current = null; setState(prev => ({ ...prev, connected: false, connecting: false })); } }, []); /** * Souscription à des événements spécifiques */ const subscribe = useCallback((eventTypes: string[]) => { if (socketRef.current?.connected) { socketRef.current.emit('subscribe:ml-events', eventTypes); } }, []); /** * Désouscription d'événements */ const unsubscribe = useCallback((eventTypes: string[]) => { if (socketRef.current?.connected) { socketRef.current.emit('unsubscribe:ml-events', eventTypes); } }, []); /** * Demande de métriques en temps réel */ const requestMetrics = useCallback((options: any = {}) => { if (socketRef.current?.connected) { socketRef.current.emit('request:metrics', options); } }, []); /** * Demande de queue de priorité */ const requestPriorityQueue = useCallback((options: any = {}) => { if (socketRef.current?.connected) { socketRef.current.emit('request:priority-queue', options); } }, []); /** * Ping pour maintenir la connexion */ const ping = useCallback(() => { if (socketRef.current?.connected) { socketRef.current.emit('ping'); } }, []); // Auto-connexion useEffect(() => { if (autoConnect) { connect(); } return () => { disconnect(); }; }, [autoConnect, connect, disconnect]); // Ping périodique pour maintenir la connexion useEffect(() => { if (state.connected) { const pingInterval = setInterval(ping, 30000); // Ping toutes les 30 secondes return () => clearInterval(pingInterval); } }, [state.connected, ping]); // Nettoyage à la fermeture useEffect(() => { return () => { if (socketRef.current) { socketRef.current.disconnect(); } }; }, []); return { // État connected: state.connected, connecting: state.connecting, error: state.error, lastEvent: state.lastEvent, // Données temps réel metrics: state.metrics, classifications: state.classifications, alerts: state.alerts, priorityQueue: state.priorityQueue, // Actions connect, disconnect, subscribe, unsubscribe, requestMetrics, requestPriorityQueue, ping, // Utilitaires isConnected: state.connected, hasError: !!state.error, socket: socketRef.current }; }; export default useMLWebSocket;