import { useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState, AppDispatch } from '../store';
import { login, register, logout, clearError, validateToken } from '../store/authSlice';
import { RegisterData } from '../types';

export const useAuth = () => {
  const dispatch = useDispatch<AppDispatch>();
  const auth = useSelector((state: RootState) => state.auth);

  const handleLogin = useCallback((email: string, password: string) => {
    return dispatch(login({ email, password }));
  }, [dispatch]);

  const handleRegister = useCallback((data: RegisterData) => {
    return dispatch(register(data));
  }, [dispatch]);

  const handleLogout = useCallback(() => {
    dispatch(logout());
  }, [dispatch]);

  const handleClearError = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  const handleValidateToken = useCallback(() => {
    return dispatch(validateToken());
  }, [dispatch]);

  return {
    ...auth,
    login: handleLogin,
    register: handleRegister,
    logout: handleLogout,
    clearError: handleClearError,
    validateToken: handleValidateToken,
  };
};