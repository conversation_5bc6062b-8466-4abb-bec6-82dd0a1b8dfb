/** * ============================================= * WEBSOCKET HOOK * Custom React hook for WebSocket connections * Real-time communication with backend services * ============================================= */ import { useEffect, useRef, useState, useCallback } from 'react'; import { useDispatch, useSelector } from 'react-redux'; import io from 'socket.io-client'; import { toast } from 'react-toastify'; // Import Redux actions for real-time updates import { setSocketConnected as setSimulationSocketConnected, updateRealTimeMetrics as updateSimulationMetrics, addAICoaching, updateSessionProgress, addSessionMessage } from '../store/slices/simulationSlice'; import { setSocketConnected as setPredictiveSocketConnected, addRealTimeAlert, updateSystemHealthScore, updateChurnPrediction, updateEscalationPrediction, addAnomaly } from '../store/slices/predictiveSlice'; import { setSocketConnected as setEnhancedAISocketConnected, addRealTimeSuggestion, addUrgentAlert, updateSentimentAnalysis, updateEscalationRisk } from '../store/slices/enhancedAISlice'; import { updateRealTimeMetrics as updateAnalyticsMetrics, updateLiveKPIs, updateSystemHealth } from '../store/slices/analyticsSlice'; /** * WebSocket hook for real-time connections * @param {string} namespace - Socket.IO namespace to connect to * @param {object} options - Connection options * @returns {object} Socket instance and connection state */ export const useWebSocket = (namespace = '', options = {}) => { const dispatch = useDispatch(); const { user } = useSelector(state => state.auth); const [socket, setSocket] = useState(null); const [isConnected, setIsConnected] = useState(false); const [connectionError, setConnectionError] = useState(null); const [reconnectAttempts, setReconnectAttempts] = useState(0); const reconnectTimeoutRef = useRef(null); const maxReconnectAttempts = 5; const reconnectDelay = 1000; // Start with 1 second const defaultOptions = { transports: ['websocket', 'polling'], timeout: 20000, forceNew: false, reconnection: true, reconnectionAttempts: maxReconnectAttempts, reconnectionDelay: reconnectDelay, reconnectionDelayMax: 10000, maxHttpBufferSize: 1e6, pingTimeout: 60000, pingInterval: 25000, ...options }; // Get backend URL const getBackendURL = useCallback(() => { return process.env.REACT_APP_BACKEND_URL || 'http://localhost:5000'; }, []); // Connect to WebSocket const connect = useCallback(() => { if (socket?.connected) { return socket; } try { const backendURL = getBackendURL(); const socketURL = namespace ? `${backendURL}${namespace}` : backendURL; console.log(`Connecting to WebSocket: ${socketURL}`); const newSocket = io(socketURL, { ...defaultOptions, auth: { token: localStorage.getItem('token'), userId: user?.id, role: user?.role } }); // Connection event handlers newSocket.on('connect', () => { console.log(`WebSocket connected to ${namespace || 'main'}`); setIsConnected(true); setConnectionError(null); setReconnectAttempts(0); // Update Redux state based on namespace switch (namespace) { case '/simulation': dispatch(setSimulationSocketConnected(true)); break; case '/predictive': dispatch(setPredictiveSocketConnected(true)); break; case '/enhanced-ai': dispatch(setEnhancedAISocketConnected(true)); break; default: break; } }); newSocket.on('disconnect', (reason) => { console.log(`WebSocket disconnected from ${namespace || 'main'}:`, reason); setIsConnected(false); // Update Redux state switch (namespace) { case '/simulation': dispatch(setSimulationSocketConnected(false)); break; case '/predictive': dispatch(setPredictiveSocketConnected(false)); break; case '/enhanced-ai': dispatch(setEnhancedAISocketConnected(false)); break; default: break; } // Handle reconnection for certain disconnect reasons if (reason === 'io server disconnect') { // Server initiated disconnect, try to reconnect handleReconnect(); } }); newSocket.on('connect_error', (error) => { console.error(`WebSocket connection error for ${namespace || 'main'}:`, error); setConnectionError(error.message); setIsConnected(false); handleReconnect(); }); // Set up namespace-specific event handlers setupNamespaceHandlers(newSocket, namespace); setSocket(newSocket); return newSocket; } catch (error) { console.error('Error creating WebSocket connection:', error); setConnectionError(error.message); return null; } }, [namespace, user, dispatch, defaultOptions, getBackendURL]); // Handle reconnection logic const handleReconnect = useCallback(() => { if (reconnectAttempts >= maxReconnectAttempts) { console.log('Max reconnection attempts reached'); toast.error('Unable to establish real-time connection. Please refresh the page.'); return; } const delay = Math.min(reconnectDelay * Math.pow(2, reconnectAttempts), 10000); if (reconnectTimeoutRef.current) { clearTimeout(reconnectTimeoutRef.current); } reconnectTimeoutRef.current = setTimeout(() => { console.log(`Attempting to reconnect (${reconnectAttempts + 1}/${maxReconnectAttempts})...`); setReconnectAttempts(prev => prev + 1); connect(); }, delay); }, [reconnectAttempts, connect]); // Set up namespace-specific event handlers const setupNamespaceHandlers = useCallback((socket, namespace) => { switch (namespace) { case '/simulation': setupSimulationHandlers(socket); break; case '/predictive': setupPredictiveHandlers(socket); break; case '/enhanced-ai': setupEnhancedAIHandlers(socket); break; default: setupGeneralHandlers(socket); break; } }, [dispatch]); // Simulation namespace handlers const setupSimulationHandlers = useCallback((socket) => { socket.on('session_joined', (data) => { console.log('Simulation session joined:', data); }); socket.on('message_processed', (data) => { dispatch(addSessionMessage(data.message)); if (data.performance_metrics) { dispatch(updateSessionProgress({ performance_metrics: data.performance_metrics })); } }); socket.on('ai_coaching', (coaching) => { dispatch(addAICoaching(coaching)); }); socket.on('session_metrics_update', (metrics) => { dispatch(updateSimulationMetrics(metrics)); }); socket.on('session_ended', (results) => { console.log('Simulation session ended:', results); toast.success('Simulation session completed!'); }); }, [dispatch]); // Predictive namespace handlers const setupPredictiveHandlers = useCallback((socket) => { socket.on('churn_prediction_update', (prediction) => { dispatch(updateChurnPrediction(prediction)); dispatch(addRealTimeAlert({ type: 'churn_risk', severity: prediction.risk_level, message: `High churn risk detected for customer ${prediction.customer_id}`, data: prediction })); }); socket.on('escalation_risk_alert', (alert) => { dispatch(updateEscalationPrediction(alert.prediction)); dispatch(addRealTimeAlert({ type: 'escalation_risk', severity: alert.risk_level, message: alert.message, data: alert })); }); socket.on('anomaly_detected', (anomaly) => { dispatch(addAnomaly(anomaly)); dispatch(addRealTimeAlert({ type: 'anomaly', severity: anomaly.severity, message: `Anomaly detected: ${anomaly.description}`, data: anomaly })); }); socket.on('system_health_update', (healthData) => { dispatch(updateSystemHealthScore(healthData.score)); }); }, [dispatch]); // Enhanced AI namespace handlers const setupEnhancedAIHandlers = useCallback((socket) => { socket.on('suggestions_generated', (data) => { dispatch(addRealTimeSuggestion(data)); }); socket.on('sentiment_update', (data) => { dispatch(updateSentimentAnalysis(data)); if (data.escalation_risk > 0.7) { dispatch(addUrgentAlert({ type: 'high_escalation_risk', message: 'High escalation risk detected in current conversation', data })); } }); socket.on('urgent_suggestions', (data) => { dispatch(addUrgentAlert({ type: 'urgent_suggestions', message: 'Urgent AI suggestions available', data })); }); socket.on('escalation_analysis', (analysis) => { dispatch(updateEscalationRisk(analysis)); }); }, [dispatch]); // General handlers for main namespace const setupGeneralHandlers = useCallback((socket) => { socket.on('system_metrics_update', (metrics) => { dispatch(updateAnalyticsMetrics(metrics)); }); socket.on('kpi_update', (kpis) => { dispatch(updateLiveKPIs(kpis)); }); socket.on('health_update', (health) => { dispatch(updateSystemHealth(health)); }); }, [dispatch]); // Disconnect WebSocket const disconnect = useCallback(() => { if (reconnectTimeoutRef.current) { clearTimeout(reconnectTimeoutRef.current); } if (socket) { socket.disconnect(); setSocket(null); setIsConnected(false); setConnectionError(null); setReconnectAttempts(0); } }, [socket]); // Send message through WebSocket const sendMessage = useCallback((event, data) => { if (socket && isConnected) { socket.emit(event, data); return true; } else { console.warn('WebSocket not connected, cannot send message'); return false; } }, [socket, isConnected]); // Initialize connection useEffect(() => { if (user?.id) { connect(); } return () => { disconnect(); }; }, [user?.id, namespace]); // Cleanup on unmount useEffect(() => { return () => { if (reconnectTimeoutRef.current) { clearTimeout(reconnectTimeoutRef.current); } disconnect(); }; }, [disconnect]); return { socket, isConnected, connectionError, reconnectAttempts, connect, disconnect, sendMessage }; }; export default useWebSocket;