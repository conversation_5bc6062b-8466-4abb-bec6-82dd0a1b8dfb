import { useState, useEffect, useCallback } from 'react';
import analyticsService, { VolumeData, CategoryData, PerformanceData } from '../services/analytics.service';

interface UseAnalyticsState {
  volumeData: VolumeData[];
  categoryData: CategoryData[];
  performanceData: PerformanceData[];
  loading: {
    volume: boolean;
    category: boolean;
    performance: boolean;
  };
  error: {
    volume: string | null;
    category: string | null;
    performance: string | null;
  };
}

interface UseAnalyticsReturn extends UseAnalyticsState {
  refreshVolumeData: (timeRange: string) => Promise<void>;
  refreshCategoryData: () => Promise<void>;
  refreshPerformanceData: (timeRange: string) => Promise<void>;
  refreshAllData: (timeRange?: string) => Promise<void>;
  clearErrors: () => void;
}

export const useAnalytics = (initialTimeRange: string = '7d'): UseAnalyticsReturn => {
  const [state, setState] = useState<UseAnalyticsState>({
    volumeData: [],
    categoryData: [],
    performanceData: [],
    loading: {
      volume: false,
      category: false,
      performance: false,
    },
    error: {
      volume: null,
      category: null,
      performance: null,
    },
  });

  // Set loading state for specific data type
  const setLoading = useCallback((type: keyof UseAnalyticsState['loading'], isLoading: boolean) => {
    setState(prev => ({
      ...prev,
      loading: {
        ...prev.loading,
        [type]: isLoading,
      },
    }));
  }, []);

  // Set error state for specific data type
  const setError = useCallback((type: keyof UseAnalyticsState['error'], error: string | null) => {
    setState(prev => ({
      ...prev,
      error: {
        ...prev.error,
        [type]: error,
      },
    }));
  }, []);

  // Refresh volume data
  const refreshVolumeData = useCallback(async (timeRange: string) => {
    setLoading('volume', true);
    setError('volume', null);
    
    try {
      const data = await analyticsService.getVolumeData(timeRange);
      setState(prev => ({
        ...prev,
        volumeData: data,
      }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch volume data';
      setError('volume', errorMessage);
      console.error('Error fetching volume data:', error);
    } finally {
      setLoading('volume', false);
    }
  }, [setLoading, setError]);

  // Refresh category data
  const refreshCategoryData = useCallback(async () => {
    setLoading('category', true);
    setError('category', null);
    
    try {
      const data = await analyticsService.getCategoryData();
      setState(prev => ({
        ...prev,
        categoryData: data,
      }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch category data';
      setError('category', errorMessage);
      console.error('Error fetching category data:', error);
    } finally {
      setLoading('category', false);
    }
  }, [setLoading, setError]);

  // Refresh performance data
  const refreshPerformanceData = useCallback(async (timeRange: string) => {
    setLoading('performance', true);
    setError('performance', null);
    
    try {
      const data = await analyticsService.getPerformanceTrends(timeRange);
      setState(prev => ({
        ...prev,
        performanceData: data,
      }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch performance data';
      setError('performance', errorMessage);
      console.error('Error fetching performance data:', error);
    } finally {
      setLoading('performance', false);
    }
  }, [setLoading, setError]);

  // Refresh all data
  const refreshAllData = useCallback(async (timeRange: string = initialTimeRange) => {
    await Promise.all([
      refreshVolumeData(timeRange),
      refreshCategoryData(),
      refreshPerformanceData(timeRange),
    ]);
  }, [refreshVolumeData, refreshCategoryData, refreshPerformanceData, initialTimeRange]);

  // Clear all errors
  const clearErrors = useCallback(() => {
    setState(prev => ({
      ...prev,
      error: {
        volume: null,
        category: null,
        performance: null,
      },
    }));
  }, []);

  // Initial data load
  useEffect(() => {
    refreshAllData(initialTimeRange);
  }, [refreshAllData, initialTimeRange]);

  return {
    ...state,
    refreshVolumeData,
    refreshCategoryData,
    refreshPerformanceData,
    refreshAllData,
    clearErrors,
  };
};

// Hook for real-time data updates
export const useRealTimeAnalytics = (refreshInterval: number = 30000) => {
  const [realTimeData, setRealTimeData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchRealTimeData = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await analyticsService.getRealtimeMetrics();
      setRealTimeData(response.data);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch real-time data';
      setError(errorMessage);
      console.error('Error fetching real-time data:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    // Initial fetch
    fetchRealTimeData();

    // Set up polling interval
    const interval = setInterval(fetchRealTimeData, refreshInterval);

    return () => clearInterval(interval);
  }, [fetchRealTimeData, refreshInterval]);

  return {
    realTimeData,
    loading,
    error,
    refresh: fetchRealTimeData,
  };
};

// Hook for WebSocket real-time updates
export const useWebSocketAnalytics = () => {
  const [wsData, setWsData] = useState<any>(null);
  const [connected, setConnected] = useState(false);

  useEffect(() => {
    // WebSocket connection logic would go here
    // For now, we'll use polling as fallback
    console.log('WebSocket analytics connection would be established here');
    
    return () => {
      console.log('WebSocket analytics connection would be closed here');
    };
  }, []);

  return {
    data: wsData,
    connected,
  };
};
