/** * ============================================= * SOCIAL MEDIA API HOOK * Custom hook for social media service integration * Handles all API calls with error handling and caching * ============================================= */ import { useState, useCallback, useRef } from 'react'; import { useAuth } from '../contexts/AuthContext'; import { useNotification } from '../contexts/NotificationContext'; const SOCIAL_MEDIA_API_BASE = process.env.REACT_APP_SOCIAL_MEDIA_API_URL || 'http://localhost:5010/api'; export const useSocialMediaAPI = () => { const { token } = useAuth(); const { showNotification } = useNotification(); const [loading, setLoading] = useState(false); const [error, setError] = useState(null); const abortControllerRef = useRef(null); // Base API call function const apiCall = useCallback(async (endpoint, options = {}) => { try { // Cancel previous request if still pending if (abortControllerRef.current) { abortControllerRef.current.abort(); } // Create new abort controller abortControllerRef.current = new AbortController(); const config = { method: 'GET', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${token}`, ...options.headers }, signal: abortControllerRef.current.signal, ...options }; if (options.body && typeof options.body === 'object') { config.body = JSON.stringify(options.body); } const response = await fetch(`${SOCIAL_MEDIA_API_BASE}${endpoint}`, config); if (!response.ok) { const errorData = await response.json().catch(() => ({})); throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`); } const data = await response.json(); return data; } catch (err) { if (err.name === 'AbortError') { console.log('Request aborted'); return null; } console.error('API call failed:', err); setError(err.message); if (!options.silent) { showNotification(`Erreur API: ${err.message}`, 'error'); } throw err; } }, [token, showNotification]); // Get conversations const getConversations = useCallback(async (filters = {}) => { setLoading(true); setError(null); try { const queryParams = new URLSearchParams(); Object.entries(filters).forEach(([key, value]) => { if (value !== undefined && value !== null && value !== '') { queryParams.append(key, value); } }); const endpoint = `/conversations${queryParams.toString() ? `?${queryParams.toString()}` : ''}`; const data = await apiCall(endpoint); return data; } catch (err) { return { conversations: [], total: 0 }; } finally { setLoading(false); } }, [apiCall]); // Get conversation details const getConversationDetails = useCallback(async (conversationId) => { setLoading(true); setError(null); try { const data = await apiCall(`/conversations/${conversationId}`); return data; } catch (err) { return null; } finally { setLoading(false); } }, [apiCall]); // Get customer details const getCustomerDetails = useCallback(async (customerId) => { setLoading(true); setError(null); try { const data = await apiCall(`/customers/${customerId}`); return data; } catch (err) { return null; } finally { setLoading(false); } }, [apiCall]); // Send message const sendMessage = useCallback(async (conversationId, messageData, agentId) => { setLoading(true); setError(null); try { const data = await apiCall(`/conversations/${conversationId}/messages`, { method: 'POST', body: { ...messageData, agentId } }); return data; } catch (err) { throw err; } finally { setLoading(false); } }, [apiCall]); // Update agent status const updateAgentStatus = useCallback(async (agentId, status) => { try { const data = await apiCall(`/agents/${agentId}/status`, { method: 'PUT', body: { status }, silent: true }); return data; } catch (err) { console.error('Failed to update agent status:', err); return null; } }, [apiCall]); // Mark, conversation as viewed const markConversationAsViewed = useCallback(async (conversationId, agentId) => { try { await apiCall(`/conversations/${conversationId}/view`, { method: 'POST', body: { agentId }, silent: true }); } catch (err) { console.error('Failed to mark, conversation as viewed:', err); } }, [apiCall]); // Get metrics const getMetrics = useCallback(async (filters = {}) => { try { const queryParams = new URLSearchParams(); Object.entries(filters).forEach(([key, value]) => { if (value !== undefined && value !== null && value !== '') { queryParams.append(key, value); } }); const endpoint = `/metrics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`; const data = await apiCall(endpoint, { silent: true }); return data || { totalConversations: 0, activeConversations: 0, pendingConversations: 0, averageResponseTime: 0, platformBreakdown: {} }; } catch (err) { return { totalConversations: 0, activeConversations: 0, pendingConversations: 0, averageResponseTime: 0, platformBreakdown: {} }; } }, [apiCall]); // Assign conversation const assignConversation = useCallback(async (conversationId, agentId) => { setLoading(true); setError(null); try { const data = await apiCall(`/conversations/${conversationId}/assign`, { method: 'POST', body: { agentId } }); showNotification('Conversation assignée avec succès', 'success'); return data; } catch (err) { throw err; } finally { setLoading(false); } }, [apiCall, showNotification]); // Escalate conversation const escalateConversation = useCallback(async (conversationId, reason) => { setLoading(true); setError(null); try { const data = await apiCall(`/conversations/${conversationId}/escalate`, { method: 'POST', body: { reason } }); showNotification('Conversation escaladée avec succès', 'success'); return data; } catch (err) { throw err; } finally { setLoading(false); } }, [apiCall, showNotification]); // Resolve conversation const resolveConversation = useCallback(async (conversationId, resolutionData = {}) => { setLoading(true); setError(null); try { const data = await apiCall(`/conversations/${conversationId}/resolve`, { method: 'POST', body: resolutionData }); showNotification('Conversation résolue avec succès', 'success'); return data; } catch (err) { throw err; } finally { setLoading(false); } }, [apiCall, showNotification]); // Transfer conversation const transferConversation = useCallback(async (conversationId, targetAgentId, reason) => { setLoading(true); setError(null); try { const data = await apiCall(`/conversations/${conversationId}/transfer`, { method: 'POST', body: { targetAgentId, reason } }); showNotification('Conversation transférée avec succès', 'success'); return data; } catch (err) { throw err; } finally { setLoading(false); } }, [apiCall, showNotification]); // Upload media const uploadMedia = useCallback(async (file, platform) => { setLoading(true); setError(null); try { const formData = new FormData(); formData.append('file', file); formData.append('platform', platform); const data = await apiCall('/media/upload', { method: 'POST', headers: { 'Authorization': `Bearer ${token}` // Don't set Content-Type for FormData }, body: formData }); return data; } catch (err) { throw err; } finally { setLoading(false); } }, [apiCall, token]); // Get message templates const getMessageTemplates = useCallback(async (platform) => { try { const data = await apiCall(`/templates?platform=${platform}`, { silent: true }); return data?.templates || []; } catch (err) { return []; } }, [apiCall]); // Get quick replies const getQuickReplies = useCallback(async (platform) => { try { const data = await apiCall(`/quick-replies?platform=${platform}`, { silent: true }); return data?.quickReplies || []; } catch (err) { return []; } }, [apiCall]); // Search conversations const searchConversations = useCallback(async (query, filters = {}) => { setLoading(true); setError(null); try { const queryParams = new URLSearchParams(); queryParams.append('q', query); Object.entries(filters).forEach(([key, value]) => { if (value !== undefined && value !== null && value !== '') { queryParams.append(key, value); } }); const data = await apiCall(`/conversations/search?${queryParams.toString()}`); return data; } catch (err) { return { conversations: [], total: 0 }; } finally { setLoading(false); } }, [apiCall]); // Get conversation analytics const getConversationAnalytics = useCallback(async (conversationId) => { try { const data = await apiCall(`/conversations/${conversationId}/analytics`, { silent: true }); return data; } catch (err) { return null; } }, [apiCall]); // Cancel ongoing requests const cancelRequests = useCallback(() => { if (abortControllerRef.current) { abortControllerRef.current.abort(); abortControllerRef.current = null; } }, []); return { // State loading, error, // Conversation methods getConversations, getConversationDetails, searchConversations, assignConversation, escalateConversation, resolveConversation, transferConversation, markConversationAsViewed, getConversationAnalytics, // Customer methods getCustomerDetails, // Message methods sendMessage, uploadMedia, getMessageTemplates, getQuickReplies, // Agent methods updateAgentStatus, // Analytics methods getMetrics, // Utility methods cancelRequests }; };