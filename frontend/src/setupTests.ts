// jest-dom adds custom jest matchers for asserting on DOM nodes. // allows you to do things like: // expect(element).toHaveTextContent(/react/i) // learn more: https://github.com/testing-library/jest-dom import '@testing-library/jest-dom'; // Mock axios before any imports that might use it jest.mock('axios', () => { const mockAxios = jest.fn(() => Promise.resolve({ data: {} })); // Add all HTTP methods mockAxios.get = jest.fn(() => Promise.resolve({ data: {} })); mockAxios.post = jest.fn(() => Promise.resolve({ data: {} })); mockAxios.put = jest.fn(() => Promise.resolve({ data: {} })); mockAxios.delete = jest.fn(() => Promise.resolve({ data: {} })); mockAxios.patch = jest.fn(() => Promise.resolve({ data: {} })); mockAxios.head = jest.fn(() => Promise.resolve({ data: {} })); mockAxios.options = jest.fn(() => Promise.resolve({ data: {} })); mockAxios.request = jest.fn(() => Promise.resolve({ data: {} })); // Add create method mockAxios.create = jest.fn(() => ({ get: jest.fn(() => Promise.resolve({ data: {} })), post: jest.fn(() => Promise.resolve({ data: {} })), put: jest.fn(() => Promise.resolve({ data: {} })), delete: jest.fn(() => Promise.resolve({ data: {} })), patch: jest.fn(() => Promise.resolve({ data: {} })), head: jest.fn(() => Promise.resolve({ data: {} })), options: jest.fn(() => Promise.resolve({ data: {} })), request: jest.fn(() => Promise.resolve({ data: {} })), interceptors: { request: { use: jest.fn(), eject: jest.fn(), clear: jest.fn() }, response: { use: jest.fn(), eject: jest.fn(), clear: jest.fn() }, }, defaults: { headers: { common: {}, delete: {}, get: {}, head: {}, post: {}, put: {}, patch: {} }, timeout: 0, baseURL: '', }, })); // Add interceptors and other properties mockAxios.interceptors = { request: { use: jest.fn(), eject: jest.fn(), clear: jest.fn() }, response: { use: jest.fn(), eject: jest.fn(), clear: jest.fn() }, }; mockAxios.defaults = { headers: { common: {}, delete: {}, get: {}, head: {}, post: {}, put: {}, patch: {} }, timeout: 0, baseURL: '', }; mockAxios.isAxiosError = jest.fn(() => false); mockAxios.CancelToken = { source: jest.fn(() => ({ token: {}, cancel: jest.fn() })) }; mockAxios.Cancel = jest.fn(); mockAxios.isCancel = jest.fn(() => false); return mockAxios; });