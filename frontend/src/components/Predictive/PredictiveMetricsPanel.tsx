/** * ============================================= * [ANALYTICS] PREDICTIVE METRICS PANEL COMPONENT * ML model performance and business impact metrics * ============================================= */ import React from 'react'; import { Box, Typography, Card, CardContent, Grid, LinearProgress, Chip } from '@mui/material'; import { Assessment as MetricsIcon, ModelTraining as ModelIcon, TrendingUp as ImpactIcon, Schedule as UpdateIcon } from '@mui/icons-material'; interface PredictiveMetricsPanelProps { metrics: any; loading: boolean; } const PredictiveMetricsPanel: React.FC<PredictiveMetricsPanelProps> = ({ metrics, loading }) => { const getAccuracyColor = (accuracy: number) => { if (accuracy >= 0.9) return 'success'; if (accuracy >= 0.8) return 'warning'; return 'error'; }; const formatCurrency = (amount: number) => { return new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(amount); }; const formatPercentage = (value: number) => { return `${Math.round(value * 100)}%`; }; return ( <Box> <Grid container spacing={3}> {/* Model Accuracy */} <Grid item xs={12} md={6}> <Card> <CardContent> <Typography variant="h6" sx={{ mb: 2 }}> <ModelIcon sx={{ mr: 1, verticalAlign: 'middle' }} /> Précision des Modèles </Typography> {[ { name: 'Prédiction Churn', accuracy: 0.945 }, { name: 'Prévision Demande', accuracy: 0.892 }, { name: 'Prédiction Escalade', accuracy: 0.912 }, { name: 'Détection Anomalies', accuracy: 0.887 } ].map((model, index) => ( <Box key={index} sx={{ mb: 2 }}> <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}> <Typography variant="body2"> {model.name} </Typography> <Typography variant="body2" color={getAccuracyColor(model.accuracy) + '.main'}> {formatPercentage(model.accuracy)} </Typography> </Box> <LinearProgress variant="determinate" value={model.accuracy * 100} color={getAccuracyColor(model.accuracy)} sx={{ height: 6, borderRadius: 3 }} /> </Box> ))} </CardContent> </Card> </Grid> {/* Business Impact */} <Grid item xs={12} md={6}> <Card> <CardContent> <Typography variant="h6" sx={{ mb: 2 }}> <ImpactIcon sx={{ mr: 1, verticalAlign: 'middle' }} /> Impact Business </Typography> <Grid container spacing={2}> <Grid item xs={6}> <Typography variant="body2" color="text.secondary"> Churn Évité </Typography> <Typography variant="h6" color="success.main"> {metrics?.business_impact?.churn_prevented || 127} </Typography> <Typography variant="caption" color="text.secondary"> clients ce mois </Typography> </Grid> <Grid item xs={6}> <Typography variant="body2" color="text.secondary"> Économies </Typography> <Typography variant="h6" color="success.main"> {formatCurrency(metrics?.business_impact?.cost_savings || 45000)} </Typography> <Typography variant="caption" color="text.secondary"> ce mois </Typography> </Grid> <Grid item xs={6}> <Typography variant="body2" color="text.secondary"> Efficacité + </Typography> <Typography variant="h6" color="info.main"> {formatPercentage(metrics?.business_impact?.efficiency_improvement || 0.23)} </Typography> <Typography variant="caption" color="text.secondary"> amélioration </Typography> </Grid> <Grid item xs={6}> <Typography variant="body2" color="text.secondary"> Satisfaction + </Typography> <Typography variant="h6" color="warning.main"> {formatPercentage(metrics?.business_impact?.satisfaction_improvement || 0.15)} </Typography> <Typography variant="caption" color="text.secondary"> amélioration </Typography> </Grid> </Grid> </CardContent> </Card> </Grid> {/* Prediction Confidence */} <Grid item xs={12} md={6}> <Card> <CardContent> <Typography variant="h6" sx={{ mb: 2 }}> <MetricsIcon sx={{ mr: 1, verticalAlign: 'middle' }} /> Confiance des Prédictions </Typography> <Box sx={{ mb: 2 }}> <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}> Confiance Moyenne </Typography> <Typography variant="h4" color="primary.main"> {formatPercentage(metrics?.prediction_confidence?.average || 0.87)} </Typography> </Box> <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}> {Object.entries(metrics?.prediction_confidence?.by_category || { 'Churn': 0.92, 'Demande': 0.85, 'Escalade': 0.89, 'Anomalies': 0.83 }).map(([category, confidence]) => ( <Chip key={category} label={`${category}: ${formatPercentage(confidence as number)}`} color={getAccuracyColor(confidence as number)} size="small" /> ))} </Box> </CardContent> </Card> </Grid> {/* Model Updates */} <Grid item xs={12} md={6}> <Card> <CardContent> <Typography variant="h6" sx={{ mb: 2 }}> <UpdateIcon sx={{ mr: 1, verticalAlign: 'middle' }} /> Mises à Jour des Modèles </Typography> <Box sx={{ mb: 2 }}> <Typography variant="body2" color="text.secondary"> Dernière Mise à Jour </Typography> <Typography variant="body1"> {metrics?.last_model_update || 'Il y a 2 heures'} </Typography> </Box> <Box sx={{ mb: 2 }}> <Typography variant="body2" color="text.secondary"> Prochaine Mise à Jour </Typography> <Typography variant="body1"> Dans 6 heures </Typography> </Box> <Box> <Typography variant="body2" color="text.secondary"> Statut </Typography> <Chip label="Modèles à jour" color="success" size="small" /> </Box> </CardContent> </Card> </Grid> </Grid> </Box> ); }; export default PredictiveMetricsPanel;