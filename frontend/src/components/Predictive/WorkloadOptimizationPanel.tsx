/** * ============================================= * WORKLOAD OPTIMIZATION PANEL COMPONENT * Agent workload optimization and assignment recommendations * ============================================= */ import React from 'react'; import { Box, Typography, Card, CardContent, Grid, LinearProgress, Chip, List, ListItem, ListItemText, Avatar, Button } from '@mui/material'; import { Assignment as WorkloadIcon, Person as AgentIcon, TrendingUp as EfficiencyIcon, Recommend as RecommendIcon } from '@mui/icons-material'; interface WorkloadOptimizationPanelProps { agentWorkloads: any[]; teamEfficiency: number; optimalAssignments: any[]; loading: boolean; } const WorkloadOptimizationPanel: React.FC<WorkloadOptimizationPanelProps> = ({ agentWorkloads, teamEfficiency, optimalAssignments, loading }) => { const getWorkloadColor = (workload: number) => { if (workload > 85) return 'error'; if (workload > 70) return 'warning'; return 'success'; }; const getEfficiencyColor = (efficiency: number) => { if (efficiency >= 90) return 'success'; if (efficiency >= 70) return 'warning'; return 'error'; }; return ( <Box> <Typography variant="h6" sx={{ mb: 3 }}> Optimisation de la Charge de Travail </Typography> <Grid container spacing={3}> {/* Team Efficiency */} <Grid item xs={12} md={4}> <Card> <CardContent> <Typography variant="h6" sx={{ mb: 2 }}> <EfficiencyIcon sx={{ mr: 1, verticalAlign: 'middle' }} /> Efficacité Équipe </Typography> <Box sx={{ textAlign: 'center', mb: 2 }}> <Typography variant="h3" color={getEfficiencyColor(teamEfficiency) + '.main'}> {Math.round(teamEfficiency)}% </Typography> <LinearProgress variant="determinate" value={teamEfficiency} color={getEfficiencyColor(teamEfficiency)} sx={{ mt: 1, height: 8, borderRadius: 4 }} /> </Box> <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}> <Typography variant="body2" color="text.secondary"> Agents Actifs </Typography> <Typography variant="body2"> 8/12 </Typography> </Box> <Box sx={{ display: 'flex', justifyContent: 'space-between' }}> <Typography variant="body2" color="text.secondary"> Charge Moyenne </Typography> <Typography variant="body2"> 73% </Typography> </Box> </CardContent> </Card> </Grid> {/* Agent Workloads */} <Grid item xs={12} md={8}> <Card> <CardContent> <Typography variant="h6" sx={{ mb: 2 }}> <WorkloadIcon sx={{ mr: 1, verticalAlign: 'middle' }} /> Charge par Agent </Typography> <List> {[ { name: 'Agent 1', current: 85, optimal: 75, efficiency: 92 }, { name: 'Agent 2', current: 65, optimal: 70, efficiency: 88 }, { name: 'Agent 3', current: 90, optimal: 80, efficiency: 85 }, { name: 'Agent 4', current: 55, optimal: 65, efficiency: 95 }, { name: 'Agent 5', current: 75, optimal: 75, efficiency: 90 } ].map((agent, index) => ( <ListItem key={index} divider> <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}> <AgentIcon /> </Avatar> <ListItemText primary={agent.name} secondary={ <Box sx={{ mt: 1 }}> <Box sx={{ display: 'flex', gap: 1, mb: 1 }}> <Chip label={`Charge: ${agent.current}%`} color={getWorkloadColor(agent.current)} size="small" /> <Chip label={`Optimal: ${agent.optimal}%`} variant="outlined" size="small" /> <Chip label={`Efficacité: ${agent.efficiency}%`} color={getEfficiencyColor(agent.efficiency)} size="small" /> </Box> <LinearProgress variant="determinate" value={agent.current} color={getWorkloadColor(agent.current)} sx={{ height: 6, borderRadius: 3 }} /> </Box> } /> <Button variant="outlined" size="small" disabled={Math.abs(agent.current - agent.optimal) < 5} > Optimiser </Button> </ListItem> ))} </List> </CardContent> </Card> </Grid> {/* Optimal Assignments */} <Grid item xs={12}> <Card> <CardContent> <Typography variant="h6" sx={{ mb: 2 }}> <RecommendIcon sx={{ mr: 1, verticalAlign: 'middle' }} /> Recommandations d'Attribution </Typography> <List> {[ { ticket: 'Ticket #12345', agent: 'Agent 2', reason: 'Spécialité facturation', priority: 'Haute' }, { ticket: 'Ticket #12346', agent: 'Agent 4', reason: 'Charge faible', priority: 'Moyenne' }, { ticket: 'Ticket #12347', agent: 'Agent 1', reason: 'Expertise technique', priority: 'Haute' }, { ticket: 'Ticket #12348', agent: 'Agent 5', reason: 'Disponibilité immédiate', priority: 'Urgente' } ].map((assignment, index) => ( <ListItem key={index} divider> <ListItemText primary={`${assignment.ticket} → ${assignment.agent}`} secondary={ <Box sx={{ display: 'flex', gap: 1, mt: 1 }}> <Chip label={assignment.priority} color={assignment.priority === 'Urgente' ? 'error' : assignment.priority === 'Haute' ? 'warning' : 'default'} size="small" /> <Typography variant="caption" color="text.secondary"> {assignment.reason} </Typography> </Box> } /> <Button variant="contained" size="small" color="primary" > Attribuer </Button> </ListItem> ))} </List> </CardContent> </Card> </Grid> </Grid> </Box> ); }; export default WorkloadOptimizationPanel;