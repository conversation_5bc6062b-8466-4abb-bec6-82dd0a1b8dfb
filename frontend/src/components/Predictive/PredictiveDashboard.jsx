/** * ============================================= * PREDICTIVE ANALYTICS DASHBOARD * ML-powered insights and predictions display * Real-time monitoring and alerts * ============================================= */ import React, { useState, useEffect, useCallback } from 'react'; import { useDispatch, useSelector } from 'react-redux'; import { Box, Grid, Card, CardContent, Typography, Button, Chip, Alert, Tab, Tabs, IconButton, Tooltip, CircularProgress, LinearProgress, Badge, Switch, FormControlLabel } from '@mui/material'; import { TrendingUp as TrendingUpIcon, Warning as WarningIcon, PeopleAlt as PeopleIcon, Assessment as AssessmentIcon, Refresh as RefreshIcon, Notifications as NotificationsIcon, FilterList as FilterIcon, Download as DownloadIcon } from '@mui/icons-material'; import { motion, AnimatePresence } from 'framer-motion'; import { toast } from 'react-toastify'; import { fetchChurnPredictions, fetchDemandForecast, fetchEscalationPredictions, fetchAnomalies, fetchPredictiveInsights } from '../../store/slices/predictiveSlice'; import { useWebSocket } from '../../hooks/useWebSocket'; import ChurnPredictionCard from './ChurnPredictionCard'; import DemandForecastChart from './DemandForecastChart'; import EscalationRiskCard from './EscalationRiskCard'; import AnomalyAlert from './AnomalyAlert'; import PredictionMetrics from './PredictionMetrics'; const PredictiveDashboard = () => { const dispatch = useDispatch(); const { churnPredictions, demandForecast, escalationPredictions, anomalies, insights, loading, error } = useSelector(state => state.predictive); const { user } = useSelector(state => state.auth); const [activeTab, setActiveTab] = useState(0); const [realTimeEnabled, setRealTimeEnabled] = useState(true); const [filters, setFilters] = useState({ timeRange: '24h', riskLevel: 'all', showResolved: false }); // WebSocket connection for real-time predictions const { socket, isConnected } = useWebSocket('/predictive'); useEffect(() => { // Fetch initial data dispatch(fetchPredictiveInsights()); loadTabData(activeTab); }, [dispatch, activeTab]); useEffect(() => { // Set up WebSocket event listeners if (socket && isConnected && realTimeEnabled) { socket.emit('authenticate', { token: localStorage.getItem('token'), userId: user.id, role: user.role }); socket.on('authenticated', (data) => { console.log('Predictive WebSocket authenticated:', data); }); socket.on('churn_prediction_update', (prediction) => { toast.warning(`High churn risk detected for customer ${prediction.customer_id}`, { position: 'top-right', autoClose: 8000 }); // Refresh churn predictions if on that tab if (activeTab === 0) { dispatch(fetchChurnPredictions(filters)); } }); socket.on('escalation_risk_alert', (alert) => { toast.error(`Escalation risk: ${alert.message}`, { position: 'top-right', autoClose: 10000 }); // Refresh escalation predictions if on that tab if (activeTab === 2) { dispatch(fetchEscalationPredictions(filters)); } }); socket.on('anomaly_detected', (anomaly) => { toast.error(`Anomaly detected: ${anomaly.description}`, { position: 'top-right', autoClose: 12000 }); // Refresh anomalies if on that tab if (activeTab === 3) { dispatch(fetchAnomalies(filters)); } }); socket.on('demand_forecast_update', (forecast) => { console.log('Demand forecast updated:', forecast); // Refresh demand forecast if on that tab if (activeTab === 1) { dispatch(fetchDemandForecast(filters)); } }); return () => { socket.off('authenticated'); socket.off('churn_prediction_update'); socket.off('escalation_risk_alert'); socket.off('anomaly_detected'); socket.off('demand_forecast_update'); }; } }, [socket, isConnected, realTimeEnabled, user, activeTab, filters, dispatch]); const loadTabData = useCallback((tabIndex) => { switch (tabIndex) { case 0: dispatch(fetchChurnPredictions(filters)); break; case 1: dispatch(fetchDemandForecast(filters)); break; case 2: dispatch(fetchEscalationPredictions(filters)); break; case 3: dispatch(fetchAnomalies(filters)); break; default: break; } }, [dispatch, filters]); const handleTabChange = (event, newValue) => { setActiveTab(newValue); }; const handleRefresh = () => { dispatch(fetchPredictiveInsights()); loadTabData(activeTab); }; const getRiskLevelColor = (level) => { switch (level) { case 'low': return 'success'; case 'medium': return 'warning'; case 'high': return 'error'; case 'critical': return 'error'; default: return 'default'; } }; const getActiveAnomaliesCount = () => { return anomalies?.filter(a => a.status === 'active').length || 0; }; const getHighRiskCount = (predictions) => { return predictions?.filter(p => p.risk_level === 'high' || p.risk_level === 'critical').length || 0; }; // Check user permissions const canViewPredictions = ['admin', 'supervisor', 'analyst'].includes(user.role); if (!canViewPredictions) { return ( <Box sx={{ p: 3 }}> <Alert severity="warning"> You don't have permission to view predictive analytics. </Alert> </Box> ); } return ( <Box sx={{ p: 3 }}> {/* Header */} <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}> <Box> <Typography variant="h4" component="h1" gutterBottom> Predictive Analytics </Typography> <Typography variant="body1" color="text.secondary"> ML-powered insights and predictions for proactive decision making </Typography> </Box> <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}> <FormControlLabel control={ <Switch checked={realTimeEnabled} onChange={(e) => setRealTimeEnabled(e.target.checked)} color="primary" /> } label="Real-time updates" /> <Tooltip title="Refresh data"> <IconButton onClick={handleRefresh} disabled={loading}> <RefreshIcon /> </IconButton> </Tooltip> <Tooltip title="Export data"> <IconButton> <DownloadIcon /> </IconButton> </Tooltip> </Box> </Box> {/* Connection Status */} {realTimeEnabled && !isConnected && ( <Alert severity="warning" sx={{ mb: 3 }}> Real-time predictions are currently unavailable. Attempting to reconnect... </Alert> )} {/* Key Metrics Overview */} <Grid container spacing={3} sx={{ mb: 4 }}> <Grid item xs={12} md={3}> <Card> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}> <Box> <Typography variant="h6" color="error"> High Risk Churn </Typography> <Typography variant="h4"> {getHighRiskCount(churnPredictions)} </Typography> </Box> <PeopleIcon color="error" sx={{ fontSize: 40 }} /> </Box> {loading && <LinearProgress sx={{ mt: 1 }} />} </CardContent> </Card> </Grid> <Grid item xs={12} md={3}> <Card> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}> <Box> <Typography variant="h6" color="warning"> Escalation Risk </Typography> <Typography variant="h4"> {getHighRiskCount(escalationPredictions)} </Typography> </Box> <TrendingUpIcon color="warning" sx={{ fontSize: 40 }} /> </Box> {loading && <LinearProgress sx={{ mt: 1 }} />} </CardContent> </Card> </Grid> <Grid item xs={12} md={3}> <Card> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}> <Box> <Typography variant="h6" color="error"> Active Anomalies </Typography> <Typography variant="h4"> {getActiveAnomaliesCount()} </Typography> </Box> <Badge badgeContent={getActiveAnomaliesCount()} color="error"> <WarningIcon color="error" sx={{ fontSize: 40 }} /> </Badge> </Box> {loading && <LinearProgress sx={{ mt: 1 }} />} </CardContent> </Card> </Grid> <Grid item xs={12} md={3}> <Card> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}> <Box> <Typography variant="h6" color="info"> System Health </Typography> <Typography variant="h4"> {insights?.system_health_score || 0}% </Typography> </Box> <AssessmentIcon color="info" sx={{ fontSize: 40 }} /> </Box> {loading && <LinearProgress sx={{ mt: 1 }} />} </CardContent> </Card> </Grid> </Grid> {/* Prediction Tabs */} <Card> <Box sx={{ borderBottom: 1, borderColor: 'divider' }}> <Tabs value={activeTab} onChange={handleTabChange} aria-label="prediction tabs"> <Tab label={ <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}> Churn Predictions {getHighRiskCount(churnPredictions) > 0 && ( <Chip label={getHighRiskCount(churnPredictions)} color="error" size="small" /> )} </Box> } /> <Tab label="Demand Forecast" /> <Tab label={ <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}> Escalation Risk {getHighRiskCount(escalationPredictions) > 0 && ( <Chip label={getHighRiskCount(escalationPredictions)} color="warning" size="small" /> )} </Box> } /> <Tab label={ <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}> Anomaly Detection {getActiveAnomaliesCount() > 0 && ( <Badge badgeContent={getActiveAnomaliesCount()} color="error"> <NotificationsIcon /> </Badge> )} </Box> } /> </Tabs> </Box> <CardContent> <AnimatePresence mode="wait"> {/* Churn Predictions Tab */} {activeTab === 0 && ( <motion.div key="churn" initial={{ opacity: 0, x: 20 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -20 }} transition={{ duration: 0.3 }} > <Box sx={{ mb: 3, display: 'flex', gap: 2, alignItems: 'center' }}> <Typography variant="h6">Customer Churn Predictions</Typography> <Chip label={`${churnPredictions?.length || 0} predictions`} variant="outlined" /> </Box> {loading ? ( <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}> <CircularProgress /> </Box> ) : ( <Grid container spacing={2}> {churnPredictions?.slice(0, 6).map((prediction, index) => ( <Grid item xs={12} md={6} lg={4} key={prediction.customer_id}> <ChurnPredictionCard prediction={prediction} /> </Grid> ))} </Grid> )} </motion.div> )} {/* Demand Forecast Tab */} {activeTab === 1 && ( <motion.div key="demand" initial={{ opacity: 0, x: 20 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -20 }} transition={{ duration: 0.3 }} > <Box sx={{ mb: 3 }}> <Typography variant="h6">Demand Forecasting</Typography> <Typography variant="body2" color="text.secondary"> Predicted customer service volume for the next 24 hours </Typography> </Box> {loading ? ( <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}> <CircularProgress /> </Box> ) : ( <DemandForecastChart data={demandForecast} /> )} </motion.div> )} {/* Escalation Risk Tab */} {activeTab === 2 && ( <motion.div key="escalation" initial={{ opacity: 0, x: 20 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -20 }} transition={{ duration: 0.3 }} > <Box sx={{ mb: 3, display: 'flex', gap: 2, alignItems: 'center' }}> <Typography variant="h6">Escalation Risk Predictions</Typography> <Chip label={`${escalationPredictions?.length || 0} tickets`} variant="outlined" /> </Box> {loading ? ( <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}> <CircularProgress /> </Box> ) : ( <Grid container spacing={2}> {escalationPredictions?.slice(0, 6).map((prediction, index) => ( <Grid item xs={12} md={6} lg={4} key={prediction.ticket_id}> <EscalationRiskCard prediction={prediction} /> </Grid> ))} </Grid> )} </motion.div> )} {/* Anomaly Detection Tab */} {activeTab === 3 && ( <motion.div key="anomalies" initial={{ opacity: 0, x: 20 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -20 }} transition={{ duration: 0.3 }} > <Box sx={{ mb: 3, display: 'flex', gap: 2, alignItems: 'center' }}> <Typography variant="h6">System Anomalies</Typography> <Chip label={`${getActiveAnomaliesCount()} active`} color="error" variant="outlined" /> </Box> {loading ? ( <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}> <CircularProgress /> </Box> ) : ( <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}> {anomalies?.filter(a => a.status === 'active').map((anomaly, index) => ( <AnomalyAlert key={anomaly.id} anomaly={anomaly} /> ))} {getActiveAnomaliesCount() === 0 && ( <Alert severity="success"> No active anomalies detected. System is operating normally. </Alert> )} </Box> )} </motion.div> )} </AnimatePresence> </CardContent> </Card> {/* Prediction Metrics */} {insights && ( <Card sx={{ mt: 4 }}> <CardContent> <Typography variant="h6" gutterBottom> Model Performance Metrics </Typography> <PredictionMetrics metrics={insights.confidence_metrics} /> </CardContent> </Card> )} {/* Error Display */} {error && ( <Alert severity="error" sx={{ mt: 2 }}> {error} </Alert> )} </Box> ); }; export default PredictiveDashboard;