/** * ============================================= * [SEARCH] ANOMALY DETECTION PANEL COMPONENT * System anomaly detection and health monitoring * ============================================= */ import React from 'react'; import { Box, Typography, Card, CardContent, Grid, List, ListItem, ListItemText, Chip, Button, Alert, LinearProgress, IconButton } from '@mui/material'; import { BugReport as AnomalyIcon, HealthAndSafety as HealthIcon, Timeline as TrendIcon, CheckCircle as ResolveIcon, Visibility as ViewIcon } from '@mui/icons-material'; interface AnomalyDetectionPanelProps { activeAnomalies: any[]; anomalyHistory: any[]; systemHealthScore: number; loading: boolean; } const AnomalyDetectionPanel: React.FC<AnomalyDetectionPanelProps> = ({ activeAnomalies, anomalyHistory, systemHealthScore, loading }) => { const getSeverityColor = (severity: string): 'success' | 'warning' | 'error' | 'info' => { switch (severity) { case 'low': return 'info'; case 'medium': return 'warning'; case 'high': return 'error'; case 'critical': return 'error'; default: return 'info'; } }; const getHealthColor = (score: number) => { if (score >= 90) return 'success'; if (score >= 70) return 'warning'; return 'error'; }; const getTypeIcon = (type: string) => { switch (type) { case 'performance': return '[PERFORMANCE]'; case 'volume': return '[ANALYTICS]'; case 'satisfaction': return ''; case 'system': return ''; default: return '[SEARCH]'; } }; return ( <Box> <Typography variant="h6" sx={{ mb: 3 }}> Détection d'Anomalies </Typography> {activeAnomalies.filter(a => a.severity === 'critical').length > 0 && ( <Alert severity="error" sx={{ mb: 3 }}> <Typography variant="body1"> <strong>Anomalies Critiques:</strong> {activeAnomalies.filter(a => a.severity === 'critical').length} anomalies nécessitent une attention immédiate </Typography> </Alert> )} <Grid container spacing={3}> {/* System Health */} <Grid item xs={12} md={4}> <Card> <CardContent> <Typography variant="h6" sx={{ mb: 2 }}> <HealthIcon sx={{ mr: 1, verticalAlign: 'middle' }} /> Santé du Système </Typography> <Box sx={{ textAlign: 'center', mb: 2 }}> <Typography variant="h3" color={getHealthColor(systemHealthScore) + '.main'}> {systemHealthScore}% </Typography> <LinearProgress variant="determinate" value={systemHealthScore} color={getHealthColor(systemHealthScore)} sx={{ mt: 1, height: 8, borderRadius: 4 }} /> </Box> <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}> <Typography variant="body2" color="text.secondary"> Anomalies Actives </Typography> <Typography variant="body2"> {activeAnomalies.length} </Typography> </Box> <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}> <Typography variant="body2" color="text.secondary"> Critiques </Typography> <Typography variant="body2" color="error.main"> {activeAnomalies.filter(a => a.severity === 'critical').length} </Typography> </Box> <Box sx={{ display: 'flex', justifyContent: 'space-between' }}> <Typography variant="body2" color="text.secondary"> Résolues Aujourd'hui </Typography> <Typography variant="body2" color="success.main"> 12 </Typography> </Box> </CardContent> </Card> </Grid> {/* Active Anomalies */} <Grid item xs={12} md={8}> <Card> <CardContent> <Typography variant="h6" sx={{ mb: 2 }}> <AnomalyIcon sx={{ mr: 1, verticalAlign: 'middle' }} /> Anomalies Actives ({activeAnomalies.length}) </Typography> <List> {[ { id: '1', type: 'performance', severity: 'critical', description: 'Temps de réponse API élevé', score: 0.95, detected: '2 min ago' }, { id: '2', type: 'volume', severity: 'high', description: 'Pic de trafic inhabituel', score: 0.87, detected: '15 min ago' }, { id: '3', type: 'satisfaction', severity: 'medium', description: 'Baisse satisfaction client', score: 0.72, detected: '1h ago' }, { id: '4', type: 'system', severity: 'low', description: 'Utilisation mémoire élevée', score: 0.65, detected: '2h ago' } ].map((anomaly, index) => ( <ListItem key={index} divider> <ListItemText primary={ <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}> <Typography variant="h6" component="span"> {getTypeIcon(anomaly.type)} </Typography> <Typography variant="body1"> {anomaly.description} </Typography> </Box> } secondary={ <Box sx={{ mt: 1 }}> <Box sx={{ display: 'flex', gap: 1, mb: 1 }}> <Chip label={anomaly.severity} color={getSeverityColor(anomaly.severity)} size="small" /> <Chip label={`Score: ${Math.round(anomaly.score * 100)}%`} variant="outlined" size="small" /> <Chip label={anomaly.detected} variant="outlined" size="small" /> </Box> <Typography variant="caption" color="text.secondary"> Type: {anomaly.type} • ID: {anomaly.id} </Typography> </Box> } /> <Box sx={{ display: 'flex', gap: 1 }}> <IconButton size="small" color="primary"> <ViewIcon /> </IconButton> <IconButton size="small" color="success"> <ResolveIcon /> </IconButton> </Box> </ListItem> ))} </List> {activeAnomalies.length === 0 && ( <Box sx={{ textAlign: 'center', py: 4 }}> <Typography variant="h6" color="success.main"> [COMPLETE] Aucune anomalie détectée </Typography> <Typography variant="body2" color="text.secondary"> Tous les systèmes fonctionnent normalement </Typography> </Box> )} </CardContent> </Card> </Grid> {/* Anomaly Trends */} <Grid item xs={12}> <Card> <CardContent> <Typography variant="h6" sx={{ mb: 2 }}> <TrendIcon sx={{ mr: 1, verticalAlign: 'middle' }} /> Tendances des Anomalies </Typography> <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}> <Typography variant="body2" color="text.secondary"> Graphique des tendances d'anomalies (à implémenter avec Chart.js) </Typography> </Box> </CardContent> </Card> </Grid> </Grid> </Box> ); }; export default AnomalyDetectionPanel;