/** * ============================================= * [METRICS] DEMAND FORECAST PANEL COMPONENT * Demand forecasting and staffing recommendations * ============================================= */ import React from 'react'; import { Box, Typography, Card, CardContent, Grid, Chip, Alert, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper } from '@mui/material'; import { Schedule as ForecastIcon, People as StaffingIcon, TrendingUp as TrendIcon, AccessTime as TimeIcon } from '@mui/icons-material'; interface DemandForecastPanelProps { forecast: any[]; peakHours: number[]; staffingRecommendations: any; loading: boolean; } const DemandForecastPanel: React.FC<DemandForecastPanelProps> = ({ forecast, peakHours, staffingRecommendations, loading }) => { const getVolumeColor = (volume: number) => { if (volume > 80) return 'error'; if (volume > 60) return 'warning'; return 'success'; }; const formatHour = (hour: number) => { return `${hour.toString().padStart(2, '0')}:00`; }; return ( <Box> <Typography variant="h6" sx={{ mb: 3 }}> Prévision de la Demande </Typography> {peakHours.length > 0 && ( <Alert severity="info" sx={{ mb: 3 }}> <Typography variant="body1"> <strong>Heures de pointe prévues:</strong> {peakHours.map(formatHour).join(', ')} </Typography> </Alert> )} <Grid container spacing={3}> {/* Current Staffing */} <Grid item xs={12} md={4}> <Card> <CardContent> <Typography variant="h6" sx={{ mb: 2 }}> <StaffingIcon sx={{ mr: 1, verticalAlign: 'middle' }} /> Recommandations Personnel </Typography> <Box sx={{ mb: 2 }}> <Typography variant="body2" color="text.secondary"> Heure Actuelle </Typography> <Typography variant="h4" color="primary.main"> {staffingRecommendations?.current_hour || 8} agents </Typography> </Box> <Box sx={{ mb: 2 }}> <Typography variant="body2" color="text.secondary"> Prochaines 4h </Typography> <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}> {(staffingRecommendations?.next_4_hours || [10, 12, 15, 13]).map((staff: number, index: number) => ( <Chip key={index} label={`${staff}`} color={getVolumeColor(staff * 5)} size="small" /> ))} </Box> </Box> <Box> <Typography variant="body2" color="text.secondary"> Pic Journalier </Typography> <Typography variant="h6" color="warning.main"> 18 agents à 14h </Typography> </Box> </CardContent> </Card> </Grid> {/* Forecast Chart */} <Grid item xs={12} md={8}> <Card> <CardContent> <Typography variant="h6" sx={{ mb: 2 }}> <TrendIcon sx={{ mr: 1, verticalAlign: 'middle' }} /> Prévision 24h </Typography> <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}> <Typography variant="body2" color="text.secondary"> Graphique de prévision de demande (à implémenter avec Chart.js) </Typography> </Box> </CardContent> </Card> </Grid> {/* Hourly Forecast Table */} <Grid item xs={12}> <Card> <CardContent> <Typography variant="h6" sx={{ mb: 2 }}> <TimeIcon sx={{ mr: 1, verticalAlign: 'middle' }} /> Prévisions Horaires </Typography> <TableContainer component={Paper} variant="outlined"> <Table size="small"> <TableHead> <TableRow> <TableCell>Heure</TableCell> <TableCell align="right">Volume Prévu</TableCell> <TableCell align="right">Confiance</TableCell> <TableCell align="right">Personnel Recommandé</TableCell> <TableCell>Statut</TableCell> </TableRow> </TableHead> <TableBody> {Array.from({ length: 12 }, (_, index) => { const hour = new Date().getHours() + index; const volume = Math.floor(Math.random() * 100) + 20; const confidence = Math.floor(Math.random() * 20) + 80; const staff = Math.ceil(volume / 8); return ( <TableRow key={index}> <TableCell>{formatHour(hour % 24)}</TableCell> <TableCell align="right">{volume}</TableCell> <TableCell align="right">{confidence}%</TableCell> <TableCell align="right">{staff}</TableCell> <TableCell> <Chip label={volume > 80 ? 'Pic' : volume > 60 ? 'Élevé' : 'Normal'} color={getVolumeColor(volume)} size="small" /> </TableCell> </TableRow> ); })} </TableBody> </Table> </TableContainer> </CardContent> </Card> </Grid> </Grid> </Box> ); }; export default DemandForecastPanel;