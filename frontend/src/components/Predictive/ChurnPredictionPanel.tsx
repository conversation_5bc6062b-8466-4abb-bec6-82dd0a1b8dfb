/** * ============================================= * CHURN PREDICTION PANEL COMPONENT * Customer churn prediction and risk analysis * ============================================= */ import React from 'react'; import { Box, Typography, Card, CardContent, Grid, List, ListItem, ListItemText, Chip, LinearProgress, Alert, Button } from '@mui/material'; import { TrendingDown as ChurnIcon, Warning as RiskIcon, Person as CustomerIcon, Assessment as AnalysisIcon } from '@mui/icons-material'; interface ChurnPredictionPanelProps { predictions: any[]; highRiskCustomers: any[]; trends: any; loading: boolean; } const ChurnPredictionPanel: React.FC<ChurnPredictionPanelProps> = ({ predictions, highRiskCustomers, trends, loading }) => { const getRiskColor = (level: string): 'success' | 'warning' | 'error' | 'info' => { switch (level) { case 'low': return 'success'; case 'medium': return 'warning'; case 'high': return 'error'; case 'critical': return 'error'; default: return 'info'; } }; const getRiskLabel = (probability: number) => { if (probability >= 0.8) return 'critical'; if (probability >= 0.6) return 'high'; if (probability >= 0.4) return 'medium'; return 'low'; }; return ( <Box> <Typography variant="h6" sx={{ mb: 3 }}> Prédiction de Churn Client </Typography> {highRiskCustomers.length > 10 && ( <Alert severity="warning" sx={{ mb: 3 }}> <Typography variant="body1"> <strong>Alerte:</strong> {highRiskCustomers.length} clients présentent un risque élevé de désabonnement </Typography> </Alert> )} <Grid container spacing={3}> {/* High Risk Customers */} <Grid item xs={12} md={8}> <Card> <CardContent> <Typography variant="h6" sx={{ mb: 2 }}> <RiskIcon sx={{ mr: 1, verticalAlign: 'middle' }} /> Clients à Risque Élevé ({highRiskCustomers.length}) </Typography> <List> {[1, 2, 3, 4, 5].map((index) => ( <ListItem key={index} divider> <ListItemText primary={`Client ${index} - ID: 12345${index}`} secondary={ <Box sx={{ mt: 1 }}> <Box sx={{ display: 'flex', gap: 1, mb: 1 }}> <Chip label={`Risque: ${85 + index}%`} color={getRiskColor('critical')} size="small" /> <Chip label="Facturation" variant="outlined" size="small" /> <Chip label="Support récent" variant="outlined" size="small" /> </Box> <Typography variant="caption" color="text.secondary"> Facteurs: Factures impayées, plaintes récentes, usage en baisse </Typography> </Box> } /> <Button variant="outlined" size="small" color="primary" > Actions </Button> </ListItem> ))} </List> </CardContent> </Card> </Grid> {/* Churn Statistics */} <Grid item xs={12} md={4}> <Card sx={{ mb: 2 }}> <CardContent> <Typography variant="h6" sx={{ mb: 2 }}> <AnalysisIcon sx={{ mr: 1, verticalAlign: 'middle' }} /> Statistiques Churn </Typography> <Box sx={{ mb: 2 }}> <Typography variant="body2" color="text.secondary"> Taux de Churn Prévu </Typography> <Typography variant="h4" color="error.main"> 3.2% </Typography> </Box> <Box sx={{ mb: 2 }}> <Typography variant="body2" color="text.secondary"> Clients à Risque </Typography> <Typography variant="h4" color="warning.main"> {highRiskCustomers.length} </Typography> </Box> <Box> <Typography variant="body2" color="text.secondary"> Précision du Modèle </Typography> <Typography variant="h4" color="success.main"> 94.5% </Typography> </Box> </CardContent> </Card> <Card> <CardContent> <Typography variant="h6" sx={{ mb: 2 }}> Facteurs de Risque </Typography> {[ { factor: 'Factures impayées', impact: 85 }, { factor: 'Plaintes récentes', impact: 72 }, { factor: 'Usage en baisse', impact: 68 }, { factor: 'Support fréquent', impact: 45 } ].map((item, index) => ( <Box key={index} sx={{ mb: 2 }}> <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}> <Typography variant="body2"> {item.factor} </Typography> <Typography variant="body2"> {item.impact}% </Typography> </Box> <LinearProgress variant="determinate" value={item.impact} color={item.impact > 70 ? 'error' : item.impact > 50 ? 'warning' : 'success'} sx={{ height: 6, borderRadius: 3 }} /> </Box> ))} </CardContent> </Card> </Grid> {/* Churn Trends */} <Grid item xs={12}> <Card> <CardContent> <Typography variant="h6" sx={{ mb: 2 }}> <ChurnIcon sx={{ mr: 1, verticalAlign: 'middle' }} /> Tendances de Churn </Typography> <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}> <Typography variant="body2" color="text.secondary"> Graphique des tendances de churn (à implémenter avec Chart.js) </Typography> </Box> </CardContent> </Card> </Grid> </Grid> </Box> ); }; export default ChurnPredictionPanel;