/** * ============================================= * ESCALATION PREDICTION PANEL COMPONENT * Ticket escalation prediction and prevention * ============================================= */ import React from 'react'; import { Box, Typography, Card, CardContent, Grid, List, ListItem, ListItemText, Chip, Button, Alert, LinearProgress } from '@mui/material'; import { Warning as EscalationIcon, Schedule as TimeIcon, TrendingUp as TrendIcon, Block as PreventIcon } from '@mui/icons-material'; interface EscalationPredictionPanelProps { predictions: any[]; highRiskTickets: any[]; trends: any; loading: boolean; } const EscalationPredictionPanel: React.FC<EscalationPredictionPanelProps> = ({ predictions, highRiskTickets, trends, loading }) => { const getRiskColor = (level: string): 'success' | 'warning' | 'error' | 'info' => { switch (level) { case 'low': return 'success'; case 'medium': return 'warning'; case 'high': return 'error'; case 'critical': return 'error'; default: return 'info'; } }; const getUrgencyColor = (time: number) => { if (time < 30) return 'error'; if (time < 60) return 'warning'; return 'info'; }; return ( <Box> <Typography variant="h6" sx={{ mb: 3 }}> Prédiction d'Escalade </Typography> {highRiskTickets.length > 5 && ( <Alert severity="warning" sx={{ mb: 3 }}> <Typography variant="body1"> <strong>Attention:</strong> {highRiskTickets.length} tickets présentent un risque élevé d'escalade </Typography> </Alert> )} <Grid container spacing={3}> {/* High Risk Tickets */} <Grid item xs={12} md={8}> <Card> <CardContent> <Typography variant="h6" sx={{ mb: 2 }}> <EscalationIcon sx={{ mr: 1, verticalAlign: 'middle' }} /> Tickets à Risque Élevé ({highRiskTickets.length}) </Typography> <List> {[ { id: '12345', probability: 0.85, time: 25, reason: 'Client frustré', category: 'facturation' }, { id: '12346', probability: 0.78, time: 45, reason: 'Problème complexe', category: 'technique' }, { id: '12347', probability: 0.72, time: 35, reason: 'Délai dépassé', category: 'service' }, { id: '12348', probability: 0.68, time: 55, reason: 'Réclamation répétée', category: 'réclamation' }, { id: '12349', probability: 0.65, time: 40, reason: 'Agent inexpérimenté', category: 'support' } ].map((ticket, index) => ( <ListItem key={index} divider> <ListItemText primary={`Ticket #${ticket.id}`} secondary={ <Box sx={{ mt: 1 }}> <Box sx={{ display: 'flex', gap: 1, mb: 1 }}> <Chip label={`Risque: ${Math.round(ticket.probability * 100)}%`} color={getRiskColor(ticket.probability > 0.7 ? 'high' : 'medium')} size="small" /> <Chip label={`${ticket.time}min`} color={getUrgencyColor(ticket.time)} size="small" icon={<TimeIcon />} /> <Chip label={ticket.category} variant="outlined" size="small" /> </Box> <Typography variant="caption" color="text.secondary"> Raison principale: {ticket.reason} </Typography> <LinearProgress variant="determinate" value={ticket.probability * 100} color={getRiskColor(ticket.probability > 0.7 ? 'high' : 'medium')} sx={{ mt: 1, height: 4, borderRadius: 2 }} /> </Box> } /> <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}> <Button variant="contained" size="small" color="primary" > Intervenir </Button> <Button variant="outlined" size="small" color="warning" > Escalader </Button> </Box> </ListItem> ))} </List> </CardContent> </Card> </Grid> {/* Escalation Statistics */} <Grid item xs={12} md={4}> <Card sx={{ mb: 2 }}> <CardContent> <Typography variant="h6" sx={{ mb: 2 }}> Statistiques Escalade </Typography> <Box sx={{ mb: 2 }}> <Typography variant="body2" color="text.secondary"> Taux d'Escalade Prévu </Typography> <Typography variant="h4" color="warning.main"> 12.5% </Typography> </Box> <Box sx={{ mb: 2 }}> <Typography variant="body2" color="text.secondary"> Tickets à Risque </Typography> <Typography variant="h4" color="error.main"> {highRiskTickets.length} </Typography> </Box> <Box> <Typography variant="body2" color="text.secondary"> Précision du Modèle </Typography> <Typography variant="h4" color="success.main"> 91.2% </Typography> </Box> </CardContent> </Card> <Card> <CardContent> <Typography variant="h6" sx={{ mb: 2 }}> <PreventIcon sx={{ mr: 1, verticalAlign: 'middle' }} /> Actions Préventives </Typography> {[ { action: 'Réassignation agent', effectiveness: 85 }, { action: 'Intervention superviseur', effectiveness: 92 }, { action: 'Escalade proactive', effectiveness: 78 }, { action: 'Formation express', effectiveness: 65 } ].map((item, index) => ( <Box key={index} sx={{ mb: 2 }}> <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}> <Typography variant="body2"> {item.action} </Typography> <Typography variant="body2"> {item.effectiveness}% </Typography> </Box> <LinearProgress variant="determinate" value={item.effectiveness} color={item.effectiveness > 80 ? 'success' : item.effectiveness > 70 ? 'warning' : 'error'} sx={{ height: 6, borderRadius: 3 }} /> </Box> ))} </CardContent> </Card> </Grid> {/* Escalation Trends */} <Grid item xs={12}> <Card> <CardContent> <Typography variant="h6" sx={{ mb: 2 }}> <TrendIcon sx={{ mr: 1, verticalAlign: 'middle' }} /> Tendances d'Escalade </Typography> <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}> <Typography variant="body2" color="text.secondary"> Graphique des tendances d'escalade (à implémenter avec Chart.js) </Typography> </Box> </CardContent> </Card> </Grid> </Grid> </Box> ); }; export default EscalationPredictionPanel;