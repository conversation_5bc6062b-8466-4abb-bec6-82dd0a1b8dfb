/**
 * =============================================
 * 🧪 SIMULATION DASHBOARD TESTS
 * React component tests for simulation features
 * Testing user interactions and state management
 * =============================================
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { configureStore } from '@reduxjs/toolkit';
import { toast } from 'react-toastify';

import SimulationDashboard from '../SimulationDashboard';
import simulationReducer from '../../../store/slices/simulationSlice';
import authReducer from '../../../store/slices/authSlice';

// Mock WebSocket hook
jest.mock('../../../hooks/useWebSocket', () => ({
  useWebSocket: () => ({
    socket: {
      emit: jest.fn(),
      on: jest.fn(),
      off: jest.fn()
    },
    isConnected: true,
    sendMessage: jest.fn()
  })
}));

// Mock toast notifications
jest.mock('react-toastify', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn()
  }
}));

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }) => <div {...props}>{children}</div>
  },
  AnimatePresence: ({ children }) => <>{children}</>
}));

const theme = createTheme();

const mockScenarios = [
  {
    _id: 'scenario1',
    title: 'Billing Issue Resolution',
    description: 'Handle customer billing disputes',
    difficulty: 'intermediate',
    category: 'billing',
    tags: ['billing', 'dispute'],
    estimated_duration: 15,
    learning_objectives: ['empathy', 'accuracy']
  },
  {
    _id: 'scenario2',
    title: 'Technical Support',
    description: 'Resolve technical connectivity issues',
    difficulty: 'expert',
    category: 'technical',
    tags: ['technical', 'connectivity'],
    estimated_duration: 20,
    learning_objectives: ['problem_solving', 'efficiency']
  }
];

const mockAgentProgress = {
  total_sessions: 10,
  completed_sessions: 8,
  average_score: 78.5,
  skill_levels: {
    empathy: 75,
    efficiency: 82,
    accuracy: 79,
    communication: 80,
    problem_solving: 73
  },
  badges_earned: [
    {
      id: 'first_session',
      name: 'First Steps',
      description: 'Completed first simulation',
      rarity: 'common'
    }
  ],
  current_streak: 3,
  best_streak: 5
};

const mockUser = {
  id: 'user123',
  email: '<EMAIL>',
  role: 'agent',
  firstName: 'Test',
  lastName: 'User'
};

const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      simulation: simulationReducer,
      auth: authReducer
    },
    preloadedState: {
      simulation: {
        scenarios: mockScenarios,
        agentProgress: mockAgentProgress,
        activeSession: null,
        loading: { scenarios: false, session: false, progress: false },
        error: null,
        socketConnected: true,
        filters: { difficulty: 'all', category: 'all' },
        ...initialState.simulation
      },
      auth: {
        user: mockUser,
        isAuthenticated: true,
        token: 'mock-token',
        ...initialState.auth
      }
    }
  });
};

const renderWithProviders = (component, initialState = {}) => {
  const store = createMockStore(initialState);
  
  return render(
    <Provider store={store}>
      <BrowserRouter>
        <ThemeProvider theme={theme}>
          {component}
        </ThemeProvider>
      </BrowserRouter>
    </Provider>
  );
};

describe('SimulationDashboard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders dashboard with scenarios and progress', () => {
      renderWithProviders(<SimulationDashboard />);

      expect(screen.getByText('🎮 Training Simulations')).toBeInTheDocument();
      expect(screen.getByText('Practice your customer service skills with AI-powered scenarios')).toBeInTheDocument();
      
      // Check progress cards
      expect(screen.getByText('Overall Progress')).toBeInTheDocument();
      expect(screen.getByText('78.5%')).toBeInTheDocument();
      expect(screen.getByText('Sessions Completed')).toBeInTheDocument();
      expect(screen.getByText('8')).toBeInTheDocument();
      
      // Check scenarios
      expect(screen.getByText('Billing Issue Resolution')).toBeInTheDocument();
      expect(screen.getByText('Technical Support')).toBeInTheDocument();
    });

    it('shows loading state when scenarios are loading', () => {
      renderWithProviders(<SimulationDashboard />, {
        simulation: {
          scenarios: [],
          loading: { scenarios: true }
        }
      });

      expect(screen.getAllByTestId('skeleton')).toHaveLength(6);
    });

    it('displays error message when there is an error', () => {
      const errorMessage = 'Failed to load scenarios';
      renderWithProviders(<SimulationDashboard />, {
        simulation: {
          error: errorMessage
        }
      });

      expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });

    it('shows connection warning when WebSocket is disconnected', () => {
      // Mock disconnected WebSocket
      jest.doMock('../../../hooks/useWebSocket', () => ({
        useWebSocket: () => ({
          socket: null,
          isConnected: false,
          sendMessage: jest.fn()
        })
      }));

      renderWithProviders(<SimulationDashboard />);

      expect(screen.getByText(/Real-time features are currently unavailable/)).toBeInTheDocument();
    });
  });

  describe('Active Session Handling', () => {
    it('shows active session alert when session exists', () => {
      const activeSession = {
        session_id: 'session123',
        scenario: {
          title: 'Active Billing Scenario'
        },
        status: 'active'
      };

      renderWithProviders(<SimulationDashboard />, {
        simulation: {
          activeSession
        }
      });

      expect(screen.getByText(/You have an active simulation session/)).toBeInTheDocument();
      expect(screen.getByText('Active Billing Scenario')).toBeInTheDocument();
      expect(screen.getByText('Resume Session')).toBeInTheDocument();
    });

    it('disables scenario cards when active session exists', () => {
      const activeSession = {
        session_id: 'session123',
        scenario: { title: 'Active Session' },
        status: 'active'
      };

      renderWithProviders(<SimulationDashboard />, {
        simulation: {
          activeSession
        }
      });

      const startButtons = screen.getAllByText(/Start/);
      startButtons.forEach(button => {
        expect(button).toBeDisabled();
      });
    });
  });

  describe('Filtering', () => {
    it('filters scenarios by difficulty', async () => {
      renderWithProviders(<SimulationDashboard />);

      // Click intermediate filter
      const intermediateButton = screen.getByText('Intermediate');
      fireEvent.click(intermediateButton);

      await waitFor(() => {
        expect(screen.getByText('Billing Issue Resolution')).toBeInTheDocument();
        expect(screen.queryByText('Technical Support')).not.toBeInTheDocument();
      });
    });

    it('shows all scenarios when "All Levels" is selected', async () => {
      renderWithProviders(<SimulationDashboard />);

      // First filter to intermediate
      fireEvent.click(screen.getByText('Intermediate'));
      
      // Then click "All Levels"
      fireEvent.click(screen.getByText('All Levels'));

      await waitFor(() => {
        expect(screen.getByText('Billing Issue Resolution')).toBeInTheDocument();
        expect(screen.getByText('Technical Support')).toBeInTheDocument();
      });
    });

    it('shows "no scenarios" message when filters match nothing', async () => {
      renderWithProviders(<SimulationDashboard />, {
        simulation: {
          scenarios: [] // Empty scenarios after filtering
        }
      });

      expect(screen.getByText('No scenarios found matching your filters')).toBeInTheDocument();
      expect(screen.getByText('Clear Filters')).toBeInTheDocument();
    });
  });

  describe('Scenario Interaction', () => {
    it('opens session dialog when starting a scenario', async () => {
      const store = createMockStore();
      const mockDispatch = jest.fn().mockResolvedValue({
        unwrap: () => Promise.resolve({
          success: true,
          data: { session_id: 'new-session' }
        })
      });
      
      store.dispatch = mockDispatch;

      render(
        <Provider store={store}>
          <BrowserRouter>
            <ThemeProvider theme={theme}>
              <SimulationDashboard />
            </ThemeProvider>
          </BrowserRouter>
        </Provider>
      );

      // Find and click start button for first scenario
      const startButtons = screen.getAllByText(/Start/);
      fireEvent.click(startButtons[0]);

      await waitFor(() => {
        expect(mockDispatch).toHaveBeenCalledWith(
          expect.objectContaining({
            type: expect.stringContaining('createSimulationSession')
          })
        );
      });
    });

    it('shows error toast when session creation fails', async () => {
      const store = createMockStore();
      const mockDispatch = jest.fn().mockRejectedValue(new Error('Session creation failed'));
      
      store.dispatch = mockDispatch;

      render(
        <Provider store={store}>
          <BrowserRouter>
            <ThemeProvider theme={theme}>
              <SimulationDashboard />
            </ThemeProvider>
          </BrowserRouter>
        </Provider>
      );

      const startButtons = screen.getAllByText(/Start/);
      fireEvent.click(startButtons[0]);

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith(
          expect.stringContaining('Failed to start simulation')
        );
      });
    });
  });

  describe('Progress Display', () => {
    it('displays skill levels correctly', () => {
      renderWithProviders(<SimulationDashboard />);

      expect(screen.getByText('Skill Development')).toBeInTheDocument();
      expect(screen.getByText('empathy')).toBeInTheDocument();
      expect(screen.getByText('efficiency')).toBeInTheDocument();
      expect(screen.getByText('accuracy')).toBeInTheDocument();
      expect(screen.getByText('communication')).toBeInTheDocument();
      expect(screen.getByText('problem solving')).toBeInTheDocument();
    });

    it('shows achievements when badges are earned', () => {
      renderWithProviders(<SimulationDashboard />);

      expect(screen.getByText('Recent Achievements')).toBeInTheDocument();
      expect(screen.getByText('First Steps')).toBeInTheDocument();
    });

    it('calculates progress percentages correctly', () => {
      renderWithProviders(<SimulationDashboard />);

      // Check that skill percentages are displayed
      expect(screen.getByText('75%')).toBeInTheDocument(); // empathy
      expect(screen.getByText('82%')).toBeInTheDocument(); // efficiency
      expect(screen.getByText('79%')).toBeInTheDocument(); // accuracy
    });
  });

  describe('WebSocket Integration', () => {
    it('handles WebSocket authentication', () => {
      const mockSocket = {
        emit: jest.fn(),
        on: jest.fn(),
        off: jest.fn()
      };

      jest.doMock('../../../hooks/useWebSocket', () => ({
        useWebSocket: () => ({
          socket: mockSocket,
          isConnected: true,
          sendMessage: jest.fn()
        })
      }));

      renderWithProviders(<SimulationDashboard />);

      expect(mockSocket.emit).toHaveBeenCalledWith('authenticate', {
        token: 'mock-token',
        agentId: 'user123'
      });
    });

    it('handles session join events', () => {
      const mockSocket = {
        emit: jest.fn(),
        on: jest.fn(),
        off: jest.fn()
      };

      jest.doMock('../../../hooks/useWebSocket', () => ({
        useWebSocket: () => ({
          socket: mockSocket,
          isConnected: true,
          sendMessage: jest.fn()
        })
      }));

      renderWithProviders(<SimulationDashboard />);

      // Verify event listeners are set up
      expect(mockSocket.on).toHaveBeenCalledWith('authenticated', expect.any(Function));
      expect(mockSocket.on).toHaveBeenCalledWith('session_joined', expect.any(Function));
      expect(mockSocket.on).toHaveBeenCalledWith('ai_coaching', expect.any(Function));
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels and roles', () => {
      renderWithProviders(<SimulationDashboard />);

      expect(screen.getByRole('main')).toBeInTheDocument();
      expect(screen.getByRole('heading', { name: /Training Simulations/ })).toBeInTheDocument();
      
      // Check that buttons have accessible names
      const startButtons = screen.getAllByRole('button', { name: /Start/ });
      expect(startButtons.length).toBeGreaterThan(0);
    });

    it('supports keyboard navigation', () => {
      renderWithProviders(<SimulationDashboard />);

      const firstButton = screen.getAllByRole('button')[0];
      firstButton.focus();
      expect(document.activeElement).toBe(firstButton);
    });
  });

  describe('Responsive Design', () => {
    it('adapts to different screen sizes', () => {
      // Mock window.matchMedia for responsive testing
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: jest.fn().mockImplementation(query => ({
          matches: query.includes('max-width: 768px'),
          media: query,
          onchange: null,
          addListener: jest.fn(),
          removeListener: jest.fn(),
          addEventListener: jest.fn(),
          removeEventListener: jest.fn(),
          dispatchEvent: jest.fn(),
        })),
      });

      renderWithProviders(<SimulationDashboard />);

      // Component should render without errors on mobile
      expect(screen.getByText('🎮 Training Simulations')).toBeInTheDocument();
    });
  });

  describe('Performance', () => {
    it('renders efficiently with large number of scenarios', () => {
      const manyScenarios = Array.from({ length: 50 }, (_, i) => ({
        _id: `scenario${i}`,
        title: `Scenario ${i}`,
        description: `Description ${i}`,
        difficulty: 'intermediate',
        category: 'general',
        tags: ['test'],
        estimated_duration: 15,
        learning_objectives: ['empathy']
      }));

      const startTime = performance.now();
      
      renderWithProviders(<SimulationDashboard />, {
        simulation: {
          scenarios: manyScenarios
        }
      });

      const endTime = performance.now();
      const renderTime = endTime - startTime;

      // Should render within reasonable time (less than 100ms)
      expect(renderTime).toBeLessThan(100);
      expect(screen.getByText('Scenario 0')).toBeInTheDocument();
    });
  });
});
