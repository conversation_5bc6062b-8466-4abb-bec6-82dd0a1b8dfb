/** * ============================================= * SCENARIO SELECTOR COMPONENT * Interactive scenario selection for agent training * ============================================= */ import React, { useState } from 'react'; import { Box, Grid, Card, CardContent, CardActions, Typography, Button, Chip, FormControl, InputLabel, Select, MenuItem, TextField, InputAdornment, CircularProgress, Alert } from '@mui/material'; import { Search as SearchIcon, PlayArrow as StartIcon, Star as DifficultyIcon, Category as CategoryIcon, Schedule as TimeIcon, Person as CustomerIcon } from '@mui/icons-material'; import { SimulationScenario } from '../../store/slices/simulationSlice'; interface ScenarioSelectorProps { scenarios: SimulationScenario[]; onStartSimulation: (scenarioId: string) => void; onFilterChange: (difficulty: string, category: string) => void; selectedDifficulty: string; selectedCategory: string; loading: boolean; } const ScenarioSelector: React.FC<ScenarioSelectorProps> = ({ scenarios, onStartSimulation, onFilterChange, selectedDifficulty, selectedCategory, loading }) => { const [searchTerm, setSearchTerm] = useState(''); const filteredScenarios = scenarios.filter(scenario => scenario.title.toLowerCase().includes(searchTerm.toLowerCase()) || scenario.description.toLowerCase().includes(searchTerm.toLowerCase()) ); const getDifficultyColor = (difficulty: string) => { switch (difficulty) { case 'beginner': return 'success'; case 'intermediate': return 'warning'; case 'expert': return 'error'; default: return 'default'; } }; const getCategoryIcon = (category: string) => { switch (category) { case 'billing': return ''; case 'technical': return '[CONFIG]'; case 'sales': return ''; case 'retention': return ''; case 'complaint': return ''; default: return ''; } }; if (loading) { return ( <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}> <CircularProgress /> </Box> ); } return ( <Box> {/* Filters */} <Box sx={{ mb: 3 }}> <Grid container spacing={2} alignItems="center"> <Grid item xs={12} md={4}> <TextField fullWidth placeholder="Rechercher un scénario..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} InputProps={{ startAdornment: ( <InputAdornment position="start"> <SearchIcon /> </InputAdornment> ), }} /> </Grid> <Grid item xs={12} md={3}> <FormControl fullWidth> <InputLabel>Difficulté</InputLabel> <Select value={selectedDifficulty} label="Difficulté" onChange={(e) => onFilterChange(e.target.value, selectedCategory)} > <MenuItem value="all">Toutes</MenuItem> <MenuItem value="beginner">Débutant</MenuItem> <MenuItem value="intermediate">Intermédiaire</MenuItem> <MenuItem value="expert">Expert</MenuItem> </Select> </FormControl> </Grid> <Grid item xs={12} md={3}> <FormControl fullWidth> <InputLabel>Catégorie</InputLabel> <Select value={selectedCategory} label="Catégorie" onChange={(e) => onFilterChange(selectedDifficulty, e.target.value)} > <MenuItem value="all">Toutes</MenuItem> <MenuItem value="billing">Facturation</MenuItem> <MenuItem value="technical">Technique</MenuItem> <MenuItem value="sales">Ventes</MenuItem> <MenuItem value="retention">Rétention</MenuItem> <MenuItem value="complaint">Réclamation</MenuItem> </Select> </FormControl> </Grid> <Grid item xs={12} md={2}> <Typography variant="body2" color="text.secondary"> {filteredScenarios.length} scénario(s) </Typography> </Grid> </Grid> </Box> {/* Scenarios Grid */} {filteredScenarios.length > 0 ? ( <Grid container spacing={3}> {filteredScenarios.map((scenario) => ( <Grid item xs={12} md={6} lg={4} key={scenario.id}> <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column', transition: 'transform 0.2s, box-shadow 0.2s', '&:hover': { transform: 'translateY(-4px)', boxShadow: 4 } }} > <CardContent sx={{ flexGrow: 1 }}> <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}> <Typography variant="h6" component="h3" fontWeight="bold"> {scenario.title} </Typography> <Box sx={{ display: 'flex', gap: 0.5 }}> <Chip label={scenario.difficulty} color={getDifficultyColor(scenario.difficulty)} size="small" icon={<DifficultyIcon />} /> </Box> </Box> <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}> {scenario.description} </Typography> <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}> <Chip label={scenario.category} variant="outlined" size="small" icon={<span>{getCategoryIcon(scenario.category)}</span>} /> <Chip label={`${scenario.expected_resolution_time}min`} variant="outlined" size="small" icon={<TimeIcon />} /> <Chip label={scenario.customer_profile.personality} variant="outlined" size="small" icon={<CustomerIcon />} /> </Box> <Typography variant="subtitle2" sx={{ mb: 1 }}> Objectifs d'apprentissage: </Typography> <Box component="ul" sx={{ pl: 2, m: 0 }}> {scenario.learning_objectives.slice(0, 3).map((objective, index) => ( <Typography component="li" variant="body2" key={index} sx={{ mb: 0.5 }}> {objective} </Typography> ))} {scenario.learning_objectives.length > 3 && ( <Typography component="li" variant="body2" color="text.secondary"> +{scenario.learning_objectives.length - 3} autres... </Typography> )} </Box> <Box sx={{ mt: 2 }}> <Typography variant="subtitle2" sx={{ mb: 1 }}> Critères de succès: </Typography> <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}> <Chip label={`Satisfaction ≥ ${scenario.success_criteria.min_satisfaction}/10`} size="small" variant="outlined" color="success" /> <Chip label={`Temps ≤ ${scenario.success_criteria.max_resolution_time}min`} size="small" variant="outlined" color="info" /> </Box> </Box> </CardContent> <CardActions sx={{ p: 2, pt: 0 }}> <Button fullWidth variant="contained" startIcon={<StartIcon />} onClick={() => onStartSimulation(scenario.id)} sx={{ background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)', '&:hover': { background: 'linear-gradient(45deg, #1976D2 30%, #0288D1 90%)', } }} > Commencer la Simulation </Button> </CardActions> </Card> </Grid> ))} </Grid> ) : ( <Alert severity="info" sx={{ mt: 4 }}> <Typography variant="h6">Aucun scénario trouvé</Typography> <Typography variant="body2"> Essayez de modifier vos critères de recherche ou de filtrage. </Typography> </Alert> )} </Box> ); }; export default ScenarioSelector;