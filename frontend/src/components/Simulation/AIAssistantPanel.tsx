/** * ============================================= * [AI] AI ASSISTANT PANEL COMPONENT * Real-time AI suggestions and feedback during simulation * ============================================= */ import React from 'react'; import { Box, Typography, Card, CardContent, List, ListItem, ListItemText, ListItemIcon, Chip, IconButton, Drawer, Button } from '@mui/material'; import { Psychology as AIIcon, Lightbulb as SuggestionIcon, Feedback as FeedbackIcon, Close as CloseIcon, ThumbUp as ThumbUpIcon, ThumbDown as ThumbDownIcon } from '@mui/icons-material'; interface AIAssistantPanelProps { suggestions: any[]; feedback: any[]; onClose: () => void; } const AIAssistantPanel: React.FC<AIAssistantPanelProps> = ({ suggestions, feedback, onClose }) => { return ( <Drawer anchor="right" open={true} onClose={onClose} sx={{ '& .MuiDrawer-paper': { width: 400, p: 2 } }} > <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}> <Typography variant="h6"> <AIIcon sx={{ mr: 1, verticalAlign: 'middle' }} /> Assistant IA </Typography> <IconButton onClick={onClose}> <CloseIcon /> </IconButton> </Box> {/* AI Suggestions */} <Card sx={{ mb: 2 }}> <CardContent> <Typography variant="subtitle1" sx={{ mb: 2 }}> <SuggestionIcon sx={{ mr: 1, verticalAlign: 'middle' }} /> Suggestions ({suggestions.length}) </Typography> <List dense> {suggestions.slice(0, 3).map((suggestion, index) => ( <ListItem key={index} sx={{ px: 0 }}> <ListItemText primary={suggestion.content || `Suggestion ${index + 1}`} secondary={`Confiance: ${Math.round((suggestion.confidence || 0.8) * 100)}%`} /> <Box sx={{ display: 'flex', gap: 0.5 }}> <IconButton size="small" color="success"> <ThumbUpIcon fontSize="small" /> </IconButton> <IconButton size="small" color="error"> <ThumbDownIcon fontSize="small" /> </IconButton> </Box> </ListItem> ))} </List> {suggestions.length === 0 && ( <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 2 }}> Aucune suggestion disponible </Typography> )} </CardContent> </Card> {/* Real-time Feedback */} <Card> <CardContent> <Typography variant="subtitle1" sx={{ mb: 2 }}> <FeedbackIcon sx={{ mr: 1, verticalAlign: 'middle' }} /> Feedback Temps Réel </Typography> <List dense> {feedback.slice(0, 5).map((item, index) => ( <ListItem key={index} sx={{ px: 0 }}> <ListItemIcon> <Chip label={item.type || 'info'} color={item.type === 'praise' ? 'success' : item.type === 'warning' ? 'warning' : 'default'} size="small" /> </ListItemIcon> <ListItemText primary={item.message || `Feedback ${index + 1}`} secondary={item.category || 'Général'} /> </ListItem> ))} </List> {feedback.length === 0 && ( <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 2 }}> Aucun feedback disponible </Typography> )} </CardContent> </Card> </Drawer> ); }; export default AIAssistantPanel;