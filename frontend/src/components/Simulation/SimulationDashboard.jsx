/** * ============================================= * SIMULATION DASHBOARD COMPONENT * Main dashboard for agent training simulations * Real-time progress tracking and scenario selection * ============================================= */ import React, { useState, useEffect, useCallback } from 'react'; import { useDispatch, useSelector } from 'react-redux'; import { Box, Grid, Card, CardContent, Typography, Button, Chip, LinearProgress, Avatar, IconButton, Tooltip, Dialog, DialogTitle, DialogContent, DialogActions, Alert, Skeleton } from '@mui/material'; import { PlayArrow as PlayIcon, Pause as PauseIcon, Stop as StopIcon, TrendingUp as TrendingUpIcon, School as SchoolIcon, EmojiEvents as TrophyIcon, Assessment as AssessmentIcon, Refresh as RefreshIcon } from '@mui/icons-material'; import { motion, AnimatePresence } from 'framer-motion'; import { toast } from 'react-toastify'; import { fetchScenarios, fetchAgentProgress, createSimulationSession, fetchActiveSession } from '../../store/slices/simulationSlice'; import { useWebSocket } from '../../hooks/useWebSocket'; import ScenarioCard from './ScenarioCard'; import ProgressChart from './ProgressChart'; import AchievementBadge from './AchievementBadge'; import SimulationSession from './SimulationSession'; const SimulationDashboard = () => { const dispatch = useDispatch(); const { scenarios, agentProgress, activeSession, loading, error } = useSelector(state => state.simulation); const { user } = useSelector(state => state.auth); const [selectedScenario, setSelectedScenario] = useState(null); const [showSessionDialog, setShowSessionDialog] = useState(false); const [filters, setFilters] = useState({ difficulty: 'all', category: 'all' }); // WebSocket connection for real-time updates const { socket, isConnected } = useWebSocket('/simulation'); useEffect(() => { // Fetch initial data dispatch(fetchScenarios()); dispatch(fetchAgentProgress()); dispatch(fetchActiveSession()); }, [dispatch]); useEffect(() => { // Set up WebSocket event listeners if (socket && isConnected) { socket.emit('authenticate', { token: localStorage.getItem('token'), agentId: user.id }); socket.on('authenticated', (data) => { console.log('Simulation WebSocket authenticated:', data); }); socket.on('session_joined', (sessionData) => { console.log('Session joined:', sessionData); }); socket.on('message_processed', (data) => { // Handle real-time message processing updates console.log('Message processed:', data); }); socket.on('ai_coaching', (coaching) => { // Show AI coaching notifications toast.info(coaching.message, { position: 'top-right', autoClose: 5000 }); }); socket.on('session_ended', (results) => { // Handle session completion console.log('Session ended:', results); dispatch(fetchAgentProgress()); // Refresh progress setShowSessionDialog(false); }); return () => { socket.off('authenticated'); socket.off('session_joined'); socket.off('message_processed'); socket.off('ai_coaching'); socket.off('session_ended'); }; } }, [socket, isConnected, user.id, dispatch]); const handleStartSimulation = useCallback(async (scenario) => { try { const result = await dispatch(createSimulationSession({ scenarioId: scenario._id, settings: { difficulty_adjustment: true, ai_coaching_enabled: true, real_time_feedback: true } })).unwrap(); if (result.success) { setSelectedScenario(scenario); setShowSessionDialog(true); // Join WebSocket session if (socket) { socket.emit('join_session', { sessionId: result.data.session_id }); } } } catch (error) { toast.error('Failed to start simulation: ' + error.message); } }, [dispatch, socket]); const handleResumeSession = useCallback(() => { if (activeSession) { setSelectedScenario(activeSession.scenario); setShowSessionDialog(true); // Join existing WebSocket session if (socket) { socket.emit('join_session', { sessionId: activeSession.session_id }); } } }, [activeSession, socket]); const filteredScenarios = scenarios.filter(scenario => { if (filters.difficulty !== 'all' && scenario.difficulty !== filters.difficulty) { return false; } if (filters.category !== 'all' && scenario.category !== filters.category) { return false; } return true; }); const getDifficultyColor = (difficulty) => { switch (difficulty) { case 'beginner': return 'success'; case 'intermediate': return 'warning'; case 'expert': return 'error'; default: return 'default'; } }; const getSkillLevelColor = (level) => { if (level >= 80) return 'success'; if (level >= 60) return 'warning'; return 'error'; }; if (loading && !scenarios.length) { return ( <Box sx={{ p: 3 }}> <Grid container spacing={3}> {[...Array(6)].map((_, index) => ( <Grid item xs={12} md={6} lg={4} key={index}> <Skeleton variant="rectangular" height={200} /> </Grid> ))} </Grid> </Box> ); } return ( <Box sx={{ p: 3 }}> {/* Header */} <Box sx={{ mb: 4 }}> <Typography variant="h4" component="h1" gutterBottom> Training Simulations </Typography> <Typography variant="body1" color="text.secondary"> Practice your customer service skills with AI-powered scenarios </Typography> </Box> {/* Connection Status */} {!isConnected && ( <Alert severity="warning" sx={{ mb: 3 }}> Real-time features are currently unavailable. Attempting to reconnect... </Alert> )} {/* Active Session Alert */} {activeSession && ( <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.3 }} > <Alert severity="info" sx={{ mb: 3 }} action={ <Button color="inherit" size="small" onClick={handleResumeSession} startIcon={<PlayIcon />} > Resume Session </Button> } > You have an active simulation session: {activeSession.scenario?.title} </Alert> </motion.div> )} {/* Progress Overview */} <Grid container spacing={3} sx={{ mb: 4 }}> <Grid item xs={12} md={3}> <Card> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}> <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}> <SchoolIcon /> </Avatar> <Typography variant="h6">Overall Progress</Typography> </Box> <Typography variant="h4" color="primary"> {agentProgress?.average_score || 0}% </Typography> <LinearProgress variant="determinate" value={agentProgress?.average_score || 0} sx={{ mt: 1 }} /> </CardContent> </Card> </Grid> <Grid item xs={12} md={3}> <Card> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}> <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}> <TrophyIcon /> </Avatar> <Typography variant="h6">Sessions Completed</Typography> </Box> <Typography variant="h4" color="success.main"> {agentProgress?.completed_sessions || 0} </Typography> <Typography variant="body2" color="text.secondary"> Total: {agentProgress?.total_sessions || 0} </Typography> </CardContent> </Card> </Grid> <Grid item xs={12} md={3}> <Card> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}> <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}> <TrendingUpIcon /> </Avatar> <Typography variant="h6">Current Streak</Typography> </Box> <Typography variant="h4" color="warning.main"> {agentProgress?.current_streak || 0} </Typography> <Typography variant="body2" color="text.secondary"> Best: {agentProgress?.best_streak || 0} </Typography> </CardContent> </Card> </Grid> <Grid item xs={12} md={3}> <Card> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}> <Avatar sx={{ bgcolor: 'info.main', mr: 2 }}> <AssessmentIcon /> </Avatar> <Typography variant="h6">Badges Earned</Typography> </Box> <Typography variant="h4" color="info.main"> {agentProgress?.badges_earned?.length || 0} </Typography> <Typography variant="body2" color="text.secondary"> Achievements unlocked </Typography> </CardContent> </Card> </Grid> </Grid> {/* Skill Levels */} {agentProgress?.skill_levels && ( <Card sx={{ mb: 4 }}> <CardContent> <Typography variant="h6" gutterBottom> Skill Development </Typography> <Grid container spacing={2}> {Object.entries(agentProgress.skill_levels).map(([skill, level]) => ( <Grid item xs={12} sm={6} md={2.4} key={skill}> <Box sx={{ textAlign: 'center' }}> <Typography variant="body2" sx={{ mb: 1, textTransform: 'capitalize' }}> {skill.replace('_', ' ')} </Typography> <Box sx={{ position: 'relative', display: 'inline-flex' }}> <LinearProgress variant="determinate" value={level} color={getSkillLevelColor(level)} sx={{ height: 8, borderRadius: 4, width: 80 }} /> </Box> <Typography variant="caption" color="text.secondary"> {level}% </Typography> </Box> </Grid> ))} </Grid> </CardContent> </Card> )} {/* Filters */} <Box sx={{ mb: 3, display: 'flex', gap: 2, flexWrap: 'wrap' }}> <Button variant={filters.difficulty === 'all' ? 'contained' : 'outlined'} onClick={() => setFilters(prev => ({ ...prev, difficulty: 'all' }))} > All Levels </Button> <Button variant={filters.difficulty === 'beginner' ? 'contained' : 'outlined'} color="success" onClick={() => setFilters(prev => ({ ...prev, difficulty: 'beginner' }))} > Beginner </Button> <Button variant={filters.difficulty === 'intermediate' ? 'contained' : 'outlined'} color="warning" onClick={() => setFilters(prev => ({ ...prev, difficulty: 'intermediate' }))} > Intermediate </Button> <Button variant={filters.difficulty === 'expert' ? 'contained' : 'outlined'} color="error" onClick={() => setFilters(prev => ({ ...prev, difficulty: 'expert' }))} > Expert </Button> <Tooltip title="Refresh scenarios"> <IconButton onClick={() => dispatch(fetchScenarios())}> <RefreshIcon /> </IconButton> </Tooltip> </Box> {/* Scenarios Grid */} <Grid container spacing={3}> <AnimatePresence> {filteredScenarios.map((scenario, index) => ( <Grid item xs={12} md={6} lg={4} key={scenario._id}> <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} exit={{ opacity: 0, y: -20 }} transition={{ duration: 0.3, delay: index * 0.1 }} > <ScenarioCard scenario={scenario} onStart={() => handleStartSimulation(scenario)} disabled={!!activeSession} /> </motion.div> </Grid> ))} </AnimatePresence> </Grid> {/* No scenarios message */} {filteredScenarios.length === 0 && !loading && ( <Box sx={{ textAlign: 'center', py: 8 }}> <Typography variant="h6" color="text.secondary"> No scenarios found matching your filters </Typography> <Button variant="outlined" onClick={() => setFilters({ difficulty: 'all', category: 'all' })} sx={{ mt: 2 }} > Clear Filters </Button> </Box> )} {/* Recent Achievements */} {agentProgress?.badges_earned?.length > 0 && ( <Card sx={{ mt: 4 }}> <CardContent> <Typography variant="h6" gutterBottom> Recent Achievements </Typography> <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}> {agentProgress.badges_earned.slice(0, 5).map((badge, index) => ( <AchievementBadge key={index} badge={badge} /> ))} </Box> </CardContent> </Card> )} {/* Simulation Session Dialog */} <Dialog open={showSessionDialog} onClose={() => setShowSessionDialog(false)} maxWidth="lg" fullWidth PaperProps={{ sx: { height: '90vh' } }} > <DialogTitle> <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}> <Typography variant="h6"> {selectedScenario?.title} </Typography> <Chip label={selectedScenario?.difficulty} color={getDifficultyColor(selectedScenario?.difficulty)} size="small" /> </Box> </DialogTitle> <DialogContent sx={{ p: 0 }}> {selectedScenario && ( <SimulationSession scenario={selectedScenario} onEnd={() => setShowSessionDialog(false)} /> )} </DialogContent> </Dialog> {/* Error Display */} {error && ( <Alert severity="error" sx={{ mt: 2 }}> {error} </Alert> )} </Box> ); }; export default SimulationDashboard;