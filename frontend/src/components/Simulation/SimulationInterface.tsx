/** * ============================================= * SIMULATION INTERFACE COMPONENT * Main simulation chat interface for agent training * ============================================= */ import React, { useState, useRef, useEffect } from 'react'; import { Box, Paper, Typography, TextField, Button, Avatar, Chip, LinearProgress, Grid, Card, CardContent, IconButton, Tooltip, Divider } from '@mui/material'; import { Send as SendIcon, Stop as StopIcon, Pause as PauseIcon, Psychology as AIIcon, Assessment as MetricsIcon, Person as CustomerIcon, Support as AgentIcon } from '@mui/icons-material'; import { SimulationSession } from '../../store/slices/simulationSlice'; interface SimulationInterfaceProps { session: SimulationSession; onEndSimulation: () => void; } const SimulationInterface: React.FC<SimulationInterfaceProps> = ({ session, onEndSimulation }) => { const [message, setMessage] = useState(''); const [isTyping, setIsTyping] = useState(false); const messagesEndRef = useRef<HTMLDivElement>(null); const scrollToBottom = () => { messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' }); }; useEffect(() => { scrollToBottom(); }, [session.messages]); const handleSendMessage = () => { if (message.trim()) { // This would dispatch the sendMessage action console.log('Sending message:', message); setMessage(''); setIsTyping(true); // Simulate customer response delay setTimeout(() => { setIsTyping(false); }, 2000); } }; const handleKeyPress = (event: React.KeyboardEvent) => { if (event.key === 'Enter' && !event.shiftKey) { event.preventDefault(); handleSendMessage(); } }; const getMessageAvatar = (sender: string) => { switch (sender) { case 'customer': return <CustomerIcon />; case 'agent': return <AgentIcon />; case 'system': return <AIIcon />; default: return <CustomerIcon />; } }; const getMessageColor = (sender: string) => { switch (sender) { case 'customer': return '#f5f5f5'; case 'agent': return '#e3f2fd'; case 'system': return '#fff3e0'; default: return '#f5f5f5'; } }; const getPerformanceColor = (score: number) => { if (score >= 80) return 'success'; if (score >= 60) return 'warning'; return 'error'; }; return ( <Box sx={{ height: '70vh', display: 'flex', flexDirection: 'column' }}> {/* Header with session info and metrics */} <Paper sx={{ p: 2, mb: 2 }}> <Grid container spacing={2} alignItems="center"> <Grid item xs={12} md={8}> <Typography variant="h6" sx={{ mb: 1 }}> Simulation en cours - {session.scenario_id} </Typography> <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}> <Chip label={`Statut: ${session.status}`} color={session.status === 'active' ? 'success' : 'default'} size="small" /> <Chip label={`Messages: ${session.messages.length}`} color="info" size="small" /> <Chip label={`Temps: ${Math.round(session.performance_metrics.resolution_time)}min`} color="default" size="small" /> </Box> </Grid> <Grid item xs={12} md={4}> <Box sx={{ textAlign: 'right' }}> <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}> Score Global </Typography> <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', gap: 1 }}> <Typography variant="h6" color={getPerformanceColor(session.performance_metrics.overall_score) + '.main'}> {Math.round(session.performance_metrics.overall_score)}% </Typography> <LinearProgress variant="determinate" value={session.performance_metrics.overall_score} color={getPerformanceColor(session.performance_metrics.overall_score)} sx={{ width: 100, height: 8, borderRadius: 4 }} /> </Box> </Box> </Grid> </Grid> </Paper> {/* Performance Metrics Cards */} <Grid container spacing={2} sx={{ mb: 2 }}> <Grid item xs={6} md={3}> <Card> <CardContent sx={{ textAlign: 'center', py: 1 }}> <Typography variant="body2" color="text.secondary">Empathie</Typography> <Typography variant="h6" color={getPerformanceColor(session.performance_metrics.empathy_score) + '.main'}> {Math.round(session.performance_metrics.empathy_score)}% </Typography> </CardContent> </Card> </Grid> <Grid item xs={6} md={3}> <Card> <CardContent sx={{ textAlign: 'center', py: 1 }}> <Typography variant="body2" color="text.secondary">Efficacité</Typography> <Typography variant="h6" color={getPerformanceColor(session.performance_metrics.efficiency_score) + '.main'}> {Math.round(session.performance_metrics.efficiency_score)}% </Typography> </CardContent> </Card> </Grid> <Grid item xs={6} md={3}> <Card> <CardContent sx={{ textAlign: 'center', py: 1 }}> <Typography variant="body2" color="text.secondary">Précision</Typography> <Typography variant="h6" color={getPerformanceColor(session.performance_metrics.accuracy_score) + '.main'}> {Math.round(session.performance_metrics.accuracy_score)}% </Typography> </CardContent> </Card> </Grid> <Grid item xs={6} md={3}> <Card> <CardContent sx={{ textAlign: 'center', py: 1 }}> <Typography variant="body2" color="text.secondary">Satisfaction</Typography> <Typography variant="h6" color={getPerformanceColor(session.performance_metrics.customer_satisfaction * 10) + '.main'}> {session.performance_metrics.customer_satisfaction}/10 </Typography> </CardContent> </Card> </Grid> </Grid> {/* Chat Messages */} <Paper sx={{ flexGrow: 1, p: 2, overflow: 'auto', mb: 2 }}> <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}> {session.messages.map((msg, index) => ( <Box key={msg.id} sx={{ display: 'flex', alignItems: 'flex-start', gap: 2, flexDirection: msg.sender === 'agent' ? 'row-reverse' : 'row' }} > <Avatar sx={{ bgcolor: msg.sender === 'agent' ? 'primary.main' : 'grey.500' }}> {getMessageAvatar(msg.sender)} </Avatar> <Paper sx={{ p: 2, maxWidth: '70%', bgcolor: getMessageColor(msg.sender), borderRadius: 2 }} > <Typography variant="body1">{msg.content}</Typography> <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}> {new Date(msg.timestamp).toLocaleTimeString()} {msg.metadata?.response_time && ( <> • Temps de réponse: {msg.metadata.response_time}s</> )} {msg.metadata?.ai_suggestion_used && ( <> • IA utilisée</> )} </Typography> </Paper> </Box> ))} {isTyping && ( <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}> <Avatar sx={{ bgcolor: 'grey.500' }}> <CustomerIcon /> </Avatar> <Paper sx={{ p: 2, bgcolor: '#f5f5f5', borderRadius: 2 }}> <Typography variant="body1" color="text.secondary"> Le client est en train d'écrire... </Typography> </Paper> </Box> )} <div ref={messagesEndRef} /> </Box> </Paper> {/* Message Input */} <Paper sx={{ p: 2 }}> <Box sx={{ display: 'flex', gap: 2, alignItems: 'flex-end' }}> <TextField fullWidth multiline maxRows={3} placeholder="Tapez votre réponse..." value={message} onChange={(e) => setMessage(e.target.value)} onKeyPress={handleKeyPress} disabled={session.status !== 'active'} /> <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}> <Button variant="contained" onClick={handleSendMessage} disabled={!message.trim() || session.status !== 'active'} startIcon={<SendIcon />} > Envoyer </Button> <Button variant="outlined" color="error" onClick={onEndSimulation} startIcon={<StopIcon />} size="small" > Terminer </Button> </Box> </Box> </Paper> </Box> ); }; export default SimulationInterface;