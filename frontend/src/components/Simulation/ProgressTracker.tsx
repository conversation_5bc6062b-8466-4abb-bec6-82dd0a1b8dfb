/** * ============================================= * [METRICS] PROGRESS TRACKER COMPONENT * Agent progress tracking and session history * ============================================= */ import React from 'react'; import { Box, Typography, Card, CardContent, Grid, LinearProgress, Chip, List, ListItem, ListItemText, Avatar } from '@mui/material'; import { TrendingUp as ProgressIcon, EmojiEvents as BadgeIcon, History as HistoryIcon, Star as StarIcon } from '@mui/icons-material'; interface ProgressTrackerProps { agentProgress: any; completedSessions: any[]; } const ProgressTracker: React.FC<ProgressTrackerProps> = ({ agentProgress, completedSessions }) => { const getSkillColor = (level: number) => { if (level >= 80) return 'success'; if (level >= 60) return 'warning'; return 'error'; }; return ( <Box> <Typography variant="h6" sx={{ mb: 3 }}> Progression et Historique </Typography> <Grid container spacing={3}> {/* Skills Progress */} <Grid item xs={12} md={6}> <Card> <CardContent> <Typography variant="h6" sx={{ mb: 2 }}> <ProgressIcon sx={{ mr: 1, verticalAlign: 'middle' }} /> Compétences </Typography> {Object.entries(agentProgress.skill_levels || {}).map(([skill, level]) => ( <Box key={skill} sx={{ mb: 2 }}> <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}> <Typography variant="body2" sx={{ textTransform: 'capitalize' }}> {skill} </Typography> <Typography variant="body2" color={getSkillColor(level as number) + '.main'}> {(level as number)}% </Typography> </Box> <LinearProgress variant="determinate" value={level as number} color={getSkillColor(level as number)} sx={{ height: 6, borderRadius: 3 }} /> </Box> ))} </CardContent> </Card> </Grid> {/* Badges */} <Grid item xs={12} md={6}> <Card> <CardContent> <Typography variant="h6" sx={{ mb: 2 }}> <BadgeIcon sx={{ mr: 1, verticalAlign: 'middle' }} /> Badges Obtenus ({agentProgress.badges_earned?.length || 0}) </Typography> <Grid container spacing={1}> {(agentProgress.badges_earned || []).slice(0, 6).map((badge: any, index: number) => ( <Grid item xs={4} key={index}> <Card variant="outlined" sx={{ textAlign: 'center', p: 1 }}> <Avatar sx={{ mx: 'auto', mb: 1, bgcolor: 'primary.main' }}> <StarIcon /> </Avatar> <Typography variant="caption" display="block"> {badge.name || `Badge ${index + 1}`} </Typography> <Chip label={badge.rarity || 'common'} size="small" color={badge.rarity === 'legendary' ? 'error' : badge.rarity === 'epic' ? 'warning' : 'default'} /> </Card> </Grid> ))} </Grid> {(agentProgress.badges_earned?.length || 0) === 0 && ( <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 2 }}> Aucun badge obtenu pour le moment </Typography> )} </CardContent> </Card> </Grid> {/* Session History */} <Grid item xs={12}> <Card> <CardContent> <Typography variant="h6" sx={{ mb: 2 }}> <HistoryIcon sx={{ mr: 1, verticalAlign: 'middle' }} /> Historique des Sessions ({completedSessions.length}) </Typography> <List> {completedSessions.slice(0, 5).map((session, index) => ( <ListItem key={index} divider> <ListItemText primary={`Session ${index + 1} - ${session.scenario_id || 'Scénario inconnu'}`} secondary={ <Box sx={{ display: 'flex', gap: 1, mt: 1 }}> <Chip label={`Score: ${Math.round(session.performance_metrics?.overall_score || 0)}%`} size="small" color={getSkillColor(session.performance_metrics?.overall_score || 0)} /> <Chip label={`Temps: ${Math.round(session.performance_metrics?.resolution_time || 0)}min`} size="small" variant="outlined" /> <Chip label={`Satisfaction: ${session.performance_metrics?.customer_satisfaction || 0}/10`} size="small" variant="outlined" /> </Box> } /> </ListItem> ))} </List> {completedSessions.length === 0 && ( <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 2 }}> Aucune session complétée </Typography> )} </CardContent> </Card> </Grid> </Grid> </Box> ); }; export default ProgressTracker;