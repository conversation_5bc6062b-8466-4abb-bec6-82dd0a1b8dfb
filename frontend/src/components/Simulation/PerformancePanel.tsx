/** * ============================================= * [ANALYTICS] PERFORMANCE PANEL COMPONENT * Performance metrics and leaderboard display * ============================================= */ import React from 'react'; import { Box, Typography, Card, CardContent, Grid, List, ListItem, ListItemText, ListItemAvatar, Avatar, Chip, LinearProgress } from '@mui/material'; import { EmojiEvents as TrophyIcon, TrendingUp as TrendIcon, Person as PersonIcon } from '@mui/icons-material'; interface PerformancePanelProps { leaderboard: any[]; currentAgent: any; } const PerformancePanel: React.FC<PerformancePanelProps> = ({ leaderboard, currentAgent }) => { return ( <Box> <Typography variant="h6" sx={{ mb: 3 }}> Performance et Classement </Typography> <Grid container spacing={3}> <Grid item xs={12} md={6}> <Card> <CardContent> <Typography variant="h6" sx={{ mb: 2 }}> <TrophyIcon sx={{ mr: 1, verticalAlign: 'middle' }} /> Classement Global </Typography> <List> {[1, 2, 3, 4, 5].map((rank) => ( <ListItem key={rank}> <ListItemAvatar> <Avatar sx={{ bgcolor: rank <= 3 ? 'gold' : 'grey.300' }}> {rank} </Avatar> </ListItemAvatar> <ListItemText primary={`Agent ${rank}`} secondary={`Score: ${95 - rank * 5}% • ${20 - rank} sessions`} /> <Chip label={rank <= 3 ? 'Top 3' : 'Actif'} color={rank <= 3 ? 'primary' : 'default'} size="small" /> </ListItem> ))} </List> </CardContent> </Card> </Grid> <Grid item xs={12} md={6}> <Card> <CardContent> <Typography variant="h6" sx={{ mb: 2 }}> <TrendIcon sx={{ mr: 1, verticalAlign: 'middle' }} /> Votre Progression </Typography> <Box sx={{ mb: 3 }}> <Typography variant="body2" sx={{ mb: 1 }}> Score Moyen: {currentAgent.average_score}% </Typography> <LinearProgress variant="determinate" value={currentAgent.average_score} sx={{ height: 8, borderRadius: 4 }} /> </Box> <Grid container spacing={2}> <Grid item xs={6}> <Typography variant="body2" color="text.secondary"> Sessions Complétées </Typography> <Typography variant="h6"> {currentAgent.completed_sessions} </Typography> </Grid> <Grid item xs={6}> <Typography variant="body2" color="text.secondary"> Badges Obtenus </Typography> <Typography variant="h6"> {currentAgent.badges_earned.length} </Typography> </Grid> </Grid> </CardContent> </Card> </Grid> </Grid> </Box> ); }; export default PerformancePanel;