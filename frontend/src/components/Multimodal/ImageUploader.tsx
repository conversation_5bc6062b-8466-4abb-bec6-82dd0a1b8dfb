/** * ============================================= * IMAGE UPLOADER COMPONENT * Drag-and-drop image upload with preview and validation * Format validation, size limits, and processing status * ============================================= */ import React, { useState, useCallback, useRef } from 'react'; import { useDispatch, useSelector } from 'react-redux'; import { Box, Button, Typography, Alert, Chip, IconButton, Tooltip, Card, CardContent, LinearProgress, Paper } from '@mui/material'; import { CloudUpload as UploadIcon, Image as ImageIcon, Delete as DeleteIcon, ZoomIn as ZoomIcon, Info as InfoIcon, CheckCircle as CheckIcon, Error as ErrorIcon } from '@mui/icons-material'; import { RootState } from '../../store'; import { setImageFile, setError } from '../../store/slices/multimodalSlice'; interface ImageUploaderProps { maxSize?: number; // in bytes acceptedFormats?: string[]; onImageSelect?: (file: File) => void; } const ImageUploader: React.FC<ImageUploaderProps> = ({ maxSize = 50 * 1024 * 1024, // 50MB default acceptedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'], onImageSelect }) => { const dispatch = useDispatch(); const { imageInput, isProcessing } = useSelector((state: RootState) => state.multimodal); const [isDragOver, setIsDragOver] = useState(false); const [uploadProgress, setUploadProgress] = useState(0); const [validationErrors, setValidationErrors] = useState<string[]>([]); const [showPreview, setShowPreview] = useState(false); const fileInputRef = useRef<HTMLInputElement>(null); // Validate file const validateFile = (file: File): string[] => { const errors: string[] = []; // Check file type if (!acceptedFormats.includes(file.type)) { errors.push(`Format non supporté. Formats acceptés: ${acceptedFormats.map(f => f.split('/')[1].toUpperCase()).join(', ')}`); } // Check file size if (file.size > maxSize) { const maxSizeMB = Math.round(maxSize / (1024 * 1024)); const fileSizeMB = Math.round(file.size / (1024 * 1024)); errors.push(`Fichier trop volumineux (${fileSizeMB}MB). Taille maximale: ${maxSizeMB}MB`); } // Check if file is actually an image if (!file.type.startsWith('image/')) { errors.push('Le fichier doit être une image'); } return errors; }; // Get image metadata const getImageMetadata = (file: File): Promise<any> => { return new Promise((resolve) => { const img = new Image(); img.onload = () => { resolve({ width: img.width, height: img.height, size: file.size, format: file.type, quality: Math.min(100, Math.max(0, 100 - (file.size / (img.width * img.height * 3)) * 100)) }); }; img.onerror = () => { resolve({ width: 0, height: 0, size: file.size, format: file.type, quality: 0 }); }; img.src = URL.createObjectURL(file); }); }; // Handle file selection const handleFileSelect = useCallback(async (file: File) => { setUploadProgress(0); setValidationErrors([]); // Validate file const errors = validateFile(file); if (errors.length > 0) { setValidationErrors(errors); dispatch(setError(errors.join('. '))); return; } try { // Simulate upload progress const progressInterval = setInterval(() => { setUploadProgress(prev => { if (prev >= 90) { clearInterval(progressInterval); return 90; } return prev + 10; }); }, 100); // Get image metadata const metadata = await getImageMetadata(file); // Create preview URL const preview = URL.createObjectURL(file); // Complete upload setUploadProgress(100); clearInterval(progressInterval); // Update Redux state dispatch(setImageFile({ file, preview, metadata })); onImageSelect?.(file); // Clear progress after a short delay setTimeout(() => setUploadProgress(0), 1000); } catch (error) { console.error('Error processing image:', error); dispatch(setError('Erreur lors du traitement de l\'image')); setUploadProgress(0); } }, [dispatch, maxSize, acceptedFormats, onImageSelect]); // Handle drag events const handleDragOver = useCallback((e: React.DragEvent) => { e.preventDefault(); setIsDragOver(true); }, []); const handleDragLeave = useCallback((e: React.DragEvent) => { e.preventDefault(); setIsDragOver(false); }, []); const handleDrop = useCallback((e: React.DragEvent) => { e.preventDefault(); setIsDragOver(false); const files = Array.from(e.dataTransfer.files); if (files.length > 0) { handleFileSelect(files[0]); } }, [handleFileSelect]); // Handle file input change const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => { const files = e.target.files; if (files && files.length > 0) { handleFileSelect(files[0]); } }, [handleFileSelect]); // Open file dialog const openFileDialog = () => { fileInputRef.current?.click(); }; // Remove image const removeImage = () => { if (imageInput.preview) { URL.revokeObjectURL(imageInput.preview); } dispatch(setImageFile({ file: null, preview: null, metadata: null })); setValidationErrors([]); setUploadProgress(0); }; // Format file size const formatFileSize = (bytes: number): string => { if (bytes === 0) return '0 B'; const k = 1024; const sizes = ['B', 'KB', 'MB', 'GB']; const i = Math.floor(Math.log(bytes) / Math.log(k)); return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]; }; // Get image quality color const getQualityColor = (quality: number) => { if (quality >= 80) return 'success'; if (quality >= 60) return 'info'; if (quality >= 40) return 'warning'; return 'error'; }; return ( <Box> {/* Validation Errors */} {validationErrors.length > 0 && ( <Alert severity="error" sx={{ mb: 2 }}> <ul style={{ margin: 0, paddingLeft: '20px' }}> {validationErrors.map((error, index) => ( <li key={index}>{error}</li> ))} </ul> </Alert> )} {/* Upload Area */} {!imageInput.file && ( <Paper elevation={isDragOver ? 4 : 1} sx={{ p: 4, textAlign: 'center', border: isDragOver ? '2px dashed #1976d2' : '2px dashed #ddd', backgroundColor: isDragOver ? 'action.hover' : 'background.paper', cursor: 'pointer', transition: 'all 0.3s ease', '&:hover': { backgroundColor: 'action.hover', borderColor: 'primary.main' } }} onDragOver={handleDragOver} onDragLeave={handleDragLeave} onDrop={handleDrop} onClick={openFileDialog} > <UploadIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} /> <Typography variant="h6" gutterBottom> Glissez-déposez une image ici </Typography> <Typography variant="body2" color="text.secondary" gutterBottom> ou cliquez pour sélectionner un fichier </Typography> <Typography variant="caption" color="text.secondary"> Formats supportés: JPEG, PNG, WebP, GIF (max {Math.round(maxSize / (1024 * 1024))}MB) </Typography> <input ref={fileInputRef} type="file" accept={acceptedFormats.join(',')} onChange={handleFileInputChange} style={{ display: 'none' }} disabled={isProcessing} /> </Paper> )} {/* Upload Progress */} {uploadProgress > 0 && uploadProgress < 100 && ( <Box mt={2}> <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}> <Typography variant="body2">Téléchargement...</Typography> <Typography variant="body2">{uploadProgress}%</Typography> </Box> <LinearProgress variant="determinate" value={uploadProgress} /> </Box> )} {/* Image Preview */} {imageInput.file && imageInput.preview && ( <Card variant="outlined" sx={{ mt: 2 }}> <CardContent> {/* Image Display */} <Box position="relative" mb={2}> <img src={imageInput.preview} alt="Preview" style={{ width: '100%', maxHeight: '300px', objectFit: 'contain', borderRadius: '4px' }} /> <Box position="absolute" top={8} right={8} display="flex" gap={1} > <Tooltip title="Agrandir"> <IconButton size="small" sx={{ backgroundColor: 'rgba(0,0,0,0.5)', color: 'white' }} onClick={() => setShowPreview(true)} > <ZoomIcon /> </IconButton> </Tooltip> <Tooltip title="Supprimer"> <IconButton size="small" sx={{ backgroundColor: 'rgba(0,0,0,0.5)', color: 'white' }} onClick={removeImage} > <DeleteIcon /> </IconButton> </Tooltip> </Box> </Box> {/* Image Information */} <Box display="flex" flexWrap="wrap" gap={1} mb={2}> <Chip icon={<ImageIcon />} label={imageInput.file.name} variant="outlined" size="small" /> <Chip label={formatFileSize(imageInput.file.size)} variant="outlined" size="small" /> {imageInput.metadata && ( <> <Chip label={`${imageInput.metadata.width}×${imageInput.metadata.height}`} variant="outlined" size="small" /> <Chip icon={imageInput.metadata.quality >= 70 ? <CheckIcon /> : <ErrorIcon />} label={`Qualité: ${Math.round(imageInput.metadata.quality)}%`} color={getQualityColor(imageInput.metadata.quality)} variant="outlined" size="small" /> </> )} </Box> {/* Image Quality Assessment */} {imageInput.metadata && ( <Box> <Typography variant="body2" color="text.secondary" gutterBottom> Évaluation de l'image </Typography> <Box display="flex" alignItems="center" gap={1} mb={1}> <Typography variant="caption">Qualité:</Typography> <LinearProgress variant="determinate" value={imageInput.metadata.quality} color={getQualityColor(imageInput.metadata.quality) as any} sx={{ flexGrow: 1, height: 6, borderRadius: 3 }} /> <Typography variant="caption"> {Math.round(imageInput.metadata.quality)}% </Typography> </Box> {/* Quality Recommendations */} {imageInput.metadata.quality < 70 && ( <Alert severity="info" sx={{ mt: 1 }}> <Typography variant="caption"> [FEATURE] Pour de meilleurs résultats d'analyse, utilisez une image de meilleure qualité avec une résolution plus élevée et moins de compression. </Typography> </Alert> )} </Box> )} {/* Action Buttons */} <Box mt={2} display="flex" gap={2}> <Button variant="outlined" startIcon={<UploadIcon />} onClick={openFileDialog} disabled={isProcessing} > Changer d'image </Button> <Button variant="outlined" color="error" startIcon={<DeleteIcon />} onClick={removeImage} disabled={isProcessing} > Supprimer </Button> </Box> </CardContent> </Card> )} {/* Processing Tips */} <Box mt={2}> <Typography variant="body2" color="text.secondary"> [FEATURE] <strong>Conseils :</strong> Utilisez des images nettes et bien éclairées. Les images contenant du texte, des produits Free Mobile, ou des visages donneront de meilleurs résultats d'analyse. </Typography> </Box> {/* Hidden file input */} <input ref={fileInputRef} type="file" accept={acceptedFormats.join(',')} onChange={handleFileInputChange} style={{ display: 'none' }} disabled={isProcessing} /> {/* Full-size preview modal would go here */} {/* This would typically be implemented with a Material-UI Dialog */} </Box> ); }; export default ImageUploader;