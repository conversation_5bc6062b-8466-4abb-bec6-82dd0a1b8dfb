/** * ============================================= * VOICE RECORDER COMPONENT * Real-time voice recording with waveform visualization * Audio quality indicators and recording controls * ============================================= */ import React, { useState, useEffect, useRef, useCallback } from 'react'; import { useDispatch, useSelector } from 'react-redux'; import { Box, Button, Typography, LinearProgress, Alert, Chip, IconButton, Tooltip, Card, CardContent } from '@mui/material'; import { Mic as MicIcon, MicOff as MicOffIcon, Stop as StopIcon, PlayArrow as PlayIcon, Pause as PauseIcon, Delete as DeleteIcon, VolumeUp as VolumeIcon, GraphicEq as WaveformIcon } from '@mui/icons-material'; import { RootState } from '../../store'; import { setVoiceRecording, updateVoiceRecording, setVoiceBlob, setError } from '../../store/slices/multimodalSlice'; interface VoiceRecorderProps { maxDuration?: number; // in seconds onRecordingComplete?: (audioBlob: Blob) => void; } const VoiceRecorder: React.FC<VoiceRecorderProps> = ({ maxDuration = 300, // 5 minutes default onRecordingComplete }) => { const dispatch = useDispatch(); const { voiceInput, isProcessing } = useSelector((state: RootState) => state.multimodal); // Recording state const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null); const [audioStream, setAudioStream] = useState<MediaStream | null>(null); const [audioContext, setAudioContext] = useState<AudioContext | null>(null); const [analyser, setAnalyser] = useState<AnalyserNode | null>(null); const [isPlaying, setIsPlaying] = useState(false); const [audioUrl, setAudioUrl] = useState<string | null>(null); const [permissionStatus, setPermissionStatus] = useState<'granted' | 'denied' | 'prompt'>('prompt'); // Refs const canvasRef = useRef<HTMLCanvasElement>(null); const audioRef = useRef<HTMLAudioElement>(null); const animationFrameRef = useRef<number>(); const recordingTimerRef = useRef<NodeJS.Timeout>(); // Initialize microphone permissions useEffect(() => { checkMicrophonePermission(); return () => { cleanup(); }; }, []); // Cleanup on unmount const cleanup = useCallback(() => { if (animationFrameRef.current) { cancelAnimationFrame(animationFrameRef.current); } if (recordingTimerRef.current) { clearInterval(recordingTimerRef.current); } if (audioStream) { audioStream.getTracks().forEach(track => track.stop()); } if (audioContext) { audioContext.close(); } if (audioUrl) { URL.revokeObjectURL(audioUrl); } }, [audioStream, audioContext, audioUrl]); // Check microphone permission const checkMicrophonePermission = async () => { try { const permission = await navigator.permissions.query({ name: 'microphone' as PermissionName }); setPermissionStatus(permission.state); permission.addEventListener('change', () => { setPermissionStatus(permission.state); }); } catch (error) { console.warn('Permission API not supported'); } }; // Request microphone access const requestMicrophoneAccess = async (): Promise<MediaStream> => { try { const stream = await navigator.mediaDevices.getUserMedia({ audio: { echoCancellation: true, noiseSuppression: true, autoGainControl: true, sampleRate: 44100 } }); setPermissionStatus('granted'); return stream; } catch (error) { setPermissionStatus('denied'); dispatch(setError('Accès au microphone refusé. Veuillez autoriser l\'accès dans les paramètres du navigateur.')); throw error; } }; // Setup audio analysis const setupAudioAnalysis = (stream: MediaStream) => { const context = new (window.AudioContext || (window as any).webkitAudioContext)(); const analyserNode = context.createAnalyser(); const source = context.createMediaStreamSource(stream); analyserNode.fftSize = 256; analyserNode.smoothingTimeConstant = 0.8; source.connect(analyserNode); setAudioContext(context); setAnalyser(analyserNode); return { context, analyser: analyserNode }; }; // Draw waveform visualization const drawWaveform = useCallback(() => { if (!analyser || !canvasRef.current) return; const canvas = canvasRef.current; const canvasContext = canvas.getContext('2d'); if (!canvasContext) return; const bufferLength = analyser.frequencyBinCount; const dataArray = new Uint8Array(bufferLength); analyser.getByteFrequencyData(dataArray); // Clear canvas canvasContext.fillStyle = '#f5f5f5'; canvasContext.fillRect(0, 0, canvas.width, canvas.height); // Draw waveform const barWidth = (canvas.width / bufferLength) * 2.5; let barHeight; let x = 0; for (let i = 0; i < bufferLength; i++) { barHeight = (dataArray[i] / 255) * canvas.height * 0.8; // Color gradient based on frequency const hue = (i / bufferLength) * 360; canvasContext.fillStyle = `hsl(${hue}, 70%, 50%)`; canvasContext.fillRect(x, canvas.height - barHeight, barWidth, barHeight); x += barWidth + 1; } // Update waveform data for Redux const waveformData = Array.from(dataArray).slice(0, 50); // Reduce data size dispatch(updateVoiceRecording({ waveformData })); // Calculate audio quality metrics const averageVolume = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length; const maxVolume = Math.max(...dataArray); const audioQuality = { score: Math.min(100, (averageVolume / 128) * 100), rating: (averageVolume > 100 ? 'excellent' : averageVolume > 60 ? 'good' : averageVolume > 30 ? 'fair' : 'poor') as 'excellent' | 'good' | 'fair' | 'poor', issues: [ ...(averageVolume < 30 ? ['Volume trop faible'] : []), ...(maxVolume > 240 ? ['Saturation détectée'] : []), ...(averageVolume < 10 ? ['Aucun signal audio'] : []) ], recommendations: [ ...(averageVolume < 30 ? ['Rapprochez-vous du microphone'] : []), ...(maxVolume > 240 ? ['Réduisez le volume d\'entrée'] : []) ] }; dispatch(updateVoiceRecording({ audioQuality })); if (voiceInput.isRecording) { animationFrameRef.current = requestAnimationFrame(drawWaveform); } }, [analyser, voiceInput.isRecording, dispatch]); // Start recording const startRecording = async () => { try { const stream = await requestMicrophoneAccess(); setAudioStream(stream); // Setup audio analysis setupAudioAnalysis(stream); // Create MediaRecorder const recorder = new MediaRecorder(stream, { mimeType: 'audio/webm;codecs=opus' }); const audioChunks: Blob[] = []; recorder.ondataavailable = (event) => { if (event.data.size > 0) { audioChunks.push(event.data); } }; recorder.onstop = () => { const audioBlob = new Blob(audioChunks, { type: 'audio/webm' }); dispatch(setVoiceBlob(audioBlob)); // Create audio URL for playback const url = URL.createObjectURL(audioBlob); setAudioUrl(url); onRecordingComplete?.(audioBlob); }; recorder.start(100); // Collect data every 100ms setMediaRecorder(recorder); dispatch(setVoiceRecording(true)); // Start waveform visualization drawWaveform(); // Start duration timer let duration = 0; recordingTimerRef.current = setInterval(() => { duration += 1; dispatch(updateVoiceRecording({ duration })); // Auto-stop at max duration if (duration >= maxDuration) { stopRecording(); } }, 1000); } catch (error) { console.error('Failed to start recording:', error); } }; // Stop recording const stopRecording = () => { if (mediaRecorder && mediaRecorder.state === 'recording') { mediaRecorder.stop(); } if (audioStream) { audioStream.getTracks().forEach(track => track.stop()); setAudioStream(null); } if (recordingTimerRef.current) { clearInterval(recordingTimerRef.current); } if (animationFrameRef.current) { cancelAnimationFrame(animationFrameRef.current); } dispatch(setVoiceRecording(false)); setMediaRecorder(null); }; // Play recorded audio const playRecording = () => { if (audioRef.current && audioUrl) { if (isPlaying) { audioRef.current.pause(); setIsPlaying(false); } else { audioRef.current.play(); setIsPlaying(true); } } }; // Delete recording const deleteRecording = () => { dispatch(setVoiceBlob(null)); dispatch(updateVoiceRecording({ duration: 0, waveformData: [] })); if (audioUrl) { URL.revokeObjectURL(audioUrl); setAudioUrl(null); } setIsPlaying(false); }; // Format duration const formatDuration = (seconds: number): string => { const mins = Math.floor(seconds / 60); const secs = seconds % 60; return `${mins}:${secs.toString().padStart(2, '0')}`; }; // Get quality color const getQualityColor = (rating: string) => { switch (rating) { case 'excellent': return 'success'; case 'good': return 'info'; case 'fair': return 'warning'; case 'poor': return 'error'; default: return 'default'; } }; return ( <Box> {/* Permission Alert */} {permissionStatus === 'denied' && ( <Alert severity="error" sx={{ mb: 2 }}> Accès au microphone refusé. Veuillez autoriser l'accès dans les paramètres de votre navigateur. </Alert> )} {/* Recording Interface */} <Card variant="outlined"> <CardContent> {/* Waveform Visualization */} <Box mb={2} textAlign="center"> <canvas ref={canvasRef} width={400} height={100} style={{ width: '100%', maxWidth: '400px', height: '100px', border: '1px solid #ddd', borderRadius: '4px', backgroundColor: '#f5f5f5' }} /> </Box> {/* Recording Status */} <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}> <Box display="flex" alignItems="center" gap={1}> {voiceInput.isRecording && ( <Chip icon={<MicIcon />} label="Enregistrement..." color="error" variant="filled" size="small" /> )} {voiceInput.audioBlob && !voiceInput.isRecording && ( <Chip icon={<VolumeIcon />} label="Enregistrement prêt" color="success" variant="filled" size="small" /> )} </Box> <Typography variant="body2" color="text.secondary"> {formatDuration(voiceInput.duration)} / {formatDuration(maxDuration)} </Typography> </Box> {/* Audio Quality Indicator */} {voiceInput.audioQuality && ( <Box mb={2}> <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}> <Typography variant="body2">Qualité audio</Typography> <Chip label={voiceInput.audioQuality.rating} color={getQualityColor(voiceInput.audioQuality.rating)} size="small" /> </Box> <LinearProgress variant="determinate" value={voiceInput.audioQuality.score} color={getQualityColor(voiceInput.audioQuality.rating) as any} sx={{ height: 6, borderRadius: 3 }} /> {voiceInput.audioQuality.issues.length > 0 && ( <Typography variant="caption" color="warning.main" display="block" mt={1}> {voiceInput.audioQuality.issues.join(', ')} </Typography> )} </Box> )} {/* Control Buttons */} <Box display="flex" justifyContent="center" gap={2}> {!voiceInput.isRecording && !voiceInput.audioBlob && ( <Button variant="contained" startIcon={<MicIcon />} onClick={startRecording} disabled={permissionStatus === 'denied' || isProcessing} color="primary" size="large" > Commencer l'enregistrement </Button> )} {voiceInput.isRecording && ( <Button variant="contained" startIcon={<StopIcon />} onClick={stopRecording} color="error" size="large" > Arrêter </Button> )} {voiceInput.audioBlob && !voiceInput.isRecording && ( <> <Tooltip title={isPlaying ? 'Pause' : 'Écouter'}> <IconButton onClick={playRecording} color="primary" size="large" > {isPlaying ? <PauseIcon /> : <PlayIcon />} </IconButton> </Tooltip> <Tooltip title="Supprimer"> <IconButton onClick={deleteRecording} color="error" size="large" > <DeleteIcon /> </IconButton> </Tooltip> <Button variant="outlined" startIcon={<MicIcon />} onClick={startRecording} disabled={isProcessing} > Réenregistrer </Button> </> )} </Box> {/* Hidden audio element for playback */} {audioUrl && ( <audio ref={audioRef} src={audioUrl} onEnded={() => setIsPlaying(false)} style={{ display: 'none' }} /> )} </CardContent> </Card> {/* Recording Tips */} <Box mt={2}> <Typography variant="body2" color="text.secondary"> [FEATURE] <strong>Conseils :</strong> Parlez clairement, évitez les bruits de fond, et maintenez une distance constante du microphone pour une meilleure qualité. </Typography> </Box> </Box> ); }; export default VoiceRecorder;