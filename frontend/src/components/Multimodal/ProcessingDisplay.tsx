/** * ============================================= * [ANALYTICS] PROCESSING DISPLAY COMPONENT * Real-time analysis results with confidence indicators * Displays text, voice, image, and multimodal fusion results * ============================================= */ import React, { useState } from 'react'; import { useSelector, useDispatch } from 'react-redux'; import { Box, Card, CardContent, Typography, Accordion, AccordionSummary, AccordionDetails, Chip, LinearProgress, Grid, Alert, Divider, IconButton, Tooltip, Badge } from '@mui/material'; import { ExpandMore as ExpandMoreIcon, TextFields as TextIcon, Mic as VoiceIcon, Image as ImageIcon, AutoAwesome as FusionIcon, TrendingUp as TrendingUpIcon, TrendingDown as TrendingDownIcon, TrendingFlat as TrendingFlatIcon, Psychology as IntentIcon, Mood as EmotionIcon, Speed as UrgencyIcon, Star as ConfidenceIcon, Lightbulb as InsightIcon, Warning as WarningIcon, CheckCircle as CheckIcon, Info as InfoIcon } from '@mui/icons-material'; import { RootState } from '../../store'; import { toggleExpandedSection } from '../../store/slices/multimodalSlice'; const ProcessingDisplay: React.FC = () => { const dispatch = useDispatch(); const { textAnalysis, voiceAnalysis, imageAnalysis, fusionResult, expandedSections, processingHistory } = useSelector((state: RootState) => state.multimodal); // Get confidence color const getConfidenceColor = (confidence: number) => { if (confidence >= 0.8) return 'success'; if (confidence >= 0.6) return 'info'; if (confidence >= 0.4) return 'warning'; return 'error'; }; // Get sentiment color const getSentimentColor = (sentiment: string) => { switch (sentiment.toLowerCase()) { case 'positive': return 'success'; case 'negative': return 'error'; case 'neutral': return 'info'; default: return 'default'; } }; // Get urgency color const getUrgencyColor = (urgency: string) => { switch (urgency.toLowerCase()) { case 'urgent': case 'high': return 'error'; case 'medium': return 'warning'; case 'low': return 'success'; default: return 'default'; } }; // Format processing time const formatProcessingTime = (ms: number) => { if (ms < 1000) return `${ms}ms`; return `${(ms / 1000).toFixed(1)}s`; }; // Check if section is expanded const isSectionExpanded = (section: string) => { return expandedSections.includes(section); }; // Toggle section expansion const toggleSection = (section: string) => { dispatch(toggleExpandedSection(section)); }; // Render confidence indicator const renderConfidenceIndicator = (confidence: number, label: string = 'Confiance') => ( <Box display="flex" alignItems="center" gap={1}> <Typography variant="caption" color="text.secondary"> {label}: </Typography> <LinearProgress variant="determinate" value={confidence * 100} color={getConfidenceColor(confidence) as any} sx={{ width: 60, height: 6, borderRadius: 3 }} /> <Typography variant="caption" color="text.secondary"> {Math.round(confidence * 100)}% </Typography> </Box> ); // Render text analysis results const renderTextAnalysis = () => { if (!textAnalysis) return null; return ( <Accordion expanded={isSectionExpanded('text')} onChange={() => toggleSection('text')} > <AccordionSummary expandIcon={<ExpandMoreIcon />}> <Box display="flex" alignItems="center" gap={2} width="100%"> <TextIcon color="primary" /> <Typography variant="h6">Analyse Textuelle</Typography> <Box ml="auto" display="flex" gap={1}> {renderConfidenceIndicator(textAnalysis.confidence)} <Chip label={formatProcessingTime(textAnalysis.processingTime)} size="small" variant="outlined" /> </Box> </Box> </AccordionSummary> <AccordionDetails> <Grid container spacing={3}> {/* Intent */} <Grid item xs={12} md={6}> <Card variant="outlined"> <CardContent> <Box display="flex" alignItems="center" gap={1} mb={2}> <IntentIcon color="primary" /> <Typography variant="subtitle1">Intention</Typography> </Box> <Chip label={textAnalysis.intent.primary} color="primary" sx={{ mb: 1 }} /> {renderConfidenceIndicator(textAnalysis.intent.confidence)} {textAnalysis.intent.alternatives.length > 0 && ( <Box mt={2}> <Typography variant="caption" color="text.secondary"> Alternatives: </Typography> <Box display="flex" gap={1} mt={1} flexWrap="wrap"> {textAnalysis.intent.alternatives.map((alt, index) => ( <Chip key={index} label={`${alt.intent} (${Math.round(alt.confidence * 100)}%)`} size="small" variant="outlined" /> ))} </Box> </Box> )} </CardContent> </Card> </Grid> {/* Sentiment */} <Grid item xs={12} md={6}> <Card variant="outlined"> <CardContent> <Box display="flex" alignItems="center" gap={1} mb={2}> <EmotionIcon color="primary" /> <Typography variant="subtitle1">Sentiment</Typography> </Box> <Chip label={textAnalysis.sentiment.label} color={getSentimentColor(textAnalysis.sentiment.label)} sx={{ mb: 1 }} /> {renderConfidenceIndicator(textAnalysis.sentiment.confidence)} <Typography variant="body2" color="text.secondary" mt={1}> Score: {textAnalysis.sentiment.score.toFixed(2)} </Typography> </CardContent> </Card> </Grid> {/* Emotion */} <Grid item xs={12} md={6}> <Card variant="outlined"> <CardContent> <Box display="flex" alignItems="center" gap={1} mb={2}> <EmotionIcon color="secondary" /> <Typography variant="subtitle1">Émotion</Typography> </Box> <Chip label={textAnalysis.emotion.dominant} color="secondary" sx={{ mb: 1 }} /> {renderConfidenceIndicator(textAnalysis.emotion.confidence)} {Object.keys(textAnalysis.emotion.emotions).length > 0 && ( <Box mt={2}> <Typography variant="caption" color="text.secondary"> Détail des émotions: </Typography> {Object.entries(textAnalysis.emotion.emotions).map(([emotion, score]) => ( <Box key={emotion} display="flex" alignItems="center" gap={1} mt={1}> <Typography variant="caption" sx={{ minWidth: 60 }}> {emotion}: </Typography> <LinearProgress variant="determinate" value={score * 100} sx={{ flexGrow: 1, height: 4 }} /> <Typography variant="caption"> {Math.round(score * 100)}% </Typography> </Box> ))} </Box> )} </CardContent> </Card> </Grid> {/* Urgency */} <Grid item xs={12} md={6}> <Card variant="outlined"> <CardContent> <Box display="flex" alignItems="center" gap={1} mb={2}> <UrgencyIcon color="warning" /> <Typography variant="subtitle1">Urgence</Typography> </Box> <Chip label={textAnalysis.urgency.level} color={getUrgencyColor(textAnalysis.urgency.level)} sx={{ mb: 1 }} /> <Typography variant="body2" color="text.secondary"> Score: {textAnalysis.urgency.score.toFixed(2)} </Typography> </CardContent> </Card> </Grid> {/* Entities */} {Object.keys(textAnalysis.entities).length > 0 && ( <Grid item xs={12}> <Card variant="outlined"> <CardContent> <Typography variant="subtitle1" gutterBottom> Entités détectées </Typography> <Box display="flex" gap={1} flexWrap="wrap"> {Object.entries(textAnalysis.entities).map(([type, values]) => ( values.map((value, index) => ( <Chip key={`${type}-${index}`} label={`${type}: ${value}`} size="small" variant="outlined" /> )) ))} </Box> </CardContent> </Card> </Grid> )} {/* Keywords */} {textAnalysis.keywords.length > 0 && ( <Grid item xs={12}> <Card variant="outlined"> <CardContent> <Typography variant="subtitle1" gutterBottom> Mots-clés </Typography> <Box display="flex" gap={1} flexWrap="wrap"> {textAnalysis.keywords.map((keyword, index) => ( <Chip key={index} label={keyword} size="small" color="primary" variant="outlined" /> ))} </Box> </CardContent> </Card> </Grid> )} </Grid> </AccordionDetails> </Accordion> ); }; // Render voice analysis results const renderVoiceAnalysis = () => { if (!voiceAnalysis) return null; return ( <Accordion expanded={isSectionExpanded('voice')} onChange={() => toggleSection('voice')} > <AccordionSummary expandIcon={<ExpandMoreIcon />}> <Box display="flex" alignItems="center" gap={2} width="100%"> <VoiceIcon color="secondary" /> <Typography variant="h6">Analyse Vocale</Typography> <Box ml="auto" display="flex" gap={1}> {renderConfidenceIndicator(voiceAnalysis.confidence)} <Chip label={formatProcessingTime(voiceAnalysis.processingTime)} size="small" variant="outlined" /> </Box> </Box> </AccordionSummary> <AccordionDetails> <Grid container spacing={3}> {/* Transcription */} <Grid item xs={12}> <Card variant="outlined"> <CardContent> <Typography variant="subtitle1" gutterBottom> Transcription </Typography> <Typography variant="body1" sx={{ fontStyle: 'italic', mb: 2 }}> "{voiceAnalysis.transcription.text}" </Typography> {renderConfidenceIndicator(voiceAnalysis.transcription.confidence)} </CardContent> </Card> </Grid> {/* Voice Emotion */} <Grid item xs={12} md={6}> <Card variant="outlined"> <CardContent> <Typography variant="subtitle1" gutterBottom> Émotion vocale </Typography> <Chip label={voiceAnalysis.emotion.dominant} color="secondary" sx={{ mb: 1 }} /> {renderConfidenceIndicator(voiceAnalysis.emotion.confidence)} {voiceAnalysis.emotion.audioCharacteristics && ( <Box mt={2}> <Typography variant="caption" color="text.secondary"> Caractéristiques audio: </Typography> <Typography variant="body2"> Pitch: {voiceAnalysis.emotion.audioCharacteristics.pitch}Hz </Typography> <Typography variant="body2"> Énergie: {Math.round(voiceAnalysis.emotion.audioCharacteristics.energy * 100)}% </Typography> <Typography variant="body2"> Tempo: {voiceAnalysis.emotion.audioCharacteristics.tempo}x </Typography> </Box> )} </CardContent> </Card> </Grid> {/* Audio Quality */} <Grid item xs={12} md={6}> <Card variant="outlined"> <CardContent> <Typography variant="subtitle1" gutterBottom> Qualité audio </Typography> <Chip label={voiceAnalysis.audioQuality.rating} color={getConfidenceColor(voiceAnalysis.audioQuality.score / 100)} sx={{ mb: 1 }} /> <LinearProgress variant="determinate" value={voiceAnalysis.audioQuality.score} color={getConfidenceColor(voiceAnalysis.audioQuality.score / 100) as any} sx={{ mb: 1, height: 6, borderRadius: 3 }} /> <Typography variant="body2"> Score: {voiceAnalysis.audioQuality.score}/100 </Typography> {voiceAnalysis.audioQuality.issues.length > 0 && ( <Alert severity="warning" sx={{ mt: 2 }}> <Typography variant="caption"> Problèmes détectés: {voiceAnalysis.audioQuality.issues.join(', ')} </Typography> </Alert> )} </CardContent> </Card> </Grid> {/* Voice Activity */} <Grid item xs={12}> <Card variant="outlined"> <CardContent> <Typography variant="subtitle1" gutterBottom> Activité vocale </Typography> <Box display="flex" alignItems="center" gap={2}> <Chip icon={voiceAnalysis.voiceActivity.hasVoice ? <CheckIcon /> : <WarningIcon />} label={voiceAnalysis.voiceActivity.hasVoice ? 'Voix détectée' : 'Aucune voix'} color={voiceAnalysis.voiceActivity.hasVoice ? 'success' : 'warning'} /> <Typography variant="body2"> {Math.round(voiceAnalysis.voiceActivity.voicePercentage)}% de contenu vocal </Typography> </Box> {renderConfidenceIndicator(voiceAnalysis.voiceActivity.confidence)} </CardContent> </Card> </Grid> </Grid> </AccordionDetails> </Accordion> ); }; // Render image analysis results const renderImageAnalysis = () => { if (!imageAnalysis) return null; return ( <Accordion expanded={isSectionExpanded('image')} onChange={() => toggleSection('image')} > <AccordionSummary expandIcon={<ExpandMoreIcon />}> <Box display="flex" alignItems="center" gap={2} width="100%"> <ImageIcon color="info" /> <Typography variant="h6">Analyse d'Image</Typography> <Box ml="auto" display="flex" gap={1}> {renderConfidenceIndicator(imageAnalysis.confidence)} <Chip label={formatProcessingTime(imageAnalysis.processingTime)} size="small" variant="outlined" /> </Box> </Box> </AccordionSummary> <AccordionDetails> <Grid container spacing={3}> {/* OCR Results */} {imageAnalysis.ocr.text && ( <Grid item xs={12}> <Card variant="outlined"> <CardContent> <Typography variant="subtitle1" gutterBottom> Texte détecté (OCR) </Typography> <Typography variant="body1" sx={{ fontStyle: 'italic', mb: 2 }}> "{imageAnalysis.ocr.text}" </Typography> {renderConfidenceIndicator(imageAnalysis.ocr.confidence)} </CardContent> </Card> </Grid> )} {/* Detected Objects */} {imageAnalysis.objects.objects.length > 0 && ( <Grid item xs={12} md={6}> <Card variant="outlined"> <CardContent> <Typography variant="subtitle1" gutterBottom> Objets détectés </Typography> <Box display="flex" gap={1} flexWrap="wrap"> {imageAnalysis.objects.objects.map((obj, index) => ( <Chip key={index} label={`${obj.name} (${Math.round(obj.confidence * 100)}%)`} size="small" color="info" variant="outlined" /> ))} </Box> </CardContent> </Card> </Grid> )} {/* Face Analysis */} {imageAnalysis.faces.totalFaces > 0 && ( <Grid item xs={12} md={6}> <Card variant="outlined"> <CardContent> <Typography variant="subtitle1" gutterBottom> Analyse faciale </Typography> <Typography variant="body2" gutterBottom> {imageAnalysis.faces.totalFaces} visage(s) détecté(s) </Typography> {imageAnalysis.faces.faces.map((face, index) => ( <Box key={index} mb={2}> <Chip label={`Visage ${index + 1}: ${face.dominantEmotion}`} color="secondary" size="small" sx={{ mb: 1 }} /> {renderConfidenceIndicator(face.confidence)} </Box> ))} </CardContent> </Card> </Grid> )} {/* Products */} {imageAnalysis.products.products.length > 0 && ( <Grid item xs={12}> <Card variant="outlined"> <CardContent> <Typography variant="subtitle1" gutterBottom> Produits Free Mobile détectés </Typography> <Box display="flex" gap={1} flexWrap="wrap"> {imageAnalysis.products.products.map((product, index) => ( <Chip key={index} label={`${product.name} (${product.category})`} color="success" variant="outlined" /> ))} </Box> </CardContent> </Card> </Grid> )} {/* Image Quality */} <Grid item xs={12}> <Card variant="outlined"> <CardContent> <Typography variant="subtitle1" gutterBottom> Qualité de l'image </Typography> <Chip label={imageAnalysis.quality.rating} color={getConfidenceColor(imageAnalysis.quality.score / 100)} sx={{ mb: 1 }} /> <LinearProgress variant="determinate" value={imageAnalysis.quality.score} color={getConfidenceColor(imageAnalysis.quality.score / 100) as any} sx={{ mb: 1, height: 6, borderRadius: 3 }} /> <Typography variant="body2"> Score: {imageAnalysis.quality.score}/100 </Typography> {imageAnalysis.quality.issues.length > 0 && ( <Alert severity="info" sx={{ mt: 2 }}> <Typography variant="caption"> Recommandations: {imageAnalysis.quality.recommendations.join(', ')} </Typography> </Alert> )} </CardContent> </Card> </Grid> </Grid> </AccordionDetails> </Accordion> ); }; // Render fusion results const renderFusionResults = () => { if (!fusionResult) return null; return ( <Accordion expanded={isSectionExpanded('fusion')} onChange={() => toggleSection('fusion')} > <AccordionSummary expandIcon={<ExpandMoreIcon />}> <Box display="flex" alignItems="center" gap={2} width="100%"> <FusionIcon color="warning" /> <Typography variant="h6">Analyse Multimodale</Typography> <Box ml="auto" display="flex" gap={1}> {renderConfidenceIndicator(fusionResult.confidence)} <Chip label={formatProcessingTime(fusionResult.processingTime)} size="small" variant="outlined" /> </Box> </Box> </AccordionSummary> <AccordionDetails> <Grid container spacing={3}> {/* Context */} <Grid item xs={12}> <Alert severity="info" icon={<InfoIcon />}> <Typography variant="body2"> <strong>Contexte détecté:</strong> {fusionResult.context} </Typography> </Alert> </Grid> {/* Fused Results */} <Grid item xs={12} md={4}> <Card variant="outlined"> <CardContent> <Typography variant="subtitle1" gutterBottom> Intention fusionnée </Typography> <Chip label={fusionResult.intent.primary} color="primary" sx={{ mb: 1 }} /> {renderConfidenceIndicator(fusionResult.intent.confidence)} <Typography variant="caption" color="text.secondary" display="block" mt={1}> Sources: {fusionResult.intent.sources.map(s => s.source).join(', ')} </Typography> </CardContent> </Card> </Grid> <Grid item xs={12} md={4}> <Card variant="outlined"> <CardContent> <Typography variant="subtitle1" gutterBottom> Sentiment fusionné </Typography> <Chip label={fusionResult.sentiment.label} color={getSentimentColor(fusionResult.sentiment.label)} sx={{ mb: 1 }} /> {renderConfidenceIndicator(fusionResult.sentiment.confidence)} <Typography variant="caption" color="text.secondary" display="block" mt={1}> Sources: {fusionResult.sentiment.sources.map(s => s.source).join(', ')} </Typography> </CardContent> </Card> </Grid> <Grid item xs={12} md={4}> <Card variant="outlined"> <CardContent> <Typography variant="subtitle1" gutterBottom> Émotion fusionnée </Typography> <Chip label={fusionResult.emotion.dominant} color="secondary" sx={{ mb: 1 }} /> {renderConfidenceIndicator(fusionResult.emotion.confidence)} <Typography variant="caption" color="text.secondary" display="block" mt={1}> Sources: {fusionResult.emotion.sources.map(s => s.source).join(', ')} </Typography> </CardContent> </Card> </Grid> {/* Key Insights */} {fusionResult.keyInsights.length > 0 && ( <Grid item xs={12}> <Card variant="outlined"> <CardContent> <Box display="flex" alignItems="center" gap={1} mb={2}> <InsightIcon color="warning" /> <Typography variant="subtitle1">Insights clés</Typography> </Box> {fusionResult.keyInsights.map((insight, index) => ( <Alert key={index} severity={insight.actionable ? 'warning' : 'info'} sx={{ mb: 1 }} > <Typography variant="body2"> <strong>{insight.type}:</strong> {insight.insight} </Typography> <Typography variant="caption" color="text.secondary"> Confiance: {Math.round(insight.confidence * 100)}% </Typography> </Alert> ))} </CardContent> </Card> </Grid> )} {/* Recommendations */} {fusionResult.recommendations.length > 0 && ( <Grid item xs={12}> <Card variant="outlined"> <CardContent> <Typography variant="subtitle1" gutterBottom> Recommandations </Typography> {fusionResult.recommendations.map((rec, index) => ( <Alert key={index} severity={rec.priority === 'high' ? 'error' : rec.priority === 'medium' ? 'warning' : 'info'} sx={{ mb: 1 }} > <Typography variant="body2"> <strong>{rec.type}:</strong> {rec.message} </Typography> </Alert> ))} </CardContent> </Card> </Grid> )} {/* Modality Contributions */} <Grid item xs={12}> <Card variant="outlined"> <CardContent> <Typography variant="subtitle1" gutterBottom> Contributions par modalité </Typography> {Object.entries(fusionResult.modalityContributions).map(([modality, contribution]) => ( <Box key={modality} mb={2}> <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}> <Typography variant="body2" sx={{ textTransform: 'capitalize' }}> {modality} </Typography> <Typography variant="caption"> Poids: {Math.round(contribution.weight * 100)}% </Typography> </Box> <LinearProgress variant="determinate" value={contribution.dataQuality * 100} sx={{ mb: 1, height: 6, borderRadius: 3 }} /> <Typography variant="caption" color="text.secondary"> {contribution.keyContributions.join(', ')} </Typography> </Box> ))} </CardContent> </Card> </Grid> </Grid> </AccordionDetails> </Accordion> ); }; return ( <Box> <Typography variant="h5" gutterBottom> Résultats d'analyse </Typography> <Box display="flex" gap={2} mb={3}> {textAnalysis && ( <Badge badgeContent={Math.round(textAnalysis.confidence * 100)} color="primary"> <Chip icon={<TextIcon />} label="Texte" variant="outlined" /> </Badge> )} {voiceAnalysis && ( <Badge badgeContent={Math.round(voiceAnalysis.confidence * 100)} color="secondary"> <Chip icon={<VoiceIcon />} label="Voix" variant="outlined" /> </Badge> )} {imageAnalysis && ( <Badge badgeContent={Math.round(imageAnalysis.confidence * 100)} color="info"> <Chip icon={<ImageIcon />} label="Image" variant="outlined" /> </Badge> )} {fusionResult && ( <Badge badgeContent={Math.round(fusionResult.confidence * 100)} color="warning"> <Chip icon={<FusionIcon />} label="Fusion" variant="outlined" /> </Badge> )} </Box> <Box> {renderTextAnalysis()} {renderVoiceAnalysis()} {renderImageAnalysis()} {renderFusionResults()} </Box> </Box> ); }; export default ProcessingDisplay;