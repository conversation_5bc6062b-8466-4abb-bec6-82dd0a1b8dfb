/** * ============================================= * MULTIMODAL INPUT COMPONENT * Unified input component supporting text, voice recording, and image upload * Real-time processing status and multimodal interaction * ============================================= */ import React, { useState, useEffect, useCallback } from 'react'; import { useAppDispatch, useAppSelector } from '../../hooks/redux'; import { Box, Card, CardContent, Tabs, Tab, Typography, Button, Alert, Chip, LinearProgress, IconButton, Tooltip, Fade, Collapse } from '@mui/material'; import { TextFields as TextIcon, Mic as MicIcon, Image as ImageIcon, AutoAwesome as MultimodalIcon, Send as SendIcon, Clear as ClearIcon, Refresh as RefreshIcon, Settings as SettingsIcon, Info as InfoIcon } from '@mui/icons-material'; import { RootState } from '../../store'; import { setActiveTab, setTextInput, clearResults, clearError, setError, processText, processVoice, processImage, processMultimodal, connectWebSocket } from '../../store/slices/multimodalSlice'; import { multimodalService } from '../../services/multimodal.service'; import VoiceRecorder from './VoiceRecorder'; import ImageUploader from './ImageUploader'; import ProcessingDisplay from './ProcessingDisplay'; interface MultimodalInputProps { className?: string; onResultsChange?: (hasResults: boolean) => void; showAdvancedOptions?: boolean; } const MultimodalInput: React.FC<MultimodalInputProps> = ({ className, onResultsChange, showAdvancedOptions = false }) => { const dispatch = useAppDispatch(); const { activeTab, textInput, voiceInput, imageInput, isProcessing, processingType, processingProgress, isConnected, connectionStatus, error, showResults, textAnalysis, voiceAnalysis, imageAnalysis, fusionResult } = useAppSelector((state: RootState) => state.multimodal); const { user } = useAppSelector((state: RootState) => state.auth); const [textValue, setTextValue] = useState(''); const [showSettings, setShowSettings] = useState(false); const [processingOptions, setProcessingOptions] = useState({ enhancedAnalysis: false, includeRawFeatures: false, language: 'fr' }); // Initialize WebSocket connection useEffect(() => { if (user?.id && !isConnected && connectionStatus !== 'connecting') { dispatch(connectWebSocket(user.id)); } }, [user?.id, isConnected, connectionStatus, dispatch]); // Sync text input with Redux state useEffect(() => { setTextValue(textInput); }, [textInput]); // Notify parent of results changes useEffect(() => { const hasResults = !!(textAnalysis || voiceAnalysis || imageAnalysis || fusionResult); onResultsChange?.(hasResults); }, [textAnalysis, voiceAnalysis, imageAnalysis, fusionResult, onResultsChange]); // Handle tab change const handleTabChange = useCallback((event: React.SyntheticEvent, newValue: string) => { dispatch(setActiveTab(newValue as any)); dispatch(clearError()); }, [dispatch]); // Handle text input change const handleTextChange = useCallback((event: React.ChangeEvent<HTMLTextAreaElement>) => { const value = event.target.value; setTextValue(value); dispatch(setTextInput(value)); }, [dispatch]); // Handle text processing const handleProcessText = useCallback(async () => { if (!textValue.trim()) return; try { await dispatch(processText({ text: textValue.trim(), options: processingOptions })).unwrap(); } catch (error) { console.error('Text processing failed:', error); } }, [dispatch, textValue, processingOptions]); // Handle voice processing const handleProcessVoice = useCallback(async () => { if (!voiceInput.audioBlob) return; try { await dispatch(processVoice({ audioBlob: voiceInput.audioBlob, options: processingOptions })).unwrap(); } catch (error) { console.error('Voice processing failed:', error); } }, [dispatch, voiceInput.audioBlob, processingOptions]); // Handle image processing const handleProcessImage = useCallback(async () => { if (!imageInput.file) return; try { await dispatch(processImage({ imageFile: imageInput.file, options: processingOptions })).unwrap(); } catch (error) { console.error('Image processing failed:', error); } }, [dispatch, imageInput.file, processingOptions]); // Handle multimodal processing const handleProcessMultimodal = useCallback(async () => { const hasText = textValue.trim().length > 0; const hasVoice = !!voiceInput.audioBlob; const hasImage = !!imageInput.file; if (!hasText && !hasVoice && !hasImage) { dispatch(setError('Please provide at least one input (text, voice, or image)')); return; } try { await dispatch(processMultimodal({ text: hasText ? textValue.trim() : undefined, audioBlob: hasVoice && voiceInput.audioBlob ? voiceInput.audioBlob : undefined, imageFile: hasImage && imageInput.file ? imageInput.file : undefined, options: processingOptions })).unwrap(); } catch (error) { console.error('Multimodal processing failed:', error); } }, [dispatch, textValue, voiceInput.audioBlob, imageInput.file, processingOptions]); // Handle clear all const handleClearAll = useCallback(() => { dispatch(clearResults()); setTextValue(''); dispatch(setTextInput('')); }, [dispatch]); // Handle retry connection const handleRetryConnection = useCallback(async () => { try { await multimodalService.retry(); } catch (error) { console.error('Retry connection failed:', error); } }, []); // Get processing button props const getProcessingButtonProps = () => { switch (activeTab) { case 'text': return { disabled: !textValue.trim() || isProcessing, onClick: handleProcessText, text: 'Analyser le texte' }; case 'voice': return { disabled: !voiceInput.audioBlob || isProcessing, onClick: handleProcessVoice, text: 'Analyser la voix' }; case 'image': return { disabled: !imageInput.file || isProcessing, onClick: handleProcessImage, text: 'Analyser l\'image' }; case 'multimodal': return { disabled: (!textValue.trim() && !voiceInput.audioBlob && !imageInput.file) || isProcessing, onClick: handleProcessMultimodal, text: 'Analyse multimodale' }; default: return { disabled: true, onClick: () => {}, text: 'Analyser' }; } }; const processingButtonProps = getProcessingButtonProps(); return ( <Box className={className}> {/* Connection Status */} {connectionStatus !== 'connected' && ( <Alert severity={connectionStatus === 'error' ? 'error' : 'warning'} action={ connectionStatus === 'error' ? ( <IconButton size="small" onClick={handleRetryConnection}> <RefreshIcon /> </IconButton> ) : null } sx={{ mb: 2 }} > {connectionStatus === 'connecting' && 'Connexion au service multimodal...'} {connectionStatus === 'disconnected' && 'Service multimodal déconnecté'} {connectionStatus === 'error' && 'Erreur de connexion au service multimodal'} </Alert> )} {/* Error Display */} {error && ( <Alert severity="error" onClose={() => dispatch(clearError())} sx={{ mb: 2 }} > {error} </Alert> )} {/* Main Input Card */} <Card elevation={2}> <CardContent> {/* Header */} <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}> <Typography variant="h6" component="h2"> Analyse Multimodale </Typography> <Box> {showAdvancedOptions && ( <Tooltip title="Options avancées"> <IconButton size="small" onClick={() => setShowSettings(!showSettings)} color={showSettings ? 'primary' : 'default'} > <SettingsIcon /> </IconButton> </Tooltip> )} <Tooltip title="Informations"> <IconButton size="small"> <InfoIcon /> </IconButton> </Tooltip> </Box> </Box> {/* Advanced Settings */} <Collapse in={showSettings}> <Box mb={2} p={2} bgcolor="grey.50" borderRadius={1}> <Typography variant="subtitle2" gutterBottom> Options de traitement </Typography> <Box display="flex" gap={1} flexWrap="wrap"> <Chip label="Analyse avancée" variant={processingOptions.enhancedAnalysis ? 'filled' : 'outlined'} onClick={() => setProcessingOptions(prev => ({ ...prev, enhancedAnalysis: !prev.enhancedAnalysis }))} size="small" /> <Chip label="Données brutes" variant={processingOptions.includeRawFeatures ? 'filled' : 'outlined'} onClick={() => setProcessingOptions(prev => ({ ...prev, includeRawFeatures: !prev.includeRawFeatures }))} size="small" /> </Box> </Box> </Collapse> {/* Tabs */} <Tabs value={activeTab} onChange={handleTabChange} variant="fullWidth" sx={{ mb: 2 }} > <Tab icon={<TextIcon />} label="Texte" value="text" iconPosition="start" /> <Tab icon={<MicIcon />} label="Voix" value="voice" iconPosition="start" /> <Tab icon={<ImageIcon />} label="Image" value="image" iconPosition="start" /> <Tab icon={<MultimodalIcon />} label="Multimodal" value="multimodal" iconPosition="start" /> </Tabs> {/* Tab Content */} <Box minHeight={200}> {/* Text Input */} {activeTab === 'text' && ( <Fade in timeout={300}> <Box> <textarea value={textValue} onChange={handleTextChange} placeholder="Saisissez votre texte ici pour analyse de sentiment, détection d'intention, extraction d'entités..." style={{ width: '100%', minHeight: '150px', padding: '12px', border: '1px solid #ddd', borderRadius: '4px', fontSize: '14px', fontFamily: 'inherit', resize: 'vertical' }} disabled={isProcessing} /> <Box mt={1} display="flex" justifyContent="space-between" alignItems="center"> <Typography variant="caption" color="text.secondary"> {textValue.length} caractères </Typography> {textValue && ( <Button size="small" startIcon={<ClearIcon />} onClick={() => { setTextValue(''); dispatch(setTextInput('')); }} > Effacer </Button> )} </Box> </Box> </Fade> )} {/* Voice Input */} {activeTab === 'voice' && ( <Fade in timeout={300}> <Box> <VoiceRecorder /> </Box> </Fade> )} {/* Image Input */} {activeTab === 'image' && ( <Fade in timeout={300}> <Box> <ImageUploader /> </Box> </Fade> )} {/* Multimodal Input */} {activeTab === 'multimodal' && ( <Fade in timeout={300}> <Box> <Typography variant="body2" color="text.secondary" mb={2}> Combinez plusieurs modalités pour une analyse plus riche et précise. </Typography> <Box display="flex" gap={2} mb={2}> <Chip icon={<TextIcon />} label={`Texte ${textValue ? '' : ''}`} color={textValue ? 'success' : 'default'} variant={textValue ? 'filled' : 'outlined'} /> <Chip icon={<MicIcon />} label={`Voix ${voiceInput.audioBlob ? '' : ''}`} color={voiceInput.audioBlob ? 'success' : 'default'} variant={voiceInput.audioBlob ? 'filled' : 'outlined'} /> <Chip icon={<ImageIcon />} label={`Image ${imageInput.file ? '' : ''}`} color={imageInput.file ? 'success' : 'default'} variant={imageInput.file ? 'filled' : 'outlined'} /> </Box> <Typography variant="body2" color="text.secondary"> Utilisez les onglets ci-dessus pour ajouter du contenu, puis revenez ici pour l'analyse multimodale. </Typography> </Box> </Fade> )} </Box> {/* Processing Progress */} {isProcessing && ( <Box mt={2}> <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}> <Typography variant="body2" color="text.secondary"> Traitement en cours... ({processingType}) </Typography> <Typography variant="body2" color="text.secondary"> {Math.round(processingProgress)}% </Typography> </Box> <LinearProgress variant="determinate" value={processingProgress} sx={{ height: 6, borderRadius: 3 }} /> </Box> )} {/* Action Buttons */} <Box mt={3} display="flex" gap={2} justifyContent="space-between"> <Button variant="outlined" startIcon={<ClearIcon />} onClick={handleClearAll} disabled={isProcessing} > Tout effacer </Button> <Button variant="contained" startIcon={<SendIcon />} onClick={processingButtonProps.onClick} disabled={processingButtonProps.disabled} size="large" > {processingButtonProps.text} </Button> </Box> </CardContent> </Card> {/* Results Display */} {showResults && ( <Box mt={3}> <ProcessingDisplay /> </Box> )} </Box> ); }; export default MultimodalInput;