import React, { useState } from 'react'; import { Card, CardContent, Typography, Accordion, AccordionSummary, AccordionDetails, Box, Chip, TextField, InputAdornment, } from '@mui/material'; import { ExpandMore as ExpandMoreIcon, Search as SearchIcon, HelpOutline as HelpIcon, Build as TechnicalIcon, Receipt as BillingIcon, SignalCellularAlt as NetworkIcon, } from '@mui/icons-material'; interface FAQItem { id: string; question: string; answer: string; category: 'technical' | 'billing' | 'network' | 'general'; tags: string[]; } const faqData: FAQItem[] = [ { id: '1', question: 'Comment résoudre les problèmes de connexion ?', answer: 'Vérifiez d\'abord que tous les câbles sont bien connectés. Redémarrez votre box en la débranchant pendant 30 secondes puis en la rebranchant. Si le problème persiste, vérifiez les paramètres de votre réseau WiFi.', category: 'technical', tags: ['connexion', 'wifi', 'réseau'], }, { id: '2', question: 'Problèmes de facturation', answer: 'Consultez votre espace client pour voir le détail de votre facture. En cas de désaccord, contactez notre service client avec votre numéro de facture. Vous pouvez également demander un échéancier de paiement si nécessaire.', category: 'billing', tags: ['facture', 'paiement', 'tarif'], }, { id: '3', question: 'Comment améliorer la qualité du signal mobile ?', answer: 'Vérifiez la couverture réseau dans votre zone sur notre carte de couverture. Redémarrez votre téléphone et vérifiez que vous êtes en zone couverte. En intérieur, rapprochez-vous d\'une fenêtre.', category: 'network', tags: ['signal', 'mobile', 'couverture'], }, { id: '4', question: 'Comment configurer ma messagerie vocale ?', answer: 'Composez le 666 depuis votre mobile Free ou le 06 95 60 00 66 depuis un autre téléphone. Suivez les instructions vocales pour personnaliser votre message d\'accueil et votre code secret.', category: 'technical', tags: ['messagerie', 'vocal', 'configuration'], }, { id: '5', question: 'Que faire en cas de panne de ma Freebox ?', answer: 'Vérifiez l\'état des voyants de votre Freebox. Si tous les voyants sont éteints, vérifiez l\'alimentation. Consultez notre page de statut des services pour voir s\'il y a une panne générale dans votre secteur.', category: 'technical', tags: ['freebox', 'panne', 'dépannage'], }, ]; const FAQSection: React.FC = () => { const [searchTerm, setSearchTerm] = useState(''); const [expandedPanel, setExpandedPanel] = useState<string | false>(false); const handleAccordionChange = (panel: string) => ( event: React.SyntheticEvent, isExpanded: boolean ) => { setExpandedPanel(isExpanded ? panel : false); }; const filteredFAQ = faqData.filter( (item) => item.question.toLowerCase().includes(searchTerm.toLowerCase()) || item.answer.toLowerCase().includes(searchTerm.toLowerCase()) || item.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase())) ); const getCategoryIcon = (category: string) => { switch (category) { case 'technical': return <TechnicalIcon fontSize="small" />; case 'billing': return <BillingIcon fontSize="small" />; case 'network': return <NetworkIcon fontSize="small" />; default: return <HelpIcon fontSize="small" />; } }; const getCategoryLabel = (category: string) => { switch (category) { case 'technical': return 'Technique'; case 'billing': return 'Facturation'; case 'network': return 'Réseau'; default: return 'Général'; } }; const getCategoryColor = (category: string) => { switch (category) { case 'technical': return 'primary'; case 'billing': return 'secondary'; case 'network': return 'success'; default: return 'default'; } }; return ( <Card elevation={2}> <CardContent sx={{ p: 4 }}> <Typography variant="h5" gutterBottom sx={{ fontWeight: 'bold', mb: 3 }}> Questions fréquentes </Typography> {/* Search Bar */} <TextField fullWidth placeholder="Rechercher dans les questions fréquentes..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} InputProps={{ startAdornment: ( <InputAdornment position="start"> <SearchIcon color="action" /> </InputAdornment> ), }} sx={{ mb: 3 }} /> {/* FAQ Items */} <Box> {filteredFAQ.length === 0 ? ( <Typography color="text.secondary" textAlign="center" py={4}> Aucune question trouvée pour votre recherche. </Typography> ) : ( filteredFAQ.map((item) => ( <Accordion key={item.id} expanded={expandedPanel === item.id} onChange={handleAccordionChange(item.id)} sx={{ mb: 1, '&:before': { display: 'none', }, boxShadow: 1, borderRadius: 1, '&.Mui-expanded': { boxShadow: 2, }, }} > <AccordionSummary expandIcon={<ExpandMoreIcon />} sx={{ '& .MuiAccordionSummary-content': { alignItems: 'center', gap: 2, }, }} > <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexGrow: 1 }}> <Chip icon={getCategoryIcon(item.category)} label={getCategoryLabel(item.category)} size="small" color={getCategoryColor(item.category) as any} variant="outlined" /> <Typography variant="subtitle1" fontWeight="medium"> {item.question} </Typography> </Box> </AccordionSummary> <AccordionDetails sx={{ pt: 0 }}> <Typography variant="body2" color="text.secondary" paragraph> {item.answer} </Typography> <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}> {item.tags.map((tag) => ( <Chip key={tag} label={tag} size="small" variant="outlined" sx={{ fontSize: '0.7rem', height: 20 }} /> ))} </Box> </AccordionDetails> </Accordion> )) )} </Box> {/* Help Text */} <Box sx={{ mt: 3, p: 2, backgroundColor: 'grey.50', borderRadius: 1 }}> <Typography variant="body2" color="text.secondary" textAlign="center"> Vous ne trouvez pas la réponse à votre question ? Utilisez le chat en direct ou remplissez le formulaire de contact ci-dessus. </Typography> </Box> </CardContent> </Card> ); }; export default FAQSection;