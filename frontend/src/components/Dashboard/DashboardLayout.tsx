import React, { useState } from 'react';
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  Typography,
  IconButton,
  useTheme,
  useMediaQuery,
  Container,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import { Outlet, useLocation } from 'react-router-dom';
import { FREE_MOBILE_COLORS } from '../../utils/constants';

const DRAWER_WIDTH = 280;

interface DashboardLayoutProps {
  children?: React.ReactNode;
}

// Simple sidebar component for now
const SimpleSidebar: React.FC<{ onItemClick?: () => void }> = ({ onItemClick }) => (
  <Box sx={{ p: 2 }}>
    <Typography variant="h6" sx={{ mb: 2 }}>
      Navigation
    </Typography>
    <Typography variant="body2" color="text.secondary">
      Sidebar content will be implemented here
    </Typography>
  </Box>
);

// Simple top navigation component for now
const SimpleTopNavigation: React.FC = () => (
  <Box sx={{ display: 'flex', alignItems: 'center' }}>
    <Typography variant="body2" color="inherit">
      Dashboard
    </Typography>
  </Box>
);

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [mobileOpen, setMobileOpen] = useState(false);
  const location = useLocation();

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const isAdminRoute = location.pathname.includes('/dashboard/admin');

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      {/* App Bar */}
      <AppBar
        position="fixed"
        sx={{
          width: { md: isAdminRoute ? `calc(100% - ${DRAWER_WIDTH}px)` : '100%' },
          ml: { md: isAdminRoute ? `${DRAWER_WIDTH}px` : 0 },
          backgroundColor: FREE_MOBILE_COLORS.PRIMARY,
          zIndex: theme.zIndex.drawer + 1,
        }}
      >
        <Toolbar>
          {(isMobile || isAdminRoute) && (
            <IconButton
              color="inherit"
              aria-label="open drawer"
              edge="start"
              onClick={handleDrawerToggle}
              sx={{ mr: 2, display: { md: isAdminRoute ? 'block' : 'none' } }}
            >
              <MenuIcon />
            </IconButton>
          )}
          <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
            <Typography
              variant="h6"
              noWrap
              component="div"
              sx={{
                fontWeight: 'bold',
                color: 'white',
                mr: 4,
              }}
            >
              Free Mobile
            </Typography>
            {/* Top Navigation Tabs */}
            <SimpleTopNavigation />
          </Box>
        </Toolbar>
      </AppBar>

      {/* Sidebar Drawer (only for admin routes) */}
      {isAdminRoute && (
        <Box
          component="nav"
          sx={{ width: { md: DRAWER_WIDTH }, flexShrink: { md: 0 } }}
        >
          {/* Mobile drawer */}
          <Drawer
            variant="temporary"
            open={mobileOpen}
            onClose={handleDrawerToggle}
            ModalProps={{
              keepMounted: true, // Better open performance on mobile
            }}
            sx={{
              display: { xs: 'block', md: 'none' },
              '& .MuiDrawer-paper': {
                boxSizing: 'border-box',
                width: DRAWER_WIDTH,
                backgroundColor: '#f8f9fa',
              },
            }}
          >
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', p: 1 }}>
              <IconButton onClick={handleDrawerToggle}>
                <CloseIcon />
              </IconButton>
            </Box>
            <SimpleSidebar onItemClick={() => setMobileOpen(false)} />
          </Drawer>

          {/* Desktop drawer */}
          <Drawer
            variant="permanent"
            sx={{
              display: { xs: 'none', md: 'block' },
              '& .MuiDrawer-paper': {
                boxSizing: 'border-box',
                width: DRAWER_WIDTH,
                backgroundColor: '#f8f9fa',
                borderRight: '1px solid #e0e0e0',
              },
            }}
            open
          >
            <Toolbar /> {/* Spacer for AppBar */}
            <SimpleSidebar />
          </Drawer>
        </Box>
      )}

      {/* Main content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          width: { md: isAdminRoute ? `calc(100% - ${DRAWER_WIDTH}px)` : '100%' },
          backgroundColor: '#f5f5f5',
          minHeight: '100vh',
        }}
      >
        <Toolbar /> {/* Spacer for AppBar */}
        <Container
          maxWidth={false}
          sx={{
            py: 3,
            px: { xs: 2, sm: 3 },
          }}
        >
          {children || <Outlet />}
        </Container>
      </Box>
    </Box>
  );
};

export default DashboardLayout;