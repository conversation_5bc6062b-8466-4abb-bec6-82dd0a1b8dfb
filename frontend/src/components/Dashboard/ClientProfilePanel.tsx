import React, { useState } from 'react'; import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON><PERSON>, Box, Typo<PERSON>, Avatar, Chip, Divider, List, ListItem, ListItemText, ListItemIcon, Button, IconButton, Collapse, } from '@mui/material'; import { Person as PersonIcon, Phone as PhoneIcon, Email as EmailIcon, LocationOn as LocationIcon, CalendarToday as CalendarIcon, History as HistoryIcon, ExpandMore as ExpandMoreIcon, ExpandLess as ExpandLessIcon, Call as CallIcon, Message as MessageIcon, } from '@mui/icons-material'; import { FREE_MOBILE_COLORS } from '../../utils/constants'; interface ClientProfile { id: string; name: string; email: string; phone: string; address: string; joinDate: string; status: 'Actif' | 'Inactif' | 'Suspendu'; plan: string; avatar: string; lastActivity: string; totalTickets: number; resolvedTickets: number; satisfaction: number; } interface InteractionHistory { id: string; type: 'call' | 'email' | 'chat' | 'ticket'; title: string; description: string; date: string; status: string; } const mockProfile: ClientProfile = { id: 'FREE-12345678', name: '<PERSON>pont', email: '<EMAIL>', phone: '06 12 34 56 78', address: '123 Rue de la Paix, 75001 Paris', joinDate: '15/01/2023', status: 'Actif', plan: 'Forfait 150Go', avatar: 'JD', lastActivity: '2 heures', totalTickets: 12, resolvedTickets: 10, satisfaction: 4.2, }; const mockHistory: InteractionHistory[] = [ { id: '1', type: 'ticket', title: 'Problème de connexion', description: 'Ticket #12347 - Résolu', date: '27/08/2024', status: 'Résolu', }, { id: '2', type: 'chat', title: 'Question facturation', description: 'Chat avec Agent Marie', date: '25/08/2024', status: 'Terminé', }, { id: '3', type: 'call', title: 'Appel support', description: 'Durée: 15 minutes', date: '20/08/2024', status: 'Terminé', }, ]; const ClientProfilePanel: React.FC = () => { const [profile] = useState<ClientProfile>(mockProfile); const [history] = useState<InteractionHistory[]>(mockHistory); const [showHistory, setShowHistory] = useState(false); const getStatusColor = (status: string) => { switch (status.toLowerCase()) { case 'actif': return 'success'; case 'inactif': return 'default'; case 'suspendu': return 'error'; default: return 'default'; } }; const getInteractionIcon = (type: string) => { switch (type) { case 'call': return <CallIcon fontSize="small" />; case 'email': return <EmailIcon fontSize="small" />; case 'chat': return <MessageIcon fontSize="small" />; case 'ticket': return <HistoryIcon fontSize="small" />; default: return <HistoryIcon fontSize="small" />; } }; const satisfactionColor = profile.satisfaction >= 4 ? FREE_MOBILE_COLORS.SUCCESS : profile.satisfaction >= 3 ? FREE_MOBILE_COLORS.WARNING : FREE_MOBILE_COLORS.ERROR; return ( <Card elevation={2} sx={{ height: 'fit-content' }}> <CardHeader title="Profil Client" sx={{ pb: 1 }} /> <CardContent sx={{ pt: 0 }}> {/* Client Info */} <Box sx={{ textAlign: 'center', mb: 3 }}> <Avatar sx={{ width: 80, height: 80, mx: 'auto', mb: 2, backgroundColor: FREE_MOBILE_COLORS.PRIMARY, fontSize: '1.5rem', }} > {profile.avatar} </Avatar> <Typography variant="h6" fontWeight="bold" gutterBottom> {profile.name} </Typography> <Chip label={profile.status} size="small" color={getStatusColor(profile.status) as any} sx={{ mb: 2 }} /> <Typography variant="body2" color="text.secondary"> Client depuis le {profile.joinDate} </Typography> </Box> <Divider sx={{ my: 2 }} /> {/* Contact Info */} <List dense> <ListItem sx={{ px: 0 }}> <ListItemIcon sx={{ minWidth: 36 }}> <PersonIcon color="action" fontSize="small" /> </ListItemIcon> <ListItemText primary="ID Client" secondary={profile.id} /> </ListItem> <ListItem sx={{ px: 0 }}> <ListItemIcon sx={{ minWidth: 36 }}> <EmailIcon color="action" fontSize="small" /> </ListItemIcon> <ListItemText primary="Email" secondary={profile.email} /> </ListItem> <ListItem sx={{ px: 0 }}> <ListItemIcon sx={{ minWidth: 36 }}> <PhoneIcon color="action" fontSize="small" /> </ListItemIcon> <ListItemText primary="Téléphone" secondary={profile.phone} /> </ListItem> <ListItem sx={{ px: 0 }}> <ListItemIcon sx={{ minWidth: 36 }}> <LocationIcon color="action" fontSize="small" /> </ListItemIcon> <ListItemText primary="Adresse" secondary={profile.address} /> </ListItem> </List> <Divider sx={{ my: 2 }} /> {/* Stats */} <Box sx={{ mb: 3 }}> <Typography variant="subtitle2" fontWeight="bold" gutterBottom> Statistiques </Typography> <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}> <Typography variant="body2" color="text.secondary"> Forfait actuel </Typography> <Typography variant="body2" fontWeight="medium"> {profile.plan} </Typography> </Box> <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}> <Typography variant="body2" color="text.secondary"> Tickets total </Typography> <Typography variant="body2" fontWeight="medium"> {profile.totalTickets} </Typography> </Box> <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}> <Typography variant="body2" color="text.secondary"> Tickets résolus </Typography> <Typography variant="body2" fontWeight="medium"> {profile.resolvedTickets} </Typography> </Box> <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}> <Typography variant="body2" color="text.secondary"> Satisfaction </Typography> <Typography variant="body2" fontWeight="medium" sx={{ color: satisfactionColor }} > {profile.satisfaction}/5 </Typography> </Box> <Box sx={{ display: 'flex', justifyContent: 'space-between' }}> <Typography variant="body2" color="text.secondary"> Dernière activité </Typography> <Typography variant="body2" fontWeight="medium"> Il y a {profile.lastActivity} </Typography> </Box> </Box> {/* Quick Actions */} <Box sx={{ display: 'flex', gap: 1, mb: 2 }}> <Button variant="outlined" size="small" startIcon={<CallIcon />} fullWidth > Appeler </Button> <Button variant="outlined" size="small" startIcon={<MessageIcon />} fullWidth > Chat </Button> </Box> <Divider sx={{ my: 2 }} /> {/* Interaction History */} <Box> <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', cursor: 'pointer', }} onClick={() => setShowHistory(!showHistory)} > <Typography variant="subtitle2" fontWeight="bold"> Historique récent </Typography> <IconButton size="small"> {showHistory ? <ExpandLessIcon /> : <ExpandMoreIcon />} </IconButton> </Box> <Collapse in={showHistory}> <List dense sx={{ mt: 1 }}> {history.map((item) => ( <ListItem key={item.id} sx={{ px: 0, py: 0.5 }}> <ListItemIcon sx={{ minWidth: 32 }}> {getInteractionIcon(item.type)} </ListItemIcon> <ListItemText primary={ <Typography variant="body2" fontWeight="medium"> {item.title} </Typography> } secondary={ <Box> <Typography variant="caption" color="text.secondary"> {item.description} </Typography> <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}> {item.date} </Typography> </Box> } /> </ListItem> ))} </List> </Collapse> </Box> </CardContent> </Card> ); }; export default ClientProfilePanel;