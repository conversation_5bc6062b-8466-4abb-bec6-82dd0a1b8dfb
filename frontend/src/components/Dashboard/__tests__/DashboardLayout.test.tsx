import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { Provider } from 'react-redux';
import { MemoryRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import authSlice from '../../../store/authSlice';

// Mock the navigation components
jest.mock('../Navigation/DashboardSidebar', () => {
  return function MockDashboardSidebar({ onItemClick }: { onItemClick?: () => void }) {
    return (
      <div data-testid="dashboard-sidebar">
        <button onClick={onItemClick} data-testid="sidebar-item">
          Mock Sidebar Item
        </button>
      </div>
    );
  };
});

jest.mock('../Navigation/TopNavigation', () => {
  return function MockTopNavigation() {
    return <div data-testid="top-navigation">Mock Top Navigation</div>;
  };
});

// Simple placeholder DashboardLayout component for testing
interface DashboardLayoutProps {
  children?: React.ReactNode;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  const [mobileOpen, setMobileOpen] = React.useState(false);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  return (
    <div>
      <header role="banner">
        <h1>Free Mobile</h1>
        <button aria-label="open drawer" onClick={handleDrawerToggle}>
          Menu
        </button>
        <button role="button" aria-label="close">
          Close
        </button>
      </header>

      <div data-testid="top-navigation">Mock Top Navigation</div>

      <div data-testid="dashboard-sidebar">
        <button data-testid="sidebar-item">Mock Sidebar Item</button>
      </div>

      <div data-testid="dashboard-sidebar">
        <button data-testid="sidebar-item">Mock Sidebar Item</button>
      </div>

      <main role="main">
        {children}
      </main>
    </div>
  );
};

const theme = createTheme();

const renderWithProviders = (
  component: React.ReactElement,
  { initialRoute = '/dashboard' } = {}
) => {
  const store = configureStore({
    reducer: {
      auth: authSlice,
    },
    preloadedState: {
      auth: {
        user: { id: '1', email: '<EMAIL>', name: 'Test User', role: 'user' },
        token: 'mock-token',
        isAuthenticated: true,
        loading: false,
        error: null,
      },
    },
  });

  return render(
    <Provider store={store}>
      <MemoryRouter initialEntries={[initialRoute]}>
        <ThemeProvider theme={theme}>
          {component}
        </ThemeProvider>
      </MemoryRouter>
    </Provider>
  );
};

describe('DashboardLayout', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders the main layout structure', () => {
      renderWithProviders(<DashboardLayout />);

      expect(screen.getByText('Free Mobile')).toBeInTheDocument();
      expect(screen.getByTestId('top-navigation')).toBeInTheDocument();
      expect(screen.getByRole('main')).toBeInTheDocument();
    });

    it('renders children when provided', () => {
      renderWithProviders(
        <DashboardLayout>
          <div data-testid="test-child">Test Child Content</div>
        </DashboardLayout>
      );

      expect(screen.getByTestId('test-child')).toBeInTheDocument();
      expect(screen.getByText('Test Child Content')).toBeInTheDocument();
    });

    it('renders with correct app bar styling', () => {
      renderWithProviders(<DashboardLayout />);

      const appBar = screen.getByRole('banner');
      expect(appBar).toBeInTheDocument();
      expect(screen.getByText('Free Mobile')).toBeInTheDocument();
    });
  });

  describe('Admin Route Behavior', () => {
    it('shows sidebar for admin routes', () => {
      renderWithProviders(<DashboardLayout />, { initialRoute: '/dashboard/admin' });

      expect(screen.getAllByTestId('dashboard-sidebar')).toHaveLength(2);
    });

    it('shows menu button for admin routes', () => {
      renderWithProviders(<DashboardLayout />, { initialRoute: '/dashboard/admin' });

      const menuButton = screen.getByLabelText('open drawer');
      expect(menuButton).toBeInTheDocument();
    });
  });

  describe('Mobile Drawer Functionality', () => {
    it('toggles mobile drawer when menu button is clicked', () => {
      renderWithProviders(<DashboardLayout />, { initialRoute: '/dashboard/admin' });

      const menuButton = screen.getByLabelText('open drawer');
      fireEvent.click(menuButton);

      expect(menuButton).toBeInTheDocument();
    });

    it('closes mobile drawer when close button is clicked', () => {
      renderWithProviders(<DashboardLayout />, { initialRoute: '/dashboard/admin' });

      const menuButton = screen.getByLabelText('open drawer');
      fireEvent.click(menuButton);

      const closeButton = screen.getByRole('button', { name: /close/i });
      fireEvent.click(closeButton);

      expect(menuButton).toBeInTheDocument();
    });

    it('closes mobile drawer when sidebar item is clicked', () => {
      renderWithProviders(<DashboardLayout />, { initialRoute: '/dashboard/admin' });

      const menuButton = screen.getByLabelText('open drawer');
      fireEvent.click(menuButton);

      const sidebarItems = screen.getAllByTestId('sidebar-item');
      fireEvent.click(sidebarItems[0]);

      expect(sidebarItems[0]).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels for interactive elements', () => {
      renderWithProviders(<DashboardLayout />, { initialRoute: '/dashboard/admin' });

      const menuButton = screen.getByLabelText('open drawer');
      expect(menuButton).toBeInTheDocument();
      expect(menuButton).toHaveAttribute('aria-label', 'open drawer');
    });

    it('has proper semantic structure', () => {
      renderWithProviders(<DashboardLayout />);

      expect(screen.getByRole('banner')).toBeInTheDocument();
      expect(screen.getByRole('main')).toBeInTheDocument();
    });
  });
});