import React, { useState } from 'react'; import { Box, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TablePagination, Paper, Chip, Avatar, IconButton, Menu, MenuItem, TextField, InputAdornment, FormControl, InputLabel, Select, Toolbar, Typography, } from '@mui/material'; import { MoreVert as MoreVertIcon, Search as SearchIcon, FilterList as FilterIcon, } from '@mui/icons-material'; import { FREE_MOBILE_COLORS } from '../../utils/constants'; interface ClientRequest { id: string; client: string; category: string; status: string; date: string; tags: string[]; priority: 'Haute' | 'Moyenne' | 'Basse'; avatar: string; } const mockData: ClientRequest[] = [ { id: '#12347', client: '<PERSON>', category: 'Réseau', status: 'En attente', date: '27/08/2024', tags: ['urgent'], priority: 'Haute', avatar: 'JD', }, { id: '#12346', client: '<PERSON>', category: 'Facturation', status: 'Résolu', date: '26/08/2024', tags: ['standard'], priority: 'Moyenne', avatar: 'MM', }, { id: '#12345', client: 'Pierre <PERSON>', category: 'Technique', status: 'En cours', date: '25/08/2024', tags: ['complexe'], priority: 'Haute', avatar: 'PB', }, { id: '#12344', client: 'Sophie Dubois', category: 'Abonnement', status: 'Résolu', date: '24/08/2024', tags: ['simple'], priority: 'Basse', avatar: 'SD', }, { id: '#12343', client: 'Luc Moreau', category: 'Réseau', status: 'En attente', date: '23/08/2024', tags: ['urgent', 'escalade'], priority: 'Haute', avatar: 'LM', }, ]; const ClientRequestsTable: React.FC = () => { const [data, setData] = useState<ClientRequest[]>(mockData); const [page, setPage] = useState(0); const [rowsPerPage, setRowsPerPage] = useState(10); const [searchTerm, setSearchTerm] = useState(''); const [statusFilter, setStatusFilter] = useState(''); const [categoryFilter, setCategoryFilter] = useState(''); const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null); const [selectedRow, setSelectedRow] = useState<string | null>(null); const handleChangePage = (event: unknown, newPage: number) => { setPage(newPage); }; const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => { setRowsPerPage(parseInt(event.target.value, 10)); setPage(0); }; const handleMenuClick = (event: React.MouseEvent<HTMLElement>, id: string) => { setAnchorEl(event.currentTarget); setSelectedRow(id); }; const handleMenuClose = () => { setAnchorEl(null); setSelectedRow(null); }; const getStatusColor = (status: string) => { switch (status.toLowerCase()) { case 'résolu': return 'success'; case 'en cours': return 'primary'; case 'en attente': return 'warning'; default: return 'default'; } }; const getPriorityColor = (priority: string) => { switch (priority.toLowerCase()) { case 'haute': return FREE_MOBILE_COLORS.ERROR; case 'moyenne': return FREE_MOBILE_COLORS.WARNING; case 'basse': return FREE_MOBILE_COLORS.SUCCESS; default: return FREE_MOBILE_COLORS.TEXT_SECONDARY; } }; const filteredData = data.filter((item) => { const matchesSearch = item.client.toLowerCase().includes(searchTerm.toLowerCase()) || item.id.toLowerCase().includes(searchTerm.toLowerCase()); const matchesStatus = !statusFilter || item.status === statusFilter; const matchesCategory = !categoryFilter || item.category === categoryFilter; return matchesSearch && matchesStatus && matchesCategory; }); const paginatedData = filteredData.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage); const uniqueStatuses = [...new Set(data.map(item => item.status))]; const uniqueCategories = [...new Set(data.map(item => item.category))]; return ( <Paper elevation={2}> {/* Toolbar */} <Toolbar sx={{ px: 2, py: 1 }}> <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}> Demandes clients </Typography> <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}> <TextField size="small" placeholder="Rechercher..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} InputProps={{ startAdornment: ( <InputAdornment position="start"> <SearchIcon /> </InputAdornment> ), }} sx={{ width: 200 }} /> <FormControl size="small" sx={{ minWidth: 120 }}> <InputLabel>Statut</InputLabel> <Select value={statusFilter} onChange={(e) => setStatusFilter(e.target.value)} label="Statut" > <MenuItem value="">Tous les statuts</MenuItem> {uniqueStatuses.map((status) => ( <MenuItem key={status} value={status}> {status} </MenuItem> ))} </Select> </FormControl> <FormControl size="small" sx={{ minWidth: 120 }}> <InputLabel>Catégorie</InputLabel> <Select value={categoryFilter} onChange={(e) => setCategoryFilter(e.target.value)} label="Catégorie" > <MenuItem value="">Toutes catégories</MenuItem> {uniqueCategories.map((category) => ( <MenuItem key={category} value={category}> {category} </MenuItem> ))} </Select> </FormControl> </Box> </Toolbar> {/* Table */} <TableContainer> <Table> <TableHead> <TableRow> <TableCell>Client</TableCell> <TableCell>Catégorie</TableCell> <TableCell>Statut</TableCell> <TableCell>Date</TableCell> <TableCell>Tags</TableCell> <TableCell align="right">Actions</TableCell> </TableRow> </TableHead> <TableBody> {paginatedData.map((row) => ( <TableRow key={row.id} hover> <TableCell> <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}> <Avatar sx={{ width: 32, height: 32, fontSize: '0.8rem' }}> {row.avatar} </Avatar> <Box> <Typography variant="subtitle2" fontWeight="medium"> {row.client} </Typography> <Typography variant="caption" color="text.secondary"> {row.id} </Typography> </Box> </Box> </TableCell> <TableCell> <Typography variant="body2">{row.category}</Typography> </TableCell> <TableCell> <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}> <Box sx={{ width: 8, height: 8, borderRadius: '50%', backgroundColor: getPriorityColor(row.priority), }} /> <Chip label={row.status} size="small" color={getStatusColor(row.status) as any} variant="outlined" /> </Box> </TableCell> <TableCell> <Typography variant="body2" color="text.secondary"> {row.date} </Typography> </TableCell> <TableCell> <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}> {row.tags.map((tag) => ( <Chip key={tag} label={tag} size="small" variant="outlined" sx={{ fontSize: '0.7rem', height: 20 }} /> ))} </Box> </TableCell> <TableCell align="right"> <IconButton size="small" onClick={(e) => handleMenuClick(e, row.id)} > <MoreVertIcon /> </IconButton> </TableCell> </TableRow> ))} </TableBody> </Table> </TableContainer> {/* Pagination */} <TablePagination rowsPerPageOptions={[5, 10, 25]} component="div" count={filteredData.length} rowsPerPage={rowsPerPage} page={page} onPageChange={handleChangePage} onRowsPerPageChange={handleChangeRowsPerPage} labelRowsPerPage="Lignes par page:" labelDisplayedRows={({ from, to, count }) => `${from}-${to} sur ${count}`} /> {/* Context Menu */} <Menu anchorEl={anchorEl} open={Boolean(anchorEl)} onClose={handleMenuClose} > <MenuItem onClick={handleMenuClose}>Voir détails</MenuItem> <MenuItem onClick={handleMenuClose}>Assigner agent</MenuItem> <MenuItem onClick={handleMenuClose}>Changer statut</MenuItem> <MenuItem onClick={handleMenuClose}>Ajouter note</MenuItem> </Menu> </Paper> ); }; export default ClientRequestsTable;