import React, { useState, useRef, useEffect } from 'react'; import { Card, Card<PERSON>ontent, CardHeader, Box, Typography, TextField, IconButton, Avatar, Chip, Divider, Paper, } from '@mui/material'; import { Send as SendIcon, Circle as OnlineIcon, SupportAgent as AgentIcon, } from '@mui/icons-material'; import { FREE_MOBILE_COLORS } from '../../utils/constants'; interface ChatMessage { id: string; sender: 'user' | 'agent' | 'bot'; message: string; timestamp: Date; senderName?: string; } const LiveChatWidget: React.FC = () => { const [messages, setMessages] = useState<ChatMessage[]>([ { id: '1', sender: 'bot', message: 'Bonjour ! Je suis votre assistant virtuel Free Mobile. Comment puis-je vous aider aujourd\'hui ?', timestamp: new Date(), senderName: 'Assistant Free', }, ]); const [newMessage, setNewMessage] = useState(''); const [isOnline, setIsOnline] = useState(true); const messagesEndRef = useRef<HTMLDivElement>(null); const scrollToBottom = () => { messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' }); }; useEffect(() => { scrollToBottom(); }, [messages]); const handleSendMessage = () => { if (!newMessage.trim()) return; const userMessage: ChatMessage = { id: Date.now().toString(), sender: 'user', message: newMessage, timestamp: new Date(), senderName: 'Vous', }; setMessages(prev => [...prev, userMessage]); setNewMessage(''); // Simulate bot response setTimeout(() => { const botResponse: ChatMessage = { id: (Date.now() + 1).toString(), sender: 'bot', message: 'Merci pour votre message. Un agent va vous répondre dans quelques instants.', timestamp: new Date(), senderName: 'Assistant Free', }; setMessages(prev => [...prev, botResponse]); }, 1000); }; const handleKeyPress = (event: React.KeyboardEvent) => { if (event.key === 'Enter' && !event.shiftKey) { event.preventDefault(); handleSendMessage(); } }; const getMessageAlignment = (sender: string) => { return sender === 'user' ? 'flex-end' : 'flex-start'; }; const getMessageColor = (sender: string) => { switch (sender) { case 'user': return FREE_MOBILE_COLORS.PRIMARY; case 'agent': return FREE_MOBILE_COLORS.SUCCESS; default: return '#f0f0f0'; } }; const getTextColor = (sender: string) => { return sender === 'user' ? 'white' : 'black'; }; return ( <Card sx={{ height: 600, display: 'flex', flexDirection: 'column' }}> <CardHeader avatar={ <Avatar sx={{ backgroundColor: FREE_MOBILE_COLORS.PRIMARY }}> <AgentIcon /> </Avatar> } title={ <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}> <Typography variant="h6" fontWeight="bold"> Chat en direct </Typography> <Chip icon={<OnlineIcon sx={{ fontSize: '12px !important' }} />} label={isOnline ? 'En ligne' : 'Hors ligne'} size="small" color={isOnline ? 'success' : 'default'} sx={{ height: 20 }} /> </Box> } subheader={ <Typography variant="body2" color="text.secondary"> Support Gratuit • Comment puis-je vous aider ? </Typography> } sx={{ pb: 1 }} /> <Divider /> {/* Messages Area */} <CardContent sx={{ flexGrow: 1, overflow: 'auto', display: 'flex', flexDirection: 'column', gap: 1, p: 2, }} > {messages.map((message) => ( <Box key={message.id} sx={{ display: 'flex', justifyContent: getMessageAlignment(message.sender), mb: 1, }} > <Paper elevation={1} sx={{ p: 1.5, maxWidth: '80%', backgroundColor: getMessageColor(message.sender), color: getTextColor(message.sender), borderRadius: 2, ...(message.sender === 'user' && { borderBottomRightRadius: 4, }), ...(message.sender !== 'user' && { borderBottomLeftRadius: 4, }), }} > <Typography variant="body2" sx={{ wordBreak: 'break-word' }}> {message.message} </Typography> <Typography variant="caption" sx={{ display: 'block', mt: 0.5, opacity: 0.7, fontSize: '0.7rem', }} > {message.timestamp.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit', })} </Typography> </Paper> </Box> ))} <div ref={messagesEndRef} /> </CardContent> <Divider /> {/* Message Input */} <Box sx={{ p: 2 }}> <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}> <TextField fullWidth multiline maxRows={3} placeholder="Tapez votre message..." value={newMessage} onChange={(e) => setNewMessage(e.target.value)} onKeyPress={handleKeyPress} variant="outlined" size="small" disabled={!isOnline} sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2, }, }} /> <IconButton onClick={handleSendMessage} disabled={!newMessage.trim() || !isOnline} sx={{ backgroundColor: FREE_MOBILE_COLORS.PRIMARY, color: 'white', '&:hover': { backgroundColor: '#cc0000', }, '&:disabled': { backgroundColor: 'grey.300', }, }} > <SendIcon /> </IconButton> </Box> <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}> Appuyez sur Entrée pour envoyer </Typography> </Box> </Card> ); }; export default LiveChatWidget;