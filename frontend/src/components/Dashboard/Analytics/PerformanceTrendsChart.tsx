import React, { useState, useMemo, useRef } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Alert,
  CircularProgress,
  Divider,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Tooltip,
  useTheme,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  Speed as SpeedIcon,
  Timer as TimerIcon,
  Star as StarIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
} from '@mui/icons-material';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
  ChartOptions,
  ChartData,
} from 'chart.js';
import { Line } from 'react-chartjs-2';
import { FREE_MOBILE_COLORS } from '../../../utils/constants';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  ChartTooltip,
  Legend
);

interface PerformanceDataPoint {
  date: string;
  satisfaction: number;
  responseTime: number;
  resolutionRate: number;
  escalationRate: number;
}

interface PerformanceData {
  metric: string;
  value: number;
  unit: string;
  trend: 'up' | 'down' | 'stable';
  trendValue: number;
  color: string;
  icon: React.ReactElement;
}

interface PerformanceTrendsChartProps {
  data?: PerformanceDataPoint[];
  loading?: boolean;
  error?: string | null;
  height?: number;
  onRefresh?: () => void;
  onExport?: (format: 'png' | 'pdf' | 'csv') => void;
  onTimeRangeChange?: (range: string) => void;
}

type TimeRange = '24h' | '7d' | '30d' | '90d';

const PerformanceTrendsChart: React.FC<PerformanceTrendsChartProps> = ({
  data = [],
  loading = false,
  error = null,
  height = 400,
  onRefresh,
  onExport,
  onTimeRangeChange,
}) => {
  const theme = useTheme();
  const chartRef = useRef<ChartJS<'line'>>(null);
  const [timeRange, setTimeRange] = useState<TimeRange>('7d');

  // Generate sample chart data if none provided
  const chartData: PerformanceDataPoint[] = useMemo(() => {
    if (data.length > 0) return data;

    const days = timeRange === '24h' ? 24 : timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90;
    const result: PerformanceDataPoint[] = [];

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      if (timeRange === '24h') {
        date.setHours(date.getHours() - i);
      } else {
        date.setDate(date.getDate() - i);
      }

      // Generate realistic performance data with trends
      const satisfaction = Math.max(3.5, Math.min(5.0, 4.5 + (Math.random() - 0.5) * 0.8));
      const responseTime = Math.max(1.0, Math.min(8.0, 2.5 + (Math.random() - 0.5) * 2.0));
      const resolutionRate = Math.max(80, Math.min(98, 92 + (Math.random() - 0.5) * 12));
      const escalationRate = Math.max(2, Math.min(15, 8 + (Math.random() - 0.5) * 6));

      result.push({
        date: timeRange === '24h'
          ? date.toISOString()
          : date.toISOString().split('T')[0],
        satisfaction: Math.round(satisfaction * 10) / 10,
        responseTime: Math.round(responseTime * 10) / 10,
        resolutionRate: Math.round(resolutionRate * 10) / 10,
        escalationRate: Math.round(escalationRate * 10) / 10,
      });
    }

    return result;
  }, [data, timeRange]);

  // Generate summary metrics from chart data
  const sampleData: PerformanceData[] = useMemo(() => {
    const avgSatisfaction = chartData.reduce((sum, item) => sum + item.satisfaction, 0) / chartData.length;
    const avgResponseTime = chartData.reduce((sum, item) => sum + item.responseTime, 0) / chartData.length;
    const avgResolutionRate = chartData.reduce((sum, item) => sum + item.resolutionRate, 0) / chartData.length;
    const avgEscalationRate = chartData.reduce((sum, item) => sum + item.escalationRate, 0) / chartData.length;

    return [
      {
        metric: 'Temps de réponse moyen',
        value: Math.round(avgResponseTime * 10) / 10,
        unit: 'min',
        trend: 'down',
        trendValue: 12,
        color: '#10b981',
        icon: <TimerIcon />,
      },
      {
        metric: 'Taux de résolution',
        value: Math.round(avgResolutionRate * 10) / 10,
        unit: '%',
        trend: 'up',
        trendValue: 3.5,
        color: FREE_MOBILE_COLORS.PRIMARY,
        icon: <SpeedIcon />,
      },
      {
        metric: 'Satisfaction client',
        value: Math.round(avgSatisfaction * 10) / 10,
        unit: '/5',
        trend: 'up',
        trendValue: 0.2,
        color: '#f59e0b',
        icon: <StarIcon />,
      },
      {
        metric: 'Taux d\'escalade',
        value: Math.round(avgEscalationRate * 10) / 10,
        unit: '%',
        trend: 'down',
        trendValue: 1.8,
        color: '#ef4444',
        icon: <TrendingUpIcon />,
      },
    ];
  }, [chartData]);

  const handleTimeRangeChange = (newRange: TimeRange) => {
    setTimeRange(newRange);
    onTimeRangeChange?.(newRange);
  };

  const handleExport = () => {
    if (chartRef.current && onExport) {
      onExport('png');
    }
  };

  const getTrendColor = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return '#10b981';
      case 'down':
        return '#ef4444';
      default:
        return '#6b7280';
    }
  };

  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return '↗';
      case 'down':
        return '↘';
      default:
        return '→';
    }
  };

  // Chart data configuration
  const lineChartData: ChartData<'line'> = useMemo(() => {
    const labels = chartData.map(item => {
      const date = new Date(item.date);
      if (timeRange === '24h') {
        return date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
      } else if (timeRange === '7d') {
        return date.toLocaleDateString('fr-FR', { weekday: 'short', day: 'numeric' });
      } else {
        return date.toLocaleDateString('fr-FR', { day: 'numeric', month: 'short' });
      }
    });

    return {
      labels,
      datasets: [
        {
          label: 'Satisfaction (/5)',
          data: chartData.map(item => item.satisfaction),
          borderColor: '#f59e0b',
          backgroundColor: '#f59e0b20',
          tension: 0.4,
          pointRadius: 4,
          pointHoverRadius: 6,
          pointBackgroundColor: '#f59e0b',
          pointBorderColor: '#ffffff',
          pointBorderWidth: 2,
          borderWidth: 3,
          yAxisID: 'y1',
        },
        {
          label: 'Taux de résolution (%)',
          data: chartData.map(item => item.resolutionRate),
          borderColor: FREE_MOBILE_COLORS.PRIMARY,
          backgroundColor: `${FREE_MOBILE_COLORS.PRIMARY}20`,
          tension: 0.4,
          pointRadius: 3,
          pointHoverRadius: 5,
          pointBackgroundColor: FREE_MOBILE_COLORS.PRIMARY,
          pointBorderColor: '#ffffff',
          pointBorderWidth: 2,
          borderWidth: 2,
          yAxisID: 'y',
        },
        {
          label: 'Temps de réponse (min)',
          data: chartData.map(item => item.responseTime),
          borderColor: '#10b981',
          backgroundColor: '#10b98120',
          tension: 0.4,
          pointRadius: 3,
          pointHoverRadius: 5,
          pointBackgroundColor: '#10b981',
          pointBorderColor: '#ffffff',
          pointBorderWidth: 2,
          borderWidth: 2,
          yAxisID: 'y2',
        },
        {
          label: 'Taux d\'escalade (%)',
          data: chartData.map(item => item.escalationRate),
          borderColor: '#ef4444',
          backgroundColor: '#ef444420',
          tension: 0.4,
          pointRadius: 3,
          pointHoverRadius: 5,
          pointBackgroundColor: '#ef4444',
          pointBorderColor: '#ffffff',
          pointBorderWidth: 2,
          borderWidth: 2,
          yAxisID: 'y',
        },
      ],
    };
  }, [chartData, timeRange]);

  // Chart options configuration
  const chartOptions: ChartOptions<'line'> = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'index' as const,
      intersect: false,
    },
    plugins: {
      legend: {
        position: 'top' as const,
        align: 'end' as const,
        labels: {
          usePointStyle: true,
          pointStyle: 'circle',
          padding: 20,
          font: {
            size: 12,
            weight: 'bold',
          },
        },
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#ffffff',
        bodyColor: '#ffffff',
        borderColor: FREE_MOBILE_COLORS.PRIMARY,
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true,
        callbacks: {
          title: (context) => {
            const date = new Date(chartData[context[0].dataIndex].date);
            return date.toLocaleDateString('fr-FR', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              ...(timeRange === '24h' && { hour: '2-digit', minute: '2-digit' })
            });
          },
          label: (context) => {
            const value = context.parsed.y;
            const label = context.dataset.label;
            if (label?.includes('Satisfaction')) {
              return `${label}: ${value}/5`;
            } else if (label?.includes('Temps')) {
              return `${label}: ${value} min`;
            } else {
              return `${label}: ${value}%`;
            }
          },
        },
      },
    },
    scales: {
      x: {
        display: true,
        grid: {
          display: true,
          color: 'rgba(0, 0, 0, 0.05)',
        },
        ticks: {
          font: {
            size: 11,
          },
          maxTicksLimit: timeRange === '24h' ? 8 : timeRange === '7d' ? 7 : 10,
        },
      },
      y: {
        type: 'linear' as const,
        display: true,
        position: 'left' as const,
        beginAtZero: true,
        max: 100,
        grid: {
          display: true,
          color: 'rgba(0, 0, 0, 0.05)',
        },
        ticks: {
          font: {
            size: 11,
          },
          callback: (value) => `${value}%`,
        },
        title: {
          display: true,
          text: 'Pourcentage (%)',
          font: {
            size: 12,
            weight: 'bold',
          },
        },
      },
      y1: {
        type: 'linear' as const,
        display: true,
        position: 'right' as const,
        min: 0,
        max: 5,
        grid: {
          drawOnChartArea: false,
        },
        ticks: {
          font: {
            size: 11,
          },
          callback: (value) => `${value}/5`,
        },
        title: {
          display: true,
          text: 'Satisfaction (/5)',
          font: {
            size: 12,
            weight: 'bold',
          },
        },
      },
      y2: {
        type: 'linear' as const,
        display: false,
        position: 'right' as const,
        min: 0,
        max: 10,
      },
    },
    elements: {
      point: {
        hoverBorderWidth: 3,
      },
    },
    animation: {
      duration: 1000,
      easing: 'easeInOutQuart',
    },
  }), [chartData, timeRange]);

  if (loading) {
    return (
      <Card elevation={3} sx={{ height, borderRadius: 2 }}>
        <CardContent sx={{ 
          height: '100%', 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center', 
          flexDirection: 'column', 
          gap: 2 
        }}>
          <CircularProgress size={48} sx={{ color: FREE_MOBILE_COLORS.PRIMARY }} />
          <Typography variant="body1" color="text.secondary">
            Chargement des données...
          </Typography>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card elevation={3} sx={{ height, borderRadius: 2 }}>
        <CardContent sx={{ 
          height: '100%', 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center' 
        }}>
          <Alert severity="error" sx={{ width: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Erreur de chargement
            </Typography>
            <Typography variant="body2">
              {error}
            </Typography>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card elevation={3} sx={{ borderRadius: 2, overflow: 'hidden' }}>
      {/* Header */}
      <CardHeader
        title={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <TrendingUpIcon sx={{ color: FREE_MOBILE_COLORS.PRIMARY }} />
            <Typography variant="h6" fontWeight="bold">
              Tendances de performance
            </Typography>
          </Box>
        }
        action={
          <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Période</InputLabel>
              <Select
                value={timeRange}
                label="Période"
                onChange={(e) => handleTimeRangeChange(e.target.value as TimeRange)}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    '&:hover fieldset': {
                      borderColor: FREE_MOBILE_COLORS.PRIMARY,
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: FREE_MOBILE_COLORS.PRIMARY,
                    },
                  },
                }}
              >
                <MenuItem value="24h">24 heures</MenuItem>
                <MenuItem value="7d">7 jours</MenuItem>
                <MenuItem value="30d">30 jours</MenuItem>
                <MenuItem value="90d">3 mois</MenuItem>
              </Select>
            </FormControl>

            <Tooltip title="Actualiser les données">
              <IconButton
                onClick={onRefresh}
                disabled={loading}
                sx={{
                  color: FREE_MOBILE_COLORS.PRIMARY,
                  '&:hover': {
                    backgroundColor: `${FREE_MOBILE_COLORS.PRIMARY}10`,
                  },
                }}
              >
                <RefreshIcon />
              </IconButton>
            </Tooltip>

            <Tooltip title="Exporter le graphique">
              <IconButton
                onClick={handleExport}
                sx={{
                  color: FREE_MOBILE_COLORS.SECONDARY,
                  '&:hover': {
                    backgroundColor: `${FREE_MOBILE_COLORS.SECONDARY}10`,
                  },
                }}
              >
                <DownloadIcon />
              </IconButton>
            </Tooltip>
          </Box>
        }
        sx={{ pb: 1 }}
      />
      <Divider />

      {/* Chart Content */}
      <CardContent sx={{ p: 3 }}>
        {/* Summary Statistics */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          {sampleData.map((metric, index) => (
            <Grid item xs={12} sm={6} md={3} key={metric.metric}>
              <Card
                variant="outlined"
                sx={{
                  height: '100%',
                  border: `2px solid ${metric.color}20`,
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: 4,
                  },
                  transition: 'all 0.2s ease-in-out',
                }}
              >
                <CardContent sx={{ textAlign: 'center' }}>
                  <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
                    <Box
                      sx={{
                        width: 56,
                        height: 56,
                        borderRadius: '50%',
                        backgroundColor: `${metric.color}20`,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: metric.color,
                      }}
                    >
                      {metric.icon}
                    </Box>
                  </Box>
                  
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {metric.metric}
                  </Typography>
                  
                  <Typography variant="h4" fontWeight="bold" color={metric.color} gutterBottom>
                    {metric.value}{metric.unit}
                  </Typography>
                  
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 0.5 }}>
                    <Typography
                      variant="body2"
                      sx={{
                        color: getTrendColor(metric.trend),
                        fontWeight: 'bold',
                        display: 'flex',
                        alignItems: 'center',
                        gap: 0.5,
                      }}
                    >
                      <span style={{ fontSize: '1.2em' }}>
                        {getTrendIcon(metric.trend)}
                      </span>
                      {metric.trend === 'up' ? '+' : metric.trend === 'down' ? '-' : ''}
                      {metric.trendValue}
                      {metric.unit === '%' ? 'pts' : metric.unit === '/5' ? '' : '%'}
                    </Typography>
                  </Box>
                  
                  <Typography variant="caption" color="text.secondary">
                    vs période précédente
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        {/* Chart Container */}
        <Box
          sx={{
            height: height - 300,
            position: 'relative',
            borderRadius: 2,
            backgroundColor: 'background.paper',
            p: 2,
            mb: 3,
            border: `1px solid ${theme.palette.divider}`,
          }}
        >
          <Line
            ref={chartRef}
            data={lineChartData}
            options={chartOptions}
          />
        </Box>

        <Box sx={{ mt: 4 }}>
          <Typography variant="h6" gutterBottom>
            Analyse des tendances ({timeRange === '24h' ? '24 dernières heures' :
             timeRange === '7d' ? '7 derniers jours' :
             timeRange === '30d' ? '30 derniers jours' : '3 derniers mois'})
          </Typography>
          
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Typography variant="body2" color="text.secondary" paragraph>
                <strong>Points positifs:</strong>
              </Typography>
              <ul style={{ margin: 0, paddingLeft: 20 }}>
                {sampleData
                  .filter(m => m.trend === 'up' || (m.trend === 'down' && m.metric.includes('escalade')))
                  .map(m => (
                    <li key={m.metric}>
                      <Typography variant="body2" color="text.secondary">
                        {m.metric}: {m.trend === 'up' ? 'amélioration' : 'réduction'} de {m.trendValue}
                        {m.unit === '%' ? 'pts' : m.unit === '/5' ? '' : '%'}
                      </Typography>
                    </li>
                  ))}
              </ul>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Typography variant="body2" color="text.secondary" paragraph>
                <strong>Points d'attention:</strong>
              </Typography>
              <ul style={{ margin: 0, paddingLeft: 20 }}>
                {sampleData
                  .filter(m => m.trend === 'down' && !m.metric.includes('escalade'))
                  .map(m => (
                    <li key={m.metric}>
                      <Typography variant="body2" color="text.secondary">
                        {m.metric}: dégradation de {m.trendValue}
                        {m.unit === '%' ? 'pts' : m.unit === '/5' ? '' : '%'}
                      </Typography>
                    </li>
                  ))}
              </ul>
            </Grid>
          </Grid>
        </Box>
      </CardContent>
    </Card>
  );
};

export default PerformanceTrendsChart;
