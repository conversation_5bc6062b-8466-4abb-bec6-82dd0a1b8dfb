/**
 * Drill-down Modal Component
 * Provides detailed data views when chart elements are clicked
 * Supports multiple chart types and data visualization modes
 */

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Tabs,
  Tab,
  CircularProgress,
  Alert,
  Divider,
} from '@mui/material';
import {
  Close as CloseIcon,
  TrendingUp as TrendingUpIcon,
  Assessment as AssessmentIcon,
  TableChart as TableIcon,
  Timeline as TimelineIcon,
} from '@mui/icons-material';
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, BarElement, Title, Tooltip, Legend } from 'chart.js';
import { Line, Bar } from 'react-chartjs-2';
import { useTheme } from '@mui/material/styles';
import { FREE_MOBILE_COLORS } from '../../../utils/constants';

// Register Chart.js components
ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, BarElement, Title, Tooltip, Legend);

interface DrillDownData {
  type: 'volume' | 'category' | 'performance';
  title: string;
  subtitle?: string;
  selectedValue: string | number;
  selectedLabel: string;
  timeSeriesData?: Array<{
    date: string;
    value: number;
    label?: string;
  }>;
  detailData?: Array<{
    id: string;
    label: string;
    value: number;
    percentage?: number;
    trend?: 'up' | 'down' | 'stable';
    metadata?: Record<string, any>;
  }>;
  summaryStats?: {
    total: number;
    average: number;
    peak: number;
    growth?: number;
  };
}

interface DrillDownModalProps {
  open: boolean;
  onClose: () => void;
  data: DrillDownData | null;
  loading?: boolean;
}

const DrillDownModal: React.FC<DrillDownModalProps> = ({
  open,
  onClose,
  data,
  loading = false
}) => {
  const theme = useTheme();
  const [activeTab, setActiveTab] = useState(0);

  useEffect(() => {
    if (open) {
      setActiveTab(0);
    }
  }, [open]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const getTrendIcon = (trend?: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return <TrendingUpIcon sx={{ color: FREE_MOBILE_COLORS.SUCCESS, fontSize: 16 }} />;
      case 'down':
        return <TrendingUpIcon sx={{ color: FREE_MOBILE_COLORS.ERROR, fontSize: 16, transform: 'rotate(180deg)' }} />;
      case 'stable':
        return <TrendingUpIcon sx={{ color: FREE_MOBILE_COLORS.WARNING, fontSize: 16, transform: 'rotate(90deg)' }} />;
      default:
        return null;
    }
  };

  const renderTimeSeriesChart = () => {
    if (!data?.timeSeriesData) return null;

    const chartData = {
      labels: data.timeSeriesData.map(item => new Date(item.date).toLocaleDateString('fr-FR')),
      datasets: [
        {
          label: data.selectedLabel,
          data: data.timeSeriesData.map(item => item.value),
          borderColor: FREE_MOBILE_COLORS.PRIMARY,
          backgroundColor: `${FREE_MOBILE_COLORS.PRIMARY}20`,
          fill: true,
          tension: 0.4,
        },
      ],
    };

    const options = {
      responsive: true,
      plugins: {
        legend: {
          position: 'top' as const,
        },
        title: {
          display: true,
          text: `Évolution temporelle - ${data.selectedLabel}`,
        },
      },
      scales: {
        y: {
          beginAtZero: true,
        },
      },
    };

    return (
      <Box sx={{ height: 300, mb: 2 }}>
        <Line data={chartData} options={options} />
      </Box>
    );
  };

  const renderDetailTable = () => {
    if (!data?.detailData) return null;

    return (
      <TableContainer component={Paper} sx={{ maxHeight: 400 }}>
        <Table stickyHeader>
          <TableHead>
            <TableRow>
              <TableCell>Élément</TableCell>
              <TableCell align="right">Valeur</TableCell>
              <TableCell align="right">Pourcentage</TableCell>
              <TableCell align="center">Tendance</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {data.detailData.map((item) => (
              <TableRow key={item.id} hover>
                <TableCell>
                  <Typography variant="body2" fontWeight="medium">
                    {item.label}
                  </Typography>
                </TableCell>
                <TableCell align="right">
                  <Typography variant="body2">
                    {typeof item.value === 'number' ? item.value.toLocaleString('fr-FR') : item.value}
                  </Typography>
                </TableCell>
                <TableCell align="right">
                  {item.percentage && (
                    <Chip
                      label={`${item.percentage.toFixed(1)}%`}
                      size="small"
                      color={item.percentage > 50 ? 'primary' : 'default'}
                    />
                  )}
                </TableCell>
                <TableCell align="center">
                  {getTrendIcon(item.trend)}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    );
  };

  const renderSummaryStats = () => {
    if (!data?.summaryStats) return null;

    const stats = [
      { label: 'Total', value: data.summaryStats.total, color: FREE_MOBILE_COLORS.PRIMARY },
      { label: 'Moyenne', value: data.summaryStats.average, color: FREE_MOBILE_COLORS.SECONDARY },
      { label: 'Pic', value: data.summaryStats.peak, color: FREE_MOBILE_COLORS.SUCCESS },
    ];

    if (data.summaryStats.growth !== undefined) {
      stats.push({
        label: 'Croissance',
        value: data.summaryStats.growth,
        color: data.summaryStats.growth >= 0 ? FREE_MOBILE_COLORS.SUCCESS : FREE_MOBILE_COLORS.ERROR,
      });
    }

    return (
      <Box sx={{ display: 'flex', gap: 2, mb: 3, flexWrap: 'wrap' }}>
        {stats.map((stat, index) => (
          <Paper
            key={index}
            sx={{
              p: 2,
              minWidth: 120,
              textAlign: 'center',
              borderLeft: `4px solid ${stat.color}`,
            }}
          >
            <Typography variant="h6" color={stat.color} fontWeight="bold">
              {typeof stat.value === 'number' 
                ? stat.label === 'Croissance' 
                  ? `${stat.value > 0 ? '+' : ''}${stat.value.toFixed(1)}%`
                  : stat.value.toLocaleString('fr-FR')
                : stat.value
              }
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {stat.label}
            </Typography>
          </Paper>
        ))}
      </Box>
    );
  };

  if (!data) return null;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          minHeight: '60vh',
          maxHeight: '90vh',
        },
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box>
            <Typography variant="h6" component="div">
              {data.title}
            </Typography>
            {data.subtitle && (
              <Typography variant="body2" color="text.secondary">
                {data.subtitle}
              </Typography>
            )}
          </Box>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <Box sx={{ mb: 2 }}>
              <Chip
                label={`Sélection: ${data.selectedLabel} (${data.selectedValue})`}
                color="primary"
                variant="outlined"
                size="medium"
              />
            </Box>

            <Divider sx={{ mb: 2 }} />

            {renderSummaryStats()}

            <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 2 }}>
              <Tab
                icon={<TimelineIcon />}
                label="Évolution temporelle"
                disabled={!data.timeSeriesData}
              />
              <Tab
                icon={<TableIcon />}
                label="Données détaillées"
                disabled={!data.detailData}
              />
              <Tab
                icon={<AssessmentIcon />}
                label="Analyse"
              />
            </Tabs>

            {activeTab === 0 && data.timeSeriesData && renderTimeSeriesChart()}
            {activeTab === 1 && data.detailData && renderDetailTable()}
            {activeTab === 2 && (
              <Alert severity="info">
                <Typography variant="body2">
                  Analyse détaillée pour "{data.selectedLabel}" sera disponible dans une prochaine version.
                </Typography>
              </Alert>
            )}
          </>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} variant="outlined">
          Fermer
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DrillDownModal;
