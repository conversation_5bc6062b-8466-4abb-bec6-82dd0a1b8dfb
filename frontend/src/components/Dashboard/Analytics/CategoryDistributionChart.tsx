import React, { useMemo, useRef, useEffect, useState, useCallback } from 'react';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Alert,
  Grid,
  Chip,
  IconButton,
  Tooltip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  useTheme,
} from '@mui/material';
import {
  PieChart as PieChartIcon,
  Refresh as RefreshIcon,
  Circle as CircleIcon,
} from '@mui/icons-material';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip as ChartTooltip,
  Legend,
  ChartOptions,
  ChartData,
} from 'chart.js';
import { Doughnut } from 'react-chartjs-2';
import { FREE_MOBILE_COLORS } from '../../../utils/constants';
import analyticsService, { CategoryData } from '../../../services/analytics.service';
import ChartSkeleton from './ChartSkeleton';
import ExportMenu from './ExportMenu';
import DrillDownModal from './DrillDownModal';

// Register Chart.js components
ChartJS.register(ArcElement, ChartTooltip, Legend);

interface CategoryDistributionChartProps {
  data?: CategoryData[];
  loading?: boolean;
  error?: string | null;
  height?: number;
  onRefresh?: () => void;
  onExport?: (format: 'png' | 'pdf' | 'csv') => void;
}

const CategoryDistributionChart: React.FC<CategoryDistributionChartProps> = ({
  data: propData = [],
  loading: propLoading = false,
  error: propError = null,
  height = 400,
  onRefresh,
  onExport,
}) => {
  const theme = useTheme();
  const chartRef = useRef<ChartJS<'doughnut'>>(null);

  // Internal state for data fetching
  const [data, setData] = useState<CategoryData[]>(propData);
  const [loading, setLoading] = useState(propLoading);
  const [error, setError] = useState<string | null>(propError);

  // Drill-down state
  const [drillDownOpen, setDrillDownOpen] = useState(false);
  const [drillDownData, setDrillDownData] = useState<any>(null);
  const [drillDownLoading, setDrillDownLoading] = useState(false);

  // Fetch data from API
  const fetchData = async () => {
    if (propData.length > 0) return; // Use prop data if provided

    setLoading(true);
    setError(null);

    try {
      const categoryData = await analyticsService.getCategoryData();
      setData(categoryData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch category data';
      setError(errorMessage);
      console.error('Error fetching category data:', err);

      // Fallback to sample data on error
      setData(generateSampleData());
    } finally {
      setLoading(false);
    }
  };

  // Effect to fetch data when component mounts
  useEffect(() => {
    fetchData();
  }, []);

  // Update internal state when props change
  useEffect(() => {
    if (propData.length > 0) {
      setData(propData);
      setLoading(propLoading);
      setError(propError);
    }
  }, [propData, propLoading, propError]);

  // Generate sample data fallback
  const generateSampleData = (): CategoryData[] => {
    const categories = [
      { category: 'Support technique', count: 456, color: FREE_MOBILE_COLORS.PRIMARY },
      { category: 'Facturation', count: 312, color: FREE_MOBILE_COLORS.SUCCESS },
      { category: 'Forfaits & Abonnements', count: 289, color: FREE_MOBILE_COLORS.INFO },
      { category: 'Réseau mobile', count: 190, color: FREE_MOBILE_COLORS.WARNING },
      { category: 'Équipement', count: 143, color: theme.palette.secondary.main },
      { category: 'Internet fixe', count: 98, color: theme.palette.error.main },
      { category: 'Autres', count: 67, color: '#6b7280' },
    ];

    const total = categories.reduce((sum, cat) => sum + cat.count, 0);

    return categories.map(cat => ({
      ...cat,
      percentage: Math.round((cat.count / total) * 100),
    }));
  };

  // Use real data or sample data
  const chartData: ChartData<'doughnut'> = useMemo(() => {
    const currentData = data.length > 0 ? data : generateSampleData();

    return {
      labels: currentData.map(item => item.category),
      datasets: [
        {
          data: currentData.map(item => item.count),
          backgroundColor: currentData.map(item => item.color),
          borderColor: currentData.map(item => item.color),
          borderWidth: 2,
          hoverBorderWidth: 4,
          hoverOffset: 8,
          cutout: '60%',
        },
      ],
    };
  }, [data, theme]);

  // Handle drill-down click
  const handleChartClick = useCallback(async (event: any, elements: any[]) => {
    if (elements.length === 0) return;

    const elementIndex = elements[0].index;
    const currentData = data.length > 0 ? data : generateSampleData();
    const clickedData = currentData[elementIndex];

    if (!clickedData) return;

    setDrillDownLoading(true);
    setDrillDownOpen(true);

    try {
      // Generate detailed drill-down data for category
      const detailData = [
        { id: 'total', label: 'Total des tickets', value: clickedData.count, percentage: 100, trend: 'stable' as const },
        { id: 'resolved', label: 'Tickets résolus', value: Math.floor(clickedData.count * 0.7), percentage: 70, trend: 'up' as const },
        { id: 'pending', label: 'Tickets en attente', value: Math.floor(clickedData.count * 0.2), percentage: 20, trend: 'stable' as const },
        { id: 'escalated', label: 'Tickets escaladés', value: Math.floor(clickedData.count * 0.1), percentage: 10, trend: 'down' as const },
      ];

      // Generate time series data for this category
      const timeSeriesData = Array.from({ length: 30 }, (_, i) => ({
        date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString(),
        value: Math.floor(Math.random() * clickedData.count * 0.2) + clickedData.count * 0.8,
      }));

      const drillDownInfo = {
        type: 'category' as const,
        title: 'Analyse détaillée par catégorie',
        subtitle: `Données pour la catégorie "${clickedData.category}"`,
        selectedValue: clickedData.count,
        selectedLabel: clickedData.category,
        timeSeriesData,
        detailData,
        summaryStats: {
          total: clickedData.count,
          average: Math.round(clickedData.count / 30),
          peak: Math.floor(clickedData.count * 1.2),
          growth: Math.random() * 30 - 15, // Mock growth percentage
        },
      };

      setDrillDownData(drillDownInfo);
    } catch (error) {
      console.error('Error loading drill-down data:', error);
    } finally {
      setDrillDownLoading(false);
    }
  }, [data]);

  // Chart options configuration
  const chartOptions: ChartOptions<'doughnut'> = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false, // We'll create a custom legend
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#ffffff',
        bodyColor: '#ffffff',
        borderColor: FREE_MOBILE_COLORS.PRIMARY,
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true,
        callbacks: {
          label: (context) => {
            const currentData = data.length > 0 ? data : generateSampleData();
            const item = currentData[context.dataIndex];
            return [
              `${item.category}: ${item.count} tickets`,
              `Pourcentage: ${item.percentage}%`
            ];
          },
        },
      },
    },
    animation: {
      animateRotate: true,
      animateScale: true,
      duration: 1000,
      easing: 'easeInOutQuart',
    },
    onHover: (event, elements) => {
      if (event.native?.target) {
        (event.native.target as HTMLElement).style.cursor = elements.length > 0 ? 'pointer' : 'default';
      }
    },
    onClick: handleChartClick,
  }), [data, theme]);

  const currentData = data.length > 0 ? data : generateSampleData();
  const totalTickets = currentData.reduce((sum, item) => sum + item.count, 0);

  const handleRefresh = () => {
    fetchData();
    onRefresh?.();
  };





  if (loading) {
    return (
      <ChartSkeleton
        height={height}
        showHeader={true}
        showLegend={true}
        variant="doughnut"
      />
    );
  }

  if (error) {
    return (
      <Card elevation={4} sx={{ height, borderRadius: 3, overflow: 'hidden' }}>
        <CardContent sx={{
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          p: 3,
        }}>
          <Alert
            severity="error"
            sx={{
              width: '100%',
              borderRadius: 2,
            }}
            action={
              <IconButton
                aria-label="retry"
                color="inherit"
                size="small"
                onClick={handleRefresh}
              >
                <RefreshIcon />
              </IconButton>
            }
          >
            <Typography variant="h6" gutterBottom>
              Erreur de chargement
            </Typography>
            <Typography variant="body2">
              {error}
            </Typography>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card
      elevation={4}
      sx={{
        borderRadius: 3,
        overflow: 'hidden',
        transition: 'all 0.3s ease',
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: 6,
        },
      }}
    >
      {/* Enhanced Header */}
      <CardHeader
        title={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <PieChartIcon sx={{ color: FREE_MOBILE_COLORS.PRIMARY, fontSize: 28 }} />
            <Typography variant="h6" fontWeight="bold">
              Distribution par Catégorie
            </Typography>
          </Box>
        }
        subheader={
          <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
            Total: {totalTickets.toLocaleString()} tickets analysés
          </Typography>
        }
        action={
          <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
            <Chip
              label={`${currentData.length} catégories`}
              size="small"
              color="primary"
              variant="outlined"
              sx={{ fontWeight: 'bold' }}
            />

            <Tooltip title="Actualiser les données">
              <IconButton
                onClick={handleRefresh}
                disabled={loading}
                sx={{
                  color: FREE_MOBILE_COLORS.PRIMARY,
                  '&:hover': {
                    backgroundColor: `${FREE_MOBILE_COLORS.PRIMARY}10`,
                  },
                }}
              >
                <RefreshIcon />
              </IconButton>
            </Tooltip>

            <ExportMenu
              chartElement={chartRef.current?.canvas || null}
              chartData={{
                labels: currentData.map(item => item.category),
                datasets: [{
                  label: 'Tickets par catégorie',
                  data: currentData.map(item => item.count),
                  backgroundColor: currentData.map(item => item.color),
                }],
              }}
              defaultFilename="categories-distribution"
              defaultTitle="Distribution des Catégories - Free Mobile"
            />
          </Box>
        }
        sx={{
          pb: 1,
          backgroundColor: `${FREE_MOBILE_COLORS.PRIMARY}05`,
          borderBottom: `1px solid ${theme.palette.divider}`,
        }}
      />

      {/* Chart Content */}
      <CardContent sx={{ p: 3 }}>
        <Grid container spacing={3}>
          {/* Chart Section */}
          <Grid item xs={12} md={7}>
            <Box
              sx={{
                height: height - 180,
                position: 'relative',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Doughnut
                ref={chartRef}
                data={chartData}
                options={chartOptions}
              />

              {/* Center Label */}
              <Box
                sx={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  textAlign: 'center',
                  pointerEvents: 'none',
                }}
              >
                <Typography variant="h4" fontWeight="bold" color={FREE_MOBILE_COLORS.PRIMARY}>
                  {totalTickets.toLocaleString()}
                </Typography>
                <Typography variant="body2" color="text.secondary" fontWeight="medium">
                  Total tickets
                </Typography>
              </Box>
            </Box>
          </Grid>

          {/* Legend Section */}
          <Grid item xs={12} md={5}>
            <Typography variant="h6" gutterBottom fontWeight="bold" sx={{ mb: 2 }}>
              Répartition détaillée
            </Typography>

            <List dense sx={{ maxHeight: height - 220, overflow: 'auto' }}>
              {currentData.map((category, index) => (
                <ListItem
                  key={category.category}
                  sx={{
                    borderRadius: 2,
                    mb: 1,
                    backgroundColor: `${category.color}08`,
                    border: `1px solid ${category.color}20`,
                    '&:hover': {
                      backgroundColor: `${category.color}15`,
                    },
                  }}
                >
                  <ListItemIcon>
                    <CircleIcon sx={{ color: category.color, fontSize: 16 }} />
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="body2" fontWeight="medium">
                          {category.category}
                        </Typography>
                        <Chip
                          label={`${category.percentage}%`}
                          size="small"
                          sx={{
                            backgroundColor: category.color,
                            color: 'white',
                            fontWeight: 'bold',
                            fontSize: '0.75rem',
                          }}
                        />
                      </Box>
                    }
                    secondary={
                      <Typography variant="body2" color="text.secondary">
                        {category.count.toLocaleString()} tickets
                      </Typography>
                    }
                  />
                </ListItem>
              ))}
            </List>
          </Grid>
        </Grid>

        {/* Summary Statistics */}
        <Box sx={{
          mt: 3,
          p: 2,
          borderRadius: 2,
          backgroundColor: 'grey.50',
        }}>
          <Typography variant="subtitle2" gutterBottom fontWeight="bold">
            📊 Analyse des tendances
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" color="text.secondary">
                <strong>Catégorie dominante:</strong> {currentData[0]?.category} ({currentData[0]?.percentage}%)
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" color="text.secondary">
                <strong>Top 3 catégories:</strong> {currentData.slice(0, 3).reduce((sum, c) => sum + c.percentage, 0)}% du total
              </Typography>
            </Grid>
          </Grid>
        </Box>
      </CardContent>

      <DrillDownModal
        open={drillDownOpen}
        onClose={() => setDrillDownOpen(false)}
        data={drillDownData}
        loading={drillDownLoading}
      />
    </Card>
  );
};

export default CategoryDistributionChart;
