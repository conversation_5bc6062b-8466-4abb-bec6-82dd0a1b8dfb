import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  CardHeader,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Divider,
} from '@mui/material';
import {
  Timeline as TimelineIcon,
} from '@mui/icons-material';
import { FREE_MOBILE_COLORS } from '../../../utils/constants';

interface VolumeDataPoint {
  date: string;
  tickets: number;
  resolved: number;
  pending: number;
  escalated: number;
}

interface VolumeChartProps {
  data?: VolumeDataPoint[];
  loading?: boolean;
  error?: string | null;
  height?: number;
  onRefresh?: () => void;
  onExport?: (format: 'png' | 'pdf' | 'csv') => void;
  onTimeRangeChange?: (range: string) => void;
}

type TimeRange = '7d' | '30d' | '90d' | '1y';

const VolumeChart: React.FC<VolumeChartProps> = ({
  data = [],
  loading = false,
  error = null,
  height = 400,
  onRefresh,
  onExport,
  onTimeRangeChange,
}) => {
  const [timeRange, setTimeRange] = useState<TimeRange>('30d');

  // Generate sample data if none provided
  const sampleData: VolumeDataPoint[] = React.useMemo(() => {
    if (data.length > 0) return data;

    const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : timeRange === '90d' ? 90 : 365;
    const result: VolumeDataPoint[] = [];

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const baseTickets = Math.floor(Math.random() * 50) + 20;
      const resolved = Math.floor(baseTickets * (0.7 + Math.random() * 0.2));
      const pending = Math.floor((baseTickets - resolved) * 0.6);
      const escalated = baseTickets - resolved - pending;

      result.push({
        date: date.toISOString().split('T')[0],
        tickets: baseTickets,
        resolved,
        pending,
        escalated,
      });
    }

    return result;
  }, [data, timeRange]);

  const handleTimeRangeChange = (newRange: TimeRange) => {
    setTimeRange(newRange);
    onTimeRangeChange?.(newRange);
  };

  if (loading) {
    return (
      <Card elevation={3} sx={{ height, borderRadius: 2 }}>
        <CardContent sx={{ 
          height: '100%', 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center', 
          flexDirection: 'column', 
          gap: 2 
        }}>
          <CircularProgress size={48} sx={{ color: FREE_MOBILE_COLORS.PRIMARY }} />
          <Typography variant="body1" color="text.secondary">
            Chargement des données...
          </Typography>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card elevation={3} sx={{ height, borderRadius: 2 }}>
        <CardContent sx={{ 
          height: '100%', 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center' 
        }}>
          <Alert severity="error" sx={{ width: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Erreur de chargement
            </Typography>
            <Typography variant="body2">
              {error}
            </Typography>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  const totalTickets = sampleData.reduce((sum, item) => sum + item.tickets, 0);
  const totalResolved = sampleData.reduce((sum, item) => sum + item.resolved, 0);
  const totalPending = sampleData.reduce((sum, item) => sum + item.pending, 0);
  const totalEscalated = sampleData.reduce((sum, item) => sum + item.escalated, 0);

  return (
    <Card elevation={3} sx={{ borderRadius: 2, overflow: 'hidden' }}>
      {/* Header with controls */}
      <CardHeader
        title={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <TimelineIcon sx={{ color: FREE_MOBILE_COLORS.PRIMARY }} />
            <Typography variant="h6" fontWeight="bold">
              Graphique des volumes
            </Typography>
          </Box>
        }
        action={
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Période</InputLabel>
            <Select
              value={timeRange}
              label="Période"
              onChange={(e) => handleTimeRangeChange(e.target.value as TimeRange)}
            >
              <MenuItem value="7d">7 jours</MenuItem>
              <MenuItem value="30d">30 jours</MenuItem>
              <MenuItem value="90d">3 mois</MenuItem>
              <MenuItem value="1y">1 an</MenuItem>
            </Select>
          </FormControl>
        }
        sx={{ pb: 1 }}
      />
      <Divider />

      {/* Chart Content - Simplified for now */}
      <CardContent sx={{ p: 3 }}>
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="body2">
            Graphique des volumes - Interface simplifiée. Les graphiques Chart.js seront 
            intégrés dans une version ultérieure.
          </Typography>
        </Alert>

        <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 2 }}>
          <Card variant="outlined">
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color={FREE_MOBILE_COLORS.PRIMARY} fontWeight="bold">
                {totalTickets}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total des tickets
              </Typography>
            </CardContent>
          </Card>

          <Card variant="outlined">
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="#10b981" fontWeight="bold">
                {totalResolved}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Tickets résolus
              </Typography>
            </CardContent>
          </Card>

          <Card variant="outlined">
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="#f59e0b" fontWeight="bold">
                {totalPending}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Tickets en attente
              </Typography>
            </CardContent>
          </Card>

          <Card variant="outlined">
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="#ef4444" fontWeight="bold">
                {totalEscalated}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Tickets escaladés
              </Typography>
            </CardContent>
          </Card>
        </Box>

        <Box sx={{ mt: 3 }}>
          <Typography variant="h6" gutterBottom>
            Données de la période ({timeRange === '7d' ? '7 derniers jours' : 
             timeRange === '30d' ? '30 derniers jours' : 
             timeRange === '90d' ? '3 derniers mois' : 'Dernière année'})
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Taux de résolution: {totalTickets > 0 ? Math.round((totalResolved / totalTickets) * 100) : 0}%
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Taux d'escalade: {totalTickets > 0 ? Math.round((totalEscalated / totalTickets) * 100) : 0}%
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
};

export default VolumeChart;
