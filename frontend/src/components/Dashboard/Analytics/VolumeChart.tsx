import React, { useState, useMemo, useRef, useEffect, useCallback } from 'react';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  IconButton,
  Tooltip,
  Chip,
  useTheme,
} from '@mui/material';
import {
  Timeline as TimelineIcon,
  Refresh as RefreshIcon,
  TrendingUp as TrendingUpIcon,
} from '@mui/icons-material';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
  Filler,
  ChartOptions,
  ChartData,
} from 'chart.js';
import { Line } from 'react-chartjs-2';
import { FREE_MOBILE_COLORS } from '../../../utils/constants';
import analyticsService, { VolumeData } from '../../../services/analytics.service';
import ChartSkeleton from './ChartSkeleton';
import ExportMenu from './ExportMenu';
import DrillDownModal from './DrillDownModal';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  ChartTooltip,
  Legend,
  Filler
);

interface VolumeChartProps {
  data?: VolumeData[];
  loading?: boolean;
  error?: string | null;
  height?: number;
  onRefresh?: () => void;
  onExport?: (format: 'png' | 'pdf' | 'csv') => void;
  onTimeRangeChange?: (range: string) => void;
}

type TimeRange = '7d' | '30d' | '90d' | '1y';

const VolumeChart: React.FC<VolumeChartProps> = ({
  data: propData = [],
  loading: propLoading = false,
  error: propError = null,
  height = 400,
  onRefresh,
  onExport,
  onTimeRangeChange,
}) => {
  const theme = useTheme();
  const chartRef = useRef<ChartJS<'line'>>(null);
  const [timeRange, setTimeRange] = useState<TimeRange>('30d');
  const [chartType] = useState<'line' | 'area'>('area');

  // Internal state for data fetching
  const [data, setData] = useState<VolumeData[]>(propData);
  const [loading, setLoading] = useState(propLoading);
  const [error, setError] = useState<string | null>(propError);

  // Drill-down state
  const [drillDownOpen, setDrillDownOpen] = useState(false);
  const [drillDownData, setDrillDownData] = useState<any>(null);
  const [drillDownLoading, setDrillDownLoading] = useState(false);

  // Fetch data from API
  const fetchData = async (selectedTimeRange: string) => {
    if (propData.length > 0) return; // Use prop data if provided

    setLoading(true);
    setError(null);

    try {
      const volumeData = await analyticsService.getVolumeData(selectedTimeRange);
      setData(volumeData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch volume data';
      setError(errorMessage);
      console.error('Error fetching volume data:', err);

      // Fallback to sample data on error
      setData(generateSampleData(selectedTimeRange));
    } finally {
      setLoading(false);
    }
  };

  // Effect to fetch data when component mounts or time range changes
  useEffect(() => {
    fetchData(timeRange);
  }, [timeRange]);

  // Update internal state when props change
  useEffect(() => {
    if (propData.length > 0) {
      setData(propData);
      setLoading(propLoading);
      setError(propError);
    }
  }, [propData, propLoading, propError]);

  // Generate sample data fallback
  const generateSampleData = (selectedTimeRange: string): VolumeData[] => {
    const days = selectedTimeRange === '7d' ? 7 : selectedTimeRange === '30d' ? 30 : selectedTimeRange === '90d' ? 90 : 365;
    const result: VolumeData[] = [];

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);

      // More realistic data generation with trends
      const dayOfWeek = date.getDay();
      const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
      const baseMultiplier = isWeekend ? 0.6 : 1.0;

      const baseTickets = Math.floor((Math.random() * 30 + 40) * baseMultiplier);
      const resolved = Math.floor(baseTickets * (0.7 + Math.random() * 0.2));
      const pending = Math.floor((baseTickets - resolved) * (0.6 + Math.random() * 0.3));
      const escalated = baseTickets - resolved - pending;

      result.push({
        date: date.toISOString().split('T')[0],
        total: baseTickets,
        resolved,
        pending,
        escalated: Math.max(0, escalated),
      });
    }

    return result;
  };

  // Use real data or sample data
  const chartData: VolumeData[] = useMemo(() => {
    if (data.length > 0) return data;
    return generateSampleData(timeRange);
  }, [data, timeRange]);

  // Chart data configuration
  const lineChartData: ChartData<'line'> = useMemo(() => {
    const labels = chartData.map(item => {
      const date = new Date(item.date);
      if (timeRange === '7d') {
        return date.toLocaleDateString('fr-FR', { weekday: 'short', day: 'numeric' });
      } else if (timeRange === '30d') {
        return date.toLocaleDateString('fr-FR', { day: 'numeric', month: 'short' });
      } else {
        return date.toLocaleDateString('fr-FR', { day: 'numeric', month: 'short' });
      }
    });

    return {
      labels,
      datasets: [
        {
          label: 'Total des tickets',
          data: chartData.map(item => item.total),
          borderColor: FREE_MOBILE_COLORS.PRIMARY,
          backgroundColor: chartType === 'area'
            ? `${FREE_MOBILE_COLORS.PRIMARY}20`
            : FREE_MOBILE_COLORS.PRIMARY,
          fill: chartType === 'area',
          tension: 0.4,
          pointRadius: 4,
          pointHoverRadius: 6,
          pointBackgroundColor: FREE_MOBILE_COLORS.PRIMARY,
          pointBorderColor: '#ffffff',
          pointBorderWidth: 2,
          borderWidth: 3,
        },
        {
          label: 'Tickets résolus',
          data: chartData.map(item => item.resolved),
          borderColor: FREE_MOBILE_COLORS.SUCCESS,
          backgroundColor: chartType === 'area'
            ? `${FREE_MOBILE_COLORS.SUCCESS}20`
            : FREE_MOBILE_COLORS.SUCCESS,
          fill: chartType === 'area',
          tension: 0.4,
          pointRadius: 3,
          pointHoverRadius: 5,
          pointBackgroundColor: FREE_MOBILE_COLORS.SUCCESS,
          pointBorderColor: '#ffffff',
          pointBorderWidth: 2,
          borderWidth: 2,
        },
        {
          label: 'Tickets en attente',
          data: chartData.map(item => item.pending),
          borderColor: FREE_MOBILE_COLORS.WARNING,
          backgroundColor: chartType === 'area'
            ? `${FREE_MOBILE_COLORS.WARNING}20`
            : FREE_MOBILE_COLORS.WARNING,
          fill: chartType === 'area',
          tension: 0.4,
          pointRadius: 3,
          pointHoverRadius: 5,
          pointBackgroundColor: FREE_MOBILE_COLORS.WARNING,
          pointBorderColor: '#ffffff',
          pointBorderWidth: 2,
          borderWidth: 2,
        },
        {
          label: 'Tickets escaladés',
          data: chartData.map(item => item.escalated),
          borderColor: theme.palette.error.main,
          backgroundColor: chartType === 'area'
            ? `${theme.palette.error.main}20`
            : theme.palette.error.main,
          fill: chartType === 'area',
          tension: 0.4,
          pointRadius: 3,
          pointHoverRadius: 5,
          pointBackgroundColor: theme.palette.error.main,
          pointBorderColor: '#ffffff',
          pointBorderWidth: 2,
          borderWidth: 2,
        },
      ],
    };
  }, [chartData, timeRange, chartType, theme]);

  // Handle drill-down click
  const handleChartClick = useCallback(async (event: any, elements: any[]) => {
    if (elements.length === 0) return;

    const elementIndex = elements[0].index;
    const clickedData = chartData[elementIndex];

    if (!clickedData) return;

    setDrillDownLoading(true);
    setDrillDownOpen(true);

    try {
      // Generate detailed drill-down data
      const detailData = [
        { id: 'total', label: 'Total des tickets', value: clickedData.total, percentage: 100, trend: 'stable' as const },
        { id: 'resolved', label: 'Tickets résolus', value: clickedData.resolved, percentage: (clickedData.resolved / clickedData.total) * 100, trend: 'up' as const },
        { id: 'pending', label: 'Tickets en attente', value: clickedData.pending, percentage: (clickedData.pending / clickedData.total) * 100, trend: 'stable' as const },
        { id: 'escalated', label: 'Tickets escaladés', value: clickedData.escalated, percentage: (clickedData.escalated / clickedData.total) * 100, trend: 'down' as const },
      ];

      // Generate time series data (mock data for demonstration)
      const timeSeriesData = Array.from({ length: 24 }, (_, i) => ({
        date: new Date(Date.now() - (23 - i) * 60 * 60 * 1000).toISOString(),
        value: Math.floor(Math.random() * clickedData.total * 0.1) + clickedData.total * 0.02,
      }));

      const drillDownInfo = {
        type: 'volume' as const,
        title: 'Analyse détaillée du volume',
        subtitle: `Données pour le ${clickedData.date}`,
        selectedValue: clickedData.total,
        selectedLabel: `Volume du ${clickedData.date}`,
        timeSeriesData,
        detailData,
        summaryStats: {
          total: clickedData.total,
          average: Math.round(clickedData.total / 4),
          peak: Math.max(clickedData.resolved, clickedData.pending, clickedData.escalated),
          growth: Math.random() * 20 - 10, // Mock growth percentage
        },
      };

      setDrillDownData(drillDownInfo);
    } catch (error) {
      console.error('Error loading drill-down data:', error);
    } finally {
      setDrillDownLoading(false);
    }
  }, [chartData]);

  // Chart options configuration
  const chartOptions: ChartOptions<'line'> = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'index' as const,
      intersect: false,
    },
    plugins: {
      legend: {
        position: 'top' as const,
        align: 'end' as const,
        labels: {
          usePointStyle: true,
          pointStyle: 'circle',
          padding: 20,
          font: {
            size: 12,
            weight: 'bold',
          },
        },
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#ffffff',
        bodyColor: '#ffffff',
        borderColor: FREE_MOBILE_COLORS.PRIMARY,
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true,
        callbacks: {
          title: (context) => {
            const date = new Date(chartData[context[0].dataIndex].date);
            return date.toLocaleDateString('fr-FR', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            });
          },
          label: (context) => {
            return `${context.dataset.label}: ${context.parsed.y} tickets`;
          },
          afterBody: (context) => {
            const dataIndex = context[0].dataIndex;
            const item = chartData[dataIndex];
            const total = item.total;
            const resolved = item.resolved;
            const resolutionRate = total > 0 ? Math.round((resolved / total) * 100) : 0;
            return [``, `Taux de résolution: ${resolutionRate}%`];
          },
        },
      },
    },
    scales: {
      x: {
        display: true,
        grid: {
          display: true,
          color: 'rgba(0, 0, 0, 0.05)',
        },
        ticks: {
          font: {
            size: 11,
          },
          maxTicksLimit: timeRange === '7d' ? 7 : timeRange === '30d' ? 10 : 12,
        },
      },
      y: {
        display: true,
        beginAtZero: true,
        grid: {
          display: true,
          color: 'rgba(0, 0, 0, 0.05)',
        },
        ticks: {
          font: {
            size: 11,
          },
          callback: (value) => `${value}`,
        },
      },
    },
    elements: {
      point: {
        hoverBorderWidth: 3,
      },
    },
    animation: {
      duration: 1000,
      easing: 'easeInOutQuart',
    },
    onClick: handleChartClick,
  }), [chartData, timeRange, theme]);

  const handleTimeRangeChange = (newRange: TimeRange) => {
    setTimeRange(newRange);
    onTimeRangeChange?.(newRange);
    fetchData(newRange);
  };

  const handleRefresh = () => {
    fetchData(timeRange);
    onRefresh?.();
  };





  // Calculate summary statistics
  const totalTickets = chartData.reduce((sum, item) => sum + item.total, 0);
  const totalResolved = chartData.reduce((sum, item) => sum + item.resolved, 0);
  const totalPending = chartData.reduce((sum, item) => sum + item.pending, 0);
  const totalEscalated = chartData.reduce((sum, item) => sum + item.escalated, 0);
  const resolutionRate = totalTickets > 0 ? Math.round((totalResolved / totalTickets) * 100) : 0;
  const escalationRate = totalTickets > 0 ? Math.round((totalEscalated / totalTickets) * 100) : 0;

  if (loading) {
    return (
      <ChartSkeleton
        height={height}
        showHeader={true}
        showMetrics={true}
        variant="line"
      />
    );
  }

  if (error) {
    return (
      <Card elevation={4} sx={{ height, borderRadius: 3, overflow: 'hidden' }}>
        <CardContent sx={{
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          p: 3,
        }}>
          <Alert
            severity="error"
            sx={{
              width: '100%',
              borderRadius: 2,
              '& .MuiAlert-message': {
                width: '100%',
              },
            }}
            action={
              <IconButton
                aria-label="retry"
                color="inherit"
                size="small"
                onClick={handleRefresh}
              >
                <RefreshIcon />
              </IconButton>
            }
          >
            <Typography variant="h6" gutterBottom>
              Erreur de chargement du graphique
            </Typography>
            <Typography variant="body2">
              {error}
            </Typography>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card
      elevation={4}
      sx={{
        borderRadius: 3,
        overflow: 'hidden',
        transition: 'all 0.3s ease',
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: 6,
        },
      }}
    >
      {/* Enhanced Header with controls */}
      <CardHeader
        title={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <TimelineIcon sx={{ color: FREE_MOBILE_COLORS.PRIMARY, fontSize: 28 }} />
            <Typography variant="h6" fontWeight="bold">
              Volume des Tickets
            </Typography>
          </Box>
        }
        action={
          <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
            <Chip
              icon={<TrendingUpIcon />}
              label={`${resolutionRate}% résolution`}
              size="small"
              color="success"
              variant="outlined"
              sx={{ fontWeight: 'bold' }}
            />

            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Période</InputLabel>
              <Select
                value={timeRange}
                label="Période"
                onChange={(e) => handleTimeRangeChange(e.target.value as TimeRange)}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    '&:hover fieldset': {
                      borderColor: FREE_MOBILE_COLORS.PRIMARY,
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: FREE_MOBILE_COLORS.PRIMARY,
                    },
                  },
                }}
              >
                <MenuItem value="7d">7 jours</MenuItem>
                <MenuItem value="30d">30 jours</MenuItem>
                <MenuItem value="90d">3 mois</MenuItem>
                <MenuItem value="1y">1 an</MenuItem>
              </Select>
            </FormControl>

            <Tooltip title="Actualiser les données">
              <IconButton
                onClick={handleRefresh}
                disabled={loading}
                sx={{
                  color: FREE_MOBILE_COLORS.PRIMARY,
                  '&:hover': {
                    backgroundColor: `${FREE_MOBILE_COLORS.PRIMARY}10`,
                  },
                }}
              >
                <RefreshIcon />
              </IconButton>
            </Tooltip>

            <ExportMenu
              chartElement={chartRef.current?.canvas || null}
              chartData={{
                labels: chartData.map(item => item.date),
                datasets: [{
                  label: 'Volume des tickets',
                  data: chartData.map(item => item.total),
                  backgroundColor: FREE_MOBILE_COLORS.PRIMARY,
                  borderColor: FREE_MOBILE_COLORS.PRIMARY,
                }],
              }}
              defaultFilename="volume-tickets"
              defaultTitle="Volume des Tickets - Free Mobile"
            />
          </Box>
        }
        sx={{
          pb: 1,
          backgroundColor: `${FREE_MOBILE_COLORS.PRIMARY}05`,
          borderBottom: `1px solid ${theme.palette.divider}`,
        }}
      />

      {/* Chart Content */}
      <CardContent sx={{ p: 3 }}>
        {/* Summary Statistics */}
        <Box sx={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(140px, 1fr))',
          gap: 2,
          mb: 3
        }}>
          <Box sx={{ textAlign: 'center', p: 2, borderRadius: 2, backgroundColor: `${FREE_MOBILE_COLORS.PRIMARY}10` }}>
            <Typography variant="h5" color={FREE_MOBILE_COLORS.PRIMARY} fontWeight="bold">
              {totalTickets.toLocaleString()}
            </Typography>
            <Typography variant="caption" color="text.secondary" fontWeight="medium">
              Total tickets
            </Typography>
          </Box>

          <Box sx={{ textAlign: 'center', p: 2, borderRadius: 2, backgroundColor: `${FREE_MOBILE_COLORS.SUCCESS}10` }}>
            <Typography variant="h5" color={FREE_MOBILE_COLORS.SUCCESS} fontWeight="bold">
              {totalResolved.toLocaleString()}
            </Typography>
            <Typography variant="caption" color="text.secondary" fontWeight="medium">
              Résolus
            </Typography>
          </Box>

          <Box sx={{ textAlign: 'center', p: 2, borderRadius: 2, backgroundColor: `${FREE_MOBILE_COLORS.WARNING}10` }}>
            <Typography variant="h5" color={FREE_MOBILE_COLORS.WARNING} fontWeight="bold">
              {totalPending.toLocaleString()}
            </Typography>
            <Typography variant="caption" color="text.secondary" fontWeight="medium">
              En attente
            </Typography>
          </Box>

          <Box sx={{ textAlign: 'center', p: 2, borderRadius: 2, backgroundColor: `${theme.palette.error.main}10` }}>
            <Typography variant="h5" color="error.main" fontWeight="bold">
              {totalEscalated.toLocaleString()}
            </Typography>
            <Typography variant="caption" color="text.secondary" fontWeight="medium">
              Escaladés
            </Typography>
          </Box>
        </Box>

        {/* Chart Container */}
        <Box
          sx={{
            height: height - 200,
            position: 'relative',
            borderRadius: 2,
            backgroundColor: 'background.paper',
            p: 2,
          }}
        >
          <Line
            ref={chartRef}
            data={lineChartData}
            options={chartOptions}
          />
        </Box>

        {/* Additional Statistics */}
        <Box sx={{
          mt: 3,
          p: 2,
          borderRadius: 2,
          backgroundColor: 'grey.50',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          flexWrap: 'wrap',
          gap: 2,
        }}>
          <Typography variant="body2" color="text.secondary">
            Période: {timeRange === '7d' ? '7 derniers jours' :
             timeRange === '30d' ? '30 derniers jours' :
             timeRange === '90d' ? '3 derniers mois' : 'Dernière année'}
          </Typography>
          <Box sx={{ display: 'flex', gap: 3 }}>
            <Typography variant="body2" color="text.secondary">
              <strong>Taux de résolution:</strong> {resolutionRate}%
            </Typography>
            <Typography variant="body2" color="text.secondary">
              <strong>Taux d'escalade:</strong> {escalationRate}%
            </Typography>
          </Box>
        </Box>
      </CardContent>

      <DrillDownModal
        open={drillDownOpen}
        onClose={() => setDrillDownOpen(false)}
        data={drillDownData}
        loading={drillDownLoading}
      />
    </Card>
  );
};

export default VolumeChart;
