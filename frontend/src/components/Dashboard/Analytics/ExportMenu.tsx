/**
 * Export Menu Component
 * Provides multi-format export options for Chart.js components
 * Supports PDF, PNG, CSV, and Excel exports with customization options
 */

import React, { useState } from 'react';
import {
  Menu,
  MenuItem,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControlLabel,
  Checkbox,
  Select,
  FormControl,
  InputLabel,
  Typography,
  Box,
  Divider,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  FileDownload as DownloadIcon,
  Image as ImageIcon,
  PictureAsPdf as PdfIcon,
  TableChart as CsvIcon,
  Assessment as ExcelIcon,
} from '@mui/icons-material';
import { 
  exportChart, 
  getAvailableExportFormats, 
  ExportOptions, 
  ChartData 
} from '../../../utils/chartExport';

interface ExportMenuProps {
  chartElement: HTMLElement | null;
  chartData: ChartData;
  defaultFilename?: string;
  defaultTitle?: string;
}

const ExportMenu: React.FC<ExportMenuProps> = ({
  chartElement,
  chartData,
  defaultFilename = 'chart',
  defaultTitle = 'Analytics Chart'
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedFormat, setSelectedFormat] = useState<'pdf' | 'png' | 'csv' | 'excel'>('png');
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'png',
    filename: defaultFilename,
    quality: 2,
    includeTitle: true,
    includeDate: true,
    customTitle: defaultTitle,
  });
  const [isExporting, setIsExporting] = useState(false);
  const [exportError, setExportError] = useState<string | null>(null);

  const exportFormats = getAvailableExportFormats();

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleQuickExport = async (format: 'pdf' | 'png' | 'csv' | 'excel') => {
    if (!chartElement && (format === 'pdf' || format === 'png')) {
      setExportError('Chart element not available for image export');
      return;
    }

    setIsExporting(true);
    setExportError(null);

    try {
      await exportChart(chartElement!, chartData, {
        ...exportOptions,
        format,
        filename: `${defaultFilename}-${Date.now()}`,
      });
    } catch (error) {
      setExportError(error instanceof Error ? error.message : 'Export failed');
    } finally {
      setIsExporting(false);
      handleMenuClose();
    }
  };

  const handleCustomExport = () => {
    setDialogOpen(true);
    handleMenuClose();
  };

  const handleDialogClose = () => {
    setDialogOpen(false);
    setExportError(null);
  };

  const handleExportOptionsChange = (field: keyof ExportOptions, value: any) => {
    setExportOptions(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleCustomExportExecute = async () => {
    if (!chartElement && (selectedFormat === 'pdf' || selectedFormat === 'png')) {
      setExportError('Chart element not available for image export');
      return;
    }

    setIsExporting(true);
    setExportError(null);

    try {
      await exportChart(chartElement!, chartData, {
        ...exportOptions,
        format: selectedFormat,
      });
      setDialogOpen(false);
    } catch (error) {
      setExportError(error instanceof Error ? error.message : 'Export failed');
    } finally {
      setIsExporting(false);
    }
  };

  const getFormatIcon = (format: string) => {
    switch (format) {
      case 'png': return <ImageIcon fontSize="small" />;
      case 'pdf': return <PdfIcon fontSize="small" />;
      case 'csv': return <CsvIcon fontSize="small" />;
      case 'excel': return <ExcelIcon fontSize="small" />;
      default: return <DownloadIcon fontSize="small" />;
    }
  };

  return (
    <>
      <IconButton
        onClick={handleMenuOpen}
        size="small"
        title="Export Chart"
        disabled={isExporting}
      >
        {isExporting ? (
          <CircularProgress size={20} />
        ) : (
          <DownloadIcon />
        )}
      </IconButton>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        PaperProps={{
          sx: { minWidth: 200 }
        }}
      >
        <MenuItem disabled>
          <Typography variant="subtitle2" color="text.secondary">
            Quick Export
          </Typography>
        </MenuItem>
        
        {exportFormats.map((format) => (
          <MenuItem
            key={format.value}
            onClick={() => handleQuickExport(format.value as any)}
            disabled={isExporting}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {getFormatIcon(format.value)}
              <Typography variant="body2">
                {format.label}
              </Typography>
            </Box>
          </MenuItem>
        ))}

        <Divider />
        
        <MenuItem onClick={handleCustomExport} disabled={isExporting}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <DownloadIcon fontSize="small" />
            <Typography variant="body2">
              Custom Export...
            </Typography>
          </Box>
        </MenuItem>
      </Menu>

      <Dialog
        open={dialogOpen}
        onClose={handleDialogClose}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Export Chart
        </DialogTitle>
        
        <DialogContent>
          {exportError && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {exportError}
            </Alert>
          )}

          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
            <FormControl fullWidth>
              <InputLabel>Export Format</InputLabel>
              <Select
                value={selectedFormat}
                label="Export Format"
                onChange={(e) => {
                  setSelectedFormat(e.target.value as any);
                  handleExportOptionsChange('format', e.target.value);
                }}
              >
                {exportFormats.map((format) => (
                  <MenuItem key={format.value} value={format.value}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {getFormatIcon(format.value)}
                      {format.label}
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <TextField
              label="Filename"
              value={exportOptions.filename}
              onChange={(e) => handleExportOptionsChange('filename', e.target.value)}
              fullWidth
              helperText="File extension will be added automatically"
            />

            <TextField
              label="Custom Title"
              value={exportOptions.customTitle}
              onChange={(e) => handleExportOptionsChange('customTitle', e.target.value)}
              fullWidth
              multiline
              rows={2}
            />

            <Box>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={exportOptions.includeTitle}
                    onChange={(e) => handleExportOptionsChange('includeTitle', e.target.checked)}
                  />
                }
                label="Include title in export"
              />
              
              <FormControlLabel
                control={
                  <Checkbox
                    checked={exportOptions.includeDate}
                    onChange={(e) => handleExportOptionsChange('includeDate', e.target.checked)}
                  />
                }
                label="Include generation date"
              />
            </Box>

            {(selectedFormat === 'png' || selectedFormat === 'pdf') && (
              <FormControl fullWidth>
                <InputLabel>Image Quality</InputLabel>
                <Select
                  value={exportOptions.quality}
                  label="Image Quality"
                  onChange={(e) => handleExportOptionsChange('quality', e.target.value)}
                >
                  <MenuItem value={1}>Standard (1x)</MenuItem>
                  <MenuItem value={2}>High (2x)</MenuItem>
                  <MenuItem value={3}>Ultra (3x)</MenuItem>
                </Select>
              </FormControl>
            )}
          </Box>
        </DialogContent>

        <DialogActions>
          <Button onClick={handleDialogClose}>
            Cancel
          </Button>
          <Button
            onClick={handleCustomExportExecute}
            variant="contained"
            disabled={isExporting || !exportOptions.filename}
            startIcon={isExporting ? <CircularProgress size={16} /> : getFormatIcon(selectedFormat)}
          >
            {isExporting ? 'Exporting...' : 'Export'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ExportMenu;
