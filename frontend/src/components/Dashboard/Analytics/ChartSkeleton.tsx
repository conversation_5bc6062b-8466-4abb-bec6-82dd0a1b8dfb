import React from 'react';
import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>er,
  Skeleton,
  Grid,
  useTheme,
} from '@mui/material';
import { FREE_MOBILE_COLORS } from '../../../utils/constants';

interface ChartSkeletonProps {
  height?: number;
  showHeader?: boolean;
  showLegend?: boolean;
  showMetrics?: boolean;
  variant?: 'line' | 'doughnut' | 'bar';
}

const ChartSkeleton: React.FC<ChartSkeletonProps> = ({
  height = 400,
  showHeader = true,
  showLegend = false,
  showMetrics = false,
  variant = 'line',
}) => {
  const theme = useTheme();

  const renderLineChartSkeleton = () => (
    <Box sx={{ position: 'relative', height: height - 120 }}>
      {/* Y-axis labels */}
      <Box sx={{ position: 'absolute', left: 0, top: 0, height: '100%', width: 40 }}>
        {[...Array(5)].map((_, i) => (
          <Skeleton
            key={i}
            variant="text"
            width={30}
            height={20}
            sx={{ 
              position: 'absolute', 
              top: `${i * 20}%`,
              animation: `pulse 1.5s ease-in-out ${i * 0.1}s infinite alternate`,
            }}
          />
        ))}
      </Box>
      
      {/* Chart area */}
      <Box sx={{ ml: 5, mr: 2, height: '90%', position: 'relative' }}>
        {/* Grid lines */}
        {[...Array(5)].map((_, i) => (
          <Box
            key={i}
            sx={{
              position: 'absolute',
              top: `${i * 20}%`,
              left: 0,
              right: 0,
              height: 1,
              backgroundColor: theme.palette.divider,
              opacity: 0.3,
            }}
          />
        ))}
        
        {/* Data lines */}
        <Skeleton
          variant="rectangular"
          width="100%"
          height="70%"
          sx={{
            position: 'absolute',
            top: '15%',
            borderRadius: 1,
            background: `linear-gradient(45deg, ${FREE_MOBILE_COLORS.PRIMARY}20, transparent, ${FREE_MOBILE_COLORS.SECONDARY}20)`,
            animation: 'pulse 2s ease-in-out infinite alternate',
          }}
        />
      </Box>
      
      {/* X-axis labels */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1, mx: 5 }}>
        {[...Array(7)].map((_, i) => (
          <Skeleton
            key={i}
            variant="text"
            width={40}
            height={20}
            sx={{ 
              animation: `pulse 1.5s ease-in-out ${i * 0.1}s infinite alternate`,
            }}
          />
        ))}
      </Box>
    </Box>
  );

  const renderDoughnutChartSkeleton = () => (
    <Box sx={{ display: 'flex', alignItems: 'center', height: height - 120 }}>
      {/* Doughnut chart */}
      <Box sx={{ flex: 1, display: 'flex', justifyContent: 'center' }}>
        <Box sx={{ position: 'relative', width: 200, height: 200 }}>
          <Skeleton
            variant="circular"
            width={200}
            height={200}
            sx={{
              background: `conic-gradient(${FREE_MOBILE_COLORS.PRIMARY}40, ${FREE_MOBILE_COLORS.SECONDARY}40, ${FREE_MOBILE_COLORS.SUCCESS}40, ${FREE_MOBILE_COLORS.WARNING}40)`,
              animation: 'pulse 2s ease-in-out infinite alternate',
            }}
          />
          <Box
            sx={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              width: 120,
              height: 120,
              backgroundColor: theme.palette.background.paper,
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Skeleton variant="text" width={60} height={30} />
          </Box>
        </Box>
      </Box>
      
      {/* Legend */}
      {showLegend && (
        <Box sx={{ flex: 1, pl: 3 }}>
          {[...Array(4)].map((_, i) => (
            <Box key={i} sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Skeleton
                variant="circular"
                width={12}
                height={12}
                sx={{ 
                  mr: 1,
                  animation: `pulse 1.5s ease-in-out ${i * 0.2}s infinite alternate`,
                }}
              />
              <Skeleton
                variant="text"
                width={100}
                height={20}
                sx={{ 
                  mr: 2,
                  animation: `pulse 1.5s ease-in-out ${i * 0.2}s infinite alternate`,
                }}
              />
              <Skeleton
                variant="text"
                width={40}
                height={20}
                sx={{ 
                  animation: `pulse 1.5s ease-in-out ${i * 0.2}s infinite alternate`,
                }}
              />
            </Box>
          ))}
        </Box>
      )}
    </Box>
  );

  const renderBarChartSkeleton = () => (
    <Box sx={{ position: 'relative', height: height - 120 }}>
      {/* Y-axis */}
      <Box sx={{ position: 'absolute', left: 0, top: 0, height: '90%', width: 40 }}>
        {[...Array(5)].map((_, i) => (
          <Skeleton
            key={i}
            variant="text"
            width={30}
            height={20}
            sx={{ 
              position: 'absolute', 
              top: `${i * 20}%`,
              animation: `pulse 1.5s ease-in-out ${i * 0.1}s infinite alternate`,
            }}
          />
        ))}
      </Box>
      
      {/* Bars */}
      <Box sx={{ ml: 5, mr: 2, height: '90%', display: 'flex', alignItems: 'end', gap: 1 }}>
        {[...Array(12)].map((_, i) => (
          <Skeleton
            key={i}
            variant="rectangular"
            width={20}
            height={`${Math.random() * 80 + 20}%`}
            sx={{
              borderRadius: '4px 4px 0 0',
              backgroundColor: `${FREE_MOBILE_COLORS.PRIMARY}${Math.floor(Math.random() * 50 + 30)}`,
              animation: `pulse 1.5s ease-in-out ${i * 0.1}s infinite alternate`,
            }}
          />
        ))}
      </Box>
      
      {/* X-axis labels */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1, mx: 5 }}>
        {[...Array(6)].map((_, i) => (
          <Skeleton
            key={i}
            variant="text"
            width={30}
            height={20}
            sx={{ 
              animation: `pulse 1.5s ease-in-out ${i * 0.1}s infinite alternate`,
            }}
          />
        ))}
      </Box>
    </Box>
  );

  const renderChartContent = () => {
    switch (variant) {
      case 'doughnut':
        return renderDoughnutChartSkeleton();
      case 'bar':
        return renderBarChartSkeleton();
      default:
        return renderLineChartSkeleton();
    }
  };

  return (
    <Card elevation={3} sx={{ borderRadius: 3, height: '100%' }}>
      {showHeader && (
        <CardHeader
          title={
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Skeleton variant="circular" width={24} height={24} />
              <Skeleton variant="text" width={150} height={28} />
            </Box>
          }
          action={
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Skeleton variant="rectangular" width={80} height={32} sx={{ borderRadius: 1 }} />
              <Skeleton variant="circular" width={32} height={32} />
            </Box>
          }
        />
      )}
      
      <CardContent>
        {showMetrics && (
          <Grid container spacing={2} sx={{ mb: 3 }}>
            {[...Array(4)].map((_, i) => (
              <Grid item xs={12} sm={6} md={3} key={i}>
                <Card variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                  <Skeleton variant="circular" width={40} height={40} sx={{ mx: 'auto', mb: 1 }} />
                  <Skeleton variant="text" width={80} height={20} sx={{ mx: 'auto', mb: 1 }} />
                  <Skeleton variant="text" width={60} height={24} sx={{ mx: 'auto' }} />
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
        
        {renderChartContent()}
      </CardContent>
    </Card>
  );
};

export default ChartSkeleton;
