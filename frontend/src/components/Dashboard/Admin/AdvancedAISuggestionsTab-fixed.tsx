import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Avatar,
  Chip,
  Button,
  IconButton,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Divider,
  Paper,
  Badge,
  Tooltip,
  Alert,
  Fade,
  Collapse,
  LinearProgress,
  Rating,
  Slider,
  ButtonGroup,
} from '@mui/material';
import {
  Psychology as AIIcon,
  AutoAwesome as SuggestionIcon,
  ThumbUp as ApproveIcon,
  ThumbDown as RejectIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Sort as SortIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Speed as PerformanceIcon,
  Assessment as AnalyticsIcon,
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
  CheckCircle as CheckIcon,
  Cancel as CancelIcon,
  Star as StarIcon,
  Timeline as TimelineIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Lightbulb as IdeaIcon,
  SmartToy as BotIcon,
} from '@mui/icons-material';
import { FREE_MOBILE_COLORS } from '../../../utils/constants';

interface AISuggestion {
  id: string;
  type: 'response' | 'escalation' | 'resolution' | 'optimization';
  title: string;
  description: string;
  suggestion: string;
  confidence: number;
  impact: 'low' | 'medium' | 'high';
  category: string;
  timestamp: Date;
  status: 'pending' | 'approved' | 'rejected' | 'implemented';
  conversationId?: string;
  agentId?: string;
  metrics?: {
    expectedTimeReduction?: number;
    satisfactionImprovement?: number;
    resolutionProbability?: number;
  };
  feedback?: {
    rating: number;
    comment: string;
  };
}

interface AdvancedAISuggestionsTabProps {
  suggestionsCount: number;
  onSuggestionsCountChange: (count: number) => void;
  loading?: boolean;
}

const AdvancedAISuggestionsTab: React.FC<AdvancedAISuggestionsTabProps> = ({
  suggestionsCount,
  onSuggestionsCountChange,
  loading = false,
}) => {
  const [suggestions, setSuggestions] = useState<AISuggestion[]>([]);
  const [filteredSuggestions, setFilteredSuggestions] = useState<AISuggestion[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [impactFilter, setImpactFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('timestamp');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [minConfidence, setMinConfidence] = useState<number>(0);
  const [expandedSuggestion, setExpandedSuggestion] = useState<string | null>(null);
  const [showOnlyPending, setShowOnlyPending] = useState(false);

  // Generate sample AI suggestions
  const generateSampleSuggestions = useCallback((): AISuggestion[] => {
    const sampleSuggestions: AISuggestion[] = [
      {
        id: 'ai-sugg-1',
        type: 'response',
        title: 'Réponse automatique optimisée',
        description: 'Suggestion de réponse basée sur des conversations similaires réussies',
        suggestion: 'Bonjour, je comprends votre problème de connexion. Pouvez-vous vérifier que votre box est bien allumée et redémarrer votre équipement ?',
        confidence: 92,
        impact: 'high',
        category: 'Support technique',
        timestamp: new Date(Date.now() - 5 * 60 * 1000),
        status: 'pending',
        conversationId: 'conv-12345',
        agentId: 'agent-001',
        metrics: {
          expectedTimeReduction: 3.5,
          satisfactionImprovement: 15,
          resolutionProbability: 85,
        },
      },
      {
        id: 'ai-sugg-2',
        type: 'escalation',
        title: 'Escalade recommandée',
        description: 'Le client montre des signes de frustration élevée',
        suggestion: 'Escalader vers un superviseur - sentiment négatif détecté avec score de -0.8',
        confidence: 88,
        impact: 'high',
        category: 'Gestion client',
        timestamp: new Date(Date.now() - 15 * 60 * 1000),
        status: 'approved',
        conversationId: 'conv-12346',
        agentId: 'agent-002',
        metrics: {
          satisfactionImprovement: 25,
          resolutionProbability: 70,
        },
        feedback: {
          rating: 5,
          comment: 'Excellente suggestion, a évité une escalade majeure',
        },
      },
    ];
    return sampleSuggestions;
  }, []);

  // Initialize suggestions
  useEffect(() => {
    const sampleSuggestions = generateSampleSuggestions();
    setSuggestions(sampleSuggestions);
    
    // Update pending count
    const pendingCount = sampleSuggestions.filter(s => s.status === 'pending').length;
    onSuggestionsCountChange(pendingCount);
  }, [generateSampleSuggestions, onSuggestionsCountChange]);

  // Filter and sort suggestions
  useEffect(() => {
    let filtered = suggestions;

    // Search filter
    if (searchQuery) {
      filtered = filtered.filter(suggestion =>
        suggestion.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        suggestion.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        suggestion.suggestion.toLowerCase().includes(searchQuery.toLowerCase()) ||
        suggestion.category.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Type filter
    if (typeFilter !== 'all') {
      filtered = filtered.filter(suggestion => suggestion.type === typeFilter);
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(suggestion => suggestion.status === statusFilter);
    }

    // Impact filter
    if (impactFilter !== 'all') {
      filtered = filtered.filter(suggestion => suggestion.impact === impactFilter);
    }

    // Confidence filter
    filtered = filtered.filter(suggestion => suggestion.confidence >= minConfidence);

    // Pending only filter
    if (showOnlyPending) {
      filtered = filtered.filter(suggestion => suggestion.status === 'pending');
    }

    // Sort
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;
      switch (sortBy) {
        case 'confidence':
          aValue = a.confidence;
          bValue = b.confidence;
          break;
        case 'impact':
          const impactOrder = { low: 1, medium: 2, high: 3 };
          aValue = impactOrder[a.impact];
          bValue = impactOrder[b.impact];
          break;
        case 'timestamp':
        default:
          aValue = a.timestamp.getTime();
          bValue = b.timestamp.getTime();
          break;
      }
      return sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
    });

    setFilteredSuggestions(filtered);
  }, [suggestions, searchQuery, typeFilter, statusFilter, impactFilter, minConfidence, showOnlyPending, sortBy, sortOrder]);

  const getSuggestionIcon = (type: string) => {
    switch (type) {
      case 'response':
        return <SuggestionIcon />;
      case 'escalation':
        return <TrendingUpIcon />;
      case 'resolution':
        return <CheckIcon />;
      case 'optimization':
        return <PerformanceIcon />;
      default:
        return <AIIcon />;
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high':
        return '#f44336';
      case 'medium':
        return '#ff9800';
      case 'low':
        return '#4caf50';
      default:
        return '#757575';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return '#4caf50';
      case 'implemented':
        return '#2196f3';
      case 'rejected':
        return '#f44336';
      case 'pending':
      default:
        return '#ff9800';
    }
  };

  const handleApproveSuggestion = (suggestionId: string) => {
    setSuggestions(prev =>
      prev.map(suggestion =>
        suggestion.id === suggestionId
          ? { ...suggestion, status: 'approved' as const }
          : suggestion
      )
    );

    // Update pending count
    const updatedSuggestions = suggestions.map(suggestion =>
      suggestion.id === suggestionId
        ? { ...suggestion, status: 'approved' as const }
        : suggestion
    );
    const pendingCount = updatedSuggestions.filter(s => s.status === 'pending').length;
    onSuggestionsCountChange(pendingCount);
  };

  const handleRejectSuggestion = (suggestionId: string) => {
    setSuggestions(prev =>
      prev.map(suggestion =>
        suggestion.id === suggestionId
          ? { ...suggestion, status: 'rejected' as const }
          : suggestion
      )
    );

    // Update pending count
    const updatedSuggestions = suggestions.map(suggestion =>
      suggestion.id === suggestionId
        ? { ...suggestion, status: 'rejected' as const }
        : suggestion
    );
    const pendingCount = updatedSuggestions.filter(s => s.status === 'pending').length;
    onSuggestionsCountChange(pendingCount);
  };
