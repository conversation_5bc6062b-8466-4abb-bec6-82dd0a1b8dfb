import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Avatar,
  Chip,
  Button,
  IconButton,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Divider,
  Paper,
  Badge,
  Tooltip,
  Alert,
  Fade,
  Collapse,
  LinearProgress,
  CircularProgress,
  ButtonGroup,
  Tabs,
  Tab,
} from '@mui/material';
import {
  AutoAwesome as MLIcon,
  Memory as ModelIcon,
  Speed as PerformanceIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  CheckCircle as SuccessIcon,
  Check as CheckIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Refresh as RefreshIcon,
  Settings as SettingsIcon,
  PlayArrow as StartIcon,
  Stop as StopIcon,
  Pause as PauseIcon,
  Timeline as TimelineIcon,
  Assessment as AssessmentIcon,
  Computer as SystemIcon,
  Storage as DataIcon,
  CloudUpload as TrainingIcon,
  Analytics as AnalyticsIcon,
  MonitorHeart as MonitorIcon,
  CompareArrows as CompareIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
} from '@mui/icons-material';
import { FREE_MOBILE_COLORS } from '../../../utils/constants';

interface MLModel {
  id: string;
  name: string;
  type: 'classification' | 'regression' | 'clustering' | 'nlp';
  status: 'training' | 'active' | 'inactive' | 'error' | 'testing';
  accuracy: number;
  lastTrained: Date;
  version: string;
  performance: {
    precision: number;
    recall: number;
    f1Score: number;
    latency: number;
  };
  trainingData: {
    samples: number;
    features: number;
    lastUpdate: Date;
  };
  metrics: {
    predictionsToday: number;
    successRate: number;
    errorRate: number;
  };
}

interface MLAlert {
  id: string;
  type: 'performance' | 'error' | 'training' | 'data';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  message: string;
  modelId?: string;
  timestamp: Date;
  resolved: boolean;
  metadata?: any;
}

interface MLIntelligenceDashboardTabProps {
  alertsCount: number;
  onAlertsCountChange: (count: number) => void;
  loading?: boolean;
}

const MLIntelligenceDashboardTab: React.FC<MLIntelligenceDashboardTabProps> = ({
  alertsCount,
  onAlertsCountChange,
  loading = false,
}) => {
  const [currentTab, setCurrentTab] = useState(0);
  const [models, setModels] = useState<MLModel[]>([]);
  const [alerts, setAlerts] = useState<MLAlert[]>([]);
  const [filteredModels, setFilteredModels] = useState<MLModel[]>([]);
  const [filteredAlerts, setFilteredAlerts] = useState<MLAlert[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [expandedModel, setExpandedModel] = useState<string | null>(null);
  const [expandedAlert, setExpandedAlert] = useState<string | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Generate sample ML models
  const generateSampleModels = useCallback((): MLModel[] => {
    return [
      {
        id: 'model-1',
        name: 'Classification des Tickets',
        type: 'classification',
        status: 'active',
        accuracy: 94.2,
        lastTrained: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
        version: 'v2.1.3',
        performance: {
          precision: 93.8,
          recall: 94.6,
          f1Score: 94.2,
          latency: 45,
        },
        trainingData: {
          samples: 125000,
          features: 847,
          lastUpdate: new Date(Date.now() - 6 * 60 * 60 * 1000),
        },
        metrics: {
          predictionsToday: 1247,
          successRate: 96.3,
          errorRate: 3.7,
        },
      },
      {
        id: 'model-2',
        name: 'Analyse de Sentiment',
        type: 'nlp',
        status: 'active',
        accuracy: 89.7,
        lastTrained: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
        version: 'v1.8.2',
        performance: {
          precision: 88.9,
          recall: 90.5,
          f1Score: 89.7,
          latency: 32,
        },
        trainingData: {
          samples: 89000,
          features: 512,
          lastUpdate: new Date(Date.now() - 12 * 60 * 60 * 1000),
        },
        metrics: {
          predictionsToday: 892,
          successRate: 91.2,
          errorRate: 8.8,
        },
      },
    ];
  }, []);

  // Generate sample ML alerts
  const generateSampleAlerts = useCallback((): MLAlert[] => {
    return [
      {
        id: 'alert-1',
        type: 'performance',
        severity: 'high',
        title: 'Dégradation des performances détectée',
        message: 'Le modèle de classification des tickets montre une baisse de précision de 5% sur les dernières 24h',
        modelId: 'model-1',
        timestamp: new Date(Date.now() - 15 * 60 * 1000),
        resolved: false,
        metadata: { previousAccuracy: 94.2, currentAccuracy: 89.1 },
      },
      {
        id: 'alert-2',
        type: 'error',
        severity: 'critical',
        title: 'Échec du modèle de détection d\'anomalies',
        message: 'Le modèle a rencontré une erreur critique et nécessite une intervention immédiate',
        modelId: 'model-2',
        timestamp: new Date(Date.now() - 45 * 60 * 1000),
        resolved: false,
        metadata: { errorCode: 'ML_MODEL_CRASH', stackTrace: 'OutOfMemoryError...' },
      },
    ];
  }, []);

  // Initialize data
  useEffect(() => {
    const sampleModels = generateSampleModels();
    const sampleAlerts = generateSampleAlerts();
    setModels(sampleModels);
    setAlerts(sampleAlerts);
    setFilteredModels(sampleModels);
    setFilteredAlerts(sampleAlerts);

    // Update alerts count
    const unresolvedCount = sampleAlerts.filter(a => !a.resolved).length;
    onAlertsCountChange(unresolvedCount);
  }, [generateSampleModels, generateSampleAlerts, onAlertsCountChange]);

  // Filter models
  useEffect(() => {
    let filtered = models;
    if (searchQuery) {
      filtered = filtered.filter(model =>
        model.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        model.type.toLowerCase().includes(searchQuery.toLowerCase()) ||
        model.version.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }
    if (statusFilter !== 'all') {
      filtered = filtered.filter(model => model.status === statusFilter);
    }
    if (typeFilter !== 'all') {
      filtered = filtered.filter(model => model.type === typeFilter);
    }
    setFilteredModels(filtered);
  }, [models, searchQuery, statusFilter, typeFilter]);

  // Filter alerts
  useEffect(() => {
    let filtered = alerts;
    if (searchQuery) {
      filtered = filtered.filter(alert =>
        alert.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        alert.message.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }
    // Sort by timestamp (newest first)
    filtered.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
    setFilteredAlerts(filtered);
  }, [alerts, searchQuery]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return '#4caf50';
      case 'training': return '#2196f3';
      case 'inactive': return '#757575';
      case 'error': return '#f44336';
      case 'testing': return '#ff9800';
      default: return '#757575';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return '#f44336';
      case 'high': return '#ff5722';
      case 'medium': return '#ff9800';
      case 'low': return '#4caf50';
      default: return '#757575';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'classification': return <AssessmentIcon />;
      case 'regression': return <TimelineIcon />;
      case 'clustering': return <AnalyticsIcon />;
      case 'nlp': return <MLIcon />;
      default: return <ModelIcon />;
    }
  };

  const handleModelAction = (modelId: string, action: 'start' | 'stop' | 'retrain') => {
    setModels(prev => prev.map(model => {
      if (model.id === modelId) {
        switch (action) {
          case 'start': return { ...model, status: 'active' as const };
          case 'stop': return { ...model, status: 'inactive' as const };
          case 'retrain': return { ...model, status: 'training' as const };
          default: return model;
        }
      }
      return model;
    }));
  };

  const handleResolveAlert = (alertId: string) => {
    setAlerts(prev => prev.map(alert =>
      alert.id === alertId ? { ...alert, resolved: true } : alert
    ));

    // Update alerts count
    const updatedAlerts = alerts.map(alert =>
      alert.id === alertId ? { ...alert, resolved: true } : alert
    );
    const unresolvedCount = updatedAlerts.filter(a => !a.resolved).length;
    onAlertsCountChange(unresolvedCount);
  };

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - timestamp.getTime()) / (1000 * 60));
    if (diffInMinutes < 1) return 'À l\'instant';
    if (diffInMinutes < 60) return `Il y a ${diffInMinutes} min`;
    if (diffInMinutes < 1440) return `Il y a ${Math.floor(diffInMinutes / 60)}h`;
    return `Il y a ${Math.floor(diffInMinutes / 1440)} jour(s)`;
  };

  const activeModels = models.filter(m => m.status === 'active').length;
  const trainingModels = models.filter(m => m.status === 'training').length;
  const errorModels = models.filter(m => m.status === 'error').length;
  const unresolvedAlerts = alerts.filter(a => !a.resolved).length;
  const averageAccuracy = models.length > 0
    ? Math.round(models.reduce((sum, m) => sum + m.accuracy, 0) / models.length)
    : 0;
