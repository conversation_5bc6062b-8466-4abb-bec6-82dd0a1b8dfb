import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Avatar,
  Chip,
  Button,
  IconButton,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Divider,
  Paper,
  Badge,
  Tooltip,
  Alert,
  Fade,
  Collapse,
  LinearProgress,
} from '@mui/material';
import {
  Notifications as NotificationsIcon,
  NotificationsActive as ActiveNotificationIcon,
  Security as SecurityIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Error as ErrorIcon,
  CheckCircle as SuccessIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  MarkEmailRead as MarkReadIcon,
  Delete as DeleteIcon,
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
  Schedule as ScheduleIcon,
  Person as PersonIcon,
  Computer as SystemIcon,
  TrendingUp as PerformanceIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
} from '@mui/icons-material';
import { FREE_MOBILE_COLORS } from '../../../utils/constants';

interface Notification {
  id: string;
  type: 'system' | 'user' | 'performance' | 'security';
  severity: 'info' | 'warning' | 'error' | 'success';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  source: string;
  actionRequired: boolean;
  metadata?: {
    userId?: string;
    conversationId?: string;
    metricValue?: number;
    threshold?: number;
  };
}

interface NotificationsTabProps {
  notificationCount: number;
  onNotificationCountChange: (count: number) => void;
  loading?: boolean;
}

const NotificationsTab: React.FC<NotificationsTabProps> = ({
  notificationCount,
  onNotificationCountChange,
  loading = false,
}) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [filteredNotifications, setFilteredNotifications] = useState<Notification[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [severityFilter, setSeverityFilter] = useState<string>('all');
  const [showOnlyUnread, setShowOnlyUnread] = useState(false);
  const [expandedNotification, setExpandedNotification] = useState<string | null>(null);

  // Generate sample notifications
  const generateSampleNotifications = useCallback((): Notification[] => {
    const sampleNotifications: Notification[] = [
      {
        id: 'notif-1',
        type: 'security',
        severity: 'error',
        title: 'Tentative de connexion suspecte détectée',
        message: 'Une tentative de connexion depuis une adresse IP non reconnue a été bloquée pour l\'utilisateur <EMAIL>',
        timestamp: new Date(Date.now() - 5 * 60 * 1000),
        read: false,
        source: 'Système de sécurité',
        actionRequired: true,
        metadata: { userId: '<EMAIL>' },
      },
      {
        id: 'notif-2',
        type: 'performance',
        severity: 'warning',
        title: 'Temps de réponse élevé détecté',
        message: 'Le temps de réponse moyen a dépassé le seuil de 10 secondes (actuel: 12.5s)',
        timestamp: new Date(Date.now() - 15 * 60 * 1000),
        read: false,
        source: 'Monitoring Performance',
        actionRequired: true,
        metadata: { metricValue: 12.5, threshold: 10 },
      },
      {
        id: 'notif-3',
        type: 'user',
        severity: 'info',
        title: 'Nouveau message client prioritaire',
        message: 'Un client VIP a envoyé un message nécessitant une attention immédiate',
        timestamp: new Date(Date.now() - 30 * 60 * 1000),
        read: false,
        source: 'Système de messagerie',
        actionRequired: true,
        metadata: { conversationId: 'conv-12345' },
      },
      {
        id: 'notif-4',
        type: 'system',
        severity: 'success',
        title: 'Mise à jour système terminée',
        message: 'La mise à jour v2.1.3 a été installée avec succès. Toutes les fonctionnalités sont opérationnelles.',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
        read: true,
        source: 'Gestionnaire de système',
        actionRequired: false,
      },
    ];
    return sampleNotifications;
  }, []);

  // Initialize notifications
  useEffect(() => {
    const sampleNotifications = generateSampleNotifications();
    setNotifications(sampleNotifications);
    
    // Update unread count
    const unreadCount = sampleNotifications.filter(n => !n.read).length;
    onNotificationCountChange(unreadCount);
  }, [generateSampleNotifications, onNotificationCountChange]);

  // Filter notifications
  useEffect(() => {
    let filtered = notifications;

    // Search filter
    if (searchQuery) {
      filtered = filtered.filter(notification =>
        notification.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        notification.message.toLowerCase().includes(searchQuery.toLowerCase()) ||
        notification.source.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Type filter
    if (typeFilter !== 'all') {
      filtered = filtered.filter(notification => notification.type === typeFilter);
    }

    // Severity filter
    if (severityFilter !== 'all') {
      filtered = filtered.filter(notification => notification.severity === severityFilter);
    }

    // Unread filter
    if (showOnlyUnread) {
      filtered = filtered.filter(notification => !notification.read);
    }

    // Sort by timestamp (newest first)
    filtered.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

    setFilteredNotifications(filtered);
  }, [notifications, searchQuery, typeFilter, severityFilter, showOnlyUnread]);

  const getNotificationIcon = (type: string, severity: string) => {
    const iconProps = { fontSize: 'medium' as const };
    switch (type) {
      case 'security':
        return <SecurityIcon {...iconProps} />;
      case 'performance':
        return <PerformanceIcon {...iconProps} />;
      case 'user':
        return <PersonIcon {...iconProps} />;
      case 'system':
        return <SystemIcon {...iconProps} />;
      default:
        return <NotificationsIcon {...iconProps} />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'error':
        return '#f44336';
      case 'warning':
        return '#ff9800';
      case 'success':
        return '#4caf50';
      case 'info':
      default:
        return '#2196f3';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'security':
        return '#f44336';
      case 'performance':
        return '#ff9800';
      case 'user':
        return '#4caf50';
      case 'system':
        return '#2196f3';
      default:
        return '#757575';
    }
  };

  const handleMarkAsRead = (notificationId: string) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === notificationId
          ? { ...notification, read: true }
          : notification
      )
    );

    // Update unread count
    const updatedNotifications = notifications.map(notification =>
      notification.id === notificationId
        ? { ...notification, read: true }
        : notification
    );
    const unreadCount = updatedNotifications.filter(n => !n.read).length;
    onNotificationCountChange(unreadCount);
  };

  const handleMarkAllAsRead = () => {
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, read: true }))
    );
    onNotificationCountChange(0);
  };

  const handleDeleteNotification = (notificationId: string) => {
    setNotifications(prev =>
      prev.filter(notification => notification.id !== notificationId)
    );

    // Update unread count
    const updatedNotifications = notifications.filter(notification => notification.id !== notificationId);
    const unreadCount = updatedNotifications.filter(n => !n.read).length;
    onNotificationCountChange(unreadCount);
  };

  const handleExpandNotification = (notificationId: string) => {
    setExpandedNotification(
      expandedNotification === notificationId ? null : notificationId
    );
  };

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - timestamp.getTime()) / (1000 * 60));
    if (diffInMinutes < 1) return 'À l\'instant';
    if (diffInMinutes < 60) return `Il y a ${diffInMinutes} min`;
    if (diffInMinutes < 1440) return `Il y a ${Math.floor(diffInMinutes / 60)}h`;
    return `Il y a ${Math.floor(diffInMinutes / 1440)} jour(s)`;
  };

  const unreadCount = notifications.filter(n => !n.read).length;
