/** * Panneau Queue de Priorité ML * Free Mobile Chatbot Dashboard - Phase 3 Frontend Implementation */ import React, { useState, useEffect } from 'react'; import { Box, Paper, Typography, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TablePagination, Chip, Avatar, IconButton, Tooltip, Button, Dialog, DialogTitle, DialogContent, DialogActions, FormControl, InputLabel, Select, MenuItem, TextField, Alert, LinearProgress, Box as MuiBox, Card, CardContent } from '@mui/material'; import { Visibility, Assignment, TrendingUp, TrendingDown, MonetizationOn, PersonOff, SupportAgent, Info, FilterList, Refresh } from '@mui/icons-material'; import { useAppDispatch, useAppSelector } from '../../../hooks/redux'; import { selectMLState, fetchPriorityQueue, classifyConversation, updateFilters } from '../../../store/slices/mlSlice'; import { ConversationCategory, PriorityQueueItem } from '../../../types/ml'; import mlService from '../../../services/ml.service'; const PriorityQueuePanel: React.FC = () => { const dispatch = useAppDispatch(); const { priorityQueue, filters, loading, error } = useAppSelector(selectMLState); const [page, setPage] = useState(0); const [rowsPerPage, setRowsPerPage] = useState(25); const [selectedItem, setSelectedItem] = useState<PriorityQueueItem | null>(null); const [detailsOpen, setDetailsOpen] = useState(false); const [filtersOpen, setFiltersOpen] = useState(false); const [localFilters, setLocalFilters] = useState({ category: filters.category || '', minPriority: filters.minPriority || 0, maxItems: 100 }); // Chargement initial useEffect(() => { dispatch(fetchPriorityQueue({ limit: 100, minPriority: localFilters.minPriority, category: localFilters.category as ConversationCategory })); }, [dispatch, localFilters.minPriority, localFilters.category]); const handleChangePage = (event: unknown, newPage: number) => { setPage(newPage); }; const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => { setRowsPerPage(parseInt(event.target.value, 10)); setPage(0); }; const handleViewDetails = (item: PriorityQueueItem) => { setSelectedItem(item); setDetailsOpen(true); }; const handleCloseDetails = () => { setDetailsOpen(false); setSelectedItem(null); }; const handleApplyFilters = () => { dispatch(updateFilters({ category: localFilters.category as ConversationCategory, minPriority: localFilters.minPriority })); dispatch(fetchPriorityQueue({ limit: localFilters.maxItems, minPriority: localFilters.minPriority, category: localFilters.category as ConversationCategory })); setFiltersOpen(false); }; const handleRefresh = () => { dispatch(fetchPriorityQueue({ limit: localFilters.maxItems, minPriority: localFilters.minPriority, category: localFilters.category as ConversationCategory })); }; const handleReprocess = async (conversationId: string) => { try { await dispatch(classifyConversation({ conversationId, forceReprocess: true })); // Actualiser la queue après retraitement handleRefresh(); } catch (error) { console.error('Error reprocessing conversation:', error); } }; const getCategoryIcon = (category: ConversationCategory) => { switch (category) { case ConversationCategory.VENTE_OPPORTUNITE: return <MonetizationOn />; case ConversationCategory.RESILIATION_CRITIQUE: return <PersonOff />; case ConversationCategory.SUPPORT_URGENT: return <SupportAgent />; case ConversationCategory.RECLAMATION: return <TrendingDown />; case ConversationCategory.INFO_SIMPLE: return <Info />; default: return <Info />; } }; const getPriorityColor = (score: number) => { if (score >= 90) return 'error'; if (score >= 80) return 'warning'; if (score >= 60) return 'info'; return 'default'; }; const formatCurrency = (amount: number) => { return new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(amount); }; const formatTimestamp = (timestamp: string) => { return new Date(timestamp).toLocaleString('fr-FR'); }; // Données paginées const paginatedItems = priorityQueue.slice( page * rowsPerPage, page * rowsPerPage + rowsPerPage ); return ( <Box> {/* Header avec actions */} <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}> <Typography variant="h5" component="h2"> Queue de Priorité ({priorityQueue.length} conversations) </Typography> <Box sx={{ display: 'flex', gap: 1 }}> <Button variant="outlined" startIcon={<FilterList />} onClick={() => setFiltersOpen(true)} > Filtres </Button> <Button variant="contained" startIcon={<Refresh />} onClick={handleRefresh} disabled={loading.priorityQueue} > Actualiser </Button> </Box> </Box> {/* Métriques rapides */} <Box sx={{ display: 'flex', gap: 2, mb: 3 }}> <Card sx={{ minWidth: 200 }}> <CardContent> <Typography color="textSecondary" gutterBottom> Haute Priorité (≥80) </Typography> <Typography variant="h4" color="error.main"> {priorityQueue.filter(item => item.priority_score >= 80).length} </Typography> </CardContent> </Card> <Card sx={{ minWidth: 200 }}> <CardContent> <Typography color="textSecondary" gutterBottom> Revenus à Risque </Typography> <Typography variant="h4" color="error.main"> {formatCurrency( priorityQueue.reduce((sum, item) => sum + (item.business_impact?.revenueAtRisk || 0), 0 ) )} </Typography> </CardContent> </Card> <Card sx={{ minWidth: 200 }}> <CardContent> <Typography color="textSecondary" gutterBottom> Opportunités </Typography> <Typography variant="h4" color="success.main"> {formatCurrency( priorityQueue.reduce((sum, item) => sum + (item.business_impact?.opportunityValue || 0), 0 ) )} </Typography> </CardContent> </Card> </Box> {/* Indicateur de chargement */} {loading.priorityQueue && <LinearProgress sx={{ mb: 2 }} />} {/* Message d'erreur */} {error.priorityQueue && ( <Alert severity="error" sx={{ mb: 2 }}> {error.priorityQueue} </Alert> )} {/* Table des conversations */} <TableContainer component={Paper}> <Table> <TableHead> <TableRow> <TableCell>Priorité</TableCell> <TableCell>Catégorie</TableCell> <TableCell>Impact Business</TableCell> <TableCell>Timestamp</TableCell> <TableCell>Client</TableCell> <TableCell>Alertes</TableCell> <TableCell>Actions</TableCell> </TableRow> </TableHead> <TableBody> {paginatedItems.map((item) => ( <TableRow key={item.conversation_id} hover> <TableCell> <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}> <Avatar sx={{ bgcolor: mlService.getCategoryColor(item.category), width: 32, height: 32 }} > {item.priority_score} </Avatar> <Chip label={`${item.priority_score}/100`} color={getPriorityColor(item.priority_score)} size="small" /> </Box> </TableCell> <TableCell> <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}> <Avatar sx={{ bgcolor: mlService.getCategoryColor(item.category), width: 24, height: 24 }} > {getCategoryIcon(item.category)} </Avatar> <Typography variant="body2"> {mlService.getCategoryLabel(item.category)} </Typography> </Box> </TableCell> <TableCell> <Box> {item.business_impact?.revenueAtRisk > 0 && ( <Typography variant="body2" color="error.main"> Risque: {formatCurrency(item.business_impact.revenueAtRisk)} </Typography> )} {item.business_impact?.opportunityValue > 0 && ( <Typography variant="body2" color="success.main"> Opportunité: {formatCurrency(item.business_impact.opportunityValue)} </Typography> )} </Box> </TableCell> <TableCell> <Typography variant="body2"> {formatTimestamp(item.timestamp)} </Typography> </TableCell> <TableCell> <Typography variant="body2"> {item.customer?.name || item.customer_id} </Typography> </TableCell> <TableCell> {(item.alerts || 0) > 0 && ( <Chip label={`${item.alerts || 0} alerte${(item.alerts || 0) > 1 ? 's' : ''}`} color="warning" size="small" /> )} </TableCell> <TableCell> <Box sx={{ display: 'flex', gap: 1 }}> <Tooltip title="Voir les détails"> <IconButton size="small" onClick={() => handleViewDetails(item)} > <Visibility /> </IconButton> </Tooltip> <Tooltip title="Retraiter"> <IconButton size="small" onClick={() => handleReprocess(item.conversation_id)} > <Assignment /> </IconButton> </Tooltip> </Box> </TableCell> </TableRow> ))} </TableBody> </Table> <TablePagination rowsPerPageOptions={[10, 25, 50, 100]} component="div" count={priorityQueue.length} rowsPerPage={rowsPerPage} page={page} onPageChange={handleChangePage} onRowsPerPageChange={handleChangeRowsPerPage} labelRowsPerPage="Lignes par page:" labelDisplayedRows={({ from, to, count }) => `${from}-${to} sur ${count !== -1 ? count : `plus de ${to}`}` } /> </TableContainer> {/* Dialog des filtres */} <Dialog open={filtersOpen} onClose={() => setFiltersOpen(false)} maxWidth="sm" fullWidth> <DialogTitle>Filtres de la Queue de Priorité</DialogTitle> <DialogContent> <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}> <FormControl fullWidth> <InputLabel>Catégorie</InputLabel> <Select value={localFilters.category} label="Catégorie" onChange={(e) => setLocalFilters(prev => ({ ...prev, category: e.target.value }))} > <MenuItem value="">Toutes les catégories</MenuItem> <MenuItem value="VENTE_OPPORTUNITE">Opportunité de Vente</MenuItem> <MenuItem value="RESILIATION_CRITIQUE">Résiliation Critique</MenuItem> <MenuItem value="SUPPORT_URGENT">Support Urgent</MenuItem> <MenuItem value="RECLAMATION">Réclamation</MenuItem> <MenuItem value="INFO_SIMPLE">Information Simple</MenuItem> </Select> </FormControl> <TextField label="Priorité minimale" type="number" value={localFilters.minPriority} onChange={(e) => setLocalFilters(prev => ({ ...prev, minPriority: parseInt(e.target.value) || 0 }))} inputProps={{ min: 0, max: 100 }} fullWidth /> <TextField label="Nombre maximum d'éléments" type="number" value={localFilters.maxItems} onChange={(e) => setLocalFilters(prev => ({ ...prev, maxItems: parseInt(e.target.value) || 100 }))} inputProps={{ min: 10, max: 500 }} fullWidth /> </Box> </DialogContent> <DialogActions> <Button onClick={() => setFiltersOpen(false)}>Annuler</Button> <Button onClick={handleApplyFilters} variant="contained"> Appliquer </Button> </DialogActions> </Dialog> {/* Dialog des détails */} <Dialog open={detailsOpen} onClose={handleCloseDetails} maxWidth="md" fullWidth > <DialogTitle> Détails de la Conversation </DialogTitle> <DialogContent> {selectedItem && ( <Box sx={{ mt: 1 }}> {/* Contenu des détails - à implémenter selon les besoins */} <Typography variant="h6" gutterBottom> {mlService.getCategoryLabel(selectedItem.category)} </Typography> <Typography variant="body1"> Score de priorité: {selectedItem.priority_score}/100 </Typography> <Typography variant="body1"> Impact business: {formatCurrency( (selectedItem.business_impact?.revenueAtRisk || 0) + (selectedItem.business_impact?.opportunityValue || 0) )} </Typography> </Box> )} </DialogContent> <DialogActions> <Button onClick={handleCloseDetails}>Fermer</Button> </DialogActions> </Dialog> </Box> ); }; export default PriorityQueuePanel;