/** * Vue d'ensemble ML Dashboard * Free Mobile Chatbot Dashboard - Phase 3 Frontend Implementation */ import React from 'react'; import { Box, Grid, Paper, Typography, Card, CardContent, LinearProgress, Chip, Avatar, List, ListItem, ListItemAvatar, ListItemText, Divider, Alert } from '@mui/material'; import { TrendingUp, TrendingDown, Warning, CheckCircle, Speed, Memory, Storage, Psychology, MonetizationOn, PersonOff, SupportAgent, Notifications } from '@mui/icons-material'; import { useAppSelector } from '../../../hooks/redux'; import { selectMLState, selectHighPriorityClassifications, selectActiveAlerts, selectCriticalAlerts } from '../../../store/slices/mlSlice'; import { ConversationCategory, AlertSeverity, MLPerformanceMetrics } from '../../../types/ml'; import mlService from '../../../services/ml.service'; const MLOverview: React.FC = () => { const mlState = useAppSelector(selectMLState); const highPriorityClassifications = useAppSelector(selectHighPriorityClassifications); const activeAlerts = useAppSelector(selectActiveAlerts); const criticalAlerts = useAppSelector(selectCriticalAlerts); const { metrics, priorityQueue, loading } = mlState; // Calcul des métriques de synthèse const totalRevenueAtRisk = priorityQueue.reduce( (sum, item) => sum + (item.business_impact?.revenueAtRisk || 0), 0 ); const totalOpportunityValue = priorityQueue.reduce( (sum, item) => sum + (item.business_impact?.opportunityValue || 0), 0 ); const categoryDistribution = priorityQueue.reduce((acc, item) => { acc[item.category] = (acc[item.category] || 0) + 1; return acc; }, {} as Record<ConversationCategory, number>); // Métriques de performance ML const mlPerformance: MLPerformanceMetrics = metrics?.ml || { total_classifications: 0, average_processing_time_ms: 0, throughput_per_second: 0, cache_hit_rate: 0, models_loaded: 0, memory_usage_mb: 0, redis_connected: false, fallback: false }; const dashboardMetrics = metrics?.dashboard || {}; const getPerformanceColor = (value: number, thresholds: { good: number; warning: number }) => { if (value >= thresholds.good) return 'success'; if (value >= thresholds.warning) return 'warning'; return 'error'; }; const formatCurrency = (amount: number) => { return new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(amount); }; const formatPercentage = (value: number) => { return `${(value * 100).toFixed(1)}%`; }; return ( <Box> {/* Métriques principales */} <Grid container spacing={3} sx={{ mb: 3 }}> {/* Performance ML */} <Grid item xs={12} md={3}> <Card> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}> <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}> <Speed /> </Avatar> <Typography variant="h6">Performance ML</Typography> </Box> <Typography variant="h4" color="primary" gutterBottom> {mlPerformance.average_processing_time_ms ? mlService.formatProcessingTime(mlPerformance.average_processing_time_ms) : 'N/A' } </Typography> <Typography variant="body2" color="textSecondary"> Temps de traitement moyen </Typography> <LinearProgress variant="determinate" value={Math.min(100, (200 - (mlPerformance.average_processing_time_ms || 200)) / 2)} color={getPerformanceColor( mlPerformance.average_processing_time_ms || 200, { good: 150, warning: 200 } )} sx={{ mt: 1 }} /> </CardContent> </Card> </Grid> {/* Throughput */} <Grid item xs={12} md={3}> <Card> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}> <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}> <Psychology /> </Avatar> <Typography variant="h6">Throughput</Typography> </Box> <Typography variant="h4" color="success.main" gutterBottom> {mlPerformance.throughput_per_second ? mlService.formatThroughput(mlPerformance.throughput_per_second) : 'N/A' } </Typography> <Typography variant="body2" color="textSecondary"> Classifications par seconde </Typography> <LinearProgress variant="determinate" value={Math.min(100, (mlPerformance.throughput_per_second || 0) * 10)} color="success" sx={{ mt: 1 }} /> </CardContent> </Card> </Grid> {/* Cache Hit Rate */} <Grid item xs={12} md={3}> <Card> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}> <Avatar sx={{ bgcolor: 'info.main', mr: 2 }}> <Storage /> </Avatar> <Typography variant="h6">Cache</Typography> </Box> <Typography variant="h4" color="info.main" gutterBottom> {mlPerformance.cache_hit_rate ? formatPercentage(mlPerformance.cache_hit_rate) : 'N/A' } </Typography> <Typography variant="body2" color="textSecondary"> Taux de succès du cache </Typography> <LinearProgress variant="determinate" value={(mlPerformance.cache_hit_rate || 0) * 100} color="info" sx={{ mt: 1 }} /> </CardContent> </Card> </Grid> {/* Mémoire */} <Grid item xs={12} md={3}> <Card> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}> <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}> <Memory /> </Avatar> <Typography variant="h6">Mémoire</Typography> </Box> <Typography variant="h4" color="warning.main" gutterBottom> {mlPerformance.memory_usage_mb ? mlService.formatMemoryUsage(mlPerformance.memory_usage_mb) : 'N/A' } </Typography> <Typography variant="body2" color="textSecondary"> Utilisation mémoire </Typography> <LinearProgress variant="determinate" value={Math.min(100, (mlPerformance.memory_usage_mb || 0) / 20)} color="warning" sx={{ mt: 1 }} /> </CardContent> </Card> </Grid> </Grid> {/* Impact Business */} <Grid container spacing={3} sx={{ mb: 3 }}> <Grid item xs={12} md={4}> <Card> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}> <Avatar sx={{ bgcolor: 'error.main', mr: 2 }}> <PersonOff /> </Avatar> <Typography variant="h6">Risque de Churn</Typography> </Box> <Typography variant="h4" color="error.main" gutterBottom> {formatCurrency(totalRevenueAtRisk)} </Typography> <Typography variant="body2" color="textSecondary"> Revenus à risque </Typography> <Box sx={{ mt: 2 }}> <Chip label={`${priorityQueue.filter(item => item.category === 'RESILIATION_CRITIQUE').length} conversations critiques`} color="error" size="small" /> </Box> </CardContent> </Card> </Grid> <Grid item xs={12} md={4}> <Card> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}> <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}> <MonetizationOn /> </Avatar> <Typography variant="h6">Opportunités</Typography> </Box> <Typography variant="h4" color="success.main" gutterBottom> {formatCurrency(totalOpportunityValue)} </Typography> <Typography variant="body2" color="textSecondary"> Valeur des opportunités </Typography> <Box sx={{ mt: 2 }}> <Chip label={`${priorityQueue.filter(item => item.category === 'VENTE_OPPORTUNITE').length} opportunités détectées`} color="success" size="small" /> </Box> </CardContent> </Card> </Grid> <Grid item xs={12} md={4}> <Card> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}> <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}> <SupportAgent /> </Avatar> <Typography variant="h6">Support Urgent</Typography> </Box> <Typography variant="h4" color="warning.main" gutterBottom> {priorityQueue.filter(item => item.category === 'SUPPORT_URGENT').length} </Typography> <Typography variant="body2" color="textSecondary"> Demandes urgentes </Typography> <Box sx={{ mt: 2 }}> <Chip label={`${activeAlerts.filter(alert => alert.type === 'ESCALATION_NEEDED').length} escalades requises`} color="warning" size="small" /> </Box> </CardContent> </Card> </Grid> </Grid> {/* Alertes et Queue de priorité */} <Grid container spacing={3}> {/* Alertes critiques */} <Grid item xs={12} md={6}> <Paper sx={{ p: 2, height: 400 }}> <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}> <Avatar sx={{ bgcolor: 'error.main', mr: 2 }}> <Notifications /> </Avatar> <Typography variant="h6">Alertes Critiques</Typography> <Chip label={criticalAlerts.length} color="error" size="small" sx={{ ml: 'auto' }} /> </Box> {criticalAlerts.length === 0 ? ( <Alert severity="success" sx={{ mt: 2 }}> Aucune alerte critique en cours </Alert> ) : ( <List sx={{ maxHeight: 300, overflow: 'auto' }}> {criticalAlerts.slice(0, 5).map((alert, index) => ( <React.Fragment key={alert._id}> <ListItem alignItems="flex-start"> <ListItemAvatar> <Avatar sx={{ bgcolor: mlService.getSeverityColor(alert.severity) }}> <Warning /> </Avatar> </ListItemAvatar> <ListItemText primary={alert.title} secondary={ <Box> <Typography variant="body2" color="textSecondary"> {alert.description} </Typography> <Box sx={{ mt: 1, display: 'flex', gap: 1 }}> <Chip label={mlService.getSeverityLabel(alert.severity)} color={alert.severity === 'CRITICAL' ? 'error' : 'warning'} size="small" /> <Chip label={`Priorité: ${alert.priority}`} variant="outlined" size="small" /> </Box> </Box> } /> </ListItem> {index < criticalAlerts.length - 1 && <Divider variant="inset" component="li" />} </React.Fragment> ))} </List> )} </Paper> </Grid> {/* Queue de priorité */} <Grid item xs={12} md={6}> <Paper sx={{ p: 2, height: 400 }}> <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}> <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}> <TrendingUp /> </Avatar> <Typography variant="h6">Queue de Priorité</Typography> <Chip label={priorityQueue.length} color="primary" size="small" sx={{ ml: 'auto' }} /> </Box> {priorityQueue.length === 0 ? ( <Alert severity="info" sx={{ mt: 2 }}> Aucune conversation en attente </Alert> ) : ( <List sx={{ maxHeight: 300, overflow: 'auto' }}> {priorityQueue.slice(0, 5).map((item, index) => ( <React.Fragment key={item.conversation_id}> <ListItem alignItems="flex-start"> <ListItemAvatar> <Avatar sx={{ bgcolor: mlService.getCategoryColor(item.category) }}> {item.priority_score} </Avatar> </ListItemAvatar> <ListItemText primary={mlService.getCategoryLabel(item.category)} secondary={ <Box> <Typography variant="body2" color="textSecondary"> Impact: {formatCurrency( (item.business_impact?.revenueAtRisk || 0) + (item.business_impact?.opportunityValue || 0) )} </Typography> <Box sx={{ mt: 1, display: 'flex', gap: 1 }}> <Chip label={`Score: ${item.priority_score}`} color="primary" size="small" /> <Chip label={new Date(item.timestamp).toLocaleTimeString()} variant="outlined" size="small" /> </Box> </Box> } /> </ListItem> {index < priorityQueue.length - 1 && <Divider variant="inset" component="li" />} </React.Fragment> ))} </List> )} </Paper> </Grid> </Grid> {/* Statut du système */} {loading.metrics && ( <Box sx={{ mt: 2 }}> <LinearProgress /> </Box> )} </Box> ); }; export default MLOverview;