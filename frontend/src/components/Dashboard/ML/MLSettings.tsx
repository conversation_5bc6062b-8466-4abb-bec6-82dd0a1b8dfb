/** * Paramètres ML Dashboard * Free Mobile Chatbot Dashboard - Phase 3 Frontend Implementation */ import React, { useState } from 'react'; import { Box, Paper, Typography, Grid, Card, CardContent, CardActions, Switch, FormControlLabel, TextField, Button, Slider, Alert, Divider, List, ListItem, ListItemText, ListItemSecondaryAction, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, Chip, Avatar } from '@mui/material'; import { Save, Refresh, Delete, Edit, Psychology, Speed, Memory, Storage, Notifications, Security, Tune } from '@mui/icons-material'; import { useSelector } from 'react-redux'; import { selectMLState } from '../../../store/slices/mlSlice'; import mlService from '../../../services/ml.service'; const MLSettings: React.FC = () => { const { metrics } = useSelector(selectMLState); const [settings, setSettings] = useState({ // Paramètres de performance enableCache: true, cacheTimeout: 3600, // secondes maxConcurrentRequests: 10, requestTimeout: 30000, // millisecondes // Paramètres de classification confidenceThreshold: 0.7, priorityThreshold: 80, autoValidationEnabled: false, // Paramètres d'alertes alertsEnabled: true, criticalAlertThreshold: 90, emailNotifications: true, slackNotifications: false, // Paramètres de modèles modelUpdateFrequency: 24, // heures retrainOnFeedback: true, fallbackEnabled: true }); const [unsavedChanges, setUnsavedChanges] = useState(false); const [saveSuccess, setSaveSuccess] = useState(false); const [confirmDialog, setConfirmDialog] = useState({ open: false, title: '', message: '', action: () => {} }); const handleSettingChange = (key: string, value: any) => { setSettings(prev => ({ ...prev, [key]: value })); setUnsavedChanges(true); setSaveSuccess(false); }; const handleSave = async () => { try { // Ici on sauvegarderait les paramètres via l'API console.log('Saving ML settings:', settings); // Simulation d'une sauvegarde await new Promise(resolve => setTimeout(resolve, 1000)); setUnsavedChanges(false); setSaveSuccess(true); setTimeout(() => setSaveSuccess(false), 3000); } catch (error) { console.error('Error saving settings:', error); } }; const handleReset = () => { setConfirmDialog({ open: true, title: 'Réinitialiser les paramètres', message: 'Êtes-vous sûr de vouloir réinitialiser tous les paramètres aux valeurs par défaut ?', action: () => { // Réinitialiser aux valeurs par défaut setSettings({ enableCache: true, cacheTimeout: 3600, maxConcurrentRequests: 10, requestTimeout: 30000, confidenceThreshold: 0.7, priorityThreshold: 80, autoValidationEnabled: false, alertsEnabled: true, criticalAlertThreshold: 90, emailNotifications: true, slackNotifications: false, modelUpdateFrequency: 24, retrainOnFeedback: true, fallbackEnabled: true }); setUnsavedChanges(true); setConfirmDialog(prev => ({ ...prev, open: false })); } }); }; const handleClearCache = () => { setConfirmDialog({ open: true, title: 'Vider le cache', message: 'Êtes-vous sûr de vouloir vider tout le cache ML ? Cette action peut affecter les performances temporairement.', action: async () => { try { // Ici on viderait le cache via l'API console.log('Clearing ML cache'); setConfirmDialog(prev => ({ ...prev, open: false })); } catch (error) { console.error('Error clearing cache:', error); } } }); }; const formatBytes = (bytes: number) => { if (bytes === 0) return '0 Bytes'; const k = 1024; const sizes = ['Bytes', 'KB', 'MB', 'GB']; const i = Math.floor(Math.log(bytes) / Math.log(k)); return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]; }; return ( <Box> {/* Header */} <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}> <Typography variant="h5" component="h2"> Paramètres ML </Typography> <Box sx={{ display: 'flex', gap: 1 }}> <Button variant="outlined" startIcon={<Refresh />} onClick={handleReset} > Réinitialiser </Button> <Button variant="contained" startIcon={<Save />} onClick={handleSave} disabled={!unsavedChanges} > Sauvegarder </Button> </Box> </Box> {/* Messages de statut */} {saveSuccess && ( <Alert severity="success" sx={{ mb: 2 }}> Paramètres sauvegardés avec succès </Alert> )} {unsavedChanges && ( <Alert severity="warning" sx={{ mb: 2 }}> Vous avez des modifications non sauvegardées </Alert> )} <Grid container spacing={3}> {/* Paramètres de Performance */} <Grid item xs={12} md={6}> <Card> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}> <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}> <Speed /> </Avatar> <Typography variant="h6">Performance</Typography> </Box> <Box sx={{ mb: 2 }}> <FormControlLabel control={ <Switch checked={settings.enableCache} onChange={(e) => handleSettingChange('enableCache', e.target.checked)} /> } label="Activer le cache" /> </Box> <TextField label="Timeout du cache (secondes)" type="number" value={settings.cacheTimeout} onChange={(e) => handleSettingChange('cacheTimeout', parseInt(e.target.value))} fullWidth sx={{ mb: 2 }} inputProps={{ min: 60, max: 86400 }} /> <TextField label="Requêtes simultanées max" type="number" value={settings.maxConcurrentRequests} onChange={(e) => handleSettingChange('maxConcurrentRequests', parseInt(e.target.value))} fullWidth sx={{ mb: 2 }} inputProps={{ min: 1, max: 100 }} /> <TextField label="Timeout des requêtes (ms)" type="number" value={settings.requestTimeout} onChange={(e) => handleSettingChange('requestTimeout', parseInt(e.target.value))} fullWidth inputProps={{ min: 1000, max: 120000 }} /> </CardContent> <CardActions> <Button size="small" startIcon={<Delete />} onClick={handleClearCache} color="warning" > Vider le cache </Button> </CardActions> </Card> </Grid> {/* Paramètres de Classification */} <Grid item xs={12} md={6}> <Card> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}> <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}> <Psychology /> </Avatar> <Typography variant="h6">Classification</Typography> </Box> <Box sx={{ mb: 3 }}> <Typography gutterBottom> Seuil de confiance: {(settings.confidenceThreshold * 100).toFixed(0)}% </Typography> <Slider value={settings.confidenceThreshold} onChange={(e, value) => handleSettingChange('confidenceThreshold', value)} min={0.1} max={1.0} step={0.05} marks={[ { value: 0.1, label: '10%' }, { value: 0.5, label: '50%' }, { value: 0.9, label: '90%' } ]} /> </Box> <Box sx={{ mb: 3 }}> <Typography gutterBottom> Seuil de priorité: {settings.priorityThreshold} </Typography> <Slider value={settings.priorityThreshold} onChange={(e, value) => handleSettingChange('priorityThreshold', value)} min={0} max={100} step={5} marks={[ { value: 0, label: '0' }, { value: 50, label: '50' }, { value: 100, label: '100' } ]} /> </Box> <FormControlLabel control={ <Switch checked={settings.autoValidationEnabled} onChange={(e) => handleSettingChange('autoValidationEnabled', e.target.checked)} /> } label="Validation automatique (confiance > 95%)" /> </CardContent> </Card> </Grid> {/* Paramètres d'Alertes */} <Grid item xs={12} md={6}> <Card> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}> <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}> <Notifications /> </Avatar> <Typography variant="h6">Alertes</Typography> </Box> <Box sx={{ mb: 2 }}> <FormControlLabel control={ <Switch checked={settings.alertsEnabled} onChange={(e) => handleSettingChange('alertsEnabled', e.target.checked)} /> } label="Activer les alertes" /> </Box> <Box sx={{ mb: 3 }}> <Typography gutterBottom> Seuil d'alerte critique: {settings.criticalAlertThreshold} </Typography> <Slider value={settings.criticalAlertThreshold} onChange={(e, value) => handleSettingChange('criticalAlertThreshold', value)} min={50} max={100} step={5} disabled={!settings.alertsEnabled} /> </Box> <FormControlLabel control={ <Switch checked={settings.emailNotifications} onChange={(e) => handleSettingChange('emailNotifications', e.target.checked)} disabled={!settings.alertsEnabled} /> } label="Notifications email" /> <FormControlLabel control={ <Switch checked={settings.slackNotifications} onChange={(e) => handleSettingChange('slackNotifications', e.target.checked)} disabled={!settings.alertsEnabled} /> } label="Notifications Slack" /> </CardContent> </Card> </Grid> {/* Paramètres des Modèles */} <Grid item xs={12} md={6}> <Card> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}> <Avatar sx={{ bgcolor: 'info.main', mr: 2 }}> <Tune /> </Avatar> <Typography variant="h6">Modèles ML</Typography> </Box> <TextField label="Fréquence de mise à jour (heures)" type="number" value={settings.modelUpdateFrequency} onChange={(e) => handleSettingChange('modelUpdateFrequency', parseInt(e.target.value))} fullWidth sx={{ mb: 2 }} inputProps={{ min: 1, max: 168 }} /> <FormControlLabel control={ <Switch checked={settings.retrainOnFeedback} onChange={(e) => handleSettingChange('retrainOnFeedback', e.target.checked)} /> } label="Réentraînement sur feedback" /> <FormControlLabel control={ <Switch checked={settings.fallbackEnabled} onChange={(e) => handleSettingChange('fallbackEnabled', e.target.checked)} /> } label="Mode de secours activé" /> </CardContent> </Card> </Grid> {/* Informations Système */} <Grid item xs={12}> <Card> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}> <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}> <Memory /> </Avatar> <Typography variant="h6">Informations Système</Typography> </Box> <Grid container spacing={2}> <Grid item xs={12} md={3}> <Box sx={{ textAlign: 'center' }}> <Typography variant="h4" color="primary"> {metrics?.ml?.models_loaded || 0} </Typography> <Typography variant="body2" color="textSecondary"> Modèles chargés </Typography> </Box> </Grid> <Grid item xs={12} md={3}> <Box sx={{ textAlign: 'center' }}> <Typography variant="h4" color="success.main"> {metrics?.ml?.memory_usage_mb ? formatBytes(metrics.ml.memory_usage_mb * 1024 * 1024) : 'N/A' } </Typography> <Typography variant="body2" color="textSecondary"> Mémoire utilisée </Typography> </Box> </Grid> <Grid item xs={12} md={3}> <Box sx={{ textAlign: 'center' }}> <Typography variant="h4" color="info.main"> {metrics?.ml?.cache_hit_rate ? `${(metrics.ml.cache_hit_rate * 100).toFixed(1)}%` : 'N/A' } </Typography> <Typography variant="body2" color="textSecondary"> Taux de cache </Typography> </Box> </Grid> <Grid item xs={12} md={3}> <Box sx={{ textAlign: 'center' }}> <Chip label={metrics?.ml?.redis_connected ? 'Connecté' : 'Déconnecté'} color={metrics?.ml?.redis_connected ? 'success' : 'error'} /> <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}> Statut Redis </Typography> </Box> </Grid> </Grid> </CardContent> </Card> </Grid> </Grid> {/* Dialog de confirmation */} <Dialog open={confirmDialog.open} onClose={() => setConfirmDialog(prev => ({ ...prev, open: false }))} > <DialogTitle>{confirmDialog.title}</DialogTitle> <DialogContent> <Typography>{confirmDialog.message}</Typography> </DialogContent> <DialogActions> <Button onClick={() => setConfirmDialog(prev => ({ ...prev, open: false }))}> Annuler </Button> <Button onClick={confirmDialog.action} variant="contained" color="primary"> Confirmer </Button> </DialogActions> </Dialog> </Box> ); }; export default MLSettings;