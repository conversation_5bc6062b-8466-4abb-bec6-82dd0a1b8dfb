/** * Métriques de Performance ML * Free Mobile Chatbot Dashboard - Phase 3 Frontend Implementation */ import React, { useState, useEffect } from 'react'; import { Box, Paper, Typography, Grid, Card, CardContent, FormControl, InputLabel, Select, MenuItem, LinearProgress, Alert, Chip, Avatar, List, ListItem, ListItemAvatar, ListItemText, Divider, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from '@mui/material'; import { Speed, Memory, Storage, Psychology, TrendingUp, TrendingDown, CheckCircle, Error, Timeline, Assessment, Category, Verified } from '@mui/icons-material'; import { useAppDispatch, useAppSelector } from '../../../hooks/redux'; import { selectMLState, fetchPerformanceMetrics, fetchClassificationStats } from '../../../store/slices/mlSlice'; import mlService from '../../../services/ml.service'; const PerformanceMetrics: React.FC = () => { const dispatch = useAppDispatch(); const { metrics, loading, error } = useAppSelector(selectMLState); const [timeRange, setTimeRange] = useState<'1h' | '24h' | '7d' | '30d'>('24h'); const [classificationStats, setClassificationStats] = useState<any[]>([]); // Chargement des données useEffect(() => { dispatch(fetchPerformanceMetrics(timeRange)); // Charger les statistiques de classification const endDate = new Date(); const startDate = new Date(); switch (timeRange) { case '1h': startDate.setHours(startDate.getHours() - 1); break; case '24h': startDate.setDate(startDate.getDate() - 1); break; case '7d': startDate.setDate(startDate.getDate() - 7); break; case '30d': startDate.setDate(startDate.getDate() - 30); break; } dispatch(fetchClassificationStats({ startDate: startDate.toISOString(), endDate: endDate.toISOString(), groupBy: timeRange === '1h' ? 'hour' : timeRange === '24h' ? 'hour' : 'day' })).then((result: any) => { if (result.payload) { setClassificationStats(result.payload); } }); }, [dispatch, timeRange]); const handleTimeRangeChange = (event: any) => { setTimeRange(event.target.value); }; // Métriques ML et dashboard const mlMetrics = metrics?.ml || { total_classifications: 0, average_processing_time_ms: 0, throughput_per_second: 0, cache_hit_rate: 0, models_loaded: 0, memory_usage_mb: 0, redis_connected: false, fallback: false }; const dashboardMetrics = metrics?.dashboard || { recentClassifications: 0, activeAlerts: 0, highPriorityConversations: 0, connectedUsers: 0, timestamp: new Date().toISOString() }; // Calcul des tendances (simulation) const getTrend = (current: number, threshold: number) => { if (current > threshold) return 'up'; if (current < threshold * 0.8) return 'down'; return 'stable'; }; const formatNumber = (num: number) => { return new Intl.NumberFormat('fr-FR').format(num); }; const getPerformanceStatus = (value: number, thresholds: { good: number; warning: number }) => { if (value >= thresholds.good) return { color: 'success', status: 'Excellent' }; if (value >= thresholds.warning) return { color: 'warning', status: 'Correct' }; return { color: 'error', status: 'Critique' }; }; const formatPercentage = (value: number) => { return `${(value * 100).toFixed(1)}%`; }; return ( <Box> {/* Header avec sélecteur de période */} <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}> <Typography variant="h5" component="h2"> Métriques de Performance ML </Typography> <FormControl sx={{ minWidth: 120 }}> <InputLabel>Période</InputLabel> <Select value={timeRange} label="Période" onChange={handleTimeRangeChange} > <MenuItem value="1h">1 heure</MenuItem> <MenuItem value="24h">24 heures</MenuItem> <MenuItem value="7d">7 jours</MenuItem> <MenuItem value="30d">30 jours</MenuItem> </Select> </FormControl> </Box> {/* Indicateur de chargement */} {loading.metrics && <LinearProgress sx={{ mb: 2 }} />} {/* Message d'erreur */} {error.metrics && ( <Alert severity="error" sx={{ mb: 2 }}> {error.metrics} </Alert> )} {/* Métriques principales */} <Grid container spacing={3} sx={{ mb: 3 }}> {/* Temps de traitement */} <Grid item xs={12} md={3}> <Card> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}> <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}> <Speed /> </Avatar> <Typography variant="h6">Temps de Traitement</Typography> </Box> <Typography variant="h4" color="primary" gutterBottom> {mlMetrics.average_processing_time_ms ? mlService.formatProcessingTime(mlMetrics.average_processing_time_ms) : 'N/A' } </Typography> <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}> <Chip label={getPerformanceStatus( mlMetrics.average_processing_time_ms || 300, { good: 150, warning: 200 } ).status} color={getPerformanceStatus( mlMetrics.average_processing_time_ms || 300, { good: 150, warning: 200 } ).color as any} size="small" /> {getTrend(mlMetrics.average_processing_time_ms || 0, 150) === 'up' ? ( <TrendingUp color="error" /> ) : getTrend(mlMetrics.average_processing_time_ms || 0, 150) === 'down' ? ( <TrendingDown color="success" /> ) : null} </Box> </CardContent> </Card> </Grid> {/* Throughput */} <Grid item xs={12} md={3}> <Card> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}> <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}> <Psychology /> </Avatar> <Typography variant="h6">Throughput</Typography> </Box> <Typography variant="h4" color="success.main" gutterBottom> {mlMetrics.throughput_per_second ? mlService.formatThroughput(mlMetrics.throughput_per_second) : 'N/A' } </Typography> <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}> <Chip label={`${mlMetrics.total_classifications || 0} classifications`} color="success" size="small" /> <TrendingUp color="success" /> </Box> </CardContent> </Card> </Grid> {/* Cache Hit Rate */} <Grid item xs={12} md={3}> <Card> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}> <Avatar sx={{ bgcolor: 'info.main', mr: 2 }}> <Storage /> </Avatar> <Typography variant="h6">Cache</Typography> </Box> <Typography variant="h4" color="info.main" gutterBottom> {mlMetrics.cache_hit_rate ? mlService.formatCacheHitRate(mlMetrics.cache_hit_rate) : 'N/A' } </Typography> <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}> <Chip label={getPerformanceStatus( (mlMetrics.cache_hit_rate || 0) * 100, { good: 80, warning: 60 } ).status} color={getPerformanceStatus( (mlMetrics.cache_hit_rate || 0) * 100, { good: 80, warning: 60 } ).color as any} size="small" /> </Box> </CardContent> </Card> </Grid> {/* Mémoire */} <Grid item xs={12} md={3}> <Card> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}> <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}> <Memory /> </Avatar> <Typography variant="h6">Mémoire</Typography> </Box> <Typography variant="h4" color="warning.main" gutterBottom> {mlMetrics.memory_usage_mb ? mlService.formatMemoryUsage(mlMetrics.memory_usage_mb) : 'N/A' } </Typography> <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}> <Chip label={`${mlMetrics.models_loaded || 0} modèles`} color="warning" size="small" /> </Box> </CardContent> </Card> </Grid> </Grid> {/* Métriques de classification */} <Grid container spacing={3} sx={{ mb: 3 }}> <Grid item xs={12} md={6}> <Paper sx={{ p: 2 }}> <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}> <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}> <Assessment /> </Avatar> <Typography variant="h6">Métriques de Classification</Typography> </Box> <List> <ListItem> <ListItemAvatar> <Avatar sx={{ bgcolor: 'success.main' }}> <CheckCircle /> </Avatar> </ListItemAvatar> <ListItemText primary="Total Classifications" secondary={formatNumber(mlMetrics.total_classifications || 0)} /> </ListItem> <ListItem> <ListItemAvatar> <Avatar sx={{ bgcolor: 'info.main' }}> <Verified /> </Avatar> </ListItemAvatar> <ListItemText primary="Validées Humainement" secondary={`${formatNumber(0)} (${formatPercentage(0)})`} /> </ListItem> <ListItem> <ListItemAvatar> <Avatar sx={{ bgcolor: 'warning.main' }}> <Timeline /> </Avatar> </ListItemAvatar> <ListItemText primary="Confiance Moyenne" secondary={formatPercentage(0)} /> </ListItem> <ListItem> <ListItemAvatar> <Avatar sx={{ bgcolor: 'error.main' }}> <Error /> </Avatar> </ListItemAvatar> <ListItemText primary="Haute Priorité" secondary={formatNumber(dashboardMetrics.highPriorityConversations || 0)} /> </ListItem> </List> </Paper> </Grid> <Grid item xs={12} md={6}> <Paper sx={{ p: 2 }}> <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}> <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}> <Category /> </Avatar> <Typography variant="h6">Répartition par Catégorie</Typography> </Box> {classificationStats && classificationStats.length > 0 ? ( <TableContainer> <Table size="small"> <TableHead> <TableRow> <TableCell>Catégorie</TableCell> <TableCell align="right">Nombre</TableCell> <TableCell align="right">Priorité Moy.</TableCell> <TableCell align="right">Confiance Moy.</TableCell> </TableRow> </TableHead> <TableBody> {classificationStats.map((category: any) => ( <TableRow key={category._id}> <TableCell> <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}> <Avatar sx={{ bgcolor: mlService.getCategoryColor(category._id), width: 24, height: 24 }} /> {mlService.getCategoryLabel(category._id)} </Box> </TableCell> <TableCell align="right">{formatNumber(category.count)}</TableCell> <TableCell align="right">{category.avgPriority?.toFixed(1) || 'N/A'}</TableCell> <TableCell align="right">{formatPercentage(category.avgConfidence || 0)}</TableCell> </TableRow> ))} </TableBody> </Table> </TableContainer> ) : ( <Alert severity="info"> Aucune donnée de classification disponible pour cette période </Alert> )} </Paper> </Grid> </Grid> {/* Statut du système */} <Paper sx={{ p: 2 }}> <Typography variant="h6" gutterBottom> Statut du Système ML </Typography> <Grid container spacing={2}> <Grid item xs={12} md={4}> <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}> <Avatar sx={{ bgcolor: mlMetrics.redis_connected ? 'success.main' : 'error.main' }}> <Storage /> </Avatar> <Box> <Typography variant="body1">Redis</Typography> <Typography variant="body2" color="textSecondary"> {mlMetrics.redis_connected ? 'Connecté' : 'Déconnecté'} </Typography> </Box> </Box> </Grid> <Grid item xs={12} md={4}> <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}> <Avatar sx={{ bgcolor: 'primary.main' }}> <Psychology /> </Avatar> <Box> <Typography variant="body1">Modèles Chargés</Typography> <Typography variant="body2" color="textSecondary"> {mlMetrics.models_loaded || 0} modèles actifs </Typography> </Box> </Box> </Grid> <Grid item xs={12} md={4}> <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}> <Avatar sx={{ bgcolor: dashboardMetrics.connectedUsers > 0 ? 'success.main' : 'warning.main' }}> <Timeline /> </Avatar> <Box> <Typography variant="body1">Utilisateurs Connectés</Typography> <Typography variant="body2" color="textSecondary"> {dashboardMetrics.connectedUsers || 0} utilisateurs </Typography> </Box> </Box> </Grid> </Grid> </Paper> </Box> ); }; export default PerformanceMetrics;