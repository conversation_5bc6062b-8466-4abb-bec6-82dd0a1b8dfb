/** * Historique des Classifications ML * Free Mobile Chatbot Dashboard - Phase 3 Frontend Implementation */ import React, { useState } from 'react'; import { Box, Paper, Typography, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TablePagination, Chip, Avatar, IconButton, Tooltip, Button, Dialog, DialogTitle, DialogContent, DialogActions, FormControl, InputLabel, Select, MenuItem, TextField, Alert, LinearProgress, Box as MuiBox } from '@mui/material'; import { Visibility, CheckCircle, Cancel, Edit, FilterList, Refresh } from '@mui/icons-material'; import { useAppDispatch, useAppSelector } from '../../../hooks/redux'; import { selectFilteredClassifications, selectMLState, validateClassification, updateFilters } from '../../../store/slices/mlSlice'; import { ConversationClassification, ConversationCategory } from '../../../types/ml'; import mlService from '../../../services/ml.service'; const ClassificationHistory: React.FC = () => { const dispatch = useAppDispatch(); const classifications = useAppSelector(selectFilteredClassifications); const { loading, error, filters } = useAppSelector(selectMLState); const [page, setPage] = useState(0); const [rowsPerPage, setRowsPerPage] = useState(25); const [selectedClassification, setSelectedClassification] = useState<ConversationClassification | null>(null); const [validationOpen, setValidationOpen] = useState(false); const [filtersOpen, setFiltersOpen] = useState(false); const [validationForm, setValidationForm] = useState({ isCorrect: true, correctedCategory: undefined as ConversationCategory | undefined, feedback: '', confidence: 0.8 }); const handleChangePage = (event: unknown, newPage: number) => { setPage(newPage); }; const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => { setRowsPerPage(parseInt(event.target.value, 10)); setPage(0); }; const handleValidate = (classification: ConversationClassification) => { setSelectedClassification(classification); setValidationForm({ isCorrect: true, correctedCategory: undefined, feedback: '', confidence: classification.confidence }); setValidationOpen(true); }; const handleSubmitValidation = async () => { if (!selectedClassification) return; try { await dispatch(validateClassification({ classificationId: selectedClassification._id, isCorrect: validationForm.isCorrect, correctedCategory: validationForm.correctedCategory || undefined, feedback: validationForm.feedback, confidence: validationForm.confidence })); setValidationOpen(false); setSelectedClassification(null); } catch (error) { console.error('Error validating classification:', error); } }; const handleApplyFilters = () => { // Les filtres sont déjà appliqués via le sélecteur setFiltersOpen(false); }; const formatTimestamp = (timestamp: string) => { return new Date(timestamp).toLocaleString('fr-FR'); }; const formatCurrency = (amount: number) => { return new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(amount); }; const getPriorityColor = (score: number) => { if (score >= 90) return 'error'; if (score >= 80) return 'warning'; if (score >= 60) return 'info'; return 'default'; }; // Données paginées const paginatedClassifications = classifications.slice( page * rowsPerPage, page * rowsPerPage + rowsPerPage ); return ( <Box> {/* Header avec actions */} <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}> <Typography variant="h5" component="h2"> Historique des Classifications ({classifications.length} classifications) </Typography> <Box sx={{ display: 'flex', gap: 1 }}> <Button variant="outlined" startIcon={<FilterList />} onClick={() => setFiltersOpen(true)} > Filtres </Button> </Box> </Box> {/* Indicateur de chargement */} {loading.classifications && <LinearProgress sx={{ mb: 2 }} />} {/* Message d'erreur */} {error.classifications && ( <Alert severity="error" sx={{ mb: 2 }}> {error.classifications} </Alert> )} {/* Table des classifications */} <TableContainer component={Paper}> <Table> <TableHead> <TableRow> <TableCell>Conversation</TableCell> <TableCell>Catégorie</TableCell> <TableCell>Priorité</TableCell> <TableCell>Confiance</TableCell> <TableCell>Impact Business</TableCell> <TableCell>Sentiment</TableCell> <TableCell>Validé</TableCell> <TableCell>Date</TableCell> <TableCell>Actions</TableCell> </TableRow> </TableHead> <TableBody> {paginatedClassifications.map((classification) => ( <TableRow key={classification._id} hover> <TableCell> <Typography variant="body2"> {classification.conversationId.slice(-8)} </Typography> </TableCell> <TableCell> <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}> <Avatar sx={{ bgcolor: mlService.getCategoryColor(classification.category), width: 24, height: 24 }} /> <Typography variant="body2"> {mlService.getCategoryLabel(classification.category)} </Typography> </Box> </TableCell> <TableCell> <Chip label={`${classification.priorityScore}/100`} color={getPriorityColor(classification.priorityScore)} size="small" /> </TableCell> <TableCell> <Typography variant="body2"> {(classification.confidence * 100).toFixed(1)}% </Typography> </TableCell> <TableCell> <Box> {classification.businessImpact.revenueAtRisk > 0 && ( <Typography variant="body2" color="error.main"> Risque: {formatCurrency(classification.businessImpact.revenueAtRisk)} </Typography> )} {classification.businessImpact.opportunityValue > 0 && ( <Typography variant="body2" color="success.main"> Opp: {formatCurrency(classification.businessImpact.opportunityValue)} </Typography> )} </Box> </TableCell> <TableCell> <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}> <Typography variant="body2"> {classification.sentiment.score.toFixed(2)} </Typography> <Chip label={classification.sentiment.trend} size="small" color={ classification.sentiment.trend === 'improving' ? 'success' : classification.sentiment.trend === 'declining' ? 'error' : 'default' } /> </Box> </TableCell> <TableCell> {classification.humanValidated ? ( <Chip icon={<CheckCircle />} label="Validé" color="success" size="small" /> ) : ( <Chip icon={<Cancel />} label="Non validé" color="warning" size="small" /> )} </TableCell> <TableCell> <Typography variant="body2"> {formatTimestamp(classification.processedAt)} </Typography> </TableCell> <TableCell> <Box sx={{ display: 'flex', gap: 1 }}> <Tooltip title="Voir les détails"> <IconButton size="small"> <Visibility /> </IconButton> </Tooltip> {!classification.humanValidated && ( <Tooltip title="Valider"> <IconButton size="small" onClick={() => handleValidate(classification)} > <Edit /> </IconButton> </Tooltip> )} </Box> </TableCell> </TableRow> ))} </TableBody> </Table> <TablePagination rowsPerPageOptions={[10, 25, 50, 100]} component="div" count={classifications.length} rowsPerPage={rowsPerPage} page={page} onPageChange={handleChangePage} onRowsPerPageChange={handleChangeRowsPerPage} labelRowsPerPage="Lignes par page:" labelDisplayedRows={({ from, to, count }) => `${from}-${to} sur ${count !== -1 ? count : `plus de ${to}`}` } /> </TableContainer> {/* Dialog de validation */} <Dialog open={validationOpen} onClose={() => setValidationOpen(false)} maxWidth="sm" fullWidth> <DialogTitle>Validation de Classification</DialogTitle> <DialogContent> <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}> <FormControl fullWidth> <InputLabel>Classification correcte ?</InputLabel> <Select value={validationForm.isCorrect ? 'correct' : 'incorrect'} label="Classification correcte ?" onChange={(e) => setValidationForm(prev => ({ ...prev, isCorrect: e.target.value === 'correct' }))} > <MenuItem value="correct">Correcte</MenuItem> <MenuItem value="incorrect">Incorrecte</MenuItem> </Select> </FormControl> {!validationForm.isCorrect && ( <FormControl fullWidth> <InputLabel>Catégorie correcte</InputLabel> <Select value={validationForm.correctedCategory} label="Catégorie correcte" onChange={(e) => setValidationForm(prev => ({ ...prev, correctedCategory: e.target.value as ConversationCategory }))} > <MenuItem value="VENTE_OPPORTUNITE">Opportunité de Vente</MenuItem> <MenuItem value="RESILIATION_CRITIQUE">Résiliation Critique</MenuItem> <MenuItem value="SUPPORT_URGENT">Support Urgent</MenuItem> <MenuItem value="RECLAMATION">Réclamation</MenuItem> <MenuItem value="INFO_SIMPLE">Information Simple</MenuItem> </Select> </FormControl> )} <TextField label="Commentaires" multiline rows={3} value={validationForm.feedback} onChange={(e) => setValidationForm(prev => ({ ...prev, feedback: e.target.value }))} fullWidth /> <TextField label="Confiance (0-1)" type="number" value={validationForm.confidence} onChange={(e) => setValidationForm(prev => ({ ...prev, confidence: parseFloat(e.target.value) || 0 }))} inputProps={{ min: 0, max: 1, step: 0.1 }} fullWidth /> </Box> </DialogContent> <DialogActions> <Button onClick={() => setValidationOpen(false)}>Annuler</Button> <Button onClick={handleSubmitValidation} variant="contained"> Valider </Button> </DialogActions> </Dialog> {/* Dialog des filtres */} <Dialog open={filtersOpen} onClose={() => setFiltersOpen(false)} maxWidth="sm" fullWidth> <DialogTitle>Filtres des Classifications</DialogTitle> <DialogContent> <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}> <FormControl fullWidth> <InputLabel>Catégorie</InputLabel> <Select value={filters.category || ''} label="Catégorie" onChange={(e) => dispatch(updateFilters({ category: e.target.value as ConversationCategory }))} > <MenuItem value="">Toutes les catégories</MenuItem> <MenuItem value="VENTE_OPPORTUNITE">Opportunité de Vente</MenuItem> <MenuItem value="RESILIATION_CRITIQUE">Résiliation Critique</MenuItem> <MenuItem value="SUPPORT_URGENT">Support Urgent</MenuItem> <MenuItem value="RECLAMATION">Réclamation</MenuItem> <MenuItem value="INFO_SIMPLE">Information Simple</MenuItem> </Select> </FormControl> <TextField label="Priorité minimale" type="number" value={filters.minPriority} onChange={(e) => dispatch(updateFilters({ minPriority: parseInt(e.target.value) || 0 }))} inputProps={{ min: 0, max: 100 }} fullWidth /> <FormControl fullWidth> <InputLabel>Validation</InputLabel> <Select value={filters.showOnlyUnvalidated ? 'unvalidated' : 'all'} label="Validation" onChange={(e) => dispatch(updateFilters({ showOnlyUnvalidated: e.target.value === 'unvalidated' }))} > <MenuItem value="all">Toutes</MenuItem> <MenuItem value="unvalidated">Non validées seulement</MenuItem> </Select> </FormControl> </Box> </DialogContent> <DialogActions> <Button onClick={() => setFiltersOpen(false)}>Annuler</Button> <Button onClick={handleApplyFilters} variant="contained"> Appliquer </Button> </DialogActions> </Dialog> </Box> ); }; export default ClassificationHistory;