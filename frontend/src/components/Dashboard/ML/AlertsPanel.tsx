/** * <PERSON>nea<PERSON> d'Alertes ML * Free Mobile Chatbot Dashboard - Phase 3 Frontend Implementation */ import React, { useState, useEffect } from 'react'; import { Box, Paper, Typography, List, ListItem, ListItemAvatar, ListItemText, ListItemSecondaryAction, Avatar, IconButton, Chip, Button, Dialog, DialogTitle, DialogContent, DialogActions, FormControl, InputLabel, Select, MenuItem, Alert, LinearProgress, Divider, Card, CardContent, Grid, Tooltip, Badge } from '@mui/material'; import { CheckCircle, Warning, Error, Info, NotificationsActive, Assignment, Visibility, FilterList, Refresh, MonetizationOn, PersonOff, SupportAgent, Build, Receipt, SentimentVeryDissatisfied, Computer, Speed } from '@mui/icons-material'; import { useAppDispatch, useAppSelector } from '../../../hooks/redux'; import { selectMLState, selectActiveAlerts, selectCriticalAlerts, fetchAlerts, acknowledgeAlert } from '../../../store/slices/mlSlice'; import { AdminAlert, AlertType, AlertSeverity, AlertStatus } from '../../../types/ml'; import mlService from '../../../services/ml.service'; const AlertsPanel: React.FC = () => { const dispatch = useAppDispatch(); const { alerts, loading, error } = useAppSelector(selectMLState); const activeAlerts = useAppSelector(selectActiveAlerts); const criticalAlerts = useAppSelector(selectCriticalAlerts); const [selectedAlert, setSelectedAlert] = useState<AdminAlert | null>(null); const [detailsOpen, setDetailsOpen] = useState(false); const [filtersOpen, setFiltersOpen] = useState(false); const [filters, setFilters] = useState({ status: '' as AlertStatus | '', severity: '' as AlertSeverity | '', type: '' as AlertType | '' }); // Chargement initial useEffect(() => { dispatch(fetchAlerts({ limit: 100 })); }, [dispatch]); const handleViewDetails = (alert: AdminAlert) => { setSelectedAlert(alert); setDetailsOpen(true); }; const handleCloseDetails = () => { setDetailsOpen(false); setSelectedAlert(null); }; const handleAcknowledge = async (alertId: string) => { try { await dispatch(acknowledgeAlert(alertId)); } catch (error) { console.error('Error acknowledging alert:', error); } }; const handleApplyFilters = () => { const filterParams: any = { limit: 100 }; if (filters.status) filterParams.status = filters.status; if (filters.severity) filterParams.severity = filters.severity; if (filters.type) filterParams.type = filters.type; dispatch(fetchAlerts(filterParams)); setFiltersOpen(false); }; const handleRefresh = () => { dispatch(fetchAlerts({ limit: 100 })); }; const getAlertIcon = (type: AlertType) => { switch (type) { case AlertType.VENTE_OPPORTUNITY: return <MonetizationOn />; case AlertType.CHURN_RISK: return <PersonOff />; case AlertType.ESCALATION_NEEDED: return <SupportAgent />; case AlertType.TECHNICAL_ISSUE: return <Build />; case AlertType.BILLING_DISPUTE: return <Receipt />; case AlertType.SATISFACTION_CRITICAL: return <SentimentVeryDissatisfied />; case AlertType.SYSTEM_ANOMALY: return <Computer />; case AlertType.PERFORMANCE_DEGRADATION: return <Speed />; default: return <Info />; } }; const getSeverityIcon = (severity: AlertSeverity) => { switch (severity) { case AlertSeverity.CRITICAL: return <Error />; case AlertSeverity.HIGH: return <Warning />; case AlertSeverity.MEDIUM: return <Info />; case AlertSeverity.LOW: return <CheckCircle />; default: return <Info />; } }; const getStatusColor = (status: AlertStatus) => { switch (status) { case AlertStatus.ACTIVE: return 'error'; case AlertStatus.ACKNOWLEDGED: return 'warning'; case AlertStatus.IN_PROGRESS: return 'info'; case AlertStatus.RESOLVED: return 'success'; case AlertStatus.DISMISSED: return 'default'; default: return 'default'; } }; const getTypeLabel = (type: AlertType) => { const labels: Record<AlertType, string> = { [AlertType.VENTE_OPPORTUNITY]: 'Opportunité de Vente', [AlertType.CHURN_RISK]: 'Risque de Churn', [AlertType.ESCALATION_NEEDED]: 'Escalade Requise', [AlertType.TECHNICAL_ISSUE]: 'Problème Technique', [AlertType.BILLING_DISPUTE]: 'Litige Facturation', [AlertType.SATISFACTION_CRITICAL]: 'Satisfaction Critique', [AlertType.SYSTEM_ANOMALY]: 'Anomalie Système', [AlertType.PERFORMANCE_DEGRADATION]: 'Dégradation Performance' }; return labels[type] || type; }; const formatTimestamp = (timestamp: string) => { return new Date(timestamp).toLocaleString('fr-FR'); }; // Statistiques des alertes const alertStats = { total: alerts.length, active: activeAlerts.length, critical: criticalAlerts.length, acknowledged: alerts.filter(a => a.status === AlertStatus.ACKNOWLEDGED).length, resolved: alerts.filter(a => a.status === AlertStatus.RESOLVED).length }; // Groupement par type const alertsByType = alerts.reduce((acc, alert) => { acc[alert.type] = (acc[alert.type] || 0) + 1; return acc; }, {} as Record<AlertType, number>); return ( <Box> {/* Header avec actions */} <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}> <Typography variant="h5" component="h2"> Alertes ML ({alerts.length} alertes) </Typography> <Box sx={{ display: 'flex', gap: 1 }}> <Button variant="outlined" startIcon={<FilterList />} onClick={() => setFiltersOpen(true)} > Filtres </Button> <Button variant="contained" startIcon={<Refresh />} onClick={handleRefresh} disabled={loading.alerts} > Actualiser </Button> </Box> </Box> {/* Statistiques rapides */} <Grid container spacing={2} sx={{ mb: 3 }}> <Grid item xs={12} sm={6} md={2.4}> <Card> <CardContent sx={{ textAlign: 'center' }}> <Typography color="textSecondary" gutterBottom> Total </Typography> <Typography variant="h4"> {alertStats.total} </Typography> </CardContent> </Card> </Grid> <Grid item xs={12} sm={6} md={2.4}> <Card> <CardContent sx={{ textAlign: 'center' }}> <Typography color="textSecondary" gutterBottom> Actives </Typography> <Typography variant="h4" color="error.main"> {alertStats.active} </Typography> </CardContent> </Card> </Grid> <Grid item xs={12} sm={6} md={2.4}> <Card> <CardContent sx={{ textAlign: 'center' }}> <Typography color="textSecondary" gutterBottom> Critiques </Typography> <Typography variant="h4" color="error.main"> {alertStats.critical} </Typography> </CardContent> </Card> </Grid> <Grid item xs={12} sm={6} md={2.4}> <Card> <CardContent sx={{ textAlign: 'center' }}> <Typography color="textSecondary" gutterBottom> Acquittées </Typography> <Typography variant="h4" color="warning.main"> {alertStats.acknowledged} </Typography> </CardContent> </Card> </Grid> <Grid item xs={12} sm={6} md={2.4}> <Card> <CardContent sx={{ textAlign: 'center' }}> <Typography color="textSecondary" gutterBottom> Résolues </Typography> <Typography variant="h4" color="success.main"> {alertStats.resolved} </Typography> </CardContent> </Card> </Grid> </Grid> {/* Indicateur de chargement */} {loading.alerts && <LinearProgress sx={{ mb: 2 }} />} {/* Message d'erreur */} {error.alerts && ( <Alert severity="error" sx={{ mb: 2 }}> {error.alerts} </Alert> )} {/* Liste des alertes */} <Paper> <List> {alerts.length === 0 ? ( <ListItem> <Alert severity="info" sx={{ width: '100%' }}> Aucune alerte disponible </Alert> </ListItem> ) : ( alerts.map((alert, index) => ( <React.Fragment key={alert._id}> <ListItem alignItems="flex-start"> <ListItemAvatar> <Badge badgeContent={alert.priority} color={alert.severity === AlertSeverity.CRITICAL ? 'error' : 'primary'} max={99} > <Avatar sx={{ bgcolor: mlService.getSeverityColor(alert.severity), width: 48, height: 48 }} > {getAlertIcon(alert.type)} </Avatar> </Badge> </ListItemAvatar> <ListItemText primary={ <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}> <Typography variant="h6" component="span"> {alert.title} </Typography> <Chip label={mlService.getSeverityLabel(alert.severity)} color={mlService.getSeverityColor(alert.severity) as any} size="small" /> <Chip label={alert.status} color={getStatusColor(alert.status) as any} variant="outlined" size="small" /> </Box> } secondary={ <Box> <Typography variant="body2" color="textSecondary" paragraph> {alert.description} </Typography> <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 1 }}> <Chip label={getTypeLabel(alert.type)} variant="outlined" size="small" icon={getAlertIcon(alert.type)} /> <Chip label={`Priorité: ${alert.priority}`} variant="outlined" size="small" /> <Chip label={formatTimestamp(alert.createdAt)} variant="outlined" size="small" /> </Box> {alert.assignedTo && ( <Typography variant="body2" color="textSecondary"> Assigné à: {alert.assignedTo} </Typography> )} {alert.escalationLevel > 1 && ( <Typography variant="body2" color="warning.main"> Niveau d'escalade: {alert.escalationLevel} </Typography> )} </Box> } /> <ListItemSecondaryAction> <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}> <Tooltip title="Voir les détails"> <IconButton edge="end" onClick={() => handleViewDetails(alert)} > <Visibility /> </IconButton> </Tooltip> {alert.status === AlertStatus.ACTIVE && ( <Tooltip title="Acquitter"> <IconButton edge="end" onClick={() => handleAcknowledge(alert._id)} color="primary" > <Assignment /> </IconButton> </Tooltip> )} </Box> </ListItemSecondaryAction> </ListItem> {index < alerts.length - 1 && <Divider variant="inset" component="li" />} </React.Fragment> )) )} </List> </Paper> {/* Dialog des filtres */} <Dialog open={filtersOpen} onClose={() => setFiltersOpen(false)} maxWidth="sm" fullWidth> <DialogTitle>Filtres des Alertes</DialogTitle> <DialogContent> <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}> <FormControl fullWidth> <InputLabel>Statut</InputLabel> <Select value={filters.status} label="Statut" onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value as AlertStatus }))} > <MenuItem value="">Tous les statuts</MenuItem> <MenuItem value={AlertStatus.ACTIVE}>Actif</MenuItem> <MenuItem value={AlertStatus.ACKNOWLEDGED}>Acquitté</MenuItem> <MenuItem value={AlertStatus.IN_PROGRESS}>En cours</MenuItem> <MenuItem value={AlertStatus.RESOLVED}>Résolu</MenuItem> <MenuItem value={AlertStatus.DISMISSED}>Rejeté</MenuItem> </Select> </FormControl> <FormControl fullWidth> <InputLabel>Sévérité</InputLabel> <Select value={filters.severity} label="Sévérité" onChange={(e) => setFilters(prev => ({ ...prev, severity: e.target.value as AlertSeverity }))} > <MenuItem value="">Toutes les sévérités</MenuItem> <MenuItem value={AlertSeverity.CRITICAL}>Critique</MenuItem> <MenuItem value={AlertSeverity.HIGH}>Élevé</MenuItem> <MenuItem value={AlertSeverity.MEDIUM}>Moyen</MenuItem> <MenuItem value={AlertSeverity.LOW}>Faible</MenuItem> </Select> </FormControl> <FormControl fullWidth> <InputLabel>Type</InputLabel> <Select value={filters.type} label="Type" onChange={(e) => setFilters(prev => ({ ...prev, type: e.target.value as AlertType }))} > <MenuItem value="">Tous les types</MenuItem> <MenuItem value={AlertType.VENTE_OPPORTUNITY}>Opportunité de Vente</MenuItem> <MenuItem value={AlertType.CHURN_RISK}>Risque de Churn</MenuItem> <MenuItem value={AlertType.ESCALATION_NEEDED}>Escalade Requise</MenuItem> <MenuItem value={AlertType.TECHNICAL_ISSUE}>Problème Technique</MenuItem> <MenuItem value={AlertType.BILLING_DISPUTE}>Litige Facturation</MenuItem> <MenuItem value={AlertType.SATISFACTION_CRITICAL}>Satisfaction Critique</MenuItem> <MenuItem value={AlertType.SYSTEM_ANOMALY}>Anomalie Système</MenuItem> <MenuItem value={AlertType.PERFORMANCE_DEGRADATION}>Dégradation Performance</MenuItem> </Select> </FormControl> </Box> </DialogContent> <DialogActions> <Button onClick={() => setFiltersOpen(false)}>Annuler</Button> <Button onClick={handleApplyFilters} variant="contained"> Appliquer </Button> </DialogActions> </Dialog> {/* Dialog des détails */} <Dialog open={detailsOpen} onClose={handleCloseDetails} maxWidth="md" fullWidth > <DialogTitle> Détails de l'Alerte </DialogTitle> <DialogContent> {selectedAlert && ( <Box sx={{ mt: 1 }}> <Typography variant="h6" gutterBottom> {selectedAlert.title} </Typography> <Typography variant="body1" paragraph> {selectedAlert.description} </Typography> <Grid container spacing={2}> <Grid item xs={6}> <Typography variant="subtitle2">Type:</Typography> <Typography variant="body2">{getTypeLabel(selectedAlert.type)}</Typography> </Grid> <Grid item xs={6}> <Typography variant="subtitle2">Sévérité:</Typography> <Typography variant="body2">{mlService.getSeverityLabel(selectedAlert.severity)}</Typography> </Grid> <Grid item xs={6}> <Typography variant="subtitle2">Priorité:</Typography> <Typography variant="body2">{selectedAlert.priority}/100</Typography> </Grid> <Grid item xs={6}> <Typography variant="subtitle2">Statut:</Typography> <Typography variant="body2">{selectedAlert.status}</Typography> </Grid> <Grid item xs={12}> <Typography variant="subtitle2">Créé le:</Typography> <Typography variant="body2">{formatTimestamp(selectedAlert.createdAt)}</Typography> </Grid> </Grid> {selectedAlert.contextData && ( <Box sx={{ mt: 2 }}> <Typography variant="subtitle2" gutterBottom> Données contextuelles: </Typography> <pre style={{ fontSize: '0.8rem', overflow: 'auto' }}> {JSON.stringify(selectedAlert.contextData, null, 2)} </pre> </Box> )} </Box> )} </DialogContent> <DialogActions> <Button onClick={handleCloseDetails}>Fermer</Button> {selectedAlert?.status === AlertStatus.ACTIVE && ( <Button onClick={() => { handleAcknowledge(selectedAlert._id); handleCloseDetails(); }} variant="contained" > Acquitter </Button> )} </DialogActions> </Dialog> </Box> ); }; export default AlertsPanel;