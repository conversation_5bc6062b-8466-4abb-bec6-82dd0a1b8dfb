/** * Dashboard ML Principal * Free Mobile Chatbot Dashboard - Phase 3 Frontend Implementation */ import React, { useEffect, useState } from 'react'; import { Box, Grid, Paper, Typography, Tabs, Tab, Alert, Snackbar, Fab, Badge, Tooltip, CircularProgress, Backdrop } from '@mui/material'; import { Dashboard as DashboardIcon, Analytics as AnalyticsIcon, NotificationsActive as AlertsIcon, Queue as QueueIcon, Assessment as MetricsIcon, Refresh as RefreshIcon, Settings as SettingsIcon } from '@mui/icons-material'; import { useAppDispatch, useAppSelector } from '../../../hooks/redux'; import { useMLWebSocket } from '../../../hooks/useMLWebSocket'; import { fetchPriorityQueue, fetchPerformanceMetrics, fetchAlerts, addClassification, addAlert, updateMetrics, updatePriorityQueue, selectMLState, selectActiveAlerts, selectCriticalAlerts } from '../../../store/slices/mlSlice'; import { ClassificationEvent, AlertEvent, MetricsEvent, PriorityQueueEvent } from '../../../types/ml'; // Composants ML import MLOverview from './MLOverview'; import PriorityQueuePanel from './PriorityQueuePanel'; import AlertsPanel from './AlertsPanel'; import PerformanceMetrics from './PerformanceMetrics'; import ClassificationHistory from './ClassificationHistory'; import MLSettings from './MLSettings'; interface TabPanelProps { children?: React.ReactNode; index: number; value: number; } function TabPanel(props: TabPanelProps) { const { children, value, index, ...other } = props; return ( <div role="tabpanel" hidden={value !== index} id={`ml-tabpanel-${index}`} aria-labelledby={`ml-tab-${index}`} {...other} > {value === index && ( <Box sx={{ p: 3 }}> {children} </Box> )} </div> ); } const MLDashboard: React.FC = () => { const dispatch = useAppDispatch(); const mlState = useAppSelector(selectMLState); const activeAlerts = useAppSelector(selectActiveAlerts); const criticalAlerts = useAppSelector(selectCriticalAlerts); const [currentTab, setCurrentTab] = useState(0); const [notification, setNotification] = useState<{ open: boolean; message: string; severity: 'success' | 'error' | 'warning' | 'info'; }>({ open: false, message: '', severity: 'info' }); const [refreshing, setRefreshing] = useState(false); // WebSocket pour les événements temps réel const { connected, connecting, error: wsError, requestMetrics, requestPriorityQueue } = useMLWebSocket({ autoConnect: true, subscriptions: ['ml-events', 'high-priority', 'critical-alerts'], onClassification: (event: ClassificationEvent) => { dispatch(addClassification(event.data as any)); // Notification pour les classifications haute priorité if (event.data.priorityScore >= 80) { setNotification({ open: true, message: `Nouvelle classification haute priorité: ${event.data.category}`, severity: 'warning' }); } }, onAlert: (event: AlertEvent) => { dispatch(addAlert(event.data as any)); // Notification pour les alertes critiques if (event.data.severity === 'CRITICAL') { setNotification({ open: true, message: `Alerte critique: ${event.data.title}`, severity: 'error' }); } }, onMetrics: (event: MetricsEvent) => { dispatch(updateMetrics(event.data)); }, onPriorityQueue: (event: PriorityQueueEvent) => { dispatch(updatePriorityQueue(event.data)); }, onError: (error) => { console.error('WebSocket ML Error:', error); setNotification({ open: true, message: `Erreur WebSocket: ${error.message}`, severity: 'error' }); } }); // Chargement initial des données useEffect(() => { const loadInitialData = async () => { try { await Promise.all([ dispatch(fetchPriorityQueue({ limit: 50, minPriority: 0 })), dispatch(fetchPerformanceMetrics('24h')), dispatch(fetchAlerts({ limit: 50 })) ]); } catch (error) { console.error('Error loading initial ML data:', error); setNotification({ open: true, message: 'Erreur lors du chargement des données ML', severity: 'error' }); } }; loadInitialData(); }, [dispatch]); // Actualisation périodique des données useEffect(() => { const interval = setInterval(() => { if (connected) { requestMetrics(); requestPriorityQueue({ limit: 50 }); } }, 30000); // Toutes les 30 secondes return () => clearInterval(interval); }, [connected, requestMetrics, requestPriorityQueue]); const handleTabChange = (event: React.SyntheticEvent, newValue: number) => { setCurrentTab(newValue); }; const handleRefresh = async () => { setRefreshing(true); try { await Promise.all([ dispatch(fetchPriorityQueue({ limit: 50, minPriority: 0 })), dispatch(fetchPerformanceMetrics(mlState.filters.timeRange)), dispatch(fetchAlerts({ limit: 50 })) ]); setNotification({ open: true, message: 'Données actualisées avec succès', severity: 'success' }); } catch (error) { setNotification({ open: true, message: 'Erreur lors de l\'actualisation', severity: 'error' }); } finally { setRefreshing(false); } }; const handleCloseNotification = () => { setNotification(prev => ({ ...prev, open: false })); }; // Indicateur de statut de connexion const getConnectionStatus = () => { if (connecting) return { color: 'warning', text: 'Connexion...' }; if (connected) return { color: 'success', text: 'Connecté' }; if (wsError) return { color: 'error', text: 'Erreur de connexion' }; return { color: 'error', text: 'Déconnecté' }; }; const connectionStatus = getConnectionStatus(); return ( <Box sx={{ width: '100%', height: '100%' }}> {/* Header avec statut et actions */} <Paper sx={{ mb: 2, p: 2 }}> <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}> <Box> <Typography variant="h4" component="h1" gutterBottom> Dashboard ML Intelligence </Typography> <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}> <Badge color={connectionStatus.color as any} variant="dot" sx={{ '& .MuiBadge-badge': { right: -3, top: 3 } }} > <Typography variant="body2" color="textSecondary"> WebSocket: {connectionStatus.text} </Typography> </Badge> {mlState.lastUpdated.metrics && ( <Typography variant="body2" color="textSecondary"> Dernière mise à jour: {new Date(mlState.lastUpdated.metrics).toLocaleTimeString()} </Typography> )} </Box> </Box> <Box sx={{ display: 'flex', gap: 1 }}> <Tooltip title="Actualiser les données"> <Fab size="small" color="primary" onClick={handleRefresh} disabled={refreshing} > {refreshing ? <CircularProgress size={20} /> : <RefreshIcon />} </Fab> </Tooltip> </Box> </Box> </Paper> {/* Onglets principaux */} <Paper sx={{ width: '100%' }}> <Box sx={{ borderBottom: 1, borderColor: 'divider' }}> <Tabs value={currentTab} onChange={handleTabChange} aria-label="ML Dashboard tabs" variant="scrollable" scrollButtons="auto" > <Tab icon={<DashboardIcon />} label="Vue d'ensemble" id="ml-tab-0" aria-controls="ml-tabpanel-0" /> <Tab icon={ <Badge badgeContent={mlState.priorityQueue.length} color="error" max={99}> <QueueIcon /> </Badge> } label="Queue Priorité" id="ml-tab-1" aria-controls="ml-tabpanel-1" /> <Tab icon={ <Badge badgeContent={criticalAlerts.length} color="error" max={99}> <AlertsIcon /> </Badge> } label="Alertes" id="ml-tab-2" aria-controls="ml-tabpanel-2" /> <Tab icon={<MetricsIcon />} label="Métriques" id="ml-tab-3" aria-controls="ml-tabpanel-3" /> <Tab icon={<AnalyticsIcon />} label="Historique" id="ml-tab-4" aria-controls="ml-tabpanel-4" /> <Tab icon={<SettingsIcon />} label="Paramètres" id="ml-tab-5" aria-controls="ml-tabpanel-5" /> </Tabs> </Box> {/* Contenu des onglets */} <TabPanel value={currentTab} index={0}> <MLOverview /> </TabPanel> <TabPanel value={currentTab} index={1}> <PriorityQueuePanel /> </TabPanel> <TabPanel value={currentTab} index={2}> <AlertsPanel /> </TabPanel> <TabPanel value={currentTab} index={3}> <PerformanceMetrics /> </TabPanel> <TabPanel value={currentTab} index={4}> <ClassificationHistory /> </TabPanel> <TabPanel value={currentTab} index={5}> <MLSettings /> </TabPanel> </Paper> {/* Backdrop pour le chargement */} <Backdrop sx={{ color: '#fff', zIndex: (theme) => theme.zIndex.drawer + 1 }} open={refreshing} > <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}> <CircularProgress color="inherit" /> <Typography>Actualisation des données ML...</Typography> </Box> </Backdrop> {/* Notifications */} <Snackbar open={notification.open} autoHideDuration={6000} onClose={handleCloseNotification} anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }} > <Alert onClose={handleCloseNotification} severity={notification.severity} sx={{ width: '100%' }} > {notification.message} </Alert> </Snackbar> </Box> ); }; export default MLDashboard;