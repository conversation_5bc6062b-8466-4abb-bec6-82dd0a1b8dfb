import React from 'react'; import { Tabs, Tab, Box, useTheme, useMediaQuery, } from '@mui/material'; import { Support as SupportIcon, Assignment as FormIcon, AdminPanelSettings as AdminIcon, Analytics as AnalyticsIcon, } from '@mui/icons-material'; import { useNavigate, useLocation } from 'react-router-dom'; import { ROUTES } from '../../../utils/constants'; export interface NavigationTab { label: string; value: string; path: string; icon: React.ReactElement; } const navigationTabs: NavigationTab[] = [ { label: 'Support Client', value: 'support', path: ROUTES.DASHBOARD_OVERVIEW, icon: <SupportIcon />, }, { label: 'Formulaire Support', value: 'form', path: ROUTES.DASHBOARD_SUPPORT_FORM, icon: <FormIcon />, }, { label: 'Panel Admin', value: 'admin', path: ROUTES.DASHBOARD_ADMIN, icon: <AdminIcon />, }, { label: 'Analytics', value: 'analytics', path: ROUTES.DASHBOARD_ANALYTICS, icon: <AnalyticsIcon />, }, ]; const TopNavigation: React.FC = () => { const navigate = useNavigate(); const location = useLocation(); const theme = useTheme(); const isMobile = useMediaQuery(theme.breakpoints.down('sm')); // Determine current tab based on pathname const getCurrentTab = (): string => { const path = location.pathname; if (path.includes('/dashboard/admin')) return 'admin'; if (path.includes('/dashboard/analytics')) return 'analytics'; if (path.includes('/dashboard/support-form')) return 'form'; if (path.includes('/dashboard/overview') || path === '/dashboard') return 'support'; return 'support'; // default }; const handleTabChange = (event: React.SyntheticEvent, newValue: string) => { const tab = navigationTabs.find(t => t.value === newValue); if (tab) { navigate(tab.path); } }; return ( <Box sx={{ flexGrow: 1, display: 'flex', justifyContent: 'center' }}> <Tabs value={getCurrentTab()} onChange={handleTabChange} textColor="inherit" indicatorColor="secondary" variant={isMobile ? 'scrollable' : 'standard'} scrollButtons={isMobile ? 'auto' : false} sx={{ '& .MuiTab-root': { color: 'rgba(255, 255, 255, 0.7)', fontWeight: 500, fontSize: isMobile ? '0.8rem' : '0.9rem', minWidth: isMobile ? 'auto' : 120, '&.Mui-selected': { color: 'white', fontWeight: 600, }, '&:hover': { color: 'white', opacity: 0.8, }, }, '& .MuiTabs-indicator': { backgroundColor: 'white', height: 3, }, '& .MuiTabs-scrollButtons': { color: 'white', }, }} > {navigationTabs.map((tab) => ( <Tab key={tab.value} label={isMobile ? '' : tab.label} value={tab.value} icon={tab.icon} iconPosition={isMobile ? 'top' : 'start'} sx={{ '& .MuiSvgIcon-root': { fontSize: isMobile ? '1.2rem' : '1rem', mr: isMobile ? 0 : 1, }, }} /> ))} </Tabs> </Box> ); }; export default TopNavigation;