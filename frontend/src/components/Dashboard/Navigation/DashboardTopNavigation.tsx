import React, { useState } from 'react';
import {
  Box,
  Tabs,
  Tab,
  IconButton,
  Badge,
  Avatar,
  Menu,
  MenuItem,
  Typography,
  Divider,
  Tooltip,
} from '@mui/material';
import {
  Notifications as NotificationsIcon,
  AccountCircle as AccountIcon,
  Logout as LogoutIcon,
  Settings as SettingsIcon,
  DarkMode as DarkModeIcon,
  LightMode as LightModeIcon,
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../../hooks/useAuth';
import { ROUTES, FREE_MOBILE_COLORS } from '../../../utils/constants';

interface TopNavigationTab {
  label: string;
  value: string;
  path: string;
}

const DashboardTopNavigation: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout } = useAuth();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [notificationAnchor, setNotificationAnchor] = useState<null | HTMLElement>(null);
  const [darkMode, setDarkMode] = useState(false);

  const tabs: TopNavigationTab[] = [
    { label: 'Accueil', value: 'overview', path: ROUTES.DASHBOARD_OVERVIEW },
    { label: 'Support', value: 'support', path: ROUTES.DASHBOARD_SUPPORT_FORM },
    { label: 'Admin', value: 'admin', path: ROUTES.DASHBOARD_ADMIN },
  ];

  const getCurrentTab = (): string => {
    const currentPath = location.pathname;
    if (currentPath.includes('/admin')) return 'admin';
    if (currentPath.includes('/support-form')) return 'support';
    return 'overview';
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: string) => {
    const tab = tabs.find(t => t.value === newValue);
    if (tab) {
      navigate(tab.path);
    }
  };

  const handleProfileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleProfileMenuClose = () => {
    setAnchorEl(null);
  };

  const handleNotificationMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setNotificationAnchor(event.currentTarget);
  };

  const handleNotificationMenuClose = () => {
    setNotificationAnchor(null);
  };

  const handleLogout = () => {
    logout();
    handleProfileMenuClose();
    navigate(ROUTES.LOGIN);
  };

  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
    // Here you would implement actual dark mode toggle
  };

  const getUserInitials = (): string => {
    if (!user?.email) return 'U';
    const parts = user.email.split('@')[0].split('.');
    if (parts.length >= 2) {
      return (parts[0].charAt(0) + parts[1].charAt(0)).toUpperCase();
    }
    return user.email.charAt(0).toUpperCase();
  };

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
      {/* Navigation Tabs */}
      <Box sx={{ flexGrow: 1 }}>
        <Tabs
          value={getCurrentTab()}
          onChange={handleTabChange}
          textColor="inherit"
          indicatorColor="secondary"
          sx={{
            '& .MuiTab-root': {
              color: 'rgba(255, 255, 255, 0.7)',
              '&.Mui-selected': {
                color: 'white',
              },
            },
            '& .MuiTabs-indicator': {
              backgroundColor: 'white',
            },
          }}
        >
          {tabs.map((tab) => (
            <Tab
              key={tab.value}
              label={tab.label}
              value={tab.value}
              sx={{ minWidth: 80 }}
            />
          ))}
        </Tabs>
      </Box>

      {/* Action Icons */}
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        {/* Dark Mode Toggle */}
        <Tooltip title={darkMode ? 'Mode clair' : 'Mode sombre'}>
          <IconButton
            color="inherit"
            onClick={toggleDarkMode}
            sx={{ color: 'rgba(255, 255, 255, 0.8)' }}
          >
            {darkMode ? <LightModeIcon /> : <DarkModeIcon />}
          </IconButton>
        </Tooltip>

        {/* Notifications */}
        <Tooltip title="Notifications">
          <IconButton
            color="inherit"
            onClick={handleNotificationMenuOpen}
            sx={{ color: 'rgba(255, 255, 255, 0.8)' }}
          >
            <Badge badgeContent={3} color="error">
              <NotificationsIcon />
            </Badge>
          </IconButton>
        </Tooltip>

        {/* Profile Menu */}
        <Tooltip title="Profil utilisateur">
          <IconButton
            onClick={handleProfileMenuOpen}
            sx={{ p: 0, ml: 1 }}
          >
            <Avatar
              sx={{
                width: 32,
                height: 32,
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                color: 'white',
                fontSize: '0.875rem',
              }}
            >
              {getUserInitials()}
            </Avatar>
          </IconButton>
        </Tooltip>
      </Box>

      {/* Profile Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleProfileMenuClose}
        onClick={handleProfileMenuClose}
        PaperProps={{
          elevation: 0,
          sx: {
            overflow: 'visible',
            filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
            mt: 1.5,
            '& .MuiAvatar-root': {
              width: 32,
              height: 32,
              ml: -0.5,
              mr: 1,
            },
            '&:before': {
              content: '""',
              display: 'block',
              position: 'absolute',
              top: 0,
              right: 14,
              width: 10,
              height: 10,
              bgcolor: 'background.paper',
              transform: 'translateY(-50%) rotate(45deg)',
              zIndex: 0,
            },
          },
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <Box sx={{ px: 2, py: 1 }}>
          <Typography variant="subtitle2" fontWeight="bold">
            {user?.email || 'Utilisateur'}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {user?.role === 'admin' ? 'Administrateur' : 
             user?.role === 'agent' ? 'Agent' : 'Utilisateur'}
          </Typography>
        </Box>
        <Divider />
        <MenuItem onClick={handleProfileMenuClose}>
          <AccountIcon sx={{ mr: 2 }} />
          Mon profil
        </MenuItem>
        <MenuItem onClick={handleProfileMenuClose}>
          <SettingsIcon sx={{ mr: 2 }} />
          Paramètres
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleLogout}>
          <LogoutIcon sx={{ mr: 2 }} />
          Déconnexion
        </MenuItem>
      </Menu>

      {/* Notifications Menu */}
      <Menu
        anchorEl={notificationAnchor}
        open={Boolean(notificationAnchor)}
        onClose={handleNotificationMenuClose}
        PaperProps={{
          sx: {
            width: 320,
            maxHeight: 400,
          },
        }}
      >
        <Box sx={{ px: 2, py: 1 }}>
          <Typography variant="h6">Notifications</Typography>
        </Box>
        <Divider />
        <MenuItem onClick={handleNotificationMenuClose}>
          <Box>
            <Typography variant="body2" fontWeight="bold">
              Nouveau ticket de support
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Il y a 5 minutes
            </Typography>
          </Box>
        </MenuItem>
        <MenuItem onClick={handleNotificationMenuClose}>
          <Box>
            <Typography variant="body2" fontWeight="bold">
              Mise à jour système
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Il y a 1 heure
            </Typography>
          </Box>
        </MenuItem>
        <MenuItem onClick={handleNotificationMenuClose}>
          <Box>
            <Typography variant="body2" fontWeight="bold">
              Rapport mensuel disponible
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Il y a 2 heures
            </Typography>
          </Box>
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default DashboardTopNavigation;
