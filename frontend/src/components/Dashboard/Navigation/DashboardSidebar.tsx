import React from 'react'; import { List, ListItem, ListItemButton, ListItemIcon, ListItemText, Divider, Box, Typography, Avatar, } from '@mui/material'; import { Dashboard as DashboardIcon, Chat as ChatIcon, Notifications as NotificationsIcon, Psychology as SuggestionsIcon, PlayArrow as SimulationsIcon, People as PeopleIcon, Settings as SettingsIcon, AutoAwesome as MLIcon, Psychology as MultimodalIcon, } from '@mui/icons-material'; import { useNavigate, useLocation } from 'react-router-dom'; import { FREE_MOBILE_COLORS } from '../../../utils/constants'; export interface SidebarItem { id: string; label: string; icon: React.ReactElement; path: string; badge?: number; } const sidebarItems: SidebarItem[] = [ { id: 'dashboard', label: 'Dashboard', icon: <DashboardIcon />, path: '/dashboard/admin', }, { id: 'conversations', label: 'Conversations', icon: <ChatIcon />, path: '/dashboard/admin/conversations', badge: 3, // Example badge count }, { id: 'notifications', label: 'Notifications', icon: <NotificationsIcon />, path: '/dashboard/admin/notifications', badge: 5, }, { id: 'suggestions', label: 'Suggestions', icon: <SuggestionsIcon />, path: '/dashboard/admin/suggestions', }, { id: 'ml-intelligence', label: 'ML Intelligence', icon: <MLIcon />, path: '/dashboard/admin/ml-intelligence', }, { id: 'multimodal', label: 'Analyse Multimodale', icon: <MultimodalIcon />, path: '/dashboard/multimodal', }, { id: 'simulations', label: 'Simulations', icon: <SimulationsIcon />, path: '/dashboard/admin/simulations', }, { id: 'clients', label: 'Clients', icon: <PeopleIcon />, path: '/dashboard/admin/clients', }, { id: 'settings', label: 'Paramètres', icon: <SettingsIcon />, path: '/dashboard/admin/settings', }, ]; interface DashboardSidebarProps { onItemClick?: () => void; } const DashboardSidebar: React.FC<DashboardSidebarProps> = ({ onItemClick }) => { const navigate = useNavigate(); const location = useLocation(); const handleItemClick = (path: string) => { navigate(path); onItemClick?.(); }; const isSelected = (path: string): boolean => { return location.pathname === path || (path === '/dashboard/admin' && location.pathname === '/dashboard/admin'); }; return ( <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}> {/* Admin Profile Section */} <Box sx={{ p: 2, textAlign: 'center', backgroundColor: 'white', mb: 1 }}> <Avatar sx={{ width: 60, height: 60, mx: 'auto', mb: 1, backgroundColor: FREE_MOBILE_COLORS.PRIMARY, }} > A </Avatar> <Typography variant="subtitle1" fontWeight="bold"> Admin User </Typography> <Typography variant="body2" color="text.secondary"> Administrateur </Typography> </Box> <Divider /> {/* Navigation Items */} <List sx={{ flexGrow: 1, py: 1 }}> {sidebarItems.map((item) => ( <ListItem key={item.id} disablePadding> <ListItemButton onClick={() => handleItemClick(item.path)} selected={isSelected(item.path)} sx={{ mx: 1, borderRadius: 1, '&.Mui-selected': { backgroundColor: `${FREE_MOBILE_COLORS.PRIMARY}15`, '& .MuiListItemIcon-root': { color: FREE_MOBILE_COLORS.PRIMARY, }, '& .MuiListItemText-primary': { color: FREE_MOBILE_COLORS.PRIMARY, fontWeight: 600, }, }, '&:hover': { backgroundColor: `${FREE_MOBILE_COLORS.PRIMARY}08`, }, }} > <ListItemIcon sx={{ minWidth: 40, color: isSelected(item.path) ? FREE_MOBILE_COLORS.PRIMARY : 'text.secondary', }} > {item.icon} </ListItemIcon> <ListItemText primary={item.label} sx={{ '& .MuiListItemText-primary': { fontSize: '0.9rem', fontWeight: isSelected(item.path) ? 600 : 400, }, }} /> {item.badge && ( <Box sx={{ backgroundColor: FREE_MOBILE_COLORS.ERROR, color: 'white', borderRadius: '50%', width: 20, height: 20, display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: '0.7rem', fontWeight: 'bold', }} > {item.badge} </Box> )} </ListItemButton> </ListItem> ))} </List> <Divider /> {/* Footer */} <Box sx={{ p: 2, textAlign: 'center' }}> <Typography variant="caption" color="text.secondary"> Free Mobile Dashboard v1.0 </Typography> </Box> </Box> ); }; export default DashboardSidebar;