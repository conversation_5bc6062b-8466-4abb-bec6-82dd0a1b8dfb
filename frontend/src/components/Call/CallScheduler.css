/**
 * =============================================
 * 📅 CALL SCHEDULER STYLES
 * Calendar and time slot selection interface
 * =============================================
 */

.call-scheduler {
  /* Main container styles */
}

.call-scheduler__header {
  text-align: center;
  margin-bottom: 24px;
}

.call-scheduler__header h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #2d3436;
}

.call-scheduler__header p {
  margin: 0;
  font-size: 14px;
  color: #636e72;
}

.call-scheduler__content {
  /* Content container */
}

/* Calendar */
.call-scheduler__calendar {
  margin-bottom: 24px;
}

.calendar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.calendar-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #2d3436;
}

.calendar-nav {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  color: #636e72;
  transition: all 0.2s ease;
}

.calendar-nav:hover {
  background: #f8f9fa;
  color: #2d3436;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background: #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.calendar-day-header {
  background: #f8f9fa;
  padding: 8px;
  text-align: center;
  font-size: 12px;
  font-weight: 600;
  color: #636e72;
}

.calendar-day {
  background: white;
  padding: 12px 8px;
  text-align: center;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.calendar-day--empty {
  cursor: default;
}

.calendar-day--selectable:hover {
  background: #f8f9fa;
}

.calendar-day--disabled {
  color: #adb5bd;
  cursor: not-allowed;
}

.calendar-day--selected {
  background: #0984e3;
  color: white;
}

/* Selected Date */
.call-scheduler__selected-date {
  margin-bottom: 24px;
}

.selected-date-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #2d3436;
}

/* Time Slots */
.call-scheduler__time-slots {
  margin-bottom: 24px;
}

.call-scheduler__time-slots h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2d3436;
}

.time-slots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 8px;
}

.time-slot {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.time-slot:hover:not(.time-slot--unavailable) {
  border-color: #0984e3;
  background: #f8f9fa;
}

.time-slot--selected {
  background: #0984e3;
  border-color: #0984e3;
  color: white;
}

.time-slot--unavailable {
  background: #f8f9fa;
  color: #adb5bd;
  cursor: not-allowed;
}

.time-slot__time {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 4px;
}

.time-slot__agent {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 12px;
  opacity: 0.8;
}

.time-slot__unavailable {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 12px;
}

/* Notes */
.call-scheduler__notes {
  margin-bottom: 24px;
}

.call-scheduler__notes label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #2d3436;
}

/* Schedule Button */
.call-scheduler__schedule-btn {
  width: 100%;
}

/* Responsive */
@media (max-width: 768px) {
  .time-slots-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  }
}

@media (max-width: 480px) {
  .calendar-grid {
    font-size: 12px;
  }
  
  .calendar-day {
    padding: 8px 4px;
    min-height: 32px;
  }
  
  .time-slots-grid {
    grid-template-columns: 1fr 1fr;
  }
}
