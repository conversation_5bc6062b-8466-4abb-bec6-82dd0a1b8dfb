/** * ============================================= * EMERGENCY CALL STATUS COMPONENT * Real-time status display for emergency calls * Shows queue position, wait times, and connection status * ============================================= */ import React, { useEffect, useState } from 'react'; import { Card, CardContent, Typography, Box, LinearProgress, Chip, Button, Alert, Avatar, Divider, IconButton, Tooltip } from '@mui/material'; import { LocalHospital as EmergencyIcon, Person as PersonIcon, AccessTime as TimeIcon, Queue as QueueIcon, Phone as PhoneIcon, CallEnd as CallEndIcon, VolumeUp as VolumeUpIcon, VolumeOff as VolumeOffIcon, Mic as MicIcon, MicOff as MicOffIcon } from '@mui/icons-material'; import { useSelector, useDispatch } from 'react-redux'; import { RootState } from '../../store'; import { motion, AnimatePresence } from 'framer-motion'; import { formatDistanceToNow } from 'date-fns'; import { fr } from 'date-fns/locale'; interface EmergencyCallStatusProps { emergencyCallId: string; onEndCall?: () => void; onEscalate?: () => void; } const EmergencyCallStatus: React.FC<EmergencyCallStatusProps> = ({ emergencyCallId, onEndCall, onEscalate }) => { const dispatch = useDispatch(); const { currentCall, isConnected } = useSelector((state: RootState) => state.emergencyCall); const [elapsedTime, setElapsedTime] = useState(0); const [isMuted, setIsMuted] = useState(false); const [isSpeakerOn, setIsSpeakerOn] = useState(false); // Update elapsed time useEffect(() => { if (currentCall?.status === 'connected_to_agent' && currentCall.agentInfo?.connectedAt) { const interval = setInterval(() => { if (currentCall.agentInfo?.connectedAt) { const connectedAt = new Date(currentCall.agentInfo.connectedAt); const elapsed = Math.floor((Date.now() - connectedAt.getTime()) / 1000); setElapsedTime(elapsed); } }, 1000); return () => clearInterval(interval); } }, [currentCall?.status, currentCall?.agentInfo?.connectedAt]); const formatTime = (seconds: number): string => { const mins = Math.floor(seconds / 60); const secs = seconds % 60; return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`; }; const getStatusColor = (status: string): string => { switch (status) { case 'initiated': return '#2196f3'; case 'ai_assistance': return '#ff9800'; case 'escalated': return '#f44336'; case 'in_queue': return '#9c27b0'; case 'connected_to_agent': return '#4caf50'; case 'resolved': return '#4caf50'; case 'cancelled': return '#757575'; default: return '#2196f3'; } }; const getStatusLabel = (status: string): string => { switch (status) { case 'initiated': return 'Initié'; case 'ai_assistance': return 'Assistant IA'; case 'escalated': return 'Escaladé'; case 'in_queue': return 'En file d\'attente'; case 'connected_to_agent': return 'Connecté à un agent'; case 'resolved': return 'Résolu'; case 'cancelled': return 'Annulé'; default: return 'En cours'; } }; const getUrgencyColor = (level: string): string => { switch (level) { case 'low': return '#4caf50'; case 'normal': return '#2196f3'; case 'medium': return '#ff9800'; case 'high': return '#f44336'; case 'urgent': return '#d32f2f'; case 'critical': return '#b71c1c'; default: return '#2196f3'; } }; if (!currentCall) { return null; } return ( <AnimatePresence> <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} exit={{ opacity: 0, y: -20 }} transition={{ duration: 0.3 }} > <Card sx={{ maxWidth: 600, mx: 'auto', mt: 2, boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)', border: `2px solid ${getStatusColor(currentCall.status)}`, borderRadius: 2 }} > <CardContent> {/* Header */} <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}> <EmergencyIcon sx={{ color: '#d32f2f', mr: 1 }} /> <Typography variant="h6" sx={{ flexGrow: 1 }}> Appel d'Urgence #{currentCall.emergencyCallId.slice(-8)} </Typography> <Chip label={getStatusLabel(currentCall.status)} sx={{ backgroundColor: getStatusColor(currentCall.status), color: 'white', fontWeight: 'bold' }} /> </Box> {/* Urgency Level */} <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}> <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}> Niveau d'urgence: </Typography> <Chip size="small" label={currentCall.urgencyLevel.toUpperCase()} sx={{ backgroundColor: getUrgencyColor(currentCall.urgencyLevel), color: 'white', fontWeight: 'bold' }} /> </Box> {/* Queue Information */} {currentCall.status === 'in_queue' && currentCall.queueInfo && ( <Alert severity="info" sx={{ mb: 2 }}> <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}> <QueueIcon /> <Typography variant="body2"> Position en file: <strong>{currentCall.queueInfo.position}</strong> </Typography> </Box> <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}> <TimeIcon /> <Typography variant="body2"> Temps d'attente estimé: <strong> {Math.ceil(currentCall.queueInfo.estimatedWaitTime / 60000)} minutes </strong> </Typography> </Box> <LinearProgress variant="indeterminate" sx={{ mt: 1, borderRadius: 1 }} /> </Alert> )} {/* Agent Information */} {currentCall.status === 'connected_to_agent' && currentCall.agentInfo && ( <Box sx={{ mb: 2 }}> <Alert severity="success" sx={{ mb: 2 }}> <Typography variant="body2"> [COMPLETE] Connecté à un agent humain </Typography> </Alert> <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, p: 2, backgroundColor: '#f5f5f5', borderRadius: 1 }}> <Avatar sx={{ bgcolor: '#4caf50' }}> <PersonIcon /> </Avatar> <Box sx={{ flexGrow: 1 }}> <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}> {currentCall.agentInfo.agentName} </Typography> <Typography variant="body2" color="text.secondary"> Agent de support Free Mobile </Typography> </Box> <Box sx={{ textAlign: 'right' }}> <Typography variant="body2" color="text.secondary"> Durée d'appel </Typography> <Typography variant="h6" sx={{ fontFamily: 'monospace', color: '#4caf50' }}> {formatTime(elapsedTime)} </Typography> </Box> </Box> </Box> )} {/* Call Controls */} {currentCall.status === 'connected_to_agent' && ( <Box sx={{ mb: 2 }}> <Divider sx={{ mb: 2 }} /> <Typography variant="subtitle2" sx={{ mb: 1 }}> Contrôles d'appel: </Typography> <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center' }}> <Tooltip title={isMuted ? "Activer le micro" : "Couper le micro"}> <IconButton onClick={() => setIsMuted(!isMuted)} sx={{ backgroundColor: isMuted ? '#f44336' : '#4caf50', color: 'white', '&:hover': { backgroundColor: isMuted ? '#d32f2f' : '#388e3c' } }} > {isMuted ? <MicOffIcon /> : <MicIcon />} </IconButton> </Tooltip> <Tooltip title={isSpeakerOn ? "Désactiver haut-parleur" : "Activer haut-parleur"}> <IconButton onClick={() => setIsSpeakerOn(!isSpeakerOn)} sx={{ backgroundColor: isSpeakerOn ? '#2196f3' : '#757575', color: 'white', '&:hover': { backgroundColor: isSpeakerOn ? '#1976d2' : '#616161' } }} > {isSpeakerOn ? <VolumeUpIcon /> : <VolumeOffIcon />} </IconButton> </Tooltip> <Tooltip title="Terminer l'appel"> <IconButton onClick={onEndCall} sx={{ backgroundColor: '#f44336', color: 'white', '&:hover': { backgroundColor: '#d32f2f' } }} > <CallEndIcon /> </IconButton> </Tooltip> </Box> </Box> )} {/* Action Buttons */} <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}> {currentCall.status === 'ai_assistance' && ( <Button variant="outlined" color="warning" onClick={onEscalate} startIcon={<PersonIcon />} > Parler à un humain </Button> )} {(currentCall.status === 'in_queue' || currentCall.status === 'escalated') && ( <Button variant="outlined" color="error" onClick={onEndCall} > Annuler l'appel </Button> )} </Box> {/* Connection Status */} <Box sx={{ mt: 2, pt: 2, borderTop: '1px solid #e0e0e0' }}> <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}> <Box sx={{ width: 8, height: 8, borderRadius: '50%', backgroundColor: isConnected ? '#4caf50' : '#f44336' }} /> <Typography variant="caption" color="text.secondary"> {isConnected ? 'Connecté' : 'Déconnecté'} </Typography> <Typography variant="caption" color="text.secondary" sx={{ ml: 'auto' }}> Créé {formatDistanceToNow(new Date(currentCall.createdAt), { addSuffix: true, locale: fr })} </Typography> </Box> </Box> </CardContent> </Card> </motion.div> </AnimatePresence> ); }; export default EmergencyCallStatus;