/** * ============================================= * EMERGENCY CALL BUTTON COMPONENT * Accessible emergency call initiation button * Complies with WCAG 2.1 AA standards * ============================================= */ import React, { useState, useCallback } from 'react'; import { Button, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, Typography, Box, Alert, CircularProgress, Chip } from '@mui/material'; import { LocalHospital as EmergencyIcon, Phone as PhoneIcon, Warning as WarningIcon, CheckCircle as CheckCircleIcon } from '@mui/icons-material'; import { useDispatch, useSelector } from 'react-redux'; import { RootState } from '../../store'; import { initiateEmergencyCall } from '../../store/slices/emergencyCallSlice'; import { motion, AnimatePresence } from 'framer-motion'; interface EmergencyCallButtonProps { className?: string; variant?: 'contained' | 'outlined' | 'text'; size?: 'small' | 'medium' | 'large'; disabled?: boolean; conversationHistory?: Array<{ content: string; sender: 'user' | 'bot' | 'agent'; timestamp: Date; }>; } const EmergencyCallButton: React.FC<EmergencyCallButtonProps> = ({ className, variant = 'contained', size = 'medium', disabled = false, conversationHistory = [] }) => { const dispatch = useDispatch(); const { user } = useSelector((state: RootState) => state.auth); const { isInitiating, error } = useSelector((state: RootState) => state.emergencyCall); const [isDialogOpen, setIsDialogOpen] = useState(false); const [urgencyLevel, setUrgencyLevel] = useState<string>('high'); const [description, setDescription] = useState(''); const [isConfirming, setIsConfirming] = useState(false); const urgencyLevels = [ { value: 'medium', label: 'Moyen', color: '#ff9800', description: 'Problème gênant mais non critique' }, { value: 'high', label: 'Élevé', color: '#f44336', description: 'Problème urgent nécessitant une attention rapide' }, { value: 'urgent', label: 'Urgent', color: '#d32f2f', description: 'Problème très urgent, service impacté' }, { value: 'critical', label: 'Critique', color: '#b71c1c', description: 'Panne totale, intervention immédiate requise' } ]; const handleEmergencyCallClick = useCallback(() => { setIsDialogOpen(true); }, []); const handleDialogClose = useCallback(() => { if (!isInitiating) { setIsDialogOpen(false); setIsConfirming(false); setDescription(''); setUrgencyLevel('high'); } }, [isInitiating]); const handleInitiateCall = useCallback(async () => { if (!user?.id || !description.trim()) { return; } setIsConfirming(true); try { await dispatch(initiateEmergencyCall({ userId: user.id, urgencyLevel, description: description.trim(), conversationHistory: conversationHistory.slice(-10) // Last 10 messages }) as any); // Dialog will close automatically on success via Redux state } catch (error) { console.error('Failed to initiate emergency call:', error); setIsConfirming(false); } }, [dispatch, user?.id, urgencyLevel, description, conversationHistory]); const selectedUrgency = urgencyLevels.find(level => level.value === urgencyLevel); return ( <> {/* Emergency Call Button */} <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }} className={className} > <Button variant={variant} size={size} color="error" disabled={disabled || isInitiating} onClick={handleEmergencyCallClick} startIcon={<EmergencyIcon />} sx={{ backgroundColor: '#d32f2f', color: 'white', fontWeight: 'bold', minHeight: '48px', // WCAG touch target size '&:hover': { backgroundColor: '#b71c1c', boxShadow: '0 4px 8px rgba(211, 47, 47, 0.3)' }, '&:focus': { outline: '3px solid #ffeb3b', outlineOffset: '2px' } }} aria-label="Initier un appel d'urgence - Ouvre une boîte de dialogue pour décrire votre problème urgent" role="button" tabIndex={0} > Appel d'Urgence </Button> </motion.div> {/* Emergency Call Dialog */} <Dialog open={isDialogOpen} onClose={handleDialogClose} maxWidth="sm" fullWidth aria-labelledby="emergency-call-dialog-title" aria-describedby="emergency-call-dialog-description" PaperProps={{ sx: { borderRadius: 2, boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2)' } }} > <DialogTitle id="emergency-call-dialog-title" sx={{ backgroundColor: '#d32f2f', color: 'white', display: 'flex', alignItems: 'center', gap: 1 }} > <EmergencyIcon /> Appel d'Urgence Free Mobile </DialogTitle> <DialogContent sx={{ pt: 3 }}> <Typography id="emergency-call-dialog-description" variant="body2" color="text.secondary" sx={{ mb: 3 }} > Décrivez votre problème urgent. Nos systèmes intelligents évalueront la situation et vous connecteront rapidement à l'assistance appropriée. </Typography> {error && ( <Alert severity="error" sx={{ mb: 2 }}> {error} </Alert> )} {/* Urgency Level Selection */} <FormControl fullWidth sx={{ mb: 3 }}> <InputLabel id="urgency-level-label">Niveau d'urgence</InputLabel> <Select labelId="urgency-level-label" value={urgencyLevel} label="Niveau d'urgence" onChange={(e) => setUrgencyLevel(e.target.value)} disabled={isInitiating || isConfirming} > {urgencyLevels.map((level) => ( <MenuItem key={level.value} value={level.value}> <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}> <Chip size="small" label={level.label} sx={{ backgroundColor: level.color, color: 'white', fontWeight: 'bold' }} /> <Typography variant="body2"> {level.description} </Typography> </Box> </MenuItem> ))} </Select> </FormControl> {/* Problem Description */} <TextField fullWidth multiline rows={4} label="Description du problème" placeholder="Décrivez votre problème urgent en détail..." value={description} onChange={(e) => setDescription(e.target.value)} disabled={isInitiating || isConfirming} required inputProps={{ maxLength: 1000, 'aria-describedby': 'description-helper-text' }} helperText={`${description.length}/1000 caractères`} sx={{ mb: 2 }} /> {/* Selected Urgency Display */} {selectedUrgency && ( <Box sx={{ p: 2, backgroundColor: `${selectedUrgency.color}15`, borderRadius: 1, border: `1px solid ${selectedUrgency.color}30`, mb: 2 }} > <Typography variant="subtitle2" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}> <WarningIcon sx={{ color: selectedUrgency.color }} /> Niveau sélectionné: {selectedUrgency.label} </Typography> <Typography variant="body2" color="text.secondary"> {selectedUrgency.description} </Typography> </Box> )} {/* Emergency Contact Information */} <Box sx={{ p: 2, backgroundColor: '#f5f5f5', borderRadius: 1, mb: 2 }} > <Typography variant="subtitle2" sx={{ mb: 1 }}> Contacts d'urgence: </Typography> <Typography variant="body2"> • Hotline d'urgence: <strong>9198</strong> (24h/7j) </Typography> <Typography variant="body2"> • Support humain: <strong>0745303145</strong> </Typography> </Box> </DialogContent> <DialogActions sx={{ p: 3, gap: 1 }}> <Button onClick={handleDialogClose} disabled={isInitiating || isConfirming} color="inherit" > Annuler </Button> <Button onClick={handleInitiateCall} variant="contained" color="error" disabled={!description.trim() || isInitiating || isConfirming} startIcon={ isInitiating || isConfirming ? ( <CircularProgress size={20} color="inherit" /> ) : ( <PhoneIcon /> ) } sx={{ minWidth: '140px', fontWeight: 'bold' }} > {isInitiating || isConfirming ? 'Connexion...' : 'Lancer l\'appel'} </Button> </DialogActions> </Dialog> </> ); }; export default EmergencyCallButton;