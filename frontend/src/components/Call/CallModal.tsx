/** * ============================================= * CALL MODAL COMPONENT * Modal interface for call management and controls * Handles call initiation, scheduling, and active call controls * ============================================= */ import React, { useState, useEffect } from 'react'; import { X, Phone, PhoneOff, Mic, MicOff, Volume2, VolumeX, Calendar, Clock, User, MessageSquare, Settings } from 'lucide-react'; import { useSelector, useDispatch } from 'react-redux'; import { RootState } from '../../store'; import { initiateCall, endCall, toggleMute, toggleSpeaker, scheduleCallback } from '../../store/slices/callSlice'; import CallScheduler from './CallScheduler'; import CallControls from './CallControls'; import './CallModal.css'; interface CallModalProps { isOpen: boolean; onClose: () => void; callType: 'new' | 'active' | 'scheduled'; } const CallModal: React.FC<CallModalProps> = ({ isOpen, onClose, callType }) => { const dispatch = useDispatch(); const { currentCall, isCallActive, callStatus, isMuted, isSpeakerOn, availableAgents, estimatedWaitTime, queuePosition, isLoading } = useSelector((state: RootState) => state.call); const { user } = useSelector((state: RootState) => state.auth); const [activeTab, setActiveTab] = useState<'immediate' | 'scheduled' | 'callback'>('immediate'); const [callReason, setCallReason] = useState(''); const [urgencyLevel, setUrgencyLevel] = useState<'low' | 'normal' | 'high' | 'urgent'>('normal'); useEffect(() => { if (callType === 'active') { setActiveTab('immediate'); } }, [callType]); const handleStartCall = async () => { if (!user) return; try { await dispatch(initiateCall({ type: 'immediate', userId: user.id, priority: urgencyLevel, reason: callReason, preferredAgent: undefined }) as any); } catch (error) { console.error('Failed to start call:', error); } }; const handleEndCall = async () => { if (!currentCall) return; try { await dispatch(endCall({ callId: currentCall.id, reason: 'user_ended' }) as any); onClose(); } catch (error) { console.error('Failed to end call:', error); } }; const handleToggleMute = () => { if (currentCall) { dispatch(toggleMute(currentCall.id) as any); } }; const handleToggleSpeaker = () => { if (currentCall) { dispatch(toggleSpeaker(currentCall.id) as any); } }; const handleScheduleCallback = async (scheduledTime: Date, preferences: any) => { if (!user) return; try { await dispatch(scheduleCallback({ userId: user.id, scheduledTime, reason: callReason, priority: urgencyLevel, preferences }) as any); onClose(); } catch (error) { console.error('Failed to schedule callback:', error); } }; const getCallStatusText = () => { switch (callStatus) { case 'connecting': return 'Connexion en cours...'; case 'ringing': return 'Sonnerie...'; case 'queued': return `En attente (position ${queuePosition})`; case 'connected': return 'Connecté'; case 'ended': return 'Appel terminé'; default: return 'Prêt à appeler'; } }; const formatWaitTime = (seconds: number) => { if (seconds < 60) return `${seconds} secondes`; const minutes = Math.floor(seconds / 60); const remainingSeconds = seconds % 60; return `${minutes}min ${remainingSeconds}s`; }; if (!isOpen) return null; return ( <div className="call-modal-overlay" onClick={onClose}> <div className="call-modal" onClick={(e) => e.stopPropagation()}> {/* Header */} <div className="call-modal__header"> <h2 className="call-modal__title"> {callType === 'active' ? 'Appel en cours' : 'Contacter le support'} </h2> <button onClick={onClose} className="call-modal__close" aria-label="Fermer" > <X size={24} /> </button> </div> {/* Content */} <div className="call-modal__content"> {callType === 'active' ? ( /* Active Call Interface */ <div className="call-modal__active-call"> <div className="call-status"> <div className="call-status__indicator"> <div className={`status-dot status-dot--${callStatus}`} /> <span className="call-status__text">{getCallStatusText()}</span> </div> {currentCall && ( <div className="call-info"> <div className="call-info__agent"> <User size={16} /> <span>{currentCall.agentName || 'Agent Free Mobile'}</span> </div> <div className="call-info__duration"> <Clock size={16} /> <span>{currentCall.duration || '00:00'}</span> </div> </div> )} </div> {/* Call Controls */} <CallControls isMuted={isMuted} isSpeakerOn={isSpeakerOn} onToggleMute={handleToggleMute} onToggleSpeaker={handleToggleSpeaker} onEndCall={handleEndCall} isLoading={isLoading} /> {/* Additional Options */} <div className="call-modal__options"> <button className="btn btn--secondary btn--small"> <MessageSquare size={16} /> Chat écrit </button> <button className="btn btn--secondary btn--small"> <Settings size={16} /> Paramètres </button> </div> </div> ) : ( /* New Call Interface */ <div className="call-modal__new-call"> {/* Tabs */} <div className="call-modal__tabs"> <button className={`tab ${activeTab === 'immediate' ? 'tab--active' : ''}`} onClick={() => setActiveTab('immediate')} > <Phone size={16} /> Appel immédiat </button> <button className={`tab ${activeTab === 'scheduled' ? 'tab--active' : ''}`} onClick={() => setActiveTab('scheduled')} > <Calendar size={16} /> Planifier </button> <button className={`tab ${activeTab === 'callback' ? 'tab--active' : ''}`} onClick={() => setActiveTab('callback')} > <Clock size={16} /> Rappel </button> </div> {/* Tab Content */} <div className="call-modal__tab-content"> {activeTab === 'immediate' && ( <div className="immediate-call"> {/* Queue Info */} <div className="queue-info"> <div className="queue-info__stats"> <div className="stat"> <User size={16} /> <span>{availableAgents} agent{availableAgents !== 1 ? 's' : ''} disponible{availableAgents !== 1 ? 's' : ''}</span> </div> {estimatedWaitTime > 0 && ( <div className="stat"> <Clock size={16} /> <span>Attente estimée: {formatWaitTime(estimatedWaitTime)}</span> </div> )} </div> </div> {/* Call Form */} <div className="call-form"> <div className="form-group"> <label htmlFor="call-reason">Motif de l'appel</label> <select id="call-reason" value={callReason} onChange={(e) => setCallReason(e.target.value)} className="form-control" > <option value="">Sélectionner un motif</option> <option value="technical">Support technique</option> <option value="billing">Facturation</option> <option value="subscription">Abonnement</option> <option value="complaint">Réclamation</option> <option value="other">Autre</option> </select> </div> <div className="form-group"> <label htmlFor="urgency">Niveau d'urgence</label> <select id="urgency" value={urgencyLevel} onChange={(e) => setUrgencyLevel(e.target.value as any)} className="form-control" > <option value="low">Faible</option> <option value="normal">Normal</option> <option value="high">Élevé</option> <option value="urgent">Urgent</option> </select> </div> </div> {/* Action Button */} <button onClick={handleStartCall} disabled={isLoading || !callReason} className="btn btn--primary btn--large call-modal__action-btn" > {isLoading ? ( <> <div className="spinner spinner--small" /> Connexion... </> ) : ( <> <Phone size={20} /> Démarrer l'appel </> )} </button> </div> )} {activeTab === 'scheduled' && ( <CallScheduler onSchedule={handleScheduleCallback} availableSlots={[]} // This would come from the API isLoading={isLoading} /> )} {activeTab === 'callback' && ( <div className="callback-request"> <p className="callback-request__description"> Demandez à être rappelé(e) quand un agent sera disponible. Vous recevrez un appel dans les plus brefs délais. </p> <div className="form-group"> <label htmlFor="callback-reason">Motif du rappel</label> <textarea id="callback-reason" value={callReason} onChange={(e) => setCallReason(e.target.value)} className="form-control" rows={3} placeholder="Décrivez brièvement votre demande..." /> </div> <button onClick={() => handleScheduleCallback(new Date(), { type: 'callback' })} disabled={isLoading || !callReason} className="btn btn--primary btn--large call-modal__action-btn" > {isLoading ? ( <> <div className="spinner spinner--small" /> Demande en cours... </> ) : ( <> <Clock size={20} /> Demander un rappel </> )} </button> </div> )} </div> </div> )} </div> </div> </div> ); }; export default CallModal;