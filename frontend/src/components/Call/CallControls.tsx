/** * ============================================= * CALL CONTROLS COMPONENT * Control interface for active calls * Handles mute, speaker, video, and call termination * ============================================= */ import React, { useState } from 'react'; import { Mic, MicOff, Volume2, VolumeX, PhoneOff, Video, VideoOff, Settings, MoreVertical, Pause, Play, Circle } from 'lucide-react'; import './CallControls.css'; interface CallControlsProps { isMuted: boolean; isSpeakerOn: boolean; isVideoEnabled?: boolean; isRecording?: boolean; onToggleMute: () => void; onToggleSpeaker: () => void; onToggleVideo?: () => void; onEndCall: () => void; onToggleRecording?: () => void; isLoading?: boolean; showAdvancedControls?: boolean; } const CallControls: React.FC<CallControlsProps> = ({ isMuted, isSpeakerOn, isVideoEnabled = false, isRecording = false, onToggleMute, onToggleSpeaker, onToggleVideo, onEndCall, onToggleRecording, isLoading = false, showAdvancedControls = false }) => { const [showMoreOptions, setShowMoreOptions] = useState(false); const [volume, setVolume] = useState(75); const handleVolumeChange = (newVolume: number) => { setVolume(newVolume); // Here you would integrate with the actual audio system }; return ( <div className="call-controls"> {/* Primary Controls */} <div className="call-controls__primary"> {/* Mute Button */} <button onClick={onToggleMute} className={`call-control call-control--mute ${ isMuted ? 'call-control--active' : '' }`} disabled={isLoading} aria-label={isMuted ? 'Activer le micro' : 'Couper le micro'} title={isMuted ? 'Activer le micro' : 'Couper le micro'} > {isMuted ? <MicOff size={20} /> : <Mic size={20} />} <span className="call-control__label"> {isMuted ? 'Micro coupé' : 'Micro'} </span> </button> {/* Speaker Button */} <button onClick={onToggleSpeaker} className={`call-control call-control--speaker ${ isSpeakerOn ? 'call-control--active' : '' }`} disabled={isLoading} aria-label={isSpeakerOn ? 'Désactiver le haut-parleur' : 'Activer le haut-parleur'} title={isSpeakerOn ? 'Désactiver le haut-parleur' : 'Activer le haut-parleur'} > {isSpeakerOn ? <Volume2 size={20} /> : <VolumeX size={20} />} <span className="call-control__label"> {isSpeakerOn ? 'Haut-parleur' : 'Écouteur'} </span> </button> {/* Video Button (if supported) */} {onToggleVideo && ( <button onClick={onToggleVideo} className={`call-control call-control--video ${ isVideoEnabled ? 'call-control--active' : '' }`} disabled={isLoading} aria-label={isVideoEnabled ? 'Désactiver la vidéo' : 'Activer la vidéo'} title={isVideoEnabled ? 'Désactiver la vidéo' : 'Activer la vidéo'} > {isVideoEnabled ? <Video size={20} /> : <VideoOff size={20} />} <span className="call-control__label"> {isVideoEnabled ? 'Vidéo on' : 'Vidéo off'} </span> </button> )} {/* End Call Button */} <button onClick={onEndCall} className="call-control call-control--end-call" disabled={isLoading} aria-label="Raccrocher" title="Raccrocher" > <PhoneOff size={20} /> <span className="call-control__label">Raccrocher</span> </button> </div> {/* Advanced Controls */} {showAdvancedControls && ( <div className="call-controls__advanced"> {/* Volume Control */} <div className="call-control call-control--volume"> <Volume2 size={16} /> <input type="range" min="0" max="100" value={volume} onChange={(e) => handleVolumeChange(Number(e.target.value))} className="volume-slider" aria-label="Contrôle du volume" /> <span className="volume-value">{volume}%</span> </div> {/* Recording Button (if supported) */} {onToggleRecording && ( <button onClick={onToggleRecording} className={`call-control call-control--record ${ isRecording ? 'call-control--active call-control--recording' : '' }`} disabled={isLoading} aria-label={isRecording ? 'Arrêter l\'enregistrement' : 'Démarrer l\'enregistrement'} title={isRecording ? 'Arrêter l\'enregistrement' : 'Démarrer l\'enregistrement'} > <Circle size={16} /> <span className="call-control__label"> {isRecording ? 'Enregistrement...' : 'Enregistrer'} </span> {isRecording && <div className="recording-indicator" />} </button> )} {/* More Options */} <div className="call-control call-control--more"> <button onClick={() => setShowMoreOptions(!showMoreOptions)} className="call-control__more-btn" aria-label="Plus d'options" title="Plus d'options" > <MoreVertical size={16} /> </button> {showMoreOptions && ( <div className="call-control__more-menu"> <button className="more-option"> <Settings size={14} /> Paramètres audio </button> <button className="more-option"> <Pause size={14} /> Mettre en attente </button> </div> )} </div> </div> )} {/* Loading Overlay */} {isLoading && ( <div className="call-controls__loading"> <div className="spinner" /> <span>Traitement en cours...</span> </div> )} </div> ); }; export default CallControls;