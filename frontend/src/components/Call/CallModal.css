/**
 * =============================================
 * 📞 CALL MODAL STYLES
 * Modal interface for call management
 * =============================================
 */

.call-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(4px);
  animation: overlayFadeIn 0.2s ease;
}

@keyframes overlayFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.call-modal {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Header */
.call-modal__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
}

.call-modal__title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #2d3436;
}

.call-modal__close {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  color: #636e72;
  transition: all 0.2s ease;
}

.call-modal__close:hover {
  background: #e9ecef;
  color: #2d3436;
}

/* Content */
.call-modal__content {
  padding: 24px;
  overflow-y: auto;
  max-height: calc(90vh - 80px);
}

/* Active Call Interface */
.call-modal__active-call {
  text-align: center;
}

.call-status {
  margin-bottom: 32px;
}

.call-status__indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 16px;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-dot--connecting { background: #fdcb6e; }
.status-dot--ringing { background: #0984e3; }
.status-dot--queued { background: #fd79a8; }
.status-dot--connected { background: #00b894; }
.status-dot--ended { background: #636e72; }

.call-status__text {
  font-size: 18px;
  font-weight: 500;
  color: #2d3436;
}

.call-info {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-top: 16px;
}

.call-info__agent,
.call-info__duration {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #636e72;
}

.call-modal__options {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 24px;
}

/* New Call Interface */
.call-modal__new-call {
  /* Styles handled by tabs and content */
}

/* Tabs */
.call-modal__tabs {
  display: flex;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 24px;
}

.tab {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #636e72;
  transition: all 0.2s ease;
  border-bottom: 2px solid transparent;
}

.tab:hover {
  color: #2d3436;
  background: #f8f9fa;
}

.tab--active {
  color: #0984e3;
  border-bottom-color: #0984e3;
  background: #f8f9fa;
}

/* Tab Content */
.call-modal__tab-content {
  min-height: 300px;
}

/* Immediate Call */
.immediate-call {
  /* Styles for immediate call form */
}

.queue-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
}

.queue-info__stats {
  display: flex;
  justify-content: center;
  gap: 24px;
}

.queue-info__stats .stat {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #636e72;
}

/* Call Form */
.call-form {
  margin-bottom: 24px;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #2d3436;
}

.form-control {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-control:focus {
  outline: none;
  border-color: #0984e3;
  box-shadow: 0 0 0 3px rgba(9, 132, 227, 0.1);
}

/* Action Button */
.call-modal__action-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 16px;
  font-size: 16px;
  font-weight: 600;
}

/* Callback Request */
.callback-request {
  text-align: center;
}

.callback-request__description {
  font-size: 14px;
  color: #636e72;
  margin-bottom: 24px;
  line-height: 1.5;
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn--primary {
  background: linear-gradient(135deg, #0984e3, #74b9ff);
  color: white;
}

.btn--primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #0770c4, #5a9df7);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(9, 132, 227, 0.3);
}

.btn--secondary {
  background: #f8f9fa;
  color: #636e72;
  border: 1px solid #e9ecef;
}

.btn--secondary:hover:not(:disabled) {
  background: #e9ecef;
  color: #2d3436;
}

.btn--small {
  padding: 8px 16px;
  font-size: 12px;
}

.btn--large {
  padding: 16px 24px;
  font-size: 16px;
}

/* Spinner */
.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner--small {
  width: 14px;
  height: 14px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
  .call-modal {
    width: 95%;
    margin: 20px;
  }
  
  .call-modal__content {
    padding: 20px;
  }
  
  .queue-info__stats {
    flex-direction: column;
    gap: 12px;
  }
  
  .call-info {
    flex-direction: column;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .call-modal__tabs {
    flex-direction: column;
  }
  
  .tab {
    border-bottom: none;
    border-right: 2px solid transparent;
  }
  
  .tab--active {
    border-bottom: none;
    border-right-color: #0984e3;
  }
}
