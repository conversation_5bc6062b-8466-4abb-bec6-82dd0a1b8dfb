/**
 * =============================================
 * 📞 CALL BUTTON STYLES
 * Floating and inline call button styles
 * =============================================
 */

.call-button {
  position: relative;
  z-index: 1000;
}

/* Floating Button */
.call-button--floating {
  position: fixed;
  z-index: 1000;
}

.call-button--bottom-right {
  bottom: 20px;
  right: 20px;
}

.call-button--bottom-left {
  bottom: 20px;
  left: 20px;
}

.call-button--top-right {
  top: 20px;
  right: 20px;
}

.call-button--top-left {
  top: 20px;
  left: 20px;
}

/* But<PERSON> Sizes */
.call-button--small .call-button__btn {
  width: 48px;
  height: 48px;
}

.call-button--medium .call-button__btn {
  width: 56px;
  height: 56px;
}

.call-button--large .call-button__btn {
  width: 64px;
  height: 64px;
}

/* Button Base Styles */
.call-button__btn {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.call-button__btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.call-button__btn:active {
  transform: translateY(0);
}

.call-button__btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Button States */
.call-button--available .call-button__btn {
  background: linear-gradient(135deg, #00d4aa, #00b894);
  color: white;
}

.call-button--limited .call-button__btn {
  background: linear-gradient(135deg, #fdcb6e, #e17055);
  color: white;
}

.call-button--busy .call-button__btn {
  background: linear-gradient(135deg, #fd79a8, #e84393);
  color: white;
}

.call-button--active .call-button__btn {
  background: linear-gradient(135deg, #0984e3, #74b9ff);
  color: white;
}

/* Icon */
.call-button__icon {
  width: 20px;
  height: 20px;
  transition: transform 0.2s ease;
}

.call-button--small .call-button__icon {
  width: 16px;
  height: 16px;
}

.call-button--large .call-button__icon {
  width: 24px;
  height: 24px;
}

/* Status Indicator */
.call-button__status {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
}

.call-button__status--available {
  background-color: #00b894;
}

.call-button__status--limited {
  background-color: #fdcb6e;
}

.call-button__status--busy {
  background-color: #e17055;
}

.call-button__status--active {
  background-color: #0984e3;
  animation: pulse 2s infinite;
}

/* Loading Spinner */
.call-button__spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Pulse Animation */
.call-button--pulse .call-button__btn {
  animation: buttonPulse 1.5s ease-in-out infinite;
}

@keyframes buttonPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Tooltip */
.call-button__tooltip {
  position: absolute;
  bottom: 100%;
  right: 0;
  margin-bottom: 10px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: 16px;
  min-width: 280px;
  z-index: 1001;
  opacity: 0;
  transform: translateY(10px);
  animation: tooltipFadeIn 0.2s ease forwards;
}

@keyframes tooltipFadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.call-button__tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  right: 20px;
  border: 8px solid transparent;
  border-top-color: white;
}

.call-button__tooltip-content h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2d3436;
}

.call-button__tooltip-stats {
  margin-bottom: 16px;
}

.call-button__tooltip-stats .stat {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #636e72;
}

.call-button__tooltip-stats .stat:last-child {
  margin-bottom: 0;
}

.call-button__tooltip-actions {
  display: flex;
  gap: 8px;
}

.call-button__tooltip-actions .btn {
  flex: 1;
  padding: 8px 12px;
  font-size: 12px;
}

/* Inline Button */
.call-button--inline .call-button__btn {
  border-radius: 8px;
  padding: 12px 20px;
  width: auto;
  height: auto;
  display: inline-flex;
  gap: 8px;
}

.call-button--inline .call-button__text {
  font-size: 14px;
  font-weight: 500;
}

.call-button--inline .call-button__info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-left: 12px;
  padding-left: 12px;
  border-left: 1px solid rgba(255, 255, 255, 0.3);
}

.call-button--inline .call-button__agents,
.call-button--inline .call-button__wait-time {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  opacity: 0.9;
}

/* Responsive */
@media (max-width: 768px) {
  .call-button--floating {
    bottom: 80px; /* Account for mobile navigation */
  }
  
  .call-button__tooltip {
    right: -20px;
    min-width: 260px;
  }
  
  .call-button__tooltip::after {
    right: 40px;
  }
}

@media (max-width: 480px) {
  .call-button--medium .call-button__btn {
    width: 52px;
    height: 52px;
  }
  
  .call-button__tooltip {
    right: -40px;
    min-width: 240px;
  }
}
