/** * ============================================= * [AI] AI CALL INITIATOR COMPONENT * Intelligent call initiation based on conversation analysis * Provides AI-powered suggestions and seamless escalation * ============================================= */ import React, { useState, useEffect } from 'react'; import { Box, Button, Card, CardContent, Typography, Dialog, DialogTitle, DialogContent, DialogActions, Chip, LinearProgress, Alert, List, ListItem, ListItemIcon, ListItemText, Divider, IconButton, Tooltip } from '@mui/material'; import { SmartToy as AIIcon, Phone as PhoneIcon, Psychology as BrainIcon, TrendingUp as AnalysisIcon, Person as AgentIcon, Close as CloseIcon, CheckCircle as CheckIcon, Warning as WarningIcon, Info as InfoIcon } from '@mui/icons-material'; import { useDispatch, useSelector } from 'react-redux'; import { RootState } from '../../store'; import { analyzeConversation, initiateAICall, getAISuggestions, evaluateEscalation, escalateToHuman } from '../../store/slices/aiCallSlice'; interface AICallInitiatorProps { conversationId: string; conversationHistory: Array<{ sender: string; content: string; timestamp: Date; }>; onCallInitiated?: (callSession: any) => void; onEscalated?: (escalationInfo: any) => void; } const AICallInitiator: React.FC<AICallInitiatorProps> = ({ conversationId, conversationHistory, onCallInitiated, onEscalated }) => { const dispatch = useDispatch(); const { analysis, callSession, suggestions, escalationEvaluation, loading, error } = useSelector((state: RootState) => state.aiCall); const [showAnalysisDialog, setShowAnalysisDialog] = useState(false); const [showCallDialog, setShowCallDialog] = useState(false); const [currentStep, setCurrentStep] = useState<'analysis' | 'suggestions' | 'escalation'>('analysis'); useEffect(() => { // Auto-analyze conversation when component mounts if (conversationId && conversationHistory.length > 5) { handleAnalyzeConversation(); } }, [conversationId, conversationHistory]); const handleAnalyzeConversation = async () => { try { await dispatch(analyzeConversation(conversationId) as any); } catch (error) { console.error('Failed to analyze conversation:', error); } }; const handleInitiateAICall = async (forceCall = false) => { try { const result = await dispatch(initiateAICall({ conversationId, forceCall }) as any); if (result.payload.success) { setShowCallDialog(true); setCurrentStep('suggestions'); onCallInitiated?.(result.payload.callSession); } } catch (error) { console.error('Failed to initiate AI call:', error); } }; const handleGetSuggestions = async (userInput: string) => { if (!callSession?.callId) return; try { await dispatch(getAISuggestions({ callId: callSession.callId, userInput, context: { conversationHistory } }) as any); } catch (error) { console.error('Failed to get AI suggestions:', error); } }; const handleEvaluateEscalation = async () => { if (!callSession?.callId) return; try { const result = await dispatch(evaluateEscalation({ callId: callSession.callId, aiAttempts: callSession.aiAttempts || 0, conversationHistory }) as any); if (result.payload.evaluation.shouldEscalate) { setCurrentStep('escalation'); } } catch (error) { console.error('Failed to evaluate escalation:', error); } }; const handleEscalateToHuman = async (escalationReason: string) => { if (!callSession?.callId) return; try { const result = await dispatch(escalateToHuman({ callId: callSession.callId, escalationReason, conversationHistory, aiAttempts: callSession.aiAttempts || 0 }) as any); if (result.payload.success) { onEscalated?.(result.payload.escalation); setShowCallDialog(false); } } catch (error) { console.error('Failed to escalate to human:', error); } }; const getAnalysisColor = (confidence: number) => { if (confidence >= 0.8) return 'success'; if (confidence >= 0.6) return 'warning'; return 'info'; }; const getAnalysisIcon = (needsCall: boolean, confidence: number) => { if (needsCall && confidence >= 0.8) return <PhoneIcon color="error" />; if (needsCall) return <WarningIcon color="warning" />; return <InfoIcon color="info" />; }; return ( <Box> {/* AI Call Analysis Card */} {analysis && ( <Card sx={{ mb: 2, border: analysis.needsCall ? '2px solid #f44336' : '1px solid #e0e0e0', backgroundColor: analysis.needsCall ? '#fff3e0' : 'inherit' }} > <CardContent> <Box display="flex" alignItems="center" mb={2}> <AIIcon sx={{ mr: 1, color: '#1976d2' }} /> <Typography variant="h6"> Analyse IA de la conversation </Typography> <Chip label={`${Math.round(analysis.confidence * 100)}% confiance`} color={getAnalysisColor(analysis.confidence)} size="small" sx={{ ml: 'auto' }} /> </Box> <Alert severity={analysis.needsCall ? 'warning' : 'info'} icon={getAnalysisIcon(analysis.needsCall, analysis.confidence)} sx={{ mb: 2 }} > <Typography variant="body2"> {analysis.recommendation?.message || (analysis.needsCall ? 'Un appel téléphonique est recommandé pour résoudre ce problème.' : 'La conversation peut continuer en mode texte.' )} </Typography> </Alert> {analysis.needsCall && ( <Box display="flex" gap={1} flexWrap="wrap" mb={2}> {analysis.reasons?.map((reason, index) => ( <Chip key={index} label={reason.replace(/_/g, ' ')} size="small" variant="outlined" color="warning" /> ))} </Box> )} <Box display="flex" gap={2}> <Button variant="outlined" startIcon={<AnalysisIcon />} onClick={() => setShowAnalysisDialog(true)} size="small" > Voir détails </Button> {analysis.needsCall && ( <Button variant="contained" startIcon={<PhoneIcon />} onClick={() => handleInitiateAICall()} color="primary" size="small" > Lancer appel IA </Button> )} {!analysis.needsCall && ( <Button variant="outlined" startIcon={<PhoneIcon />} onClick={() => handleInitiateAICall(true)} size="small" > Forcer l'appel </Button> )} </Box> </CardContent> </Card> )} {/* Analysis Details Dialog */} <Dialog open={showAnalysisDialog} onClose={() => setShowAnalysisDialog(false)} maxWidth="md" fullWidth > <DialogTitle> <Box display="flex" alignItems="center"> <BrainIcon sx={{ mr: 1 }} /> Analyse détaillée de la conversation <IconButton onClick={() => setShowAnalysisDialog(false)} sx={{ ml: 'auto' }} > <CloseIcon /> </IconButton> </Box> </DialogTitle> <DialogContent> {analysis && ( <Box> <Typography variant="h6" gutterBottom> Métriques d'analyse </Typography> <Box mb={3}> <Typography variant="body2" color="textSecondary"> Score de frustration </Typography> <LinearProgress variant="determinate" value={(analysis.analysis?.frustrationScore || 0) * 100} color={analysis.analysis?.frustrationScore > 0.6 ? 'error' : 'primary'} sx={{ mb: 1 }} /> <Typography variant="body2" color="textSecondary"> Score de complexité </Typography> <LinearProgress variant="determinate" value={(analysis.analysis?.complexityScore || 0) * 100} color={analysis.analysis?.complexityScore > 0.7 ? 'warning' : 'primary'} sx={{ mb: 1 }} /> </Box> <Divider sx={{ my: 2 }} /> <Typography variant="h6" gutterBottom> Recommandation IA </Typography> <Alert severity={analysis.recommendation?.priority === 'high' ? 'error' : 'info'}> {analysis.recommendation?.message} </Alert> </Box> )} </DialogContent> </Dialog> {/* AI Call Dialog */} <Dialog open={showCallDialog} onClose={() => setShowCallDialog(false)} maxWidth="md" fullWidth PaperProps={{ sx: { minHeight: '500px' } }} > <DialogTitle> <Box display="flex" alignItems="center"> <AIIcon sx={{ mr: 1, color: '#1976d2' }} /> Assistant IA - Appel en cours <Chip label={currentStep} color="primary" size="small" sx={{ ml: 2 }} /> <IconButton onClick={() => setShowCallDialog(false)} sx={{ ml: 'auto' }} > <CloseIcon /> </IconButton> </Box> </DialogTitle> <DialogContent> {loading && <LinearProgress sx={{ mb: 2 }} />} {currentStep === 'suggestions' && suggestions && ( <Box> <Typography variant="h6" gutterBottom> Suggestions IA personnalisées </Typography> <List> {suggestions.map((suggestion, index) => ( <ListItem key={index} divider> <ListItemIcon> <CheckIcon color="success" /> </ListItemIcon> <ListItemText primary={suggestion.title} secondary={ <Box> <Typography variant="body2" color="textSecondary"> {suggestion.description} </Typography> {suggestion.timeEstimate && ( <Chip label={`⏱ ${suggestion.timeEstimate}`} size="small" variant="outlined" sx={{ mt: 1 }} /> )} </Box> } /> </ListItem> ))} </List> <Box mt={2} display="flex" gap={2}> <Button variant="contained" onClick={() => handleEvaluateEscalation()} startIcon={<AgentIcon />} > Évaluer escalade </Button> <Button variant="outlined" onClick={() => handleGetSuggestions('Aucune de ces solutions ne fonctionne')} > Autres suggestions </Button> </Box> </Box> )} {currentStep === 'escalation' && escalationEvaluation && ( <Box> <Typography variant="h6" gutterBottom> Évaluation d'escalade </Typography> {escalationEvaluation.shouldEscalate ? ( <Alert severity="warning" sx={{ mb: 2 }}> <Typography variant="body2"> L'IA recommande un transfert vers un conseiller humain. </Typography> </Alert> ) : ( <Alert severity="info" sx={{ mb: 2 }}> <Typography variant="body2"> L'IA peut continuer à vous assister. </Typography> </Alert> )} <Box mb={2}> <Typography variant="body2" color="textSecondary" gutterBottom> Confiance: {Math.round(escalationEvaluation.confidence * 100)}% </Typography> <LinearProgress variant="determinate" value={escalationEvaluation.confidence * 100} color={escalationEvaluation.confidence > 0.7 ? 'success' : 'warning'} /> </Box> {escalationEvaluation.reasons && ( <Box mb={2}> <Typography variant="body2" color="textSecondary" gutterBottom> Raisons: </Typography> {escalationEvaluation.reasons.map((reason, index) => ( <Chip key={index} label={reason.replace(/_/g, ' ')} size="small" variant="outlined" sx={{ mr: 1, mb: 1 }} /> ))} </Box> )} {escalationEvaluation.shouldEscalate && ( <Button variant="contained" color="primary" startIcon={<AgentIcon />} onClick={() => handleEscalateToHuman(escalationEvaluation.reasons[0])} fullWidth > Transférer vers conseiller humain </Button> )} </Box> )} {error && ( <Alert severity="error" sx={{ mt: 2 }}> {error} </Alert> )} </DialogContent> </Dialog> </Box> ); }; export default AICallInitiator;