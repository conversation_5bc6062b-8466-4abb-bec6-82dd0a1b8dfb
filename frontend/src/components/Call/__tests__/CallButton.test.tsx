import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import CallButton from '../CallButton';
import authSlice from '../../../store/authSlice';

// Mock CSS import
jest.mock('../CallButton.css', () => ({}));

// Simple mock call slice
const callSlice = (state = {
  isCallActive: false,
  queueStats: { currentCalls: 2, totalCalls: 10 },
  availableAgents: 5,
  estimatedWaitTime: 120,
  isLoading: false,
  error: null,
  callHistory: [],
  currentCall: null,
}, action: any) => state;

const createMockStore = (initialState: any = {}) => {
  return configureStore({
    reducer: {
      call: callSlice,
      auth: authSlice,
    },
    preloadedState: {
      call: {
        isCallActive: false,
        queueStats: { currentCalls: 2, totalCalls: 10 },
        availableAgents: 5,
        estimatedWaitTime: 120,
        isLoading: false,
        error: null,
        callHistory: [],
        currentCall: null,
        ...initialState.call,
      },
      auth: {
        user: {
          id: '1',
          email: '<EMAIL>',
          name: 'Test User',
          role: 'user',
        },
        isAuthenticated: true,
        loading: false,
        error: null,
        token: null,
        ...initialState.auth,
      },
    },
  });
};

const renderWithStore = (component: React.ReactElement, initialState: any = {}) => {
  const store = createMockStore(initialState);
  return {
    ...render(
      <Provider store={store}>
        {component}
      </Provider>
    ),
    store,
  };
};

describe('CallButton', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders with default props', () => {
      renderWithStore(<CallButton />);

      expect(screen.getByRole('button')).toBeInTheDocument();
      expect(screen.getByTestId('phone-icon')).toBeInTheDocument();
      expect(screen.getByLabelText('Appeler le support')).toBeInTheDocument();
    });

    it('renders inline variant', () => {
      renderWithStore(<CallButton variant="inline" />);

      const button = screen.getByRole('button');
      expect(button).toHaveClass('call-button__btn--inline');
      expect(screen.getByText('Appeler le support')).toBeInTheDocument();
    });

    it('renders floating variant by default', () => {
      renderWithStore(<CallButton />);

      const button = screen.getByRole('button');
      expect(button).toHaveClass('call-button__btn--floating');
    });

    it('shows queue information when enabled', () => {
      renderWithStore(<CallButton variant="inline" showQueueInfo={true} />);

      expect(screen.getByTestId('users-icon')).toBeInTheDocument();
      expect(screen.getByText('5 disponibles')).toBeInTheDocument();
      expect(screen.getByTestId('clock-icon')).toBeInTheDocument();
      expect(screen.getByText('~2min')).toBeInTheDocument();
    });

    it('hides queue information when disabled', () => {
      renderWithStore(<CallButton variant="inline" showQueueInfo={false} />);

      expect(screen.queryByTestId('users-icon')).not.toBeInTheDocument();
      expect(screen.queryByText('5 disponibles')).not.toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    it('opens modal when clicked', () => {
      renderWithStore(<CallButton />);

      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(screen.getByTestId('call-modal')).toBeInTheDocument();
      expect(screen.getByText('Call Modal - Type: new')).toBeInTheDocument();
    });

    it('closes modal when close button is clicked', () => {
      renderWithStore(<CallButton />);

      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(screen.getByTestId('call-modal')).toBeInTheDocument();

      const closeButton = screen.getByText('Close Modal');
      fireEvent.click(closeButton);

      expect(screen.queryByTestId('call-modal')).not.toBeInTheDocument();
    });

    it('handles hover events', () => {
      renderWithStore(<CallButton />);

      const button = screen.getByRole('button');
      fireEvent.mouseEnter(button);
      fireEvent.mouseLeave(button);

      expect(button).toBeInTheDocument();
    });
  });

  describe('Queue Stats', () => {
    it('formats wait time correctly for minutes', () => {
      renderWithStore(<CallButton variant="inline" />);

      expect(screen.getByText('~2min')).toBeInTheDocument();
    });

    it('handles plural agent count', () => {
      renderWithStore(<CallButton variant="inline" />);

      expect(screen.getByText('5 disponibles')).toBeInTheDocument();
    });
  });

  describe('Status Colors', () => {
    it('applies available status class when many agents available', () => {
      const { container } = renderWithStore(<CallButton />);

      expect(container.querySelector('.call-button--available')).toBeInTheDocument();
    });
  });

  describe('Props Customization', () => {
    it('applies custom className', () => {
      const { container } = renderWithStore(<CallButton className="custom-class" />);

      expect(container.querySelector('.custom-class')).toBeInTheDocument();
    });

    it('applies different sizes', () => {
      const { container } = renderWithStore(<CallButton size="large" />);

      expect(container.querySelector('.call-button--large')).toBeInTheDocument();
    });

    it('applies different positions', () => {
      const { container } = renderWithStore(<CallButton position="top-left" />);

      expect(container.querySelector('.call-button--top-left')).toBeInTheDocument();
    });
  });
});