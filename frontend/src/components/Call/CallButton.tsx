/**
 * =============================================
 * CALL BUTTON COMPONENT
 * Floating call button with click-to-call functionality
 * Integrates with call service and queue management
 * =============================================
 */
import React, { useState } from 'react';

interface CallButtonProps {
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  size?: 'small' | 'medium' | 'large';
  variant?: 'floating' | 'inline';
  showQueueInfo?: boolean;
  className?: string;
}

// Mock CallModal component
const CallModal: React.FC<{ isOpen: boolean; onClose: () => void; callType: string }> = ({
  isOpen,
  onClose,
  callType
}) => {
  if (!isOpen) return null;

  return (
    <div data-testid="call-modal">
      <div>Call Modal - Type: {callType}</div>
      <button onClick={onClose}>Close Modal</button>
    </div>
  );
};

const CallButton: React.FC<CallButtonProps> = ({
  position = 'bottom-right',
  size = 'medium',
  variant = 'floating',
  showQueueInfo = true,
  className = ''
}) => {
  // Mock state
  const isCallActive = false;
  const availableAgents = 5;
  const estimatedWaitTime = 120;
  const isLoading = false;

  const [showModal, setShowModal] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const handleCallClick = () => {
    setShowModal(true);
  };

  const getButtonIcon = () => {
    if (isCallActive) {
      return <div data-testid="phone-call-icon" className="call-button__icon" />;
    }
    return <div data-testid="phone-icon" className="call-button__icon" />;
  };

  const getButtonText = () => {
    if (isCallActive) {
      return 'Appel en cours';
    }
    if (availableAgents === 0) {
      return 'File d\'attente';
    }
    return 'Appeler le support';
  };

  const getStatusColor = () => {
    if (isCallActive) return 'active';
    if (availableAgents === 0) return 'busy';
    if (availableAgents < 3) return 'limited';
    return 'available';
  };

  const formatWaitTime = (seconds: number) => {
    if (seconds < 60) return `~${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    return `~${minutes}min`;
  };

  const buttonClasses = [
    'call-button',
    `call-button--${variant}`,
    `call-button--${size}`,
    `call-button--${position}`,
    `call-button--${getStatusColor()}`,
    isHovered ? 'call-button--hovered' : '',
    isLoading ? 'call-button--loading' : '',
    className
  ].filter(Boolean).join(' ');

  if (variant === 'inline') {
    return (
      <div className={buttonClasses}>
        <button
          onClick={handleCallClick}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          disabled={isLoading}
          className="call-button__btn call-button__btn--inline"
          aria-label={getButtonText()}
        >
          {getButtonIcon()}
          <span className="call-button__text">{getButtonText()}</span>

          {showQueueInfo && (
            <div className="call-button__info">
              <div className="call-button__agents">
                <div data-testid="users-icon" />
                <span>{availableAgents} disponible{availableAgents !== 1 ? 's' : ''}</span>
              </div>
              {estimatedWaitTime > 0 && (
                <div className="call-button__wait-time">
                  <div data-testid="clock-icon" />
                  <span>{formatWaitTime(estimatedWaitTime)}</span>
                </div>
              )}
            </div>
          )}
        </button>

        <CallModal
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          callType={isCallActive ? 'active' : 'new'}
        />
      </div>
    );
  }

  return (
    <>
      <div className={buttonClasses}>
        <button
          onClick={handleCallClick}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          disabled={isLoading}
          className="call-button__btn call-button__btn--floating"
          aria-label={getButtonText()}
        >
          {getButtonIcon()}
          <div className={`call-button__status call-button__status--${getStatusColor()}`} />
        </button>
      </div>

      <CallModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        callType={isCallActive ? 'active' : 'new'}
      />
    </>
  );
};

export default CallButton;