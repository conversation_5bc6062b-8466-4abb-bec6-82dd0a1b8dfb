/** * ============================================= * CALL SCHEDULER COMPONENT * Interface for scheduling callback appointments * Integrates with calendar and agent availability * ============================================= */ import React, { useState, useEffect } from 'react'; import { Calendar, Clock, User, CheckCircle, AlertCircle, ChevronLeft, ChevronRight } from 'lucide-react'; import './CallScheduler.css'; interface TimeSlot { id: string; time: string; available: boolean; agentName?: string; agentId?: string; } interface CallSchedulerProps { onSchedule: (scheduledTime: Date, preferences: any) => void; availableSlots: TimeSlot[]; isLoading: boolean; } const CallScheduler: React.FC<CallSchedulerProps> = ({ onSchedule, availableSlots, isLoading }) => { const [selectedDate, setSelectedDate] = useState<Date>(new Date()); const [selectedTime, setSelectedTime] = useState<string>(''); const [selectedAgent, setSelectedAgent] = useState<string>(''); const [notes, setNotes] = useState<string>(''); const [currentMonth, setCurrentMonth] = useState<Date>(new Date()); // Generate time slots for the selected date const generateTimeSlots = (): TimeSlot[] => { const slots: TimeSlot[] = []; const startHour = 9; // 9 AM const endHour = 18; // 6 PM for (let hour = startHour; hour < endHour; hour++) { for (let minute = 0; minute < 60; minute += 30) { const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`; slots.push({ id: `${selectedDate.toISOString().split('T')[0]}-${timeString}`, time: timeString, available: Math.random() > 0.3, // Mock availability agentName: Math.random() > 0.5 ? 'Agent Free Mobile' : 'Conseiller Expert', agentId: Math.random().toString(36).substr(2, 9) }); } } return slots; }; const [timeSlots, setTimeSlots] = useState<TimeSlot[]>(generateTimeSlots()); useEffect(() => { setTimeSlots(generateTimeSlots()); }, [selectedDate]); const handleDateSelect = (date: Date) => { setSelectedDate(date); setSelectedTime(''); setSelectedAgent(''); }; const handleTimeSelect = (slot: TimeSlot) => { if (!slot.available) return; setSelectedTime(slot.time); if (slot.agentId) { setSelectedAgent(slot.agentId); } }; const handleSchedule = () => { if (!selectedTime) return; const scheduledDateTime = new Date(selectedDate); const [hours, minutes] = selectedTime.split(':').map(Number); scheduledDateTime.setHours(hours, minutes, 0, 0); const preferences = { preferredAgent: selectedAgent, notes: notes, type: 'scheduled' }; onSchedule(scheduledDateTime, preferences); }; const getDaysInMonth = (date: Date) => { const year = date.getFullYear(); const month = date.getMonth(); const firstDay = new Date(year, month, 1); const lastDay = new Date(year, month + 1, 0); const daysInMonth = lastDay.getDate(); const startingDayOfWeek = firstDay.getDay(); const days = []; // Add empty cells for days before the first day of the month for (let i = 0; i < startingDayOfWeek; i++) { days.push(null); } // Add days of the month for (let day = 1; day <= daysInMonth; day++) { days.push(new Date(year, month, day)); } return days; }; const isDateSelectable = (date: Date) => { const today = new Date(); today.setHours(0, 0, 0, 0); return date >= today; }; const formatDate = (date: Date) => { return date.toLocaleDateString('fr-FR', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' }); }; const navigateMonth = (direction: 'prev' | 'next') => { const newMonth = new Date(currentMonth); if (direction === 'prev') { newMonth.setMonth(currentMonth.getMonth() - 1); } else { newMonth.setMonth(currentMonth.getMonth() + 1); } setCurrentMonth(newMonth); }; const monthNames = [ 'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin', 'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre' ]; const dayNames = ['Dim', 'Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam']; return ( <div className="call-scheduler"> <div className="call-scheduler__header"> <h3>Planifier un rappel</h3> <p>Choisissez une date et un créneau horaire pour être rappelé(e)</p> </div> <div className="call-scheduler__content"> {/* Calendar */} <div className="call-scheduler__calendar"> <div className="calendar-header"> <button onClick={() => navigateMonth('prev')} className="calendar-nav" aria-label="Mois précédent" > <ChevronLeft size={20} /> </button> <h4>{monthNames[currentMonth.getMonth()]} {currentMonth.getFullYear()}</h4> <button onClick={() => navigateMonth('next')} className="calendar-nav" aria-label="Mois suivant" > <ChevronRight size={20} /> </button> </div> <div className="calendar-grid"> {dayNames.map(day => ( <div key={day} className="calendar-day-header"> {day} </div> ))} {getDaysInMonth(currentMonth).map((date, index) => ( <div key={index} className={`calendar-day ${ date ? 'calendar-day--selectable' : 'calendar-day--empty' } ${ date && !isDateSelectable(date) ? 'calendar-day--disabled' : '' } ${ date && selectedDate.toDateString() === date.toDateString() ? 'calendar-day--selected' : '' }`} onClick={() => date && isDateSelectable(date) && handleDateSelect(date)} > {date ? date.getDate() : ''} </div> ))} </div> </div> {/* Selected Date Info */} {selectedDate && ( <div className="call-scheduler__selected-date"> <div className="selected-date-info"> <Calendar size={16} /> <span>{formatDate(selectedDate)}</span> </div> </div> )} {/* Time Slots */} <div className="call-scheduler__time-slots"> <h4>Créneaux disponibles</h4> <div className="time-slots-grid"> {timeSlots.map(slot => ( <button key={slot.id} className={`time-slot ${ !slot.available ? 'time-slot--unavailable' : '' } ${ selectedTime === slot.time ? 'time-slot--selected' : '' }`} onClick={() => handleTimeSelect(slot)} disabled={!slot.available} > <div className="time-slot__time"> <Clock size={14} /> {slot.time} </div> {slot.available && slot.agentName && ( <div className="time-slot__agent"> <User size={12} /> {slot.agentName} </div> )} {!slot.available && ( <div className="time-slot__unavailable"> <AlertCircle size={12} /> Indisponible </div> )} </button> ))} </div> </div> {/* Notes */} <div className="call-scheduler__notes"> <label htmlFor="scheduler-notes">Notes (optionnel)</label> <textarea id="scheduler-notes" value={notes} onChange={(e) => setNotes(e.target.value)} placeholder="Précisez le motif de votre demande..." rows={3} className="form-control" /> </div> {/* Schedule Button */} <button onClick={handleSchedule} disabled={!selectedTime || isLoading} className="btn btn--primary btn--large call-scheduler__schedule-btn" > {isLoading ? ( <> <div className="spinner spinner--small" /> Planification... </> ) : ( <> <CheckCircle size={20} /> Confirmer le rendez-vous </> )} </button> </div> </div> ); }; export default CallScheduler;