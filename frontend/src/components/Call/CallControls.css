/**
 * =============================================
 * 🎛️ CALL CONTROLS STYLES
 * Control interface for active calls
 * =============================================
 */

.call-controls {
  position: relative;
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Primary Controls */
.call-controls__primary {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 20px;
}

.call-control {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 12px;
  border-radius: 12px;
  transition: all 0.2s ease;
  min-width: 80px;
}

.call-control:hover:not(:disabled) {
  background: #f8f9fa;
}

.call-control:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Control States */
.call-control--mute {
  color: #636e72;
}

.call-control--mute.call-control--active {
  background: #fee2e2;
  color: #dc2626;
}

.call-control--speaker {
  color: #636e72;
}

.call-control--speaker.call-control--active {
  background: #dbeafe;
  color: #2563eb;
}

.call-control--video {
  color: #636e72;
}

.call-control--video.call-control--active {
  background: #dcfce7;
  color: #16a34a;
}

.call-control--end-call {
  background: #fee2e2;
  color: #dc2626;
}

.call-control--end-call:hover:not(:disabled) {
  background: #fecaca;
  transform: scale(1.05);
}

.call-control--record {
  color: #636e72;
}

.call-control--record.call-control--active {
  background: #fef3c7;
  color: #d97706;
}

.call-control--record.call-control--recording {
  animation: recordingPulse 2s infinite;
}

@keyframes recordingPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Control Labels */
.call-control__label {
  font-size: 12px;
  font-weight: 500;
  text-align: center;
}

/* Advanced Controls */
.call-controls__advanced {
  border-top: 1px solid #e9ecef;
  padding-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Volume Control */
.call-control--volume {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.volume-slider {
  flex: 1;
  height: 4px;
  background: #e9ecef;
  border-radius: 2px;
  outline: none;
  cursor: pointer;
}

.volume-slider::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  background: #0984e3;
  border-radius: 50%;
  cursor: pointer;
}

.volume-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: #0984e3;
  border-radius: 50%;
  border: none;
  cursor: pointer;
}

.volume-value {
  font-size: 12px;
  font-weight: 500;
  color: #636e72;
  min-width: 35px;
  text-align: right;
}

/* More Options */
.call-control--more {
  position: relative;
}

.call-control__more-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  color: #636e72;
  transition: all 0.2s ease;
}

.call-control__more-btn:hover {
  background: #f8f9fa;
  color: #2d3436;
}

.call-control__more-menu {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 8px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: 8px;
  min-width: 180px;
  z-index: 100;
}

.more-option {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 8px 12px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  color: #636e72;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.more-option:hover {
  background: #f8f9fa;
  color: #2d3436;
}

/* Recording Indicator */
.recording-indicator {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 8px;
  height: 8px;
  background: #dc2626;
  border-radius: 50%;
  animation: pulse 1s infinite;
}

/* Loading Overlay */
.call-controls__loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  border-radius: 12px;
}

.call-controls__loading .spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #e9ecef;
  border-top: 2px solid #0984e3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.call-controls__loading span {
  font-size: 14px;
  color: #636e72;
}

/* Animations */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
  .call-controls__primary {
    gap: 12px;
  }
  
  .call-control {
    min-width: 70px;
    padding: 10px;
  }
  
  .call-control__label {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .call-controls {
    padding: 16px;
  }
  
  .call-controls__primary {
    gap: 8px;
  }
  
  .call-control {
    min-width: 60px;
    padding: 8px;
  }
  
  .call-control__label {
    font-size: 10px;
  }
  
  .call-control--more {
    display: none; /* Hide advanced options on very small screens */
  }
}
