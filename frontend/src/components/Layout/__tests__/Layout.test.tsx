import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import authSlice from '../../../store/authSlice';

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useLocation: () => ({ pathname: '/support' }),
}));

// Simple placeholder Layout component for testing
interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <div>
      <div data-testid="header">
        <button data-testid="menu-toggle">Menu Toggle</button>
      </div>

      <nav role="navigation">
        <div>Support</div>
        <div onClick={() => mockNavigate('/chat')}>Chat</div>
        <div>Dashboard Admin</div>
        <div>Conversations</div>
        <div>Chat Agent</div>
        <div>Analytics</div>
      </nav>

      <main>
        {children}
      </main>
    </div>
  );
};

const theme = createTheme();

// Create a mock store
const createMockStore = () => {
  return configureStore({
    reducer: {
      auth: authSlice,
    },
  });
};

const renderWithProviders = (component: React.ReactElement) => {
  const store = createMockStore();

  return render(
    <Provider store={store}>
      <BrowserRouter>
        <ThemeProvider theme={theme}>
          {component}
        </ThemeProvider>
      </BrowserRouter>
    </Provider>
  );
};

describe('Layout', () => {
  beforeEach(() => {
    mockNavigate.mockClear();
  });

  describe('Rendering', () => {
    it('renders with children', () => {
      renderWithProviders(
        <Layout>
          <div data-testid="test-content">Test Content</div>
        </Layout>
      );

      expect(screen.getByTestId('header')).toBeInTheDocument();
      expect(screen.getByTestId('test-content')).toBeInTheDocument();
    });

    it('renders navigation drawer', () => {
      renderWithProviders(
        <Layout>
          <div>Content</div>
        </Layout>
      );

      expect(screen.getByRole('navigation')).toBeInTheDocument();
    });
  });

  describe('Navigation Items', () => {
    it('shows navigation menu items', () => {
      renderWithProviders(
        <Layout>
          <div>Content</div>
        </Layout>
      );

      expect(screen.getByText('Support')).toBeInTheDocument();
      expect(screen.getByText('Chat')).toBeInTheDocument();
      expect(screen.getByText('Dashboard Admin')).toBeInTheDocument();
    });
  });

  describe('Navigation Interactions', () => {
    it('navigates when menu item is clicked', () => {
      renderWithProviders(
        <Layout>
          <div>Content</div>
        </Layout>
      );

      const chatMenuItem = screen.getByText('Chat');
      fireEvent.click(chatMenuItem);

      expect(mockNavigate).toHaveBeenCalledWith('/chat');
    });

    it('handles drawer toggle', () => {
      renderWithProviders(
        <Layout>
          <div>Content</div>
        </Layout>
      );

      const menuToggle = screen.getByTestId('menu-toggle');
      fireEvent.click(menuToggle);

      expect(menuToggle).toBeInTheDocument();
    });
  });

  describe('Layout Structure', () => {
    it('has proper layout structure', () => {
      renderWithProviders(
        <Layout>
          <div data-testid="main-content">Main Content</div>
        </Layout>
      );

      expect(screen.getByTestId('main-content')).toBeInTheDocument();
    });

    it('renders header and navigation', () => {
      renderWithProviders(
        <Layout>
          <div data-testid="content">Content</div>
        </Layout>
      );

      expect(screen.getByTestId('header')).toBeInTheDocument();
      expect(screen.getByRole('navigation')).toBeInTheDocument();
      expect(screen.getByTestId('content')).toBeInTheDocument();
    });
  });
});