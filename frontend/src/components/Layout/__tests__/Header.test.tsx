import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import authSlice from '../../../store/authSlice';
import uiSlice from '../../../store/slices/uiSlice';

// Simple placeholder Header component for testing
interface HeaderProps {
  onMenuClick: () => void;
}

const Header: React.FC<HeaderProps> = ({ onMenuClick }) => {
  return (
    <header role="banner">
      <button aria-label="open drawer" onClick={onMenuClick}>
        Menu
      </button>

      <h1>Free Mobile - Service Client</h1>

      <button aria-label="notifications">
        <span>Notifications</span>
        <span>1</span>
      </button>

      <button aria-label="account of current user">
        <span>TU</span>
      </button>

      <button aria-label="Mode sombre">
        <span data-testid="DarkModeIcon">Dark Mode</span>
      </button>
    </header>
  );
};

const theme = createTheme();

const renderWithProviders = (component: React.ReactElement) => {
  const store = configureStore({
    reducer: {
      auth: authSlice,
      ui: uiSlice,
    },
    preloadedState: {
      auth: {
        user: {
          id: '1',
          email: '<EMAIL>',
          name: 'Test User',
          role: 'user',
        },
        token: 'mock-token',
        isAuthenticated: true,
        loading: false,
        error: null,
      },
      ui: {
        theme: 'light',
        sidebarOpen: false,
        loading: false,
        notifications: [
          {
            id: '1',
            message: 'Test notification',
            read: false,
            timestamp: new Date().toISOString()
          },
        ],
        modal: { open: false },
      },
    },
  });

  return render(
    <Provider store={store}>
      <BrowserRouter>
        <ThemeProvider theme={theme}>
          {component}
        </ThemeProvider>
      </BrowserRouter>
    </Provider>
  );
};

describe('Header', () => {
  const mockOnMenuClick = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders the header with Free Mobile branding', () => {
      renderWithProviders(<Header onMenuClick={mockOnMenuClick} />);

      expect(screen.getByText('Free Mobile - Service Client')).toBeInTheDocument();
      expect(screen.getByRole('banner')).toBeInTheDocument();
    });

    it('renders menu button and calls onMenuClick when clicked', () => {
      renderWithProviders(<Header onMenuClick={mockOnMenuClick} />);

      const menuButton = screen.getByLabelText(/open drawer/i);
      expect(menuButton).toBeInTheDocument();

      fireEvent.click(menuButton);
      expect(mockOnMenuClick).toHaveBeenCalledTimes(1);
    });

    it('renders user avatar with initials', () => {
      renderWithProviders(<Header onMenuClick={mockOnMenuClick} />);

      expect(screen.getByText('TU')).toBeInTheDocument();
      const avatar = screen.getByRole('button', { name: /account of current user/i });
      expect(avatar).toBeInTheDocument();
    });

    it('renders notifications badge', () => {
      renderWithProviders(<Header onMenuClick={mockOnMenuClick} />);

      const notificationButton = screen.getByLabelText(/notifications/i);
      expect(notificationButton).toBeInTheDocument();

      const badge = screen.getByText('1');
      expect(badge).toBeInTheDocument();
    });
  });

  describe('Theme Toggle', () => {
    it('shows theme toggle button', () => {
      renderWithProviders(<Header onMenuClick={mockOnMenuClick} />);

      expect(screen.getByTestId('DarkModeIcon')).toBeInTheDocument();
    });

    it('renders theme button with proper label', () => {
      renderWithProviders(<Header onMenuClick={mockOnMenuClick} />);

      const themeButton = screen.getByLabelText(/Mode sombre/i);
      expect(themeButton).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels for interactive elements', () => {
      renderWithProviders(<Header onMenuClick={mockOnMenuClick} />);

      expect(screen.getByLabelText(/open drawer/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/notifications/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /account of current user/i })).toBeInTheDocument();
    });

    it('has proper semantic structure', () => {
      renderWithProviders(<Header onMenuClick={mockOnMenuClick} />);

      expect(screen.getByRole('banner')).toBeInTheDocument();
      expect(screen.getByLabelText(/open drawer/i)).toBeInTheDocument();
    });
  });
});