import React, { useState } from 'react'; import { <PERSON><PERSON>B<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>graphy, IconButton, Avatar, Menu, MenuItem, Box, Badge, Tooltip, Divider, ListItemIcon, ListItemText, } from '@mui/material'; import { Menu as MenuIcon, AccountCircle, Notifications, Settings, Logout, DarkMode, LightMode, Person, } from '@mui/icons-material'; import { useAuth } from '../../hooks/useAuth'; import { useSelector, useDispatch } from 'react-redux'; import { RootState } from '../../store'; import { toggleTheme } from '../../store/slices/uiSlice'; import { FREE_MOBILE_COLORS } from '../../utils/constants'; interface HeaderProps { onMenuClick: () => void; } const Header: React.FC<HeaderProps> = ({ onMenuClick }) => { const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null); const [notificationsAnchor, setNotificationsAnchor] = useState<null | HTMLElement>(null); const { user, logout } = useAuth(); const dispatch = useDispatch(); const { theme, notifications } = useSelector((state: RootState) => state.ui); const isMenuOpen = Boolean(anchorEl); const isNotificationsOpen = Boolean(notificationsAnchor); const handleProfileMenuOpen = (event: React.MouseEvent<HTMLElement>) => { setAnchorEl(event.currentTarget); }; const handleNotificationsOpen = (event: React.MouseEvent<HTMLElement>) => { setNotificationsAnchor(event.currentTarget); }; const handleMenuClose = () => { setAnchorEl(null); }; const handleNotificationsClose = () => { setNotificationsAnchor(null); }; const handleLogout = () => { handleMenuClose(); logout(); }; const handleThemeToggle = () => { dispatch(toggleTheme()); }; const unreadNotifications = notifications.filter(n => !(n as any).read).length; const getUserInitials = (user: any) => { if (!user?.profile) return 'U'; const firstName = user.profile.firstName || ''; const lastName = user.profile.lastName || ''; return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase(); }; const getUserDisplayName = (user: any) => { if (!user?.profile) return user?.email || 'Utilisateur'; const firstName = user.profile.firstName || ''; const lastName = user.profile.lastName || ''; return `${firstName} ${lastName}`.trim() || user.email; }; const getRoleDisplayName = (role: string) => { switch (role) { case 'admin': return 'Administrateur'; case 'agent': return 'Agent SAV'; case 'user': return 'Client'; default: return role; } }; return ( <> <AppBar position="fixed" sx={{ zIndex: (theme) => theme.zIndex.drawer + 1, bgcolor: FREE_MOBILE_COLORS.PRIMARY, }} > <Toolbar> <IconButton color="inherit" aria-label="open drawer" onClick={onMenuClick} edge="start" sx={{ mr: 2 }} > <MenuIcon /> </IconButton> <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}> Free Mobile - Service Client </Typography> <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}> {/* Theme Toggle */} <Tooltip title={`Mode ${theme === 'light' ? 'sombre' : 'clair'}`}> <IconButton color="inherit" onClick={handleThemeToggle}> {theme === 'light' ? <DarkMode /> : <LightMode />} </IconButton> </Tooltip> {/* Notifications */} <Tooltip title="Notifications"> <IconButton color="inherit" onClick={handleNotificationsOpen}> <Badge badgeContent={unreadNotifications} color="error"> <Notifications /> </Badge> </IconButton> </Tooltip> {/* User Profile */} <Tooltip title="Profil utilisateur"> <IconButton size="large" edge="end" aria-label="account of current user" aria-controls="primary-search-account-menu" aria-haspopup="true" onClick={handleProfileMenuOpen} color="inherit" > <Avatar sx={{ bgcolor: 'rgba(255, 255, 255, 0.2)', width: 32, height: 32, fontSize: '0.875rem', }} > {getUserInitials(user)} </Avatar> </IconButton> </Tooltip> </Box> </Toolbar> </AppBar> {/* Profile Menu */} <Menu anchorEl={anchorEl} anchorOrigin={{ vertical: 'bottom', horizontal: 'right', }} keepMounted transformOrigin={{ vertical: 'top', horizontal: 'right', }} open={isMenuOpen} onClose={handleMenuClose} PaperProps={{ sx: { mt: 1, minWidth: 250, }, }} > <Box sx={{ px: 2, py: 1 }}> <Typography variant="subtitle1" fontWeight="bold"> {getUserDisplayName(user)} </Typography> <Typography variant="body2" color="text.secondary"> {user?.email} </Typography> <Typography variant="caption" color="text.secondary"> {getRoleDisplayName(user?.role || 'user')} </Typography> </Box> <Divider /> <MenuItem onClick={handleMenuClose}> <ListItemIcon> <Person fontSize="small" /> </ListItemIcon> <ListItemText>Mon profil</ListItemText> </MenuItem> <MenuItem onClick={handleMenuClose}> <ListItemIcon> <Settings fontSize="small" /> </ListItemIcon> <ListItemText>Paramètres</ListItemText> </MenuItem> <Divider /> <MenuItem onClick={handleLogout}> <ListItemIcon> <Logout fontSize="small" /> </ListItemIcon> <ListItemText>Déconnexion</ListItemText> </MenuItem> </Menu> {/* Notifications Menu */} <Menu anchorEl={notificationsAnchor} anchorOrigin={{ vertical: 'bottom', horizontal: 'right', }} keepMounted transformOrigin={{ vertical: 'top', horizontal: 'right', }} open={isNotificationsOpen} onClose={handleNotificationsClose} PaperProps={{ sx: { mt: 1, maxWidth: 350, maxHeight: 400, }, }} > <Box sx={{ px: 2, py: 1 }}> <Typography variant="h6"> Notifications </Typography> </Box> <Divider /> {notifications.length === 0 ? ( <MenuItem> <Typography variant="body2" color="text.secondary"> Aucune notification </Typography> </MenuItem> ) : ( notifications.slice(0, 5).map((notification) => ( <MenuItem key={notification.id} onClick={handleNotificationsClose}> <Box> <Typography variant="body2" fontWeight={(notification as any).read ? 'normal' : 'bold'}> {(notification as any).title || notification.message} </Typography> <Typography variant="caption" color="text.secondary"> {notification.message} </Typography> </Box> </MenuItem> )) )} {notifications.length > 5 && ( <> <Divider /> <MenuItem onClick={handleNotificationsClose}> <Typography variant="body2" color="primary" textAlign="center" width="100%"> Voir toutes les notifications </Typography> </MenuItem> </> )} </Menu> </> ); }; export default Header;