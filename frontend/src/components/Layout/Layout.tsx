import React, { useState } from 'react'; import { Box, Drawer, List, ListItem, ListItemButton, ListItemIcon, ListItemText, Toolbar, Divider, useTheme, useMediaQuery } from '@mui/material'; import { Dashboard, Chat, Support, Analytics, People, SupportAgent, Home, } from '@mui/icons-material'; import { useNavigate, useLocation } from 'react-router-dom'; import Header from './Header'; import { useAuth } from '../../hooks/useAuth'; import { ROUTES } from '../../utils/constants'; interface LayoutProps { children: React.ReactNode; } const drawerWidth = 240; const Layout: React.FC<LayoutProps> = ({ children }) => { const [mobileOpen, setMobileOpen] = useState(false); const navigate = useNavigate(); const location = useLocation(); const { user } = useAuth(); const theme = useTheme(); const isMobile = useMediaQuery(theme.breakpoints.down('md')); const handleDrawerToggle = () => { setMobileOpen(!mobileOpen); }; const menuItems = [ { text: 'Support', icon: <Support />, path: ROUTES.SUPPORT, roles: ['user', 'agent', 'admin'], }, { text: 'Chat', icon: <Chat />, path: ROUTES.CHAT, roles: ['user', 'agent', 'admin'], }, ...(user && ['agent', 'admin'].includes(user.role) ? [ { text: 'Dashboard Admin', icon: <Dashboard />, path: ROUTES.ADMIN_DASHBOARD, roles: ['agent', 'admin'], }, { text: 'Conversations', icon: <People />, path: ROUTES.ADMIN_CONVERSATIONS, roles: ['agent', 'admin'], }, { text: 'Chat Agent', icon: <SupportAgent />, path: ROUTES.AGENT_CHAT, roles: ['agent', 'admin'], }, ] : []), ...(user && user.role === 'admin' ? [ { text: 'Gestion Client', icon: <People />, path: '/dashboard/client-management', roles: ['admin'], }, { text: 'Analytics', icon: <Analytics />, path: ROUTES.ADMIN_ANALYTICS, roles: ['admin'], }, ] : []), ]; const filteredMenuItems = menuItems.filter(item => user && item.roles.includes(user.role) ); const drawer = ( <div> <Toolbar /> <Divider /> <List> {filteredMenuItems.map((item) => ( <ListItem key={item.text} disablePadding> <ListItemButton selected={location.pathname === item.path} onClick={() => navigate(item.path)} > <ListItemIcon>{item.icon}</ListItemIcon> <ListItemText primary={item.text} /> </ListItemButton> </ListItem> ))} </List> </div> ); return ( <Box sx={{ display: 'flex', minHeight: '100vh' }}> <Header onMenuClick={handleDrawerToggle} /> {/* Sidebar Navigation */} <Box component="nav" sx={{ width: { md: drawerWidth }, flexShrink: { md: 0 } }} > <Drawer variant={isMobile ? 'temporary' : 'permanent'} open={isMobile ? mobileOpen : true} onClose={handleDrawerToggle} ModalProps={{ keepMounted: true, // Better open performance on mobile. }} sx={{ '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth, }, }} > {drawer} </Drawer> </Box> {/* Main Content */} <Box component="main" sx={{ flexGrow: 1, width: { md: `calc(100% - ${drawerWidth}px)` }, minHeight: '100vh', bgcolor: 'background.default', }} > <Toolbar /> <Box sx={{ p: 0 }}> {children} </Box> </Box> </Box> ); }; export default Layout;