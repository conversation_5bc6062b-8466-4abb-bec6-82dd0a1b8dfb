/** * ============================================= * AI SETTINGS PANEL COMPONENT * AI configuration and learning settings * ============================================= */ import React from 'react'; import { Box, Typography, Card, CardContent, Grid, Slider, Switch, FormControlLabel, Select, MenuItem, FormControl, InputLabel } from '@mui/material'; import { Tune as SettingsIcon, School as LearningIcon, Assessment as MetricsIcon } from '@mui/icons-material'; interface AISettingsPanelProps { settings: any; learningData: any; suggestionPerformance: any; onSettingsChange: (setting: string, value: any) => void; } const AISettingsPanel: React.FC<AISettingsPanelProps> = ({ settings, learningData, suggestionPerformance, onSettingsChange }) => { return ( <Box> <Typography variant="h6" sx={{ mb: 3 }}> Configuration et Personnalisation IA </Typography> <Grid container spacing={3}> {/* AI Settings */} <Grid item xs={12} md={6}> <Card> <CardContent> <Typography variant="h6" sx={{ mb: 2 }}> <SettingsIcon sx={{ mr: 1, verticalAlign: 'middle' }} /> Paramètres IA </Typography> <Box sx={{ mb: 3 }}> <Typography variant="body2" sx={{ mb: 2 }}> Seuil de Confiance: {Math.round(settings.confidence_threshold * 100)}% </Typography> <Slider value={settings.confidence_threshold} onChange={(e, value) => onSettingsChange('confidence_threshold', value)} min={0.3} max={1.0} step={0.1} marks valueLabelDisplay="auto" valueLabelFormat={(value) => `${Math.round(value * 100)}%`} /> </Box> <Box sx={{ mb: 3 }}> <Typography variant="body2" sx={{ mb: 2 }}> Nombre Max de Suggestions: {settings.max_suggestions} </Typography> <Slider value={settings.max_suggestions} onChange={(e, value) => onSettingsChange('max_suggestions', value)} min={1} max={10} step={1} marks valueLabelDisplay="auto" /> </Box> <FormControlLabel control={ <Switch checked={settings.include_alternatives} onChange={(e) => onSettingsChange('include_alternatives', e.target.checked)} /> } label="Inclure les Alternatives" sx={{ mb: 2 }} /> <FormControl fullWidth sx={{ mb: 2 }}> <InputLabel>Niveau de Personnalisation</InputLabel> <Select value={settings.personalization_level} label="Niveau de Personnalisation" onChange={(e) => onSettingsChange('personalization_level', e.target.value)} > <MenuItem value="basic">Basique</MenuItem> <MenuItem value="advanced">Avancé</MenuItem> <MenuItem value="expert">Expert</MenuItem> </Select> </FormControl> <FormControl fullWidth> <InputLabel>Mode d'Apprentissage</InputLabel> <Select value={settings.learning_mode} label="Mode d'Apprentissage" onChange={(e) => onSettingsChange('learning_mode', e.target.value)} > <MenuItem value="passive">Passif</MenuItem> <MenuItem value="active">Actif</MenuItem> <MenuItem value="aggressive">Agressif</MenuItem> </Select> </FormControl> </CardContent> </Card> </Grid> {/* Learning Data */} <Grid item xs={12} md={6}> <Card> <CardContent> <Typography variant="h6" sx={{ mb: 2 }}> <LearningIcon sx={{ mr: 1, verticalAlign: 'middle' }} /> Données d'Apprentissage </Typography> <Box sx={{ mb: 2 }}> <Typography variant="body2" color="text.secondary"> Réponses Réussies </Typography> <Typography variant="h6"> {learningData.successful_responses?.length || 0} </Typography> </Box> <Box sx={{ mb: 2 }}> <Typography variant="body2" color="text.secondary"> Points Forts </Typography> <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 1 }}> {(learningData.strengths || ['Communication', 'Empathie']).map((strength: string, index: number) => ( <Typography key={index} variant="caption" sx={{ bgcolor: 'success.light', color: 'success.contrastText', px: 1, py: 0.5, borderRadius: 1 }}> {strength} </Typography> ))} </Box> </Box> <Box> <Typography variant="body2" color="text.secondary"> Axes d'Amélioration </Typography> <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 1 }}> {(learningData.improvement_areas || ['Efficacité', 'Technique']).map((area: string, index: number) => ( <Typography key={index} variant="caption" sx={{ bgcolor: 'warning.light', color: 'warning.contrastText', px: 1, py: 0.5, borderRadius: 1 }}> {area} </Typography> ))} </Box> </Box> </CardContent> </Card> </Grid> {/* Performance Metrics */} <Grid item xs={12}> <Card> <CardContent> <Typography variant="h6" sx={{ mb: 2 }}> <MetricsIcon sx={{ mr: 1, verticalAlign: 'middle' }} /> Performance des Suggestions </Typography> <Grid container spacing={2}> <Grid item xs={6} md={3}> <Typography variant="body2" color="text.secondary"> Taux d'Utilisation </Typography> <Typography variant="h6" color="primary"> 85% </Typography> </Grid> <Grid item xs={6} md={3}> <Typography variant="body2" color="text.secondary"> Taux de Succès </Typography> <Typography variant="h6" color="success.main"> 92% </Typography> </Grid> <Grid item xs={6} md={3}> <Typography variant="body2" color="text.secondary"> Satisfaction Moyenne </Typography> <Typography variant="h6" color="info.main"> 4.3/5 </Typography> </Grid> <Grid item xs={6} md={3}> <Typography variant="body2" color="text.secondary"> Temps de Réponse </Typography> <Typography variant="h6" color="warning.main"> 1.2s </Typography> </Grid> </Grid> </CardContent> </Card> </Grid> </Grid> </Box> ); }; export default AISettingsPanel;