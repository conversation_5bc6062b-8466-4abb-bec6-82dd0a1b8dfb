/** * ============================================= * ESCALATION ALERT COMPONENT * Escalation recommendations and alerts * ============================================= */ import React from 'react'; import { Alert, AlertTitle, Box, Typography, Chip, Button, List, ListItem, ListItemText } from '@mui/material'; import { Warning as WarningIcon, ArrowUpward as EscalateIcon } from '@mui/icons-material'; interface EscalationAlertProps { recommendation: any; onEscalate?: () => void; } const EscalationAlert: React.FC<EscalationAlertProps> = ({ recommendation, onEscalate }) => { if (!recommendation?.should_escalate) { return null; } const getSeverityColor = (urgency: string) => { switch (urgency) { case 'immediate': return 'error'; case 'soon': return 'warning'; case 'monitor': return 'info'; default: return 'warning'; } }; return ( <Alert severity={getSeverityColor(recommendation.urgency)} icon={<WarningIcon />} action={ onEscalate && ( <Button color="inherit" size="small" startIcon={<EscalateIcon />} onClick={onEscalate} > Escalader </Button> ) } > <AlertTitle>Recommandation d'Escalade</AlertTitle> <Box sx={{ mb: 2 }}> <Typography variant="body2" sx={{ mb: 1 }}> {recommendation.reasoning?.[0] || 'Escalade recommandée'} </Typography> <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}> <Chip label={`Urgence: ${recommendation.urgency}`} color={getSeverityColor(recommendation.urgency)} size="small" /> <Chip label={`Type: ${recommendation.escalation_type}`} variant="outlined" size="small" /> <Chip label={`Confiance: ${Math.round(recommendation.confidence * 100)}%`} variant="outlined" size="small" /> </Box> </Box> {recommendation.preparation_steps && recommendation.preparation_steps.length > 0 && ( <Box> <Typography variant="subtitle2" sx={{ mb: 1 }}> Étapes de préparation: </Typography> <List dense> {recommendation.preparation_steps.slice(0, 3).map((step: string, index: number) => ( <ListItem key={index} sx={{ py: 0 }}> <ListItemText primary={`• ${step}`} /> </ListItem> ))} </List> </Box> )} </Alert> ); }; export default EscalationAlert;