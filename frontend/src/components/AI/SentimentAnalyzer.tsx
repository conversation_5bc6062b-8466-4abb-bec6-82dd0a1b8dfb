/** * ============================================= * SENTIMENT ANALYZER COMPONENT * Real-time sentiment analysis and trends * ============================================= */ import React from 'react'; import { Box, Typography, Card, CardContent, Grid, LinearProgress, Chip, Alert } from '@mui/material'; import { SentimentSatisfied as PositiveIcon, SentimentNeutral as NeutralIcon, SentimentDissatisfied as NegativeIcon, TrendingUp as TrendIcon } from '@mui/icons-material'; interface SentimentAnalyzerProps { currentSentiment: any; sentimentHistory: any[]; escalationRecommendation: any; } const SentimentAnalyzer: React.FC<SentimentAnalyzerProps> = ({ currentSentiment, sentimentHistory, escalationRecommendation }) => { const getSentimentIcon = (sentiment: number) => { if (sentiment > 0.3) return <PositiveIcon color="success" />; if (sentiment < -0.3) return <NegativeIcon color="error" />; return <NeutralIcon color="warning" />; }; const getSentimentLabel = (sentiment: number) => { if (sentiment > 0.3) return 'Positif'; if (sentiment < -0.3) return 'Négatif'; return 'Neutre'; }; const getSentimentColor = (sentiment: number) => { if (sentiment > 0.3) return 'success'; if (sentiment < -0.3) return 'error'; return 'warning'; }; return ( <Box> <Typography variant="h6" sx={{ mb: 3 }}> Analyse de Sentiment en Temps Réel </Typography> {escalationRecommendation?.should_escalate && ( <Alert severity="warning" sx={{ mb: 3 }}> <Typography variant="body1"> <strong>Recommandation d'escalade:</strong> Le sentiment client nécessite une attention particulière </Typography> </Alert> )} <Grid container spacing={3}> {/* Current Sentiment */} <Grid item xs={12} md={6}> <Card> <CardContent> <Typography variant="h6" sx={{ mb: 2 }}> Sentiment Actuel </Typography> {currentSentiment ? ( <Box> <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}> {getSentimentIcon(currentSentiment.current_sentiment)} <Typography variant="h4" color={getSentimentColor(currentSentiment.current_sentiment) + '.main'}> {getSentimentLabel(currentSentiment.current_sentiment)} </Typography> </Box> <Box sx={{ mb: 2 }}> <Typography variant="body2" sx={{ mb: 1 }}> Score: {Math.round((currentSentiment.current_sentiment + 1) * 50)}% </Typography> <LinearProgress variant="determinate" value={(currentSentiment.current_sentiment + 1) * 50} color={getSentimentColor(currentSentiment.current_sentiment)} sx={{ height: 8, borderRadius: 4 }} /> </Box> <Typography variant="subtitle2" sx={{ mb: 1 }}> Approche Recommandée: </Typography> <Chip label={currentSentiment.recommended_approach} color={getSentimentColor(currentSentiment.current_sentiment)} sx={{ textTransform: 'capitalize' }} /> </Box> ) : ( <Typography variant="body2" color="text.secondary"> Aucune donnée de sentiment disponible </Typography> )} </CardContent> </Card> </Grid> {/* Emotion Breakdown */} <Grid item xs={12} md={6}> <Card> <CardContent> <Typography variant="h6" sx={{ mb: 2 }}> Analyse Émotionnelle </Typography> {currentSentiment?.emotion_breakdown ? ( Object.entries(currentSentiment.emotion_breakdown).map(([emotion, level]) => ( <Box key={emotion} sx={{ mb: 2 }}> <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}> <Typography variant="body2" sx={{ textTransform: 'capitalize' }}> {emotion} </Typography> <Typography variant="body2"> {Math.round((level as number) * 100)}% </Typography> </Box> <LinearProgress variant="determinate" value={(level as number) * 100} sx={{ height: 6, borderRadius: 3 }} /> </Box> )) ) : ( <Typography variant="body2" color="text.secondary"> Aucune analyse émotionnelle disponible </Typography> )} </CardContent> </Card> </Grid> {/* Sentiment Trend */} <Grid item xs={12}> <Card> <CardContent> <Typography variant="h6" sx={{ mb: 2 }}> <TrendIcon sx={{ mr: 1, verticalAlign: 'middle' }} /> Évolution du Sentiment </Typography> <Box sx={{ height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center' }}> <Typography variant="body2" color="text.secondary"> Graphique d'évolution du sentiment (à implémenter avec Chart.js) </Typography> </Box> </CardContent> </Card> </Grid> </Grid> </Box> ); }; export default SentimentAnalyzer;