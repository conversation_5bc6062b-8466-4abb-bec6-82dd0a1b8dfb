/** * ============================================= * [FEATURE] SUGGESTION CARD COMPONENT * Individual AI suggestion display with feedback * ============================================= */ import React, { useState } from 'react'; import { Card, CardContent, Typography, Box, Chip, Button, IconButton, Collapse, Rating, TextField, Divider } from '@mui/material'; import { ThumbUp as ThumbUpIcon, ThumbDown as ThumbDownIcon, ExpandMore as ExpandIcon, Psychology as AIIcon, ContentCopy as CopyIcon, Edit as EditIcon } from '@mui/icons-material'; import { ContextualSuggestion } from '../../store/slices/enhancedAISlice'; interface SuggestionCardProps { suggestion: ContextualSuggestion; onFeedback: (suggestionId: string, helpful: boolean, used: boolean) => void; } const SuggestionCard: React.FC<SuggestionCardProps> = ({ suggestion, onFeedback }) => { const [expanded, setExpanded] = useState(false); const [showFeedback, setShowFeedback] = useState(false); const [rating, setRating] = useState<number | null>(null); const [feedbackText, setFeedbackText] = useState(''); const getTypeColor = (type: string) => { switch (type) { case 'response': return 'primary'; case 'action': return 'secondary'; case 'escalation': return 'error'; case 'information': return 'info'; case 'template': return 'success'; default: return 'default'; } }; const getTypeIcon = (type: string) => { switch (type) { case 'response': return ''; case 'action': return '[PERFORMANCE]'; case 'escalation': return ''; case 'information': return 'ℹ'; case 'template': return ''; default: return '[FEATURE]'; } }; const getConfidenceColor = (confidence: number) => { if (confidence >= 0.8) return 'success'; if (confidence >= 0.6) return 'warning'; return 'error'; }; const handleCopy = () => { navigator.clipboard.writeText(suggestion.content); }; const handleUse = () => { onFeedback(suggestion.id, true, true); }; const handleFeedbackSubmit = () => { onFeedback(suggestion.id, rating ? rating >= 3 : false, false); setShowFeedback(false); }; return ( <Card sx={{ mb: 2, border: suggestion.used ? '2px solid' : '1px solid', borderColor: suggestion.used ? 'success.main' : 'divider', opacity: suggestion.used ? 0.8 : 1 }} > <CardContent> {/* Header */} <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}> <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}> <Typography variant="h6" component="span"> {getTypeIcon(suggestion.type)} </Typography> <Chip label={suggestion.type} color={getTypeColor(suggestion.type)} size="small" /> <Chip label={`${Math.round(suggestion.confidence * 100)}%`} color={getConfidenceColor(suggestion.confidence)} size="small" variant="outlined" /> </Box> <Box sx={{ display: 'flex', gap: 0.5 }}> <IconButton size="small" onClick={handleCopy}> <CopyIcon fontSize="small" /> </IconButton> <IconButton size="small" onClick={() => setExpanded(!expanded)} sx={{ transform: expanded ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'transform 0.3s' }} > <ExpandIcon fontSize="small" /> </IconButton> </Box> </Box> {/* Content */} <Typography variant="body1" sx={{ mb: 2 }}> {suggestion.content} </Typography> {/* Alternatives */} {suggestion.alternatives && suggestion.alternatives.length > 0 && ( <Box sx={{ mb: 2 }}> <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 1 }}> Alternatives: </Typography> {suggestion.alternatives.map((alt, index) => ( <Chip key={index} label={alt} variant="outlined" size="small" sx={{ mr: 0.5, mb: 0.5 }} /> ))} </Box> )} {/* Actions */} <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}> <Button variant="contained" size="small" onClick={handleUse} disabled={suggestion.used} startIcon={<EditIcon />} > {suggestion.used ? 'Utilisé' : 'Utiliser'} </Button> <Button variant="outlined" size="small" color="success" onClick={() => onFeedback(suggestion.id, true, false)} startIcon={<ThumbUpIcon />} > Utile </Button> <Button variant="outlined" size="small" color="error" onClick={() => setShowFeedback(true)} startIcon={<ThumbDownIcon />} > Pas utile </Button> </Box> {/* Expanded Details */} <Collapse in={expanded}> <Divider sx={{ my: 2 }} /> <Typography variant="subtitle2" sx={{ mb: 1 }}> Raisonnement IA: </Typography> <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}> {suggestion.reasoning} </Typography> {suggestion.context && ( <> <Typography variant="subtitle2" sx={{ mb: 1 }}> Contexte: </Typography> <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 2 }}> <Chip label={`Sentiment: ${suggestion.context.current_sentiment}`} size="small" /> <Chip label={`Urgence: ${suggestion.context.urgency_level}`} size="small" /> <Chip label={`Plateforme: ${suggestion.context.platform}`} size="small" /> </Box> </> )} {suggestion.personalization && ( <> <Typography variant="subtitle2" sx={{ mb: 1 }}> Personnalisation: </Typography> <Typography variant="body2" color="text.secondary"> Score d'adaptation: {Math.round(suggestion.personalization.adaptation_score * 100)}% </Typography> </> )} </Collapse> {/* Feedback Form */} <Collapse in={showFeedback}> <Divider sx={{ my: 2 }} /> <Typography variant="subtitle2" sx={{ mb: 1 }}> Évaluez cette suggestion: </Typography> <Box sx={{ mb: 2 }}> <Rating value={rating} onChange={(event, newValue) => setRating(newValue)} /> </Box> <TextField fullWidth multiline rows={2} placeholder="Commentaire optionnel..." value={feedbackText} onChange={(e) => setFeedbackText(e.target.value)} sx={{ mb: 2 }} /> <Box sx={{ display: 'flex', gap: 1 }}> <Button variant="contained" size="small" onClick={handleFeedbackSubmit} > Envoyer </Button> <Button variant="outlined" size="small" onClick={() => setShowFeedback(false)} > Annuler </Button> </Box> </Collapse> </CardContent> </Card> ); }; export default SuggestionCard;