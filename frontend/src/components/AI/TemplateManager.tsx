/** * ============================================= * TEMPLATE MANAGER COMPONENT * Response template management and personalization * ============================================= */ import React from 'react'; import { Box, Typography, Card, CardContent, Grid, List, ListItem, ListItemText, Chip, Button } from '@mui/material'; import { Description as TemplateIcon, PersonalVideo as PersonalizeIcon, Add as AddIcon } from '@mui/icons-material'; interface TemplateManagerProps { templates: any[]; personalizedTemplates: any[]; categories: string[]; } const TemplateManager: React.FC<TemplateManagerProps> = ({ templates, personalizedTemplates, categories }) => { return ( <Box> <Typography variant="h6" sx={{ mb: 3 }}> Gestion des Templates de Réponse </Typography> <Grid container spacing={3}> <Grid item xs={12} md={6}> <Card> <CardContent> <Typography variant="h6" sx={{ mb: 2 }}> <TemplateIcon sx={{ mr: 1, verticalAlign: 'middle' }} /> Templates Standard ({templates.length}) </Typography> <List> {[1, 2, 3, 4, 5].map((index) => ( <ListItem key={index}> <ListItemText primary={`Template ${index}`} secondary={`Catégorie: Général • Utilisé ${index * 10} fois`} /> <Chip label="Actif" color="success" size="small" /> </ListItem> ))} </List> <Button variant="outlined" startIcon={<AddIcon />} fullWidth sx={{ mt: 2 }} > Nouveau Template </Button> </CardContent> </Card> </Grid> <Grid item xs={12} md={6}> <Card> <CardContent> <Typography variant="h6" sx={{ mb: 2 }}> <PersonalizeIcon sx={{ mr: 1, verticalAlign: 'middle' }} /> Templates Personnalisés ({personalizedTemplates.length}) </Typography> <List> {[1, 2, 3].map((index) => ( <ListItem key={index}> <ListItemText primary={`Template Personnalisé ${index}`} secondary={`Performance: ${90 - index * 5}% • Modifié récemment`} /> <Chip label="Personnel" color="primary" size="small" /> </ListItem> ))} </List> <Button variant="outlined" startIcon={<PersonalizeIcon />} fullWidth sx={{ mt: 2 }} > Personnaliser Template </Button> </CardContent> </Card> </Grid> </Grid> </Box> ); }; export default TemplateManager;