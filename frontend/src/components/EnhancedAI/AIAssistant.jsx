/**
 * =============================================
 * [FEATURE] ENHANCED AI ASSISTANT COMPONENT
 * Real-time AI suggestions and sentiment analysis
 * Contextual assistance for customer service agents
 * =============================================
 */
import React, { useState } from 'react';
import {
  Box,
  Card,
  Typography,
  Button,
  IconButton,
  Tooltip,
  Fab,
  Badge,
} from '@mui/material';
import {
  AutoAwesome as AutoAwesomeIcon,
  Close as CloseIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
} from '@mui/icons-material';

const AIAssistant = ({
  conversationId,
  currentMessage,
  conversationHistory = [],
  customerProfile = {},
  onSuggestionSelect,
  minimized = false,
  onToggleMinimize
}) => {
  const [expanded, setExpanded] = useState(!minimized);

  if (minimized) {
    return (
      <Fab
        color="primary"
        onClick={onToggleMinimize}
        sx={{
          position: 'fixed',
          bottom: 16,
          right: 16,
          zIndex: 1000
        }}
      >
        <Badge badgeContent={0} color="error" invisible={true}>
          <AutoAwesomeIcon />
        </Badge>
      </Fab>
    );
  }

  return (
    <Card
      sx={{
        position: 'fixed',
        bottom: 16,
        right: 16,
        width: 400,
        maxHeight: '80vh',
        zIndex: 1000,
        overflow: 'hidden'
      }}
      elevation={8}
    >
      {/* Header */}
      <Box
        sx={{
          p: 2,
          bgcolor: 'primary.main',
          color: 'white',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <AutoAwesomeIcon />
          <Typography variant="h6">AI Assistant</Typography>
        </Box>
        <Box>
          <Tooltip title={expanded ? "Minimize" : "Expand"}>
            <IconButton
              color="inherit"
              size="small"
              onClick={() => setExpanded(!expanded)}
            >
              {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          </Tooltip>
          <Tooltip title="Close">
            <IconButton
              color="inherit"
              size="small"
              onClick={onToggleMinimize}
            >
              <CloseIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Content */}
      {expanded && (
        <Box sx={{ p: 2 }}>
          <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center' }}>
            AI Assistant sera implémenté dans les prochaines phases.
          </Typography>
          <Button
            variant="outlined"
            fullWidth
            sx={{ mt: 2 }}
            onClick={() => console.log('AI Assistant clicked')}
          >
            Générer des suggestions
          </Button>
        </Box>
      )}
    </Card>
  );
};

export default AIAssistant;