import React, { useState, useEffect, useRef } from 'react'; import { Box, Paper, TextField, IconButton, Typography, Avatar, Chip, LinearProgress, Button, Card, CardContent, Grid, } from '@mui/material'; import { Send, AttachFile, Mic, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Toy, Person, SupportAgent, } from '@mui/icons-material'; import { useDispatch, useSelector } from 'react-redux'; import { RootState } from '../../store'; import { startConversation, sendMessage, getConversationHistory, } from '../../store/slices/chatSlice'; import { useSocket } from '../../hooks/useSocket'; import { FREE_MOBILE_COLORS } from '../../utils/constants'; import ReactMarkdown from 'react-markdown'; import EmergencyCallButton from '../Call/EmergencyCallButton'; import EmergencyCallStatus from '../Call/EmergencyCallStatus'; interface MessageBubbleProps { message: any; isOwn: boolean; onAction?: (action: string, payload: any) => void; } const MessageBubble: React.FC<MessageBubbleProps> = ({ message, isOwn, onAction }) => { const renderContent = () => { switch (message.content?.type) { case 'text': return ( <ReactMarkdown> {message.content.text} </ReactMarkdown> ); case 'button': return ( <Box> <Typography variant="body2" sx={{ mb: 2 }}> {message.content.text} </Typography> <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}> {message.content.payload?.buttons?.map((button: any, index: number) => ( <Button key={index} variant="outlined" size="small" onClick={() => onAction?.(button.action, button.payload)} sx={{ borderColor: FREE_MOBILE_COLORS.PRIMARY, color: FREE_MOBILE_COLORS.PRIMARY, '&:hover': { bgcolor: FREE_MOBILE_COLORS.PRIMARY, color: 'white', }, }} > {button.title} </Button> ))} </Box> </Box> ); case 'card': return ( <Card sx={{ maxWidth: 300 }}> <CardContent> <Typography variant="h6" gutterBottom> {message.content.payload?.title} </Typography> <Typography variant="body2" color="text.secondary"> {message.content.payload?.description} </Typography> {message.content.payload?.buttons && ( <Box sx={{ mt: 2, display: 'flex', gap: 1 }}> {message.content.payload.buttons.map((button: any, index: number) => ( <Button key={index} variant="contained" size="small" onClick={() => onAction?.(button.action, button.payload)} sx={{ bgcolor: FREE_MOBILE_COLORS.PRIMARY }} > {button.title} </Button> ))} </Box> )} </CardContent> </Card> ); default: return ( <Typography variant="body2"> {message.content?.text || 'Message non supporté'} </Typography> ); } }; const getSenderIcon = () => { switch (message.sender) { case 'bot': return <SmartToy />; case 'agent': return <SupportAgent />; default: return <Person />; } }; const getSenderColor = () => { switch (message.sender) { case 'bot': return FREE_MOBILE_COLORS.PRIMARY; case 'agent': return FREE_MOBILE_COLORS.SUCCESS; default: return FREE_MOBILE_COLORS.SECONDARY; } }; return ( <Box sx={{ display: 'flex', justifyContent: isOwn ? 'flex-end' : 'flex-start', mb: 2, }} > {!isOwn && ( <Avatar sx={{ bgcolor: getSenderColor(), mr: 1, width: 32, height: 32, }} > {getSenderIcon()} </Avatar> )} <Paper sx={{ p: 2, maxWidth: '70%', bgcolor: isOwn ? FREE_MOBILE_COLORS.PRIMARY : 'white', color: isOwn ? 'white' : 'black', borderRadius: isOwn ? '18px 18px 4px 18px' : '18px 18px 18px 4px', boxShadow: 2, }} > {renderContent()} <Typography variant="caption" sx={{ display: 'block', mt: 1, opacity: 0.7, textAlign: 'right', }} > {new Date(message.timestamp).toLocaleTimeString()} </Typography> </Paper> {isOwn && ( <Avatar sx={{ bgcolor: getSenderColor(), ml: 1, width: 32, height: 32, }} > <Person /> </Avatar> )} </Box> ); }; const ChatWindow: React.FC = () => { const [messageText, setMessageText] = useState(''); const [isRecording, setIsRecording] = useState(false); const messagesEndRef = useRef<HTMLDivElement>(null); const fileInputRef = useRef<HTMLInputElement>(null); const dispatch = useDispatch(); const { currentConversation, messages, loading, isTyping } = useSelector( (state: RootState) => state.chat ); const { user } = useSelector((state: RootState) => state.auth); const { currentCall } = useSelector((state: RootState) => state.emergencyCall); const { socket, connected } = useSocket(); useEffect(() => { if (!currentConversation) { dispatch(startConversation() as any); } }, [dispatch, currentConversation]); useEffect(() => { messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' }); }, [messages, isTyping]); const handleSendMessage = async () => { if (!messageText.trim() || !currentConversation) return; try { await dispatch(sendMessage({ conversationId: currentConversation._id, message: messageText }) as any); setMessageText(''); } catch (error) { console.error('Error sending message:', error); } }; const handleMessageAction = (action: string, payload: any) => { console.log('Message action:', action, payload); // Handle button actions here }; const handleKeyPress = (e: React.KeyboardEvent) => { if (e.key === 'Enter' && !e.shiftKey) { e.preventDefault(); handleSendMessage(); } }; return ( <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', bgcolor: 'background.default', }} > {/* Header */} <Paper sx={{ p: 2, display: 'flex', alignItems: 'center', justifyContent: 'space-between', borderRadius: 0, bgcolor: 'white', boxShadow: 1, }} > <Box sx={{ display: 'flex', alignItems: 'center' }}> <Avatar sx={{ bgcolor: FREE_MOBILE_COLORS.PRIMARY, mr: 2 }}> <SmartToy /> </Avatar> <Box> <Typography variant="h6"> Assistant Free Mobile </Typography> <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}> <Chip label={connected ? 'En ligne' : 'Hors ligne'} color={connected ? 'success' : 'error'} size="small" /> {isTyping && ( <Chip label="En train d'écrire..." color="info" size="small" /> )} </Box> </Box> </Box> {/* Emergency Call Button */} <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}> <EmergencyCallButton size="small" conversationHistory={messages.map(msg => ({ content: msg.content?.text || '', sender: msg.sender === 'user' ? 'user' : 'bot', timestamp: new Date(msg.timestamp) }))} /> </Box> </Paper> {/* Emergency Call Status */} {currentCall && ( <EmergencyCallStatus emergencyCallId={currentCall.emergencyCallId} onEndCall={() => { // Handle end call console.log('End emergency call'); }} onEscalate={() => { // Handle escalation console.log('Escalate emergency call'); }} /> )} {/* Messages */} <Box sx={{ flex: 1, overflow: 'auto', p: 2, bgcolor: 'grey.50', }} > {messages.map((message) => ( <MessageBubble key={message._id} message={message} isOwn={message.sender === 'user'} onAction={handleMessageAction} /> ))} {isTyping && ( <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}> <Avatar sx={{ bgcolor: FREE_MOBILE_COLORS.PRIMARY, mr: 1, width: 32, height: 32 }}> <SmartToy /> </Avatar> <Paper sx={{ p: 2, borderRadius: '18px 18px 18px 4px' }}> <Typography variant="body2" color="text.secondary"> L'assistant est en train d'écrire... </Typography> <LinearProgress sx={{ mt: 1, width: 80 }} /> </Paper> </Box> )} <div ref={messagesEndRef} /> </Box> {/* Input */} <Paper sx={{ p: 2, borderRadius: 0, boxShadow: 3, }} > <Box sx={{ display: 'flex', alignItems: 'flex-end', gap: 1 }}> <TextField fullWidth multiline maxRows={4} placeholder="Tapez votre message..." value={messageText} onChange={(e) => setMessageText(e.target.value)} onKeyPress={handleKeyPress} variant="outlined" size="small" disabled={loading || !connected} sx={{ '& .MuiOutlinedInput-root': { borderRadius: 3, }, }} /> <input type="file" ref={fileInputRef} style={{ display: 'none' }} accept="image/*" /> <IconButton color="primary" onClick={() => fileInputRef.current?.click()} disabled={loading} > <AttachFile /> </IconButton> <IconButton color={isRecording ? 'error' : 'primary'} onClick={() => setIsRecording(!isRecording)} disabled={loading} > {isRecording ? <MicOff /> : <Mic />} </IconButton> <IconButton color="primary" onClick={handleSendMessage} disabled={!messageText.trim() || loading || !connected} sx={{ bgcolor: FREE_MOBILE_COLORS.PRIMARY, color: 'white', '&:hover': { bgcolor: '#CC0000', }, }} > <Send /> </IconButton> </Box> </Paper> </Box> ); }; export default ChatWindow;