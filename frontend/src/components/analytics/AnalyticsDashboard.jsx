/** * ============================================= * [ANALYTICS] ANALYTICS DASHBOARD COMPONENT * Comprehensive analytics and reporting interface * Real-time metrics and business intelligence * ============================================= */ import React, { useState, useEffect, useCallback } from 'react'; import { useDispatch, useSelector } from 'react-redux'; import { Box, Grid, Card, CardContent, Typography, Button, Chip, Tab, Tabs, IconButton, Tooltip, Select, MenuItem, FormControl, InputLabel, CircularProgress, Alert, Paper } from '@mui/material'; import { Dashboard as DashboardIcon, TrendingUp as TrendingUpIcon, People as PeopleIcon, Assessment as AssessmentIcon, School as SchoolIcon, Refresh as RefreshIcon, Download as DownloadIcon, DateRange as DateRangeIcon, FilterList as FilterIcon } from '@mui/icons-material'; import { motion, AnimatePresence } from 'framer-motion'; import { DatePicker } from '@mui/x-date-pickers/DatePicker'; import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'; import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns'; import { fetchDashboardAnalytics, fetchAgentPerformance, fetchCustomerInsights, fetchOperationalEfficiency, fetchSimulationInsights, fetchRealTimeMetrics } from '../../store/slices/analyticsSlice'; import MetricCard from './MetricCard'; import PerformanceChart from './PerformanceChart'; import CustomerSatisfactionChart from './CustomerSatisfactionChart'; import AgentLeaderboard from './AgentLeaderboard'; import RealTimeMetrics from './RealTimeMetrics'; const AnalyticsDashboard = () => { const dispatch = useDispatch(); const { dashboardData, agentPerformance, customerInsights, operationalEfficiency, simulationInsights, realTimeMetrics, loading, error } = useSelector(state => state.analytics); const { user } = useSelector(state => state.auth); const [activeTab, setActiveTab] = useState(0); const [timeRange, setTimeRange] = useState('30d'); const [selectedAgent, setSelectedAgent] = useState('all'); const [dateRange, setDateRange] = useState({ start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), end: new Date() }); useEffect(() => { // Fetch initial dashboard data dispatch(fetchDashboardAnalytics({ time_range: timeRange, include_predictions: true })); // Set up real-time metrics polling for admin/supervisor if (['admin', 'supervisor'].includes(user.role)) { const interval = setInterval(() => { dispatch(fetchRealTimeMetrics()); }, 30000); // Update every 30 seconds return () => clearInterval(interval); } }, [dispatch, timeRange, user.role]); useEffect(() => { // Load tab-specific data loadTabData(activeTab); }, [activeTab, timeRange, selectedAgent]); const loadTabData = useCallback((tabIndex) => { const params = { time_range: timeRange, agent_id: selectedAgent !== 'all' ? selectedAgent : undefined }; switch (tabIndex) { case 1: dispatch(fetchAgentPerformance(params)); break; case 2: dispatch(fetchCustomerInsights(params)); break; case 3: dispatch(fetchOperationalEfficiency(params)); break; case 4: dispatch(fetchSimulationInsights(params)); break; default: break; } }, [dispatch, timeRange, selectedAgent]); const handleTabChange = (event, newValue) => { setActiveTab(newValue); }; const handleTimeRangeChange = (newTimeRange) => { setTimeRange(newTimeRange); }; const handleRefresh = () => { dispatch(fetchDashboardAnalytics({ time_range: timeRange, include_predictions: true })); loadTabData(activeTab); }; const handleExport = () => { // Implement export functionality console.log('Exporting analytics data...'); }; const formatMetricValue = (value, type) => { switch (type) { case 'percentage': return `${value}%`; case 'currency': return `€${value.toLocaleString()}`; case 'duration': return `${value}min`; default: return value.toLocaleString(); } }; const getMetricTrend = (current, previous) => { if (!previous) return 0; return ((current - previous) / previous) * 100; }; // Check user permissions const canViewAnalytics = ['admin', 'supervisor', 'analyst'].includes(user.role); const canViewAllAgents = ['admin', 'supervisor'].includes(user.role); if (!canViewAnalytics) { return ( <Box sx={{ p: 3 }}> <Alert severity="warning"> You don't have permission to view analytics. </Alert> </Box> ); } return ( <LocalizationProvider dateAdapter={AdapterDateFns}> <Box sx={{ p: 3 }}> {/* Header */} <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}> <Box> <Typography variant="h4" component="h1" gutterBottom> [ANALYTICS] Analytics Dashboard </Typography> <Typography variant="body1" color="text.secondary"> Comprehensive insights and performance metrics </Typography> </Box> <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}> {/* Time Range Selector */} <FormControl size="small" sx={{ minWidth: 120 }}> <InputLabel>Time Range</InputLabel> <Select value={timeRange} label="Time Range" onChange={(e) => handleTimeRangeChange(e.target.value)} > <MenuItem value="1d">Last 24 hours</MenuItem> <MenuItem value="7d">Last 7 days</MenuItem> <MenuItem value="30d">Last 30 days</MenuItem> <MenuItem value="90d">Last 90 days</MenuItem> </Select> </FormControl> {/* Agent Selector */} {canViewAllAgents && ( <FormControl size="small" sx={{ minWidth: 150 }}> <InputLabel>Agent</InputLabel> <Select value={selectedAgent} label="Agent" onChange={(e) => setSelectedAgent(e.target.value)} > <MenuItem value="all">All Agents</MenuItem> {/* Add agent options dynamically */} </Select> </FormControl> )} <Tooltip title="Refresh data"> <IconButton onClick={handleRefresh} disabled={loading}> <RefreshIcon /> </IconButton> </Tooltip> <Tooltip title="Export data"> <IconButton onClick={handleExport}> <DownloadIcon /> </IconButton> </Tooltip> </Box> </Box> {/* Real-time Metrics Bar */} {['admin', 'supervisor'].includes(user.role) && realTimeMetrics && ( <Paper sx={{ p: 2, mb: 3, bgcolor: 'primary.main', color: 'white' }}> <RealTimeMetrics data={realTimeMetrics} /> </Paper> )} {/* Analytics Tabs */} <Card> <Box sx={{ borderBottom: 1, borderColor: 'divider' }}> <Tabs value={activeTab} onChange={handleTabChange} aria-label="analytics tabs"> <Tab icon={<DashboardIcon />} label="Overview" iconPosition="start" /> <Tab icon={<PeopleIcon />} label="Agent Performance" iconPosition="start" /> <Tab icon={<TrendingUpIcon />} label="Customer Insights" iconPosition="start" /> <Tab icon={<AssessmentIcon />} label="Operations" iconPosition="start" /> <Tab icon={<SchoolIcon />} label="Training" iconPosition="start" /> </Tabs> </Box> <CardContent> <AnimatePresence mode="wait"> {/* Overview Tab */} {activeTab === 0 && ( <motion.div key="overview" initial={{ opacity: 0, x: 20 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -20 }} transition={{ duration: 0.3 }} > {loading ? ( <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}> <CircularProgress /> </Box> ) : ( <Grid container spacing={3}> {/* Key Metrics */} <Grid item xs={12} md={3}> <MetricCard title="Total Tickets" value={dashboardData?.core_metrics?.total_tickets || 0} trend={getMetricTrend( dashboardData?.core_metrics?.total_tickets, dashboardData?.core_metrics?.previous_total_tickets )} icon={<AssessmentIcon />} color="primary" /> </Grid> <Grid item xs={12} md={3}> <MetricCard title="Avg Resolution Time" value={formatMetricValue( dashboardData?.core_metrics?.avg_resolution_time || 0, 'duration' )} trend={getMetricTrend( dashboardData?.core_metrics?.avg_resolution_time, dashboardData?.core_metrics?.previous_avg_resolution_time )} icon={<TrendingUpIcon />} color="warning" /> </Grid> <Grid item xs={12} md={3}> <MetricCard title="Customer Satisfaction" value={formatMetricValue( dashboardData?.satisfaction_metrics?.average_score || 0, 'percentage' )} trend={getMetricTrend( dashboardData?.satisfaction_metrics?.average_score, dashboardData?.satisfaction_metrics?.previous_average_score )} icon={<PeopleIcon />} color="success" /> </Grid> <Grid item xs={12} md={3}> <MetricCard title="First Contact Resolution" value={formatMetricValue( dashboardData?.core_metrics?.fcr_rate || 0, 'percentage' )} trend={getMetricTrend( dashboardData?.core_metrics?.fcr_rate, dashboardData?.core_metrics?.previous_fcr_rate )} icon={<TrendingUpIcon />} color="info" /> </Grid> {/* Performance Chart */} <Grid item xs={12} md={8}> <Card> <CardContent> <Typography variant="h6" gutterBottom> Performance Trends </Typography> <PerformanceChart data={dashboardData?.performance_trends} timeRange={timeRange} /> </CardContent> </Card> </Grid> {/* Agent Leaderboard */} <Grid item xs={12} md={4}> <Card> <CardContent> <Typography variant="h6" gutterBottom> Top Performers </Typography> <AgentLeaderboard data={dashboardData?.agent_statistics?.top_performers} /> </CardContent> </Card> </Grid> {/* Customer Satisfaction Chart */} <Grid item xs={12}> <Card> <CardContent> <Typography variant="h6" gutterBottom> Customer Satisfaction Trends </Typography> <CustomerSatisfactionChart data={dashboardData?.satisfaction_metrics?.trends} timeRange={timeRange} /> </CardContent> </Card> </Grid> </Grid> )} </motion.div> )} {/* Agent Performance Tab */} {activeTab === 1 && ( <motion.div key="agent-performance" initial={{ opacity: 0, x: 20 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -20 }} transition={{ duration: 0.3 }} > <Typography variant="h6" gutterBottom> Agent Performance Analytics </Typography> {/* Agent performance content */} </motion.div> )} {/* Customer Insights Tab */} {activeTab === 2 && ( <motion.div key="customer-insights" initial={{ opacity: 0, x: 20 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -20 }} transition={{ duration: 0.3 }} > <Typography variant="h6" gutterBottom> Customer Behavior Insights </Typography> {/* Customer insights content */} </motion.div> )} {/* Operations Tab */} {activeTab === 3 && ( <motion.div key="operations" initial={{ opacity: 0, x: 20 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -20 }} transition={{ duration: 0.3 }} > <Typography variant="h6" gutterBottom> Operational Efficiency </Typography> {/* Operations content */} </motion.div> )} {/* Training Tab */} {activeTab === 4 && ( <motion.div key="training" initial={{ opacity: 0, x: 20 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -20 }} transition={{ duration: 0.3 }} > <Typography variant="h6" gutterBottom> Training & Simulation Insights </Typography> {/* Training insights content */} </motion.div> )} </AnimatePresence> </CardContent> </Card> {/* Error Display */} {error && ( <Alert severity="error" sx={{ mt: 2 }}> {error} </Alert> )} </Box> </LocalizationProvider> ); }; export default AnalyticsDashboard;