/** * ============================================= * CONVERSATION INBOX * Unified conversation list with filtering and sorting * Infinite scroll with virtualization for performance * ============================================= */ import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react'; import { FixedSizeList as List } from 'react-window'; import InfiniteLoader from 'react-window-infinite-loader'; import ConversationItem from './ConversationItem'; import ConversationFilters from './ConversationFilters'; import SearchBar from './SearchBar'; import './ConversationInbox.css'; const ConversationInbox = ({ conversations, selectedConversation, onSelectConversation, filters, onFiltersChange, loading, platforms }) => { const [searchTerm, setSearchTerm] = useState(''); const [sortBy, setSortBy] = useState('lastActivity'); const [sortOrder, setSortOrder] = useState('desc'); const [hasNextPage, setHasNextPage] = useState(true); const [isLoadingMore, setIsLoadingMore] = useState(false); const [displayedConversations, setDisplayedConversations] = useState([]); const listRef = useRef(); const ITEM_HEIGHT = 80; const CONTAINER_HEIGHT = 600; // Filter and sort conversations const filteredAndSortedConversations = useMemo(() => { let filtered = [...conversations]; // Search filter if (searchTerm) { const searchLower = searchTerm.toLowerCase(); filtered = filtered.filter(conversation => { const customerName = conversation.customer?.name?.toLowerCase() || ''; const lastMessageText = conversation.lastMessage?.text?.toLowerCase() || ''; const platform = conversation.platform?.toLowerCase() || ''; return customerName.includes(searchLower) || lastMessageText.includes(searchLower) || platform.includes(searchLower); }); } // Sort conversations filtered.sort((a, b) => { let aValue, bValue; switch (sortBy) { case 'lastActivity': aValue = new Date(a.lastActivity); bValue = new Date(b.lastActivity); break; case 'customerName': aValue = a.customer?.name || ''; bValue = b.customer?.name || ''; break; case 'platform': aValue = a.platform; bValue = b.platform; break; case 'urgency': const urgencyOrder = { urgent: 4, high: 3, medium: 2, low: 1 }; aValue = urgencyOrder[a.urgency] || 0; bValue = urgencyOrder[b.urgency] || 0; break; case 'status': aValue = a.status; bValue = b.status; break; default: aValue = new Date(a.lastActivity); bValue = new Date(b.lastActivity); } if (sortOrder === 'asc') { return aValue > bValue ? 1 : aValue < bValue ? -1 : 0; } else { return aValue < bValue ? 1 : aValue > bValue ? -1 : 0; } }); return filtered; }, [conversations, searchTerm, sortBy, sortOrder]); // Update displayed conversations for virtualization useEffect(() => { setDisplayedConversations(filteredAndSortedConversations); }, [filteredAndSortedConversations]); // Handle search const handleSearch = useCallback((term) => { setSearchTerm(term); }, []); // Handle sort change const handleSortChange = useCallback((newSortBy) => { if (sortBy === newSortBy) { setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc'); } else { setSortBy(newSortBy); setSortOrder('desc'); } }, [sortBy, sortOrder]); // Load more conversations (for infinite scroll) const loadMoreConversations = useCallback(async () => { if (isLoadingMore || !hasNextPage) return; setIsLoadingMore(true); try { // This would typically call an API to load more conversations // For now, we'll simulate it await new Promise(resolve => setTimeout(resolve, 1000)); // In a real implementation, you would: // const moreConversations = await api.getConversations({ offset: conversations.length }); // setConversations(prev => [...prev, ...moreConversations]); setHasNextPage(false); // No more conversations to load } catch (error) { console.error('Failed to load more conversations:', error); } finally { setIsLoadingMore(false); } }, [isLoadingMore, hasNextPage]); // Check if item is loaded (for infinite loader) const isItemLoaded = useCallback((index) => { return !!displayedConversations[index]; }, [displayedConversations]); // Get conversation counts by status const conversationCounts = useMemo(() => { return conversations.reduce((counts, conversation) => { counts.total = (counts.total || 0) + 1; counts[conversation.status] = (counts[conversation.status] || 0) + 1; counts[conversation.urgency] = (counts[conversation.urgency] || 0) + 1; return counts; }, {}); }, [conversations]); // Get platform icon const getPlatformIcon = (platform) => { const platformConfig = platforms.find(p => p.id === platform); return platformConfig?.icon || ''; }; // Get platform color const getPlatformColor = (platform) => { const platformConfig = platforms.find(p => p.id === platform); return platformConfig?.color || '#6366f1'; }; // Render conversation item const ConversationItemRenderer = ({ index, style }) => { const conversation = displayedConversations[index]; if (!conversation) { return ( <div style={style} className="conversation-item loading"> <div className="loading-placeholder"> <div className="placeholder-avatar"></div> <div className="placeholder-content"> <div className="placeholder-line"></div> <div className="placeholder-line short"></div> </div> </div> </div> ); } return ( <div style={style}> <ConversationItem conversation={conversation} isSelected={selectedConversation?.id === conversation.id} onSelect={onSelectConversation} platformIcon={getPlatformIcon(conversation.platform)} platformColor={getPlatformColor(conversation.platform)} /> </div> ); }; return ( <div className="conversation-inbox"> {/* Header */} <div className="inbox-header"> <div className="header-title"> <h3>Conversations</h3> <span className="conversation-count"> {filteredAndSortedConversations.length} </span> </div> {/* Search Bar */} <SearchBar value={searchTerm} onChange={handleSearch} placeholder="Rechercher conversations..." /> </div> {/* Filters */} <ConversationFilters filters={filters} onFiltersChange={onFiltersChange} conversationCounts={conversationCounts} sortBy={sortBy} sortOrder={sortOrder} onSortChange={handleSortChange} /> {/* Conversation List */} <div className="conversation-list-container"> {loading ? ( <div className="loading-state"> <div className="loading-spinner"> <div className="spinner"></div> </div> <p>Chargement des conversations...</p> </div> ) : filteredAndSortedConversations.length === 0 ? ( <div className="empty-state"> {searchTerm ? ( <> <div className="empty-icon">[SEARCH]</div> <h4>Aucun résultat</h4> <p>Aucune conversation ne correspond à votre recherche "{searchTerm}"</p> <button className="clear-search-button" onClick={() => setSearchTerm('')} > Effacer la recherche </button> </> ) : ( <> <div className="empty-icon"></div> <h4>Aucune conversation</h4> <p>Aucune conversation ne correspond aux filtres sélectionnés.</p> </> )} </div> ) : ( <InfiniteLoader isItemLoaded={isItemLoaded} itemCount={hasNextPage ? displayedConversations.length + 1 : displayedConversations.length} loadMoreItems={loadMoreConversations} > {({ onItemsRendered, ref }) => ( <List ref={(list) => { ref(list); listRef.current = list; }} height={CONTAINER_HEIGHT} itemCount={displayedConversations.length} itemSize={ITEM_HEIGHT} onItemsRendered={onItemsRendered} className="conversation-list" > {ConversationItemRenderer} </List> )} </InfiniteLoader> )} {/* Loading More Indicator */} {isLoadingMore && ( <div className="loading-more"> <div className="loading-spinner small"> <div className="spinner"></div> </div> <span>Chargement...</span> </div> )} </div> {/* Quick Stats */} <div className="inbox-stats"> <div className="stat-item"> <span className="stat-label">Total</span> <span className="stat-value">{conversationCounts.total || 0}</span> </div> <div className="stat-item"> <span className="stat-label">Ouvertes</span> <span className="stat-value">{conversationCounts.open || 0}</span> </div> <div className="stat-item"> <span className="stat-label">Assignées</span> <span className="stat-value">{conversationCounts.assigned || 0}</span> </div> <div className="stat-item urgent"> <span className="stat-label">Urgentes</span> <span className="stat-value">{conversationCounts.urgent || 0}</span> </div> </div> {/* Keyboard Shortcuts Help */} <div className="keyboard-shortcuts-hint"> <small> [FEATURE] Utilisez ↑↓ pour naviguer, Entrée pour sélectionner </small> </div> </div> ); }; export default ConversationInbox;