/** * ============================================= * CONVERSATION VIEW * Main conversation interface with message display and composer * Platform-specific message rendering and real-time updates * ============================================= */ import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react'; import { FixedSizeList as List } from 'react-window'; import MessageBubble from './messages/MessageBubble'; import MessageComposer from './messages/MessageComposer'; import ConversationHeader from './ConversationHeader'; import TypingIndicator from './messages/TypingIndicator'; import MessageAnalytics from './messages/MessageAnalytics'; import './ConversationView.css'; const ConversationView = ({ conversation, customer, onSendMessage, currentUser }) => { const [messages, setMessages] = useState([]); const [isTyping, setIsTyping] = useState(false); const [typingUsers, setTypingUsers] = useState([]); const [showAnalytics, setShowAnalytics] = useState(false); const [isLoadingHistory, setIsLoadingHistory] = useState(false); const [hasMoreHistory, setHasMoreHistory] = useState(true); const [selectedMessage, setSelectedMessage] = useState(null); const messagesEndRef = useRef(null); const listRef = useRef(null); const containerRef = useRef(null); const MESSAGE_HEIGHT = 80; // Approximate height for virtualization // Initialize messages from conversation useEffect(() => { if (conversation?.messages) { setMessages(conversation.messages); scrollToBottom(); } }, [conversation?.messages]); // Auto-scroll to bottom when new messages arrive useEffect(() => { if (messages.length > 0) { const lastMessage = messages[messages.length - 1]; if (lastMessage.direction === 'inbound' || lastMessage.agentId === currentUser.id) { scrollToBottom(); } } }, [messages, currentUser.id]); // Scroll to bottom const scrollToBottom = useCallback(() => { if (listRef.current) { listRef.current.scrollToItem(messages.length - 1, 'end'); } }, [messages.length]); // Load message history const loadMessageHistory = useCallback(async () => { if (isLoadingHistory || !hasMoreHistory) return; setIsLoadingHistory(true); try { // This would typically call an API to load older messages // const olderMessages = await api.getMessageHistory(conversation.id, messages[0]?.id); // setMessages(prev => [...olderMessages, ...prev]); // For now, simulate loading await new Promise(resolve => setTimeout(resolve, 1000)); setHasMoreHistory(false); } catch (error) { console.error('Failed to load message history:', error); } finally { setIsLoadingHistory(false); } }, [conversation.id, messages, isLoadingHistory, hasMoreHistory]); // Handle message send const handleSendMessage = useCallback(async (messageData) => { try { // Optimistically add message to UI const tempMessage = { id: `temp_${Date.now()}`, text: messageData.text, type: messageData.type || 'text', direction: 'outbound', timestamp: new Date(), agentId: currentUser.id, status: 'sending', media: messageData.media || [], temporary: true }; setMessages(prev => [...prev, tempMessage]); // Send message const sentMessage = await onSendMessage(messageData); // Replace temporary message with actual message setMessages(prev => prev.map(msg => msg.id === tempMessage.id ? sentMessage : msg ) ); } catch (error) { console.error('Failed to send message:', error); // Mark temporary message as failed setMessages(prev => prev.map(msg => msg.temporary ? { ...msg, status: 'failed' } : msg ) ); } }, [onSendMessage, currentUser.id]); // Handle typing indicator const handleTyping = useCallback((isTyping) => { setIsTyping(isTyping); // In a real implementation, you would emit typing status to other users }, []); // Get platform-specific configuration const platformConfig = useMemo(() => { const configs = { whatsapp: { name: 'WhatsApp', icon: '[MOBILE]', color: '#25d366', features: { readReceipts: true, typing: true, media: true, interactive: true, location: true, contacts: true } }, facebook: { name: 'Facebook Messenger', icon: '', color: '#1877f2', features: { readReceipts: true, typing: true, media: true, interactive: true, quickReplies: true, postbacks: true } }, instagram: { name: 'Instagram', icon: '', color: '#e4405f', features: { readReceipts: true, typing: true, media: true, stories: true } }, twitter: { name: 'Twitter', icon: '', color: '#1da1f2', features: { media: true, mentions: true, hashtags: true, urls: true } } }; return configs[conversation?.platform] || configs.whatsapp; }, [conversation?.platform]); // Render message item for virtualization const MessageItem = ({ index, style }) => { const message = messages[index]; if (!message) { return ( <div style={style} className="message-loading"> <div className="loading-placeholder"> <div className="placeholder-bubble"></div> </div> </div> ); } return ( <div style={style}> <MessageBubble message={message} platform={conversation.platform} platformConfig={platformConfig} isOwn={message.direction === 'outbound' && message.agentId === currentUser.id} customer={customer} agent={message.agent || currentUser} onSelect={() => setSelectedMessage(message)} isSelected={selectedMessage?.id === message.id} /> </div> ); }; // Handle scroll to load more history const handleScroll = useCallback(({ scrollOffset }) => { if (scrollOffset === 0 && hasMoreHistory && !isLoadingHistory) { loadMessageHistory(); } }, [hasMoreHistory, isLoadingHistory, loadMessageHistory]); if (!conversation) { return ( <div className="conversation-view empty"> <div className="empty-state"> <div className="empty-icon"></div> <h3>Aucune conversation sélectionnée</h3> <p>Sélectionnez une conversation pour commencer à discuter.</p> </div> </div> ); } return ( <div className="conversation-view" ref={containerRef}> {/* Conversation Header */} <ConversationHeader conversation={conversation} customer={customer} platformConfig={platformConfig} onShowAnalytics={() => setShowAnalytics(!showAnalytics)} showAnalytics={showAnalytics} /> {/* Analytics Panel */} {showAnalytics && ( <div className="analytics-panel"> <MessageAnalytics conversation={conversation} messages={messages} onClose={() => setShowAnalytics(false)} /> </div> )} {/* Messages Container */} <div className="messages-container"> {/* Loading History Indicator */} {isLoadingHistory && ( <div className="loading-history"> <div className="loading-spinner small"> <div className="spinner"></div> </div> <span>Chargement de l'historique...</span> </div> )} {/* Messages List */} {messages.length > 0 ? ( <List ref={listRef} height={400} // This should be dynamic based on container itemCount={messages.length} itemSize={MESSAGE_HEIGHT} onScroll={handleScroll} className="messages-list" > {MessageItem} </List> ) : ( <div className="no-messages"> <div className="empty-icon"></div> <h4>Début de la conversation</h4> <p>Commencez à discuter avec {customer?.name || 'le client'}.</p> </div> )} {/* Typing Indicator */} {(isTyping || typingUsers.length > 0) && ( <TypingIndicator users={typingUsers} platform={conversation.platform} platformConfig={platformConfig} /> )} {/* Scroll to Bottom Button */} <button className="scroll-to-bottom" onClick={scrollToBottom} title="Aller au bas" > ↓ </button> </div> {/* Message Composer */} <div className="composer-container"> <MessageComposer conversation={conversation} platformConfig={platformConfig} onSendMessage={handleSendMessage} onTyping={handleTyping} disabled={conversation.status === 'closed'} /> </div> {/* Conversation Status Bar */} <div className="conversation-status-bar"> <div className="status-info"> <span className={`status-indicator ${conversation.status}`}> {conversation.status === 'open' && '🟢 Ouverte'} {conversation.status === 'assigned' && '[USER] Assignée'} {conversation.status === 'pending' && '⏳ En attente'} {conversation.status === 'resolved' && '[COMPLETE] Résolue'} {conversation.status === 'closed' && ' Fermée'} </span> {conversation.assignedAgent && ( <span className="assigned-agent"> Assignée à {conversation.assignedAgent.name} </span> )} <span className="message-count"> {messages.length} message{messages.length > 1 ? 's' : ''} </span> </div> <div className="quick-actions"> <button className="quick-action" onClick={() => { // Handle escalate action }} title="Escalader" > ⬆ </button> <button className="quick-action" onClick={() => { // Handle resolve action }} title="Résoudre" > [COMPLETE] </button> <button className="quick-action" onClick={() => { // Handle transfer action }} title="Transférer" > </button> </div> </div> {/* Keyboard Shortcuts */} <div className="keyboard-shortcuts-hint"> <small> [FEATURE] Ctrl+Enter pour envoyer, Ctrl+K pour les raccourcis </small> </div> </div> ); }; export default ConversationView;