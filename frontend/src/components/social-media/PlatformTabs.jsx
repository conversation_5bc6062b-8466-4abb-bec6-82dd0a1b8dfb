/** * ============================================= * [MOBILE] PLATFORM TABS * Platform selection tabs with conversation counts * Real-time updates and keyboard navigation * ============================================= */ import React, { useMemo } from 'react'; import './PlatformTabs.css'; const PlatformTabs = ({ platforms, activePlatform, onPlatformChange, conversationCounts = {} }) => { // Calculate total conversations for each platform const platformCounts = useMemo(() => { return platforms.reduce((counts, platform) => { if (platform.id === 'all') { counts[platform.id] = Object.values(conversationCounts).reduce((sum, count) => sum + (count || 0), 0); } else { counts[platform.id] = conversationCounts[platform.id] || 0; } return counts; }, {}); }, [platforms, conversationCounts]); // Handle tab click const handleTabClick = (platformId) => { onPlatformChange(platformId); }; // Handle keyboard navigation const handleKeyDown = (e, platformId) => { if (e.key === 'Enter' || e.key === ' ') { e.preventDefault(); onPlatformChange(platformId); } }; return ( <div className="platform-tabs"> <div className="tabs-container"> {platforms.map((platform, index) => { const isActive = activePlatform === platform.id; const count = platformCounts[platform.id] || 0; return ( <div key={platform.id} className={`platform-tab ${isActive ? 'active' : ''}`} onClick={() => handleTabClick(platform.id)} onKeyDown={(e) => handleKeyDown(e, platform.id)} tabIndex={0} role="tab" aria-selected={isActive} aria-label={`${platform.name} - ${count} conversations`} style={{ '--platform-color': platform.color }} > {/* Platform Icon */} <div className="platform-icon"> {platform.icon} </div> {/* Platform Info */} <div className="platform-info"> <div className="platform-name"> {platform.name} </div> {/* Conversation Count */} <div className="conversation-count"> {count > 0 && ( <span className="count-badge"> {count > 99 ? '99+' : count} </span> )} </div> </div> {/* Active Indicator */} {isActive && ( <div className="active-indicator" style={{ backgroundColor: platform.color }} /> )} {/* Keyboard Shortcut Hint */} {index < 5 && ( <div className="keyboard-hint"> <kbd>Ctrl+{index === 0 ? 'A' : index}</kbd> </div> )} </div> ); })} </div> {/* Platform Stats Summary */} <div className="platform-stats-summary"> <div className="stats-item"> <span className="stats-label">Total</span> <span className="stats-value">{platformCounts.all || 0}</span> </div> {platforms.filter(p => p.id !== 'all').map(platform => ( <div key={platform.id} className="stats-item"> <span className="stats-icon" style={{ color: platform.color }} > {platform.icon} </span> <span className="stats-value">{platformCounts[platform.id] || 0}</span> </div> ))} </div> </div> ); }; export default PlatformTabs;