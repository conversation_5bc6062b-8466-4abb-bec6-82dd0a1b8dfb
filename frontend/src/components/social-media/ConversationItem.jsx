/** * ============================================= * CONVERSATION ITEM * Individual conversation item in the inbox list * Platform-specific styling and status indicators * ============================================= */ import React, { memo, useMemo } from 'react'; import { formatDistanceToNow, format, isToday, isYesterday } from 'date-fns'; import { fr } from 'date-fns/locale'; import './ConversationItem.css'; const ConversationItem = memo(({ conversation, isSelected, onSelect, platformIcon, platformColor }) => { // Format last activity time const formattedTime = useMemo(() => { const lastActivity = new Date(conversation.lastActivity); if (isToday(lastActivity)) { return format(lastActivity, 'HH:mm'); } else if (isYesterday(lastActivity)) { return 'Hier'; } else { return formatDistanceToNow(lastActivity, { addSuffix: true, locale: fr }); } }, [conversation.lastActivity]); // Get status color and label const statusConfig = useMemo(() => { const configs = { open: { color: '#10b981', label: 'Ouverte', icon: '🟢' }, assigned: { color: '#3b82f6', label: 'Assignée', icon: '[USER]' }, pending: { color: '#f59e0b', label: 'En attente', icon: '⏳' }, resolved: { color: '#6b7280', label: 'Résolue', icon: '[COMPLETE]' }, closed: { color: '#6b7280', label: 'Fermée', icon: '' } }; return configs[conversation.status] || configs.open; }, [conversation.status]); // Get urgency config const urgencyConfig = useMemo(() => { const configs = { urgent: { color: '#ef4444', label: 'Urgent', icon: '' }, high: { color: '#f97316', label: 'Élevée', icon: '[PERFORMANCE]' }, medium: { color: '#eab308', label: 'Moyenne', icon: '' }, low: { color: '#22c55e', label: 'Faible', icon: '' } }; return configs[conversation.urgency] || configs.medium; }, [conversation.urgency]); // Get customer name const customerName = useMemo(() => { return conversation.customer?.name || conversation.customerName || `Client ${conversation.platform}`; }, [conversation.customer, conversation.customerName, conversation.platform]); // Get last message preview const lastMessagePreview = useMemo(() => { const lastMessage = conversation.lastMessage; if (!lastMessage) return 'Aucun message'; let preview = ''; // Add direction indicator if (lastMessage.direction === 'outbound') { preview = 'Vous: '; } // Add message content based on type switch (lastMessage.type) { case 'text': preview += lastMessage.text || ''; break; case 'image': preview += ' Image'; if (lastMessage.text) preview += `: ${lastMessage.text}`; break; case 'video': preview += ' Vidéo'; if (lastMessage.text) preview += `: ${lastMessage.text}`; break; case 'audio': preview += ' Audio'; break; case 'document': preview += ' Document'; if (lastMessage.text) preview += `: ${lastMessage.text}`; break; case 'location': preview += ' Position'; break; case 'contact': preview += '[USER] Contact'; break; case 'interactive': preview += ' Message interactif'; if (lastMessage.text) preview += `: ${lastMessage.text}`; break; case 'sticker': preview += ' Sticker'; break; default: preview += lastMessage.text || 'Message'; } // Truncate if too long return preview.length > 60 ? preview.substring(0, 57) + '...' : preview; }, [conversation.lastMessage]); // Check if conversation has unread messages const hasUnreadMessages = useMemo(() => { return conversation.unreadCount > 0; }, [conversation.unreadCount]); // Handle click const handleClick = () => { onSelect(conversation); }; // Handle keyboard navigation const handleKeyDown = (e) => { if (e.key === 'Enter' || e.key === ' ') { e.preventDefault(); onSelect(conversation); } }; return ( <div className={`conversation-item ${isSelected ? 'selected' : ''} ${hasUnreadMessages ? 'unread' : ''}`} onClick={handleClick} onKeyDown={handleKeyDown} tabIndex={0} role="button" aria-label={`Conversation avec ${customerName} sur ${conversation.platform}`} > {/* Platform Indicator */} <div className="platform-indicator" style={{ backgroundColor: platformColor }} title={conversation.platform} > <span className="platform-icon">{platformIcon}</span> </div> {/* Customer Avatar */} <div className="customer-avatar"> {conversation.customer?.profilePic ? ( <img src={conversation.customer.profilePic} alt={customerName} onError={(e) => { e.target.style.display = 'none'; e.target.nextSibling.style.display = 'flex'; }} /> ) : null} <div className="avatar-fallback" style={{ display: conversation.customer?.profilePic ? 'none' : 'flex' }} > {customerName.charAt(0).toUpperCase()} </div> {/* Online Status */} {conversation.customer?.isOnline && ( <div className="online-indicator" title="En ligne"></div> )} </div> {/* Conversation Content */} <div className="conversation-content"> {/* Header */} <div className="conversation-header"> <div className="customer-name"> {customerName} {conversation.customer?.isVip && ( <span className="vip-badge" title="Client VIP">⭐</span> )} </div> <div className="conversation-time"> {formattedTime} </div> </div> {/* Last Message */} <div className="last-message"> <span className="message-preview">{lastMessagePreview}</span> {/* Message Status Icons */} <div className="message-status"> {conversation.lastMessage?.direction === 'outbound' && ( <span className={`delivery-status ${conversation.lastMessage.status}`} title={`Message ${conversation.lastMessage.status}`} > {conversation.lastMessage.status === 'sent' && ''} {conversation.lastMessage.status === 'delivered' && ''} {conversation.lastMessage.status === 'read' && ''} {conversation.lastMessage.status === 'failed' && '[FAILED]'} </span> )} </div> </div> {/* Status and Indicators */} <div className="conversation-indicators"> {/* Status Badge */} <span className="status-badge" style={{ backgroundColor: statusConfig.color }} title={statusConfig.label} > {statusConfig.icon} </span> {/* Urgency Badge */} {conversation.urgency !== 'medium' && ( <span className="urgency-badge" style={{ backgroundColor: urgencyConfig.color }} title={`Urgence: ${urgencyConfig.label}`} > {urgencyConfig.icon} </span> )} {/* Assigned Agent */} {conversation.assignedAgent && ( <span className="assigned-badge" title={`Assigné à ${conversation.assignedAgent.name || 'Agent'}`} > [USER] </span> )} {/* Unread Count */} {hasUnreadMessages && ( <span className="unread-count"> {conversation.unreadCount > 99 ? '99+' : conversation.unreadCount} </span> )} {/* Escalation Indicator */} {conversation.escalationCount > 0 && ( <span className="escalation-badge" title={`Escaladé ${conversation.escalationCount} fois`} > ⬆ </span> )} {/* AI Analysis Indicators */} {conversation.analysis?.sentiment && ( <span className={`sentiment-indicator ${conversation.analysis.sentiment}`} title={`Sentiment: ${conversation.analysis.sentiment}`} > {conversation.analysis.sentiment === 'positive' && ''} {conversation.analysis.sentiment === 'neutral' && ''} {conversation.analysis.sentiment === 'negative' && ''} {conversation.analysis.sentiment === 'very_negative' && ''} </span> )} </div> </div> {/* Selection Indicator */} {isSelected && ( <div className="selection-indicator"> <div className="selection-dot"></div> </div> )} {/* Hover Actions */} <div className="hover-actions"> <button className="action-button" onClick={(e) => { e.stopPropagation(); // Handle quick assign action }} title="Assigner rapidement" > [USER] </button> <button className="action-button" onClick={(e) => { e.stopPropagation(); // Handle mark as urgent action }} title="Marquer comme urgent" > </button> <button className="action-button" onClick={(e) => { e.stopPropagation(); // Handle archive action }} title="Archiver" > </button> </div> </div> ); }); ConversationItem.displayName = 'ConversationItem'; export default ConversationItem;