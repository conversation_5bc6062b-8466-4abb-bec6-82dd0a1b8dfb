/**
 * =============================================
 * 📱 SOCIAL MEDIA DASHBOARD STYLES
 * Responsive design with mobile-first approach
 * Real-time updates and accessibility features
 * =============================================
 */

.social-media-dashboard {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8fafc;
  overflow: hidden;
}

/* Loading State */
.social-media-dashboard.loading {
  justify-content: center;
  align-items: center;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #6366f1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error State */
.social-media-dashboard.error {
  justify-content: center;
  align-items: center;
}

.error-message {
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.retry-button {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background-color: #6366f1;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background-color: #5856eb;
}

/* Dashboard Header */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.header-left h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
}

.metrics-summary {
  display: flex;
  gap: 1.5rem;
  margin-top: 0.5rem;
}

.metric {
  font-size: 0.875rem;
  color: #6b7280;
}

.metric strong {
  color: #1f2937;
  font-weight: 600;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.analytics-toggle,
.refresh-button {
  padding: 0.5rem;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 1rem;
}

.analytics-toggle:hover,
.refresh-button:hover {
  background: #e5e7eb;
}

.analytics-toggle.active {
  background: #6366f1;
  color: white;
  border-color: #6366f1;
}

/* Analytics Panel */
.analytics-panel {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  max-height: 400px;
  overflow-y: auto;
}

/* Main Content */
.dashboard-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* Sidebar */
.sidebar {
  width: 350px;
  background: white;
  border-right: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
}

.sidebar.collapsed {
  width: 60px;
}

/* Main Content Area */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #f8fafc;
}

/* No Conversation Selected */
.no-conversation-selected {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.empty-state {
  text-align: center;
  max-width: 400px;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.empty-state h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
}

.empty-state p {
  margin: 0 0 2rem 0;
  color: #6b7280;
  line-height: 1.5;
}

/* Keyboard Shortcuts */
.keyboard-shortcuts {
  background: #f9fafb;
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1rem;
}

.keyboard-shortcuts h4 {
  margin: 0 0 0.5rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
}

.keyboard-shortcuts ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.keyboard-shortcuts li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.25rem 0;
  font-size: 0.75rem;
  color: #6b7280;
}

kbd {
  background: #e5e7eb;
  border: 1px solid #d1d5db;
  border-radius: 3px;
  padding: 0.125rem 0.25rem;
  font-size: 0.75rem;
  font-family: monospace;
}

/* Customer Sidebar */
.customer-sidebar {
  width: 300px;
  background: white;
  border-left: 1px solid #e5e7eb;
  overflow-y: auto;
}

/* Sidebar Toggle */
.sidebar-toggle {
  position: fixed;
  left: 350px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 48px;
  background: white;
  border: 1px solid #e5e7eb;
  border-left: none;
  border-radius: 0 6px 6px 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  color: #6b7280;
  transition: all 0.3s ease;
  z-index: 10;
}

.sidebar.collapsed + .main-content + .customer-sidebar + .sidebar-toggle {
  left: 60px;
}

.sidebar-toggle:hover {
  background: #f3f4f6;
  color: #374151;
}

/* Keyboard Shortcuts Hint */
.keyboard-shortcuts-hint {
  position: fixed;
  bottom: 1rem;
  right: 1rem;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.keyboard-shortcuts-hint:hover {
  opacity: 1;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .customer-sidebar {
    position: fixed;
    right: 0;
    top: 0;
    height: 100vh;
    z-index: 20;
    transform: translateX(100%);
    transition: transform 0.3s ease;
  }

  .customer-sidebar.open {
    transform: translateX(0);
  }

  .sidebar-toggle {
    display: none;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    padding: 0.75rem 1rem;
  }

  .header-left h1 {
    font-size: 1.25rem;
  }

  .metrics-summary {
    display: none;
  }

  .sidebar {
    width: 100%;
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    z-index: 30;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .main-content {
    width: 100%;
  }

  .keyboard-shortcuts-hint {
    display: none;
  }
}

@media (max-width: 480px) {
  .dashboard-header {
    padding: 0.5rem;
  }

  .header-right {
    gap: 0.5rem;
  }

  .analytics-toggle,
  .refresh-button {
    padding: 0.375rem;
    font-size: 0.875rem;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .spinner {
    animation: none;
  }

  .sidebar,
  .customer-sidebar,
  .sidebar-toggle {
    transition: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .dashboard-header {
    border-bottom: 2px solid #000;
  }

  .sidebar {
    border-right: 2px solid #000;
  }

  .customer-sidebar {
    border-left: 2px solid #000;
  }
}

/* Focus styles for accessibility */
.analytics-toggle:focus,
.refresh-button:focus,
.sidebar-toggle:focus {
  outline: 2px solid #6366f1;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .sidebar-toggle,
  .keyboard-shortcuts-hint {
    display: none;
  }

  .dashboard-content {
    flex-direction: column;
  }

  .sidebar,
  .customer-sidebar {
    width: 100%;
    border: none;
  }
}
