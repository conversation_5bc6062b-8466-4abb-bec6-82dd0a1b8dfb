/** * ============================================= * [MOBILE] SOCIAL MEDIA DASHBOARD * Unified conversation management across all platforms * Real-time updates with WebSocket integration * ============================================= */ import React, { useState, useEffect, useCallback, useMemo } from 'react'; import { useSocket } from '../../hooks/useSocket'; import { useSocialMediaAPI } from '../../hooks/useSocialMediaAPI'; import { useAuth } from '../../contexts/AuthContext'; import { useNotification } from '../../contexts/NotificationContext'; import ConversationInbox from './ConversationInbox'; import ConversationView from './ConversationView'; import CustomerProfile from './CustomerProfile'; import SocialMediaAnalytics from './SocialMediaAnalytics'; import PlatformTabs from './PlatformTabs'; import AgentStatus from './AgentStatus'; import './SocialMediaDashboard.css'; const SocialMediaDashboard = () => { const { user } = useAuth(); const { showNotification } = useNotification(); const socket = useSocket(); const socialMediaAPI = useSocialMediaAPI(); // State management const [conversations, setConversations] = useState([]); const [selectedConversation, setSelectedConversation] = useState(null); const [selectedCustomer, setSelectedCustomer] = useState(null); const [activePlatform, setActivePlatform] = useState('all'); const [loading, setLoading] = useState(true); const [error, setError] = useState(null); const [agentStatus, setAgentStatus] = useState('available'); const [filters, setFilters] = useState({ status: 'all', urgency: 'all', assigned: 'all', dateRange: 'today' }); // Layout state const [sidebarCollapsed, setSidebarCollapsed] = useState(false); const [showAnalytics, setShowAnalytics] = useState(false); const [showCustomerProfile, setShowCustomerProfile] = useState(true); // Real-time metrics const [metrics, setMetrics] = useState({ totalConversations: 0, activeConversations: 0, pendingConversations: 0, averageResponseTime: 0, platformBreakdown: {} }); // Platform configuration const platforms = useMemo(() => [ { id: 'all', name: 'Toutes', icon: '', color: '#6366f1' }, { id: 'whatsapp', name: 'WhatsApp', icon: '[MOBILE]', color: '#25d366' }, { id: 'facebook', name: 'Facebook', icon: '', color: '#1877f2' }, { id: 'instagram', name: 'Instagram', icon: '', color: '#e4405f' }, { id: 'twitter', name: 'Twitter', icon: '', color: '#1da1f2' } ], []); // Initialize dashboard useEffect(() => { initializeDashboard(); }, []); // Socket event listeners useEffect(() => { if (!socket) return; const handleNewMessage = (data) => { handleIncomingMessage(data); }; const handleConversationUpdate = (data) => { handleConversationStatusUpdate(data); }; const handleAgentAssignment = (data) => { handleAgentAssignmentUpdate(data); }; const handleMetricsUpdate = (data) => { setMetrics(prev => ({ ...prev, ...data })); }; // Subscribe to socket events socket.on('new_message', handleNewMessage); socket.on('conversation_updated', handleConversationUpdate); socket.on('agent_assigned', handleAgentAssignment); socket.on('metrics_update', handleMetricsUpdate); socket.on('notification', handleNotification); // Join agent room for targeted notifications socket.emit('join_agent_room', user.id); return () => { socket.off('new_message', handleNewMessage); socket.off('conversation_updated', handleConversationUpdate); socket.off('agent_assigned', handleAgentAssignment); socket.off('metrics_update', handleMetricsUpdate); socket.off('notification', handleNotification); }; }, [socket, user.id]); // Initialize dashboard data const initializeDashboard = async () => { try { setLoading(true); setError(null); // Load conversations const conversationsData = await socialMediaAPI.getConversations({ agentId: user.id, platform: activePlatform === 'all' ? undefined : activePlatform, ...filters }); setConversations(conversationsData.conversations || []); // Load metrics const metricsData = await socialMediaAPI.getMetrics({ agentId: user.id, timeRange: filters.dateRange }); setMetrics(metricsData); // Set agent status await socialMediaAPI.updateAgentStatus(user.id, agentStatus); } catch (err) { console.error('Failed to initialize dashboard:', err); setError('Erreur lors du chargement du tableau de bord'); showNotification('Erreur lors du chargement des données', 'error'); } finally { setLoading(false); } }; // Handle incoming messages const handleIncomingMessage = useCallback((data) => { const { message, conversation, customer } = data; // Update conversations list setConversations(prev => { const existingIndex = prev.findIndex(conv => conv.id === conversation.id); if (existingIndex >= 0) { // Update existing conversation const updated = [...prev]; updated[existingIndex] = { ...updated[existingIndex], ...conversation, lastMessage: message, lastActivity: new Date(message.timestamp) }; // Move to top const [updatedConv] = updated.splice(existingIndex, 1); updated.unshift(updatedConv); return updated; } else { // Add new conversation return [{ ...conversation, lastMessage: message }, ...prev]; } }); // Update selected conversation if it's the active one if (selectedConversation && selectedConversation.id === conversation.id) { setSelectedConversation(prev => ({ ...prev, ...conversation, messages: [...(prev.messages || []), message] })); } // Show notification if not the active conversation if (!selectedConversation || selectedConversation.id !== conversation.id) { showNotification( `Nouveau message de ${customer.name} sur ${conversation.platform}`, 'info', { action: () => handleSelectConversation(conversation), actionLabel: 'Voir' } ); } // Play notification sound playNotificationSound(); }, [selectedConversation, showNotification]); // Handle conversation status updates const handleConversationStatusUpdate = useCallback((data) => { const { conversationId, status, agentId } = data; setConversations(prev => prev.map(conv => conv.id === conversationId ? { ...conv, status, assignedAgent: agentId } : conv ) ); if (selectedConversation && selectedConversation.id === conversationId) { setSelectedConversation(prev => ({ ...prev, status, assignedAgent: agentId })); } }, [selectedConversation]); // Handle agent assignment updates const handleAgentAssignmentUpdate = useCallback((data) => { const { conversationId, agentId, agentName } = data; if (agentId === user.id) { showNotification( `Nouvelle conversation assignée`, 'success', { action: () => { const conversation = conversations.find(conv => conv.id === conversationId); if (conversation) handleSelectConversation(conversation); }, actionLabel: 'Voir' } ); } handleConversationStatusUpdate({ conversationId, status: 'assigned', agentId }); }, [user.id, conversations, showNotification, handleConversationStatusUpdate]); // Handle notifications const handleNotification = useCallback((notification) => { showNotification(notification.title, notification.priority, { data: notification.data }); }, [showNotification]); // Select conversation const handleSelectConversation = async (conversation) => { try { setSelectedConversation(conversation); // Load conversation details and messages const conversationDetails = await socialMediaAPI.getConversationDetails(conversation.id); setSelectedConversation(conversationDetails); // Load customer details if (conversationDetails.customerId) { const customerDetails = await socialMediaAPI.getCustomerDetails(conversationDetails.customerId); setSelectedCustomer(customerDetails); } // Mark conversation as viewed await socialMediaAPI.markConversationAsViewed(conversation.id, user.id); } catch (err) { console.error('Failed to load conversation details:', err); showNotification('Erreur lors du chargement de la conversation', 'error'); } }; // Send message const handleSendMessage = async (messageData) => { if (!selectedConversation) return; try { const message = await socialMediaAPI.sendMessage( selectedConversation.id, messageData, user.id ); // Update conversation with new message setSelectedConversation(prev => ({ ...prev, messages: [...(prev.messages || []), message], lastMessage: message, lastActivity: new Date() })); // Update conversations list setConversations(prev => prev.map(conv => conv.id === selectedConversation.id ? { ...conv, lastMessage: message, lastActivity: new Date() } : conv ) ); } catch (err) { console.error('Failed to send message:', err); showNotification('Erreur lors de l\'envoi du message', 'error'); } }; // Update agent status const handleAgentStatusChange = async (newStatus) => { try { await socialMediaAPI.updateAgentStatus(user.id, newStatus); setAgentStatus(newStatus); showNotification(`Statut mis à jour: ${newStatus}`, 'success'); } catch (err) { console.error('Failed to update agent status:', err); showNotification('Erreur lors de la mise à jour du statut', 'error'); } }; // Filter conversations const filteredConversations = useMemo(() => { return conversations.filter(conversation => { // Platform filter if (activePlatform !== 'all' && conversation.platform !== activePlatform) { return false; } // Status filter if (filters.status !== 'all' && conversation.status !== filters.status) { return false; } // Urgency filter if (filters.urgency !== 'all' && conversation.urgency !== filters.urgency) { return false; } // Assignment filter if (filters.assigned === 'mine' && conversation.assignedAgent !== user.id) { return false; } if (filters.assigned === 'unassigned' && conversation.assignedAgent) { return false; } return true; }); }, [conversations, activePlatform, filters, user.id]); // Play notification sound const playNotificationSound = () => { try { const audio = new Audio('/sounds/notification.mp3'); audio.volume = 0.3; audio.play().catch(() => { // Ignore audio play errors (user interaction required) }); } catch (err) { // Ignore audio errors } }; // Keyboard shortcuts useEffect(() => { const handleKeyPress = (e) => { if (e.ctrlKey || e.metaKey) { switch (e.key) { case '1': e.preventDefault(); setActivePlatform('whatsapp'); break; case '2': e.preventDefault(); setActivePlatform('facebook'); break; case '3': e.preventDefault(); setActivePlatform('instagram'); break; case '4': e.preventDefault(); setActivePlatform('twitter'); break; case 'a': e.preventDefault(); setActivePlatform('all'); break; case 'r': e.preventDefault(); initializeDashboard(); break; } } }; window.addEventListener('keydown', handleKeyPress); return () => window.removeEventListener('keydown', handleKeyPress); }, []); if (loading) { return ( <div className="social-media-dashboard loading"> <div className="loading-spinner"> <div className="spinner"></div> <p>Chargement du tableau de bord...</p> </div> </div> ); } if (error) { return ( <div className="social-media-dashboard error"> <div className="error-message"> <h3>Erreur</h3> <p>{error}</p> <button onClick={initializeDashboard} className="retry-button"> Réessayer </button> </div> </div> ); } return ( <div className="social-media-dashboard"> {/* Header */} <div className="dashboard-header"> <div className="header-left"> <h1>Réseaux Sociaux</h1> <div className="metrics-summary"> <span className="metric"> <strong>{metrics.activeConversations}</strong> actives </span> <span className="metric"> <strong>{metrics.pendingConversations}</strong> en attente </span> <span className="metric"> <strong>{Math.round(metrics.averageResponseTime / 1000)}s</strong> temps de réponse </span> </div> </div> <div className="header-right"> <AgentStatus status={agentStatus} onStatusChange={handleAgentStatusChange} /> <button className={`analytics-toggle ${showAnalytics ? 'active' : ''}`} onClick={() => setShowAnalytics(!showAnalytics)} title="Afficher/Masquer les analyses" > [ANALYTICS] </button> <button className="refresh-button" onClick={initializeDashboard} title="Actualiser (Ctrl+R)" > </button> </div> </div> {/* Platform Tabs */} <PlatformTabs platforms={platforms} activePlatform={activePlatform} onPlatformChange={setActivePlatform} conversationCounts={metrics.platformBreakdown} /> {/* Analytics Panel */} {showAnalytics && ( <div className="analytics-panel"> <SocialMediaAnalytics metrics={metrics} conversations={filteredConversations} onClose={() => setShowAnalytics(false)} /> </div> )} {/* Main Content */} <div className="dashboard-content"> {/* Sidebar - Conversation Inbox */} <div className={`sidebar ${sidebarCollapsed ? 'collapsed' : ''}`}> <ConversationInbox conversations={filteredConversations} selectedConversation={selectedConversation} onSelectConversation={handleSelectConversation} filters={filters} onFiltersChange={setFilters} loading={loading} platforms={platforms} /> </div> {/* Main Content Area */} <div className="main-content"> {selectedConversation ? ( <ConversationView conversation={selectedConversation} customer={selectedCustomer} onSendMessage={handleSendMessage} currentUser={user} /> ) : ( <div className="no-conversation-selected"> <div className="empty-state"> <div className="empty-icon"></div> <h3>Sélectionnez une conversation</h3> <p>Choisissez une conversation dans la liste pour commencer à discuter avec vos clients.</p> <div className="keyboard-shortcuts"> <h4>Raccourcis clavier :</h4> <ul> <li><kbd>Ctrl+1</kbd> WhatsApp</li> <li><kbd>Ctrl+2</kbd> Facebook</li> <li><kbd>Ctrl+3</kbd> Instagram</li> <li><kbd>Ctrl+4</kbd> Twitter</li> <li><kbd>Ctrl+A</kbd> Toutes les plateformes</li> <li><kbd>Ctrl+R</kbd> Actualiser</li> </ul> </div> </div> </div> )} </div> {/* Customer Profile Sidebar */} {showCustomerProfile && selectedCustomer && ( <div className="customer-sidebar"> <CustomerProfile customer={selectedCustomer} conversation={selectedConversation} onClose={() => setShowCustomerProfile(false)} /> </div> )} </div> {/* Collapse/Expand Sidebar Button */} <button className="sidebar-toggle" onClick={() => setSidebarCollapsed(!sidebarCollapsed)} title={sidebarCollapsed ? 'Développer' : 'Réduire'} > {sidebarCollapsed ? '→' : '←'} </button> </div> ); }; export default SocialMediaDashboard;