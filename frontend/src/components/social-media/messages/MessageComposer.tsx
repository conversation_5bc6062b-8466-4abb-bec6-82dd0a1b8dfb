/** * ============================================= * MESSAGE COMPOSER * Universal message composer with platform-specific features * Media upload, templates, and interactive message support * ============================================= */ import React, { useState, useRef, useCallback, useEffect, useMemo } from 'react'; import EmojiPicker from 'emoji-picker-react'; // import MediaUploader from './MediaUploader'; // import TemplateSelector from './TemplateSelector'; // import QuickReplies from './QuickReplies'; import './MessageComposer.css'; interface MessageComposerProps { conversation: { platform: string; id: string; }; platformConfig?: any; onSendMessage: (message: any) => void; onTyping: (isTyping: boolean) => void; disabled?: boolean; } const MessageComposer: React.FC<MessageComposerProps> = ({ conversation, platformConfig, onSendMessage, onTyping, disabled = false }) => { const [message, setMessage] = useState<string>(''); const [messageType, setMessageType] = useState<string>('text'); const [attachedMedia, setAttachedMedia] = useState<any>(null); const [showEmojiPicker, setShowEmojiPicker] = useState<boolean>(false); const [showTemplates, setShowTemplates] = useState<boolean>(false); const [showQuickReplies, setShowQuickReplies] = useState<boolean>(false); const [isTyping, setIsTyping] = useState<boolean>(false); const [characterCount, setCharacterCount] = useState<number>(0); const textareaRef = useRef<HTMLTextAreaElement>(null); const fileInputRef = useRef<HTMLInputElement>(null); const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null); // Platform-specific character limits const characterLimits = useMemo(() => { const limits: Record<string, { text: number; dm: number }> = { whatsapp: { text: 4096, dm: 10000 }, facebook: { text: 2000, dm: 2000 }, instagram: { text: 1000, dm: 1000 }, twitter: { text: 280, dm: 10000 } }; return limits[conversation?.platform] || limits.whatsapp; }, [conversation?.platform]); // Get current character limit const currentLimit = useMemo(() => { return messageType === 'dm' ? characterLimits.dm : characterLimits.text; }, [messageType, characterLimits]); // Handle message input change const handleMessageChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => { const value = e.target.value; setMessage(value); setCharacterCount(value.length); // Handle typing indicator if (!isTyping && value.length > 0) { setIsTyping(true); onTyping(true); } // Clear typing timeout if (typingTimeoutRef.current) { clearTimeout(typingTimeoutRef.current); } // Set new typing timeout typingTimeoutRef.current = setTimeout(() => { setIsTyping(false); onTyping(false); }, 1000); // Auto-resize textarea if (textareaRef.current) { textareaRef.current.style.height = 'auto'; textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`; } }, [isTyping, onTyping]); // Handle key press const handleKeyPress = useCallback((e: React.KeyboardEvent<HTMLTextAreaElement>) => { if (e.key === 'Enter') { if (e.ctrlKey || e.metaKey) { // Ctrl+Enter or Cmd+Enter to send e.preventDefault(); handleSendMessage(); } else if (!e.shiftKey) { // Enter to send (unless Shift+Enter for new line) e.preventDefault(); handleSendMessage(); } } }, []); // Handle send message const handleSendMessage = useCallback(async () => { if (disabled) return; const trimmedMessage = message.trim(); if (!trimmedMessage && !attachedMedia) return; // Prepare message data const messageData: any = { text: trimmedMessage, type: messageType, platform: conversation.platform }; // Add media if attached if (attachedMedia) { messageData.media = attachedMedia; messageData.type = attachedMedia.type; } // Platform-specific message preparation if (conversation.platform === 'whatsapp') { messageData.messagingProduct = 'whatsapp'; } else if (conversation.platform === 'facebook') { messageData.messagingType = 'RESPONSE'; } try { await onSendMessage(messageData); // Clear composer setMessage(''); setAttachedMedia(null); setCharacterCount(0); setIsTyping(false); onTyping(false); // Reset textarea height if (textareaRef.current) { textareaRef.current.style.height = 'auto'; } } catch (error) { console.error('Failed to send message:', error); } }, [message, messageType, attachedMedia, conversation.platform, onSendMessage, onTyping, disabled]); // Handle emoji selection const handleEmojiSelect = useCallback((emojiData: any) => { const emoji = emojiData.emoji; const textarea = textareaRef.current; if (textarea) { const start = textarea.selectionStart; const end = textarea.selectionEnd; const newMessage = message.substring(0, start) + emoji + message.substring(end); setMessage(newMessage); setCharacterCount(newMessage.length); // Set cursor position after emoji setTimeout(() => { textarea.selectionStart = textarea.selectionEnd = start + emoji.length; textarea.focus(); }, 0); } setShowEmojiPicker(false); }, [message]); // Handle media upload const handleMediaUpload = useCallback((mediaData: any) => { setAttachedMedia(mediaData); setMessageType(mediaData.type); }, []); // Handle template selection const handleTemplateSelect = useCallback((template: any) => { setMessage(template.content); setCharacterCount(template.content.length); setShowTemplates(false); if (textareaRef.current) { textareaRef.current.focus(); } }, []); // Handle quick reply selection const handleQuickReplySelect = useCallback((quickReply: any) => { setMessage(quickReply.text); setCharacterCount(quickReply.text.length); setShowQuickReplies(false); if (textareaRef.current) { textareaRef.current.focus(); } }, []); // Remove attached media const handleRemoveMedia = useCallback(() => { setAttachedMedia(null); setMessageType('text'); }, []); // Check if send button should be enabled const canSend = useMemo(() => { return !disabled && (message.trim().length > 0 || attachedMedia) && characterCount <= currentLimit; }, [disabled, message, attachedMedia, characterCount, currentLimit]); // Get platform-specific features const platformFeatures = useMemo(() => { return platformConfig?.features || {}; }, [platformConfig]); // Cleanup typing timeout on unmount useEffect(() => { return () => { if (typingTimeoutRef.current) { clearTimeout(typingTimeoutRef.current); } }; }, []); return ( <div className={`message-composer ${disabled ? 'disabled' : ''}`}> {/* Quick Replies */} {showQuickReplies && ( <div className="quick-replies-panel"> {/* <QuickReplies platform={conversation.platform} onSelect={handleQuickReplySelect} onClose={() => setShowQuickReplies(false)} /> */} <div>Quick Replies (Component not implemented)</div> </div> )} {/* Templates */} {showTemplates && ( <div className="templates-panel"> {/* <TemplateSelector platform={conversation.platform} onSelect={handleTemplateSelect} onClose={() => setShowTemplates(false)} /> */} <div>Template Selector (Component not implemented)</div> </div> )} {/* Attached Media Preview */} {attachedMedia && ( <div className="attached-media-preview"> <div className="media-preview"> {attachedMedia.type === 'image' && ( <img src={attachedMedia.preview} alt="Aperçu" /> )} {attachedMedia.type === 'video' && ( <video src={attachedMedia.preview} controls /> )} {attachedMedia.type === 'audio' && ( <audio src={attachedMedia.preview} controls /> )} {attachedMedia.type === 'document' && ( <div className="document-preview"> <div className="document-icon"></div> <span className="document-name">{attachedMedia.filename}</span> </div> )} </div> <button className="remove-media-button" onClick={handleRemoveMedia} title="Supprimer le média" > </button> </div> )} {/* Main Composer */} <div className="composer-main"> {/* Toolbar */} <div className="composer-toolbar"> {/* Media Upload */} {platformFeatures.media && ( <button className="toolbar-button media-button" onClick={() => console.log('Media upload not implemented')} disabled={disabled} title="Upload Media" > </button> /* <MediaUploader platform={conversation.platform} onUpload={handleMediaUpload} disabled={disabled} /> */ )} {/* Emoji Picker */} <div className="emoji-picker-container"> <button className={`toolbar-button emoji-button ${showEmojiPicker ? 'active' : ''}`} onClick={() => setShowEmojiPicker(!showEmojiPicker)} title="Émojis" disabled={disabled} > </button> {showEmojiPicker && ( <div className="emoji-picker-popup"> <EmojiPicker onEmojiClick={handleEmojiSelect} width={300} height={400} /> </div> )} </div> {/* Templates */} <button className={`toolbar-button templates-button ${showTemplates ? 'active' : ''}`} onClick={() => setShowTemplates(!showTemplates)} title="Modèles" disabled={disabled} > </button> {/* Quick Replies */} <button className={`toolbar-button quick-replies-button ${showQuickReplies ? 'active' : ''}`} onClick={() => setShowQuickReplies(!showQuickReplies)} title="Réponses rapides" disabled={disabled} > [PERFORMANCE] </button> {/* Platform-specific features */} {platformFeatures.location && ( <button className="toolbar-button location-button" onClick={() => { // Handle location sharing }} title="Partager la position" disabled={disabled} > </button> )} {platformFeatures.contacts && ( <button className="toolbar-button contact-button" onClick={() => { // Handle contact sharing }} title="Partager un contact" disabled={disabled} > [USER] </button> )} </div> {/* Text Input */} <div className="composer-input"> <textarea ref={textareaRef} value={message} onChange={handleMessageChange} onKeyDown={handleKeyPress} placeholder={disabled ? 'Conversation fermée' : 'Tapez votre message...'} disabled={disabled} rows={1} maxLength={currentLimit} className={characterCount > currentLimit * 0.9 ? 'near-limit' : ''} /> {/* Character Counter */} <div className={`character-counter ${characterCount > currentLimit * 0.9 ? 'warning' : ''}`}> {characterCount}/{currentLimit} </div> </div> {/* Send Button */} <button className={`send-button ${canSend ? 'enabled' : 'disabled'}`} onClick={handleSendMessage} disabled={!canSend} title="Envoyer (Ctrl+Entrée)" > <span className="send-icon"></span> </button> </div> {/* Platform Indicator */} <div className="platform-indicator"> <span className="platform-icon" style={{ color: platformConfig?.color }}> {platformConfig?.icon} </span> <span className="platform-name">{platformConfig?.name}</span> </div> {/* Typing Indicator */} {isTyping && ( <div className="typing-indicator-self"> <span>En cours de frappe...</span> </div> )} </div> ); }; export default MessageComposer;