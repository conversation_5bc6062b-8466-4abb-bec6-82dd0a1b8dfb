/** * ============================================= * [PERFORMANCE] MESSAGE ACTIONS COMPONENT * Quick action buttons for messages (reply, forward, etc.) * Context-sensitive actions based on message type * ============================================= */ import React, { useState, useCallback } from 'react'; import './MessageActions.css'; interface MessageActionsProps { message: { id: string; type: string; text?: string; media?: any[]; metadata?: any; }; platform: string; isOwn: boolean; visible: boolean; onReply?: (messageId: string) => void; onForward?: (messageId: string) => void; onDelete?: (messageId: string) => void; onCopy?: (text: string) => void; onDownload?: (mediaUrl: string, filename?: string) => void; onTranslate?: (messageId: string) => void; onFlag?: (messageId: string) => void; } const MessageActions: React.FC<MessageActionsProps> = ({ message, platform, isOwn, visible, onReply, onForward, onDelete, onCopy, onDownload, onTranslate, onFlag }) => { const [showTooltip, setShowTooltip] = useState<string | null>(null); const handleReply = useCallback(() => { onReply?.(message.id); }, [onReply, message.id]); const handleForward = useCallback(() => { onForward?.(message.id); }, [onForward, message.id]); const handleDelete = useCallback(() => { if (window.confirm('Êtes-vous sûr de vouloir supprimer ce message ?')) { onDelete?.(message.id); } }, [onDelete, message.id]); const handleCopy = useCallback(() => { if (message.text) { navigator.clipboard.writeText(message.text).then(() => { onCopy?.(message.text!); }); } }, [onCopy, message.text]); const handleDownload = useCallback(() => { if (message.media && message.media.length > 0) { const media = message.media[0]; onDownload?.(media.url, media.filename); } }, [onDownload, message.media]); const handleTranslate = useCallback(() => { onTranslate?.(message.id); }, [onTranslate, message.id]); const handleFlag = useCallback(() => { onFlag?.(message.id); }, [onFlag, message.id]); const showTooltipHandler = useCallback((action: string) => { setShowTooltip(action); }, []); const hideTooltipHandler = useCallback(() => { setShowTooltip(null); }, []); if (!visible) return null; const hasText = message.text && message.text.trim().length > 0; const hasMedia = message.media && message.media.length > 0; const canReply = onReply && !isOwn; const canForward = onForward; const canDelete = onDelete && isOwn; const canCopy = onCopy && hasText; const canDownload = onDownload && hasMedia; const canTranslate = onTranslate && hasText; const canFlag = onFlag && !isOwn; return ( <div className={`message-actions ${platform} ${isOwn ? 'own' : 'other'}`}> <div className="actions-container"> {/* Reply Action */} {canReply && ( <button className="action-button reply" onClick={handleReply} onMouseEnter={() => showTooltipHandler('reply')} onMouseLeave={hideTooltipHandler} title="Répondre" > ↩ {showTooltip === 'reply' && ( <div className="action-tooltip">Répondre</div> )} </button> )} {/* Forward Action */} {canForward && ( <button className="action-button forward" onClick={handleForward} onMouseEnter={() => showTooltipHandler('forward')} onMouseLeave={hideTooltipHandler} title="Transférer" > {showTooltip === 'forward' && ( <div className="action-tooltip">Transférer</div> )} </button> )} {/* Copy Action */} {canCopy && ( <button className="action-button copy" onClick={handleCopy} onMouseEnter={() => showTooltipHandler('copy')} onMouseLeave={hideTooltipHandler} title="Copier le texte" > {showTooltip === 'copy' && ( <div className="action-tooltip">Copier</div> )} </button> )} {/* Download Action */} {canDownload && ( <button className="action-button download" onClick={handleDownload} onMouseEnter={() => showTooltipHandler('download')} onMouseLeave={hideTooltipHandler} title="Télécharger" > ⬇ {showTooltip === 'download' && ( <div className="action-tooltip">Télécharger</div> )} </button> )} {/* Translate Action */} {canTranslate && ( <button className="action-button translate" onClick={handleTranslate} onMouseEnter={() => showTooltipHandler('translate')} onMouseLeave={hideTooltipHandler} title="Traduire" > {showTooltip === 'translate' && ( <div className="action-tooltip">Traduire</div> )} </button> )} {/* Flag Action */} {canFlag && ( <button className="action-button flag" onClick={handleFlag} onMouseEnter={() => showTooltipHandler('flag')} onMouseLeave={hideTooltipHandler} title="Signaler" > {showTooltip === 'flag' && ( <div className="action-tooltip">Signaler</div> )} </button> )} {/* Delete Action */} {canDelete && ( <button className="action-button delete" onClick={handleDelete} onMouseEnter={() => showTooltipHandler('delete')} onMouseLeave={hideTooltipHandler} title="Supprimer" > {showTooltip === 'delete' && ( <div className="action-tooltip">Supprimer</div> )} </button> )} </div> </div> ); }; export default MessageActions;