/** * ============================================= * MEDIA PREVIEW COMPONENT * Displays media content with platform-specific styling * Supports images, videos, audio, and documents * ============================================= */ import React, { useState, useCallback } from 'react'; import './MediaPreview.css'; interface MediaItem { id: string; url: string; filename?: string; size?: number; mimeType?: string; thumbnail?: string; duration?: number; } interface MediaPreviewProps { media: MediaItem; type: 'image' | 'video' | 'audio' | 'document'; platform?: string; expanded?: boolean; onToggleExpand?: () => void; } const MediaPreview: React.FC<MediaPreviewProps> = ({ media, type, platform = 'whatsapp', expanded = false, onToggleExpand }) => { const [loading, setLoading] = useState(true); const [error, setError] = useState(false); const handleLoad = useCallback(() => { setLoading(false); }, []); const handleError = useCallback(() => { setLoading(false); setError(true); }, []); const formatFileSize = (bytes?: number): string => { if (!bytes) return ''; const sizes = ['B', 'KB', 'MB', 'GB']; const i = Math.floor(Math.log(bytes) / Math.log(1024)); return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`; }; const formatDuration = (seconds?: number): string => { if (!seconds) return ''; const mins = Math.floor(seconds / 60); const secs = Math.floor(seconds % 60); return `${mins}:${secs.toString().padStart(2, '0')}`; }; const renderImagePreview = () => ( <div className={`media-preview image-preview ${platform} ${expanded ? 'expanded' : ''}`}> {loading && ( <div className="media-loading"> <div className="loading-spinner"></div> </div> )} {error ? ( <div className="media-error"> <div className="error-icon"></div> <span>Image non disponible</span> </div> ) : ( <img src={media.url} alt={media.filename || 'Image'} onLoad={handleLoad} onError={handleError} onClick={onToggleExpand} style={{ display: loading ? 'none' : 'block' }} /> )} {onToggleExpand && !error && ( <button className="expand-button" onClick={onToggleExpand}> {expanded ? '' : '[SEARCH]'} </button> )} </div> ); const renderVideoPreview = () => ( <div className={`media-preview video-preview ${platform} ${expanded ? 'expanded' : ''}`}> {loading && ( <div className="media-loading"> <div className="loading-spinner"></div> </div> )} {error ? ( <div className="media-error"> <div className="error-icon"></div> <span>Vidéo non disponible</span> </div> ) : ( <video src={media.url} controls onLoadedData={handleLoad} onError={handleError} style={{ display: loading ? 'none' : 'block' }} poster={media.thumbnail} /> )} {media.duration && ( <div className="media-duration"> {formatDuration(media.duration)} </div> )} {onToggleExpand && !error && ( <button className="expand-button" onClick={onToggleExpand}> {expanded ? '' : '[SEARCH]'} </button> )} </div> ); const renderAudioPreview = () => ( <div className={`media-preview audio-preview ${platform}`}> <div className="audio-container"> <div className="audio-icon"></div> <div className="audio-info"> <div className="audio-filename"> {media.filename || 'Audio'} </div> {media.duration && ( <div className="audio-duration"> {formatDuration(media.duration)} </div> )} </div> </div> {error ? ( <div className="media-error"> <span>Audio non disponible</span> </div> ) : ( <audio src={media.url} controls onLoadedData={handleLoad} onError={handleError} /> )} </div> ); const renderDocumentPreview = () => ( <div className={`media-preview document-preview ${platform}`}> <div className="document-container"> <div className="document-icon"></div> <div className="document-info"> <div className="document-filename"> {media.filename || 'Document'} </div> {media.size && ( <div className="document-size"> {formatFileSize(media.size)} </div> )} </div> <button className="download-button" onClick={() => window.open(media.url, '_blank')} > ⬇ </button> </div> </div> ); switch (type) { case 'image': return renderImagePreview(); case 'video': return renderVideoPreview(); case 'audio': return renderAudioPreview(); case 'document': return renderDocumentPreview(); default: return ( <div className="media-preview unknown-preview"> <div className="error-icon"></div> <span>Type de média non supporté</span> </div> ); } }; export default MediaPreview;