/** * ============================================= * MESSAGE BUBBLE * Platform-specific message display with media support * Interactive elements and status indicators * ============================================= */ import React, { useState, useMemo, useCallback } from 'react'; import { format, isToday, isYesterday } from 'date-fns'; import { fr } from 'date-fns/locale'; import MediaPreview from './MediaPreview'; import InteractiveMessage from './InteractiveMessage'; import MessageActions from './MessageActions'; import './MessageBubble.css'; interface MessageBubbleProps { message: { id: string; type: string; text?: string; timestamp: string | Date; media?: any[]; metadata?: any; interactive?: any; status?: string; sentiment?: string; priority?: string; direction?: string; sender?: any; analysis?: any; }; platform: string; platformConfig?: any; isOwn: boolean; customer?: { id: string; name: string; avatar?: string; profilePic?: string; }; agent?: { id: string; name: string; avatar?: string; profilePic?: string; }; onSelect: (message: any) => void; isSelected: boolean; } const MessageBubble: React.FC<MessageBubbleProps> = ({ message, platform, platformConfig, isOwn, customer, agent, onSelect, isSelected }) => { const [showActions, setShowActions] = useState(false); const [mediaExpanded, setMediaExpanded] = useState(false); // Format message timestamp const formattedTime = useMemo(() => { const timestamp = new Date(message.timestamp); if (isToday(timestamp)) { return format(timestamp, 'HH:mm'); } else if (isYesterday(timestamp)) { return `Hier ${format(timestamp, 'HH:mm')}`; } else { return format(timestamp, 'dd/MM/yyyy HH:mm', { locale: fr }); } }, [message.timestamp]); // Get message status icon const getStatusIcon = useCallback(() => { if (message.direction === 'inbound') return null; switch (message.status) { case 'sending': return <span className="status-icon sending" title="Envoi en cours">⏳</span>; case 'sent': return <span className="status-icon sent" title="Envoyé"></span>; case 'delivered': return <span className="status-icon delivered" title="Livré"></span>; case 'read': return <span className="status-icon read" title="Lu"></span>; case 'failed': return <span className="status-icon failed" title="Échec">[FAILED]</span>; default: return null; } }, [message.direction, message.status]); // Get platform-specific styling const getPlatformStyling = useMemo(() => { const styles: Record<string, { inbound: any; outbound: any }> = { whatsapp: { inbound: { backgroundColor: '#ffffff', color: '#000000' }, outbound: { backgroundColor: '#dcf8c6', color: '#000000' } }, facebook: { inbound: { backgroundColor: '#f1f3f4', color: '#000000' }, outbound: { backgroundColor: '#0084ff', color: '#ffffff' } }, instagram: { inbound: { backgroundColor: '#f1f3f4', color: '#000000' }, outbound: { backgroundColor: '#e4405f', color: '#ffffff' } }, twitter: { inbound: { backgroundColor: '#f7f9fa', color: '#000000' }, outbound: { backgroundColor: '#1da1f2', color: '#ffffff' } } }; const platformStyle = styles[platform] || styles.whatsapp; return isOwn ? platformStyle.outbound : platformStyle.inbound; }, [platform, isOwn]); // Handle message click const handleMessageClick = useCallback(() => { onSelect(message); }, [onSelect, message]); // Render message content based on type const renderMessageContent = () => { switch (message.type) { case 'text': return ( <div className="message-text"> {message.text} </div> ); case 'image': return ( <div className="message-media"> {message.media && message.media[0] && ( <MediaPreview media={message.media[0]} type="image" platform={platform} expanded={mediaExpanded} onToggleExpand={() => setMediaExpanded(!mediaExpanded)} /> )} {message.text && ( <div className="media-caption">{message.text}</div> )} </div> ); case 'video': return ( <div className="message-media"> {message.media && message.media[0] && ( <MediaPreview media={message.media[0]} type="video" platform={platform} expanded={mediaExpanded} onToggleExpand={() => setMediaExpanded(!mediaExpanded)} /> )} {message.text && ( <div className="media-caption">{message.text}</div> )} </div> ); case 'audio': return ( <div className="message-media"> {message.media && message.media[0] && ( <MediaPreview media={message.media[0]} type="audio" platform={platform} /> )} </div> ); case 'document': return ( <div className="message-media"> {message.media && message.media[0] && ( <MediaPreview media={message.media[0]} type="document" platform={platform} /> )} {message.text && ( <div className="media-caption">{message.text}</div> )} </div> ); case 'location': return ( <div className="message-location"> <div className="location-icon"></div> <div className="location-info"> <div className="location-name"> {message.metadata?.location?.name || 'Position partagée'} </div> {message.metadata?.location?.address && ( <div className="location-address"> {message.metadata.location.address} </div> )} </div> </div> ); case 'contact': return ( <div className="message-contact"> <div className="contact-icon">[USER]</div> <div className="contact-info"> <div className="contact-name"> {message.metadata?.contacts?.[0]?.name?.formatted_name || 'Contact partagé'} </div> {message.metadata?.contacts?.[0]?.phones?.[0] && ( <div className="contact-phone"> {message.metadata.contacts[0].phones[0].phone} </div> )} </div> </div> ); case 'interactive': return ( <InteractiveMessage message={message} platform={platform} platformConfig={platformConfig} /> ); case 'sticker': return ( <div className="message-sticker"> <div className="sticker-placeholder"></div> <div className="sticker-text">Sticker</div> </div> ); case 'story_reply': return ( <div className="message-story-reply"> <div className="story-context"> <div className="story-icon"></div> <div className="story-text">Réponse à une story</div> </div> {message.text && ( <div className="reply-text">{message.text}</div> )} </div> ); case 'story_mention': return ( <div className="message-story-mention"> <div className="mention-context"> <div className="mention-icon"></div> <div className="mention-text">Mention dans une story</div> </div> {message.text && ( <div className="mention-text">{message.text}</div> )} </div> ); default: return ( <div className="message-text"> {message.text || 'Message non supporté'} </div> ); } }; // Render sender info const renderSenderInfo = () => { if (isOwn) { return ( <div className="sender-info own"> <span className="sender-name">Vous</span> {agent?.name && agent.name !== 'Vous' && ( <span className="agent-name">({agent.name})</span> )} </div> ); } else { return ( <div className="sender-info customer"> <div className="customer-avatar"> {customer?.profilePic ? ( <img src={customer.profilePic} alt={customer.name} /> ) : ( <div className="avatar-fallback"> {(customer?.name || 'C').charAt(0).toUpperCase()} </div> )} </div> <span className="sender-name"> {customer?.name || 'Client'} </span> </div> ); } }; // Render analysis indicators const renderAnalysisIndicators = () => { if (!message.analysis || isOwn) return null; return ( <div className="analysis-indicators"> {message.analysis.sentiment && ( <span className={`sentiment-indicator ${message.analysis.sentiment.label}`} title={`Sentiment: ${message.analysis.sentiment.label} (${Math.round(message.analysis.sentiment.confidence * 100)}%)`} > {message.analysis.sentiment.label === 'very_positive' && ''} {message.analysis.sentiment.label === 'positive' && ''} {message.analysis.sentiment.label === 'neutral' && ''} {message.analysis.sentiment.label === 'negative' && ''} {message.analysis.sentiment.label === 'very_negative' && ''} </span> )} {message.analysis.urgency && message.analysis.urgency.level !== 'medium' && ( <span className={`urgency-indicator ${message.analysis.urgency.level}`} title={`Urgence: ${message.analysis.urgency.level}`} > {message.analysis.urgency.level === 'urgent' && ''} {message.analysis.urgency.level === 'high' && '[PERFORMANCE]'} {message.analysis.urgency.level === 'low' && ''} </span> )} {message.analysis.intent && ( <span className="intent-indicator" title={`Intention: ${message.analysis.intent.primary}`} > [TARGET] </span> )} </div> ); }; return ( <div className={`message-bubble ${isOwn ? 'own' : 'other'} ${platform} ${isSelected ? 'selected' : ''}`} onClick={handleMessageClick} onMouseEnter={() => setShowActions(true)} onMouseLeave={() => setShowActions(false)} > {/* Sender Info (for incoming messages) */} {!isOwn && renderSenderInfo()} {/* Message Content */} <div className="message-content" style={getPlatformStyling()} > {renderMessageContent()} {/* Message Footer */} <div className="message-footer"> <span className="message-time">{formattedTime}</span> {getStatusIcon()} </div> {/* Analysis Indicators */} {renderAnalysisIndicators()} </div> {/* Sender Info (for outgoing messages) */} {isOwn && renderSenderInfo()} {/* Message Actions */} {showActions && ( <MessageActions message={message} platform={platform} isOwn={isOwn} visible={showActions} onReply={() => {}} onForward={() => {}} onDelete={() => {}} onFlag={() => {}} /> )} {/* Platform-specific indicators */} <div className="platform-indicators"> {/* WhatsApp specific */} {platform === 'whatsapp' && message.metadata?.whatsapp?.context?.forwarded && ( <div className="forwarded-indicator" title="Message transféré"> ↪ Transféré </div> )} {/* Facebook specific */} {platform === 'facebook' && message.metadata?.facebook?.isEcho && ( <div className="echo-indicator" title="Écho du message"> Écho </div> )} {/* Instagram specific */} {platform === 'instagram' && message.metadata?.instagram?.storyReply && ( <div className="story-reply-indicator" title="Réponse à une story"> Story </div> )} {/* Twitter specific */} {platform === 'twitter' && message.metadata?.twitter?.isRetweet && ( <div className="retweet-indicator" title="Retweet"> RT </div> )} </div> </div> ); }; export default MessageBubble;