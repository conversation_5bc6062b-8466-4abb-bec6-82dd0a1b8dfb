/**
 * =============================================
 * 🎯 INTERACTIVE MESSAGE STYLES
 * Platform-specific interactive element styling
 * =============================================
 */

.interactive-message {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e0e0e0;
  max-width: 300px;
}

/* Header Styles */
.interactive-header {
  border-bottom: 1px solid #f0f0f0;
}

.header-text {
  padding: 12px;
  font-weight: 600;
  color: #333;
  background: #f8f9fa;
}

.header-media {
  width: 100%;
  height: auto;
  max-height: 200px;
  object-fit: cover;
}

.header-document {
  display: flex;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
}

.document-icon {
  font-size: 24px;
  margin-right: 8px;
}

/* Body Styles */
.interactive-body {
  padding: 12px;
}

.body-text {
  color: #333;
  line-height: 1.4;
  margin: 0;
}

/* Footer Styles */
.interactive-footer {
  padding: 8px 12px;
  border-top: 1px solid #f0f0f0;
  background: #f8f9fa;
}

.footer-text {
  font-size: 12px;
  color: #666;
  margin: 0;
}

/* Button Interactive */
.interactive-buttons {
  display: flex;
  flex-direction: column;
  border-top: 1px solid #f0f0f0;
}

.interactive-button {
  padding: 12px;
  border: none;
  background: white;
  color: #007bff;
  cursor: pointer;
  text-align: center;
  font-weight: 500;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #f0f0f0;
}

.interactive-button:last-child {
  border-bottom: none;
}

.interactive-button:hover {
  background: #f8f9fa;
}

.interactive-button.reply {
  color: #007bff;
}

.interactive-button.url {
  color: #28a745;
}

.interactive-button.call {
  color: #dc3545;
}

/* List Interactive */
.interactive-list {
  border-top: 1px solid #f0f0f0;
}

.list-button {
  width: 100%;
  padding: 12px;
  border: none;
  background: #007bff;
  color: white;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.list-button:hover {
  background: #0056b3;
}

.list-sections {
  max-height: 200px;
  overflow-y: auto;
}

.list-section {
  border-bottom: 1px solid #f0f0f0;
}

.list-section:last-child {
  border-bottom: none;
}

.section-title {
  padding: 8px 12px;
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
  font-size: 14px;
  border-bottom: 1px solid #e0e0e0;
}

.section-rows {
  background: white;
}

.list-row {
  padding: 12px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.list-row:last-child {
  border-bottom: none;
}

.list-row:hover {
  background: #f8f9fa;
}

.list-row.selected {
  background: #e3f2fd;
  border-left: 3px solid #007bff;
}

.row-title {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.row-description {
  font-size: 12px;
  color: #666;
  line-height: 1.3;
}

/* Template Interactive */
.interactive-template {
  border-top: 1px solid #f0f0f0;
  padding: 12px;
  background: #f8f9fa;
}

.template-content {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
}

.template-icon {
  font-size: 24px;
  margin-right: 8px;
}

.template-text {
  font-style: italic;
}

/* Error and Unknown States */
.interactive-message-error,
.interactive-unknown {
  padding: 12px;
  text-align: center;
  color: #666;
  font-style: italic;
  background: #f8f9fa;
}

/* Platform-specific styles */
.interactive-message.whatsapp {
  border-radius: 8px;
  max-width: 280px;
}

.interactive-message.whatsapp .interactive-button {
  border-radius: 0;
}

.interactive-message.messenger {
  border-radius: 12px;
  max-width: 320px;
}

.interactive-message.messenger .interactive-button {
  border-radius: 20px;
  margin: 4px 8px;
}

.interactive-message.instagram {
  border-radius: 4px;
  max-width: 300px;
}

.interactive-message.twitter {
  border-radius: 16px;
  max-width: 350px;
}

/* Responsive design */
@media (max-width: 768px) {
  .interactive-message {
    max-width: 250px;
  }
  
  .interactive-message.messenger {
    max-width: 270px;
  }
  
  .interactive-message.twitter {
    max-width: 280px;
  }
}
