/** * ============================================= * [TARGET] INTERACTIVE MESSAGE COMPONENT * Handles interactive message types like buttons, lists, etc. * Platform-specific interactive elements * ============================================= */ import React, { useState, useCallback } from 'react'; import './InteractiveMessage.css'; interface InteractiveButton { id: string; title: string; type?: 'reply' | 'url' | 'call'; url?: string; phone?: string; } interface InteractiveListItem { id: string; title: string; description?: string; } interface InteractiveSection { title?: string; rows: InteractiveListItem[]; } interface InteractiveMessageData { type: 'button' | 'list' | 'template'; header?: { type: 'text' | 'image' | 'video' | 'document'; text?: string; media?: string; }; body: { text: string; }; footer?: { text: string; }; action?: { buttons?: InteractiveButton[]; button?: string; sections?: InteractiveSection[]; }; } interface InteractiveMessageProps { message: { id: string; type: string; interactive?: InteractiveMessageData; metadata?: any; }; platform: string; platformConfig?: any; onButtonClick?: (buttonId: string, buttonData: any) => void; onListItemClick?: (itemId: string, itemData: any) => void; } const InteractiveMessage: React.FC<InteractiveMessageProps> = ({ message, platform, platformConfig, onButtonClick, onListItemClick }) => { const [selectedItem, setSelectedItem] = useState<string | null>(null); const interactive = message.interactive; if (!interactive) { return ( <div className="interactive-message-error"> <span>Message interactif non disponible</span> </div> ); } const handleButtonClick = useCallback((button: InteractiveButton) => { if (button.type === 'url' && button.url) { window.open(button.url, '_blank'); } else if (button.type === 'call' && button.phone) { window.open(`tel:${button.phone}`, '_self'); } else { onButtonClick?.(button.id, button); } }, [onButtonClick]); const handleListItemClick = useCallback((item: InteractiveListItem) => { setSelectedItem(item.id); onListItemClick?.(item.id, item); }, [onListItemClick]); const renderHeader = () => { if (!interactive.header) return null; const { header } = interactive; return ( <div className="interactive-header"> {header.type === 'text' && header.text && ( <div className="header-text">{header.text}</div> )} {header.type === 'image' && header.media && ( <img src={header.media} alt="Header" className="header-media" /> )} {header.type === 'video' && header.media && ( <video src={header.media} controls className="header-media" /> )} {header.type === 'document' && header.media && ( <div className="header-document"> <div className="document-icon"></div> <span>Document</span> </div> )} </div> ); }; const renderBody = () => ( <div className="interactive-body"> <div className="body-text">{interactive.body.text}</div> </div> ); const renderFooter = () => { if (!interactive.footer?.text) return null; return ( <div className="interactive-footer"> <div className="footer-text">{interactive.footer.text}</div> </div> ); }; const renderButtons = () => { if (!interactive.action?.buttons) return null; return ( <div className="interactive-buttons"> {interactive.action.buttons.map((button) => ( <button key={button.id} className={`interactive-button ${button.type || 'reply'} ${platform}`} onClick={() => handleButtonClick(button)} > {button.type === 'url' && ' '} {button.type === 'call' && ' '} {button.title} </button> ))} </div> ); }; const renderList = () => { if (!interactive.action?.sections) return null; return ( <div className="interactive-list"> <button className="list-button"> {interactive.action.button || 'Voir les options'} </button> <div className="list-sections"> {interactive.action.sections.map((section, sectionIndex) => ( <div key={sectionIndex} className="list-section"> {section.title && ( <div className="section-title">{section.title}</div> )} <div className="section-rows"> {section.rows.map((row) => ( <div key={row.id} className={`list-row ${selectedItem === row.id ? 'selected' : ''}`} onClick={() => handleListItemClick(row)} > <div className="row-title">{row.title}</div> {row.description && ( <div className="row-description">{row.description}</div> )} </div> ))} </div> </div> ))} </div> </div> ); }; const renderTemplate = () => ( <div className="interactive-template"> <div className="template-content"> <div className="template-icon"></div> <div className="template-text">Template interactif</div> </div> </div> ); const renderInteractiveContent = () => { switch (interactive.type) { case 'button': return renderButtons(); case 'list': return renderList(); case 'template': return renderTemplate(); default: return ( <div className="interactive-unknown"> <span>Type interactif non supporté: {interactive.type}</span> </div> ); } }; return ( <div className={`interactive-message ${interactive.type} ${platform}`}> {renderHeader()} {renderBody()} {renderFooter()} {renderInteractiveContent()} </div> ); }; export default InteractiveMessage;