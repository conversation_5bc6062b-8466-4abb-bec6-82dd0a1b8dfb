/**
 * =============================================
 * ⚡ MESSAGE ACTIONS STYLES
 * Quick action buttons styling
 * =============================================
 */

.message-actions {
  position: absolute;
  top: -8px;
  z-index: 10;
  opacity: 0;
  transform: translateY(-5px);
  transition: all 0.2s ease;
  pointer-events: none;
}

.message-actions.own {
  left: -120px;
}

.message-actions.other {
  right: -120px;
}

.message-actions:hover,
.message-actions.visible {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.actions-container {
  display: flex;
  background: white;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  padding: 4px;
  border: 1px solid #e0e0e0;
}

.action-button {
  position: relative;
  background: none;
  border: none;
  padding: 8px;
  margin: 0 2px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.action-button:hover {
  background: #f0f0f0;
  transform: scale(1.1);
}

.action-button:active {
  transform: scale(0.95);
}

/* Specific action button styles */
.action-button.reply:hover {
  background: #e3f2fd;
  color: #1976d2;
}

.action-button.forward:hover {
  background: #f3e5f5;
  color: #7b1fa2;
}

.action-button.copy:hover {
  background: #e8f5e8;
  color: #388e3c;
}

.action-button.download:hover {
  background: #fff3e0;
  color: #f57c00;
}

.action-button.translate:hover {
  background: #e0f2f1;
  color: #00796b;
}

.action-button.flag:hover {
  background: #ffebee;
  color: #d32f2f;
}

.action-button.delete:hover {
  background: #ffebee;
  color: #d32f2f;
}

/* Tooltip styles */
.action-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  margin-bottom: 4px;
  pointer-events: none;
  z-index: 20;
}

.action-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 4px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.8);
}

/* Platform-specific styles */
.message-actions.whatsapp .actions-container {
  background: #dcf8c6;
  border-color: #4fc3f7;
}

.message-actions.whatsapp.other .actions-container {
  background: white;
  border-color: #e0e0e0;
}

.message-actions.messenger .actions-container {
  background: #0084ff;
  border-color: #0084ff;
}

.message-actions.messenger .action-button {
  color: white;
}

.message-actions.messenger .action-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

.message-actions.instagram .actions-container {
  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  border: none;
}

.message-actions.instagram .action-button {
  color: white;
}

.message-actions.instagram .action-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

.message-actions.twitter .actions-container {
  background: #1da1f2;
  border-color: #1da1f2;
}

.message-actions.twitter .action-button {
  color: white;
}

.message-actions.twitter .action-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Animation for showing/hiding */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeOutDown {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(10px);
  }
}

.message-actions.visible .actions-container {
  animation: fadeInUp 0.2s ease;
}

/* Responsive design */
@media (max-width: 768px) {
  .message-actions.own {
    left: -100px;
  }
  
  .message-actions.other {
    right: -100px;
  }
  
  .actions-container {
    padding: 2px;
  }
  
  .action-button {
    width: 28px;
    height: 28px;
    font-size: 12px;
    margin: 0 1px;
  }
  
  .action-tooltip {
    font-size: 10px;
    padding: 2px 6px;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .actions-container {
    border: 2px solid #000;
    background: #fff;
  }
  
  .action-button {
    border: 1px solid #000;
  }
  
  .action-tooltip {
    background: #000;
    border: 1px solid #fff;
  }
}
