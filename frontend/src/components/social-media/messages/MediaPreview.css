/**
 * =============================================
 * 🎬 MEDIA PREVIEW STYLES
 * Platform-specific media display styling
 * =============================================
 */

.media-preview {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  background: #f5f5f5;
  max-width: 100%;
}

/* Loading State */
.media-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 120px;
  background: #f0f0f0;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #e0e0e0;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error State */
.media-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 120px;
  color: #666;
  background: #f8f8f8;
}

.error-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

/* Image Preview */
.image-preview img {
  width: 100%;
  max-width: 300px;
  height: auto;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.image-preview.expanded img {
  max-width: 500px;
  transform: scale(1.05);
}

/* Video Preview */
.video-preview video {
  width: 100%;
  max-width: 300px;
  height: auto;
}

.video-preview.expanded video {
  max-width: 500px;
}

.media-duration {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

/* Audio Preview */
.audio-preview {
  padding: 12px;
  background: white;
  border: 1px solid #e0e0e0;
}

.audio-container {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.audio-icon {
  font-size: 24px;
  margin-right: 12px;
}

.audio-info {
  flex: 1;
}

.audio-filename {
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
}

.audio-duration {
  font-size: 12px;
  color: #666;
}

.audio-preview audio {
  width: 100%;
  height: 32px;
}

/* Document Preview */
.document-preview {
  padding: 12px;
  background: white;
  border: 1px solid #e0e0e0;
}

.document-container {
  display: flex;
  align-items: center;
}

.document-icon {
  font-size: 24px;
  margin-right: 12px;
}

.document-info {
  flex: 1;
}

.document-filename {
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
}

.document-size {
  font-size: 12px;
  color: #666;
}

.download-button {
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.2s ease;
}

.download-button:hover {
  background: #0056b3;
}

/* Expand Button */
.expand-button {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.expand-button:hover {
  background: rgba(0, 0, 0, 0.7);
}

/* Platform-specific styles */
.media-preview.whatsapp {
  border-radius: 8px;
}

.media-preview.messenger {
  border-radius: 12px;
}

.media-preview.instagram {
  border-radius: 4px;
}

.media-preview.twitter {
  border-radius: 16px;
}

/* Unknown media type */
.unknown-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 120px;
  color: #666;
  background: #f8f8f8;
}

/* Responsive design */
@media (max-width: 768px) {
  .image-preview img,
  .video-preview video {
    max-width: 250px;
  }
  
  .image-preview.expanded img,
  .video-preview.expanded video {
    max-width: 300px;
  }
}
