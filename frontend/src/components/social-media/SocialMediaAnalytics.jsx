/** * ============================================= * [ANALYTICS] SOCIAL MEDIA ANALYTICS * Real-time analytics dashboard for social media conversations * Interactive charts with platform breakdown and performance metrics * ============================================= */ import React, { useState, useEffect, useMemo, useCallback } from 'react'; import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, BarElement, Title, Tooltip, Legend, ArcElement } from 'chart.js'; import { Line, Bar, Doughnut } from 'react-chartjs-2'; import { format, subDays, startOfDay, endOfDay } from 'date-fns'; import { fr } from 'date-fns/locale'; import './SocialMediaAnalytics.css'; // Register Chart.js components ChartJS.register( CategoryScale, LinearScale, PointElement, LineElement, BarElement, Title, Tooltip, Legend, ArcElement ); const SocialMediaAnalytics = ({ metrics, conversations, onClose }) => { const [timeRange, setTimeRange] = useState('today'); const [selectedMetric, setSelectedMetric] = useState('conversations'); const [refreshInterval, setRefreshInterval] = useState(30000); // 30 seconds const [lastUpdated, setLastUpdated] = useState(new Date()); // Time range options const timeRanges = [ { value: 'today', label: "Aujourd'hui" }, { value: 'yesterday', label: 'Hier' }, { value: '7days', label: '7 derniers jours' }, { value: '30days', label: '30 derniers jours' }, { value: 'custom', label: 'Personnalisé' } ]; // Metric options const metricOptions = [ { value: 'conversations', label: 'Conversations', icon: '' }, { value: 'messages', label: 'Messages', icon: '' }, { value: 'response_time', label: 'Temps de réponse', icon: '⏱' }, { value: 'satisfaction', label: 'Satisfaction', icon: '⭐' }, { value: 'resolution', label: 'Résolution', icon: '[COMPLETE]' } ]; // Platform colors const platformColors = { whatsapp: '#25d366', facebook: '#1877f2', instagram: '#e4405f', twitter: '#1da1f2' }; // Calculate platform breakdown const platformBreakdown = useMemo(() => { const breakdown = conversations.reduce((acc, conv) => { acc[conv.platform] = (acc[conv.platform] || 0) + 1; return acc; }, {}); return Object.entries(breakdown).map(([platform, count]) => ({ platform, count, percentage: (count / conversations.length) * 100 })); }, [conversations]); // Calculate conversation metrics by time const conversationsByTime = useMemo(() => { const days = timeRange === 'today' ? 1 : timeRange === '7days' ? 7 : 30; const labels = []; const data = []; for (let i = days - 1; i >= 0; i--) { const date = subDays(new Date(), i); const dayStart = startOfDay(date); const dayEnd = endOfDay(date); const dayConversations = conversations.filter(conv => { const convDate = new Date(conv.createdAt); return convDate >= dayStart && convDate <= dayEnd; }); labels.push(format(date, days === 1 ? 'HH:mm' : 'dd/MM', { locale: fr })); data.push(dayConversations.length); } return { labels, data }; }, [conversations, timeRange]); // Calculate response time metrics const responseTimeMetrics = useMemo(() => { const responseTimes = conversations .filter(conv => conv.firstResponseTime) .map(conv => conv.firstResponseTime / 1000); // Convert to seconds if (responseTimes.length === 0) { return { average: 0, median: 0, min: 0, max: 0 }; } const sorted = responseTimes.sort((a, b) => a - b); const average = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length; const median = sorted[Math.floor(sorted.length / 2)]; const min = sorted[0]; const max = sorted[sorted.length - 1]; return { average, median, min, max }; }, [conversations]); // Calculate sentiment distribution const sentimentDistribution = useMemo(() => { const sentiments = conversations.reduce((acc, conv) => { const sentiment = conv.sentiment || 'neutral'; acc[sentiment] = (acc[sentiment] || 0) + 1; return acc; }, {}); return { labels: ['Très positif', 'Positif', 'Neutre', 'Négatif', 'Très négatif'], data: [ sentiments.very_positive || 0, sentiments.positive || 0, sentiments.neutral || 0, sentiments.negative || 0, sentiments.very_negative || 0 ] }; }, [conversations]); // Platform breakdown chart data const platformChartData = { labels: platformBreakdown.map(p => p.platform.charAt(0).toUpperCase() + p.platform.slice(1)), datasets: [{ data: platformBreakdown.map(p => p.count), backgroundColor: platformBreakdown.map(p => platformColors[p.platform] || '#6366f1'), borderWidth: 2, borderColor: '#ffffff' }] }; // Conversations over time chart data const conversationsChartData = { labels: conversationsByTime.labels, datasets: [{ label: 'Conversations', data: conversationsByTime.data, borderColor: '#6366f1', backgroundColor: 'rgba(99, 102, 241, 0.1)', tension: 0.4, fill: true }] }; // Sentiment chart data const sentimentChartData = { labels: sentimentDistribution.labels, datasets: [{ data: sentimentDistribution.data, backgroundColor: [ '#10b981', // Very positive '#34d399', // Positive '#6b7280', // Neutral '#f59e0b', // Negative '#ef4444' // Very negative ] }] }; // Chart options const chartOptions = { responsive: true, maintainAspectRatio: false, plugins: { legend: { position: 'bottom' }, tooltip: { mode: 'index', intersect: false } }, scales: { x: { display: true, grid: { display: false } }, y: { display: true, beginAtZero: true, grid: { color: 'rgba(0, 0, 0, 0.1)' } } } }; // Doughnut chart options const doughnutOptions = { responsive: true, maintainAspectRatio: false, plugins: { legend: { position: 'bottom' } } }; // Format time duration const formatDuration = useCallback((seconds) => { if (seconds < 60) { return `${Math.round(seconds)}s`; } else if (seconds < 3600) { return `${Math.round(seconds / 60)}m`; } else { return `${Math.round(seconds / 3600)}h`; } }, []); // Auto-refresh data useEffect(() => { const interval = setInterval(() => { setLastUpdated(new Date()); }, refreshInterval); return () => clearInterval(interval); }, [refreshInterval]); return ( <div className="social-media-analytics"> {/* Header */} <div className="analytics-header"> <div className="header-left"> <h3>[ANALYTICS] Analyses en temps réel</h3> <div className="last-updated"> Dernière mise à jour: {format(lastUpdated, 'HH:mm:ss')} </div> </div> <div className="header-controls"> {/* Time Range Selector */} <select value={timeRange} onChange={(e) => setTimeRange(e.target.value)} className="time-range-selector" > {timeRanges.map(range => ( <option key={range.value} value={range.value}> {range.label} </option> ))} </select> {/* Refresh Interval */} <select value={refreshInterval} onChange={(e) => setRefreshInterval(Number(e.target.value))} className="refresh-interval-selector" > <option value={10000}>10s</option> <option value={30000}>30s</option> <option value={60000}>1m</option> <option value={300000}>5m</option> </select> {/* Close Button */} <button className="close-button" onClick={onClose}> </button> </div> </div> {/* Key Metrics */} <div className="key-metrics"> <div className="metric-card"> <div className="metric-icon"></div> <div className="metric-content"> <div className="metric-value">{conversations.length}</div> <div className="metric-label">Conversations totales</div> </div> </div> <div className="metric-card"> <div className="metric-icon">🟢</div> <div className="metric-content"> <div className="metric-value">{metrics.activeConversations || 0}</div> <div className="metric-label">Conversations actives</div> </div> </div> <div className="metric-card"> <div className="metric-icon">⏱</div> <div className="metric-content"> <div className="metric-value">{formatDuration(responseTimeMetrics.average)}</div> <div className="metric-label">Temps de réponse moyen</div> </div> </div> <div className="metric-card"> <div className="metric-icon">⭐</div> <div className="metric-content"> <div className="metric-value">{(metrics.satisfactionScore || 0).toFixed(1)}</div> <div className="metric-label">Score de satisfaction</div> </div> </div> </div> {/* Charts Grid */} <div className="charts-grid"> {/* Platform Breakdown */} <div className="chart-container"> <div className="chart-header"> <h4>Répartition par plateforme</h4> </div> <div className="chart-content"> <Doughnut data={platformChartData} options={doughnutOptions} /> </div> </div> {/* Conversations Over Time */} <div className="chart-container"> <div className="chart-header"> <h4>Évolution des conversations</h4> </div> <div className="chart-content"> <Line data={conversationsChartData} options={chartOptions} /> </div> </div> {/* Sentiment Analysis */} <div className="chart-container"> <div className="chart-header"> <h4>Analyse des sentiments</h4> </div> <div className="chart-content"> <Doughnut data={sentimentChartData} options={doughnutOptions} /> </div> </div> {/* Response Time Distribution */} <div className="chart-container"> <div className="chart-header"> <h4>Distribution des temps de réponse</h4> </div> <div className="chart-content"> <div className="response-time-stats"> <div className="stat-item"> <span className="stat-label">Minimum</span> <span className="stat-value">{formatDuration(responseTimeMetrics.min)}</span> </div> <div className="stat-item"> <span className="stat-label">Médiane</span> <span className="stat-value">{formatDuration(responseTimeMetrics.median)}</span> </div> <div className="stat-item"> <span className="stat-label">Maximum</span> <span className="stat-value">{formatDuration(responseTimeMetrics.max)}</span> </div> </div> </div> </div> </div> {/* Platform Details */} <div className="platform-details"> <h4>Détails par plateforme</h4> <div className="platform-grid"> {platformBreakdown.map(platform => ( <div key={platform.platform} className="platform-card"> <div className="platform-header" style={{ backgroundColor: platformColors[platform.platform] }} > <span className="platform-name"> {platform.platform.charAt(0).toUpperCase() + platform.platform.slice(1)} </span> </div> <div className="platform-stats"> <div className="stat"> <span className="stat-label">Conversations</span> <span className="stat-value">{platform.count}</span> </div> <div className="stat"> <span className="stat-label">Pourcentage</span> <span className="stat-value">{platform.percentage.toFixed(1)}%</span> </div> </div> </div> ))} </div> </div> {/* Export Options */} <div className="export-options"> <button className="export-button"> [ANALYTICS] Exporter les données </button> <button className="export-button"> [METRICS] Générer un rapport </button> <button className="export-button"> Envoyer par email </button> </div> </div> ); }; export default SocialMediaAnalytics;