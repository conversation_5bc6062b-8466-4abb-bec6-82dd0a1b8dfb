/** * ============================================= * [USER] CUSTOMER PROFILE * Comprehensive customer information sidebar * Cross-platform conversation history and analytics * ============================================= */ import React, { useState, useMemo } from 'react'; import { format } from 'date-fns'; import { fr } from 'date-fns/locale'; import './CustomerProfile.css'; const CustomerProfile = ({ customer, conversation, onClose }) => { const [activeTab, setActiveTab] = useState('profile'); const [showAllConversations, setShowAllConversations] = useState(false); // Customer profile tabs const tabs = [ { id: 'profile', label: 'Profil', icon: '[USER]' }, { id: 'history', label: 'Historique', icon: '' }, { id: 'analytics', label: 'Analyses', icon: '[ANALYTICS]' }, { id: 'notes', label: 'Notes', icon: '' } ]; // Get customer's platform accounts const platformAccounts = useMemo(() => { if (!customer?.platformIds) return []; return Object.entries(customer.platformIds) .filter(([platform, data]) => data && Object.keys(data).length > 0) .map(([platform, data]) => ({ platform, data, icon: getPlatformIcon(platform), color: getPlatformColor(platform) })); }, [customer?.platformIds]); // Get platform icon const getPlatformIcon = (platform) => { const icons = { whatsapp: '[MOBILE]', facebook: '', instagram: '', twitter: '', linkedin: '' }; return icons[platform] || ''; }; // Get platform color const getPlatformColor = (platform) => { const colors = { whatsapp: '#25d366', facebook: '#1877f2', instagram: '#e4405f', twitter: '#1da1f2', linkedin: '#0077b5' }; return colors[platform] || '#6366f1'; }; // Format customer analytics const customerAnalytics = useMemo(() => { if (!customer?.analytics) return null; return { totalConversations: customer.analytics.totalConversations || 0, totalMessages: customer.analytics.totalMessages || 0, averageResponseTime: customer.analytics.averageResponseTime || 0, lastInteraction: customer.analytics.lastInteractionAt, firstInteraction: customer.analytics.firstInteractionAt, satisfactionRating: customer.satisfaction?.overallRating || 0, platformStats: customer.analytics.platformStats || {} }; }, [customer?.analytics, customer?.satisfaction]); // Format duration const formatDuration = (ms) => { if (!ms) return 'N/A'; const seconds = Math.floor(ms / 1000); if (seconds < 60) return `${seconds}s`; const minutes = Math.floor(seconds / 60); if (minutes < 60) return `${minutes}m`; const hours = Math.floor(minutes / 60); return `${hours}h ${minutes % 60}m`; }; // Render profile tab const renderProfileTab = () => ( <div className="profile-tab"> {/* Customer Avatar and Basic Info */} <div className="customer-header"> <div className="customer-avatar-large"> {customer?.profilePic ? ( <img src={customer.profilePic} alt={customer.name} /> ) : ( <div className="avatar-fallback-large"> {(customer?.name || 'C').charAt(0).toUpperCase()} </div> )} {customer?.isOnline && ( <div className="online-indicator-large" title="En ligne"></div> )} </div> <div className="customer-basic-info"> <h3 className="customer-name"> {customer?.name || 'Client inconnu'} {customer?.isVip && ( <span className="vip-badge" title="Client VIP">⭐</span> )} </h3> <div className="customer-segment"> <span className={`segment-badge ${customer?.segment || 'new'}`}> {customer?.segment || 'Nouveau'} </span> </div> </div> </div> {/* Contact Information */} <div className="contact-section"> <h4>Informations de contact</h4> {customer?.email && ( <div className="contact-item"> <span className="contact-icon"></span> <span className="contact-value">{customer.email}</span> </div> )} {customer?.phone && ( <div className="contact-item"> <span className="contact-icon"></span> <span className="contact-value">{customer.phone}</span> </div> )} {customer?.location?.city && ( <div className="contact-item"> <span className="contact-icon"></span> <span className="contact-value"> {customer.location.city} {customer.location.country && `, ${customer.location.country}`} </span> </div> )} </div> {/* Platform Accounts */} <div className="platforms-section"> <h4>Comptes sur les plateformes</h4> <div className="platform-accounts"> {platformAccounts.map(({ platform, data, icon, color }) => ( <div key={platform} className="platform-account"> <div className="platform-account-icon" style={{ backgroundColor: color }} > {icon} </div> <div className="platform-account-info"> <div className="platform-account-name"> {platform.charAt(0).toUpperCase() + platform.slice(1)} </div> <div className="platform-account-id"> {data.username || data.screenName || data.phoneNumber || data.userId} </div> </div> </div> ))} </div> </div> {/* Customer Preferences */} {customer?.preferences && ( <div className="preferences-section"> <h4>Préférences</h4> {customer.preferences.language && ( <div className="preference-item"> <span className="preference-label">Langue</span> <span className="preference-value">{customer.preferences.language}</span> </div> )} {customer.preferences.preferredPlatform && ( <div className="preference-item"> <span className="preference-label">Plateforme préférée</span> <span className="preference-value"> {customer.preferences.preferredPlatform} </span> </div> )} {customer.preferences.timezone && ( <div className="preference-item"> <span className="preference-label">Fuseau horaire</span> <span className="preference-value">{customer.preferences.timezone}</span> </div> )} </div> )} </div> ); // Render history tab const renderHistoryTab = () => ( <div className="history-tab"> <div className="history-header"> <h4>Historique des conversations</h4> <button className="show-all-button" onClick={() => setShowAllConversations(!showAllConversations)} > {showAllConversations ? 'Masquer' : 'Tout voir'} </button> </div> {/* Recent Conversations */} <div className="conversation-history"> {/* Current conversation would be highlighted */} <div className="history-item current"> <div className="history-platform"> <span style={{ color: getPlatformColor(conversation?.platform) }}> {getPlatformIcon(conversation?.platform)} </span> </div> <div className="history-content"> <div className="history-title">Conversation actuelle</div> <div className="history-date"> {conversation?.createdAt && format(new Date(conversation.createdAt), 'dd/MM/yyyy HH:mm', { locale: fr })} </div> <div className="history-status"> <span className={`status-badge ${conversation?.status}`}> {conversation?.status} </span> </div> </div> </div> {/* Previous conversations would be listed here */} {/* This would typically come from an API call */} </div> </div> ); // Render analytics tab const renderAnalyticsTab = () => ( <div className="analytics-tab"> {customerAnalytics ? ( <> {/* Key Metrics */} <div className="analytics-metrics"> <div className="metric-item"> <div className="metric-icon"></div> <div className="metric-content"> <div className="metric-value">{customerAnalytics.totalConversations}</div> <div className="metric-label">Conversations</div> </div> </div> <div className="metric-item"> <div className="metric-icon"></div> <div className="metric-content"> <div className="metric-value">{customerAnalytics.totalMessages}</div> <div className="metric-label">Messages</div> </div> </div> <div className="metric-item"> <div className="metric-icon">⏱</div> <div className="metric-content"> <div className="metric-value"> {formatDuration(customerAnalytics.averageResponseTime)} </div> <div className="metric-label">Temps de réponse</div> </div> </div> <div className="metric-item"> <div className="metric-icon">⭐</div> <div className="metric-content"> <div className="metric-value"> {customerAnalytics.satisfactionRating.toFixed(1)} </div> <div className="metric-label">Satisfaction</div> </div> </div> </div> {/* Platform Statistics */} <div className="platform-stats"> <h4>Statistiques par plateforme</h4> {Object.entries(customerAnalytics.platformStats).map(([platform, stats]) => ( <div key={platform} className="platform-stat"> <div className="platform-stat-header"> <span style={{ color: getPlatformColor(platform) }}> {getPlatformIcon(platform)} </span> <span className="platform-stat-name"> {platform.charAt(0).toUpperCase() + platform.slice(1)} </span> </div> <div className="platform-stat-values"> <span>{stats.conversations || 0} conversations</span> <span>{stats.messages || 0} messages</span> </div> </div> ))} </div> {/* Timeline */} <div className="customer-timeline"> <h4>Chronologie</h4> {customerAnalytics.firstInteraction && ( <div className="timeline-item"> <div className="timeline-icon">[TARGET]</div> <div className="timeline-content"> <div className="timeline-title">Premier contact</div> <div className="timeline-date"> {format(new Date(customerAnalytics.firstInteraction), 'dd/MM/yyyy HH:mm', { locale: fr })} </div> </div> </div> )} {customerAnalytics.lastInteraction && ( <div className="timeline-item"> <div className="timeline-icon"></div> <div className="timeline-content"> <div className="timeline-title">Dernière interaction</div> <div className="timeline-date"> {format(new Date(customerAnalytics.lastInteraction), 'dd/MM/yyyy HH:mm', { locale: fr })} </div> </div> </div> )} </div> </> ) : ( <div className="no-analytics"> <div className="empty-icon">[ANALYTICS]</div> <p>Aucune donnée analytique disponible</p> </div> )} </div> ); // Render notes tab const renderNotesTab = () => ( <div className="notes-tab"> <div className="notes-header"> <h4>Notes sur le client</h4> <button className="add-note-button"> + Ajouter une note </button> </div> {customer?.notes && customer.notes.length > 0 ? ( <div className="notes-list"> {customer.notes.map((note, index) => ( <div key={index} className="note-item"> <div className="note-header"> <span className="note-author">{note.createdBy?.name || 'Agent'}</span> <span className="note-date"> {format(new Date(note.createdAt), 'dd/MM/yyyy HH:mm', { locale: fr })} </span> </div> <div className="note-content">{note.content}</div> <div className="note-type"> <span className={`note-type-badge ${note.type}`}> {note.type} </span> </div> </div> ))} </div> ) : ( <div className="no-notes"> <div className="empty-icon"></div> <p>Aucune note sur ce client</p> <button className="add-first-note-button"> Ajouter la première note </button> </div> )} </div> ); if (!customer) { return ( <div className="customer-profile empty"> <div className="empty-state"> <div className="empty-icon">[USER]</div> <h3>Aucun client sélectionné</h3> <p>Sélectionnez une conversation pour voir le profil du client.</p> </div> </div> ); } return ( <div className="customer-profile"> {/* Header */} <div className="profile-header"> <h3>Profil client</h3> <button className="close-button" onClick={onClose}> </button> </div> {/* Tabs */} <div className="profile-tabs"> {tabs.map(tab => ( <button key={tab.id} className={`tab-button ${activeTab === tab.id ? 'active' : ''}`} onClick={() => setActiveTab(tab.id)} > <span className="tab-icon">{tab.icon}</span> <span className="tab-label">{tab.label}</span> </button> ))} </div> {/* Tab Content */} <div className="profile-content"> {activeTab === 'profile' && renderProfileTab()} {activeTab === 'history' && renderHistoryTab()} {activeTab === 'analytics' && renderAnalyticsTab()} {activeTab === 'notes' && renderNotesTab()} </div> </div> ); }; export default CustomerProfile;