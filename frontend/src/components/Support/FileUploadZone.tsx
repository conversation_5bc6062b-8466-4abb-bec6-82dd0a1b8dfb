import React, { useState, useCallback, useRef } from 'react'; import { Box, Paper, Typography, Button, IconButton, Chip, LinearProgress, Alert, Grid, Card, CardContent, Tooltip, Divider } from '@mui/material'; import { CloudUpload as UploadIcon, AttachFile as AttachIcon, Delete as DeleteIcon, Image as ImageIcon, PictureAsPdf as PdfIcon, Description as DocIcon, InsertDriveFile as FileIcon, CheckCircle as CheckIcon, Error as ErrorIcon } from '@mui/icons-material'; import { useDispatch } from 'react-redux'; import { showNotification } from '../../store/slices/uiSlice'; interface FileUploadZoneProps { onFilesSelected: (files: File[]) => void; maxFiles?: number; maxFileSize?: number; // in bytes acceptedTypes?: string[]; uploadType?: 'chat' | 'ticket'; disabled?: boolean; } interface UploadedFile { file: File; preview?: string; status: 'pending' | 'uploading' | 'success' | 'error'; progress: number; error?: string; } const FileUploadZone: React.FC<FileUploadZoneProps> = ({ onFilesSelected, maxFiles = 10, maxFileSize = 25 * 1024 * 1024, // 25MB default acceptedTypes = ['image/*', 'application/pdf', '.txt', '.doc', '.docx', '.xls', '.xlsx'], uploadType = 'ticket', disabled = false }) => { const dispatch = useDispatch(); const [selectedFiles, setSelectedFiles] = useState<UploadedFile[]>([]); const [isDragOver, setIsDragOver] = useState(false); const [isUploading, setIsUploading] = useState(false); const fileInputRef = useRef<HTMLInputElement>(null); const getFileIcon = (file: File) => { if (file.type.startsWith('image/')) return <ImageIcon />; if (file.type === 'application/pdf') return <PdfIcon />; if (file.type.includes('document') || file.type.includes('word')) return <DocIcon />; return <FileIcon />; }; const getFileTypeColor = (file: File) => { if (file.type.startsWith('image/')) return 'primary'; if (file.type === 'application/pdf') return 'error'; if (file.type.includes('document') || file.type.includes('word')) return 'info'; return 'default'; }; const validateFile = (file: File): string | null => { if (file.size > maxFileSize) { return `Fichier trop volumineux (max ${Math.round(maxFileSize / (1024 * 1024))}MB)`; } const isValidType = acceptedTypes.some(type => { if (type.includes('*')) { return file.type.startsWith(type.replace('*', '')); } if (type.startsWith('.')) { return file.name.toLowerCase().endsWith(type.toLowerCase()); } return file.type === type; }); if (!isValidType) { return 'Type de fichier non supporté'; } return null; }; const handleFileSelect = useCallback((files: FileList) => { const newFiles: UploadedFile[] = []; const errors: string[] = []; Array.from(files).forEach(file => { if (selectedFiles.length + newFiles.length >= maxFiles) { errors.push(`Maximum ${maxFiles} fichiers autorisés`); return; } const validationError = validateFile(file); if (validationError) { errors.push(`${file.name}: ${validationError}`); return; } // Check for duplicates const isDuplicate = selectedFiles.some(f => f.file.name === file.name && f.file.size === file.size ); if (isDuplicate) { errors.push(`${file.name}: Fichier déjà sélectionné`); return; } const uploadedFile: UploadedFile = { file, status: 'pending', progress: 0 }; // Create preview for images if (file.type.startsWith('image/')) { uploadedFile.preview = URL.createObjectURL(file); } newFiles.push(uploadedFile); }); if (errors.length > 0) { dispatch(showNotification({ message: errors.join(', '), severity: 'error' })); } if (newFiles.length > 0) { setSelectedFiles(prev => [...prev, ...newFiles]); onFilesSelected(newFiles.map(f => f.file)); } }, [selectedFiles, maxFiles, maxFileSize, acceptedTypes, dispatch, onFilesSelected]); const handleDrop = useCallback((e: React.DragEvent) => { e.preventDefault(); setIsDragOver(false); if (disabled) return; const files = e.dataTransfer.files; if (files.length > 0) { handleFileSelect(files); } }, [handleFileSelect, disabled]); const handleDragOver = useCallback((e: React.DragEvent) => { e.preventDefault(); if (!disabled) { setIsDragOver(true); } }, [disabled]); const handleDragLeave = useCallback((e: React.DragEvent) => { e.preventDefault(); setIsDragOver(false); }, []); const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => { const files = e.target.files; if (files && files.length > 0) { handleFileSelect(files); } // Reset input value to allow selecting the same file again e.target.value = ''; }, [handleFileSelect]); const removeFile = useCallback((index: number) => { setSelectedFiles(prev => { const newFiles = [...prev]; const removedFile = newFiles[index]; // Revoke object URL for images to prevent memory leaks if (removedFile.preview) { URL.revokeObjectURL(removedFile.preview); } newFiles.splice(index, 1); return newFiles; }); }, []); const openFileDialog = () => { if (!disabled && fileInputRef.current) { fileInputRef.current.click(); } }; const formatFileSize = (bytes: number): string => { if (bytes === 0) return '0 Bytes'; const k = 1024; const sizes = ['Bytes', 'KB', 'MB', 'GB']; const i = Math.floor(Math.log(bytes) / Math.log(k)); return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]; }; return ( <Box> {/* Drop Zone */} <Paper elevation={isDragOver ? 4 : 1} sx={{ p: 3, border: '2px dashed', borderColor: isDragOver ? 'primary.main' : 'grey.300', bgcolor: isDragOver ? 'primary.50' : disabled ? 'grey.100' : 'background.paper', cursor: disabled ? 'not-allowed' : 'pointer', transition: 'all 0.3s ease', opacity: disabled ? 0.6 : 1, '&:hover': { borderColor: disabled ? 'grey.300' : 'primary.main', bgcolor: disabled ? 'grey.100' : 'primary.50' } }} onDrop={handleDrop} onDragOver={handleDragOver} onDragLeave={handleDragLeave} onClick={openFileDialog} > <Box sx={{ textAlign: 'center' }}> <UploadIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} /> <Typography variant="h6" gutterBottom> Glissez-déposez vos fichiers ici </Typography> <Typography variant="body2" color="text.secondary" gutterBottom> ou cliquez pour sélectionner des fichiers </Typography> <Typography variant="caption" color="text.secondary"> Max {maxFiles} fichiers • {Math.round(maxFileSize / (1024 * 1024))}MB par fichier </Typography> <Box sx={{ mt: 2 }}> <Button variant="outlined" startIcon={<AttachIcon />} disabled={disabled} onClick={(e) => { e.stopPropagation(); openFileDialog(); }} > Choisir des fichiers </Button> </Box> </Box> <input ref={fileInputRef} type="file" multiple accept={acceptedTypes.join(',')} onChange={handleFileInputChange} style={{ display: 'none' }} disabled={disabled} /> </Paper> {/* Selected Files */} {selectedFiles.length > 0 && ( <Box sx={{ mt: 3 }}> <Typography variant="subtitle2" gutterBottom> Fichiers sélectionnés ({selectedFiles.length}/{maxFiles}) </Typography> <Grid container spacing={2}> {selectedFiles.map((uploadedFile, index) => ( <Grid item xs={12} sm={6} md={4} key={index}> <Card variant="outlined"> <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}> <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}> <Box sx={{ color: getFileTypeColor(uploadedFile.file) }}> {getFileIcon(uploadedFile.file)} </Box> <Box sx={{ flexGrow: 1, minWidth: 0 }}> <Typography variant="body2" noWrap title={uploadedFile.file.name}> {uploadedFile.file.name} </Typography> <Typography variant="caption" color="text.secondary"> {formatFileSize(uploadedFile.file.size)} </Typography> {uploadedFile.status === 'uploading' && ( <LinearProgress variant="determinate" value={uploadedFile.progress} sx={{ mt: 1 }} /> )} {uploadedFile.status === 'error' && uploadedFile.error && ( <Typography variant="caption" color="error" sx={{ display: 'block', mt: 1 }}> {uploadedFile.error} </Typography> )} </Box> <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}> {uploadedFile.status === 'success' && ( <CheckIcon color="success" fontSize="small" /> )} {uploadedFile.status === 'error' && ( <ErrorIcon color="error" fontSize="small" /> )} <IconButton size="small" onClick={() => removeFile(index)} disabled={disabled} > <DeleteIcon fontSize="small" /> </IconButton> </Box> </Box> </CardContent> </Card> </Grid> ))} </Grid> </Box> )} {/* Upload Progress */} {isUploading && ( <Box sx={{ mt: 2 }}> <Alert severity="info"> Envoi des fichiers en cours... </Alert> </Box> )} </Box> ); }; export default FileUploadZone;