import React from 'react'; import { Snackbar, Alert } from '@mui/material'; import { useSelector, useDispatch } from 'react-redux'; import { RootState } from '../../store'; import { hideNotification } from '../../store/slices/uiSlice'; interface NotificationProviderProps { children: React.ReactNode; } const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => { const dispatch = useDispatch(); const { notifications } = useSelector((state: RootState) => state.ui); const handleClose = (notificationId: string) => { dispatch(hideNotification(notificationId)); }; return ( <> {children} {notifications.map((notification) => ( <Snackbar key={notification.id} open={true} autoHideDuration={notification.duration || 6000} onClose={() => handleClose(notification.id)} anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }} > <Alert onClose={() => handleClose(notification.id)} severity={notification.type as 'error' | 'success' | 'info' | 'warning'} variant="filled" sx={{ width: '100%' }} > {notification.message} </Alert> </Snackbar> ))} </> ); }; export default NotificationProvider;