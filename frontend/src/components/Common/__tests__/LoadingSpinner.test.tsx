import React from 'react'; import { render, screen } from '@testing-library/react'; import { ThemeProvider } from '@mui/material/styles'; import LoadingSpinner from '../LoadingSpinner'; import theme from '../../../theme'; const renderWithTheme = (component: React.ReactElement) => { return render( <ThemeProvider theme={theme}> {component} </ThemeProvider> ); }; describe('LoadingSpinner', () => { describe('Rendering', () => { it('renders with default props', () => { renderWithTheme(<LoadingSpinner />); expect(screen.getByRole('progressbar')).toBeInTheDocument(); expect(screen.getByText('Chargement...')).toBeInTheDocument(); }); it('renders with custom message', () => { const customMessage = 'Loading data...'; renderWithTheme(<LoadingSpinner message={customMessage} />); expect(screen.getByRole('progressbar')).toBeInTheDocument(); expect(screen.getByText(customMessage)).toBeInTheDocument(); }); it('renders without message when message is empty', () => { renderWithTheme(<LoadingSpinner message="" />); expect(screen.getByRole('progressbar')).toBeInTheDocument(); expect(screen.queryByText('Chargement...')).not.toBeInTheDocument(); }); it('renders without message when message is null', () => { renderWithTheme(<LoadingSpinner message={null} />); expect(screen.getByRole('progressbar')).toBeInTheDocument(); // When message is explicitly null, no message should be rendered expect(screen.queryByText('Chargement...')).not.toBeInTheDocument(); // Check that no Typography element is rendered expect(screen.queryByText(/./)).not.toBeInTheDocument(); }); }); describe('Full Screen Mode', () => { it('renders in full screen mode by default', () => { const { container } = renderWithTheme(<LoadingSpinner />); // Check for the outer Box with fixed positioning const outerBox = container.firstChild as HTMLElement; expect(outerBox).toBeInTheDocument(); expect(outerBox).toHaveClass('MuiBox-root'); // Check that it contains the progress bar expect(screen.getByRole('progressbar')).toBeInTheDocument(); }); it('renders in full screen mode when explicitly set to true', () => { const { container } = renderWithTheme(<LoadingSpinner fullScreen={true} />); // Check for the outer Box with fixed positioning const outerBox = container.firstChild as HTMLElement; expect(outerBox).toBeInTheDocument(); expect(outerBox).toHaveClass('MuiBox-root'); // Check that it contains the progress bar expect(screen.getByRole('progressbar')).toBeInTheDocument(); }); it('renders inline when fullScreen is false', () => { const { container } = renderWithTheme(<LoadingSpinner fullScreen={false} />); // Should render the content directly without outer fixed container const contentBox = container.firstChild as HTMLElement; expect(contentBox).toBeInTheDocument(); expect(contentBox).toHaveClass('MuiBox-root'); // Should render the progress bar expect(screen.getByRole('progressbar')).toBeInTheDocument(); }); }); describe('Size Customization', () => { it('uses default size when not specified', () => { renderWithTheme(<LoadingSpinner />); const progressBar = screen.getByRole('progressbar'); expect(progressBar).toBeInTheDocument(); // Note: Testing exact size styling is complex with MUI components // The size prop is passed to CircularProgress internally }); it('accepts custom size prop', () => { renderWithTheme(<LoadingSpinner size={60} />); const progressBar = screen.getByRole('progressbar'); expect(progressBar).toBeInTheDocument(); // The size prop is passed to the CircularProgress component }); }); describe('Styling and Layout', () => { it('has proper flex layout structure', () => { const { container } = renderWithTheme(<LoadingSpinner fullScreen={false} />); // Check for the content Box with flex layout const contentBox = container.firstChild as HTMLElement; expect(contentBox).toBeInTheDocument(); expect(contentBox).toHaveClass('MuiBox-root'); // Check that it contains the progress bar expect(screen.getByRole('progressbar')).toBeInTheDocument(); }); it('applies backdrop styling in full screen mode', () => { const { container } = renderWithTheme(<LoadingSpinner />); // Check for the outer Box (backdrop) const backdrop = container.firstChild as HTMLElement; expect(backdrop).toBeInTheDocument(); expect(backdrop).toHaveClass('MuiBox-root'); // Check that it contains the progress bar expect(screen.getByRole('progressbar')).toBeInTheDocument(); }); }); describe('Accessibility', () => { it('has proper ARIA attributes', () => { renderWithTheme(<LoadingSpinner />); const progressBar = screen.getByRole('progressbar'); expect(progressBar).toBeInTheDocument(); }); it('message is properly associated with spinner', () => { const message = 'Loading user data'; renderWithTheme(<LoadingSpinner message={message} />); expect(screen.getByText(message)).toBeInTheDocument(); expect(screen.getByRole('progressbar')).toBeInTheDocument(); }); }); describe('Props Combinations', () => { it('works with all props combined', () => { const customMessage = 'Processing request...'; renderWithTheme( <LoadingSpinner message={customMessage} size={50} fullScreen={false} /> ); expect(screen.getByRole('progressbar')).toBeInTheDocument(); expect(screen.getByText(customMessage)).toBeInTheDocument(); const { container } = renderWithTheme( <LoadingSpinner message={customMessage} size={50} fullScreen={false} /> ); const fullScreenContainer = container.querySelector('[style*="position: fixed"]'); expect(fullScreenContainer).not.toBeInTheDocument(); }); it('handles edge case with empty string message and custom size', () => { const { container } = renderWithTheme(<LoadingSpinner message="" size={30} fullScreen={true} />); expect(screen.getByRole('progressbar')).toBeInTheDocument(); expect(screen.queryByText('Chargement...')).not.toBeInTheDocument(); // Check for the outer Box with fixed positioning const outerBox = container.firstChild as HTMLElement; expect(outerBox).toBeInTheDocument(); expect(outerBox).toHaveClass('MuiBox-root'); }); }); });