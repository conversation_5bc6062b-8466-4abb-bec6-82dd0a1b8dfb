import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import authSlice from '../../../store/authSlice';

// Mock the auth service
jest.mock('../../../services/auth.service', () => ({
  login: jest.fn(),
}));

// Mock react-router-dom navigate
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

const theme = createTheme();

const renderWithProviders = (component: React.ReactElement) => {
  const store = configureStore({
    reducer: {
      auth: authSlice,
    },
  });

  return render(
    <Provider store={store}>
      <BrowserRouter>
        <ThemeProvider theme={theme}>
          {component}
        </ThemeProvider>
      </BrowserRouter>
    </Provider>
  );
};

// Simple placeholder LoginForm component for testing
const LoginForm: React.FC = () => {
  return (
    <div>
      <h1>Connexion</h1>
      <form>
        <label htmlFor="email">Email</label>
        <input id="email" type="email" />

        <label htmlFor="password">Mot de passe</label>
        <input id="password" type="password" />

        <button type="submit" disabled>Se connecter</button>
      </form>

      <div>
        <p>Compte de test disponible:</p>
        <p><EMAIL> / AdminPassword123!</p>
      </div>
    </div>
  );
};

describe('LoginForm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders login form elements', () => {
    renderWithProviders(<LoginForm />);

    expect(screen.getByText('Connexion')).toBeInTheDocument();
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/mot de passe/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /se connecter/i })).toBeInTheDocument();
  });

  it('has disabled submit button initially', () => {
    renderWithProviders(<LoginForm />);

    const submitButton = screen.getByRole('button', { name: /se connecter/i });
    expect(submitButton).toBeDisabled();
  });

  it('allows user to type in form fields', () => {
    renderWithProviders(<LoginForm />);

    const emailInput = screen.getByLabelText(/email/i) as HTMLInputElement;
    const passwordInput = screen.getByLabelText(/mot de passe/i) as HTMLInputElement;

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });

    expect(emailInput.value).toBe('<EMAIL>');
    expect(passwordInput.value).toBe('password123');
  });

  it('displays test account information', () => {
    renderWithProviders(<LoginForm />);

    expect(screen.getByText('Compte de test disponible:')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL> / AdminPassword123!')).toBeInTheDocument();
  });
});