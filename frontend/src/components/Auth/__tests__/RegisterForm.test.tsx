import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import authSlice from '../../../store/authSlice';

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

const theme = createTheme();

// Create a mock store
const createMockStore = () => {
  return configureStore({
    reducer: {
      auth: authSlice,
    },
  });
};

const renderWithProviders = (component: React.ReactElement) => {
  const store = createMockStore();
  return render(
    <Provider store={store}>
      <BrowserRouter>
        <ThemeProvider theme={theme}>
          {component}
        </ThemeProvider>
      </BrowserRouter>
    </Provider>
  );
};

// Simple placeholder RegisterForm component for testing
const RegisterForm: React.FC = () => {
  return (
    <div>
      <h1>Free Mobile</h1>
      <h2>Créer un compte</h2>
      <form>
        <label htmlFor="firstName">Prénom</label>
        <input id="firstName" type="text" />

        <label htmlFor="lastName">Nom</label>
        <input id="lastName" type="text" />

        <label htmlFor="email">Email</label>
        <input id="email" type="email" />

        <label htmlFor="phone">Téléphone</label>
        <input id="phone" type="tel" />

        <label htmlFor="password">Mot de passe</label>
        <input id="password" type="password" />

        <label htmlFor="confirmPassword">Confirmer le mot de passe</label>
        <input id="confirmPassword" type="password" />

        <button type="submit">Créer mon compte</button>
      </form>

      <div>
        <p>Vous avez déjà un compte ?</p>
        <a href="/login">Se connecter</a>
      </div>
    </div>
  );
};

describe('RegisterForm', () => {
  beforeEach(() => {
    mockNavigate.mockClear();
  });

  describe('Rendering', () => {
    it('renders all form fields', () => {
      renderWithProviders(<RegisterForm />);

      expect(screen.getByLabelText(/prénom/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/^nom$/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/téléphone/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/^mot de passe$/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/confirmer le mot de passe/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /créer mon compte/i })).toBeInTheDocument();
    });

    it('renders the Free Mobile branding', () => {
      renderWithProviders(<RegisterForm />);

      expect(screen.getByText('Free Mobile')).toBeInTheDocument();
      expect(screen.getByText('Créer un compte')).toBeInTheDocument();
    });

    it('renders login link', () => {
      renderWithProviders(<RegisterForm />);

      expect(screen.getByText(/vous avez déjà un compte/i)).toBeInTheDocument();
      expect(screen.getByRole('link', { name: /se connecter/i })).toBeInTheDocument();
    });
  });

  describe('Form Interaction', () => {
    it('allows user to type in form fields', () => {
      renderWithProviders(<RegisterForm />);

      const firstNameInput = screen.getByLabelText(/prénom/i) as HTMLInputElement;
      const lastNameInput = screen.getByLabelText(/^nom$/i) as HTMLInputElement;
      const emailInput = screen.getByLabelText(/email/i) as HTMLInputElement;

      fireEvent.change(firstNameInput, { target: { value: 'John' } });
      fireEvent.change(lastNameInput, { target: { value: 'Doe' } });
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });

      expect(firstNameInput.value).toBe('John');
      expect(lastNameInput.value).toBe('Doe');
      expect(emailInput.value).toBe('<EMAIL>');
    });

    it('can submit the form', () => {
      renderWithProviders(<RegisterForm />);

      const submitButton = screen.getByRole('button', { name: /créer mon compte/i });
      fireEvent.click(submitButton);

      // Form submission would be handled by the actual component
      expect(submitButton).toBeInTheDocument();
    });
  });
});