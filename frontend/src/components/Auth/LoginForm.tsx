import React, { useState, useEffect } from 'react'; import { <PERSON>, Card, CardContent, TextField, Button, Typography, Alert, CircularProgress, Container, Paper, InputAdornment, IconButton, } from '@mui/material'; import { Visibility, VisibilityOff, Email, Lock, Login as LoginIcon, } from '@mui/icons-material'; import { useNavigate } from 'react-router-dom'; import { useAuth } from '../../hooks/useAuth'; import { ROUTES, FREE_MOBILE_COLORS } from '../../utils/constants'; const LoginForm: React.FC = () => { const [email, setEmail] = useState(''); const [password, setPassword] = useState(''); const [showPassword, setShowPassword] = useState(false); const [emailError, setEmailError] = useState(''); const [passwordError, setPasswordError] = useState(''); const { login, loading, error, isAuthenticated, clearError } = useAuth(); const navigate = useNavigate(); useEffect(() => { if (isAuthenticated) { navigate(ROUTES.DASHBOARD_OVERVIEW, { replace: true }); } }, [isAuthenticated, navigate]); useEffect(() => { if (error) { const timer = setTimeout(() => { clearError(); }, 5000); return () => clearTimeout(timer); } }, [error, clearError]); const validateEmail = (email: string): boolean => { const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/; if (!email) { setEmailError('Email requis'); return false; } if (!emailRegex.test(email)) { setEmailError('Format email invalide'); return false; } setEmailError(''); return true; }; const validatePassword = (password: string): boolean => { if (!password) { setPasswordError('Mot de passe requis'); return false; } if (password.length < 6) { setPasswordError('Le mot de passe doit contenir au moins 6 caractères'); return false; } setPasswordError(''); return true; }; const handleSubmit = async (e: React.FormEvent) => { e.preventDefault(); const isEmailValid = validateEmail(email); const isPasswordValid = validatePassword(password); if (!isEmailValid || !isPasswordValid) { return; } try { await login(email, password); } catch (err) { console.error('Login error:', err); } }; const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => { const value = e.target.value; setEmail(value); if (emailError && value) { validateEmail(value); } }; const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => { const value = e.target.value; setPassword(value); if (passwordError && value) { validatePassword(value); } }; return ( <Container maxWidth="sm"> <Box sx={{ minHeight: '100vh', display: 'flex', alignItems: 'center', justifyContent: 'center', py: 4, }} > <Paper elevation={8} sx={{ width: '100%', maxWidth: 400, borderRadius: 3, overflow: 'hidden', }} > {/* Header */} <Box sx={{ bgcolor: FREE_MOBILE_COLORS.PRIMARY, color: 'white', p: 3, textAlign: 'center', }} > <LoginIcon sx={{ fontSize: 48, mb: 1 }} /> <Typography variant="h4" component="h1" fontWeight="bold"> Free Mobile </Typography> <Typography variant="subtitle1" sx={{ opacity: 0.9 }}> Service Client </Typography> </Box> <CardContent sx={{ p: 4 }}> <Typography variant="h5" component="h2" gutterBottom textAlign="center"> Connexion </Typography> {error && ( <Alert severity="error" sx={{ mb: 3 }}> {error} </Alert> )} <Box component="form" onSubmit={handleSubmit} noValidate> <TextField fullWidth label="Email" type="email" value={email} onChange={handleEmailChange} error={!!emailError} helperText={emailError} margin="normal" required autoComplete="email" autoFocus disabled={loading} InputProps={{ startAdornment: ( <InputAdornment position="start"> <Email color={emailError ? 'error' : 'action'} /> </InputAdornment> ), }} sx={{ '& .MuiOutlinedInput-root': { '&.Mui-focused fieldset': { borderColor: FREE_MOBILE_COLORS.PRIMARY, }, }, '& .MuiInputLabel-root.Mui-focused': { color: FREE_MOBILE_COLORS.PRIMARY, }, }} /> <TextField fullWidth label="Mot de passe" type={showPassword ? 'text' : 'password'} value={password} onChange={handlePasswordChange} error={!!passwordError} helperText={passwordError} margin="normal" required autoComplete="current-password" disabled={loading} InputProps={{ startAdornment: ( <InputAdornment position="start"> <Lock color={passwordError ? 'error' : 'action'} /> </InputAdornment> ), endAdornment: ( <InputAdornment position="end"> <IconButton onClick={() => setShowPassword(!showPassword)} edge="end" disabled={loading} > {showPassword ? <VisibilityOff /> : <Visibility />} </IconButton> </InputAdornment> ), }} sx={{ '& .MuiOutlinedInput-root': { '&.Mui-focused fieldset': { borderColor: FREE_MOBILE_COLORS.PRIMARY, }, }, '& .MuiInputLabel-root.Mui-focused': { color: FREE_MOBILE_COLORS.PRIMARY, }, }} /> <Button type="submit" fullWidth variant="contained" disabled={loading || !email || !password} sx={{ mt: 3, mb: 2, py: 1.5, bgcolor: FREE_MOBILE_COLORS.PRIMARY, '&:hover': { bgcolor: '#CC0000', }, '&:disabled': { bgcolor: 'grey.300', }, }} > {loading ? ( <CircularProgress size={24} color="inherit" /> ) : ( 'Se connecter' )} </Button> <Box sx={{ textAlign: 'center', mt: 2 }}> <Typography variant="body2" color="text.secondary"> Compte de test disponible: </Typography> <Typography variant="body2" color="text.secondary"> <EMAIL> / AdminPassword123! </Typography> </Box> </Box> </CardContent> </Paper> </Box> </Container> ); }; export default LoginForm;