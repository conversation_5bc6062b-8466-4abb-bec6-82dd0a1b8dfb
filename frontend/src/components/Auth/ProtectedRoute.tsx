import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import LoadingSpinner from '../Common/LoadingSpinner';

// Mock useAuth hook for now
const useAuth = () => ({
  isAuthenticated: true,
  user: { id: '1', email: '<EMAIL>', role: 'user' },
  loading: false
});

// Mock ROUTES constant
const ROUTES = {
  LOGIN: '/login',
  DASHBOARD: '/dashboard'
};

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: 'user' | 'agent' | 'admin';
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children, requiredRole }) => {
  const { isAuthenticated, user, loading } = useAuth();
  const location = useLocation();

  if (loading) {
    return <LoadingSpinner />;
  }

  if (!isAuthenticated) {
    // Rediriger vers login avec la route de retour
    return <Navigate to={ROUTES.LOGIN} state={{ from: location }} replace />;
  }

  if (requiredRole && user?.role !== requiredRole) {
    // Rediriger vers la page appropriée selon le rôle
    switch (user?.role) {
      case 'admin':
        return <Navigate to="/admin" replace />;
      case 'agent':
        return <Navigate to="/agent" replace />;
      default:
        return <Navigate to={ROUTES.DASHBOARD} replace />;
    }
  }

  return <>{children}</>;
};

export default ProtectedRoute;