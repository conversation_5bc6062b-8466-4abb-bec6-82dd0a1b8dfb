import React, { useState, useEffect } from 'react'; import { Box, Card, CardContent, Typography, Grid, Chip, LinearProgress, Button, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, Alert, Divider, List, ListItem, ListItemText, ListItemIcon, Avatar, } from '@mui/material'; import { Person, Phone, Email, CreditCard, DataUsage, CallMade, Sms, Edit, Save, Cancel, Receipt, Notifications, TrendingUp, } from '@mui/icons-material'; import { useSelector, useDispatch } from 'react-redux'; import { RootState } from '../../store'; import customerService from '../../services/customer.service'; import { FREE_MOBILE_COLORS } from '../../utils/constants'; interface CustomerProfileProps { userId?: string; } const CustomerProfile: React.FC<CustomerProfileProps> = ({ userId }) => { const [profile, setProfile] = useState<any>(null); const [loading, setLoading] = useState(true); const [error, setError] = useState<string | null>(null); const [editMode, setEditMode] = useState(false); const [editData, setEditData] = useState<any>({}); const [invoices, setInvoices] = useState<any[]>([]); const [consumption, setConsumption] = useState<any>(null); const dispatch = useDispatch(); const { user } = useSelector((state: RootState) => state.auth); useEffect(() => { loadCustomerData(); }, [userId]); const loadCustomerData = async () => { try { setLoading(true); setError(null); // Charger le profil client const profileResponse = await customerService.getProfile(); setProfile(profileResponse.data); setEditData(profileResponse.data); // Charger les factures const invoicesResponse = await customerService.getInvoices(); setInvoices(invoicesResponse); // Charger la consommation const consumptionResponse = await customerService.getConsumption(); setConsumption(consumptionResponse.data); } catch (err: any) { console.error('Error loading customer data:', err); setError(err.response?.data?.error || 'Erreur lors du chargement des données'); } finally { setLoading(false); } }; const handleSaveProfile = async () => { try { setError(null); const response = await customerService.updateProfile(editData); setProfile(response.data); setEditMode(false); } catch (err: any) { setError(err.response?.data?.error || 'Erreur lors de la mise à jour'); } }; const formatDataUsage = (used: number, limit: number) => { const usedGB = used; const limitGB = limit; const percentage = (used / limit) * 100; return { used: usedGB < 1 ? `${(usedGB * 1000).toFixed(0)} MB` : `${usedGB.toFixed(2)} GB`, limit: limitGB < 1 ? `${(limitGB * 1000).toFixed(0)} MB` : `${limitGB.toFixed(2)} GB`, percentage: Math.min(percentage, 100) }; }; const getUsageColor = (percentage: number) => { if (percentage < 50) return 'success'; if (percentage < 80) return 'warning'; return 'error'; }; if (loading) { return ( <Box sx={{ p: 3 }}> <LinearProgress /> <Typography sx={{ mt: 2 }}>Chargement du profil client...</Typography> </Box> ); } if (error) { return ( <Box sx={{ p: 3 }}> <Alert severity="error">{error}</Alert> <Button onClick={loadCustomerData} sx={{ mt: 2 }}> Réessayer </Button> </Box> ); } if (!profile) { return ( <Box sx={{ p: 3 }}> <Alert severity="info">Aucun profil client trouvé</Alert> </Box> ); } const dataUsage = formatDataUsage( profile.subscription?.dataUsed || 0, profile.subscription?.dataLimit || 1 ); return ( <Box sx={{ p: 3 }}> <Typography variant="h4" gutterBottom> Profil Client </Typography> {error && ( <Alert severity="error" sx={{ mb: 3 }}> {error} </Alert> )} <Grid container spacing={3}> {/* Informations personnelles */} <Grid item xs={12} md={6}> <Card> <CardContent> <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}> <Typography variant="h6"> Informations personnelles </Typography> <Button startIcon={editMode ? <Save /> : <Edit />} onClick={editMode ? handleSaveProfile : () => setEditMode(true)} color="primary" > {editMode ? 'Sauvegarder' : 'Modifier'} </Button> </Box> <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}> <Avatar sx={{ bgcolor: FREE_MOBILE_COLORS.PRIMARY, width: 64, height: 64, mr: 2, }} > <Person sx={{ fontSize: 32 }} /> </Avatar> <Box> <Typography variant="h6"> {user?.profile?.firstName} {user?.profile?.lastName} </Typography> <Typography variant="body2" color="text.secondary"> Client Free Mobile </Typography> </Box> </Box> <List> <ListItem> <ListItemIcon> <Email /> </ListItemIcon> <ListItemText primary="Email" secondary={user?.email} /> </ListItem> <ListItem> <ListItemIcon> <Phone /> </ListItemIcon> <ListItemText primary="Téléphone" secondary={user?.profile?.phoneNumber || 'Non renseigné'} /> </ListItem> </List> </CardContent> </Card> </Grid> {/* Abonnement */} <Grid item xs={12} md={6}> <Card> <CardContent> <Typography variant="h6" gutterBottom> Mon abonnement </Typography> <Box sx={{ mb: 2 }}> <Typography variant="h4" color="primary"> {profile.subscription?.planName || 'Forfait 2€'} </Typography> <Typography variant="body2" color="text.secondary"> {profile.subscription?.monthlyPrice || 2}€/mois </Typography> </Box> <Chip label={profile.subscription?.isActive ? 'Actif' : 'Inactif'} color={profile.subscription?.isActive ? 'success' : 'error'} sx={{ mb: 2 }} /> <Typography variant="body2" color="text.secondary"> Renouvellement: {new Date(profile.subscription?.renewalDate).toLocaleDateString()} </Typography> </CardContent> </Card> </Grid> {/* Consommation Data */} <Grid item xs={12} md={4}> <Card> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}> <DataUsage sx={{ mr: 1, color: FREE_MOBILE_COLORS.PRIMARY }} /> <Typography variant="h6">Data</Typography> </Box> <Typography variant="h4" gutterBottom> {dataUsage.used} </Typography> <Typography variant="body2" color="text.secondary" gutterBottom> sur {dataUsage.limit} </Typography> <LinearProgress variant="determinate" value={dataUsage.percentage} color={getUsageColor(dataUsage.percentage)} sx={{ height: 8, borderRadius: 4 }} /> <Typography variant="caption" color="text.secondary"> {dataUsage.percentage.toFixed(1)}% utilisé </Typography> </CardContent> </Card> </Grid> {/* Consommation Appels */} <Grid item xs={12} md={4}> <Card> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}> <CallMade sx={{ mr: 1, color: FREE_MOBILE_COLORS.SUCCESS }} /> <Typography variant="h6">Appels</Typography> </Box> <Typography variant="h4" gutterBottom> {profile.subscription?.callMinutesUsed || 0} </Typography> <Typography variant="body2" color="text.secondary" gutterBottom> sur {profile.subscription?.callMinutes || 0} min </Typography> <LinearProgress variant="determinate" value={((profile.subscription?.callMinutesUsed || 0) / (profile.subscription?.callMinutes || 1)) * 100} color="success" sx={{ height: 8, borderRadius: 4 }} /> </CardContent> </Card> </Grid> {/* Consommation SMS */} <Grid item xs={12} md={4}> <Card> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}> <Sms sx={{ mr: 1, color: FREE_MOBILE_COLORS.INFO }} /> <Typography variant="h6">SMS</Typography> </Box> <Typography variant="h4" gutterBottom> {profile.subscription?.smsUsed || 0} </Typography> <Typography variant="body2" color="text.secondary" gutterBottom> sur {profile.subscription?.smsLimit || 'Illimité'} </Typography> <LinearProgress variant="determinate" value={profile.subscription?.smsLimit ? ((profile.subscription?.smsUsed || 0) / profile.subscription.smsLimit) * 100 : 0} color="info" sx={{ height: 8, borderRadius: 4 }} /> </CardContent> </Card> </Grid> {/* Factures récentes */} <Grid item xs={12} md={6}> <Card> <CardContent> <Typography variant="h6" gutterBottom> Factures récentes </Typography> {invoices.length === 0 ? ( <Typography variant="body2" color="text.secondary"> Aucune facture disponible </Typography> ) : ( <List> {invoices.slice(0, 3).map((invoice, index) => ( <ListItem key={index} divider> <ListItemIcon> <Receipt /> </ListItemIcon> <ListItemText primary={`${invoice.amount}€`} secondary={new Date(invoice.dueDate).toLocaleDateString()} /> <Chip label={invoice.status === 'paid' ? 'Payée' : 'En attente'} color={invoice.status === 'paid' ? 'success' : 'warning'} size="small" /> </ListItem> ))} </List> )} <Button fullWidth sx={{ mt: 2 }}> Voir toutes les factures </Button> </CardContent> </Card> </Grid> {/* Statistiques */} <Grid item xs={12} md={6}> <Card> <CardContent> <Typography variant="h6" gutterBottom> Statistiques </Typography> <List> <ListItem> <ListItemIcon> <TrendingUp /> </ListItemIcon> <ListItemText primary="Score de satisfaction" secondary={`${profile.metrics?.csat || 0}/5`} /> </ListItem> <ListItem> <ListItemIcon> <Notifications /> </ListItemIcon> <ListItemText primary="Tickets de support" secondary={`${profile.metrics?.supportTicketsCount || 0} tickets`} /> </ListItem> </List> </CardContent> </Card> </Grid> </Grid> </Box> ); }; export default CustomerProfile;