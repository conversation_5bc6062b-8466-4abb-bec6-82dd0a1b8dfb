import React, { useEffect, useState } from 'react'; import { Box, Grid, Card, CardContent, Typography, LinearProgress, Button, IconButton, Fab, Chip, Avatar, List, ListItem, ListItemIcon, ListItemText, Badge, Paper, Divider, } from '@mui/material'; import { Phone, DataUsage, Euro, Notifications, Chat, Settings, TrendingUp, Download, Upload, Message, Call, Sms, Add, Remove, Warning, CheckCircle, Info, } from '@mui/icons-material'; import { useAuth } from '../hooks/useAuth'; import { FREE_MOBILE_COLORS, FREE_MOBILE_PLANS } from '../utils/constants'; interface UsageData { data: { used: number; total: number; unit: string }; calls: { used: number; total: number; unit: string; unlimited: boolean }; sms: { used: number; total: number; unlimited: boolean }; } interface PlanInfo { name: string; price: number; dataAllowance: string; features: string[]; } const DashboardPage: React.FC = () => { const { user } = useAuth(); // État simulé - Dans une vraie app, cela viendrait du backend const [usage] = useState<UsageData>({ data: { used: 45.2, total: 100, unit: 'GB' }, calls: { used: 127, total: 300, unit: 'min', unlimited: false }, sms: { used: 89, total: 0, unlimited: true }, }); const [currentPlan] = useState<PlanInfo>({ name: 'Free 100Go', price: 19.99, dataAllowance: '100Go 4G/5G', features: ['Appels illimités', 'SMS illimités', '100Go en France', '25Go en Europe'], }); const [notifications] = useState([ { id: 1, type: 'info' as const, title: 'Nouvelle fonctionnalité', message: 'Le Wi-Fi Free est maintenant disponible partout !', time: '2h', }, { id: 2, type: 'warning' as const, title: 'Consommation data', message: 'Vous avez consommé 80% de votre forfait.', time: '5h', }, { id: 3, type: 'success' as const, title: 'Facture payée', message: 'Votre facture de décembre a été réglée.', time: '1j', }, ]); const getUsageColor = (percentage: number) => { if (percentage >= 90) return FREE_MOBILE_COLORS.ERROR; if (percentage >= 75) return FREE_MOBILE_COLORS.WARNING; return FREE_MOBILE_COLORS.SUCCESS; }; const getNotificationIcon = (type: string) => { switch (type) { case 'warning': return <Warning color="warning" />; case 'success': return <CheckCircle color="success" />; default: return <Info color="info" />; } }; return ( <Box sx={{ p: 3 }}> {/* Header */} <Box sx={{ mb: 4 }}> <Typography variant="h4" gutterBottom fontWeight={600}> Tableau de bord </Typography> <Typography variant="subtitle1" color="text.secondary"> Bonjour {user?.profile?.firstName || user?.email} ! Voici un aperçu de votre compte Free Mobile. </Typography> </Box> <Grid container spacing={3}> {/* Consommation Data */} <Grid item xs={12} md={4}> <Card sx={{ height: '100%' }}> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}> <DataUsage color="primary" sx={{ mr: 1 }} /> <Typography variant="h6">Consommation Data</Typography> </Box> <Box sx={{ mb: 2 }}> <Typography variant="h3" color="primary" fontWeight={700}> {usage.data.used} </Typography> <Typography variant="body1" color="text.secondary"> sur {usage.data.total} {usage.data.unit} </Typography> </Box> <LinearProgress variant="determinate" value={(usage.data.used / usage.data.total) * 100} sx={{ height: 8, borderRadius: 4, mb: 2, backgroundColor: 'grey.200', '& .MuiLinearProgress-bar': { backgroundColor: getUsageColor((usage.data.used / usage.data.total) * 100), }, }} /> <Typography variant="body2" color="text.secondary"> {Math.round((usage.data.used / usage.data.total) * 100)}% utilisé </Typography> <Box sx={{ mt: 2, display: 'flex', gap: 1 }}> <Button size="small" variant="outlined" startIcon={<Add />}> Ajouter 20Go </Button> </Box> </CardContent> </Card> </Grid> {/* Appels */} <Grid item xs={12} md={4}> <Card sx={{ height: '100%' }}> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}> <Call color="primary" sx={{ mr: 1 }} /> <Typography variant="h6">Appels</Typography> </Box> {usage.calls.unlimited ? ( <Box> <Typography variant="h3" color="primary" fontWeight={700}> ∞ </Typography> <Typography variant="body1" color="text.secondary"> Appels illimités </Typography> <Chip label="Illimité" color="success" sx={{ mt: 1 }} /> </Box> ) : ( <Box> <Typography variant="h3" color="primary" fontWeight={700}> {usage.calls.used} </Typography> <Typography variant="body1" color="text.secondary"> sur {usage.calls.total} {usage.calls.unit} </Typography> <LinearProgress variant="determinate" value={(usage.calls.used / usage.calls.total) * 100} sx={{ height: 8, borderRadius: 4, mt: 2 }} /> </Box> )} </CardContent> </Card> </Grid> {/* SMS */} <Grid item xs={12} md={4}> <Card sx={{ height: '100%' }}> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}> <Sms color="primary" sx={{ mr: 1 }} /> <Typography variant="h6">SMS</Typography> </Box> <Typography variant="h3" color="primary" fontWeight={700}> {usage.sms.used} </Typography> <Typography variant="body1" color="text.secondary"> SMS envoyés </Typography> <Chip label="Illimité" color="success" sx={{ mt: 1 }} /> </CardContent> </Card> </Grid> {/* Forfait actuel */} <Grid item xs={12} md={6}> <Card> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}> <Phone color="primary" sx={{ mr: 1 }} /> <Typography variant="h6">Mon Forfait</Typography> </Box> <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}> <Box> <Typography variant="h5" fontWeight={600}> {currentPlan.name} </Typography> <Typography variant="h6" color="primary" fontWeight={700}> {currentPlan.price}€/mois </Typography> </Box> <Chip label={currentPlan.dataAllowance} color="primary" /> </Box> <List dense> {currentPlan.features.map((feature, index) => ( <ListItem key={index} sx={{ px: 0 }}> <ListItemIcon sx={{ minWidth: 32 }}> <CheckCircle color="success" fontSize="small" /> </ListItemIcon> <ListItemText primary={feature} /> </ListItem> ))} </List> <Button variant="outlined" fullWidth sx={{ mt: 2 }}> Changer de forfait </Button> </CardContent> </Card> </Grid> {/* Notifications */} <Grid item xs={12} md={6}> <Card> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}> <Notifications color="primary" sx={{ mr: 1 }} /> <Typography variant="h6">Notifications</Typography> </Box> <List> {notifications.map((notification, index) => ( <React.Fragment key={notification.id}> <ListItem sx={{ px: 0 }}> <ListItemIcon> {getNotificationIcon(notification.type)} </ListItemIcon> <ListItemText primary={notification.title} secondary={notification.message} /> <Typography variant="caption" color="text.secondary"> {notification.time} </Typography> </ListItem> {index < notifications.length - 1 && <Divider />} </React.Fragment> ))} </List> <Button variant="text" fullWidth sx={{ mt: 1 }}> Voir toutes les notifications </Button> </CardContent> </Card> </Grid> {/* Factures récentes */} <Grid item xs={12}> <Card> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}> <Box sx={{ display: 'flex', alignItems: 'center' }}> <Euro color="primary" sx={{ mr: 1 }} /> <Typography variant="h6">Factures récentes</Typography> </Box> <Button variant="outlined" size="small"> Voir tout </Button> </Box> <Grid container spacing={2}> {[ { month: 'Décembre 2024', amount: 19.99, status: 'Payée', date: '15/12/2024' }, { month: 'Novembre 2024', amount: 19.99, status: 'Payée', date: '15/11/2024' }, { month: 'Octobre 2024', amount: 24.99, status: 'Payée', date: '15/10/2024' }, ].map((invoice, index) => ( <Grid item xs={12} sm={4} key={index}> <Card variant="outlined"> <CardContent sx={{ py: 2 }}> <Typography variant="subtitle1" fontWeight={600}> {invoice.month} </Typography> <Typography variant="h6" color="primary"> {invoice.amount}€ </Typography> <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 1 }}> <Chip label={invoice.status} color="success" size="small" /> <IconButton size="small"> <Download fontSize="small" /> </IconButton> </Box> <Typography variant="caption" color="text.secondary"> {invoice.date} </Typography> </CardContent> </Card> </Grid> ))} </Grid> </CardContent> </Card> </Grid> </Grid> {/* Bouton d'action flottant pour le chat */} <Fab color="primary" sx={{ position: 'fixed', bottom: 24, right: 24, background: `linear-gradient(45deg, ${FREE_MOBILE_COLORS.PRIMARY} 30%, #FF3333 90%)`, '&:hover': { background: `linear-gradient(45deg, #CC0000 30%, ${FREE_MOBILE_COLORS.PRIMARY} 90%)`, }, }} onClick={() => { // Navigation vers le chat window.location.href = '/chat'; }} > <Chat /> </Fab> </Box> ); }; export default DashboardPage;