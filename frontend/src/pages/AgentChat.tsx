import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemButton,
  Chip,
  TextField,
  Button,
  Paper,
  Avatar,
  Badge,
} from '@mui/material';
import {
  Send,
  Person,
  SmartToy as Bot,
  Support,
  Circle,
} from '@mui/icons-material';
import { AppDispatch, RootState } from '../store';
import { fetchConversations } from '../store/adminSlice';
import { Message, Conversation } from '../types';
import adminService from '../services/admin.service';

const AgentChat: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { conversations } = useSelector((state: RootState) => state.admin);
  const { user } = useSelector((state: RootState) => state.auth);
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Charger les conversations actives et escaladées
    dispatch(fetchConversations({ status: 'active,escalated', limit: 50 }));
  }, [dispatch]);

  const loadConversationDetails = async (conversation: Conversation) => {
    try {
      setLoading(true);
      const response = await adminService.getConversationDetails(conversation._id);
      setSelectedConversation(conversation);
      setMessages(response.messages);
    } catch (error) {
      console.error('Error loading conversation:', error);
    } finally {
      setLoading(false);
    }
  };

  const sendAgentMessage = async () => {
    if (!selectedConversation || !newMessage.trim()) return;

    try {
      const messageData = {
        conversationId: selectedConversation._id,
        sender: 'agent' as const,
        content: {
          text: newMessage,
          type: 'text' as const,
        },
        timestamp: new Date(),
        metadata: {
          processingTime: 0,
          nlpProvider: 'agent',
          fallback: false,
        },
      };

      // Ajouter le message localement
      setMessages(prev => [...prev, { ...messageData, _id: Date.now().toString() }]);
      setNewMessage('');

      // TODO: Envoyer via WebSocket ou API
      console.log('Sending agent message:', messageData);
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  const getStatusColor = (status: string): "default" | "primary" | "secondary" | "error" | "info" | "success" | "warning" => {
    switch (status) {
      case 'active':
        return 'success';
      case 'escalated':
        return 'warning';
      default:
        return 'default';
    }
  };

  const formatTime = (date: string | Date) => {
    return new Date(date).toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getSenderIcon = (sender: string) => {
    switch (sender) {
      case 'user':
        return <Person />;
      case 'bot':
        return <Bot />;
      case 'agent':
        return <Support />;
      default:
        return <Circle />;
    }
  };

  const getSenderName = (sender: string) => {
    switch (sender) {
      case 'user':
        return 'Client';
      case 'bot':
        return 'Bot Assistant';
      case 'agent':
        return 'Agent';
      default:
        return sender;
    }
  };

  const activeConversations = conversations.filter(c =>
    c.status === 'active' || c.status === 'escalated'
  );

  return (
    <Box sx={{ height: 'calc(100vh - 100px)', p: 2 }}>
      <Typography variant="h4" gutterBottom>
        Interface Agent - Chat en Direct
      </Typography>
      <Grid container spacing={2} sx={{ height: '100%' }}>
        {/* Liste des conversations */}
        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent sx={{ pb: 1 }}>
              <Typography variant="h6" gutterBottom>
                Conversations Actives
                <Badge
                  badgeContent={activeConversations.length}
                  color="primary"
                  sx={{ ml: 2 }}
                />
              </Typography>
            </CardContent>
            <List sx={{ maxHeight: 'calc(100% - 80px)', overflow: 'auto' }}>
              {activeConversations.map((conversation) => (
                <ListItem key={conversation._id} disablePadding>
                  <ListItemButton
                    selected={selectedConversation?._id === conversation._id}
                    onClick={() => loadConversationDetails(conversation)}
                  >
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="body2">
                            {(conversation as any).userId?.email || 'Client Anonyme'}
                          </Typography>
                          <Chip
                            size="small"
                            label={conversation.status}
                            color={getStatusColor(conversation.status)}
                          />
                        </Box>
                      }
                      secondary={
                        <Typography variant="caption" color="text.secondary">
                          {new Date(conversation.startedAt).toLocaleString('fr-FR')}
                        </Typography>
                      }
                    />
                  </ListItemButton>
                </ListItem>
              ))}
            </List>
          </Card>
        </Grid>

        {/* Interface de chat */}
        <Grid item xs={12} md={8}>
          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            {selectedConversation ? (
              <>
                {/* En-tête de conversation */}
                <CardContent sx={{ pb: 1, borderBottom: '1px solid #e0e0e0' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Avatar sx={{ bgcolor: 'primary.main' }}>
                      <Person />
                    </Avatar>
                    <Box>
                      <Typography variant="h6">
                        {(selectedConversation as any).userId?.email || 'Client Anonyme'}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Session: {selectedConversation.sessionId}
                      </Typography>
                    </Box>
                    <Box sx={{ ml: 'auto' }}>
                      <Chip
                        label={selectedConversation.status}
                        color={getStatusColor(selectedConversation.status)}
                      />
                    </Box>
                  </Box>
                </CardContent>

                {/* Messages */}
                <Box sx={{
                  flex: 1,
                  overflow: 'auto',
                  p: 2,
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 1
                }}>
                  {messages.map((message) => (
                    <Paper
                      key={message._id}
                      sx={{
                        p: 2,
                        maxWidth: '70%',
                        alignSelf: message.sender === 'agent' ? 'flex-end' : 'flex-start',
                        bgcolor: message.sender === 'agent'
                          ? 'primary.light'
                          : message.sender === 'bot'
                          ? 'grey.100'
                          : 'background.paper',
                        color: message.sender === 'agent' ? 'white' : 'text.primary',
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                        {getSenderIcon(message.sender)}
                        <Typography variant="caption" sx={{ fontWeight: 'bold' }}>
                          {getSenderName(message.sender)}
                        </Typography>
                        <Typography variant="caption" sx={{ ml: 'auto', opacity: 0.7 }}>
                          {formatTime(message.timestamp)}
                        </Typography>
                      </Box>
                      <Typography variant="body1">
                        {message.content.text}
                      </Typography>
                      {message.intent && (
                        <Chip
                          size="small"
                          label={`${message.intent.name} (${Math.round(message.intent.confidence * 100)}%)`}
                          variant="outlined"
                          sx={{ mt: 1 }}
                        />
                      )}
                    </Paper>
                  ))}
                </Box>

                {/* Zone de saisie */}
                <Box sx={{ p: 2, borderTop: '1px solid #e0e0e0' }}>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <TextField
                      fullWidth
                      multiline
                      maxRows={3}
                      placeholder="Tapez votre réponse..."
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      onKeyPress={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault();
                          sendAgentMessage();
                        }
                      }}
                    />
                    <Button
                      variant="contained"
                      endIcon={<Send />}
                      onClick={sendAgentMessage}
                      disabled={!newMessage.trim()}
                    >
                      Envoyer
                    </Button>
                  </Box>
                </Box>
              </>
            ) : (
              <CardContent sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                height: '100%'
              }}>
                <Typography variant="h6" color="text.secondary">
                  Sélectionnez une conversation pour commencer
                </Typography>
              </CardContent>
            )}
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AgentChat;