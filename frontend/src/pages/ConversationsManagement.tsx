import React, { useEffect, useState } from 'react'; import { useDispatch, useSelector } from 'react-redux'; import { Box, Card, CardContent, Typography, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TablePagination, Chip, Button, TextField, FormControl, InputLabel, Select, MenuItem, Dialog, DialogTitle, DialogContent, DialogActions, Alert, CircularProgress, IconButton, Grid, } from '@mui/material'; import { Visibility, Assignment, Close, Search, Refresh, } from '@mui/icons-material'; import { AppDispatch, RootState } from '../store'; import { fetchConversations, assignAgent, closeConversation } from '../store/adminSlice'; import { Conversation } from '../types'; const ConversationsManagement: React.FC = () => { const dispatch = useDispatch<AppDispatch>(); const { conversations, loading, error } = useSelector((state: RootState) => state.admin); const [page, setPage] = useState(0); const [rowsPerPage, setRowsPerPage] = useState(10); const [statusFilter, setStatusFilter] = useState(''); const [searchTerm, setSearchTerm] = useState(''); const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null); const [assignDialogOpen, setAssignDialogOpen] = useState(false); const [closeDialogOpen, setCloseDialogOpen] = useState(false); const [agentId, setAgentId] = useState(''); const [satisfaction, setSatisfaction] = useState({ rating: 5, feedback: '' }); useEffect(() => { loadConversations(); }, [page, rowsPerPage, statusFilter, searchTerm]); const loadConversations = () => { dispatch(fetchConversations({ page: page + 1, limit: rowsPerPage, status: statusFilter || undefined, search: searchTerm || undefined, })); }; const handlePageChange = (event: unknown, newPage: number) => { setPage(newPage); }; const handleRowsPerPageChange = (event: React.ChangeEvent<HTMLInputElement>) => { setRowsPerPage(parseInt(event.target.value, 10)); setPage(0); }; const handleSearch = () => { setPage(0); loadConversations(); }; const handleAssignAgent = async () => { if (selectedConversation && agentId) { await dispatch(assignAgent({ conversationId: selectedConversation._id, agentId })); setAssignDialogOpen(false); setAgentId(''); loadConversations(); } }; const handleCloseConversation = async () => { if (selectedConversation) { await dispatch(closeConversation({ conversationId: selectedConversation._id, satisfaction: satisfaction.rating > 0 ? satisfaction : undefined })); setCloseDialogOpen(false); setSatisfaction({ rating: 5, feedback: '' }); loadConversations(); } }; const getStatusColor = (status: string): "default" | "primary" | "secondary" | "error" | "info" | "success" | "warning" => { switch (status) { case 'active': return 'primary'; case 'resolved': return 'success'; case 'escalated': return 'warning'; case 'abandoned': return 'error'; default: return 'default'; } }; const formatDate = (date: string | Date) => { return new Date(date).toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit', year: 'numeric', hour: '2-digit', minute: '2-digit', }); }; if (loading && conversations.length === 0) { return ( <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px"> <CircularProgress /> </Box> ); } return ( <Box sx={{ p: 3 }}> <Typography variant="h4" gutterBottom> Gestion des Conversations </Typography> {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>} {/* Filtres et recherche */} <Card sx={{ mb: 3 }}> <CardContent> <Grid container spacing={2} alignItems="center"> <Grid item xs={12} sm={4}> <TextField fullWidth label="Rechercher" value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} InputProps={{ endAdornment: ( <IconButton onClick={handleSearch}> <Search /> </IconButton> ), }} /> </Grid> <Grid item xs={12} sm={3}> <FormControl fullWidth> <InputLabel>Statut</InputLabel> <Select value={statusFilter} onChange={(e) => setStatusFilter(e.target.value)} label="Statut" > <MenuItem value="">Tous</MenuItem> <MenuItem value="active">Active</MenuItem> <MenuItem value="resolved">Résolue</MenuItem> <MenuItem value="escalated">Escaladée</MenuItem> <MenuItem value="abandoned">Abandonnée</MenuItem> </Select> </FormControl> </Grid> <Grid item xs={12} sm={2}> <Button fullWidth variant="outlined" startIcon={<Refresh />} onClick={loadConversations} > Actualiser </Button> </Grid> </Grid> </CardContent> </Card> {/* Tableau des conversations */} <Card> <TableContainer> <Table> <TableHead> <TableRow> <TableCell>ID Session</TableCell> <TableCell>Client</TableCell> <TableCell>Statut</TableCell> <TableCell>Canal</TableCell> <TableCell>Date de début</TableCell> <TableCell>Agent</TableCell> <TableCell>Actions</TableCell> </TableRow> </TableHead> <TableBody> {conversations.map((conversation) => ( <TableRow key={conversation._id}> <TableCell>{conversation.sessionId}</TableCell> <TableCell> {(conversation as any).userId?.email || 'Anonyme'} </TableCell> <TableCell> <Chip label={conversation.status} color={getStatusColor(conversation.status)} size="small" /> </TableCell> <TableCell>{conversation.channel}</TableCell> <TableCell>{formatDate(conversation.startedAt)}</TableCell> <TableCell> {(conversation as any).agentId?.email || 'Non assigné'} </TableCell> <TableCell> <IconButton size="small" onClick={() => { // Navigate to conversation details console.log('View conversation:', conversation._id); }} > <Visibility /> </IconButton> {conversation.status === 'active' && ( <IconButton size="small" onClick={() => { setSelectedConversation(conversation); setAssignDialogOpen(true); }} > <Assignment /> </IconButton> )} {conversation.status !== 'resolved' && ( <IconButton size="small" onClick={() => { setSelectedConversation(conversation); setCloseDialogOpen(true); }} > <Close /> </IconButton> )} </TableCell> </TableRow> ))} </TableBody> </Table> </TableContainer> <TablePagination component="div" count={conversations.length} page={page} onPageChange={handlePageChange} rowsPerPage={rowsPerPage} onRowsPerPageChange={handleRowsPerPageChange} labelRowsPerPage="Lignes par page:" /> </Card> {/* Dialog d'assignation d'agent */} <Dialog open={assignDialogOpen} onClose={() => setAssignDialogOpen(false)}> <DialogTitle>Assigner un Agent</DialogTitle> <DialogContent> <TextField fullWidth label="ID de l'agent" value={agentId} onChange={(e) => setAgentId(e.target.value)} margin="normal" helperText="Entrez l'ID de l'agent à assigner" /> </DialogContent> <DialogActions> <Button onClick={() => setAssignDialogOpen(false)}> Annuler </Button> <Button onClick={handleAssignAgent} variant="contained"> Assigner </Button> </DialogActions> </Dialog> {/* Dialog de fermeture de conversation */} <Dialog open={closeDialogOpen} onClose={() => setCloseDialogOpen(false)}> <DialogTitle>Fermer la Conversation</DialogTitle> <DialogContent> <TextField fullWidth type="number" label="Note de satisfaction (1-5)" value={satisfaction.rating} onChange={(e) => setSatisfaction({ ...satisfaction, rating: parseInt(e.target.value) })} margin="normal" inputProps={{ min: 1, max: 5 }} /> <TextField fullWidth multiline rows={3} label="Commentaire (optionnel)" value={satisfaction.feedback} onChange={(e) => setSatisfaction({ ...satisfaction, feedback: e.target.value })} margin="normal" /> </DialogContent> <DialogActions> <Button onClick={() => setCloseDialogOpen(false)}> Annuler </Button> <Button onClick={handleCloseConversation} variant="contained"> Fermer </Button> </DialogActions> </Dialog> </Box> ); }; export default ConversationsManagement;