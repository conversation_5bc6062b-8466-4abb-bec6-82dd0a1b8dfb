import React, { useState, useEffect } from 'react'; import { <PERSON>, Typo<PERSON>, Tabs, Tab, Card, CardContent, TextField, Button, Grid, FormControl, InputLabel, Select, MenuItem, Chip, List, ListItem, ListItemText, ListItemIcon, Accordion, AccordionSummary, AccordionDetails, Alert, LinearProgress, Paper, IconButton, Badge, Tooltip, Divider, Avatar, Snackbar, CircularProgress, } from '@mui/material'; import { ExpandMore, Support, ChatBubble, Help, Analytics, Report, Star, TrendingUp, TrendingDown, Phone, Email, Message, AccessTime, CheckCircle, Warning, Error as ErrorIcon, Info, Send, Attachment, Search, Settings, Circle as OnlineIcon, SupportAgent, Notifications, Refresh, } from '@mui/icons-material'; import { FREE_MOBILE_COLORS } from '../utils/constants'; // Types pour les analytics interface AnalyticsData { totalTickets: number; csatScore: number; averageTime: string; resolutionRate: number; } interface TabPanelProps { children?: React.ReactNode; index: number; value: number; } function TabPanel(props: TabPanelProps) { const { children, value, index, ...other } = props; return ( <div role="tabpanel" hidden={value !== index} id={`simple-tabpanel-${index}`} aria-labelledby={`simple-tab-${index}`} {...other} > {value === index && <Box sx={{ p: 3 }}>{children}</Box>} </div> ); } const SupportHomePage: React.FC = () => { const [value, setValue] = useState(0); const [formData, setFormData] = useState({ fullName: '', clientId: '', phoneNumber: '', issueType: '', priority: '', description: '', }); const [chatMessage, setChatMessage] = useState(''); const [chatHistory, setChatHistory] = useState<Array<{ id: number; sender: 'user' | 'bot' | 'agent'; message: string; timestamp: Date; senderName?: string; }>>([ { id: 1, sender: 'bot', message: 'Bonjour ! Je suis votre assistant Free Mobile. Comment puis-je vous aider aujourd\'hui ?', timestamp: new Date(), senderName: 'Assistant Free', }, ]); // Enhanced state for live chat functionality const [isChatConnected, setIsChatConnected] = useState(true); const [isTyping, setIsTyping] = useState(false); const [unreadMessages, setUnreadMessages] = useState(0); const [snackbarOpen, setSnackbarOpen] = useState(false); const [snackbarMessage, setSnackbarMessage] = useState(''); // Enhanced analytics with real-time updates const [analytics, setAnalytics] = useState<AnalyticsData>({ totalTickets: 1547, csatScore: 4.2, averageTime: '2h 15m', resolutionRate: 94.5, }); const [analyticsLoading, setAnalyticsLoading] = useState(false); // Real-time statistics const [realtimeStats, setRealtimeStats] = useState({ activeAgents: 12, queueLength: 3, averageWaitTime: '45s', onlineUsers: 156, lastUpdated: new Date(), }); // Real-time updates for statistics useEffect(() => { const interval = setInterval(() => { setRealtimeStats(prev => ({ ...prev, activeAgents: Math.floor(Math.random() * 5) + 10, queueLength: Math.floor(Math.random() * 8), averageWaitTime: `${Math.floor(Math.random() * 60) + 30}s`, onlineUsers: Math.floor(Math.random() * 50) + 130, lastUpdated: new Date(), })); }, 30000); // Update every 30 seconds return () => clearInterval(interval); }, []); // Simulate chat connection status useEffect(() => { const connectionCheck = setInterval(() => { // Simulate occasional disconnections for realism const shouldDisconnect = Math.random() < 0.05; // 5% chance if (shouldDisconnect && isChatConnected) { setIsChatConnected(false); setSnackbarMessage('Connexion au chat interrompue. Reconnexion en cours...'); setSnackbarOpen(true); // Reconnect after 3 seconds setTimeout(() => { setIsChatConnected(true); setSnackbarMessage('Connexion au chat rétablie !'); setSnackbarOpen(true); }, 3000); } }, 60000); // Check every minute return () => clearInterval(connectionCheck); }, [isChatConnected]); const handleChange = (event: React.SyntheticEvent, newValue: number) => { setValue(newValue); // Reset unread messages when switching to chat tab if (newValue === 1) { setUnreadMessages(0); } }; const handleFormSubmit = (e: React.FormEvent) => { e.preventDefault(); console.log('Form submitted:', formData); // Logic pour envoyer le formulaire }; const handleChatSubmit = (e: React.FormEvent) => { e.preventDefault(); if (!chatMessage.trim() || !isChatConnected) return; const newMessage = { id: chatHistory.length + 1, sender: 'user' as const, message: chatMessage, timestamp: new Date(), senderName: 'Vous', }; setChatHistory([...chatHistory, newMessage]); setChatMessage(''); setIsTyping(true); // Enhanced bot response with more realistic behavior setTimeout(() => { setIsTyping(false); const responses = [ 'Merci pour votre message. Un agent va vous répondre dans quelques instants.', 'Je comprends votre demande. Laissez-moi vérifier les informations disponibles.', 'Votre demande a été transmise à notre équipe support. Temps d\'attente estimé : 2-3 minutes.', 'Je vais vous mettre en relation avec un agent spécialisé pour votre demande.', ]; const randomResponse = responses[Math.floor(Math.random() * responses.length)]; const botResponse = { id: chatHistory.length + 2, sender: 'bot' as const, message: randomResponse, timestamp: new Date(), senderName: 'Assistant Free', }; setChatHistory(prev => [...prev, botResponse]); // Simulate agent joining occasionally if (Math.random() < 0.3) { // 30% chance setTimeout(() => { const agentMessage = { id: Date.now(), sender: 'agent' as const, message: 'Bonjour ! Je suis Sarah, votre conseillère Free Mobile. Comment puis-je vous aider ?', timestamp: new Date(), senderName: 'Sarah - Conseillère', }; setChatHistory(prev => [...prev, agentMessage]); setUnreadMessages(prev => prev + 1); }, 3000); } }, 1500); }; // Function to refresh analytics const refreshAnalytics = async () => { setAnalyticsLoading(true); // Simulate API call setTimeout(() => { setAnalytics(prev => ({ ...prev, totalTickets: prev.totalTickets + Math.floor(Math.random() * 10), csatScore: Math.round((Math.random() * 0.5 + 4.0) * 10) / 10, resolutionRate: Math.round((Math.random() * 5 + 92) * 10) / 10, })); setAnalyticsLoading(false); setSnackbarMessage('Statistiques mises à jour !'); setSnackbarOpen(true); }, 1000); }; const faqData = [ { question: 'Comment puis-je consulter ma consommation ?', answer: 'Vous pouvez consulter votre consommation via l\'Espace Abonné Free Mobile ou l\'application mobile Free.', }, { question: 'Que faire en cas de perte de mon téléphone ?', answer: 'En cas de perte, connectez-vous immédiatement à votre Espace Abonné pour suspendre votre ligne et demander une nouvelle carte SIM.', }, { question: 'Comment modifier mon forfait ?', answer: 'Vous pouvez modifier votre forfait depuis votre Espace Abonné dans la section "Gérer mon compte".', }, ]; return ( <Box sx={{ width: '100%', bgcolor: 'background.default', minHeight: '100vh' }}> <Box sx={{ borderBottom: 1, borderColor: 'divider' }}> <Tabs value={value} onChange={handleChange} aria-label="support tabs" sx={{ '& .MuiTab-root': { color: FREE_MOBILE_COLORS.TEXT_PRIMARY, '&.Mui-selected': { color: FREE_MOBILE_COLORS.PRIMARY, }, }, '& .MuiTabs-indicator': { backgroundColor: FREE_MOBILE_COLORS.PRIMARY, }, }} > <Tab icon={<Support />} label="Formulaire Support" /> <Tab icon={ <Badge badgeContent={unreadMessages} color="error"> <ChatBubble /> </Badge> } label="Chat en Direct" /> <Tab icon={<Help />} label="FAQ" /> <Tab icon={<Analytics />} label="Statistiques" /> </Tabs> </Box> <TabPanel value={value} index={0}> <Typography variant="h4" gutterBottom sx={{ color: FREE_MOBILE_COLORS.PRIMARY }}> Formulaire de Support </Typography> <Card sx={{ maxWidth: 800, mx: 'auto' }}> <CardContent> <form onSubmit={handleFormSubmit}> <Grid container spacing={3}> <Grid xs={12} sm={6}> <TextField fullWidth label="Nom complet" required value={formData.fullName} onChange={(e) => setFormData({...formData, fullName: e.target.value})} /> </Grid> <Grid xs={12} sm={6}> <TextField fullWidth label="ID Client Free" required value={formData.clientId} onChange={(e) => setFormData({...formData, clientId: e.target.value})} /> </Grid> <Grid xs={12} sm={6}> <TextField fullWidth label="Numéro de téléphone" value={formData.phoneNumber} onChange={(e) => setFormData({...formData, phoneNumber: e.target.value})} /> </Grid> <Grid xs={12} sm={6}> <FormControl fullWidth> <InputLabel>Type de problème</InputLabel> <Select value={formData.issueType} onChange={(e) => setFormData({...formData, issueType: e.target.value})} > <MenuItem value="technique">Problème technique</MenuItem> <MenuItem value="facturation">Facturation</MenuItem> <MenuItem value="reseau">Réseau/Couverture</MenuItem> <MenuItem value="forfait">Forfait/Options</MenuItem> <MenuItem value="autre">Autre</MenuItem> </Select> </FormControl> </Grid> <Grid xs={12} sm={6}> <FormControl fullWidth> <InputLabel>Priorité</InputLabel> <Select value={formData.priority} onChange={(e) => setFormData({...formData, priority: e.target.value})} > <MenuItem value="low">Faible</MenuItem> <MenuItem value="medium">Moyenne</MenuItem> <MenuItem value="high">Élevée</MenuItem> <MenuItem value="urgent">Urgente</MenuItem> </Select> </FormControl> </Grid> <Grid xs={12}> <TextField fullWidth multiline rows={4} label="Description du problème" required value={formData.description} onChange={(e) => setFormData({...formData, description: e.target.value})} /> </Grid> <Grid xs={12}> <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}> <Button variant="outlined">Annuler</Button> <Button type="submit" variant="contained" sx={{ bgcolor: FREE_MOBILE_COLORS.PRIMARY, '&:hover': { bgcolor: FREE_MOBILE_COLORS.SECONDARY } }} > Envoyer </Button> </Box> </Grid> </Grid> </form> </CardContent> </Card> </TabPanel> <TabPanel value={value} index={1}> <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}> <Typography variant="h4" sx={{ color: FREE_MOBILE_COLORS.PRIMARY, flexGrow: 1 }}> Chat en Direct </Typography> <Chip icon={<OnlineIcon sx={{ fontSize: '12px !important' }} />} label={isChatConnected ? 'En ligne' : 'Hors ligne'} color={isChatConnected ? 'success' : 'error'} size="small" /> <Badge badgeContent={unreadMessages} color="error"> <Notifications /> </Badge> </Box> {/* Connection Status Alert */} {!isChatConnected && ( <Alert severity="warning" sx={{ mb: 2, maxWidth: 800, mx: 'auto' }}> Connexion au chat interrompue. Reconnexion en cours... </Alert> )} <Card sx={{ maxWidth: 800, mx: 'auto', height: 600 }}> {/* Chat Header */} <Box sx={{ p: 2, bgcolor: FREE_MOBILE_COLORS.PRIMARY, color: 'white' }}> <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}> <Avatar sx={{ bgcolor: 'white', color: FREE_MOBILE_COLORS.PRIMARY }}> <SupportAgent /> </Avatar> <Box sx={{ flexGrow: 1 }}> <Typography variant="h6" fontWeight="bold"> Support Free Mobile </Typography> <Typography variant="body2" sx={{ opacity: 0.9 }}> {realtimeStats.activeAgents} agents en ligne • {realtimeStats.queueLength} en attente </Typography> </Box> <Chip label={`Temps d'attente: ${realtimeStats.averageWaitTime}`} size="small" sx={{ bgcolor: 'white', color: FREE_MOBILE_COLORS.PRIMARY }} /> </Box> </Box> <Divider /> <CardContent sx={{ height: 'calc(100% - 140px)', display: 'flex', flexDirection: 'column', p: 0 }}> {/* Messages Area */} <Box sx={{ flexGrow: 1, overflow: 'auto', p: 2, bgcolor: 'grey.50' }}> {chatHistory.map((msg) => ( <Box key={msg.id} sx={{ display: 'flex', justifyContent: msg.sender === 'user' ? 'flex-end' : 'flex-start', mb: 2, }} > <Box sx={{ maxWidth: '70%' }}> {msg.sender !== 'user' && ( <Typography variant="caption" color="textSecondary" sx={{ ml: 1 }}> {msg.senderName} </Typography> )} <Paper sx={{ p: 2, bgcolor: msg.sender === 'user' ? FREE_MOBILE_COLORS.PRIMARY : msg.sender === 'agent' ? '#e3f2fd' : 'white', color: msg.sender === 'user' ? 'white' : 'black', borderRadius: 2, boxShadow: 1, }} > <Typography variant="body2">{msg.message}</Typography> <Typography variant="caption" sx={{ opacity: 0.7, display: 'block', mt: 0.5 }}> {msg.timestamp.toLocaleTimeString()} </Typography> </Paper> </Box> </Box> ))} {/* Typing Indicator */} {isTyping && ( <Box sx={{ display: 'flex', justifyContent: 'flex-start', mb: 2 }}> <Paper sx={{ p: 2, bgcolor: 'white', borderRadius: 2 }}> <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}> <CircularProgress size={16} /> <Typography variant="body2" color="textSecondary"> Assistant Free tape... </Typography> </Box> </Paper> </Box> )} </Box> <Divider /> {/* Message Input */} <Box sx={{ p: 2 }}> <form onSubmit={handleChatSubmit}> <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}> <TextField fullWidth multiline maxRows={3} placeholder={isChatConnected ? "Tapez votre message..." : "Chat hors ligne"} value={chatMessage} onChange={(e) => setChatMessage(e.target.value)} disabled={!isChatConnected} variant="outlined" size="small" sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2, }, }} /> <Tooltip title={isChatConnected ? "Envoyer" : "Chat hors ligne"}> <span> <IconButton type="submit" disabled={!chatMessage.trim() || !isChatConnected} sx={{ bgcolor: FREE_MOBILE_COLORS.PRIMARY, color: 'white', '&:hover': { bgcolor: '#cc0000' }, '&:disabled': { bgcolor: 'grey.300' } }} > <Send /> </IconButton> </span> </Tooltip> </Box> </form> </Box> </CardContent> </Card> </TabPanel> <TabPanel value={value} index={2}> <Typography variant="h4" gutterBottom sx={{ color: FREE_MOBILE_COLORS.PRIMARY }}> Foire Aux Questions </Typography> <Box sx={{ maxWidth: 800, mx: 'auto' }}> {faqData.map((faq, index) => ( <Accordion key={index}> <AccordionSummary expandIcon={<ExpandMore />}> <Typography variant="h6">{faq.question}</Typography> </AccordionSummary> <AccordionDetails> <Typography>{faq.answer}</Typography> </AccordionDetails> </Accordion> ))} <Box sx={{ mt: 3, textAlign: 'center' }}> <Typography variant="body2" color="textSecondary"> Vous ne trouvez pas de réponse ? Contactez notre support ! </Typography> <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center', gap: 2 }}> <Chip icon={<Phone />} label="3244" clickable /> <Chip icon={<Email />} label="<EMAIL>" clickable /> <Chip icon={<Message />} label="Chat" clickable /> </Box> </Box> </Box> </TabPanel> <TabPanel value={value} index={3}> <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}> <Typography variant="h4" sx={{ color: FREE_MOBILE_COLORS.PRIMARY, flexGrow: 1 }}> Statistiques du Support </Typography> <Tooltip title="Actualiser les données"> <IconButton onClick={refreshAnalytics} disabled={analyticsLoading} sx={{ color: FREE_MOBILE_COLORS.PRIMARY }} > {analyticsLoading ? <CircularProgress size={24} /> : <Refresh />} </IconButton> </Tooltip> <Chip label={`Dernière mise à jour: ${realtimeStats.lastUpdated.toLocaleTimeString()}`} size="small" variant="outlined" /> </Box> {/* Real-time Status Bar */} <Card sx={{ mb: 3, bgcolor: '#f5f5f5' }}> <CardContent sx={{ py: 2 }}> <Grid container spacing={3} alignItems="center"> <Grid xs={12} sm={3}> <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}> <OnlineIcon sx={{ color: '#4caf50', fontSize: 12 }} /> <Typography variant="body2"> <strong>{realtimeStats.activeAgents}</strong> agents en ligne </Typography> </Box> </Grid> <Grid xs={12} sm={3}> <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}> <Typography variant="body2"> <strong>{realtimeStats.queueLength}</strong> en file d'attente </Typography> </Box> </Grid> <Grid xs={12} sm={3}> <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}> <Typography variant="body2"> Attente moyenne: <strong>{realtimeStats.averageWaitTime}</strong> </Typography> </Box> </Grid> <Grid xs={12} sm={3}> <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}> <Typography variant="body2"> <strong>{realtimeStats.onlineUsers}</strong> utilisateurs connectés </Typography> </Box> </Grid> </Grid> </CardContent> </Card> {/* Enhanced Metrics Cards */} <Grid container spacing={3} sx={{ mb: 3 }}> <Grid xs={12} sm={6} md={3}> <Card sx={{ height: '100%', position: 'relative', overflow: 'visible' }}> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}> <Avatar sx={{ bgcolor: FREE_MOBILE_COLORS.PRIMARY, mr: 2 }}> <Report /> </Avatar> <Box sx={{ flexGrow: 1 }}> <Typography variant="h4" color="primary"> {analytics.totalTickets.toLocaleString()} </Typography> <Typography variant="body2" color="textSecondary"> Total Tickets </Typography> </Box> <TrendingUp sx={{ color: '#4caf50' }} /> </Box> <Chip label="+12 aujourd'hui" size="small" color="success" sx={{ position: 'absolute', top: 8, right: 8 }} /> </CardContent> </Card> </Grid> <Grid xs={12} sm={6} md={3}> <Card sx={{ height: '100%', position: 'relative', overflow: 'visible' }}> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}> <Avatar sx={{ bgcolor: '#4caf50', mr: 2 }}> <Star /> </Avatar> <Box sx={{ flexGrow: 1 }}> <Typography variant="h4" color="success.main"> {analytics.csatScore}/5 </Typography> <Typography variant="body2" color="textSecondary"> Score CSAT </Typography> </Box> <TrendingUp sx={{ color: '#4caf50' }} /> </Box> <Chip label="Excellent" size="small" color="success" sx={{ position: 'absolute', top: 8, right: 8 }} /> </CardContent> </Card> </Grid> <Grid xs={12} sm={6} md={3}> <Card sx={{ height: '100%', position: 'relative', overflow: 'visible' }}> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}> <Avatar sx={{ bgcolor: '#ff9800', mr: 2 }}> <AccessTime /> </Avatar> <Box sx={{ flexGrow: 1 }}> <Typography variant="h4" color="warning.main"> {analytics.averageTime} </Typography> <Typography variant="body2" color="textSecondary"> Temps Moyen </Typography> </Box> <TrendingDown sx={{ color: '#4caf50' }} /> </Box> <Chip label="-15min" size="small" color="success" sx={{ position: 'absolute', top: 8, right: 8 }} /> </CardContent> </Card> </Grid> <Grid xs={12} sm={6} md={3}> <Card sx={{ height: '100%', position: 'relative', overflow: 'visible' }}> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}> <Avatar sx={{ bgcolor: '#4caf50', mr: 2 }}> <CheckCircle /> </Avatar> <Box sx={{ flexGrow: 1 }}> <Typography variant="h4" color="success.main"> {analytics.resolutionRate}% </Typography> <Typography variant="body2" color="textSecondary"> Taux de Résolution </Typography> </Box> <TrendingUp sx={{ color: '#4caf50' }} /> </Box> <Chip label="+2.1%" size="small" color="success" sx={{ position: 'absolute', top: 8, right: 8 }} /> </CardContent> </Card> </Grid> </Grid> {/* Graphiques */} <Grid container spacing={3}> <Grid xs={12} md={6}> <Card> <CardContent> <Typography variant="h6" gutterBottom> Évolution des Tickets </Typography> <Box sx={{ height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center' }}> <Typography color="textSecondary">Graphique en cours de développement</Typography> </Box> </CardContent> </Card> </Grid> <Grid xs={12} md={6}> <Card> <CardContent> <Typography variant="h6" gutterBottom> Répartition par Type </Typography> <Box sx={{ height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center' }}> <Typography color="textSecondary">Graphique en cours de développement</Typography> </Box> </CardContent> </Card> </Grid> </Grid> {/* Analyses supplémentaires */} <Grid container spacing={3} sx={{ mt: 3 }}> <Grid xs={12} md={6}> <Card> <CardContent> <Typography variant="h6" gutterBottom> Satisfaction Client </Typography> <Box sx={{ mb: 2 }}> <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}> <Typography>Très Satisfait</Typography> <Typography>65%</Typography> </Box> <LinearProgress variant="determinate" value={65} sx={{ height: 8, borderRadius: 4 }} /> </Box> <Box sx={{ mb: 2 }}> <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}> <Typography>Satisfait</Typography> <Typography>25%</Typography> </Box> <LinearProgress variant="determinate" value={25} sx={{ height: 8, borderRadius: 4 }} /> </Box> <Box> <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}> <Typography>Insatisfait</Typography> <Typography>10%</Typography> </Box> <LinearProgress variant="determinate" value={10} sx={{ height: 8, borderRadius: 4 }} /> </Box> </CardContent> </Card> </Grid> <Grid xs={12} md={6}> <Card> <CardContent> <Typography variant="h6" gutterBottom> Tendances Récentes </Typography> <List dense> <ListItem> <ListItemIcon> <TrendingUp sx={{ color: '#4caf50' }} /> </ListItemIcon> <ListItemText primary="Amélioration du temps de réponse" secondary="+15% cette semaine" /> </ListItem> <ListItem> <ListItemIcon> <Warning sx={{ color: '#ff9800' }} /> </ListItemIcon> <ListItemText primary="Pic de demandes facturation" secondary="Période de facturation mensuelle" /> </ListItem> <ListItem> <ListItemIcon> <Info sx={{ color: FREE_MOBILE_COLORS.PRIMARY }} /> </ListItemIcon> <ListItemText primary="Nouveau service chat" secondary="Déployé avec succès" /> </ListItem> </List> </CardContent> </Card> </Grid> </Grid> </TabPanel> {/* Snackbar for notifications */} <Snackbar open={snackbarOpen} autoHideDuration={4000} onClose={() => setSnackbarOpen(false)} message={snackbarMessage} anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }} /> </Box> ); }; export default SupportHomePage;