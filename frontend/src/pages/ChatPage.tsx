import React, { useState, useEffect, useRef, useCallback } from 'react'; import { Box, Paper, Typography, TextField, IconButton, Button, Chip, Avatar, Card, CardContent, Grid, List, ListItem, ListItemIcon, ListItemText, Fab, Menu, MenuItem, Dialog, DialogTitle, DialogContent, DialogActions, Badge, Tooltip, Divider, LinearProgress, } from '@mui/material'; import { Send, AttachFile, Image, Mic, MicOff, VideoCall, ScreenShare, LocationOn, EmojiEmotions, MoreVert, SmartToy, SupportAgent, Phone, DataUsage, Euro, Settings, Warning, CheckCircle, Info, Close, ExpandMore, StarRate, ThumbUp, ThumbDown, } from '@mui/icons-material'; import { useSelector, useDispatch } from 'react-redux'; import { RootState, AppDispatch } from '../store'; import { startConversation, sendMessage, addMessage, setTyping, escalateToAgent, clearError, updateTypingUsers, setConnectionStatus, setRealTimeEnabled } from '../store/slices/chatSlice'; import { showNotification } from '../store/slices/uiSlice'; import webSocketService from '../services/websocket.service'; import { FREE_MOBILE_COLORS } from '../utils/constants'; import ReactMarkdown from 'react-markdown'; // Composants pour les différents types de messages const MessageBubble: React.FC<{ message: any; isOwn: boolean; onAction?: (action: string, payload: any) => void }> = ({ message, isOwn, onAction }) => { const renderContent = () => { switch (message.content.type) { case 'text': return ( <ReactMarkdown> {message.content.text} </ReactMarkdown> ); case 'button': return ( <Box> <Typography variant="body1" sx={{ mb: 2 }}> {message.content.text} </Typography> <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}> {message.content.payload?.buttons?.map((button: any, index: number) => ( <Button key={index} variant={button.style === 'primary' ? 'contained' : 'outlined'} color={button.style === 'danger' ? 'error' : 'primary'} size="small" onClick={() => onAction?.(button.action, button.payload)} sx={{ textTransform: 'none' }} > {button.text} </Button> ))} </Box> </Box> ); case 'card': const card = message.content.payload?.card; return ( <Card sx={{ maxWidth: 300 }}> {card?.imageUrl && ( <Box component="img" sx={{ width: '100%', height: 160, objectFit: 'cover' }} src={card.imageUrl} alt={card.title} /> )} <CardContent> <Typography variant="h6" gutterBottom> {card?.title} </Typography> {card?.subtitle && ( <Typography variant="subtitle2" color="text.secondary" gutterBottom> {card.subtitle} </Typography> )} <Typography variant="body2"> {card?.description} </Typography> {card?.buttons && ( <Box sx={{ mt: 2, display: 'flex', gap: 1 }}> {card.buttons.map((button: any, index: number) => ( <Button key={index} size="small" variant="outlined" onClick={() => onAction?.(button.action, button.payload)} > {button.text} </Button> ))} </Box> )} </CardContent> </Card> ); case 'carousel': return ( <Box sx={{ display: 'flex', gap: 2, overflowX: 'auto', pb: 1 }}> {message.content.payload?.carousel?.map((item: any, index: number) => ( <Card key={index} sx={{ minWidth: 280, flexShrink: 0 }}> {item.imageUrl && ( <Box component="img" sx={{ width: '100%', height: 140, objectFit: 'cover' }} src={item.imageUrl} alt={item.title} /> )} <CardContent> <Typography variant="h6" gutterBottom> {item.title} </Typography> <Typography variant="body2" color="text.secondary"> {item.description} </Typography> {item.buttons && ( <Box sx={{ mt: 1, display: 'flex', gap: 1 }}> {item.buttons.map((button: any, btnIndex: number) => ( <Button key={btnIndex} size="small" variant="outlined" onClick={() => onAction?.(button.action, button.payload)} > {button.text} </Button> ))} </Box> )} </CardContent> </Card> ))} </Box> ); case 'quick_reply': return ( <Box> <Typography variant="body1" sx={{ mb: 2 }}> {message.content.text} </Typography> <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}> {message.content.payload?.quickReplies?.map((reply: any, index: number) => ( <Chip key={index} label={reply.text} clickable variant="outlined" onClick={() => onAction?.('quick_reply', reply.payload)} /> ))} </Box> </Box> ); default: return ( <Typography variant="body1"> {message.content.text} </Typography> ); } }; return ( <Box sx={{ display: 'flex', justifyContent: isOwn ? 'flex-end' : 'flex-start', mb: 2, }} > {!isOwn && ( <Avatar sx={{ bgcolor: message.sender === 'agent' ? FREE_MOBILE_COLORS.SUCCESS : FREE_MOBILE_COLORS.PRIMARY, mr: 1, width: 32, height: 32, }} > {message.sender === 'agent' ? <SupportAgent /> : <SmartToy />} </Avatar> )} <Paper elevation={1} sx={{ p: 2, maxWidth: '75%', bgcolor: isOwn ? FREE_MOBILE_COLORS.PRIMARY : 'background.paper', color: isOwn ? 'white' : 'text.primary', borderRadius: isOwn ? '18px 18px 4px 18px' : '18px 18px 18px 4px', }} > {!isOwn && ( <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}> <Typography variant="caption" fontWeight={600}> {message.sender === 'agent' ? 'Agent Free Mobile' : 'Assistant IA'} </Typography> {message.intent && ( <Chip label={`${message.intent.name} (${Math.round(message.intent.confidence * 100)}%)`} size="small" variant="outlined" sx={{ ml: 1, fontSize: '0.7rem', height: 20 }} /> )} </Box> )} {renderContent()} <Typography variant="caption" sx={{ display: 'block', mt: 1, opacity: 0.7, fontSize: '0.75rem' }} > {new Date(message.timestamp).toLocaleTimeString()} </Typography> </Paper> </Box> ); }; // Composant principal de la page Chat const ChatPage: React.FC = () => { const dispatch = useDispatch<AppDispatch>(); const { currentConversation, messages, loading, isTyping, isAgentOnline, typingUsers, connectionStatus, realTimeEnabled } = useSelector((state: RootState) => state.chat); const { user } = useSelector((state: RootState) => state.auth); const [messageText, setMessageText] = useState(''); const [isRecording, setIsRecording] = useState(false); const [showQuickActions, setShowQuickActions] = useState(false); const [satisfactionDialog, setSatisfactionDialog] = useState(false); const [escalationDialog, setEscalationDialog] = useState(false); const [escalationReason, setEscalationReason] = useState(''); const [typingTimeout, setTypingTimeout] = useState<NodeJS.Timeout | null>(null); const [selectedFiles, setSelectedFiles] = useState<File[]>([]); const [uploadProgress, setUploadProgress] = useState<number>(0); const [isUploading, setIsUploading] = useState(false); const messagesEndRef = useRef<HTMLDivElement>(null); const fileInputRef = useRef<HTMLInputElement>(null); // Initialize WebSocket connection useEffect(() => { const initializeWebSocket = async () => { if (user && !webSocketService.isConnectedToChat()) { try { const token = localStorage.getItem('authToken'); if (token) { await webSocketService.initializeChatSocket({ url: process.env.REACT_APP_API_URL || 'http://localhost:3000', token, userId: user.id }); dispatch(setRealTimeEnabled(true)); } } catch (error) { console.error('Failed to initialize WebSocket:', error); dispatch(setConnectionStatus({ connected: false, error: 'WebSocket connection failed' })); } } }; initializeWebSocket(); // Cleanup on unmount return () => { webSocketService.leaveConversation(); }; }, [user, dispatch]); // Initialiser la conversation useEffect(() => { if (!currentConversation) { dispatch(startConversation()); } }, [currentConversation, dispatch]); // Join conversation when it's available useEffect(() => { if (currentConversation && webSocketService.isConnectedToChat()) { webSocketService.joinConversation(currentConversation._id); } }, [currentConversation]); // Auto-scroll vers le bas useEffect(() => { messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' }); }, [messages, isTyping]); // Handle typing indicators const handleTypingStart = useCallback(() => { if (currentConversation && webSocketService.isConnectedToChat()) { webSocketService.startTyping(currentConversation._id); } }, [currentConversation]); const handleTypingStop = useCallback(() => { if (currentConversation && webSocketService.isConnectedToChat()) { webSocketService.stopTyping(currentConversation._id); } }, [currentConversation]); // Handle message input changes with typing indicators const handleMessageChange = (event: React.ChangeEvent<HTMLInputElement>) => { const value = event.target.value; setMessageText(value); if (realTimeEnabled && currentConversation) { // Start typing indicator handleTypingStart(); // Clear existing timeout if (typingTimeout) { clearTimeout(typingTimeout); } // Set new timeout to stop typing indicator const timeout = setTimeout(() => { handleTypingStop(); }, 2000); setTypingTimeout(timeout); } }; // File upload handling const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => { const files = Array.from(event.target.files || []); const validFiles = files.filter(file => { const maxSize = 10 * 1024 * 1024; // 10MB const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'application/pdf', 'text/plain']; if (file.size > maxSize) { dispatch(showNotification({ message: `Fichier ${file.name} trop volumineux (max 10MB)`, severity: 'error' })); return false; } if (!allowedTypes.includes(file.type)) { dispatch(showNotification({ message: `Type de fichier ${file.name} non supporté`, severity: 'error' })); return false; } return true; }); setSelectedFiles(prev => [...prev, ...validFiles]); if (event.target) { event.target.value = ''; } }; const removeFile = (index: number) => { setSelectedFiles(prev => prev.filter((_, i) => i !== index)); }; const uploadFiles = async (files: File[]): Promise<string[]> => { if (files.length === 0) return []; setIsUploading(true); setUploadProgress(0); try { const formData = new FormData(); files.forEach(file => { formData.append('files', file); }); const response = await fetch('/api/upload/chat', { method: 'POST', headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` }, body: formData }); if (!response.ok) { throw new Error('Upload failed'); } const result = await response.json(); setUploadProgress(100); return result.fileUrls || []; } catch (error) { console.error('File upload error:', error); dispatch(showNotification({ message: 'Erreur lors de l\'envoi des fichiers', severity: 'error' })); return []; } finally { setIsUploading(false); setTimeout(() => setUploadProgress(0), 1000); } }; // Gestion de l'envoi de message const handleSendMessage = async () => { if ((!messageText.trim() && selectedFiles.length === 0) || !currentConversation) return; // Stop typing indicator if (typingTimeout) { clearTimeout(typingTimeout); setTypingTimeout(null); } handleTypingStop(); try { // Upload files first if any let attachments: string[] = []; if (selectedFiles.length > 0) { attachments = await uploadFiles(selectedFiles); } if (realTimeEnabled && webSocketService.isConnectedToChat()) { // Use real-time WebSocket for immediate response webSocketService.sendMessage(currentConversation._id, messageText, 'text', attachments); setMessageText(''); setSelectedFiles([]); } else { // Fallback to HTTP API await dispatch(sendMessage({ conversationId: currentConversation._id, message: messageText, attachments })).unwrap(); setMessageText(''); setSelectedFiles([]); } } catch (error) { dispatch(showNotification({ message: 'Erreur lors de l\'envoi du message', severity: 'error' })); } }; // Gestion des actions sur les messages const handleMessageAction = async (action: string, payload: any) => { switch (action) { case 'quick_reply': setMessageText(payload); break; case 'escalate_agent': setEscalationDialog(true); break; case 'change_plan': dispatch(showNotification({ message: 'Redirection vers changement de forfait...', severity: 'info' })); break; case 'view_invoice': dispatch(showNotification({ message: 'Ouverture de la facture...', severity: 'info' })); break; case 'add_data': dispatch(showNotification({ message: 'Ajout de data en cours...', severity: 'info' })); break; default: console.log('Action:', action, 'Payload:', payload); } }; // Escalade vers agent const handleEscalateToAgent = async () => { if (!currentConversation) return; try { await dispatch(escalateToAgent({ conversationId: currentConversation._id, reason: escalationReason })).unwrap(); setEscalationDialog(false); setEscalationReason(''); dispatch(showNotification({ message: 'Escalade vers un agent en cours...', severity: 'info' })); } catch (error) { dispatch(showNotification({ message: 'Erreur lors de l\'escalade', severity: 'error' })); } }; // Actions rapides prédéfinies const quickActions = [ { icon: <DataUsage />, text: 'Ma consommation', action: 'Ma consommation data' }, { icon: <Euro />, text: 'Ma facture', action: 'Voir ma dernière facture' }, { icon: <Phone />, text: 'Mon forfait', action: 'Détails de mon forfait' }, { icon: <Settings />, text: 'Paramètres', action: 'Aide paramètres' }, ]; return ( <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}> {/* Header du chat */} <Paper elevation={2} sx={{ p: 2, borderRadius: 0, bgcolor: FREE_MOBILE_COLORS.PRIMARY, color: 'white', }} > <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}> <Box sx={{ display: 'flex', alignItems: 'center' }}> <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', mr: 2 }}> {isAgentOnline ? <SupportAgent /> : <SmartToy />} </Avatar> <Box> <Typography variant="h6" fontWeight={600}> {isAgentOnline ? 'Agent Free Mobile' : 'Assistant Free Mobile'} </Typography> <Box sx={{ display: 'flex', alignItems: 'center' }}> <Badge color={connectionStatus.connected ? 'success' : 'error'} variant="dot" sx={{ mr: 1 }} /> <Typography variant="caption"> {connectionStatus.connected ? 'En ligne' : 'Hors ligne'} {realTimeEnabled && ' • Temps réel'} {isAgentOnline && ' • Agent disponible'} </Typography> </Box> </Box> </Box> <Box> <IconButton color="inherit" onClick={() => setShowQuickActions(!showQuickActions)}> <MoreVert /> </IconButton> </Box> </Box> </Paper> {/* Actions rapides */} {showQuickActions && ( <Paper sx={{ p: 2, borderRadius: 0 }}> <Typography variant="subtitle2" gutterBottom> Actions rapides </Typography> <Grid container spacing={1}> {quickActions.map((action, index) => ( <Grid item xs={6} sm={3} key={index}> <Button variant="outlined" startIcon={action.icon} fullWidth size="small" onClick={() => setMessageText(action.action)} sx={{ textTransform: 'none' }} > {action.text} </Button> </Grid> ))} </Grid> </Paper> )} {/* Zone de messages */} <Box sx={{ flex: 1, overflow: 'auto', p: 2, bgcolor: 'grey.50', }} > {messages.map((message) => ( <MessageBubble key={message._id} message={message} isOwn={message.sender === 'user'} onAction={handleMessageAction} /> ))} {/* Typing indicators */} {(isTyping || typingUsers.length > 0) && ( <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}> <Avatar sx={{ bgcolor: FREE_MOBILE_COLORS.PRIMARY, mr: 1, width: 32, height: 32 }}> <SmartToy /> </Avatar> <Paper sx={{ p: 2, borderRadius: '18px 18px 18px 4px' }}> <Typography variant="body2" color="text.secondary"> {typingUsers.length > 0 ? `${typingUsers.length} utilisateur${typingUsers.length > 1 ? 's' : ''} en train d'écrire...` : "L'assistant est en train d'écrire..." } </Typography> <LinearProgress sx={{ mt: 1, width: 80 }} /> </Paper> </Box> )} <div ref={messagesEndRef} /> </Box> {/* File preview section */} {selectedFiles.length > 0 && ( <Paper elevation={1} sx={{ p: 2, borderTop: '1px solid', borderColor: 'divider', bgcolor: 'grey.50' }} > <Typography variant="subtitle2" gutterBottom> Fichiers sélectionnés ({selectedFiles.length}) </Typography> <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}> {selectedFiles.map((file, index) => ( <Chip key={index} label={`${file.name} (${(file.size / 1024 / 1024).toFixed(1)}MB)`} onDelete={() => removeFile(index)} color="primary" variant="outlined" size="small" /> ))} </Box> {isUploading && ( <Box sx={{ mt: 2 }}> <Typography variant="caption" color="text.secondary"> Envoi en cours... {uploadProgress}% </Typography> <LinearProgress variant="determinate" value={uploadProgress} sx={{ mt: 1 }} /> </Box> )} </Paper> )} {/* Zone de saisie */} <Paper elevation={3} sx={{ p: 2, borderRadius: 0, borderTop: '1px solid', borderColor: 'divider', }} > <Box sx={{ display: 'flex', alignItems: 'flex-end', gap: 1 }}> <TextField fullWidth multiline maxRows={4} placeholder="Tapez votre message..." value={messageText} onChange={handleMessageChange} onKeyPress={(e) => { if (e.key === 'Enter' && !e.shiftKey) { e.preventDefault(); handleSendMessage(); } }} variant="outlined" size="small" disabled={loading || !connectionStatus.connected} sx={{ '& .MuiOutlinedInput-root': { borderRadius: 3, }, }} /> <input type="file" ref={fileInputRef} style={{ display: 'none' }} accept="image/*,application/pdf,.txt" multiple onChange={handleFileSelect} /> <IconButton color="primary" onClick={() => fileInputRef.current?.click()} disabled={loading} > <AttachFile /> </IconButton> <IconButton color={isRecording ? 'error' : 'primary'} onClick={() => { setIsRecording(!isRecording); dispatch(showNotification({ message: isRecording ? 'Enregistrement arrêté' : 'Enregistrement vocal démarré', severity: 'info' })); }} disabled={loading} > {isRecording ? <MicOff /> : <Mic />} </IconButton> <IconButton color="primary" onClick={handleSendMessage} disabled={!messageText.trim() || loading || !connectionStatus.connected} sx={{ bgcolor: FREE_MOBILE_COLORS.PRIMARY, color: 'white', '&:hover': { bgcolor: '#CC0000', }, }} > <Send /> </IconButton> </Box> </Paper> {/* Dialog d'escalade vers agent */} <Dialog open={escalationDialog} onClose={() => setEscalationDialog(false)} maxWidth="sm" fullWidth> <DialogTitle> Escalade vers un agent humain </DialogTitle> <DialogContent> <Typography variant="body1" sx={{ mb: 2 }}> Décrivez brièvement pourquoi vous souhaitez parler à un agent : </Typography> <TextField fullWidth multiline rows={3} value={escalationReason} onChange={(e) => setEscalationReason(e.target.value)} placeholder="Ex: Problème technique complexe, réclamation, etc." variant="outlined" /> </DialogContent> <DialogActions> <Button onClick={() => setEscalationDialog(false)}> Annuler </Button> <Button onClick={handleEscalateToAgent} variant="contained" disabled={!escalationReason.trim()} > Contacter un agent </Button> </DialogActions> </Dialog> {/* Dialog de satisfaction */} <Dialog open={satisfactionDialog} onClose={() => setSatisfactionDialog(false)}> <DialogTitle> Évaluez votre expérience </DialogTitle> <DialogContent> <Typography variant="body1" sx={{ mb: 2 }}> Comment évaluez-vous cette conversation ? </Typography> <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2 }}> {[1, 2, 3, 4, 5].map((rating) => ( <IconButton key={rating} color="primary" onClick={() => { dispatch(showNotification({ message: `Merci pour votre évaluation : ${rating}/5`, severity: 'success' })); setSatisfactionDialog(false); }} > <StarRate /> </IconButton> ))} </Box> </DialogContent> </Dialog> {/* Bouton d'escalade flottant */} {!isAgentOnline && currentConversation && ( <Fab color="secondary" size="small" sx={{ position: 'fixed', bottom: 100, right: 24, }} onClick={() => setEscalationDialog(true)} > <SupportAgent /> </Fab> )} </Box> ); }; export default ChatPage;