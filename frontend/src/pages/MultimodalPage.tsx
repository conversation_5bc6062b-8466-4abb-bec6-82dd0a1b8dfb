/** * ============================================= * MULTIMODAL PAGE * Main page for multimodal analysis interface * Integrates text, voice, and image processing * ============================================= */ import React, { useEffect, useState } from 'react'; import { useAppDispatch, useAppSelector } from '../hooks/redux'; import { Container, Typography, Box, Alert, Fab, Tooltip, Snackbar, Breadcrumbs, Link, Paper } from '@mui/material'; import { AutoAwesome as MultimodalIcon, History as HistoryIcon, Settings as SettingsIcon, Help as HelpIcon } from '@mui/icons-material'; import { RootState } from '../store'; import { connectWebSocket, clearError } from '../store/slices/multimodalSlice'; import MultimodalInput from '../components/Multimodal/MultimodalInput'; import { multimodalService } from '../services/multimodal.service'; const MultimodalPage: React.FC = () => { const dispatch = useAppDispatch(); const { isConnected, connectionStatus, error, processingHistory, showResults } = useAppSelector((state: RootState) => state.multimodal); const { user } = useAppSelector((state: RootState) => state.auth); const [showHistory, setShowHistory] = useState(false); const [showSettings, setShowSettings] = useState(false); const [snackbarOpen, setSnackbarOpen] = useState(false); const [snackbarMessage, setSnackbarMessage] = useState(''); // Initialize connection on mount useEffect(() => { if (user?.id && !isConnected) { dispatch(connectWebSocket(user.id)); } }, [user?.id, isConnected, dispatch]); // Handle connection status changes useEffect(() => { if (connectionStatus === 'connected' && !snackbarOpen) { setSnackbarMessage('Service multimodal connecté'); setSnackbarOpen(true); } else if (connectionStatus === 'error') { setSnackbarMessage('Erreur de connexion au service multimodal'); setSnackbarOpen(true); } }, [connectionStatus, snackbarOpen]); // Handle results change const handleResultsChange = (hasResults: boolean) => { // Could trigger additional UI updates here }; // Handle snackbar close const handleSnackbarClose = () => { setSnackbarOpen(false); if (error) { dispatch(clearError()); } }; // Check service health const checkServiceHealth = async () => { try { const health = await multimodalService.getHealthStatus(); console.log('Service health:', health); setSnackbarMessage('Service multimodal opérationnel'); setSnackbarOpen(true); } catch (error) { console.error('Health check failed:', error); setSnackbarMessage('Service multimodal indisponible'); setSnackbarOpen(true); } }; return ( <Container maxWidth="xl" sx={{ py: 3 }}> {/* Breadcrumbs */} <Breadcrumbs sx={{ mb: 2 }}> <Link color="inherit" href="/dashboard"> Dashboard </Link> <Typography color="text.primary">Analyse Multimodale</Typography> </Breadcrumbs> {/* Page Header */} <Box mb={4}> <Box display="flex" alignItems="center" gap={2} mb={2}> <MultimodalIcon sx={{ fontSize: 40, color: 'primary.main' }} /> <Box> <Typography variant="h4" component="h1" gutterBottom> Analyse Multimodale </Typography> <Typography variant="subtitle1" color="text.secondary"> Analysez du contenu textuel, vocal et visuel avec l'intelligence artificielle </Typography> </Box> </Box> {/* Connection Status */} <Box display="flex" alignItems="center" gap={2}> <Box sx={{ width: 12, height: 12, borderRadius: '50%', backgroundColor: connectionStatus === 'connected' ? 'success.main' : connectionStatus === 'connecting' ? 'warning.main' : connectionStatus === 'error' ? 'error.main' : 'grey.400' }} /> <Typography variant="body2" color="text.secondary"> {connectionStatus === 'connected' && 'Service multimodal connecté'} {connectionStatus === 'connecting' && 'Connexion en cours...'} {connectionStatus === 'disconnected' && 'Service multimodal déconnecté'} {connectionStatus === 'error' && 'Erreur de connexion'} </Typography> {processingHistory.length > 0 && ( <Typography variant="body2" color="text.secondary"> • {processingHistory.length} analyse(s) effectuée(s) </Typography> )} </Box> </Box> {/* Service Unavailable Alert */} {connectionStatus === 'error' && ( <Alert severity="warning" sx={{ mb: 3 }} action={ <Link component="button" variant="body2" onClick={checkServiceHealth} sx={{ textDecoration: 'underline' }} > Vérifier le service </Link> } > Le service d'analyse multimodale n'est pas disponible. Certaines fonctionnalités peuvent être limitées. </Alert> )} {/* Main Content */} <Box display="flex" gap={3}> {/* Main Input Panel */} <Box flex={1}> <MultimodalInput onResultsChange={handleResultsChange} showAdvancedOptions={true} /> </Box> {/* Side Panel */} <Box width={300} display={{ xs: 'none', lg: 'block' }}> <Paper elevation={1} sx={{ p: 2, mb: 2 }}> <Typography variant="h6" gutterBottom> Guide d'utilisation </Typography> <Typography variant="body2" color="text.secondary" paragraph> <strong>Texte:</strong> Saisissez du texte pour analyser le sentiment, détecter l'intention et extraire des entités. </Typography> <Typography variant="body2" color="text.secondary" paragraph> <strong>Voix:</strong> Enregistrez un message vocal pour la transcription et l'analyse émotionnelle. </Typography> <Typography variant="body2" color="text.secondary" paragraph> <strong>Image:</strong> Téléchargez une image pour la reconnaissance de texte (OCR), d'objets et d'émotions faciales. </Typography> <Typography variant="body2" color="text.secondary"> <strong>Multimodal:</strong> Combinez plusieurs modalités pour une analyse plus riche et précise. </Typography> </Paper> {/* Processing History */} {processingHistory.length > 0 && ( <Paper elevation={1} sx={{ p: 2 }}> <Typography variant="h6" gutterBottom> Historique récent </Typography> {processingHistory.slice(0, 5).map((item, index) => ( <Box key={item.id} mb={1}> <Typography variant="body2" noWrap> {item.type} • {Math.round(item.confidence * 100)}% </Typography> <Typography variant="caption" color="text.secondary"> {new Date(item.timestamp).toLocaleTimeString()} </Typography> </Box> ))} {processingHistory.length > 5 && ( <Typography variant="caption" color="text.secondary"> +{processingHistory.length - 5} autres analyses </Typography> )} </Paper> )} </Box> </Box> {/* Floating Action Buttons */} <Box position="fixed" bottom={24} right={24} display="flex" flexDirection="column" gap={1} > <Tooltip title="Historique des analyses" placement="left"> <Fab size="small" color="default" onClick={() => setShowHistory(!showHistory)} > <HistoryIcon /> </Fab> </Tooltip> <Tooltip title="Paramètres" placement="left"> <Fab size="small" color="default" onClick={() => setShowSettings(!showSettings)} > <SettingsIcon /> </Fab> </Tooltip> <Tooltip title="Aide" placement="left"> <Fab size="small" color="primary" onClick={() => { // Open help dialog or navigate to help page window.open('/help/multimodal', '_blank'); }} > <HelpIcon /> </Fab> </Tooltip> </Box> {/* Snackbar for notifications */} <Snackbar open={snackbarOpen} autoHideDuration={4000} onClose={handleSnackbarClose} message={snackbarMessage} anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }} /> {/* Error Snackbar */} <Snackbar open={!!error} autoHideDuration={6000} onClose={() => dispatch(clearError())} anchorOrigin={{ vertical: 'top', horizontal: 'center' }} > <Alert severity="error" onClose={() => dispatch(clearError())}> {error} </Alert> </Snackbar> </Container> ); }; export default MultimodalPage;