import React, { useState, useEffect } from 'react'; import { Box, Paper, Typography, TextField, Button, Grid, Avatar, Card, CardContent, Chip, List, ListItem, ListItemIcon, ListItemText, Switch, FormControlLabel, Divider, Alert, Dialog, DialogTitle, DialogContent, DialogActions, IconButton, Tabs, Tab, } from '@mui/material'; import { Person, Email, Phone, LocationOn, Security, Notifications, Language, Palette, Download, Upload, Edit, Save, Cancel, CreditCard, History, Settings, Visibility, VisibilityOff, } from '@mui/icons-material'; import { useAppSelector, useAppDispatch } from '../hooks/redux'; import { RootState } from '../store'; import { updateUser } from '../store/slices/authSlice'; import { showNotification } from '../store/slices/uiSlice'; import { FREE_MOBILE_COLORS, FREE_MOBILE_PLANS } from '../utils/constants'; interface TabPanelProps { children?: React.ReactNode; index: number; value: number; } const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => { return ( <div role="tabpanel" hidden={value !== index}> {value === index && <Box sx={{ p: 3 }}>{children}</Box>} </div> ); }; const ProfilePage: React.FC = () => { const dispatch = useAppDispatch(); const { user } = useAppSelector((state: RootState) => state.auth); const [tabValue, setTabValue] = useState(0); const [isEditing, setIsEditing] = useState(false); const [showPasswordDialog, setShowPasswordDialog] = useState(false); const [showPassword, setShowPassword] = useState(false); const [formData, setFormData] = useState({ firstName: user?.profile?.firstName || '', lastName: user?.profile?.lastName || '', phoneNumber: user?.profile?.phoneNumber || '', email: user?.email || '', }); const [preferences, setPreferences] = useState({ notifications: user?.preferences?.notifications ?? true, language: user?.preferences?.language || 'fr', theme: user?.preferences?.theme || 'light', }); const [passwordData, setPasswordData] = useState({ currentPassword: '', newPassword: '', confirmPassword: '', }); const [errors, setErrors] = useState<Record<string, string>>({}); // Données simulées du profil client Free Mobile const customerData = { customerId: 'FREE-' + (user?.profile?.customerId || '12345678'), plan: FREE_MOBILE_PLANS[1], // Forfait 150Go joinDate: '2023-01-15', lastLogin: new Date().toISOString(), totalSpent: 239.88, loyaltyPoints: 1250, options: [ { name: 'International', price: 5, active: true }, { name: 'Music illimité', price: 0, active: true }, ], devices: [ { name: 'iPhone 14 Pro', imei: '***********1234', active: true }, { name: 'Samsung Galaxy S23', imei: '***********5678', active: false }, ], }; const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => { const { name, value } = e.target; setFormData(prev => ({ ...prev, [name]: value })); if (errors[name]) { setErrors(prev => ({ ...prev, [name]: '' })); } }; const handlePreferenceChange = (name: string, value: any) => { setPreferences(prev => ({ ...prev, [name]: value })); }; const validateForm = (): boolean => { const newErrors: Record<string, string> = {}; if (!formData.firstName.trim()) { newErrors.firstName = 'Le prénom est requis'; } if (!formData.lastName.trim()) { newErrors.lastName = 'Le nom est requis'; } if (!formData.email.trim()) { newErrors.email = 'L\'email est requis'; } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) { newErrors.email = 'Format d\'email invalide'; } setErrors(newErrors); return Object.keys(newErrors).length === 0; }; const handleSave = async () => { if (!validateForm()) return; try { await dispatch(updateUser({ profile: { firstName: formData.firstName, lastName: formData.lastName, phoneNumber: formData.phoneNumber, }, email: formData.email, preferences, })); dispatch(showNotification({ message: 'Profil mis à jour avec succès', severity: 'success', })); setIsEditing(false); } catch (error) { dispatch(showNotification({ message: 'Erreur lors de la mise à jour', severity: 'error', })); } }; const handlePasswordChange = async () => { const newErrors: Record<string, string> = {}; if (!passwordData.currentPassword) { newErrors.currentPassword = 'Mot de passe actuel requis'; } if (!passwordData.newPassword) { newErrors.newPassword = 'Nouveau mot de passe requis'; } else if (passwordData.newPassword.length < 6) { newErrors.newPassword = 'Au moins 6 caractères requis'; } if (passwordData.newPassword !== passwordData.confirmPassword) { newErrors.confirmPassword = 'Les mots de passe ne correspondent pas'; } setErrors(newErrors); if (Object.keys(newErrors).length === 0) { dispatch(showNotification({ message: 'Mot de passe modifié avec succès', severity: 'success', })); setShowPasswordDialog(false); setPasswordData({ currentPassword: '', newPassword: '', confirmPassword: '' }); } }; const handleExportData = () => { const data = { profile: user, customerData, exportDate: new Date().toISOString(), }; const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' }); const url = URL.createObjectURL(blob); const a = document.createElement('a'); a.href = url; a.download = `free-mobile-profile-${new Date().toISOString().split('T')[0]}.json`; document.body.appendChild(a); a.click(); document.body.removeChild(a); URL.revokeObjectURL(url); dispatch(showNotification({ message: 'Données exportées avec succès', severity: 'success', })); }; return ( <Box sx={{ p: 3 }}> <Typography variant="h4" gutterBottom fontWeight={600}> Mon Profil </Typography> <Paper sx={{ mb: 3 }}> <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)} sx={{ borderBottom: '1px solid', borderColor: 'divider' }} > <Tab label="Informations personnelles" /> <Tab label="Compte Free Mobile" /> <Tab label="Préférences" /> <Tab label="Sécurité" /> <Tab label="Données" /> </Tabs> <TabPanel value={tabValue} index={0}> <Grid container spacing={3}> <Grid item xs={12} md={4}> <Card> <CardContent sx={{ textAlign: 'center' }}> <Avatar sx={{ width: 100, height: 100, mx: 'auto', mb: 2, bgcolor: FREE_MOBILE_COLORS.PRIMARY, fontSize: '2rem', }} > {formData.firstName[0]}{formData.lastName[0]} </Avatar> <Typography variant="h6" gutterBottom> {formData.firstName} {formData.lastName} </Typography> <Chip label={user?.role || 'Client'} color="primary" size="small" /> <Box sx={{ mt: 2 }}> <Button variant={isEditing ? 'contained' : 'outlined'} startIcon={isEditing ? <Save /> : <Edit />} onClick={isEditing ? handleSave : () => setIsEditing(true)} color="primary" fullWidth > {isEditing ? 'Sauvegarder' : 'Modifier'} </Button> {isEditing && ( <Button variant="outlined" startIcon={<Cancel />} onClick={() => { setIsEditing(false); setFormData({ firstName: user?.profile?.firstName || '', lastName: user?.profile?.lastName || '', phoneNumber: user?.profile?.phoneNumber || '', email: user?.email || '', }); }} sx={{ mt: 1 }} fullWidth > Annuler </Button> )} </Box> </CardContent> </Card> </Grid> <Grid item xs={12} md={8}> <Card> <CardContent> <Typography variant="h6" gutterBottom> Informations personnelles </Typography> <Grid container spacing={2}> <Grid item xs={12} sm={6}> <TextField fullWidth label="Prénom" name="firstName" value={formData.firstName} onChange={handleChange} disabled={!isEditing} error={!!errors.firstName} helperText={errors.firstName} InputProps={{ startAdornment: <Person color="action" sx={{ mr: 1 }} />, }} /> </Grid> <Grid item xs={12} sm={6}> <TextField fullWidth label="Nom" name="lastName" value={formData.lastName} onChange={handleChange} disabled={!isEditing} error={!!errors.lastName} helperText={errors.lastName} InputProps={{ startAdornment: <Person color="action" sx={{ mr: 1 }} />, }} /> </Grid> <Grid item xs={12}> <TextField fullWidth label="Email" name="email" value={formData.email} onChange={handleChange} disabled={!isEditing} error={!!errors.email} helperText={errors.email} InputProps={{ startAdornment: <Email color="action" sx={{ mr: 1 }} />, }} /> </Grid> <Grid item xs={12}> <TextField fullWidth label="Téléphone" name="phoneNumber" value={formData.phoneNumber} onChange={handleChange} disabled={!isEditing} InputProps={{ startAdornment: <Phone color="action" sx={{ mr: 1 }} />, }} /> </Grid> </Grid> </CardContent> </Card> </Grid> </Grid> </TabPanel> <TabPanel value={tabValue} index={1}> <Grid container spacing={3}> <Grid item xs={12} md={6}> <Card> <CardContent> <Typography variant="h6" gutterBottom> Compte Free Mobile </Typography> <List> <ListItem> <ListItemIcon><Person /></ListItemIcon> <ListItemText primary="ID Client" secondary={customerData.customerId} /> </ListItem> <ListItem> <ListItemIcon><Phone /></ListItemIcon> <ListItemText primary="Forfait actuel" secondary={`${customerData.plan.name} - ${customerData.plan.price}€/mois`} /> </ListItem> <ListItem> <ListItemIcon><History /></ListItemIcon> <ListItemText primary="Client depuis" secondary={new Date(customerData.joinDate).toLocaleDateString()} /> </ListItem> <ListItem> <ListItemIcon><CreditCard /></ListItemIcon> <ListItemText primary="Total dépensé" secondary={`${customerData.totalSpent}€`} /> </ListItem> </List> </CardContent> </Card> </Grid> <Grid item xs={12} md={6}> <Card> <CardContent> <Typography variant="h6" gutterBottom> Options actives </Typography> <List> {customerData.options.map((option, index) => ( <ListItem key={index}> <ListItemText primary={option.name} secondary={option.price === 0 ? 'Gratuit' : `${option.price}€/mois`} /> <Chip label={option.active ? 'Active' : 'Inactive'} color={option.active ? 'success' : 'default'} size="small" /> </ListItem> ))} </List> </CardContent> </Card> </Grid> <Grid item xs={12}> <Card> <CardContent> <Typography variant="h6" gutterBottom> Appareils connectés </Typography> <List> {customerData.devices.map((device, index) => ( <ListItem key={index}> <ListItemIcon><Phone /></ListItemIcon> <ListItemText primary={device.name} secondary={`IMEI: ${device.imei}`} /> <Chip label={device.active ? 'Actif' : 'Inactif'} color={device.active ? 'success' : 'default'} size="small" /> </ListItem> ))} </List> </CardContent> </Card> </Grid> </Grid> </TabPanel> <TabPanel value={tabValue} index={2}> <Card> <CardContent> <Typography variant="h6" gutterBottom> Préférences </Typography> <List> <ListItem> <ListItemIcon><Notifications /></ListItemIcon> <ListItemText primary="Notifications push" secondary="Recevoir des notifications sur mobile et web" /> <Switch checked={preferences.notifications} onChange={(e) => handlePreferenceChange('notifications', e.target.checked)} color="primary" /> </ListItem> <Divider /> <ListItem> <ListItemIcon><Language /></ListItemIcon> <ListItemText primary="Langue" secondary="Langue de l'interface" /> <Button variant="outlined" size="small"> Français </Button> </ListItem> <Divider /> <ListItem> <ListItemIcon><Palette /></ListItemIcon> <ListItemText primary="Thème" secondary="Apparence de l'application" /> <Button variant="outlined" size="small"> {preferences.theme === 'light' ? 'Clair' : 'Sombre'} </Button> </ListItem> </List> </CardContent> </Card> </TabPanel> <TabPanel value={tabValue} index={3}> <Card> <CardContent> <Typography variant="h6" gutterBottom> Sécurité </Typography> <List> <ListItem> <ListItemIcon><Security /></ListItemIcon> <ListItemText primary="Modifier le mot de passe" secondary="Dernière modification il y a 3 mois" /> <Button variant="outlined" onClick={() => setShowPasswordDialog(true)} > Modifier </Button> </ListItem> <Divider /> <ListItem> <ListItemIcon><Phone /></ListItemIcon> <ListItemText primary="Authentification à deux facteurs" secondary="Sécurisez votre compte avec votre téléphone" /> <Switch color="primary" /> </ListItem> <Divider /> <ListItem> <ListItemIcon><History /></ListItemIcon> <ListItemText primary="Sessions actives" secondary="Gérer les appareils connectés" /> <Button variant="outlined"> Voir </Button> </ListItem> </List> </CardContent> </Card> </TabPanel> <TabPanel value={tabValue} index={4}> <Card> <CardContent> <Typography variant="h6" gutterBottom> Gestion des données </Typography> <Alert severity="info" sx={{ mb: 3 }}> Conformément au RGPD, vous avez le droit d'accéder, modifier ou supprimer vos données personnelles. </Alert> <List> <ListItem> <ListItemIcon><Download /></ListItemIcon> <ListItemText primary="Exporter mes données" secondary="Télécharger toutes vos données au format JSON" /> <Button variant="outlined" onClick={handleExportData} startIcon={<Download />} > Exporter </Button> </ListItem> <Divider /> <ListItem> <ListItemIcon><Upload /></ListItemIcon> <ListItemText primary="Importer des données" secondary="Restaurer vos données depuis un fichier" /> <Button variant="outlined" startIcon={<Upload />}> Importer </Button> </ListItem> </List> </CardContent> </Card> </TabPanel> </Paper> {/* Dialog changement de mot de passe */} <Dialog open={showPasswordDialog} onClose={() => setShowPasswordDialog(false)} maxWidth="sm" fullWidth > <DialogTitle>Modifier le mot de passe</DialogTitle> <DialogContent> <Grid container spacing={2} sx={{ mt: 1 }}> <Grid item xs={12}> <TextField fullWidth label="Mot de passe actuel" type={showPassword ? 'text' : 'password'} value={passwordData.currentPassword} onChange={(e) => setPasswordData(prev => ({ ...prev, currentPassword: e.target.value }))} error={!!errors.currentPassword} helperText={errors.currentPassword} InputProps={{ endAdornment: ( <IconButton onClick={() => setShowPassword(!showPassword)}> {showPassword ? <VisibilityOff /> : <Visibility />} </IconButton> ), }} /> </Grid> <Grid item xs={12}> <TextField fullWidth label="Nouveau mot de passe" type="password" value={passwordData.newPassword} onChange={(e) => setPasswordData(prev => ({ ...prev, newPassword: e.target.value }))} error={!!errors.newPassword} helperText={errors.newPassword} /> </Grid> <Grid item xs={12}> <TextField fullWidth label="Confirmer le nouveau mot de passe" type="password" value={passwordData.confirmPassword} onChange={(e) => setPasswordData(prev => ({ ...prev, confirmPassword: e.target.value }))} error={!!errors.confirmPassword} helperText={errors.confirmPassword} /> </Grid> </Grid> </DialogContent> <DialogActions> <Button onClick={() => setShowPasswordDialog(false)}> Annuler </Button> <Button onClick={handlePasswordChange} variant="contained"> Modifier </Button> </DialogActions> </Dialog> </Box> ); }; export default ProfilePage;