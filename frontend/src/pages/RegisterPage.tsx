import React, { useState, useEffect } from 'react'; import { Link, useNavigate } from 'react-router-dom'; import { Box, Paper, TextField, Button, Typography, Alert, Container, Grid, InputAdornment, IconButton, Stepper, Step, StepLabel, Checkbox, FormControlLabel, } from '@mui/material'; import { Visibility, VisibilityOff, Email, Lock, Person, Phone, CheckCircle, } from '@mui/icons-material'; import { useAuth } from '../hooks/useAuth'; import { ROUTES, FREE_MOBILE_COLORS } from '../utils/constants'; import LoadingSpinner from '../components/Common/LoadingSpinner'; const RegisterPage: React.FC = () => { const navigate = useNavigate(); const { register, loading, error, isAuthenticated, clearError } = useAuth(); const [activeStep, setActiveStep] = useState(0); const [showPassword, setShowPassword] = useState(false); const [showConfirmPassword, setShowConfirmPassword] = useState(false); const [acceptTerms, setAcceptTerms] = useState(false); const [formData, setFormData] = useState({ email: '', password: '', confirmPassword: '', firstName: '', lastName: '', phoneNumber: '', }); const [errors, setErrors] = useState<Record<string, string>>({}); const steps = ['Informations personnelles', 'Identifiants de connexion', 'Confirmation']; useEffect(() => { if (isAuthenticated) { navigate(ROUTES.DASHBOARD); } }, [isAuthenticated, navigate]); useEffect(() => { clearError(); }, [clearError]); const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => { const { name, value } = e.target; setFormData(prev => ({ ...prev, [name]: value, })); // Clear error when user starts typing if (errors[name]) { setErrors(prev => ({ ...prev, [name]: '', })); } }; const validateStep = (step: number): boolean => { const newErrors: Record<string, string> = {}; switch (step) { case 0: if (!formData.firstName.trim()) { newErrors.firstName = 'Le prénom est requis'; } if (!formData.lastName.trim()) { newErrors.lastName = 'Le nom est requis'; } if (!formData.phoneNumber.trim()) { newErrors.phoneNumber = 'Le numéro de téléphone est requis'; } else if (!/^(\+33|0)[1-9](\d{8})$/.test(formData.phoneNumber.replace(/\s/g, ''))) { newErrors.phoneNumber = 'Format de téléphone invalide'; } break; case 1: if (!formData.email.trim()) { newErrors.email = 'L\'email est requis'; } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) { newErrors.email = 'Format d\'email invalide'; } if (!formData.password) { newErrors.password = 'Le mot de passe est requis'; } else if (formData.password.length < 6) { newErrors.password = 'Le mot de passe doit contenir au moins 6 caractères'; } if (formData.password !== formData.confirmPassword) { newErrors.confirmPassword = 'Les mots de passe ne correspondent pas'; } break; case 2: if (!acceptTerms) { newErrors.terms = 'Vous devez accepter les conditions d\'utilisation'; } break; } setErrors(newErrors); return Object.keys(newErrors).length === 0; }; const handleNext = () => { if (validateStep(activeStep)) { setActiveStep(prev => prev + 1); } }; const handleBack = () => { setActiveStep(prev => prev - 1); }; const handleSubmit = async (e: React.FormEvent) => { e.preventDefault(); if (!validateStep(2)) return; try { await register({ email: formData.email, password: formData.password, firstName: formData.firstName, lastName: formData.lastName, phoneNumber: formData.phoneNumber, }).unwrap(); navigate(ROUTES.DASHBOARD); } catch (error) { console.error('Erreur d\'inscription:', error); } }; const renderStepContent = (step: number) => { switch (step) { case 0: return ( <Box> <Typography variant="h6" gutterBottom> Informations personnelles </Typography> <Grid container spacing={2}> <Grid item xs={12} sm={6}> <TextField fullWidth label="Prénom" name="firstName" value={formData.firstName} onChange={handleChange} error={!!errors.firstName} helperText={errors.firstName} InputProps={{ startAdornment: ( <InputAdornment position="start"> <Person color="action" /> </InputAdornment> ), }} /> </Grid> <Grid item xs={12} sm={6}> <TextField fullWidth label="Nom" name="lastName" value={formData.lastName} onChange={handleChange} error={!!errors.lastName} helperText={errors.lastName} InputProps={{ startAdornment: ( <InputAdornment position="start"> <Person color="action" /> </InputAdornment> ), }} /> </Grid> <Grid item xs={12}> <TextField fullWidth label="Numéro de téléphone" name="phoneNumber" value={formData.phoneNumber} onChange={handleChange} error={!!errors.phoneNumber} helperText={errors.phoneNumber} placeholder="06 12 34 56 78" InputProps={{ startAdornment: ( <InputAdornment position="start"> <Phone color="action" /> </InputAdornment> ), }} /> </Grid> </Grid> </Box> ); case 1: return ( <Box> <Typography variant="h6" gutterBottom> Identifiants de connexion </Typography> <Grid container spacing={2}> <Grid item xs={12}> <TextField fullWidth label="Adresse email" name="email" type="email" value={formData.email} onChange={handleChange} error={!!errors.email} helperText={errors.email} InputProps={{ startAdornment: ( <InputAdornment position="start"> <Email color="action" /> </InputAdornment> ), }} /> </Grid> <Grid item xs={12}> <TextField fullWidth label="Mot de passe" name="password" type={showPassword ? 'text' : 'password'} value={formData.password} onChange={handleChange} error={!!errors.password} helperText={errors.password} InputProps={{ startAdornment: ( <InputAdornment position="start"> <Lock color="action" /> </InputAdornment> ), endAdornment: ( <InputAdornment position="end"> <IconButton onClick={() => setShowPassword(!showPassword)} edge="end" > {showPassword ? <VisibilityOff /> : <Visibility />} </IconButton> </InputAdornment> ), }} /> </Grid> <Grid item xs={12}> <TextField fullWidth label="Confirmer le mot de passe" name="confirmPassword" type={showConfirmPassword ? 'text' : 'password'} value={formData.confirmPassword} onChange={handleChange} error={!!errors.confirmPassword} helperText={errors.confirmPassword} InputProps={{ startAdornment: ( <InputAdornment position="start"> <Lock color="action" /> </InputAdornment> ), endAdornment: ( <InputAdornment position="end"> <IconButton onClick={() => setShowConfirmPassword(!showConfirmPassword)} edge="end" > {showConfirmPassword ? <VisibilityOff /> : <Visibility />} </IconButton> </InputAdornment> ), }} /> </Grid> </Grid> </Box> ); case 2: return ( <Box> <Typography variant="h6" gutterBottom> Confirmation </Typography> <Box sx={{ mb: 3 }}> <Typography variant="subtitle1" gutterBottom> Récapitulatif de votre inscription : </Typography> <Paper sx={{ p: 2, bgcolor: 'grey.50' }}> <Typography variant="body2"> <strong>Nom :</strong> {formData.firstName} {formData.lastName} </Typography> <Typography variant="body2"> <strong>Email :</strong> {formData.email} </Typography> <Typography variant="body2"> <strong>Téléphone :</strong> {formData.phoneNumber} </Typography> </Paper> </Box> <FormControlLabel control={ <Checkbox checked={acceptTerms} onChange={(e) => setAcceptTerms(e.target.checked)} color="primary" /> } label={ <Typography variant="body2"> J'accepte les{' '} <Link to="/terms" style={{ color: FREE_MOBILE_COLORS.PRIMARY }} > conditions d'utilisation </Link>{' '} et la{' '} <Link to="/privacy" style={{ color: FREE_MOBILE_COLORS.PRIMARY }} > politique de confidentialité </Link> </Typography> } /> {errors.terms && ( <Typography variant="caption" color="error"> {errors.terms} </Typography> )} </Box> ); default: return null; } }; if (loading) { return <LoadingSpinner message="Création du compte..." />; } return ( <Container maxWidth="md" sx={{ minHeight: '100vh', py: 4 }}> <Paper elevation={6} sx={{ p: 4, borderRadius: 3, background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)', border: `2px solid ${FREE_MOBILE_COLORS.PRIMARY}20`, }} > <Box sx={{ textAlign: 'center', mb: 4 }}> <Typography variant="h3" sx={{ fontWeight: 700, color: FREE_MOBILE_COLORS.PRIMARY, mb: 1, }} > Free Mobile </Typography> <Typography variant="h5" gutterBottom fontWeight={600}> Créer votre compte </Typography> <Typography variant="body1" color="text.secondary"> Rejoignez des millions d'utilisateurs Free Mobile </Typography> </Box> {error && ( <Alert severity="error" sx={{ mb: 3 }}> {error} </Alert> )} <Stepper activeStep={activeStep} sx={{ mb: 4 }}> {steps.map((label) => ( <Step key={label}> <StepLabel>{label}</StepLabel> </Step> ))} </Stepper> <Box component="form" onSubmit={handleSubmit}> {renderStepContent(activeStep)} <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}> <Button onClick={handleBack} disabled={activeStep === 0} variant="outlined" > Précédent </Button> {activeStep === steps.length - 1 ? ( <Button type="submit" variant="contained" disabled={loading} startIcon={<CheckCircle />} sx={{ minWidth: 140 }} > Créer le compte </Button> ) : ( <Button onClick={handleNext} variant="contained" sx={{ minWidth: 140 }} > Suivant </Button> )} </Box> </Box> <Box sx={{ textAlign: 'center', mt: 3 }}> <Typography variant="body2" color="text.secondary"> Vous avez déjà un compte ?{' '} <Link to={ROUTES.LOGIN} style={{ color: FREE_MOBILE_COLORS.PRIMARY, textDecoration: 'none', fontWeight: 500, }} > Se connecter </Link> </Typography> </Box> </Paper> </Container> ); }; export default RegisterPage;