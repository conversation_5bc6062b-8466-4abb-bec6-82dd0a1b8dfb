import React, { useState, useEffect } from 'react'; import { Link, useNavigate, useLocation } from 'react-router-dom'; import { Box, Paper, TextField, Button, Typography, Alert, Container, Grid, InputAdornment, IconButton, Divider, Chip, } from '@mui/material'; import { Visibility, VisibilityOff, Email, Lock, Phone, Speed, Security, SupportAgent, } from '@mui/icons-material'; import { useAuth } from '../hooks/useAuth'; import { ROUTES, FREE_MOBILE_COLORS } from '../utils/constants'; import LoadingSpinner from '../components/Common/LoadingSpinner'; const LoginPage: React.FC = () => { const navigate = useNavigate(); const location = useLocation(); const { login, loading, error, isAuthenticated, clearError } = useAuth(); const [formData, setFormData] = useState({ email: '<EMAIL>', password: 'Password131', }); const [showPassword, setShowPassword] = useState(false); const from = location.state?.from?.pathname || ROUTES.DASHBOARD; useEffect(() => { if (isAuthenticated) { navigate(from, { replace: true }); } }, [isAuthenticated, navigate, from]); useEffect(() => { clearError(); }, [clearError]); const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => { setFormData({ ...formData, [e.target.name]: e.target.value, }); }; const handleSubmit = async (e: React.FormEvent) => { e.preventDefault(); try { await login(formData.email, formData.password).unwrap(); navigate(from, { replace: true }); } catch (error) { console.error('Erreur de connexion:', error); } }; const togglePasswordVisibility = () => { setShowPassword(!showPassword); }; if (loading) { return <LoadingSpinner message="Connexion en cours..." />; } return ( <Container maxWidth="lg" sx={{ minHeight: '100vh', py: 4 }}> <Grid container spacing={4} alignItems="center" justifyContent="center"> {/* Section gauche - Branding */} <Grid item xs={12} md={6}> <Box sx={{ textAlign: { xs: 'center', md: 'left' }, mb: { xs: 4, md: 0 } }}> <Typography variant="h2" sx={{ fontWeight: 700, color: FREE_MOBILE_COLORS.PRIMARY, mb: 2, }} > Free Mobile </Typography> <Typography variant="h4" sx={{ color: FREE_MOBILE_COLORS.SECONDARY, mb: 3, fontWeight: 500, }} > Assistant Support Intelligent </Typography> <Typography variant="h6" color="text.secondary" sx={{ mb: 4, lineHeight: 1.6 }} > Accédez à votre espace client et chattez avec notre assistant IA pour gérer votre forfait, consulter vos factures et obtenir de l'aide instantanément. </Typography> {/* Fonctionnalités */} <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}> <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}> <Phone color="primary" /> <Typography variant="body1"> Gestion complète de votre forfait </Typography> </Box> <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}> <Speed color="primary" /> <Typography variant="body1"> Réponses instantanées 24h/24 </Typography> </Box> <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}> <Security color="primary" /> <Typography variant="body1"> Sécurisé et confidentiel </Typography> </Box> <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}> <SupportAgent color="primary" /> <Typography variant="body1"> Escalade vers agent humain si besoin </Typography> </Box> </Box> </Box> </Grid> {/* Section droite - Formulaire */} <Grid item xs={12} md={6}> <Paper elevation={6} sx={{ p: 4, borderRadius: 3, background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)', border: `2px solid ${FREE_MOBILE_COLORS.PRIMARY}20`, }} > <Box sx={{ textAlign: 'center', mb: 3 }}> <Typography variant="h4" gutterBottom fontWeight={600}> Connexion </Typography> <Typography variant="body1" color="text.secondary"> Connectez-vous à votre espace client </Typography> </Box> {error && ( <Alert severity="error" sx={{ mb: 3 }}> {error} </Alert> )} {/* Comptes de test */} <Box sx={{ mb: 3 }}> <Divider sx={{ mb: 2 }}> <Chip label="Compte de test disponible" size="small" /> </Divider> <Alert severity="info" sx={{ mb: 2 }}> <Typography variant="body2"> <strong>Compte root :</strong><br /> Email: <EMAIL><br /> Mot de passe: Password131 </Typography> </Alert> </Box> <Box component="form" onSubmit={handleSubmit}> <TextField fullWidth label="Adresse email" name="email" type="email" value={formData.email} onChange={handleChange} margin="normal" required autoComplete="email" InputProps={{ startAdornment: ( <InputAdornment position="start"> <Email color="action" /> </InputAdornment> ), }} sx={{ mb: 2 }} /> <TextField fullWidth label="Mot de passe" name="password" type={showPassword ? 'text' : 'password'} value={formData.password} onChange={handleChange} margin="normal" required autoComplete="current-password" InputProps={{ startAdornment: ( <InputAdornment position="start"> <Lock color="action" /> </InputAdornment> ), endAdornment: ( <InputAdornment position="end"> <IconButton onClick={togglePasswordVisibility} edge="end" aria-label="toggle password visibility" > {showPassword ? <VisibilityOff /> : <Visibility />} </IconButton> </InputAdornment> ), }} sx={{ mb: 3 }} /> <Button type="submit" fullWidth variant="contained" size="large" disabled={loading} sx={{ py: 1.5, mb: 2, fontWeight: 600, fontSize: '1.1rem', }} > {loading ? 'Connexion...' : 'Se connecter'} </Button> <Divider sx={{ my: 2 }} /> <Box sx={{ textAlign: 'center' }}> <Typography variant="body2" color="text.secondary"> Pas encore de compte ?{' '} <Link to={ROUTES.REGISTER} style={{ color: FREE_MOBILE_COLORS.PRIMARY, textDecoration: 'none', fontWeight: 500, }} > Créer un compte </Link> </Typography> </Box> </Box> </Paper> </Grid> </Grid> </Container> ); }; export default LoginPage;