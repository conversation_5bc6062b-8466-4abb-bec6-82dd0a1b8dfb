import React, { useEffect } from 'react'; import { useDispatch, useSelector } from 'react-redux'; import { Box, Grid, Card, CardContent, Typography, CircularProgress, Alert, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, } from '@mui/material'; import { TrendingUp, Chat, CheckCircle, Schedule, Star, } from '@mui/icons-material'; import { AppDispatch, RootState } from '../store'; import { fetchDashboard } from '../store/adminSlice'; const AdminDashboard: React.FC = () => { const dispatch = useDispatch<AppDispatch>(); const { dashboard, loading, error } = useSelector((state: RootState) => state.admin); useEffect(() => { dispatch(fetchDashboard()); }, [dispatch]); if (loading) { return ( <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px"> <CircularProgress /> </Box> ); } if (error) { return <Alert severity="error">{error}</Alert>; } if (!dashboard) { return <Alert severity="info">Aucune donnée disponible</Alert>; } const getStatusColor = (status: string) => { switch (status) { case 'active': return 'primary'; case 'resolved': return 'success'; case 'escalated': return 'warning'; case 'abandoned': return 'error'; default: return 'default'; } }; const formatDate = (date: string | Date) => { return new Date(date).toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit', year: 'numeric', hour: '2-digit', minute: '2-digit', }); }; return ( <Box sx={{ p: 3 }}> <Typography variant="h4" gutterBottom> Dashboard Administrateur </Typography> {/* Statistiques principales */} <Grid container spacing={3} sx={{ mb: 4 }}> <Grid item xs={12} sm={6} md={3}> <Card> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center' }}> <Chat sx={{ mr: 2, color: 'primary.main' }} /> <Box> <Typography variant="h5"> {dashboard.statistics.totalConversations} </Typography> <Typography variant="body2" color="text.secondary"> Total Conversations </Typography> </Box> </Box> </CardContent> </Card> </Grid> <Grid item xs={12} sm={6} md={3}> <Card> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center' }}> <Schedule sx={{ mr: 2, color: 'warning.main' }} /> <Box> <Typography variant="h5"> {dashboard.statistics.activeConversations} </Typography> <Typography variant="body2" color="text.secondary"> Conversations Actives </Typography> </Box> </Box> </CardContent> </Card> </Grid> <Grid item xs={12} sm={6} md={3}> <Card> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center' }}> <CheckCircle sx={{ mr: 2, color: 'success.main' }} /> <Box> <Typography variant="h5"> {dashboard.statistics.resolvedConversations} </Typography> <Typography variant="body2" color="text.secondary"> Conversations Résolues </Typography> </Box> </Box> </CardContent> </Card> </Grid> <Grid item xs={12} sm={6} md={3}> <Card> <CardContent> <Box sx={{ display: 'flex', alignItems: 'center' }}> <Star sx={{ mr: 2, color: 'info.main' }} /> <Box> <Typography variant="h5"> {dashboard.statistics.avgSatisfaction.toFixed(1)}/5 </Typography> <Typography variant="body2" color="text.secondary"> Satisfaction Moyenne </Typography> </Box> </Box> </CardContent> </Card> </Grid> </Grid> <Grid container spacing={3}> {/* Répartition par statut */} <Grid item xs={12} md={6}> <Card> <CardContent> <Typography variant="h6" gutterBottom> Répartition par Statut </Typography> <Box sx={{ mt: 2 }}> {dashboard.conversationsByStatus.map((item) => ( <Box key={item._id} sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}> <Chip label={item._id} color={getStatusColor(item._id) as any} variant="outlined" /> <Typography variant="body1">{item.count}</Typography> </Box> ))} </Box> </CardContent> </Card> </Grid> {/* Conversations récentes */} <Grid item xs={12} md={6}> <Card> <CardContent> <Typography variant="h6" gutterBottom> Conversations Récentes </Typography> <TableContainer> <Table size="small"> <TableHead> <TableRow> <TableCell>Client</TableCell> <TableCell>Statut</TableCell> <TableCell>Date</TableCell> </TableRow> </TableHead> <TableBody> {dashboard.recentConversations.slice(0, 5).map((conversation) => ( <TableRow key={conversation._id}> <TableCell> {(conversation as any).userId?.profile?.firstName || 'Anonyme'} </TableCell> <TableCell> <Chip size="small" label={conversation.status} color={getStatusColor(conversation.status) as any} /> </TableCell> <TableCell> {formatDate(conversation.startedAt)} </TableCell> </TableRow> ))} </TableBody> </Table> </TableContainer> </CardContent> </Card> </Grid> </Grid> </Box> ); }; export default AdminDashboard;