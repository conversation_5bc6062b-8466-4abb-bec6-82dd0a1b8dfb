import React from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Paper,
  Avatar,
} from '@mui/material';
import {
  Support as SupportIcon,
  Assignment as FormIcon,
  Chat as ChatIcon,
  Phone as PhoneIcon,
  Schedule as ScheduleIcon,
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { FREE_MOBILE_COLORS, ROUTES } from '../../utils/constants';

const DashboardOverview: React.FC = () => {
  const navigate = useNavigate();

  const quickActions = [
    {
      title: 'Nouveau ticket',
      description: 'Créer une demande de support',
      icon: <FormIcon />,
      action: () => navigate(ROUTES.DASHBOARD_SUPPORT_FORM),
      color: FREE_MOBILE_COLORS.PRIMARY,
    },
    {
      title: 'Chat en direct',
      description: 'Parler avec un agent',
      icon: <ChatIcon />,
      action: () => navigate(ROUTES.CHAT),
      color: FREE_MOBILE_COLORS.SUCCESS,
    },
    {
      title: 'Appel téléphonique',
      description: 'Nous appeler directement',
      icon: <PhoneIcon />,
      action: () => window.open('tel:1044'),
      color: FREE_MOBILE_COLORS.INFO,
    },
  ];

  const supportStats = [
    {
      label: 'Tickets ouverts',
      value: '12',
      icon: <SupportIcon />,
      color: FREE_MOBILE_COLORS.WARNING,
    },
    {
      label: 'Temps de réponse moyen',
      value: '2.4h',
      icon: <ScheduleIcon />,
      color: FREE_MOBILE_COLORS.INFO,
    },
    {
      label: 'Satisfaction client',
      value: '4.2/5',
      icon: <TrendingUpIcon />,
      color: FREE_MOBILE_COLORS.SUCCESS,
    },
    {
      label: 'Agents disponibles',
      value: '8',
      icon: <PeopleIcon />,
      color: FREE_MOBILE_COLORS.PRIMARY,
    },
  ];

  return (
    <Box>
      {/* Welcome Section */}
      <Paper sx={{ p: 4, mb: 4, backgroundColor: FREE_MOBILE_COLORS.PRIMARY, color: 'white' }}>
        <Typography variant="h4" gutterBottom fontWeight="bold">
          Bienvenue sur le support Free Mobile
        </Typography>
        <Typography variant="h6" sx={{ opacity: 0.9 }}>
          Nous sommes là pour vous aider 24h/24 et 7j/7
        </Typography>
      </Paper>

      <Grid container spacing={3}>
        {/* Quick Actions */}
        <Grid item xs={12} md={8}>
          <Card elevation={2}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom fontWeight="bold">
                Actions rapides
              </Typography>
              <Grid container spacing={2}>
                {quickActions.map((action, index) => (
                  <Grid item xs={12} sm={4} key={index}>
                    <Card
                      variant="outlined"
                      sx={{
                        cursor: 'pointer',
                        transition: 'all 0.2s',
                        '&:hover': {
                          boxShadow: 2,
                          transform: 'translateY(-2px)',
                        },
                      }}
                      onClick={action.action}
                    >
                      <CardContent sx={{ textAlign: 'center', p: 3 }}>
                        <Avatar
                          sx={{
                            backgroundColor: action.color,
                            width: 56,
                            height: 56,
                            mx: 'auto',
                            mb: 2,
                          }}
                        >
                          {action.icon}
                        </Avatar>
                        <Typography variant="h6" gutterBottom fontWeight="bold">
                          {action.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {action.description}
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Support Stats */}
        <Grid item xs={12} md={4}>
          <Card elevation={2}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom fontWeight="bold">
                Statistiques du support
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {supportStats.map((stat, index) => (
                  <Paper
                    key={index}
                    variant="outlined"
                    sx={{
                      p: 2,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 2,
                    }}
                  >
                    <Avatar
                      sx={{
                        backgroundColor: stat.color,
                        width: 40,
                        height: 40,
                      }}
                    >
                      {stat.icon}
                    </Avatar>
                    <Box>
                      <Typography variant="h6" fontWeight="bold">
                        {stat.value}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {stat.label}
                      </Typography>
                    </Box>
                  </Paper>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default DashboardOverview;