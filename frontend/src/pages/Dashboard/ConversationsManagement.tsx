import React, { useState } from 'react'; import { <PERSON>, <PERSON>rid, Card, Card<PERSON>ontent, CardHeader, Typography, List, ListItem, ListItemText, ListItemIcon, Avatar, Chip, Button, TextField, InputAdornment, FormControl, InputLabel, Select, MenuItem, Tabs, Tab, Badge, IconButton, Menu, Paper, } from '@mui/material'; import { Search as SearchIcon, FilterList as FilterIcon, MoreVert as MoreVertIcon, <PERSON>t as ChatIcon, Person as PersonIcon, Schedule as ScheduleIcon, Assignment as AssignmentIcon, } from '@mui/icons-material'; import { FREE_MOBILE_COLORS } from '../../utils/constants'; interface Conversation { id: string; customer: string; agent?: string; channel: 'web' | 'mobile' | 'phone' | 'email'; status: 'active' | 'waiting' | 'resolved' | 'escalated'; priority: 'high' | 'medium' | 'low'; subject: string; lastMessage: string; lastActivity: string; duration: string; avatar: string; unreadCount?: number; } const mockConversations: Conversation[] = [ { id: 'conv-1', customer: '<PERSON>', agent: 'Agent <PERSON>', channel: 'web', status: 'active', priority: 'high', subject: 'Problème de connexion WiFi', lastMessage: 'Tous mes appareils n\'arrivent plus à se connecter', lastActivity: '2 min', duration: '15 min', avatar: 'MD', unreadCount: 2, }, { id: 'conv-2', customer: 'Jean Martin', channel: 'mobile', status: 'waiting', priority: 'medium', subject: 'Question sur la facturation', lastMessage: 'Je ne comprends pas cette ligne sur ma facture', lastActivity: '5 min', duration: '8 min', avatar: 'JM', unreadCount: 1, }, { id: 'conv-3', customer: 'Sophie Bernard', agent: 'Agent Marc', channel: 'phone', status: 'resolved', priority: 'low', subject: 'Changement de forfait', lastMessage: 'Parfait, merci pour votre aide !', lastActivity: '1h', duration: '12 min', avatar: 'SB', }, { id: 'conv-4', customer: 'Pierre Dubois', channel: 'email', status: 'escalated', priority: 'high', subject: 'Problème technique complexe', lastMessage: 'Le problème persiste malgré les manipulations', lastActivity: '30 min', duration: '45 min', avatar: 'PD', unreadCount: 3, }, ]; const ConversationsManagement: React.FC = () => { const [conversations] = useState<Conversation[]>(mockConversations); const [selectedTab, setSelectedTab] = useState(0); const [searchTerm, setSearchTerm] = useState(''); const [statusFilter, setStatusFilter] = useState(''); const [channelFilter, setChannelFilter] = useState(''); const [selectedConversation, setSelectedConversation] = useState<string | null>(null); const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null); const getStatusColor = (status: string) => { switch (status) { case 'active': return 'success'; case 'waiting': return 'warning'; case 'resolved': return 'default'; case 'escalated': return 'error'; default: return 'default'; } }; const getPriorityColor = (priority: string) => { switch (priority) { case 'high': return FREE_MOBILE_COLORS.ERROR; case 'medium': return FREE_MOBILE_COLORS.WARNING; case 'low': return FREE_MOBILE_COLORS.SUCCESS; default: return FREE_MOBILE_COLORS.TEXT_SECONDARY; } }; const getChannelIcon = (channel: string) => { switch (channel) { case 'web': return ''; case 'mobile': return '[MOBILE]'; case 'phone': return ''; case 'email': return ''; default: return ''; } }; const getStatusLabel = (status: string) => { switch (status) { case 'active': return 'Actif'; case 'waiting': return 'En attente'; case 'resolved': return 'Résolu'; case 'escalated': return 'Escaladé'; default: return status; } }; const filteredConversations = conversations.filter((conv) => { const matchesSearch = conv.customer.toLowerCase().includes(searchTerm.toLowerCase()) || conv.subject.toLowerCase().includes(searchTerm.toLowerCase()); const matchesStatus = !statusFilter || conv.status === statusFilter; const matchesChannel = !channelFilter || conv.channel === channelFilter; return matchesSearch && matchesStatus && matchesChannel; }); const getTabCounts = () => { return { all: conversations.length, active: conversations.filter(c => c.status === 'active').length, waiting: conversations.filter(c => c.status === 'waiting').length, escalated: conversations.filter(c => c.status === 'escalated').length, }; }; const tabCounts = getTabCounts(); const handleMenuClick = (event: React.MouseEvent<HTMLElement>, convId: string) => { setAnchorEl(event.currentTarget); setSelectedConversation(convId); }; const handleMenuClose = () => { setAnchorEl(null); setSelectedConversation(null); }; return ( <Box> {/* Header */} <Typography variant="h4" gutterBottom fontWeight="bold"> Gestion des Conversations </Typography> <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}> Surveillez et gérez toutes les conversations client en temps réel </Typography> <Grid container spacing={3}> {/* Conversations List */} <Grid item xs={12} md={8}> <Card elevation={2}> <CardHeader title="Conversations" action={ <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}> <TextField size="small" placeholder="Rechercher..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} InputProps={{ startAdornment: ( <InputAdornment position="start"> <SearchIcon /> </InputAdornment> ), }} sx={{ width: 200 }} /> <FormControl size="small" sx={{ minWidth: 120 }}> <InputLabel>Statut</InputLabel> <Select value={statusFilter} onChange={(e) => setStatusFilter(e.target.value)} label="Statut" > <MenuItem value="">Tous</MenuItem> <MenuItem value="active">Actif</MenuItem> <MenuItem value="waiting">En attente</MenuItem> <MenuItem value="resolved">Résolu</MenuItem> <MenuItem value="escalated">Escaladé</MenuItem> </Select> </FormControl> <FormControl size="small" sx={{ minWidth: 120 }}> <InputLabel>Canal</InputLabel> <Select value={channelFilter} onChange={(e) => setChannelFilter(e.target.value)} label="Canal" > <MenuItem value="">Tous</MenuItem> <MenuItem value="web">Web</MenuItem> <MenuItem value="mobile">Mobile</MenuItem> <MenuItem value="phone">Téléphone</MenuItem> <MenuItem value="email">Email</MenuItem> </Select> </FormControl> </Box> } /> {/* Tabs */} <Tabs value={selectedTab} onChange={(e, newValue) => setSelectedTab(newValue)} sx={{ px: 2 }} > <Tab label={ <Badge badgeContent={tabCounts.all} color="primary"> Toutes </Badge> } /> <Tab label={ <Badge badgeContent={tabCounts.active} color="success"> Actives </Badge> } /> <Tab label={ <Badge badgeContent={tabCounts.waiting} color="warning"> En attente </Badge> } /> <Tab label={ <Badge badgeContent={tabCounts.escalated} color="error"> Escaladées </Badge> } /> </Tabs> <CardContent sx={{ pt: 0 }}> <List> {filteredConversations.map((conv) => ( <ListItem key={conv.id} sx={{ border: '1px solid', borderColor: 'divider', borderRadius: 1, mb: 1, '&:hover': { backgroundColor: 'action.hover', }, }} > <ListItemIcon> <Badge badgeContent={conv.unreadCount} color="error" invisible={!conv.unreadCount} > <Avatar sx={{ width: 40, height: 40, fontSize: '0.8rem' }}> {conv.avatar} </Avatar> </Badge> </ListItemIcon> <ListItemText primary={ <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}> <Typography variant="subtitle1" fontWeight="bold"> {conv.customer} </Typography> <Typography variant="body2"> {getChannelIcon(conv.channel)} </Typography> <Box sx={{ width: 6, height: 6, borderRadius: '50%', backgroundColor: getPriorityColor(conv.priority), }} /> <Chip label={getStatusLabel(conv.status)} size="small" color={getStatusColor(conv.status) as any} variant="outlined" /> </Box> } secondary={ <Box> <Typography variant="body2" fontWeight="medium" gutterBottom> {conv.subject} </Typography> <Typography variant="body2" color="text.secondary" gutterBottom> {conv.lastMessage} </Typography> <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}> <Typography variant="caption" color="text.secondary"> {conv.agent ? `Assigné à ${conv.agent}` : 'Non assigné'} • Durée: {conv.duration} </Typography> <Typography variant="caption" color="text.secondary"> Il y a {conv.lastActivity} </Typography> </Box> </Box> } /> <IconButton onClick={(e) => handleMenuClick(e, conv.id)} > <MoreVertIcon /> </IconButton> </ListItem> ))} </List> </CardContent> </Card> </Grid> {/* Quick Stats & Actions */} <Grid item xs={12} md={4}> {/* Quick Stats */} <Card elevation={2} sx={{ mb: 3 }}> <CardHeader title="Statistiques temps réel" /> <CardContent> <Grid container spacing={2}> <Grid item xs={6}> <Paper sx={{ p: 2, textAlign: 'center' }}> <ChatIcon color="primary" sx={{ fontSize: 32, mb: 1 }} /> <Typography variant="h6" fontWeight="bold"> {tabCounts.active} </Typography> <Typography variant="body2" color="text.secondary"> Conversations actives </Typography> </Paper> </Grid> <Grid item xs={6}> <Paper sx={{ p: 2, textAlign: 'center' }}> <ScheduleIcon color="warning" sx={{ fontSize: 32, mb: 1 }} /> <Typography variant="h6" fontWeight="bold"> {tabCounts.waiting} </Typography> <Typography variant="body2" color="text.secondary"> En attente </Typography> </Paper> </Grid> <Grid item xs={6}> <Paper sx={{ p: 2, textAlign: 'center' }}> <PersonIcon color="success" sx={{ fontSize: 32, mb: 1 }} /> <Typography variant="h6" fontWeight="bold"> 8 </Typography> <Typography variant="body2" color="text.secondary"> Agents en ligne </Typography> </Paper> </Grid> <Grid item xs={6}> <Paper sx={{ p: 2, textAlign: 'center' }}> <AssignmentIcon color="error" sx={{ fontSize: 32, mb: 1 }} /> <Typography variant="h6" fontWeight="bold"> {tabCounts.escalated} </Typography> <Typography variant="body2" color="text.secondary"> Escaladées </Typography> </Paper> </Grid> </Grid> </CardContent> </Card> {/* Quick Actions */} <Card elevation={2}> <CardHeader title="Actions rapides" /> <CardContent> <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}> <Button variant="contained" fullWidth startIcon={<AssignmentIcon />} sx={{ backgroundColor: FREE_MOBILE_COLORS.PRIMARY }} > Assigner conversations </Button> <Button variant="outlined" fullWidth startIcon={<ChatIcon />} > Rejoindre conversation </Button> <Button variant="outlined" fullWidth startIcon={<PersonIcon />} > Gérer les agents </Button> </Box> </CardContent> </Card> </Grid> </Grid> {/* Context Menu */} <Menu anchorEl={anchorEl} open={Boolean(anchorEl)} onClose={handleMenuClose} > <MenuItem onClick={handleMenuClose}>Rejoindre conversation</MenuItem> <MenuItem onClick={handleMenuClose}>Assigner à un agent</MenuItem> <MenuItem onClick={handleMenuClose}>Changer le statut</MenuItem> <MenuItem onClick={handleMenuClose}>Escalader</MenuItem> <MenuItem onClick={handleMenuClose}>Voir l'historique</MenuItem> </Menu> </Box> ); }; export default ConversationsManagement;