import React, { useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Alert,
  Avatar,
} from '@mui/material';
import {
  Support as SupportIcon,
  Send as SendIcon,
} from '@mui/icons-material';
import { FREE_MOBILE_COLORS } from '../../utils/constants';

interface SupportFormData {
  fullName: string;
  email: string;
  category: string;
  description: string;
}

const SupportFormPage: React.FC = () => {
  const [formData, setFormData] = useState<SupportFormData>({
    fullName: '',
    email: '',
    category: '',
    description: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  const handleInputChange = (field: keyof SupportFormData) => (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value,
    }));
  };

  const handleSelectChange = (field: keyof SupportFormData) => (event: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value,
    }));
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log('Form submitted:', formData);
      setSubmitSuccess(true);

      // Reset form after success
      setTimeout(() => {
        setFormData({
          fullName: '',
          email: '',
          category: '',
          description: '',
        });
        setSubmitSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const isFormValid = formData.fullName && formData.email && formData.category && formData.description;

  return (
    <Box sx={{ maxWidth: 800, mx: 'auto', p: 2 }}>
      <Card elevation={3} sx={{ borderRadius: 2, overflow: 'hidden' }}>
        {/* Header */}
        <Box
          sx={{
            background: `linear-gradient(135deg, ${FREE_MOBILE_COLORS.PRIMARY} 0%, #cc0000 100%)`,
            color: 'white',
            p: 3,
            display: 'flex',
            alignItems: 'center',
            gap: 2
          }}
        >
          <Avatar sx={{ bgcolor: 'white', color: FREE_MOBILE_COLORS.PRIMARY }}>
            <SupportIcon />
          </Avatar>
          <Box>
            <Typography variant="h4" fontWeight="bold">
              Formulaire de Support
            </Typography>
            <Typography variant="body2" sx={{ opacity: 0.9 }}>
              Décrivez votre problème et nous vous aiderons rapidement
            </Typography>
          </Box>
        </Box>

        <CardContent sx={{ p: 3 }}>
          {/* Success Alert */}
          {submitSuccess && (
            <Alert severity="success" sx={{ mb: 3, borderRadius: 2 }}>
              <Typography variant="h6" gutterBottom>
                Demande envoyée avec succès !
              </Typography>
              <Typography variant="body2">
                Votre demande a été transmise à notre équipe. Nous vous répondrons dans les plus brefs délais.
              </Typography>
            </Alert>
          )}

          {/* Form */}
          <form onSubmit={handleSubmit}>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Nom complet"
                  value={formData.fullName}
                  onChange={handleInputChange('fullName')}
                  required
                  variant="outlined"
                  sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Adresse email"
                  type="email"
                  value={formData.email}
                  onChange={handleInputChange('email')}
                  required
                  variant="outlined"
                  sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                />
              </Grid>

              <Grid item xs={12}>
                <FormControl fullWidth required>
                  <InputLabel>Catégorie du problème</InputLabel>
                  <Select
                    value={formData.category}
                    onChange={handleSelectChange('category')}
                    label="Catégorie du problème"
                    sx={{ borderRadius: 2 }}
                  >
                    <MenuItem value="technique">Problème technique</MenuItem>
                    <MenuItem value="facturation">Facturation</MenuItem>
                    <MenuItem value="service">Service client</MenuItem>
                    <MenuItem value="autre">Autre</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description détaillée du problème"
                  multiline
                  rows={6}
                  value={formData.description}
                  onChange={handleInputChange('description')}
                  required
                  variant="outlined"
                  placeholder="Décrivez votre problème en détail..."
                  sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                />
              </Grid>
            </Grid>

            {/* Submit Button */}
            <Box sx={{ mt: 4, textAlign: 'center' }}>
              <Button
                type="submit"
                variant="contained"
                size="large"
                disabled={!isFormValid || isSubmitting}
                startIcon={<SendIcon />}
                sx={{
                  borderRadius: 2,
                  bgcolor: FREE_MOBILE_COLORS.PRIMARY,
                  '&:hover': { bgcolor: '#cc0000' },
                  minWidth: 200
                }}
              >
                {isSubmitting ? 'Envoi en cours...' : 'Envoyer ma demande'}
              </Button>
            </Box>
          </form>
        </CardContent>
      </Card>
    </Box>
  );
};

export default SupportFormPage;