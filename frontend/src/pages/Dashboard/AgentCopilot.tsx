import React, { useState } from 'react'; import { Box, <PERSON>rid, Card, CardContent, CardHeader, Typography, List, ListItem, ListItemText, ListItemIcon, Chip, Button, Paper, Avatar, Divider, TextField, IconButton, } from '@mui/material'; import { Psychology as AIIcon, Lightbulb as SuggestionIcon, Person as CustomerIcon, Chat as ChatIcon, Send as SendIcon, ThumbUp as ThumbUpIcon, ThumbDown as ThumbDownIcon, AutoAwesome as AutoIcon, History as HistoryIcon, } from '@mui/icons-material'; import { FREE_MOBILE_COLORS } from '../../utils/constants'; const AgentCopilot: React.FC = () => { const [selectedConversation, setSelectedConversation] = useState('conv-1'); const [agentMessage, setAgentMessage] = useState(''); const activeConversations = [ { id: 'conv-1', customer: '<PERSON>', issue: 'Problème de connexion WiFi', priority: 'Haute', duration: '5 min', status: 'En cours', avatar: 'MD', }, { id: 'conv-2', customer: '<PERSON>', issue: 'Question facturation', priority: 'Moyenne', duration: '12 min', status: 'En attente', avatar: 'JM', }, { id: 'conv-3', customer: 'Sophie Bernard', issue: 'Changement de forfait', priority: 'Basse', duration: '3 min', status: 'Nouveau', avatar: 'SB', }, ]; const aiSuggestions = [ { type: 'response', confidence: 95, title: 'Réponse suggérée', content: 'Bonjour Marie, je comprends votre problème de connexion WiFi. Pouvez-vous me confirmer si le voyant WiFi de votre Freebox clignote ou est fixe ?', reasoning: 'Basé sur 1,247 conversations similaires résolues avec succès', }, { type: 'action', confidence: 87, title: 'Action recommandée', content: 'Proposer un redémarrage de la Freebox', reasoning: 'Résout 78% des problèmes de connexion WiFi', }, { type: 'escalation', confidence: 23, title: 'Escalade suggérée', content: 'Pas d\'escalade nécessaire pour le moment', reasoning: 'Problème standard résolvable en niveau 1', }, ]; const customerContext = { name: 'Marie Dupont', id: 'FREE-12345678', plan: 'Forfait 150Go', joinDate: '15/01/2023', satisfaction: 4.2, previousIssues: [ { date: '12/07/2024', issue: 'Problème de facturation', status: 'Résolu' }, { date: '03/06/2024', issue: 'Question sur le forfait', status: 'Résolu' }, ], deviceInfo: { model: 'Freebox Revolution', firmware: '4.2.3', lastRestart: '2 jours', }, }; const conversationHistory = [ { sender: 'customer', message: 'Bonjour, j\'ai un problème avec ma connexion WiFi depuis ce matin', time: '14:32', }, { sender: 'agent', message: 'Bonjour Marie, je vais vous aider avec votre problème de WiFi. Pouvez-vous me dire si d\'autres appareils sont également affectés ?', time: '14:33', }, { sender: 'customer', message: 'Oui, tous mes appareils n\'arrivent plus à se connecter', time: '14:34', }, ]; const getPriorityColor = (priority: string) => { switch (priority.toLowerCase()) { case 'haute': return FREE_MOBILE_COLORS.ERROR; case 'moyenne': return FREE_MOBILE_COLORS.WARNING; case 'basse': return FREE_MOBILE_COLORS.SUCCESS; default: return FREE_MOBILE_COLORS.TEXT_SECONDARY; } }; const getConfidenceColor = (confidence: number) => { if (confidence >= 90) return FREE_MOBILE_COLORS.SUCCESS; if (confidence >= 70) return FREE_MOBILE_COLORS.WARNING; return FREE_MOBILE_COLORS.ERROR; }; const handleSendMessage = () => { if (agentMessage.trim()) { // Logic to send message setAgentMessage(''); } }; const handleUseSuggestion = (suggestion: any) => { setAgentMessage(suggestion.content); }; return ( <Box> {/* Header */} <Typography variant="h4" gutterBottom fontWeight="bold"> Agent Co-pilot </Typography> <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}> Assistant IA pour optimiser vos interactions client </Typography> <Grid container spacing={3}> {/* Active Conversations */} <Grid item xs={12} md={3}> <Card elevation={2}> <CardHeader title="Conversations actives" /> <CardContent sx={{ pt: 0 }}> <List dense> {activeConversations.map((conv) => ( <ListItem key={conv.id} button selected={selectedConversation === conv.id} onClick={() => setSelectedConversation(conv.id)} sx={{ borderRadius: 1, mb: 1, '&.Mui-selected': { backgroundColor: `${FREE_MOBILE_COLORS.PRIMARY}15`, }, }} > <ListItemIcon> <Avatar sx={{ width: 32, height: 32, fontSize: '0.8rem' }}> {conv.avatar} </Avatar> </ListItemIcon> <ListItemText primary={ <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}> <Typography variant="subtitle2" fontWeight="medium"> {conv.customer} </Typography> <Box sx={{ width: 6, height: 6, borderRadius: '50%', backgroundColor: getPriorityColor(conv.priority), }} /> </Box> } secondary={ <Box> <Typography variant="caption" color="text.secondary"> {conv.issue} </Typography> <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}> {conv.duration} </Typography> </Box> } /> </ListItem> ))} </List> </CardContent> </Card> </Grid> {/* Main Chat Area */} <Grid item xs={12} md={6}> <Card elevation={2} sx={{ height: 600, display: 'flex', flexDirection: 'column' }}> <CardHeader title={`Conversation avec ${customerContext.name}`} subheader={`${customerContext.id} • ${customerContext.plan}`} /> {/* Chat Messages */} <CardContent sx={{ flexGrow: 1, overflow: 'auto' }}> {conversationHistory.map((msg, index) => ( <Box key={index} sx={{ display: 'flex', justifyContent: msg.sender === 'agent' ? 'flex-end' : 'flex-start', mb: 2, }} > <Paper elevation={1} sx={{ p: 2, maxWidth: '70%', backgroundColor: msg.sender === 'agent' ? FREE_MOBILE_COLORS.PRIMARY : 'grey.100', color: msg.sender === 'agent' ? 'white' : 'text.primary', }} > <Typography variant="body2">{msg.message}</Typography> <Typography variant="caption" sx={{ display: 'block', mt: 0.5, opacity: 0.7, }} > {msg.time} </Typography> </Paper> </Box> ))} </CardContent> {/* Message Input */} <Divider /> <Box sx={{ p: 2 }}> <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}> <TextField fullWidth multiline maxRows={3} placeholder="Tapez votre réponse..." value={agentMessage} onChange={(e) => setAgentMessage(e.target.value)} variant="outlined" size="small" /> <IconButton onClick={handleSendMessage} disabled={!agentMessage.trim()} sx={{ backgroundColor: FREE_MOBILE_COLORS.PRIMARY, color: 'white', '&:hover': { backgroundColor: '#cc0000', }, }} > <SendIcon /> </IconButton> </Box> </Box> </Card> </Grid> {/* AI Suggestions & Customer Context */} <Grid item xs={12} md={3}> {/* AI Suggestions */} <Card elevation={2} sx={{ mb: 3 }}> <CardHeader title="Suggestions IA" avatar={<AIIcon color="primary" />} /> <CardContent sx={{ pt: 0 }}> <List dense> {aiSuggestions.map((suggestion, index) => ( <ListItem key={index} sx={{ px: 0, alignItems: 'flex-start' }}> <ListItemIcon sx={{ mt: 0.5 }}> <SuggestionIcon color="warning" fontSize="small" /> </ListItemIcon> <ListItemText primary={ <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}> <Typography variant="subtitle2" fontWeight="bold"> {suggestion.title} </Typography> <Chip label={`${suggestion.confidence}%`} size="small" sx={{ backgroundColor: getConfidenceColor(suggestion.confidence), color: 'white', fontSize: '0.7rem', }} /> </Box> } secondary={ <Box> <Typography variant="body2" color="text.secondary" paragraph> {suggestion.content} </Typography> <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}> {suggestion.reasoning} </Typography> <Box sx={{ display: 'flex', gap: 0.5 }}> <Button size="small" variant="outlined" onClick={() => handleUseSuggestion(suggestion)} startIcon={<AutoIcon />} > Utiliser </Button> <IconButton size="small"> <ThumbUpIcon fontSize="small" /> </IconButton> <IconButton size="small"> <ThumbDownIcon fontSize="small" /> </IconButton> </Box> </Box> } /> </ListItem> ))} </List> </CardContent> </Card> {/* Customer Context */} <Card elevation={2}> <CardHeader title="Contexte client" avatar={<CustomerIcon color="primary" />} /> <CardContent sx={{ pt: 0 }}> <Box sx={{ mb: 2 }}> <Typography variant="subtitle2" fontWeight="bold" gutterBottom> Informations générales </Typography> <Typography variant="body2" color="text.secondary"> Satisfaction: {customerContext.satisfaction}/5 </Typography> <Typography variant="body2" color="text.secondary"> Client depuis: {customerContext.joinDate} </Typography> </Box> <Divider sx={{ my: 2 }} /> <Box sx={{ mb: 2 }}> <Typography variant="subtitle2" fontWeight="bold" gutterBottom> Équipement </Typography> <Typography variant="body2" color="text.secondary"> {customerContext.deviceInfo.model} </Typography> <Typography variant="body2" color="text.secondary"> Firmware: {customerContext.deviceInfo.firmware} </Typography> <Typography variant="body2" color="text.secondary"> Dernier redémarrage: {customerContext.deviceInfo.lastRestart} </Typography> </Box> <Divider sx={{ my: 2 }} /> <Box> <Typography variant="subtitle2" fontWeight="bold" gutterBottom> Historique récent </Typography> {customerContext.previousIssues.map((issue, index) => ( <Box key={index} sx={{ mb: 1 }}> <Typography variant="body2" fontWeight="medium"> {issue.issue} </Typography> <Typography variant="caption" color="text.secondary"> {issue.date} • {issue.status} </Typography> </Box> ))} </Box> </CardContent> </Card> </Grid> </Grid> </Box> ); }; export default AgentCopilot;