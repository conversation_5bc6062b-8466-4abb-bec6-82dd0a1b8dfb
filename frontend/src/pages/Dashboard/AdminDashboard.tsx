import React from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  Typography,
  Paper,
  Avatar,
} from '@mui/material';
import {
  AdminPanelSettings as AdminIcon,
  People as PeopleIcon,
  Chat as ChatIcon,
  Analytics as AnalyticsIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import { FREE_MOBILE_COLORS } from '../../utils/constants';

const AdminDashboard: React.FC = () => {
  const adminStats = [
    {
      title: 'Utilisateurs Totaux',
      value: '1,234',
      icon: <PeopleIcon />,
      color: FREE_MOBILE_COLORS.PRIMARY,
    },
    {
      title: 'Conversations Actives',
      value: '89',
      icon: <ChatIcon />,
      color: FREE_MOBILE_COLORS.SUCCESS,
    },
    {
      title: 'Tickets Résolus',
      value: '456',
      icon: <AnalyticsIcon />,
      color: FREE_MOBILE_COLORS.INFO,
    },
    {
      title: 'Système',
      value: 'En ligne',
      icon: <SettingsIcon />,
      color: FREE_MOBILE_COLORS.SUCCESS,
    },
  ];

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Paper sx={{ p: 4, mb: 4, backgroundColor: FREE_MOBILE_COLORS.PRIMARY, color: 'white' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Avatar sx={{ bgcolor: 'white', color: FREE_MOBILE_COLORS.PRIMARY, width: 56, height: 56 }}>
            <AdminIcon />
          </Avatar>
          <Box>
            <Typography variant="h4" gutterBottom fontWeight="bold">
              Panneau d'Administration
            </Typography>
            <Typography variant="h6" sx={{ opacity: 0.9 }}>
              Gestion et supervision du système Free Mobile
            </Typography>
          </Box>
        </Box>
      </Paper>

      {/* Stats Grid */}
      <Grid container spacing={3}>
        {adminStats.map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card elevation={2} sx={{ borderRadius: 2 }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar sx={{ bgcolor: stat.color, width: 56, height: 56 }}>
                    {stat.icon}
                  </Avatar>
                  <Box>
                    <Typography variant="h4" fontWeight="bold">
                      {stat.value}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {stat.title}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}

        {/* Admin Sections */}
        <Grid item xs={12}>
          <Card elevation={2} sx={{ borderRadius: 2 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Sections d'Administration
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={4}>
                  <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h6">Notifications</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Gérer les notifications système
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h6">Suggestions IA</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Configuration des suggestions automatiques
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h6">ML Intelligence</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Tableau de bord Machine Learning
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AdminDashboard;