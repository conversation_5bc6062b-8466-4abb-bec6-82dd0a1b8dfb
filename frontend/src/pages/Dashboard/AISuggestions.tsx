/** * ============================================= * [FEATURE] AI SUGGESTIONS DASHBOARD PAGE * Advanced AI-powered suggestions and contextual intelligence * Enhances existing AI services with personalization * ============================================= */ import React, { useEffect, useState } from 'react'; import { useAppDispatch, useAppSelector } from '../../hooks/redux'; import { Box, Container, Typography, Grid, Card, CardContent, Button, Chip, Alert, Switch, FormControlLabel, Slider, Select, MenuItem, FormControl, InputLabel, Tabs, Tab, Badge, Tooltip, IconButton, Breadcrumbs, Link, Paper, List, ListItem, ListItemText, ListItemIcon, Divider } from '@mui/material'; import { Psychology as AIIcon, TrendingUp as TrendIcon, Warning as EscalationIcon, Description as TemplateIcon, Settings as SettingsIcon, Refresh as RefreshIcon, ThumbUp as ThumbUpIcon, ThumbDown as ThumbDownIcon, AutoAwesome as SuggestionIcon, SentimentSatisfied as SentimentIcon, NavigateNext as NavigateNextIcon, Speed as SpeedIcon, PersonalVideo as PersonalizeIcon } from '@mui/icons-material'; import { RootState } from '../../store'; import { generateContextualSuggestions, analyzeSentiment, getEscalationRecommendation, fetchResponseTemplates, submitSuggestionFeedback, toggleSuggestionsPanel, toggleTemplatesPanel, toggleSentimentPanel, setPersonalizationEnabled, setAutoSuggestions, setSuggestionFrequency, updateAISettings, selectEnhancedAIState, selectActiveSuggestions, selectCurrentSentiment, selectEscalationRecommendation, selectResponseTemplates, selectAISettings, selectPersonalizedTemplates, selectLoading, selectError, SuggestionFeedback } from '../../store/slices/enhancedAISlice'; import { FREE_MOBILE_COLORS } from '../../utils/constants'; // Import AI components (to be created) import SuggestionCard from '../../components/AI/SuggestionCard'; import SentimentAnalyzer from '../../components/AI/SentimentAnalyzer'; import EscalationAlert from '../../components/AI/EscalationAlert'; import TemplateManager from '../../components/AI/TemplateManager'; import AISettingsPanel from '../../components/AI/AISettingsPanel'; interface TabPanelProps { children?: React.ReactNode; index: number; value: number; } function TabPanel(props: TabPanelProps) { const { children, value, index, ...other } = props; return ( <div role="tabpanel" hidden={value !== index} id={`ai-tabpanel-${index}`} aria-labelledby={`ai-tab-${index}`} {...other} > {value === index && ( <Box sx={{ p: 3 }}> {children} </Box> )} </div> ); } const AISuggestions: React.FC = () => { const dispatch = useAppDispatch(); const aiState = useAppSelector(selectEnhancedAIState); const activeSuggestions = useAppSelector(selectActiveSuggestions); const currentSentiment = useAppSelector(selectCurrentSentiment); const escalationRecommendation = useAppSelector(selectEscalationRecommendation); const responseTemplates = useAppSelector(selectResponseTemplates); const aiSettings = useAppSelector(selectAISettings); const [activeTab, setActiveTab] = useState(0); const [settingsOpen, setSettingsOpen] = useState(false); useEffect(() => { // Initialize AI data dispatch(fetchResponseTemplates('current-agent')); // Start real-time context monitoring if auto-suggestions are enabled if (aiState.auto_suggestions) { // This would start monitoring current conversation context } }, [dispatch, aiState.auto_suggestions]); const handleTabChange = (event: React.SyntheticEvent, newValue: number) => { setActiveTab(newValue); }; const handleSuggestionFeedback = async (suggestionId: string, helpful: boolean, used: boolean) => { const feedback: SuggestionFeedback = { rating: helpful ? (5 as const) : (2 as const), helpful, used_as_is: used, modified: false, outcome: helpful ? ('successful' as const) : ('unsuccessful' as const), customer_response: helpful ? ('positive' as const) : ('neutral' as const) }; try { await dispatch(submitSuggestionFeedback({ suggestion_id: suggestionId, feedback })); } catch (error) { console.error('Failed to submit feedback:', error); } }; const handleSettingsChange = (setting: string, value: any) => { dispatch(updateAISettings({ [setting]: value })); }; const getSentimentColor = (sentiment: number) => { if (sentiment > 0.3) return 'success'; if (sentiment < -0.3) return 'error'; return 'warning'; }; const getSentimentLabel = (sentiment: number) => { if (sentiment > 0.3) return 'Positif'; if (sentiment < -0.3) return 'Négatif'; return 'Neutre'; }; const getConfidenceColor = (confidence: number) => { if (confidence > 0.8) return 'success'; if (confidence > 0.6) return 'warning'; return 'error'; }; return ( <Container maxWidth={false} sx={{ py: 3 }}> {/* Header */} <Box sx={{ mb: 4 }}> <Breadcrumbs separator={<NavigateNextIcon fontSize="small" />} sx={{ mb: 2 }}> <Link color="inherit" href="/dashboard"> Dashboard </Link> <Link color="inherit" href="/dashboard/admin"> Administration </Link> <Typography color="text.primary">Suggestions IA Avancées</Typography> </Breadcrumbs> <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}> <Box> <Typography variant="h4" component="h1" fontWeight="bold" color={FREE_MOBILE_COLORS.PRIMARY}> [FEATURE] Suggestions IA Avancées </Typography> <Typography variant="subtitle1" color="text.secondary"> Intelligence contextuelle et recommandations personnalisées pour optimiser les interactions </Typography> </Box> <Box sx={{ display: 'flex', gap: 1 }}> <FormControlLabel control={ <Switch checked={aiState.auto_suggestions} onChange={(e) => dispatch(setAutoSuggestions(e.target.checked))} /> } label="Suggestions Auto" /> <FormControlLabel control={ <Switch checked={aiState.personalization_enabled} onChange={(e) => dispatch(setPersonalizationEnabled(e.target.checked))} /> } label="Personnalisation" /> <Tooltip title="Paramètres IA"> <IconButton onClick={() => setSettingsOpen(true)}> <SettingsIcon /> </IconButton> </Tooltip> </Box> </Box> {/* Quick Stats */} <Grid container spacing={2} sx={{ mb: 3 }}> <Grid item xs={12} sm={6} md={3}> <Card> <CardContent sx={{ textAlign: 'center' }}> <Typography variant="h3" color="primary" fontWeight="bold"> {activeSuggestions.length} </Typography> <Typography variant="body2" color="text.secondary"> Suggestions Actives </Typography> </CardContent> </Card> </Grid> <Grid item xs={12} sm={6} md={3}> <Card> <CardContent sx={{ textAlign: 'center' }}> <Typography variant="h3" color="success.main" fontWeight="bold"> {Math.round((activeSuggestions.reduce((sum: number, s: any) => sum + s.confidence, 0) / activeSuggestions.length || 0) * 100)}% </Typography> <Typography variant="body2" color="text.secondary"> Confiance Moyenne </Typography> </CardContent> </Card> </Grid> <Grid item xs={12} sm={6} md={3}> <Card> <CardContent sx={{ textAlign: 'center' }}> <Typography variant="h3" color="warning.main" fontWeight="bold"> {responseTemplates.length} </Typography> <Typography variant="body2" color="text.secondary"> Templates Disponibles </Typography> </CardContent> </Card> </Grid> <Grid item xs={12} sm={6} md={3}> <Card> <CardContent sx={{ textAlign: 'center' }}> <Typography variant="h3" color={currentSentiment ? getSentimentColor(currentSentiment.current_sentiment) + '.main' : 'text.secondary'} fontWeight="bold" > {currentSentiment ? getSentimentLabel(currentSentiment.current_sentiment) : 'N/A'} </Typography> <Typography variant="body2" color="text.secondary"> Sentiment Actuel </Typography> </CardContent> </Card> </Grid> </Grid> {/* Escalation Alert */} {escalationRecommendation && escalationRecommendation.should_escalate && ( <Alert severity={escalationRecommendation.urgency === 'immediate' ? 'error' : 'warning'} sx={{ mb: 3 }} icon={<EscalationIcon />} > <Typography variant="body1"> <strong>Recommandation d'Escalade:</strong> {escalationRecommendation.reasoning[0]} </Typography> <Typography variant="body2" sx={{ mt: 1 }}> Confiance: {Math.round(escalationRecommendation.confidence * 100)}% | Type: {escalationRecommendation.escalation_type} | Urgence: {escalationRecommendation.urgency} </Typography> </Alert> )} </Box> {/* Main Content Tabs */} <Card> <Box sx={{ borderBottom: 1, borderColor: 'divider' }}> <Tabs value={activeTab} onChange={handleTabChange} aria-label="ai suggestions tabs"> <Tab label="Suggestions Contextuelles" icon={ <Badge badgeContent={activeSuggestions.length} color="primary"> <SuggestionIcon /> </Badge> } iconPosition="start" /> <Tab label="Analyse de Sentiment" icon={<SentimentIcon />} iconPosition="start" /> <Tab label="Templates de Réponse" icon={ <Badge badgeContent={responseTemplates.length} color="secondary"> <TemplateIcon /> </Badge> } iconPosition="start" /> <Tab label="Personnalisation" icon={<PersonalizeIcon />} iconPosition="start" /> </Tabs> </Box> {/* Tab Panels */} <TabPanel value={activeTab} index={0}> <Grid container spacing={3}> <Grid item xs={12} lg={8}> <Typography variant="h6" sx={{ mb: 2 }}> Suggestions Contextuelles en Temps Réel </Typography> {activeSuggestions.length > 0 ? ( <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}> {activeSuggestions.map((suggestion: any) => ( <SuggestionCard key={suggestion.id} suggestion={suggestion} onFeedback={handleSuggestionFeedback} /> ))} </Box> ) : ( <Paper sx={{ p: 4, textAlign: 'center' }}> <AIIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} /> <Typography variant="h6" color="text.secondary"> Aucune suggestion disponible </Typography> <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}> Les suggestions apparaîtront automatiquement pendant les conversations </Typography> <Button variant="outlined" startIcon={<RefreshIcon />} onClick={() => { // Trigger suggestion generation }} > Générer des Suggestions </Button> </Paper> )} </Grid> <Grid item xs={12} lg={4}> <Typography variant="h6" sx={{ mb: 2 }}> Paramètres Rapides </Typography> <Card> <CardContent> <Typography variant="subtitle2" sx={{ mb: 2 }}> Fréquence des Suggestions </Typography> <FormControl fullWidth size="small" sx={{ mb: 3 }}> <Select value={aiState.suggestion_frequency} onChange={(e) => dispatch(setSuggestionFrequency(e.target.value as any))} > <MenuItem value="low">Faible</MenuItem> <MenuItem value="medium">Moyenne</MenuItem> <MenuItem value="high">Élevée</MenuItem> </Select> </FormControl> <Typography variant="subtitle2" sx={{ mb: 2 }}> Seuil de Confiance: {Math.round(aiSettings.confidence_threshold * 100)}% </Typography> <Slider value={aiSettings.confidence_threshold} onChange={(e, value) => handleSettingsChange('confidence_threshold', value)} min={0.3} max={1.0} step={0.1} marks valueLabelDisplay="auto" valueLabelFormat={(value) => `${Math.round(value * 100)}%`} /> <Typography variant="subtitle2" sx={{ mb: 2, mt: 3 }}> Nombre Max de Suggestions: {aiSettings.max_suggestions} </Typography> <Slider value={aiSettings.max_suggestions} onChange={(e, value) => handleSettingsChange('max_suggestions', value)} min={1} max={10} step={1} marks valueLabelDisplay="auto" /> </CardContent> </Card> </Grid> </Grid> </TabPanel> <TabPanel value={activeTab} index={1}> <SentimentAnalyzer currentSentiment={currentSentiment} sentimentHistory={aiState.sentiment_history} escalationRecommendation={escalationRecommendation} /> </TabPanel> <TabPanel value={activeTab} index={2}> <TemplateManager templates={responseTemplates} personalizedTemplates={aiState.personalized_templates} categories={aiState.template_categories} /> </TabPanel> <TabPanel value={activeTab} index={3}> <AISettingsPanel settings={aiSettings} learningData={aiState.agent_learning_data} suggestionPerformance={aiState.suggestion_performance} onSettingsChange={handleSettingsChange} /> </TabPanel> </Card> {/* Settings Dialog would be rendered here */} </Container> ); }; export default AISuggestions;