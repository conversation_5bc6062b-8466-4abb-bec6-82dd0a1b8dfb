import React, { useState, useEffect, useCallback } from 'react'; import { Box, Container, Typography, Tabs, Tab, Card, CardContent, Avatar, Badge, Fade, Alert, Snackbar, IconButton, Tooltip, Button, ButtonGroup, Divider, CircularProgress, } from '@mui/material'; import { Notifications as NotificationsIcon, Psychology as AIIcon, AutoAwesome as MLIcon, Refresh as RefreshIcon, Settings as SettingsIcon, Dashboard as DashboardIcon, AdminPanelSettings as AdminIcon, } from '@mui/icons-material'; import { FREE_MOBILE_COLORS } from '../../utils/constants'; // Import the three main tab components import NotificationsTab from '../../components/Dashboard/Admin/NotificationsTab'; import AdvancedAISuggestionsTab from '../../components/Dashboard/Admin/AdvancedAISuggestionsTab'; import MLIntelligenceDashboardTab from '../../components/Dashboard/Admin/MLIntelligenceDashboardTab'; interface TabPanelProps { children?: React.ReactNode; index: number; value: number; } function TabPanel(props: TabPanelProps) { const { children, value, index, ...other } = props; return ( <div role="tabpanel" hidden={value !== index} id={`admin-tabpanel-${index}`} aria-labelledby={`admin-tab-${index}`} {...other} > {value === index && ( <Box sx={{ py: 3 }}> {children} </Box> )} </div> ); } const AdministratorPanel: React.FC = () => { const [currentTab, setCurrentTab] = useState(0); const [loading, setLoading] = useState(false); const [lastRefresh, setLastRefresh] = useState(new Date()); const [snackbarOpen, setSnackbarOpen] = useState(false); const [snackbarMessage, setSnackbarMessage] = useState(''); const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error' | 'info' | 'warning'>('info'); // Mock data for badges (in real app, this would come from state/API) const [notificationCount, setNotificationCount] = useState(12); const [aiSuggestionsCount, setAISuggestionsCount] = useState(8); const [mlAlertsCount, setMLAlertsCount] = useState(3); const handleTabChange = (event: React.SyntheticEvent, newValue: number) => { setCurrentTab(newValue); }; const handleRefreshData = useCallback(async () => { setLoading(true); try { // Simulate API call await new Promise(resolve => setTimeout(resolve, 1500)); setLastRefresh(new Date()); setSnackbarMessage('Données administrateur mises à jour avec succès'); setSnackbarSeverity('success'); setSnackbarOpen(true); // Update badge counts (simulate real-time updates) setNotificationCount(prev => Math.max(0, prev + Math.floor(Math.random() * 5) - 2)); setAISuggestionsCount(prev => Math.max(0, prev + Math.floor(Math.random() * 3) - 1)); setMLAlertsCount(prev => Math.max(0, prev + Math.floor(Math.random() * 2) - 1)); } catch (error) { setSnackbarMessage('Erreur lors de la mise à jour des données'); setSnackbarSeverity('error'); setSnackbarOpen(true); } finally { setLoading(false); } }, []); const handleSettings = () => { setSnackbarMessage('Paramètres administrateur - Fonctionnalité à venir'); setSnackbarSeverity('info'); setSnackbarOpen(true); }; // Auto-refresh every 30 seconds useEffect(() => { const interval = setInterval(() => { handleRefreshData(); }, 30000); return () => clearInterval(interval); }, [handleRefreshData]); return ( <Container maxWidth="xl" sx={{ py: 3 }}> {/* Header */} <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4, p: 3, background: `linear-gradient(135deg, ${FREE_MOBILE_COLORS.PRIMARY} 0%, #cc0000 100%)`, borderRadius: 2, color: 'white' }}> <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}> <Avatar sx={{ bgcolor: 'white', color: FREE_MOBILE_COLORS.PRIMARY, width: 64, height: 64 }}> <AdminIcon fontSize="large" /> </Avatar> <Box> <Typography variant="h4" fontWeight="bold" sx={{ mb: 0.5 }}> Panneau Administrateur </Typography> <Typography variant="h6" sx={{ opacity: 0.9, mb: 0.5 }}> Gestion des Conversations </Typography> <Typography variant="body2" sx={{ opacity: 0.7 }}> Dernière mise à jour: {lastRefresh.toLocaleTimeString('fr-FR')} </Typography> </Box> </Box> <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}> <ButtonGroup variant="outlined" sx={{ borderColor: 'rgba(255,255,255,0.5)' }}> <Tooltip title="Actualiser les données"> <Button onClick={handleRefreshData} disabled={loading} sx={{ color: 'white', borderColor: 'rgba(255,255,255,0.5)', '&:hover': { borderColor: 'white', bgcolor: 'rgba(255,255,255,0.1)' } }} > {loading ? <CircularProgress size={20} color="inherit" /> : <RefreshIcon />} </Button> </Tooltip> <Tooltip title="Paramètres administrateur"> <Button onClick={handleSettings} sx={{ color: 'white', borderColor: 'rgba(255,255,255,0.5)', '&:hover': { borderColor: 'white', bgcolor: 'rgba(255,255,255,0.1)' } }} > <SettingsIcon /> </Button> </Tooltip> </ButtonGroup> </Box> </Box> {/* Main Content Card */} <Card elevation={3} sx={{ borderRadius: 2, overflow: 'hidden' }}> {/* Tab Navigation */} <Box sx={{ borderBottom: 1, borderColor: 'divider', bgcolor: 'grey.50' }}> <Tabs value={currentTab} onChange={handleTabChange} aria-label="administrator panel tabs" variant="fullWidth" sx={{ '& .MuiTab-root': { minHeight: 80, fontSize: '1rem', fontWeight: 'medium', textTransform: 'none', '&.Mui-selected': { color: FREE_MOBILE_COLORS.PRIMARY, fontWeight: 'bold', }, }, '& .MuiTabs-indicator': { backgroundColor: FREE_MOBILE_COLORS.PRIMARY, height: 3, }, }} > <Tab label="Notifications" icon={ <Badge badgeContent={notificationCount} color="error" max={99}> <NotificationsIcon /> </Badge> } iconPosition="top" id="admin-tab-0" aria-controls="admin-tabpanel-0" /> <Tab label="Suggestions IA Avancées" icon={ <Badge badgeContent={aiSuggestionsCount} color="primary" max={99}> <AIIcon /> </Badge> } iconPosition="top" id="admin-tab-1" aria-controls="admin-tabpanel-1" /> <Tab label="Tableau de Bord ML Intelligence" icon={ <Badge badgeContent={mlAlertsCount} color="warning" max={99}> <MLIcon /> </Badge> } iconPosition="top" id="admin-tab-2" aria-controls="admin-tabpanel-2" /> </Tabs> </Box> {/* Tab Content */} <CardContent sx={{ p: 0 }}> <TabPanel value={currentTab} index={0}> <Fade in={currentTab === 0} timeout={300}> <Box> <NotificationsTab notificationCount={notificationCount} onNotificationCountChange={setNotificationCount} loading={loading} /> </Box> </Fade> </TabPanel> <TabPanel value={currentTab} index={1}> <Fade in={currentTab === 1} timeout={300}> <Box> <AdvancedAISuggestionsTab suggestionsCount={aiSuggestionsCount} onSuggestionsCountChange={setAISuggestionsCount} loading={loading} /> </Box> </Fade> </TabPanel> <TabPanel value={currentTab} index={2}> <Fade in={currentTab === 2} timeout={300}> <Box> <MLIntelligenceDashboardTab alertsCount={mlAlertsCount} onAlertsCountChange={setMLAlertsCount} loading={loading} /> </Box> </Fade> </TabPanel> </CardContent> </Card> {/* Snackbar for notifications */} <Snackbar open={snackbarOpen} autoHideDuration={4000} onClose={() => setSnackbarOpen(false)} anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }} > <Alert onClose={() => setSnackbarOpen(false)} severity={snackbarSeverity} sx={{ borderRadius: 2 }} > {snackbarMessage} </Alert> </Snackbar> </Container> ); }; export default AdministratorPanel;