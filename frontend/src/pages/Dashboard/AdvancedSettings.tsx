/** * ============================================= * ADVANCED SYSTEM CONFIGURATION PAGE * Comprehensive system settings and AI configuration * Extends existing settings with advanced AI parameters * ============================================= */ import React, { useEffect, useState } from 'react'; import { useDispatch, useSelector } from 'react-redux'; import { Box, Container, Typography, Grid, Card, CardContent, CardHeader, Button, Switch, FormControlLabel, Slider, Select, MenuItem, FormControl, InputLabel, TextField, Tabs, Tab, Alert, Divider, Tooltip, IconButton, Breadcrumbs, Link, Paper, List, ListItem, ListItemText, ListItemIcon, ListItemSecondaryAction, Accordion, AccordionSummary, AccordionDetails } from '@mui/material'; import { Settings as SettingsIcon, Psychology as AIIcon, Security as SecurityIcon, Speed as PerformanceIcon, Hub as IntegrationIcon, Notifications as NotificationIcon, Save as SaveIcon, Restore as RestoreIcon, ExpandMore as ExpandMoreIcon, NavigateNext as NavigateNextIcon, Tune as TuneIcon, ModelTraining as ModelIcon, Api as ApiIcon, Storage as StorageIcon } from '@mui/icons-material'; import { RootState } from '../../store'; import { FREE_MOBILE_COLORS } from '../../utils/constants'; interface TabPanelProps { children?: React.ReactNode; index: number; value: number; } function TabPanel(props: TabPanelProps) { const { children, value, index, ...other } = props; return ( <div role="tabpanel" hidden={value !== index} id={`settings-tabpanel-${index}`} aria-labelledby={`settings-tab-${index}`} {...other} > {value === index && ( <Box sx={{ p: 3 }}> {children} </Box> )} </div> ); } const AdvancedSettings: React.FC = () => { const dispatch = useDispatch(); const [activeTab, setActiveTab] = useState(0); const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false); // AI Settings State const [aiSettings, setAISettings] = useState({ gpt_temperature: 0.7, confidence_threshold: 0.8, max_tokens: 150, response_timeout: 5000, enable_learning: true, enable_personalization: true, sentiment_sensitivity: 0.6, escalation_threshold: 0.7 }); // Performance Settings State const [performanceSettings, setPerformanceSettings] = useState({ cache_duration: 300, max_concurrent_requests: 100, request_timeout: 30000, enable_compression: true, enable_cdn: true, auto_scaling: true, memory_limit: 512, cpu_limit: 80 }); // Integration Settings State const [integrationSettings, setIntegrationSettings] = useState({ whatsapp_enabled: true, facebook_enabled: true, instagram_enabled: true, twitter_enabled: true, webhook_timeout: 10000, retry_attempts: 3, rate_limit: 1000, enable_analytics: true }); // Security Settings State const [securitySettings, setSecuritySettings] = useState({ session_timeout: 3600, max_login_attempts: 5, password_expiry: 90, enable_2fa: true, enable_audit_log: true, data_retention_days: 365, encryption_level: 'AES-256', enable_ip_whitelist: false }); useEffect(() => { // Load current settings from backend loadSettings(); }, []); const loadSettings = async () => { try { // This would load settings from the backend console.log('Loading settings...'); } catch (error) { console.error('Failed to load settings:', error); } }; const handleTabChange = (event: React.SyntheticEvent, newValue: number) => { setActiveTab(newValue); }; const handleAISettingChange = (setting: string, value: any) => { setAISettings(prev => ({ ...prev, [setting]: value })); setHasUnsavedChanges(true); }; const handlePerformanceSettingChange = (setting: string, value: any) => { setPerformanceSettings(prev => ({ ...prev, [setting]: value })); setHasUnsavedChanges(true); }; const handleIntegrationSettingChange = (setting: string, value: any) => { setIntegrationSettings(prev => ({ ...prev, [setting]: value })); setHasUnsavedChanges(true); }; const handleSecuritySettingChange = (setting: string, value: any) => { setSecuritySettings(prev => ({ ...prev, [setting]: value })); setHasUnsavedChanges(true); }; const saveSettings = async () => { try { // Save all settings to backend const allSettings = { ai: aiSettings, performance: performanceSettings, integration: integrationSettings, security: securitySettings }; console.log('Saving settings:', allSettings); setHasUnsavedChanges(false); // Show success message } catch (error) { console.error('Failed to save settings:', error); } }; const resetToDefaults = () => { // Reset all settings to defaults setHasUnsavedChanges(true); }; return ( <Container maxWidth={false} sx={{ py: 3 }}> {/* Header */} <Box sx={{ mb: 4 }}> <Breadcrumbs separator={<NavigateNextIcon fontSize="small" />} sx={{ mb: 2 }}> <Link color="inherit" href="/dashboard"> Dashboard </Link> <Link color="inherit" href="/dashboard/admin"> Administration </Link> <Typography color="text.primary">Configuration Avancée</Typography> </Breadcrumbs> <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}> <Box> <Typography variant="h4" component="h1" fontWeight="bold" color={FREE_MOBILE_COLORS.PRIMARY}> Configuration Avancée du Système </Typography> <Typography variant="subtitle1" color="text.secondary"> Paramètres avancés pour l'IA, les performances et les intégrations </Typography> </Box> <Box sx={{ display: 'flex', gap: 1 }}> <Button variant="outlined" startIcon={<RestoreIcon />} onClick={resetToDefaults} > Réinitialiser </Button> <Button variant="contained" startIcon={<SaveIcon />} onClick={saveSettings} disabled={!hasUnsavedChanges} > Sauvegarder </Button> </Box> </Box> {hasUnsavedChanges && ( <Alert severity="warning" sx={{ mb: 3 }}> Vous avez des modifications non sauvegardées. N'oubliez pas de sauvegarder vos changements. </Alert> )} </Box> {/* Main Content Tabs */} <Card> <Box sx={{ borderBottom: 1, borderColor: 'divider' }}> <Tabs value={activeTab} onChange={handleTabChange} aria-label="advanced settings tabs"> <Tab label="Intelligence Artificielle" icon={<AIIcon />} iconPosition="start" /> <Tab label="Performance & Optimisation" icon={<PerformanceIcon />} iconPosition="start" /> <Tab label="Intégrations" icon={<IntegrationIcon />} iconPosition="start" /> <Tab label="Sécurité & Conformité" icon={<SecurityIcon />} iconPosition="start" /> </Tabs> </Box> {/* AI Settings Tab */} <TabPanel value={activeTab} index={0}> <Typography variant="h6" sx={{ mb: 3 }}> Paramètres de l'Intelligence Artificielle </Typography> <Grid container spacing={3}> <Grid item xs={12} md={6}> <Accordion defaultExpanded> <AccordionSummary expandIcon={<ExpandMoreIcon />}> <Typography variant="subtitle1" fontWeight="bold"> <ModelIcon sx={{ mr: 1, verticalAlign: 'middle' }} /> Modèles GPT </Typography> </AccordionSummary> <AccordionDetails> <Box sx={{ mb: 3 }}> <Typography variant="body2" sx={{ mb: 2 }}> Température: {aiSettings.gpt_temperature} </Typography> <Slider value={aiSettings.gpt_temperature} onChange={(e, value) => handleAISettingChange('gpt_temperature', value)} min={0.1} max={2.0} step={0.1} marks valueLabelDisplay="auto" /> </Box> <Box sx={{ mb: 3 }}> <Typography variant="body2" sx={{ mb: 2 }}> Seuil de Confiance: {Math.round(aiSettings.confidence_threshold * 100)}% </Typography> <Slider value={aiSettings.confidence_threshold} onChange={(e, value) => handleAISettingChange('confidence_threshold', value)} min={0.5} max={1.0} step={0.05} marks valueLabelDisplay="auto" valueLabelFormat={(value) => `${Math.round(value * 100)}%`} /> </Box> <TextField fullWidth label="Tokens Maximum" type="number" value={aiSettings.max_tokens} onChange={(e) => handleAISettingChange('max_tokens', parseInt(e.target.value))} sx={{ mb: 2 }} /> <TextField fullWidth label="Timeout Réponse (ms)" type="number" value={aiSettings.response_timeout} onChange={(e) => handleAISettingChange('response_timeout', parseInt(e.target.value))} /> </AccordionDetails> </Accordion> </Grid> <Grid item xs={12} md={6}> <Accordion defaultExpanded> <AccordionSummary expandIcon={<ExpandMoreIcon />}> <Typography variant="subtitle1" fontWeight="bold"> <TuneIcon sx={{ mr: 1, verticalAlign: 'middle' }} /> Apprentissage & Personnalisation </Typography> </AccordionSummary> <AccordionDetails> <FormControlLabel control={ <Switch checked={aiSettings.enable_learning} onChange={(e) => handleAISettingChange('enable_learning', e.target.checked)} /> } label="Apprentissage Automatique" sx={{ mb: 2 }} /> <FormControlLabel control={ <Switch checked={aiSettings.enable_personalization} onChange={(e) => handleAISettingChange('enable_personalization', e.target.checked)} /> } label="Personnalisation des Réponses" sx={{ mb: 3 }} /> <Box sx={{ mb: 3 }}> <Typography variant="body2" sx={{ mb: 2 }}> Sensibilité Sentiment: {Math.round(aiSettings.sentiment_sensitivity * 100)}% </Typography> <Slider value={aiSettings.sentiment_sensitivity} onChange={(e, value) => handleAISettingChange('sentiment_sensitivity', value)} min={0.1} max={1.0} step={0.1} marks valueLabelDisplay="auto" valueLabelFormat={(value) => `${Math.round(value * 100)}%`} /> </Box> <Box> <Typography variant="body2" sx={{ mb: 2 }}> Seuil d'Escalade: {Math.round(aiSettings.escalation_threshold * 100)}% </Typography> <Slider value={aiSettings.escalation_threshold} onChange={(e, value) => handleAISettingChange('escalation_threshold', value)} min={0.3} max={1.0} step={0.05} marks valueLabelDisplay="auto" valueLabelFormat={(value) => `${Math.round(value * 100)}%`} /> </Box> </AccordionDetails> </Accordion> </Grid> </Grid> </TabPanel> {/* Performance Settings Tab */} <TabPanel value={activeTab} index={1}> <Typography variant="h6" sx={{ mb: 3 }}> Performance & Optimisation du Système </Typography> <Grid container spacing={3}> <Grid item xs={12} md={6}> <Accordion defaultExpanded> <AccordionSummary expandIcon={<ExpandMoreIcon />}> <Typography variant="subtitle1" fontWeight="bold"> <StorageIcon sx={{ mr: 1, verticalAlign: 'middle' }} /> Cache & Stockage </Typography> </AccordionSummary> <AccordionDetails> <TextField fullWidth label="Durée Cache (secondes)" type="number" value={performanceSettings.cache_duration} onChange={(e) => handlePerformanceSettingChange('cache_duration', parseInt(e.target.value))} sx={{ mb: 2 }} /> <FormControlLabel control={ <Switch checked={performanceSettings.enable_compression} onChange={(e) => handlePerformanceSettingChange('enable_compression', e.target.checked)} /> } label="Compression Activée" sx={{ mb: 2 }} /> <FormControlLabel control={ <Switch checked={performanceSettings.enable_cdn} onChange={(e) => handlePerformanceSettingChange('enable_cdn', e.target.checked)} /> } label="CDN Activé" /> </AccordionDetails> </Accordion> </Grid> <Grid item xs={12} md={6}> <Accordion defaultExpanded> <AccordionSummary expandIcon={<ExpandMoreIcon />}> <Typography variant="subtitle1" fontWeight="bold"> <PerformanceIcon sx={{ mr: 1, verticalAlign: 'middle' }} /> Limites & Ressources </Typography> </AccordionSummary> <AccordionDetails> <TextField fullWidth label="Requêtes Concurrentes Max" type="number" value={performanceSettings.max_concurrent_requests} onChange={(e) => handlePerformanceSettingChange('max_concurrent_requests', parseInt(e.target.value))} sx={{ mb: 2 }} /> <TextField fullWidth label="Timeout Requête (ms)" type="number" value={performanceSettings.request_timeout} onChange={(e) => handlePerformanceSettingChange('request_timeout', parseInt(e.target.value))} sx={{ mb: 2 }} /> <Box sx={{ mb: 2 }}> <Typography variant="body2" sx={{ mb: 1 }}> Limite Mémoire: {performanceSettings.memory_limit} MB </Typography> <Slider value={performanceSettings.memory_limit} onChange={(e, value) => handlePerformanceSettingChange('memory_limit', value)} min={256} max={2048} step={128} marks valueLabelDisplay="auto" /> </Box> <Box> <Typography variant="body2" sx={{ mb: 1 }}> Limite CPU: {performanceSettings.cpu_limit}% </Typography> <Slider value={performanceSettings.cpu_limit} onChange={(e, value) => handlePerformanceSettingChange('cpu_limit', value)} min={50} max={100} step={5} marks valueLabelDisplay="auto" /> </Box> </AccordionDetails> </Accordion> </Grid> </Grid> </TabPanel> {/* Integration Settings Tab */} <TabPanel value={activeTab} index={2}> <Typography variant="h6" sx={{ mb: 3 }}> Configuration des Intégrations </Typography> <Grid container spacing={3}> <Grid item xs={12} md={6}> <Card> <CardHeader title="Plateformes Sociales" /> <CardContent> <FormControlLabel control={ <Switch checked={integrationSettings.whatsapp_enabled} onChange={(e) => handleIntegrationSettingChange('whatsapp_enabled', e.target.checked)} /> } label="WhatsApp" sx={{ mb: 1 }} /> <FormControlLabel control={ <Switch checked={integrationSettings.facebook_enabled} onChange={(e) => handleIntegrationSettingChange('facebook_enabled', e.target.checked)} /> } label="Facebook Messenger" sx={{ mb: 1 }} /> <FormControlLabel control={ <Switch checked={integrationSettings.instagram_enabled} onChange={(e) => handleIntegrationSettingChange('instagram_enabled', e.target.checked)} /> } label="Instagram" sx={{ mb: 1 }} /> <FormControlLabel control={ <Switch checked={integrationSettings.twitter_enabled} onChange={(e) => handleIntegrationSettingChange('twitter_enabled', e.target.checked)} /> } label="Twitter" /> </CardContent> </Card> </Grid> <Grid item xs={12} md={6}> <Card> <CardHeader title="Paramètres API" /> <CardContent> <TextField fullWidth label="Timeout Webhook (ms)" type="number" value={integrationSettings.webhook_timeout} onChange={(e) => handleIntegrationSettingChange('webhook_timeout', parseInt(e.target.value))} sx={{ mb: 2 }} /> <TextField fullWidth label="Tentatives de Retry" type="number" value={integrationSettings.retry_attempts} onChange={(e) => handleIntegrationSettingChange('retry_attempts', parseInt(e.target.value))} sx={{ mb: 2 }} /> <TextField fullWidth label="Limite de Taux (req/min)" type="number" value={integrationSettings.rate_limit} onChange={(e) => handleIntegrationSettingChange('rate_limit', parseInt(e.target.value))} sx={{ mb: 2 }} /> <FormControlLabel control={ <Switch checked={integrationSettings.enable_analytics} onChange={(e) => handleIntegrationSettingChange('enable_analytics', e.target.checked)} /> } label="Analytics Activées" /> </CardContent> </Card> </Grid> </Grid> </TabPanel> {/* Security Settings Tab */} <TabPanel value={activeTab} index={3}> <Typography variant="h6" sx={{ mb: 3 }}> Sécurité & Conformité </Typography> <Grid container spacing={3}> <Grid item xs={12} md={6}> <Card> <CardHeader title="Authentification & Sessions" /> <CardContent> <TextField fullWidth label="Timeout Session (secondes)" type="number" value={securitySettings.session_timeout} onChange={(e) => handleSecuritySettingChange('session_timeout', parseInt(e.target.value))} sx={{ mb: 2 }} /> <TextField fullWidth label="Tentatives de Connexion Max" type="number" value={securitySettings.max_login_attempts} onChange={(e) => handleSecuritySettingChange('max_login_attempts', parseInt(e.target.value))} sx={{ mb: 2 }} /> <TextField fullWidth label="Expiration Mot de Passe (jours)" type="number" value={securitySettings.password_expiry} onChange={(e) => handleSecuritySettingChange('password_expiry', parseInt(e.target.value))} sx={{ mb: 2 }} /> <FormControlLabel control={ <Switch checked={securitySettings.enable_2fa} onChange={(e) => handleSecuritySettingChange('enable_2fa', e.target.checked)} /> } label="Authentification à 2 Facteurs" /> </CardContent> </Card> </Grid> <Grid item xs={12} md={6}> <Card> <CardHeader title="Données & Conformité" /> <CardContent> <TextField fullWidth label="Rétention Données (jours)" type="number" value={securitySettings.data_retention_days} onChange={(e) => handleSecuritySettingChange('data_retention_days', parseInt(e.target.value))} sx={{ mb: 2 }} /> <FormControl fullWidth sx={{ mb: 2 }}> <InputLabel>Niveau de Chiffrement</InputLabel> <Select value={securitySettings.encryption_level} label="Niveau de Chiffrement" onChange={(e) => handleSecuritySettingChange('encryption_level', e.target.value)} > <MenuItem value="AES-128">AES-128</MenuItem> <MenuItem value="AES-256">AES-256</MenuItem> <MenuItem value="RSA-2048">RSA-2048</MenuItem> </Select> </FormControl> <FormControlLabel control={ <Switch checked={securitySettings.enable_audit_log} onChange={(e) => handleSecuritySettingChange('enable_audit_log', e.target.checked)} /> } label="Journal d'Audit" sx={{ mb: 1 }} /> <FormControlLabel control={ <Switch checked={securitySettings.enable_ip_whitelist} onChange={(e) => handleSecuritySettingChange('enable_ip_whitelist', e.target.checked)} /> } label="Liste Blanche IP" /> </CardContent> </Card> </Grid> </Grid> </TabPanel> </Card> </Container> ); }; export default AdvancedSettings;