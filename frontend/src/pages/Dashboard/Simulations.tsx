/** * ============================================= * AGENT TRAINING SIMULATIONS PAGE * Interactive training environment for customer service agents * Integrates with existing AI services for realistic scenarios * ============================================= */ import React, { useEffect, useState } from 'react'; import { useAppDispatch, useAppSelector } from '../../hooks/redux'; import { Box, Container, Typography, Grid, Card, CardContent, Button, Chip, LinearProgress, Alert, Fab, Dialog, DialogTitle, DialogContent, DialogActions, Tabs, Tab, Badge, Tooltip, IconButton, Breadcrumbs, Link } from '@mui/material'; import { PlayArrow as StartIcon, Pause as PauseIcon, Stop as StopIcon, Psychology as AIIcon, Assessment as MetricsIcon, EmojiEvents as TrophyIcon, Settings as SettingsIcon, Refresh as RefreshIcon, School as TrainingIcon, Speed as SpeedIcon, PlayArrow, NavigateNext as NavigateNextIcon } from '@mui/icons-material'; import { RootState } from '../../store'; import { fetchScenarios, startSimulation, endSimulation, fetchAgentProgress, setSelectedScenario, toggleAIPanel, togglePerformancePanel, setSimulationSpeed, selectSimulationState, selectCurrentSession, selectScenarios, selectAgentProgress, selectIsSimulationActive } from '../../store/slices/simulationSlice'; import { FREE_MOBILE_COLORS } from '../../utils/constants'; // Import simulation components (to be created) import ScenarioSelector from '../../components/Simulation/ScenarioSelector'; import SimulationInterface from '../../components/Simulation/SimulationInterface'; import PerformancePanel from '../../components/Simulation/PerformancePanel'; import AIAssistantPanel from '../../components/Simulation/AIAssistantPanel'; import ProgressTracker from '../../components/Simulation/ProgressTracker'; interface TabPanelProps { children?: React.ReactNode; index: number; value: number; } function TabPanel(props: TabPanelProps) { const { children, value, index, ...other } = props; return ( <div role="tabpanel" hidden={value !== index} id={`simulation-tabpanel-${index}`} aria-labelledby={`simulation-tab-${index}`} {...other} > {value === index && ( <Box sx={{ p: 3 }}> {children} </Box> )} </div> ); } const Simulations: React.FC = () => { const dispatch = useAppDispatch(); const simulationState = useAppSelector(selectSimulationState); const currentSession = useAppSelector(selectCurrentSession); const scenarios = useAppSelector(selectScenarios); const agentProgress = useAppSelector(selectAgentProgress); const isSimulationActive = useAppSelector(selectIsSimulationActive); const [activeTab, setActiveTab] = useState(0); const [settingsOpen, setSettingsOpen] = useState(false); const [selectedDifficulty, setSelectedDifficulty] = useState<string>('all'); const [selectedCategory, setSelectedCategory] = useState<string>('all'); useEffect(() => { // Initialize simulation data dispatch(fetchScenarios()); dispatch(fetchAgentProgress('current-agent')); // This would get current agent ID }, [dispatch]); const handleTabChange = (event: React.SyntheticEvent, newValue: number) => { setActiveTab(newValue); }; const handleStartSimulation = async (scenarioId: string) => { try { await dispatch(startSimulation(scenarioId)); setActiveTab(1); // Switch to simulation interface tab } catch (error) { console.error('Failed to start simulation:', error); } }; const handleEndSimulation = async () => { if (currentSession) { try { await dispatch(endSimulation(currentSession.id)); setActiveTab(2); // Switch to results tab } catch (error) { console.error('Failed to end simulation:', error); } } }; const handleScenarioFilter = (difficulty: string, category: string) => { setSelectedDifficulty(difficulty); setSelectedCategory(category); // Apply filters const filters = { difficulty: difficulty !== 'all' ? [difficulty] : [], category: category !== 'all' ? [category] : [], tags: [] }; // This would trigger filtering in the component }; const getDifficultyColor = (difficulty: string) => { switch (difficulty) { case 'beginner': return 'success'; case 'intermediate': return 'warning'; case 'expert': return 'error'; default: return 'default'; } }; const getProgressColor = (score: number) => { if (score >= 80) return 'success'; if (score >= 60) return 'warning'; return 'error'; }; return ( <Container maxWidth={false} sx={{ py: 3 }}> {/* Header */} <Box sx={{ mb: 4 }}> <Breadcrumbs separator={<NavigateNextIcon fontSize="small" />} sx={{ mb: 2 }}> <Link color="inherit" href="/dashboard"> Dashboard </Link> <Link color="inherit" href="/dashboard/admin"> Administration </Link> <Typography color="text.primary">Simulations d'Entraînement</Typography> </Breadcrumbs> <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}> <Box> <Typography variant="h4" component="h1" fontWeight="bold" color={FREE_MOBILE_COLORS.PRIMARY}> Simulations d'Entraînement Agent </Typography> <Typography variant="subtitle1" color="text.secondary"> Environnement d'entraînement interactif avec IA pour améliorer les compétences des agents </Typography> </Box> <Box sx={{ display: 'flex', gap: 1 }}> <Tooltip title="Actualiser les données"> <IconButton onClick={() => dispatch(fetchScenarios())} disabled={simulationState?.loading?.scenarios || false} > <RefreshIcon /> </IconButton> </Tooltip> <Tooltip title="Paramètres de simulation"> <IconButton onClick={() => setSettingsOpen(true)}> <SettingsIcon /> </IconButton> </Tooltip> {isSimulationActive && ( <Button variant="contained" color="error" startIcon={<StopIcon />} onClick={handleEndSimulation} sx={{ ml: 2 }} > Terminer Simulation </Button> )} </Box> </Box> {/* Quick Stats */} <Grid container spacing={2} sx={{ mb: 3 }}> <Grid item xs={12} sm={6} md={3}> <Card> <CardContent sx={{ textAlign: 'center' }}> <Typography variant="h3" color="primary" fontWeight="bold"> {agentProgress.completed_sessions} </Typography> <Typography variant="body2" color="text.secondary"> Sessions Complétées </Typography> </CardContent> </Card> </Grid> <Grid item xs={12} sm={6} md={3}> <Card> <CardContent sx={{ textAlign: 'center' }}> <Typography variant="h3" color="success.main" fontWeight="bold"> {Math.round(agentProgress.average_score)}% </Typography> <Typography variant="body2" color="text.secondary"> Score Moyen </Typography> </CardContent> </Card> </Grid> <Grid item xs={12} sm={6} md={3}> <Card> <CardContent sx={{ textAlign: 'center' }}> <Typography variant="h3" color="warning.main" fontWeight="bold"> {agentProgress.badges_earned.length} </Typography> <Typography variant="body2" color="text.secondary"> Badges Obtenus </Typography> </CardContent> </Card> </Grid> <Grid item xs={12} sm={6} md={3}> <Card> <CardContent sx={{ textAlign: 'center' }}> <Typography variant="h3" color="info.main" fontWeight="bold"> {scenarios.length} </Typography> <Typography variant="body2" color="text.secondary"> Scénarios Disponibles </Typography> </CardContent> </Card> </Grid> </Grid> {/* Current Session Status */} {isSimulationActive && currentSession && ( <Alert severity="info" sx={{ mb: 3 }} action={ <Box sx={{ display: 'flex', gap: 1 }}> <Chip label={`Score: ${Math.round(currentSession.performance_metrics.overall_score)}%`} color={getProgressColor(currentSession.performance_metrics.overall_score)} size="small" /> <Chip label={`Temps: ${Math.round(currentSession.performance_metrics.resolution_time)}min`} color="default" size="small" /> </Box> } > <Typography variant="body1"> <strong>Simulation en cours:</strong> {currentSession.scenario_id} </Typography> <LinearProgress variant="determinate" value={currentSession.performance_metrics.overall_score} sx={{ mt: 1 }} /> </Alert> )} </Box> {/* Main Content Tabs */} <Card> <Box sx={{ borderBottom: 1, borderColor: 'divider' }}> <Tabs value={activeTab} onChange={handleTabChange} aria-label="simulation tabs"> <Tab label="Sélection de Scénario" icon={<TrainingIcon />} iconPosition="start" disabled={isSimulationActive} /> <Tab label="Interface de Simulation" icon={<PlayArrow />} iconPosition="start" disabled={!isSimulationActive} /> <Tab label="Progression & Résultats" icon={<MetricsIcon />} iconPosition="start" /> <Tab label="Classement" icon={<TrophyIcon />} iconPosition="start" /> </Tabs> </Box> {/* Tab Panels */} <TabPanel value={activeTab} index={0}> <ScenarioSelector scenarios={scenarios} onStartSimulation={handleStartSimulation} onFilterChange={handleScenarioFilter} selectedDifficulty={selectedDifficulty} selectedCategory={selectedCategory} loading={simulationState?.loading?.scenarios || false} /> </TabPanel> <TabPanel value={activeTab} index={1}> {isSimulationActive && currentSession ? ( <SimulationInterface session={currentSession} onEndSimulation={handleEndSimulation} /> ) : ( <Box sx={{ textAlign: 'center', py: 8 }}> <Typography variant="h6" color="text.secondary"> Aucune simulation active </Typography> <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}> Sélectionnez un scénario pour commencer l'entraînement </Typography> <Button variant="outlined" onClick={() => setActiveTab(0)} startIcon={<TrainingIcon />} > Choisir un Scénario </Button> </Box> )} </TabPanel> <TabPanel value={activeTab} index={2}> <ProgressTracker agentProgress={agentProgress} completedSessions={simulationState?.completed_sessions || []} /> </TabPanel> <TabPanel value={activeTab} index={3}> <PerformancePanel leaderboard={simulationState?.leaderboard || []} currentAgent={agentProgress} /> </TabPanel> </Card> {/* Floating Action Buttons */} {isSimulationActive && ( <Box sx={{ position: 'fixed', bottom: 24, right: 24, display: 'flex', flexDirection: 'column', gap: 1 }}> <Tooltip title="Assistant IA" placement="left"> <Fab color={simulationState?.show_ai_panel ? 'primary' : 'default'} onClick={() => dispatch(toggleAIPanel())} size="medium" > <Badge badgeContent={simulationState?.ai_suggestions?.length || 0} color="error"> <AIIcon /> </Badge> </Fab> </Tooltip> <Tooltip title="Métriques de Performance" placement="left"> <Fab color={simulationState?.show_performance_panel ? 'primary' : 'default'} onClick={() => dispatch(togglePerformancePanel())} size="medium" > <MetricsIcon /> </Fab> </Tooltip> <Tooltip title="Vitesse de Simulation" placement="left"> <Fab color="secondary" onClick={() => { const speeds = ['slow', 'normal', 'fast'] as const; const currentIndex = speeds.indexOf(simulationState?.simulation_speed || 'normal'); const nextSpeed = speeds[(currentIndex + 1) % speeds.length]; dispatch(setSimulationSpeed(nextSpeed)); }} size="small" > <SpeedIcon /> </Fab> </Tooltip> </Box> )} {/* AI Assistant Panel */} {simulationState?.show_ai_panel && isSimulationActive && ( <AIAssistantPanel suggestions={simulationState?.ai_suggestions || []} feedback={simulationState?.real_time_feedback || []} onClose={() => dispatch(toggleAIPanel())} /> )} {/* Settings Dialog */} <Dialog open={settingsOpen} onClose={() => setSettingsOpen(false)} maxWidth="md" fullWidth> <DialogTitle>Paramètres de Simulation</DialogTitle> <DialogContent> <Typography variant="body1" sx={{ mb: 2 }}> Configurez les paramètres de simulation selon vos préférences d'entraînement. </Typography> {/* Settings content would go here */} </DialogContent> <DialogActions> <Button onClick={() => setSettingsOpen(false)}>Annuler</Button> <Button variant="contained" onClick={() => setSettingsOpen(false)}> Sauvegarder </Button> </DialogActions> </Dialog> </Container> ); }; export default Simulations;