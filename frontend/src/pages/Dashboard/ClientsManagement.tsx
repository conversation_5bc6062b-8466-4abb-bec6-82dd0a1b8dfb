import React from 'react'; import { Box, Grid, Typography, } from '@mui/material'; import ClientRequestsTable from '../../components/Dashboard/ClientRequestsTable'; import ClientProfilePanel from '../../components/Dashboard/ClientProfilePanel'; const ClientsManagement: React.FC = () => { return ( <Box> {/* Header */} <Typography variant="h4" gutterBottom fontWeight="bold"> Gestion des Clients </Typography> <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}> Gérez les demandes clients et consultez les profils détaillés </Typography> <Grid container spacing={3}> {/* Client Requests Table */} <Grid item xs={12} lg={8}> <ClientRequestsTable /> </Grid> {/* Client Profile Panel */} <Grid item xs={12} lg={4}> <ClientProfilePanel /> </Grid> </Grid> </Box> ); }; export default ClientsManagement;