import React, { useState, useEffect, useCallback } from 'react'; import { Box, Grid, Typography, Card, CardContent, CardHeader, FormControl, InputLabel, Select, MenuItem, Paper, List, ListItem, ListItemText, ListItemIcon, Chip, Button, ButtonGroup, IconButton, Tooltip, Alert, Snackbar, Fade, Avatar, Divider, CircularProgress, } from '@mui/material'; import { Assignment as TicketIcon, Star as SatisfactionIcon, Schedule as TimeIcon, TrendingUp as TrendingIcon, Lightbulb as IdeaIcon, Psychology as AIIcon, Analytics as AnalyticsIcon, Refresh as RefreshIcon, Download as DownloadIcon, FilterList as FilterIcon, DateRange as DateRangeIcon, Assessment as AssessmentIcon, } from '@mui/icons-material'; import KPICard from '../../components/Dashboard/Analytics/KPICard'; import { VolumeChart, CategoryDistributionChart, PerformanceTrendsChart } from '../../components/Dashboard/Analytics'; import { FREE_MOBILE_COLORS } from '../../utils/constants'; const AnalyticsDashboard: React.FC = () => { const [timeRange, setTimeRange] = useState('30d'); const [categoryFilter, setCategoryFilter] = useState('all'); const [loading, setLoading] = useState(false); const [error, setError] = useState<string | null>(null); const [lastRefresh, setLastRefresh] = useState(new Date()); const [snackbarOpen, setSnackbarOpen] = useState(false); const [snackbarMessage, setSnackbarMessage] = useState(''); const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error' | 'info'>('info'); const [autoRefresh, setAutoRefresh] = useState(false); // Auto-refresh functionality useEffect(() => { let interval: NodeJS.Timeout; if (autoRefresh) { interval = setInterval(() => { handleRefreshData(); }, 30000); // Refresh every 30 seconds } return () => { if (interval) { clearInterval(interval); } }; }, [autoRefresh]); const handleRefreshData = useCallback(async () => { setLoading(true); setError(null); try { // Simulate API call await new Promise(resolve => setTimeout(resolve, 1000)); setLastRefresh(new Date()); setSnackbarMessage('Données mises à jour avec succès'); setSnackbarSeverity('success'); setSnackbarOpen(true); } catch (err) { setError('Erreur lors de la mise à jour des données'); setSnackbarMessage('Erreur lors de la mise à jour'); setSnackbarSeverity('error'); setSnackbarOpen(true); } finally { setLoading(false); } }, []); const handleExportData = useCallback((format: 'png' | 'pdf' | 'csv') => { setSnackbarMessage(`Export ${format.toUpperCase()} en cours...`); setSnackbarSeverity('info'); setSnackbarOpen(true); }, []); const handleTimeRangeChange = useCallback((newRange: string) => { setTimeRange(newRange); handleRefreshData(); }, [handleRefreshData]); const kpiData = React.useMemo(() => { // Generate dynamic KPI data based on time range const baseValues = { '7d': { tickets: 287, satisfaction: 4.1, responseTime: 9.2, resolution: 85.1 }, '30d': { tickets: 1247, satisfaction: 4.2, responseTime: 8.5, resolution: 87.3 }, '90d': { tickets: 3891, satisfaction: 4.0, responseTime: 9.8, resolution: 84.7 }, '1y': { tickets: 15234, satisfaction: 4.1, responseTime: 10.2, resolution: 86.2 }, }; const values = baseValues[timeRange as keyof typeof baseValues] || baseValues['30d']; return [ { title: `Volume des tickets (${timeRange === '7d' ? '7 jours' : timeRange === '30d' ? '30 jours' : timeRange === '90d' ? '3 mois' : '1 an'})`, value: values.tickets.toLocaleString('fr-FR'), change: '+12%', trend: 'up' as const, icon: <TicketIcon />, color: FREE_MOBILE_COLORS.PRIMARY, subtitle: 'Total des demandes', loading: loading, }, { title: 'Satisfaction client', value: `${values.satisfaction.toFixed(1)}/5`, change: '+0.3', trend: 'up' as const, icon: <SatisfactionIcon />, color: '#10b981', subtitle: 'Note moyenne', loading: loading, }, { title: 'Temps de réponse moyen', value: `${values.responseTime.toFixed(1)} min`, change: '-2.1 min', trend: 'up' as const, icon: <TimeIcon />, color: '#f59e0b', subtitle: 'Première réponse', loading: loading, }, { title: 'Taux de résolution', value: `${values.resolution.toFixed(1)}%`, change: '****%', trend: 'up' as const, icon: <TrendingIcon />, color: '#8b5cf6', subtitle: 'Premier contact', loading: loading, }, ]; }, [timeRange, loading]); const aiInsights = [ { insight: "Les tickets techniques augmentent de 15% le lundi matin", impact: "Élevé", confidence: 87, }, { insight: "Temps de réponse optimal entre 9h-11h", impact: "Moyen", confidence: 92, }, { insight: "Satisfaction client corrélée aux temps de réponse", impact: "Élevé", confidence: 95, }, { insight: "Pic d'activité prévu demain après-midi", impact: "Moyen", confidence: 78, }, ]; const getImpactColor = (impact: string) => { switch (impact.toLowerCase()) { case 'élevé': case 'high': return 'error'; case 'moyen': case 'medium': return 'warning'; case 'faible': case 'low': return 'success'; default: return 'default'; } }; const categoryDistribution = [ { category: 'Technique', count: 456, percentage: 36.6, color: FREE_MOBILE_COLORS.PRIMARY }, { category: 'Facturation', count: 312, percentage: 25.0, color: FREE_MOBILE_COLORS.SUCCESS }, { category: 'Réseau', count: 289, percentage: 23.2, color: FREE_MOBILE_COLORS.INFO }, { category: 'Abonnement', count: 190, percentage: 15.2, color: FREE_MOBILE_COLORS.WARNING }, ]; const topReasons = [ { reason: 'Problème de connexion WiFi', count: 89, trend: 'up' }, { reason: 'Question sur la facture', count: 67, trend: 'stable' }, { reason: 'Changement de forfait', count: 54, trend: 'down' }, { reason: 'Problème de réseau mobile', count: 43, trend: 'up' }, { reason: 'Configuration email', count: 38, trend: 'stable' }, ]; const aiSuggestions = [ { title: 'Optimiser les réponses automatiques', description: 'Améliorer le taux de résolution de 15% en enrichissant la base de connaissances', impact: 'Élevé', effort: 'Moyen', }, { title: 'Formation agents sur WiFi', description: 'Réduire le temps de résolution des problèmes de connexion de 30%', impact: 'Moyen', effort: 'Faible', }, { title: 'Chatbot proactif facturation', description: 'Anticiper les questions de facturation avant l\'échéance', impact: 'Élevé', effort: 'Élevé', }, ]; return ( <Box sx={{ maxWidth: 1400, mx: 'auto', p: 2 }}> {/* Header */} <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4, p: 3, background: `linear-gradient(135deg, ${FREE_MOBILE_COLORS.PRIMARY} 0%, #cc0000 100%)`, borderRadius: 2, color: 'white' }}> <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}> <Avatar sx={{ bgcolor: 'white', color: FREE_MOBILE_COLORS.PRIMARY, width: 56, height: 56 }}> <AnalyticsIcon fontSize="large" /> </Avatar> <Box> <Typography variant="h4" gutterBottom fontWeight="bold" sx={{ mb: 0.5 }}> Analytics Dashboard </Typography> <Typography variant="body1" sx={{ opacity: 0.9 }}> Analyse des performances et insights du support client </Typography> <Typography variant="body2" sx={{ opacity: 0.7, mt: 0.5 }}> Dernière mise à jour: {lastRefresh.toLocaleTimeString('fr-FR')} </Typography> </Box> </Box> <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}> <FormControl size="small" sx={{ minWidth: 150 }}> <InputLabel sx={{ color: 'white', '&.Mui-focused': { color: 'white' } }}>Période</InputLabel> <Select value={timeRange} onChange={(e) => handleTimeRangeChange(e.target.value)} label="Période" sx={{ color: 'white', '.MuiOutlinedInput-notchedOutline': { borderColor: 'rgba(255,255,255,0.5)' }, '&:hover .MuiOutlinedInput-notchedOutline': { borderColor: 'white' }, '&.Mui-focused .MuiOutlinedInput-notchedOutline': { borderColor: 'white' }, '.MuiSvgIcon-root': { color: 'white' } }} > <MenuItem value="7d">7 derniers jours</MenuItem> <MenuItem value="30d">30 derniers jours</MenuItem> <MenuItem value="90d">3 derniers mois</MenuItem> <MenuItem value="1y">Dernière année</MenuItem> </Select> </FormControl> <FormControl size="small" sx={{ minWidth: 150 }}> <InputLabel sx={{ color: 'white', '&.Mui-focused': { color: 'white' } }}>Catégorie</InputLabel> <Select value={categoryFilter} onChange={(e) => setCategoryFilter(e.target.value)} label="Catégorie" sx={{ color: 'white', '.MuiOutlinedInput-notchedOutline': { borderColor: 'rgba(255,255,255,0.5)' }, '&:hover .MuiOutlinedInput-notchedOutline': { borderColor: 'white' }, '&.Mui-focused .MuiOutlinedInput-notchedOutline': { borderColor: 'white' }, '.MuiSvgIcon-root': { color: 'white' } }} > <MenuItem value="all">Toutes les catégories</MenuItem> <MenuItem value="technique">Problèmes techniques</MenuItem> <MenuItem value="billing">Facturation</MenuItem> <MenuItem value="network">Réseau mobile</MenuItem> <MenuItem value="internet">Internet fixe</MenuItem> </Select> </FormControl> <ButtonGroup variant="outlined" sx={{ borderColor: 'rgba(255,255,255,0.5)' }}> <Tooltip title={autoRefresh ? "Désactiver l'actualisation automatique" : "Activer l'actualisation automatique"}> <Button onClick={() => setAutoRefresh(!autoRefresh)} sx={{ color: 'white', borderColor: 'rgba(255,255,255,0.5)', bgcolor: autoRefresh ? 'rgba(255,255,255,0.2)' : 'transparent', '&:hover': { borderColor: 'white', bgcolor: 'rgba(255,255,255,0.1)' } }} > <RefreshIcon /> </Button> </Tooltip> <Tooltip title="Actualiser maintenant"> <Button onClick={handleRefreshData} disabled={loading} sx={{ color: 'white', borderColor: 'rgba(255,255,255,0.5)', '&:hover': { borderColor: 'white', bgcolor: 'rgba(255,255,255,0.1)' } }} > {loading ? <CircularProgress size={20} color="inherit" /> : <RefreshIcon />} </Button> </Tooltip> <Tooltip title="Exporter les données"> <Button onClick={() => handleExportData('csv')} sx={{ color: 'white', borderColor: 'rgba(255,255,255,0.5)', '&:hover': { borderColor: 'white', bgcolor: 'rgba(255,255,255,0.1)' } }} > <DownloadIcon /> </Button> </Tooltip> </ButtonGroup> </Box> </Box> <Grid container spacing={3}> {/* KPI Cards */} <Grid item xs={12}> <Grid container spacing={3}> {kpiData.map((kpi, index) => ( <Grid item xs={12} sm={6} lg={3} key={index}> <Fade in={!loading} timeout={300 + index * 100}> <Box> <KPICard {...kpi} /> </Box> </Fade> </Grid> ))} </Grid> </Grid> {/* Volume Chart - Now with Chart.js Implementation */} <Grid item xs={12} lg={8}> <VolumeChart loading={loading} error={error} height={450} onRefresh={handleRefreshData} onExport={handleExportData} onTimeRangeChange={handleTimeRangeChange} /> </Grid> {/* Category Distribution Chart */} <Grid item xs={12} lg={4}> <CategoryDistributionChart loading={loading} error={error} height={450} onRefresh={handleRefreshData} onExport={() => handleExportData('png')} /> </Grid> {/* Performance Trends Chart */} <Grid item xs={12}> <PerformanceTrendsChart loading={loading} error={error} height={400} onRefresh={handleRefreshData} onExport={() => handleExportData('png')} /> </Grid> {/* AI Insights and Recommendations */} <Grid item xs={12} md={6}> <Card elevation={3} sx={{ borderRadius: 2, height: '100%' }}> <CardHeader title={ <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}> <AIIcon sx={{ color: FREE_MOBILE_COLORS.PRIMARY }} /> <Typography variant="h6" fontWeight="bold"> Insights IA </Typography> </Box> } /> <Divider /> <CardContent> <List dense> {aiInsights.map((insight, index) => ( <ListItem key={index} sx={{ px: 0, py: 1 }}> <ListItemIcon> <IdeaIcon sx={{ color: '#f59e0b' }} /> </ListItemIcon> <ListItemText primary={insight.insight} secondary={ <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}> <Chip label={insight.impact} size="small" color={getImpactColor(insight.impact) as any} variant="outlined" /> <Typography variant="caption" color="text.secondary"> Confiance: {insight.confidence}% </Typography> </Box> } /> </ListItem> ))} </List> </CardContent> </Card> </Grid> {/* Top Issues */} <Grid item xs={12} md={6}> <Card elevation={3} sx={{ borderRadius: 2, height: '100%' }}> <CardHeader title={ <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}> <AssessmentIcon sx={{ color: FREE_MOBILE_COLORS.PRIMARY }} /> <Typography variant="h6" fontWeight="bold"> Problèmes fréquents </Typography> </Box> } /> <Divider /> <CardContent> <List dense> {topReasons.map((reason, index) => ( <ListItem key={index} sx={{ px: 0, py: 1 }}> <ListItemText primary={ <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}> <Typography variant="body2" fontWeight="medium"> {reason.reason} </Typography> <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}> <Typography variant="body2" fontWeight="bold" color={FREE_MOBILE_COLORS.PRIMARY}> {reason.count} </Typography> <Chip label={reason.trend === 'up' ? '↗' : reason.trend === 'down' ? '↘' : '→'} size="small" color={reason.trend === 'up' ? 'error' : reason.trend === 'down' ? 'success' : 'default'} sx={{ minWidth: 32, fontSize: '0.75rem' }} /> </Box> </Box> } secondary={`${((reason.count / 1247) * 100).toFixed(1)}% du total`} /> </ListItem> ))} </List> </CardContent> </Card> </Grid> </Grid> {/* Snackbar for notifications */} <Snackbar open={snackbarOpen} autoHideDuration={4000} onClose={() => setSnackbarOpen(false)} anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }} > <Alert onClose={() => setSnackbarOpen(false)} severity={snackbarSeverity} sx={{ borderRadius: 2 }} > {snackbarMessage} </Alert> </Snackbar> </Box> ); }; export default AnalyticsDashboard;