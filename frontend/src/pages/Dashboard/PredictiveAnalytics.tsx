/** * ============================================= * PREDICTIVE ANALYTICS DASHBOARD PAGE * ML-powered predictions and advanced analytics * Integrates with existing analytics for enhanced insights * ============================================= */ import React, { useEffect, useState } from 'react'; import { useAppDispatch, useAppSelector } from '../../hooks/redux'; import { Box, Container, Typography, Grid, Card, CardContent, Button, Chip, Alert, Switch, FormControlLabel, Select, MenuItem, FormControl, InputLabel, Tabs, Tab, Badge, Tooltip, IconButton, Breadcrumbs, Link, Paper, LinearProgress, CircularProgress } from '@mui/material'; import { TrendingUp as TrendIcon, Warning as WarningIcon, Schedule as ForecastIcon, Assignment as WorkloadIcon, BugReport as AnomalyIcon, Settings as SettingsIcon, Refresh as RefreshIcon, Assessment as MetricsIcon, Psychology as PredictiveIcon, NavigateNext as NavigateNextIcon, Speed as SpeedIcon, HealthAndSafety as HealthIcon } from '@mui/icons-material'; import { RootState } from '../../store'; import { fetchChurnPredictions, fetchDemandForecast, fetchWorkloadOptimization, fetchEscalationPredictions, fetchAnomalyDetection, fetchPredictiveMetrics, setActiveDashboard, setAutoRefresh, setTimeRange, setFilters, selectPredictiveState, selectChurnPredictions, selectHighRiskCustomers, selectDemandForecast, selectActiveAnomalies, selectSystemHealthScore } from '../../store/slices/predictiveSlice'; import { FREE_MOBILE_COLORS } from '../../utils/constants'; // Import predictive components (to be created) import ChurnPredictionPanel from '../../components/Predictive/ChurnPredictionPanel'; import DemandForecastPanel from '../../components/Predictive/DemandForecastPanel'; import WorkloadOptimizationPanel from '../../components/Predictive/WorkloadOptimizationPanel'; import EscalationPredictionPanel from '../../components/Predictive/EscalationPredictionPanel'; import AnomalyDetectionPanel from '../../components/Predictive/AnomalyDetectionPanel'; import PredictiveMetricsPanel from '../../components/Predictive/PredictiveMetricsPanel'; interface TabPanelProps { children?: React.ReactNode; index: number; value: number; } function TabPanel(props: TabPanelProps) { const { children, value, index, ...other } = props; return ( <div role="tabpanel" hidden={value !== index} id={`predictive-tabpanel-${index}`} aria-labelledby={`predictive-tab-${index}`} {...other} > {value === index && ( <Box sx={{ p: 3 }}> {children} </Box> )} </div> ); } const PredictiveAnalytics: React.FC = () => { const dispatch = useAppDispatch(); const predictiveState = useAppSelector(selectPredictiveState); const churnPredictions = useAppSelector(selectChurnPredictions); const highRiskCustomers = useAppSelector(selectHighRiskCustomers); const demandForecast = useAppSelector(selectDemandForecast); const activeAnomalies = useAppSelector(selectActiveAnomalies); const systemHealthScore = useAppSelector(selectSystemHealthScore); const [activeTab, setActiveTab] = useState(0); useEffect(() => { // Initialize predictive data dispatch(fetchChurnPredictions()); dispatch(fetchDemandForecast(24)); dispatch(fetchWorkloadOptimization()); dispatch(fetchEscalationPredictions()); dispatch(fetchAnomalyDetection('24h')); dispatch(fetchPredictiveMetrics()); // Set up auto-refresh if enabled if (predictiveState.auto_refresh) { const interval = setInterval(() => { refreshAllData(); }, predictiveState.refresh_interval * 1000); return () => clearInterval(interval); } }, [dispatch, predictiveState.auto_refresh, predictiveState.refresh_interval]); const refreshAllData = () => { dispatch(fetchChurnPredictions()); dispatch(fetchDemandForecast(24)); dispatch(fetchWorkloadOptimization()); dispatch(fetchEscalationPredictions()); dispatch(fetchAnomalyDetection(predictiveState.filters.time_range)); dispatch(fetchPredictiveMetrics()); }; const handleTabChange = (event: React.SyntheticEvent, newValue: number) => { setActiveTab(newValue); // Update active dashboard in state const dashboards = ['churn', 'demand', 'workload', 'escalation', 'anomaly'] as const; dispatch(setActiveDashboard(dashboards[newValue])); }; const handleTimeRangeChange = (timeRange: string) => { dispatch(setTimeRange(timeRange as any)); // Refresh data with new time range setTimeout(() => refreshAllData(), 100); }; const getHealthColor = (score: number) => { if (score >= 90) return 'success'; if (score >= 70) return 'warning'; return 'error'; }; const getRiskColor = (level: string): 'success' | 'warning' | 'error' | 'info' => { switch (level) { case 'low': return 'success'; case 'medium': return 'warning'; case 'high': return 'error'; case 'critical': return 'error'; default: return 'info'; } }; const getAnomalySeverityCount = (severity: string) => { return activeAnomalies.filter(a => a.severity === severity).length; }; return ( <Container maxWidth={false} sx={{ py: 3 }}> {/* Header */} <Box sx={{ mb: 4 }}> <Breadcrumbs separator={<NavigateNextIcon fontSize="small" />} sx={{ mb: 2 }}> <Link color="inherit" href="/dashboard"> Dashboard </Link> <Link color="inherit" href="/dashboard/admin"> Administration </Link> <Typography color="text.primary">Analyses Prédictives</Typography> </Breadcrumbs> <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}> <Box> <Typography variant="h4" component="h1" fontWeight="bold" color={FREE_MOBILE_COLORS.PRIMARY}> Analyses Prédictives ML </Typography> <Typography variant="subtitle1" color="text.secondary"> Intelligence artificielle pour la prédiction et l'optimisation des performances </Typography> </Box> <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}> <FormControl size="small" sx={{ minWidth: 120 }}> <InputLabel>Période</InputLabel> <Select value={predictiveState.filters.time_range} label="Période" onChange={(e) => handleTimeRangeChange(e.target.value)} > <MenuItem value="1h">1 Heure</MenuItem> <MenuItem value="4h">4 Heures</MenuItem> <MenuItem value="24h">24 Heures</MenuItem> <MenuItem value="7d">7 Jours</MenuItem> <MenuItem value="30d">30 Jours</MenuItem> </Select> </FormControl> <FormControlLabel control={ <Switch checked={predictiveState.auto_refresh} onChange={(e) => dispatch(setAutoRefresh(e.target.checked))} /> } label="Auto-actualisation" /> <Tooltip title="Actualiser toutes les données"> <IconButton onClick={refreshAllData} disabled={Object.values(predictiveState.loading).some(loading => loading)} > <RefreshIcon /> </IconButton> </Tooltip> </Box> </Box> {/* System Health & Quick Stats */} <Grid container spacing={2} sx={{ mb: 3 }}> <Grid item xs={12} sm={6} md={2.4}> <Card> <CardContent sx={{ textAlign: 'center' }}> <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 1 }}> <HealthIcon color={getHealthColor(systemHealthScore)} sx={{ mr: 1 }} /> <Typography variant="h4" color={getHealthColor(systemHealthScore) + '.main'} fontWeight="bold"> {systemHealthScore}% </Typography> </Box> <Typography variant="body2" color="text.secondary"> Santé du Système </Typography> <LinearProgress variant="determinate" value={systemHealthScore} color={getHealthColor(systemHealthScore)} sx={{ mt: 1 }} /> </CardContent> </Card> </Grid> <Grid item xs={12} sm={6} md={2.4}> <Card> <CardContent sx={{ textAlign: 'center' }}> <Typography variant="h4" color="error.main" fontWeight="bold"> {highRiskCustomers.length} </Typography> <Typography variant="body2" color="text.secondary"> Clients à Risque </Typography> <Chip label="Critique" color="error" size="small" sx={{ mt: 1 }} /> </CardContent> </Card> </Grid> <Grid item xs={12} sm={6} md={2.4}> <Card> <CardContent sx={{ textAlign: 'center' }}> <Typography variant="h4" color="warning.main" fontWeight="bold"> {predictiveState.high_risk_tickets.length} </Typography> <Typography variant="body2" color="text.secondary"> Escalades Prévues </Typography> <Chip label="Surveillance" color="warning" size="small" sx={{ mt: 1 }} /> </CardContent> </Card> </Grid> <Grid item xs={12} sm={6} md={2.4}> <Card> <CardContent sx={{ textAlign: 'center' }}> <Typography variant="h4" color="info.main" fontWeight="bold"> {Math.round(predictiveState.team_efficiency)}% </Typography> <Typography variant="body2" color="text.secondary"> Efficacité Équipe </Typography> <Chip label="Optimisé" color="info" size="small" sx={{ mt: 1 }} /> </CardContent> </Card> </Grid> <Grid item xs={12} sm={6} md={2.4}> <Card> <CardContent sx={{ textAlign: 'center' }}> <Typography variant="h4" color="primary.main" fontWeight="bold"> {activeAnomalies.length} </Typography> <Typography variant="body2" color="text.secondary"> Anomalies Actives </Typography> <Box sx={{ display: 'flex', justifyContent: 'center', gap: 0.5, mt: 1 }}> {getAnomalySeverityCount('critical') > 0 && ( <Chip label={`${getAnomalySeverityCount('critical')} Critique`} color="error" size="small" /> )} {getAnomalySeverityCount('high') > 0 && ( <Chip label={`${getAnomalySeverityCount('high')} Élevé`} color="warning" size="small" /> )} </Box> </CardContent> </Card> </Grid> </Grid> {/* Critical Alerts */} {activeAnomalies.filter(a => a.severity === 'critical').length > 0 && ( <Alert severity="error" sx={{ mb: 3 }} icon={<AnomalyIcon />} > <Typography variant="body1"> <strong>Anomalies Critiques Détectées:</strong> {activeAnomalies.filter(a => a.severity === 'critical').length} anomalies nécessitent une attention immédiate </Typography> </Alert> )} {highRiskCustomers.length > 10 && ( <Alert severity="warning" sx={{ mb: 3 }} icon={<WarningIcon />} > <Typography variant="body1"> <strong>Alerte Churn:</strong> {highRiskCustomers.length} clients présentent un risque élevé de désabonnement </Typography> </Alert> )} </Box> {/* Main Content Tabs */} <Card> <Box sx={{ borderBottom: 1, borderColor: 'divider' }}> <Tabs value={activeTab} onChange={handleTabChange} aria-label="predictive analytics tabs"> <Tab label="Prédiction Churn" icon={ <Badge badgeContent={highRiskCustomers.length} color="error"> <TrendIcon /> </Badge> } iconPosition="start" /> <Tab label="Prévision Demande" icon={<ForecastIcon />} iconPosition="start" /> <Tab label="Optimisation Charge" icon={<WorkloadIcon />} iconPosition="start" /> <Tab label="Prédiction Escalade" icon={ <Badge badgeContent={predictiveState.high_risk_tickets.length} color="warning"> <WarningIcon /> </Badge> } iconPosition="start" /> <Tab label="Détection Anomalies" icon={ <Badge badgeContent={activeAnomalies.length} color="primary"> <AnomalyIcon /> </Badge> } iconPosition="start" /> </Tabs> </Box> {/* Tab Panels */} <TabPanel value={activeTab} index={0}> <ChurnPredictionPanel predictions={churnPredictions} highRiskCustomers={highRiskCustomers} trends={predictiveState.churn_trends} loading={predictiveState.loading.churn} /> </TabPanel> <TabPanel value={activeTab} index={1}> <DemandForecastPanel forecast={demandForecast} peakHours={predictiveState.peak_hours} staffingRecommendations={predictiveState.staffing_recommendations} loading={predictiveState.loading.demand} /> </TabPanel> <TabPanel value={activeTab} index={2}> <WorkloadOptimizationPanel agentWorkloads={predictiveState.agent_workloads} teamEfficiency={predictiveState.team_efficiency} optimalAssignments={predictiveState.optimal_assignments} loading={predictiveState.loading.workload} /> </TabPanel> <TabPanel value={activeTab} index={3}> <EscalationPredictionPanel predictions={predictiveState.escalation_predictions} highRiskTickets={predictiveState.high_risk_tickets} trends={predictiveState.escalation_trends} loading={predictiveState.loading.escalation} /> </TabPanel> <TabPanel value={activeTab} index={4}> <AnomalyDetectionPanel activeAnomalies={activeAnomalies} anomalyHistory={predictiveState.anomaly_history} systemHealthScore={systemHealthScore} loading={predictiveState.loading.anomaly} /> </TabPanel> </Card> {/* Model Performance Metrics */} <Card sx={{ mt: 3 }}> <CardContent> <Typography variant="h6" sx={{ mb: 2 }}> Performance des Modèles ML </Typography> <PredictiveMetricsPanel metrics={predictiveState.predictive_metrics} loading={predictiveState.loading.metrics} /> </CardContent> </Card> </Container> ); }; export default PredictiveAnalytics;