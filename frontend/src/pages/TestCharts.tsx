import React from 'react';
import { Box, Container, Typography, Grid } from '@mui/material';
import VolumeChart from '../components/Dashboard/Analytics/VolumeChart';
import CategoryDistributionChart from '../components/Dashboard/Analytics/CategoryDistributionChart';
import PerformanceTrendsChart from '../components/Dashboard/Analytics/PerformanceTrendsChart';

const TestCharts: React.FC = () => {
  const handleRefresh = () => {
    console.log('Refresh clicked');
  };

  const handleExport = (format: 'png' | 'pdf' | 'csv') => {
    console.log('Export clicked:', format);
  };

  const handleTimeRangeChange = (range: string) => {
    console.log('Time range changed:', range);
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h3" gutterBottom align="center" fontWeight="bold">
        Test des Graphiques Chart.js
      </Typography>
      <Typography variant="body1" align="center" color="text.secondary" sx={{ mb: 4 }}>
        Vérification des composants de graphiques avec Chart.js
      </Typography>

      <Grid container spacing={4}>
        {/* Volume Chart */}
        <Grid item xs={12} lg={8}>
          <VolumeChart
            loading={false}
            error={null}
            height={450}
            onRefresh={handleRefresh}
            onExport={handleExport}
            onTimeRangeChange={handleTimeRangeChange}
          />
        </Grid>

        {/* Category Distribution Chart */}
        <Grid item xs={12} lg={4}>
          <CategoryDistributionChart
            loading={false}
            error={null}
            height={450}
            onRefresh={handleRefresh}
            onExport={handleExport}
          />
        </Grid>

        {/* Performance Trends Chart */}
        <Grid item xs={12}>
          <PerformanceTrendsChart
            loading={false}
            error={null}
            height={400}
            onRefresh={handleRefresh}
            onExport={handleExport}
            onTimeRangeChange={handleTimeRangeChange}
          />
        </Grid>
      </Grid>
    </Container>
  );
};

export default TestCharts;
