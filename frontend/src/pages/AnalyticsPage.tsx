import React, { useEffect, useState } from 'react'; import { useDispatch, useSelector } from 'react-redux'; import { Box, Card, CardContent, Typography, Grid, TextField, Button, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Alert, CircularProgress, } from '@mui/material'; import { DatePicker, LocalizationProvider, } from '@mui/x-date-pickers'; import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns'; import { fr } from 'date-fns/locale'; import { AppDispatch, RootState } from '../store'; import { fetchAnalytics } from '../store/adminSlice'; const AnalyticsPage: React.FC = () => { const dispatch = useDispatch<AppDispatch>(); const { analytics, loading, error } = useSelector((state: RootState) => state.admin); const [startDate, setStartDate] = useState<Date | null>( new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) ); const [endDate, setEndDate] = useState<Date | null>(new Date()); useEffect(() => { loadAnalytics(); }, []); const loadAnalytics = () => { dispatch(fetchAnalytics({ startDate: startDate?.toISOString(), endDate: endDate?.toISOString(), })); }; const formatDate = (dateObj: { year: number; month: number; day: number }) => { return `${dateObj.day}/${dateObj.month}/${dateObj.year}`; }; if (loading) { return ( <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px"> <CircularProgress /> </Box> ); } return ( <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={fr}> <Box sx={{ p: 3 }}> <Typography variant="h4" gutterBottom> Analytics et Rapports </Typography> {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>} {/* Sélection de période */} <Card sx={{ mb: 3 }}> <CardContent> <Typography variant="h6" gutterBottom> Période d'analyse </Typography> <Grid container spacing={2} alignItems="center"> <Grid item xs={12} sm={4}> <DatePicker label="Date de début" value={startDate} onChange={setStartDate} slotProps={{ textField: { fullWidth: true, size: 'small' } }} /> </Grid> <Grid item xs={12} sm={4}> <DatePicker label="Date de fin" value={endDate} onChange={setEndDate} slotProps={{ textField: { fullWidth: true, size: 'small' } }} /> </Grid> <Grid item xs={12} sm={4}> <Button fullWidth variant="contained" onClick={loadAnalytics} > Actualiser </Button> </Grid> </Grid> </CardContent> </Card> {analytics && ( <Grid container spacing={3}> {/* Evolution des conversations */} <Grid item xs={12} md={6}> <Card> <CardContent> <Typography variant="h6" gutterBottom> Évolution des Conversations </Typography> <TableContainer> <Table size="small"> <TableHead> <TableRow> <TableCell>Date</TableCell> <TableCell align="right">Nombre</TableCell> </TableRow> </TableHead> <TableBody> {analytics.conversationsOverTime.map((item, index) => ( <TableRow key={index}> <TableCell>{formatDate(item._id)}</TableCell> <TableCell align="right">{item.count}</TableCell> </TableRow> ))} </TableBody> </Table> </TableContainer> </CardContent> </Card> </Grid> {/* Statistiques des messages */} <Grid item xs={12} md={6}> <Card> <CardContent> <Typography variant="h6" gutterBottom> Statistiques des Messages </Typography> <TableContainer> <Table size="small"> <TableHead> <TableRow> <TableCell>Expéditeur</TableCell> <TableCell align="right">Nombre</TableCell> <TableCell align="right">Longueur Moy.</TableCell> </TableRow> </TableHead> <TableBody> {analytics.messageStats.map((item) => ( <TableRow key={item._id}> <TableCell> {item._id === 'user' ? 'Client' : item._id === 'bot' ? 'Bot' : 'Agent'} </TableCell> <TableCell align="right">{item.count}</TableCell> <TableCell align="right"> {Math.round(item.avgLength)} caractères </TableCell> </TableRow> ))} </TableBody> </Table> </TableContainer> </CardContent> </Card> </Grid> {/* Analyse des intentions */} <Grid item xs={12}> <Card> <CardContent> <Typography variant="h6" gutterBottom> Top 10 des Intentions Détectées </Typography> <TableContainer> <Table> <TableHead> <TableRow> <TableCell>Intention</TableCell> <TableCell align="right">Occurrences</TableCell> <TableCell align="right">Confiance Moyenne</TableCell> </TableRow> </TableHead> <TableBody> {analytics.intentStats.slice(0, 10).map((item) => ( <TableRow key={item._id}> <TableCell>{item._id}</TableCell> <TableCell align="right">{item.count}</TableCell> <TableCell align="right"> {(item.avgConfidence * 100).toFixed(1)}% </TableCell> </TableRow> ))} </TableBody> </Table> </TableContainer> </CardContent> </Card> </Grid> {/* Résumé de la période */} <Grid item xs={12}> <Card> <CardContent> <Typography variant="h6" gutterBottom> Résumé de la Période </Typography> <Grid container spacing={2}> <Grid item xs={12} sm={6} md={3}> <Paper sx={{ p: 2, textAlign: 'center' }}> <Typography variant="h4" color="primary"> {analytics.conversationsOverTime.reduce((sum, item) => sum + item.count, 0)} </Typography> <Typography variant="body2" color="text.secondary"> Total Conversations </Typography> </Paper> </Grid> <Grid item xs={12} sm={6} md={3}> <Paper sx={{ p: 2, textAlign: 'center' }}> <Typography variant="h4" color="success.main"> {analytics.messageStats.reduce((sum, item) => sum + item.count, 0)} </Typography> <Typography variant="body2" color="text.secondary"> Total Messages </Typography> </Paper> </Grid> <Grid item xs={12} sm={6} md={3}> <Paper sx={{ p: 2, textAlign: 'center' }}> <Typography variant="h4" color="info.main"> {analytics.intentStats.length} </Typography> <Typography variant="body2" color="text.secondary"> Intentions Différentes </Typography> </Paper> </Grid> <Grid item xs={12} sm={6} md={3}> <Paper sx={{ p: 2, textAlign: 'center' }}> <Typography variant="h4" color="warning.main"> {analytics.intentStats.length > 0 ? (analytics.intentStats.reduce((sum, item) => sum + item.avgConfidence, 0) / analytics.intentStats.length * 100).toFixed(1) : 0}% </Typography> <Typography variant="body2" color="text.secondary"> Confiance Moyenne </Typography> </Paper> </Grid> </Grid> </CardContent> </Card> </Grid> </Grid> )} </Box> </LocalizationProvider> ); }; export default AnalyticsPage;