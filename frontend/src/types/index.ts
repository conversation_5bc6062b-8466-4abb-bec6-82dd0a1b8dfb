export interface User { id: string; email: string; role: 'user' | 'agent' | 'admin'; profile: { firstName?: string; lastName?: string; phoneNumber?: string; customerId?: string; }; preferences?: { language?: string; notifications?: boolean; theme?: 'light' | 'dark'; }; createdAt?: Date; lastLogin?: Date; } export interface RegisterData { email: string; password: string; firstName: string; lastName: string; phoneNumber?: string; role?: 'user' | 'agent' | 'admin'; } export interface LoginResponse { user: User; token: string; message: string; } export interface Message { _id: string; conversationId: string; sender: 'user' | 'bot' | 'agent'; content: { text: string; type: 'text' | 'button' | 'card' | 'image' | 'file'; payload?: any; }; timestamp: Date; intent?: { name: string; confidence: number; }; entities?: Array<{ entity: string; value: string; confidence: number; }>; metadata?: { processingTime?: number; nlpProvider?: string; fallback?: boolean; }; } export interface Conversation { _id: string; userId?: string; sessionId: string; status: 'active' | 'resolved' | 'escalated' | 'abandoned'; channel: 'web' | 'mobile' | 'voice'; startedAt: Date; endedAt?: Date; agentId?: string; satisfaction?: { rating: number; feedback: string; }; metadata?: { userAgent?: string; ipAddress?: string; location?: any; }; } export interface AuthState { user: User | null; token: string | null; isAuthenticated: boolean; loading: boolean; error: string | null; } export interface ChatState { conversations: Conversation[]; currentConversation: Conversation | null; messages: Message[]; loading: boolean; error: string | null; isTyping: boolean; isAgentOnline: boolean; typingUsers: string[]; connectionStatus: { connected: boolean; error: string | null; }; realTimeEnabled: boolean; } export interface AdminState { dashboard: DashboardData | null; conversations: Conversation[]; analytics: AnalyticsData | null; loading: boolean; error: string | null; } export interface DashboardData { statistics: { totalConversations: number; activeConversations: number; resolvedConversations: number; conversationsToday: number; avgSatisfaction: number; }; conversationsByStatus: Array<{ _id: string; count: number; }>; recentConversations: Conversation[]; } export interface AnalyticsData { conversationsOverTime: Array<{ _id: { year: number; month: number; day: number; }; count: number; }>; messageStats: Array<{ _id: string; count: number; avgLength: number; }>; intentStats: Array<{ _id: string; count: number; avgConfidence: number; }>; dateRange: { start: Date; end: Date; }; } // Types pour le client export interface CustomerProfile { _id: string; firstName: string; lastName: string; email: string; phoneNumber: string; customerId: string; plan: { name: string; price: number; features: string[]; }; options: Array<{ name: string; price: number; active: boolean; }>; usage: { data: number; calls: number; sms: number; }; billing: { nextBillDate: Date; lastBillAmount: number; balance: number; }; } export interface Invoice { _id: string; customerId: string; amount: number; date: Date; dueDate: Date; status: 'paid' | 'pending' | 'overdue'; items: Array<{ description: string; amount: number; }>; } export interface Notification { _id: string; customerId: string; title: string; message: string; type: 'info' | 'warning' | 'error' | 'success'; read: boolean; createdAt: Date; } export interface CustomerState { profile: CustomerProfile | null; invoices: Invoice[]; notifications: Notification[]; loading: boolean; error: string | null; } export interface UIState { theme: 'light' | 'dark'; sidebarOpen: boolean; loading: boolean; notifications: Array<{ id: string; message: string; type: 'success' | 'error' | 'warning' | 'info'; duration?: number; }>; modal: { open: boolean; title?: string; content?: string; onConfirm?: () => void; }; }