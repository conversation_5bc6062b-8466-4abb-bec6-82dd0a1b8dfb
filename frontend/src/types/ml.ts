/** * Types TypeScript pour les fonctionnalités ML * Free Mobile Chatbot Dashboard - Phase 3 Frontend Implementation */ // Énumérations pour les catégories ML export enum ConversationCategory { VENTE_OPPORTUNITE = 'VENTE_OPPORTUNITE', RESILIATION_CRITIQUE = 'RESILIATION_CRITIQUE', SUPPORT_URGENT = 'SUPPORT_URGENT', RECLAMATION = 'RECLAMATION', INFO_SIMPLE = 'INFO_SIMPLE' } export enum SentimentTrend { IMPROVING = 'improving', DECLINING = 'declining', STABLE = 'stable' } export enum AlertType { VENTE_OPPORTUNITY = 'VENTE_OPPORTUNITY', CHURN_RISK = 'CHURN_RISK', ESCALATION_NEEDED = 'ESCALATION_NEEDED', TECHNICAL_ISSUE = 'TECHNICAL_ISSUE', BILLING_DISPUTE = 'BILLING_DISPUTE', SATISFACTION_CRITICAL = 'SATISFACTION_CRITICAL', SYSTEM_ANOMALY = 'SYSTEM_ANOMALY', PERFORMANCE_DEGRADATION = 'PERFORMANCE_DEGRADATION' } export enum AlertSeverity { LOW = 'LOW', MEDIUM = 'MEDIUM', HIGH = 'HIGH', CRITICAL = 'CRITICAL' } export enum AlertStatus { ACTIVE = 'ACTIVE', ACKNOWLEDGED = 'ACKNOWLEDGED', IN_PROGRESS = 'IN_PROGRESS', RESOLVED = 'RESOLVED', DISMISSED = 'DISMISSED', EXPIRED = 'EXPIRED' } // Interfaces pour les données ML export interface BusinessImpact { revenueAtRisk: number; opportunityValue: number; retentionProbability: number; lifetimeValueImpact: number; } export interface SentimentAnalysis { score: number; trend: SentimentTrend; confidence: number; keyEmotions: string[]; history?: Array<{ timestamp: string; score: number; trigger?: string; }>; } export interface RecommendedAction { type: string; priority: number; script: string; expectedOutcome: string; confidence: number; estimatedRevenueImpact: number; } export interface ConversationClassification { _id: string; conversationId: string; customerId: string; category: ConversationCategory; priorityScore: number; confidence: number; businessImpact: BusinessImpact; sentiment: SentimentAnalysis; recommendedActions: RecommendedAction[]; mlModelVersion: string; processingTimeMs: number; featuresUsed: string[]; humanValidated: boolean; validatedBy?: string; validatedAt?: string; validationFeedback?: string; processedAt: string; createdAt: string; updatedAt: string; } export interface AdminAlert { _id: string; type: AlertType; severity: AlertSeverity; status: AlertStatus; title: string; description: string; conversationId?: string; customerId: string; classificationId?: string; priority: number; assignedTo?: string; assignedAt?: string; acknowledgedAt?: string; acknowledgedBy?: string; resolution?: { action: string; outcome: string; revenueImpact: number; resolvedBy: string; resolvedAt: string; notes?: string; }; escalationLevel: number; escalationHistory: Array<{ level: number; escalatedTo: string; escalatedAt: string; reason: string; previousAssignee?: string; }>; contextData?: any; createdAt: string; updatedAt: string; } export interface PriorityQueueItem { conversation_id: string; priority_score: number; category: ConversationCategory; timestamp: string; customer_id: string; business_impact: BusinessImpact; classification?: ConversationClassification; conversation?: any; customer?: any; alerts?: number; } export interface MLPerformanceMetrics { total_classifications: number; average_processing_time_ms: number; throughput_per_second: number; cache_hit_rate: number; models_loaded: number; memory_usage_mb?: number; redis_connected?: boolean; fallback?: boolean; } export interface DashboardMetrics { recentClassifications: number; activeAlerts: number; highPriorityConversations: number; connectedUsers: number; timestamp: string; } export interface MLMetricsResponse { ml: MLPerformanceMetrics; dashboard: DashboardMetrics; } export interface ClassificationStats { _id: string; count: number; avgPriority: number; avgConfidence: number; avgProcessingTime: number; totalRevenueAtRisk: number; totalOpportunityValue: number; highPriorityCount: number; } export interface AlertStats { _id: { type: AlertType; severity: AlertSeverity; }; count: number; avgTimeToResolve: number; avgTimeToAcknowledge: number; resolvedCount: number; } // Interfaces pour les requêtes API export interface ClassifyConversationRequest { conversationId: string; forceReprocess?: boolean; } export interface ClassifyConversationResponse { success: boolean; classification: ConversationClassification; cached?: boolean; message?: string; error?: string; } export interface PriorityQueueRequest { limit?: number; minPriority?: number; category?: ConversationCategory; status?: string; } export interface PriorityQueueResponse { success: boolean; queue: PriorityQueueItem[]; totalCount: number; filters: { limit: number; minPriority: number; category?: ConversationCategory; }; } export interface PerformanceMetricsRequest { timeRange?: '1h' | '24h' | '7d' | '30d'; } export interface PerformanceMetricsResponse { success: boolean; timeRange: string; timestamp: string; mlService: MLPerformanceMetrics; classifications: { total: number; humanValidated: number; validationRate: number; avgConfidence: number; highPriorityCount: number; categoryBreakdown: ClassificationStats[]; }; alerts: { breakdown: AlertStats[]; totalActive: number; }; } export interface ValidateClassificationRequest { isCorrect: boolean; correctedCategory?: ConversationCategory; feedback?: string; confidence?: number; } export interface ValidateClassificationResponse { success: boolean; classification: ConversationClassification; message: string; } export interface AlertsRequest { status?: AlertStatus; severity?: AlertSeverity; type?: AlertType; assignedTo?: string; limit?: number; } export interface AlertsResponse { success: boolean; alerts: AdminAlert[]; totalCount: number; } export interface AcknowledgeAlertResponse { success: boolean; alert: AdminAlert; message: string; } // Interfaces pour les événements WebSocket export interface MLWebSocketEvent { type: string; data: any; timestamp: string; } export interface ClassificationEvent extends MLWebSocketEvent { type: 'classification:new'; data: { id: string; conversationId: string; customerId: string; category: ConversationCategory; priorityScore: number; confidence: number; businessImpact: BusinessImpact; sentiment: SentimentAnalysis; processedAt: string; }; } export interface AlertEvent extends MLWebSocketEvent { type: 'alert:new'; data: { id: string; type: AlertType; severity: AlertSeverity; title: string; description: string; conversationId?: string; customerId: string; priority: number; assignedTo?: string; createdAt: string; }; } export interface MetricsEvent extends MLWebSocketEvent { type: 'metrics:update'; data: MLMetricsResponse; } export interface PriorityQueueEvent extends MLWebSocketEvent { type: 'priority-queue:update'; data: PriorityQueueItem[]; } // Types pour les filtres et options d'affichage export interface MLDashboardFilters { timeRange: '1h' | '24h' | '7d' | '30d'; category?: ConversationCategory; minPriority: number; alertSeverity?: AlertSeverity; alertStatus?: AlertStatus; showOnlyUnvalidated: boolean; } export interface MLDashboardState { classifications: ConversationClassification[]; alerts: AdminAlert[]; priorityQueue: PriorityQueueItem[]; metrics: MLMetricsResponse | null; filters: MLDashboardFilters; loading: { classifications: boolean; alerts: boolean; priorityQueue: boolean; metrics: boolean; }; error: { classifications: string | null; alerts: string | null; priorityQueue: string | null; metrics: string | null; }; lastUpdated: { classifications: string | null; alerts: string | null; priorityQueue: string | null; metrics: string | null; }; } // Types pour les graphiques et visualisations export interface ChartDataPoint { x: string | number; y: number; label?: string; color?: string; } export interface TimeSeriesData { timestamp: string; value: number; category?: string; } export interface CategoryDistribution { category: ConversationCategory; count: number; percentage: number; avgPriority: number; color: string; } export interface SentimentTrendData { timestamp: string; score: number; trend: SentimentTrend; conversationCount: number; }