import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { Provider } from 'react-redux';
import { store } from './store';
import LoginForm from './components/Auth/LoginForm';
import DashboardRoutes from './routes/dashboardRoutes';
import { useAuth } from './hooks/useAuth';
import { ROUTES, FREE_MOBILE_COLORS } from './utils/constants';

const theme = createTheme({
  palette: {
    primary: {
      main: FREE_MOBILE_COLORS.PRIMARY, // Rouge Free Mobile
    },
    secondary: {
      main: FREE_MOBILE_COLORS.SECONDARY, // Noir Free Mobile
    },
    success: {
      main: FREE_MOBILE_COLORS.SUCCESS,
    },
    warning: {
      main: FREE_MOBILE_COLORS.WARNING,
    },
    error: {
      main: FREE_MOBILE_COLORS.ERROR,
    },
    info: {
      main: FREE_MOBILE_COLORS.INFO,
    },
    background: {
      default: '#f5f5f5',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontWeight: 700,
    },
    h2: {
      fontWeight: 700,
    },
    h3: {
      fontWeight: 600,
    },
    h4: {
      fontWeight: 600,
    },
    h5: {
      fontWeight: 500,
    },
    h6: {
      fontWeight: 500,
    },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          textTransform: 'none',
          fontWeight: 500,
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: 6,
        },
      },
    },
  },
});

const PrivateRoute: React.FC<{
  children: React.ReactNode;
  requiredRoles?: string[];
}> = ({ children, requiredRoles }) => {
  const { isAuthenticated, user, loading } = useAuth();

  // Show loading while checking authentication
  if (loading) {
    return <div>Chargement...</div>;
  }

  if (!isAuthenticated) {
    return <Navigate to={ROUTES.LOGIN} replace />;
  }

  if (requiredRoles && user && !requiredRoles.includes(user.role)) {
    return <Navigate to={ROUTES.DASHBOARD_OVERVIEW} replace />;
  }

  return <>{children}</>;
};

function AppContent() {
  return (
    <Router>
      <Routes>
        {/* Public Routes */}
        <Route path={ROUTES.LOGIN} element={<LoginForm />} />

        {/* Dashboard Routes - Primary routing system */}
        <Route
          path="/dashboard/*"
          element={
            <PrivateRoute>
              <DashboardRoutes />
            </PrivateRoute>
          }
        />

        {/* Legacy Routes - Redirect to dashboard */}
        <Route path={ROUTES.SUPPORT} element={<Navigate to={ROUTES.DASHBOARD_OVERVIEW} replace />} />
        <Route path={ROUTES.CHAT} element={<Navigate to={ROUTES.DASHBOARD_OVERVIEW} replace />} />
        <Route path={ROUTES.ADMIN_DASHBOARD} element={<Navigate to={ROUTES.DASHBOARD_ADMIN} replace />} />
        <Route path={ROUTES.ADMIN_CONVERSATIONS} element={<Navigate to={ROUTES.DASHBOARD_ADMIN_CONVERSATIONS} replace />} />
        <Route path={ROUTES.ADMIN_ANALYTICS} element={<Navigate to={ROUTES.DASHBOARD_ANALYTICS} replace />} />
        <Route path={ROUTES.AGENT_CHAT} element={<Navigate to={ROUTES.DASHBOARD_AGENT_COPILOT} replace />} />

        {/* Default Redirections */}
        <Route path={ROUTES.HOME} element={<Navigate to={ROUTES.DASHBOARD_OVERVIEW} replace />} />
        <Route path="*" element={<Navigate to={ROUTES.DASHBOARD_OVERVIEW} replace />} />
      </Routes>
    </Router>
  );
}

function App() {
  return (
    <Provider store={store}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <AppContent />
      </ThemeProvider>
    </Provider>
  );
}

export default App;