/** * ============================================= * PREDICTIVE ANALYTICS SERVICE * ML-powered predictions and analytics service * Integrates with existing ML service for advanced predictions * ============================================= */ import { io, Socket } from 'socket.io-client'; import { store } from '../store'; import { addAnomaly, updateChurnPrediction, updateEscalationPrediction, updateLastRefresh } from '../store/slices/predictiveSlice'; import { ChurnPrediction, DemandForecast, WorkloadOptimization, EscalationPrediction, AnomalyDetection, PredictiveMetrics } from '../store/slices/predictiveSlice'; class PredictiveService { private socket: Socket | null = null; private baseURL: string; private isConnected: boolean = false; private refreshInterval: NodeJS.Timeout | null = null; constructor() { // Use existing ML service URL pattern this.baseURL = process.env.REACT_APP_ML_API_URL || 'http://localhost:5001'; } // WebSocket connection for real-time predictions connectWebSocket(): Promise<void> { return new Promise((resolve, reject) => { try { this.socket = io(`${this.baseURL}/predictive`, { transports: ['websocket'], timeout: 10000, }); this.socket.on('connect', () => { console.log(' Predictive Analytics WebSocket connected'); this.isConnected = true; resolve(); }); this.socket.on('disconnect', () => { console.log(' Predictive Analytics WebSocket disconnected'); this.isConnected = false; }); this.socket.on('connect_error', (error) => { console.error(' Predictive Analytics WebSocket connection error:', error); reject(error); }); // Real-time prediction events this.socket.on('churn_prediction_update', (prediction: ChurnPrediction) => { store.dispatch(updateChurnPrediction(prediction)); }); this.socket.on('escalation_prediction_update', (prediction: EscalationPrediction) => { store.dispatch(updateEscalationPrediction(prediction)); }); this.socket.on('anomaly_detected', (anomaly: AnomalyDetection) => { store.dispatch(addAnomaly(anomaly)); }); this.socket.on('metrics_update', (metrics) => { this.handleMetricsUpdate(metrics); }); } catch (error) { console.error(' Failed to initialize predictive WebSocket:', error); reject(error); } }); } disconnectWebSocket(): void { if (this.socket) { this.socket.disconnect(); this.socket = null; this.isConnected = false; } if (this.refreshInterval) { clearInterval(this.refreshInterval); this.refreshInterval = null; } } // Auto-refresh functionality startAutoRefresh(intervalSeconds: number = 300): void { if (this.refreshInterval) { clearInterval(this.refreshInterval); } this.refreshInterval = setInterval(async () => { try { await this.refreshAllPredictions(); store.dispatch(updateLastRefresh()); } catch (error) { console.error(' Auto-refresh failed:', error); } }, intervalSeconds * 1000); } stopAutoRefresh(): void { if (this.refreshInterval) { clearInterval(this.refreshInterval); this.refreshInterval = null; } } // Churn prediction async fetchChurnPredictions(filters?: { risk_level?: string; time_range?: string; customer_segment?: string; }): Promise<{ predictions: ChurnPrediction[]; high_risk_customers: ChurnPrediction[]; trends: any; }> { try { const response = await fetch(`${this.baseURL}/api/ml/predictions/churn`, { method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${this.getAuthToken()}` }, body: JSON.stringify(filters || {}) }); if (!response.ok) { throw new Error(`Failed to fetch churn predictions: ${response.statusText}`); } return response.json(); } catch (error) { console.error(' Error fetching churn predictions:', error); throw error; } } async updateChurnModel(modelParams: any): Promise<void> { try { const response = await fetch(`${this.baseURL}/api/ml/models/churn/update`, { method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${this.getAuthToken()}` }, body: JSON.stringify(modelParams) }); if (!response.ok) { throw new Error(`Failed to update churn model: ${response.statusText}`); } } catch (error) { console.error(' Error updating churn model:', error); throw error; } } // Demand forecasting async fetchDemandForecast(hoursAhead: number = 24): Promise<{ forecast: DemandForecast[]; peak_hours: number[]; staffing_recommendations: any; }> { try { const response = await fetch(`${this.baseURL}/api/ml/predictions/demand?hours=${hoursAhead}`, { headers: { 'Authorization': `Bearer ${this.getAuthToken()}` } }); if (!response.ok) { throw new Error(`Failed to fetch demand forecast: ${response.statusText}`); } return response.json(); } catch (error) { console.error(' Error fetching demand forecast:', error); throw error; } } async updateDemandModel(modelParams: any): Promise<void> { try { const response = await fetch(`${this.baseURL}/api/ml/models/demand/update`, { method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${this.getAuthToken()}` }, body: JSON.stringify(modelParams) }); if (!response.ok) { throw new Error(`Failed to update demand model: ${response.statusText}`); } } catch (error) { console.error(' Error updating demand model:', error); throw error; } } // Workload optimization async fetchWorkloadOptimization(): Promise<{ agent_workloads: WorkloadOptimization[]; team_efficiency: number; optimal_assignments: any[]; }> { try { const response = await fetch(`${this.baseURL}/api/ml/optimization/workload`, { headers: { 'Authorization': `Bearer ${this.getAuthToken()}` } }); if (!response.ok) { throw new Error(`Failed to fetch workload optimization: ${response.statusText}`); } return response.json(); } catch (error) { console.error(' Error fetching workload optimization:', error); throw error; } } async optimizeAgentAssignment(ticketId: string, constraints?: any): Promise<any> { try { const response = await fetch(`${this.baseURL}/api/ml/optimization/assignment`, { method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${this.getAuthToken()}` }, body: JSON.stringify({ ticket_id: ticketId, constraints: constraints || {} }) }); if (!response.ok) { throw new Error(`Failed to optimize assignment: ${response.statusText}`); } return response.json(); } catch (error) { console.error(' Error optimizing assignment:', error); throw error; } } // Escalation prediction async fetchEscalationPredictions(filters?: { risk_level?: string; time_range?: string; }): Promise<{ predictions: EscalationPrediction[]; high_risk_tickets: EscalationPrediction[]; trends: any; }> { try { const response = await fetch(`${this.baseURL}/api/ml/predictions/escalation`, { method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${this.getAuthToken()}` }, body: JSON.stringify(filters || {}) }); if (!response.ok) { throw new Error(`Failed to fetch escalation predictions: ${response.statusText}`); } return response.json(); } catch (error) { console.error(' Error fetching escalation predictions:', error); throw error; } } async predictTicketEscalation(ticketId: string, context?: any): Promise<EscalationPrediction> { try { const response = await fetch(`${this.baseURL}/api/ml/predictions/escalation/ticket`, { method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${this.getAuthToken()}` }, body: JSON.stringify({ ticket_id: ticketId, context: context || {} }) }); if (!response.ok) { throw new Error(`Failed to predict ticket escalation: ${response.statusText}`); } return response.json(); } catch (error) { console.error(' Error predicting ticket escalation:', error); throw error; } } // Anomaly detection async fetchAnomalyDetection(timeRange: string = '24h'): Promise<{ active_anomalies: AnomalyDetection[]; history: AnomalyDetection[]; system_health_score: number; }> { try { const response = await fetch(`${this.baseURL}/api/ml/anomaly/detect?range=${timeRange}`, { headers: { 'Authorization': `Bearer ${this.getAuthToken()}` } }); if (!response.ok) { throw new Error(`Failed to fetch anomaly detection: ${response.statusText}`); } return response.json(); } catch (error) { console.error(' Error fetching anomaly detection:', error); throw error; } } async acknowledgeAnomaly(anomalyId: string): Promise<void> { try { const response = await fetch(`${this.baseURL}/api/ml/anomaly/acknowledge`, { method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${this.getAuthToken()}` }, body: JSON.stringify({ anomaly_id: anomalyId }) }); if (!response.ok) { throw new Error(`Failed to acknowledge anomaly: ${response.statusText}`); } } catch (error) { console.error(' Error acknowledging anomaly:', error); throw error; } } async resolveAnomaly(anomalyId: string, resolution: string): Promise<void> { try { const response = await fetch(`${this.baseURL}/api/ml/anomaly/resolve`, { method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${this.getAuthToken()}` }, body: JSON.stringify({ anomaly_id: anomalyId, resolution: resolution }) }); if (!response.ok) { throw new Error(`Failed to resolve anomaly: ${response.statusText}`); } } catch (error) { console.error(' Error resolving anomaly:', error); throw error; } } // Model performance and metrics async fetchPredictiveMetrics(): Promise<{ metrics: PredictiveMetrics }> { try { const response = await fetch(`${this.baseURL}/api/ml/metrics/predictive`, { headers: { 'Authorization': `Bearer ${this.getAuthToken()}` } }); if (!response.ok) { throw new Error(`Failed to fetch predictive metrics: ${response.statusText}`); } return response.json(); } catch (error) { console.error(' Error fetching predictive metrics:', error); throw error; } } async retrainModel(modelType: string, parameters?: any): Promise<void> { try { const response = await fetch(`${this.baseURL}/api/ml/models/${modelType}/retrain`, { method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${this.getAuthToken()}` }, body: JSON.stringify(parameters || {}) }); if (!response.ok) { throw new Error(`Failed to retrain model: ${response.statusText}`); } } catch (error) { console.error(' Error retraining model:', error); throw error; } } // Batch operations async refreshAllPredictions(): Promise<void> { try { const promises = [ this.fetchChurnPredictions(), this.fetchDemandForecast(), this.fetchWorkloadOptimization(), this.fetchEscalationPredictions(), this.fetchAnomalyDetection(), this.fetchPredictiveMetrics() ]; await Promise.allSettled(promises); } catch (error) { console.error(' Error refreshing all predictions:', error); throw error; } } // Integration with existing services async integrateWithMLService(data: any): Promise<any> { try { // Integration with existing ML service const response = await fetch(`${this.baseURL}/api/ml/integration/predictive`, { method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${this.getAuthToken()}` }, body: JSON.stringify(data) }); if (!response.ok) { throw new Error(`Failed to integrate with ML service: ${response.statusText}`); } return response.json(); } catch (error) { console.error(' Error integrating with ML service:', error); throw error; } } async integrateWithAnalytics(data: any): Promise<any> { try { // Integration with existing analytics service const response = await fetch(`${this.baseURL}/api/analytics/integration/predictive`, { method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${this.getAuthToken()}` }, body: JSON.stringify(data) }); if (!response.ok) { throw new Error(`Failed to integrate with analytics: ${response.statusText}`); } return response.json(); } catch (error) { console.error(' Error integrating with analytics:', error); throw error; } } // Event handlers private handleMetricsUpdate(metrics: any): void { console.log(' Metrics update received:', metrics); // This would update metrics in the store } // Utility methods private getAuthToken(): string { const state = store.getState(); return state.auth?.token || ''; } // Connection status isWebSocketConnected(): boolean { return this.isConnected; } getConnectionStatus(): string { if (!this.socket) return 'disconnected'; return this.socket.connected ? 'connected' : 'disconnected'; } } // Export singleton instance export const predictiveService = new PredictiveService(); export default predictiveService;