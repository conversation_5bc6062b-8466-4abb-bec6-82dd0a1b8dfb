import api from './api'; import { User, RegisterData, LoginResponse } from '../types'; class AuthService { async login(email: string, password: string): Promise<LoginResponse> { const response = await api.post<LoginResponse>('/auth/login', { email, password, }); if (response.data.token) { localStorage.setItem('token', response.data.token); localStorage.setItem('user', JSON.stringify(response.data.user)); } return response.data; } async register(data: RegisterData): Promise<LoginResponse> { const response = await api.post<LoginResponse>('/auth/register', data); if (response.data.token) { localStorage.setItem('token', response.data.token); localStorage.setItem('user', JSON.stringify(response.data.user)); } return response.data; } async validateToken(): Promise<{ user: User }> { const response = await api.get<{ user: User }>('/auth/validate'); return response.data; } logout(): void { localStorage.removeItem('token'); localStorage.removeItem('user'); } getCurrentUser(): User | null { const userStr = localStorage.getItem('user'); return userStr ? JSON.parse(userStr) : null; } getToken(): string | null { return localStorage.getItem('token'); } isAuthenticated(): boolean { return !!this.getToken(); } } export default new AuthService();