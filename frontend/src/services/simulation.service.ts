/** * ============================================= * SIMULATION SERVICE * WebSocket and HTTP service for agent training simulations * Integrates with existing AI services for realistic scenarios * ============================================= */ import { io, Socket } from 'socket.io-client'; import { store } from '../store'; import { addAISuggestion, addRealTimeFeedback, setSelectedScenario, resetSimulation } from '../store/slices/simulationSlice'; import { SimulationScenario, SimulationSession, CustomerProfile, AISuggestion, AIFeedback } from '../store/slices/simulationSlice'; class SimulationService { private socket: Socket | null = null; private baseURL: string; private isConnected: boolean = false; constructor() { // Use existing backend URL pattern this.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:5000'; } // WebSocket connection for real-time simulation connectWebSocket(): Promise<void> { return new Promise((resolve, reject) => { try { this.socket = io(`${this.baseURL}/simulation`, { transports: ['websocket'], timeout: 10000, }); this.socket.on('connect', () => { console.log(' Simulation WebSocket connected'); this.isConnected = true; resolve(); }); this.socket.on('disconnect', () => { console.log(' Simulation WebSocket disconnected'); this.isConnected = false; }); this.socket.on('connect_error', (error) => { console.error(' Simulation WebSocket connection error:', error); reject(error); }); // Real-time simulation events this.socket.on('customer_message', (data) => { this.handleCustomerMessage(data); }); this.socket.on('ai_suggestion', (suggestion: AISuggestion) => { store.dispatch(addAISuggestion(suggestion)); }); this.socket.on('real_time_feedback', (feedback: AIFeedback) => { store.dispatch(addRealTimeFeedback(feedback)); }); this.socket.on('simulation_ended', (data) => { this.handleSimulationEnd(data); }); this.socket.on('performance_update', (metrics) => { this.handlePerformanceUpdate(metrics); }); } catch (error) { console.error(' Failed to initialize simulation WebSocket:', error); reject(error); } }); } disconnectWebSocket(): void { if (this.socket) { this.socket.disconnect(); this.socket = null; this.isConnected = false; } } // Scenario management - Updated to use dashboard endpoints async fetchScenarios(filters?: { difficulty?: string; category?: string; tags?: string[]; }): Promise<SimulationScenario[]> { try { // Build query parameters for dashboard endpoint const params = new URLSearchParams(); if (filters?.difficulty) params.append('difficulty', filters.difficulty); if (filters?.category) params.append('category', filters.category); if (filters?.tags?.length) params.append('tags', filters.tags.join(',')); const queryString = params.toString(); const url = `${this.baseURL}/api/dashboard/simulations${queryString ? `?${queryString}` : ''}`; const response = await fetch(url, { method: 'GET', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${this.getAuthToken()}` } }); if (!response.ok) { throw new Error(`Failed to fetch scenarios: ${response.statusText}`); } const data = await response.json(); console.log(' Dashboard scenarios response:', data); // Return scenarios in expected format return data.success ? data.data.scenarios : []; } catch (error) { console.error(' Error fetching scenarios:', error); throw error; } } async createCustomScenario(scenario: Partial<SimulationScenario>): Promise<SimulationScenario> { try { const response = await fetch(`${this.baseURL}/api/simulation/scenarios`, { method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${this.getAuthToken()}` }, body: JSON.stringify(scenario) }); if (!response.ok) { throw new Error(`Failed to create scenario: ${response.statusText}`); } return response.json(); } catch (error) { console.error(' Error creating scenario:', error); throw error; } } // Session management async startSimulation(scenarioId: string): Promise<SimulationSession> { try { const response = await fetch(`${this.baseURL}/api/simulation/start`, { method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${this.getAuthToken()}` }, body: JSON.stringify({ scenario_id: scenarioId, agent_id: this.getCurrentAgentId() }) }); if (!response.ok) { throw new Error(`Failed to start simulation: ${response.statusText}`); } const session = await response.json(); // Join simulation room for real-time updates if (this.socket && this.isConnected) { this.socket.emit('join_simulation', { session_id: session.id }); } return session; } catch (error) { console.error(' Error starting simulation:', error); throw error; } } async sendMessage(sessionId: string, message: string): Promise<void> { try { if (this.socket && this.isConnected) { // Send via WebSocket for real-time response this.socket.emit('agent_message', { session_id: sessionId, message: message, timestamp: new Date().toISOString() }); } else { // Fallback to HTTP const response = await fetch(`${this.baseURL}/api/simulation/message`, { method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${this.getAuthToken()}` }, body: JSON.stringify({ session_id: sessionId, message: message }) }); if (!response.ok) { throw new Error(`Failed to send message: ${response.statusText}`); } } } catch (error) { console.error(' Error sending message:', error); throw error; } } async endSimulation(sessionId: string): Promise<any> { try { const response = await fetch(`${this.baseURL}/api/simulation/end`, { method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${this.getAuthToken()}` }, body: JSON.stringify({ session_id: sessionId }) }); if (!response.ok) { throw new Error(`Failed to end simulation: ${response.statusText}`); } const result = await response.json(); // Leave simulation room if (this.socket && this.isConnected) { this.socket.emit('leave_simulation', { session_id: sessionId }); } return result; } catch (error) { console.error(' Error ending simulation:', error); throw error; } } async pauseSimulation(sessionId: string): Promise<void> { try { if (this.socket && this.isConnected) { this.socket.emit('pause_simulation', { session_id: sessionId }); } } catch (error) { console.error(' Error pausing simulation:', error); throw error; } } async resumeSimulation(sessionId: string): Promise<void> { try { if (this.socket && this.isConnected) { this.socket.emit('resume_simulation', { session_id: sessionId }); } } catch (error) { console.error(' Error resuming simulation:', error); throw error; } } // Progress and analytics - Updated to use dashboard endpoints async fetchAgentProgress(agentId?: string): Promise<any> { try { const id = agentId || this.getCurrentAgentId(); const response = await fetch(`${this.baseURL}/api/dashboard/metrics/${id}`, { headers: { 'Authorization': `Bearer ${this.getAuthToken()}` } }); if (!response.ok) { throw new Error(`Failed to fetch progress: ${response.statusText}`); } const data = await response.json(); console.log(' Dashboard agent progress response:', data); return data.success ? data.data : {}; } catch (error) { console.error(' Error fetching progress:', error); throw error; } } async fetchLeaderboard(params?: { timeframe?: string; limit?: number }): Promise<any> { try { const queryParams = new URLSearchParams(); if (params?.timeframe) queryParams.append('timeframe', params.timeframe); if (params?.limit) queryParams.append('limit', params.limit.toString()); const queryString = queryParams.toString(); const url = `${this.baseURL}/api/dashboard/leaderboard${queryString ? `?${queryString}` : ''}`; const response = await fetch(url, { headers: { 'Authorization': `Bearer ${this.getAuthToken()}` } }); if (!response.ok) { throw new Error(`Failed to fetch leaderboard: ${response.statusText}`); } const data = await response.json(); console.log(' Dashboard leaderboard response:', data); return data.success ? data.data : {}; } catch (error) { console.error(' Error fetching leaderboard:', error); throw error; } } // Session history - New method using dashboard endpoints async fetchSessionHistory(params?: { status?: string; limit?: number; page?: number; agent_id?: string }): Promise<any> { try { const queryParams = new URLSearchParams(); if (params?.status) queryParams.append('status', params.status); if (params?.limit) queryParams.append('limit', params.limit.toString()); if (params?.page) queryParams.append('page', params.page.toString()); if (params?.agent_id) queryParams.append('agent_id', params.agent_id); const queryString = queryParams.toString(); const url = `${this.baseURL}/api/dashboard/sessions${queryString ? `?${queryString}` : ''}`; const response = await fetch(url, { headers: { 'Authorization': `Bearer ${this.getAuthToken()}` } }); if (!response.ok) { throw new Error(`Failed to fetch session history: ${response.statusText}`); } const data = await response.json(); console.log(' Dashboard session history response:', data); return data.success ? data.data : {}; } catch (error) { console.error(' Error fetching session history:', error); throw error; } } // Dashboard analytics - New method using dashboard endpoints async fetchDashboardAnalytics(params?: { timeframe?: string }): Promise<any> { try { const queryParams = new URLSearchParams(); if (params?.timeframe) queryParams.append('timeframe', params.timeframe); const queryString = queryParams.toString(); const url = `${this.baseURL}/api/dashboard/analytics${queryString ? `?${queryString}` : ''}`; const response = await fetch(url, { headers: { 'Authorization': `Bearer ${this.getAuthToken()}` } }); if (!response.ok) { throw new Error(`Failed to fetch dashboard analytics: ${response.statusText}`); } const data = await response.json(); console.log(' Dashboard analytics response:', data); return data.success ? data.data : {}; } catch (error) { console.error(' Error fetching dashboard analytics:', error); throw error; } } async submitFeedback(sessionId: string, feedback: any): Promise<void> { try { const response = await fetch(`${this.baseURL}/api/simulation/feedback`, { method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${this.getAuthToken()}` }, body: JSON.stringify({ session_id: sessionId, feedback: feedback }) }); if (!response.ok) { throw new Error(`Failed to submit feedback: ${response.statusText}`); } } catch (error) { console.error(' Error submitting feedback:', error); throw error; } } // AI Integration async requestAISuggestion(context: any): Promise<AISuggestion> { try { const response = await fetch(`${this.baseURL}/api/simulation/ai-suggestion`, { method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${this.getAuthToken()}` }, body: JSON.stringify(context) }); if (!response.ok) { throw new Error(`Failed to get AI suggestion: ${response.statusText}`); } return response.json(); } catch (error) { console.error(' Error requesting AI suggestion:', error); throw error; } } // Event handlers private handleCustomerMessage(data: any): void { // Handle incoming customer message in simulation console.log(' Customer message received:', data); // This would update the current session in the store } private handleSimulationEnd(data: any): void { console.log(' Simulation ended:', data); store.dispatch(resetSimulation()); } private handlePerformanceUpdate(metrics: any): void { console.log(' Performance update:', metrics); // This would update performance metrics in the store } // Utility methods private getAuthToken(): string { const state = store.getState(); return state.auth?.token || ''; } private getCurrentAgentId(): string { const state = store.getState(); return state.auth?.user?.id || ''; } // Integration with existing AI services async integrateWithMultimodal(sessionId: string, data: any): Promise<void> { try { // Integration with existing multimodal service const response = await fetch(`${this.baseURL}/api/simulation/multimodal-integration`, { method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${this.getAuthToken()}` }, body: JSON.stringify({ session_id: sessionId, multimodal_data: data }) }); if (!response.ok) { throw new Error(`Failed to integrate with multimodal: ${response.statusText}`); } } catch (error) { console.error(' Error integrating with multimodal:', error); throw error; } } async integrateWithMLService(sessionId: string, data: any): Promise<void> { try { // Integration with existing ML service const response = await fetch(`${this.baseURL}/api/simulation/ml-integration`, { method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${this.getAuthToken()}` }, body: JSON.stringify({ session_id: sessionId, ml_data: data }) }); if (!response.ok) { throw new Error(`Failed to integrate with ML service: ${response.statusText}`); } } catch (error) { console.error(' Error integrating with ML service:', error); throw error; } } // Connection status isWebSocketConnected(): boolean { return this.isConnected; } getConnectionStatus(): string { if (!this.socket) return 'disconnected'; return this.socket.connected ? 'connected' : 'disconnected'; } } // Export singleton instance export const simulationService = new SimulationService(); export default simulationService;