import apiService from './api';

// Types for analytics data
export interface VolumeData {
  date: string;
  total: number;
  resolved: number;
  pending: number;
  escalated: number;
}

export interface CategoryData {
  category: string;
  count: number;
  percentage: number;
  color: string;
}

export interface PerformanceData {
  date: string;
  satisfaction: number;
  responseTime: number;
  resolutionRate: number;
  escalationRate: number;
}

export interface DashboardAnalyticsResponse {
  volume: VolumeData[];
  categories: CategoryData[];
  performance: PerformanceData[];
  summary: {
    totalTickets: number;
    avgSatisfaction: number;
    avgResponseTime: number;
    resolutionRate: number;
  };
}

class AnalyticsService {
  /**
   * Get dashboard analytics data
   */
  async getDashboardAnalytics(params: any = {}) {
    const response = await apiService.get('/analytics/dashboard', { params });
    return response;
  }

  /**
   * Get conversation analytics
   */
  async getConversationAnalytics(params: any = {}) {
    const response = await apiService.get('/admin/analytics/conversations', { params });
    return response;
  }

  /**
   * Get agent performance data
   */
  async getAgentPerformance(params: any = {}) {
    const response = await apiService.get('/admin/analytics/agents', { params });
    return response;
  }

  /**
   * Get customer satisfaction data
   */
  async getCustomerSatisfaction(params: any = {}) {
    const response = await apiService.get('/admin/analytics/satisfaction', { params });
    return response;
  }

  /**
   * Get business metrics
   */
  async getBusinessMetrics(params: any = {}) {
    const response = await apiService.get('/admin/analytics/business', { params });
    return response;
  }

  /**
   * Get real-time metrics
   */
  async getRealtimeMetrics() {
    const response = await apiService.get('/analytics/realtime');
    return response;
  }

  /**
   * Get agent performance real-time
   */
  async getAgentPerformanceRealtime(params: any = {}) {
    const response = await apiService.get('/analytics/agent-performance-realtime', { params });
    return response;
  }

  /**
   * Get customer insights real-time
   */
  async getCustomerInsightsRealtime(params: any = {}) {
    const response = await apiService.get('/analytics/customer-insights-realtime', { params });
    return response;
  }

  /**
   * Get operational efficiency real-time
   */
  async getOperationalEfficiencyRealtime(params: any = {}) {
    const response = await apiService.get('/analytics/operational-efficiency-realtime', { params });
    return response;
  }

  /**
   * Trigger metrics update
   */
  async triggerMetricsUpdate() {
    const response = await apiService.post('/analytics/trigger-update');
    return response;
  }

  /**
   * Export analytics report
   */
  async exportReport(params: any = {}) {
    const response = await apiService.post('/admin/analytics/export', params);
    return response;
  }

  /**
   * Get volume data for charts
   */
  async getVolumeData(timeRange: string): Promise<VolumeData[]> {
    try {
      const response = await this.getDashboardAnalytics({ timeRange });
      return response.data.volume || [];
    } catch (error) {
      console.error('Error fetching volume data:', error);
      return [];
    }
  }

  /**
   * Get category distribution data
   */
  async getCategoryData(): Promise<CategoryData[]> {
    try {
      const response = await this.getDashboardAnalytics();
      return response.data.categories || [];
    } catch (error) {
      console.error('Error fetching category data:', error);
      return [];
    }
  }

  /**
   * Get performance trends data
   */
  async getPerformanceTrends(timeRange: string): Promise<PerformanceData[]> {
    try {
      const response = await this.getDashboardAnalytics({ timeRange });
      return response.data.performance || [];
    } catch (error) {
      console.error('Error fetching performance trends:', error);
      return [];
    }
  }
}

export default new AnalyticsService();