/** * ============================================= * EMERGENCY CALL SERVICE * Frontend service for emergency call API communication * Handles WebSocket connections and real-time updates * ============================================= */ import axios, { AxiosResponse } from 'axios'; import { io, Socket } from 'socket.io-client'; import { store } from '../store'; import { updateCallStatus, updateQueuePosition, setAgentConnected, setConnectionStatus } from '../store/slices/emergencyCallSlice'; const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000'; const EMERGENCY_API_URL = `${API_BASE_URL}/api/emergency-calls`; // Create axios instance with auth interceptor const emergencyApi = axios.create({ baseURL: EMERGENCY_API_URL, timeout: 30000, // 30 seconds timeout for emergency calls headers: { 'Content-Type': 'application/json' } }); // Add auth token to requests emergencyApi.interceptors.request.use((config) => { const token = localStorage.getItem('authToken'); if (token) { config.headers.Authorization = `Bearer ${token}`; } return config; }); // Response interceptor for error handling emergencyApi.interceptors.response.use( (response) => response, (error) => { console.error('Emergency Call API Error:', error.response?.data || error.message); // Handle specific emergency call errors if (error.response?.status === 429) { // Rate limit exceeded throw new Error('Trop d\'appels d\'urgence récents. Veuillez patienter.'); } else if (error.response?.status === 503) { // Service unavailable throw new Error('Service d\'urgence temporairement indisponible. Appelez le 9198.'); } return Promise.reject(error); } ); // Types interface InitiateEmergencyCallParams { userId: string; urgencyLevel: string; description: string; conversationHistory?: Array<{ content: string; sender: 'user' | 'bot' | 'agent'; timestamp: Date; }>; } interface EscalateCallParams { emergencyCallId: string; reason?: string; agentPreference?: string; } interface ConnectToAgentParams { emergencyCallId: string; agentId: string; } class EmergencyCallService { private socket: Socket | null = null; private isConnected: boolean = false; /** * Initialize WebSocket connection for real-time updates */ initializeSocket(userId: string): void { if (this.socket?.connected) { return; } this.socket = io(API_BASE_URL, { auth: { token: localStorage.getItem('authToken'), userId }, transports: ['websocket', 'polling'], timeout: 10000 }); // Connection events this.socket.on('connect', () => { console.log(' Connected to emergency call service'); this.isConnected = true; store.dispatch(setConnectionStatus(true)); }); this.socket.on('disconnect', () => { console.log(' Disconnected from emergency call service'); this.isConnected = false; store.dispatch(setConnectionStatus(false)); }); this.socket.on('connect_error', (error) => { console.error(' Emergency call service connection error:', error); this.isConnected = false; store.dispatch(setConnectionStatus(false)); }); // Emergency call events this.socket.on('emergency-call-initiated', (data) => { console.log(' Emergency call initiated:', data); store.dispatch(updateCallStatus({ emergencyCallId: data.emergencyCallId, status: data.status, data: data.routing })); }); this.socket.on('emergency-call-escalated', (data) => { console.log(' Emergency call escalated:', data); store.dispatch(updateCallStatus({ emergencyCallId: data.emergencyCallId, status: 'escalated' })); if (data.queuePosition && data.estimatedWaitTime) { store.dispatch(updateQueuePosition({ emergencyCallId: data.emergencyCallId, position: data.queuePosition, estimatedWaitTime: data.estimatedWaitTime })); } }); this.socket.on('emergency-call-queued', (data) => { console.log(' Emergency call queued:', data); store.dispatch(updateQueuePosition({ emergencyCallId: data.emergencyCallId, position: data.queuePosition, estimatedWaitTime: data.estimatedWaitTime })); }); this.socket.on('emergency-call-connected', (data) => { console.log(' Emergency call connected to agent:', data); store.dispatch(setAgentConnected({ emergencyCallId: data.emergencyCallId, agentInfo: data.agentInfo, webrtcSessionId: data.webrtcSessionId })); }); this.socket.on('emergency-call-assigned', (data) => { console.log(' Emergency call assigned (agent view):', data); // This event is primarily for agents }); this.socket.on('emergency-ai-assistance-started', (data) => { console.log(' Emergency AI assistance started:', data); store.dispatch(updateCallStatus({ emergencyCallId: data.emergencyCallId, status: 'ai_assistance', data: { assistanceType: data.assistanceType } })); }); // Queue updates this.socket.on('queue-position-updated', (data) => { store.dispatch(updateQueuePosition({ emergencyCallId: data.emergencyCallId, position: data.position, estimatedWaitTime: data.estimatedWaitTime })); }); } /** * Disconnect WebSocket */ disconnectSocket(): void { if (this.socket) { this.socket.disconnect(); this.socket = null; this.isConnected = false; store.dispatch(setConnectionStatus(false)); } } /** * Initiate emergency call */ async initiateEmergencyCall(params: InitiateEmergencyCallParams): Promise<AxiosResponse> { try { // Initialize socket if not connected if (!this.isConnected) { this.initializeSocket(params.userId); } const response = await emergencyApi.post('/initiate', params); // Join emergency call room for real-time updates if (this.socket && response.data.emergencyCallId) { this.socket.emit('join-emergency-call', { emergencyCallId: response.data.emergencyCallId, userId: params.userId }); } return response; } catch (error) { console.error('Failed to initiate emergency call:', error); throw error; } } /** * Escalate emergency call to human agent */ async escalateToHumanAgent(params: EscalateCallParams): Promise<AxiosResponse> { try { const response = await emergencyApi.post(`/${params.emergencyCallId}/escalate`, { reason: params.reason, agentPreference: params.agentPreference }); return response; } catch (error) { console.error('Failed to escalate emergency call:', error); throw error; } } /** * Connect emergency call to specific agent (agent-only) */ async connectToHumanAgent(params: ConnectToAgentParams): Promise<AxiosResponse> { try { const response = await emergencyApi.post(`/${params.emergencyCallId}/connect-agent`, { agentId: params.agentId }); return response; } catch (error) { console.error('Failed to connect to human agent:', error); throw error; } } /** * Get emergency call status */ async getEmergencyCallStatus(emergencyCallId: string): Promise<AxiosResponse> { try { const response = await emergencyApi.get(`/${emergencyCallId}/status`); return response; } catch (error) { console.error('Failed to get emergency call status:', error); throw error; } } /** * Get queue statistics (agent/admin only) */ async getQueueStats(): Promise<AxiosResponse> { try { const response = await emergencyApi.get('/queue/stats'); return response; } catch (error) { console.error('Failed to get queue statistics:', error); throw error; } } /** * End emergency call */ async endEmergencyCall(emergencyCallId: string, reason?: string): Promise<void> { try { // Notify server about call end if (this.socket) { this.socket.emit('end-emergency-call', { emergencyCallId, reason: reason || 'User ended call' }); } // Leave emergency call room if (this.socket) { this.socket.emit('leave-emergency-call', { emergencyCallId }); } } catch (error) { console.error('Failed to end emergency call:', error); throw error; } } /** * Check service health */ async checkServiceHealth(): Promise<AxiosResponse> { try { const response = await emergencyApi.get('/health'); return response; } catch (error) { console.error('Emergency call service health check failed:', error); throw error; } } /** * Get connection status */ isSocketConnected(): boolean { return this.isConnected && this.socket?.connected === true; } /** * Emit custom event to socket */ emitEvent(eventName: string, data: any): void { if (this.socket?.connected) { this.socket.emit(eventName, data); } } /** * Listen to custom socket event */ onEvent(eventName: string, callback: (data: any) => void): void { if (this.socket) { this.socket.on(eventName, callback); } } /** * Remove socket event listener */ offEvent(eventName: string, callback?: (data: any) => void): void { if (this.socket) { if (callback) { this.socket.off(eventName, callback); } else { this.socket.off(eventName); } } } } // Export singleton instance export const emergencyCallService = new EmergencyCallService(); export default emergencyCallService;