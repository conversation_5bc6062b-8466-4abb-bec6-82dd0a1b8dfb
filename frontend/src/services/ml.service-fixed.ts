/**
 * Service ML pour les appels API
 * Free Mobile Chatbot Dashboard - Phase 3 Frontend Implementation
 */

import axios, { AxiosResponse } from 'axios';

// Simplified types for now
interface ClassifyConversationRequest {
  conversationId: string;
  forceReprocess?: boolean;
}

interface ClassifyConversationResponse {
  success: boolean;
  classification: any;
}

interface PriorityQueueRequest {
  limit?: number;
  minPriority?: number;
  category?: string;
  status?: string;
}

interface PriorityQueueResponse {
  success: boolean;
  queue: any[];
}

interface PerformanceMetricsRequest {
  timeRange?: '1h' | '24h' | '7d' | '30d';
}

interface PerformanceMetricsResponse {
  success: boolean;
  mlService: any;
  classifications?: any;
  alerts?: any;
  timestamp: string;
}

interface ValidateClassificationRequest {
  isCorrect: boolean;
  correctedCategory?: string;
  feedback?: string;
  confidence?: number;
}

interface ValidateClassificationResponse {
  success: boolean;
  classification: any;
}

interface AlertsRequest {
  status?: string;
  severity?: string;
  type?: string;
  assignedTo?: string;
  limit?: number;
}

interface AlertsResponse {
  success: boolean;
  alerts: any[];
}

interface AcknowledgeAlertResponse {
  success: boolean;
  alert: any;
}

class MLService {
  private baseURL: string;
  private apiVersion: string = 'v2';

  constructor() {
    this.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:5000';
  }

  /**
   * Configuration des headers avec authentification
   */
  private getAuthHeaders() {
    const token = localStorage.getItem('token');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` })
    };
  }

  /**
   * Gestion des erreurs API
   */
  private handleError(error: any): never {
    if (error.response) {
      // Erreur de réponse du serveur
      const message = error.response.data?.error || error.response.data?.message || 'Erreur serveur';
      throw new Error(`${error.response.status}: ${message}`);
    } else if (error.request) {
      // Erreur de réseau
      throw new Error('Erreur de connexion au serveur ML');
    } else {
      // Autre erreur
      throw new Error(error.message || 'Erreur inconnue');
    }
  }

  /**
   * Classification d'une conversation
   */
  async classifyConversation(request: ClassifyConversationRequest): Promise<ClassifyConversationResponse> {
    try {
      const response: AxiosResponse<ClassifyConversationResponse> = await axios.post(
        `${this.baseURL}/api/${this.apiVersion}/ml/classify`,
        request,
        { headers: this.getAuthHeaders() }
      );
      return response.data;
    } catch (error) {
      this.handleError(error);
    }
  }

  /**
   * Récupération de la queue de priorité
   */
  async getPriorityQueue(request: PriorityQueueRequest = {}): Promise<PriorityQueueResponse> {
    try {
      const params = new URLSearchParams();
      if (request.limit) params.append('limit', request.limit.toString());
      if (request.minPriority) params.append('minPriority', request.minPriority.toString());
      if (request.category) params.append('category', request.category);
      if (request.status) params.append('status', request.status);

      const response: AxiosResponse<PriorityQueueResponse> = await axios.get(
        `${this.baseURL}/api/${this.apiVersion}/ml/queue/priority?${params.toString()}`,
        { headers: this.getAuthHeaders() }
      );
      return response.data;
    } catch (error) {
      this.handleError(error);
    }
  }

  /**
   * Récupération des métriques de performance
   */
  async getPerformanceMetrics(request: PerformanceMetricsRequest = {}): Promise<PerformanceMetricsResponse> {
    try {
      const params = new URLSearchParams();
      if (request.timeRange) params.append('timeRange', request.timeRange);

      const response: AxiosResponse<PerformanceMetricsResponse> = await axios.get(
        `${this.baseURL}/api/${this.apiVersion}/ml/metrics/performance?${params.toString()}`,
        { headers: this.getAuthHeaders() }
      );
      return response.data;
    } catch (error) {
      this.handleError(error);
    }
  }

  /**
   * Validation humaine d'une classification
   */
  async validateClassification(
    classificationId: string,
    request: ValidateClassificationRequest
  ): Promise<ValidateClassificationResponse> {
    try {
      const response: AxiosResponse<ValidateClassificationResponse> = await axios.put(
        `${this.baseURL}/api/${this.apiVersion}/ml/classifications/${classificationId}/validate`,
        request,
        { headers: this.getAuthHeaders() }
      );
      return response.data;
    } catch (error) {
      this.handleError(error);
    }
  }

  /**
   * Récupération des alertes
   */
  async getAlerts(request: AlertsRequest = {}): Promise<AlertsResponse> {
    try {
      const params = new URLSearchParams();
      if (request.status) params.append('status', request.status);
      if (request.severity) params.append('severity', request.severity);
      if (request.type) params.append('type', request.type);
      if (request.assignedTo) params.append('assignedTo', request.assignedTo);
      if (request.limit) params.append('limit', request.limit.toString());

      const response: AxiosResponse<AlertsResponse> = await axios.get(
        `${this.baseURL}/api/${this.apiVersion}/ml/alerts?${params.toString()}`,
        { headers: this.getAuthHeaders() }
      );
      return response.data;
    } catch (error) {
      this.handleError(error);
    }
  }

  /**
   * Acquittement d'une alerte
   */
  async acknowledgeAlert(alertId: string): Promise<AcknowledgeAlertResponse> {
    try {
      const response: AxiosResponse<AcknowledgeAlertResponse> = await axios.put(
        `${this.baseURL}/api/${this.apiVersion}/ml/alerts/${alertId}/acknowledge`,
        {},
        { headers: this.getAuthHeaders() }
      );
      return response.data;
    } catch (error) {
      this.handleError(error);
    }
  }

  /**
   * Vérification de la santé du service ML
   */
  async healthCheck(): Promise<{
    success: boolean;
    health: {
      status: string;
      ml_service?: any;
      error?: string;
      timestamp: string;
    };
  }> {
    try {
      const response = await axios.get(
        `${this.baseURL}/api/${this.apiVersion}/ml/health`,
        { headers: this.getAuthHeaders() }
      );
      return response.data;
    } catch (error) {
      this.handleError(error);
    }
  }

  /**
   * Utilitaires pour les catégories
   */
  getCategoryColor(category: string): string {
    const colors: { [key: string]: string } = {
      'VENTE_OPPORTUNITE': '#4caf50',
      'RESILIATION_CRITIQUE': '#f44336',
      'SUPPORT_URGENT': '#ff9800',
      'RECLAMATION': '#ff5722',
      'INFO_SIMPLE': '#2196f3'
    };
    return colors[category] || '#9e9e9e';
  }

  getCategoryLabel(category: string): string {
    const labels: { [key: string]: string } = {
      'VENTE_OPPORTUNITE': 'Opportunité de Vente',
      'RESILIATION_CRITIQUE': 'Résiliation Critique',
      'SUPPORT_URGENT': 'Support Urgent',
      'RECLAMATION': 'Réclamation',
      'INFO_SIMPLE': 'Information Simple'
    };
    return labels[category] || category;
  }

  getSeverityColor(severity: string): string {
    const colors: { [key: string]: string } = {
      'LOW': '#4caf50',
      'MEDIUM': '#ff9800',
      'HIGH': '#ff5722',
      'CRITICAL': '#f44336'
    };
    return colors[severity] || '#9e9e9e';
  }

  getSeverityLabel(severity: string): string {
    const labels: { [key: string]: string } = {
      'LOW': 'Faible',
      'MEDIUM': 'Moyen',
      'HIGH': 'Élevé',
      'CRITICAL': 'Critique'
    };
    return labels[severity] || severity;
  }
}

const mlService = new MLService();
export default mlService;
