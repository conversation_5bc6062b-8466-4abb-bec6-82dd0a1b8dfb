import api from './api'; import { CustomerProfile, Invoice, Notification } from '../types'; class CustomerService { async getCustomerProfile(): Promise<CustomerProfile> { const response = await api.get<CustomerProfile>('/customer/profile'); return response.data; } // Alias methods for backward compatibility async getProfile(): Promise<{ data: CustomerProfile }> { const data = await this.getCustomerProfile(); return { data }; } async updateCustomerProfile(data: Partial<CustomerProfile>): Promise<CustomerProfile> { const response = await api.put<CustomerProfile>('/customer/profile', data); return response.data; } async updateProfile(data: Partial<CustomerProfile>): Promise<{ data: CustomerProfile }> { const result = await this.updateCustomerProfile(data); return { data: result }; } async getConsumption(): Promise<{ data: any }> { const response = await api.get('/customer/consumption'); return { data: response.data }; } async getInvoices(): Promise<Invoice[]> { const response = await api.get<Invoice[]>('/customer/invoices'); return response.data; } async getNotifications(): Promise<Notification[]> { const response = await api.get<Notification[]>('/customer/notifications'); return response.data; } async markNotificationAsRead(notificationId: string): Promise<void> { await api.put(`/customer/notifications/${notificationId}/read`); } async changePlan(planId: string): Promise<{ success: boolean; newPlan: any }> { const response = await api.post(`/customer/plan/change`, { planId }); return response.data; } async activateOption(optionId: string): Promise<{ success: boolean; option: any }> { const response = await api.post(`/customer/options/activate`, { optionId }); return response.data; } async deactivateOption(optionId: string): Promise<{ success: boolean; optionName: string }> { const response = await api.post(`/customer/options/deactivate`, { optionId }); return response.data; } } const customerService = new CustomerService(); export default customerService;