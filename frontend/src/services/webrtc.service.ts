/** * ============================================= * WEBRTC SERVICE * WebRTC functionality for voice/video calls * Handles peer connections and media streams * ============================================= */ import { io, Socket } from 'socket.io-client'; interface WebRTCConfig { iceServers: RTCIceServer[]; socketUrl: string; } interface MediaConstraints { audio: boolean; video: boolean; } class WebRTCService { private socket: Socket | null = null; private peerConnection: RTCPeerConnection | null = null; private localStream: MediaStream | null = null; private remoteStream: MediaStream | null = null; private config: WebRTCConfig; private isInitialized = false; constructor() { this.config = { iceServers: [ { urls: 'stun:stun.l.google.com:19302' }, { urls: 'stun:stun1.l.google.com:19302' } ], socketUrl: process.env.REACT_APP_WEBRTC_SOCKET_URL || 'http://localhost:5004' }; } /** * Initialize WebRTC service */ async initialize(): Promise<void> { if (this.isInitialized) return; try { // Initialize socket connection this.socket = io(this.config.socketUrl, { transports: ['websocket'], autoConnect: false }); this.setupSocketListeners(); this.isInitialized = true; } catch (error) { console.error('Failed to initialize WebRTC service:', error); throw error; } } /** * Start a call with media constraints */ async startCall(callId: string, constraints: MediaConstraints = { audio: true, video: false }): Promise<void> { if (!this.isInitialized) { await this.initialize(); } try { // Get user media this.localStream = await navigator.mediaDevices.getUserMedia(constraints); // Create peer connection this.createPeerConnection(); // Add local stream to peer connection if (this.localStream && this.peerConnection) { this.localStream.getTracks().forEach(track => { this.peerConnection!.addTrack(track, this.localStream!); }); } // Connect socket this.socket?.connect(); this.socket?.emit('join-call', { callId }); } catch (error) { console.error('Failed to start call:', error); throw error; } } /** * End the current call */ async endCall(): Promise<void> { try { // Stop local stream if (this.localStream) { this.localStream.getTracks().forEach(track => track.stop()); this.localStream = null; } // Close peer connection if (this.peerConnection) { this.peerConnection.close(); this.peerConnection = null; } // Disconnect socket if (this.socket) { this.socket.emit('leave-call'); this.socket.disconnect(); } this.remoteStream = null; } catch (error) { console.error('Failed to end call:', error); throw error; } } /** * Toggle mute state */ toggleMute(callId: string, isMuted: boolean): void { if (this.localStream) { const audioTrack = this.localStream.getAudioTracks()[0]; if (audioTrack) { audioTrack.enabled = !isMuted; this.socket?.emit('toggle-mute', { callId, isMuted }); } } } /** * Toggle speaker state */ toggleSpeaker(callId: string, isSpeakerOn: boolean): void { // This would typically control audio output routing // Implementation depends on the specific audio system this.socket?.emit('toggle-speaker', { callId, isSpeakerOn }); } /** * Toggle video state */ toggleVideo(callId: string, isVideoEnabled: boolean): void { if (this.localStream) { const videoTrack = this.localStream.getVideoTracks()[0]; if (videoTrack) { videoTrack.enabled = isVideoEnabled; this.socket?.emit('toggle-video', { callId, isVideoEnabled }); } } } /** * Get local stream */ getLocalStream(): MediaStream | null { return this.localStream; } /** * Get remote stream */ getRemoteStream(): MediaStream | null { return this.remoteStream; } /** * Create peer connection */ private createPeerConnection(): void { this.peerConnection = new RTCPeerConnection({ iceServers: this.config.iceServers }); // Handle remote stream this.peerConnection.ontrack = (event) => { this.remoteStream = event.streams[0]; }; // Handle ICE candidates this.peerConnection.onicecandidate = (event) => { if (event.candidate) { this.socket?.emit('ice-candidate', event.candidate); } }; // Handle connection state changes this.peerConnection.onconnectionstatechange = () => { console.log('Connection state:', this.peerConnection?.connectionState); }; } /** * Setup socket event listeners */ private setupSocketListeners(): void { if (!this.socket) return; this.socket.on('offer', async (offer: RTCSessionDescriptionInit) => { if (!this.peerConnection) return; try { await this.peerConnection.setRemoteDescription(offer); const answer = await this.peerConnection.createAnswer(); await this.peerConnection.setLocalDescription(answer); this.socket?.emit('answer', answer); } catch (error) { console.error('Failed to handle offer:', error); } }); this.socket.on('answer', async (answer: RTCSessionDescriptionInit) => { if (!this.peerConnection) return; try { await this.peerConnection.setRemoteDescription(answer); } catch (error) { console.error('Failed to handle answer:', error); } }); this.socket.on('ice-candidate', async (candidate: RTCIceCandidateInit) => { if (!this.peerConnection) return; try { await this.peerConnection.addIceCandidate(candidate); } catch (error) { console.error('Failed to add ICE candidate:', error); } }); this.socket.on('call-ended', () => { this.endCall(); }); this.socket.on('participant-muted', (data: { userId: string; isMuted: boolean }) => { console.log('Participant muted:', data); }); this.socket.on('participant-video-toggled', (data: { userId: string; isVideoEnabled: boolean }) => { console.log('Participant video toggled:', data); }); this.socket.on('connect', () => { console.log('WebRTC socket connected'); }); this.socket.on('disconnect', () => { console.log('WebRTC socket disconnected'); }); this.socket.on('error', (error: any) => { console.error('WebRTC socket error:', error); }); } /** * Create and send offer */ async createOffer(): Promise<void> { if (!this.peerConnection) return; try { const offer = await this.peerConnection.createOffer(); await this.peerConnection.setLocalDescription(offer); this.socket?.emit('offer', offer); } catch (error) { console.error('Failed to create offer:', error); throw error; } } /** * Check if WebRTC is supported */ static isSupported(): boolean { return !!( navigator.mediaDevices && typeof navigator.mediaDevices.getUserMedia === 'function' && window.RTCPeerConnection ); } /** * Get available media devices */ static async getMediaDevices(): Promise<MediaDeviceInfo[]> { try { const devices = await navigator.mediaDevices.enumerateDevices(); return devices; } catch (error) { console.error('Failed to get media devices:', error); return []; } } } // Export singleton instance export const webrtcService = new WebRTCService(); export default webrtcService;