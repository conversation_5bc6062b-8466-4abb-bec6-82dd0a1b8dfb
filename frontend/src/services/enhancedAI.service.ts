/** * ============================================= * [FEATURE] ENHANCED AI SERVICE * Advanced AI-powered suggestions and contextual intelligence * Extends existing AI services with personalization and learning * ============================================= */ import { io, Socket } from 'socket.io-client'; import { store } from '../store'; import { addContextualSuggestion, updateSentimentAnalysis, updateContext, clearError } from '../store/slices/enhancedAISlice'; import { ContextualSuggestion, SuggestionContext, SentimentAnalysis, EscalationRecommendation, ResponseTemplate, SuggestionFeedback } from '../store/slices/enhancedAISlice'; class EnhancedAIService { private socket: Socket | null = null; private baseURL: string; private isConnected: boolean = false; private contextUpdateInterval: NodeJS.Timeout | null = null; constructor() { // Use existing AI service URL pattern this.baseURL = process.env.REACT_APP_AI_API_URL || 'http://localhost:5001'; } // WebSocket connection for real-time AI suggestions connectWebSocket(): Promise<void> { return new Promise((resolve, reject) => { try { this.socket = io(`${this.baseURL}/enhanced-ai`, { transports: ['websocket'], timeout: 10000, }); this.socket.on('connect', () => { console.log('[FEATURE] Enhanced AI WebSocket connected'); this.isConnected = true; resolve(); }); this.socket.on('disconnect', () => { console.log('[FEATURE] Enhanced AI WebSocket disconnected'); this.isConnected = false; }); this.socket.on('connect_error', (error) => { console.error('[FEATURE] Enhanced AI WebSocket connection error:', error); reject(error); }); // Real-time AI events this.socket.on('contextual_suggestion', (suggestion: ContextualSuggestion) => { store.dispatch(addContextualSuggestion(suggestion)); }); this.socket.on('sentiment_update', (sentiment: SentimentAnalysis) => { store.dispatch(updateSentimentAnalysis(sentiment)); }); this.socket.on('context_change', (context: Partial<SuggestionContext>) => { store.dispatch(updateContext(context)); }); this.socket.on('escalation_alert', (alert) => { this.handleEscalationAlert(alert); }); } catch (error) { console.error('[FEATURE] Failed to initialize enhanced AI WebSocket:', error); reject(error); } }); } disconnectWebSocket(): void { if (this.socket) { this.socket.disconnect(); this.socket = null; this.isConnected = false; } if (this.contextUpdateInterval) { clearInterval(this.contextUpdateInterval); this.contextUpdateInterval = null; } } // Contextual suggestions async generateContextualSuggestions(context: SuggestionContext): Promise<{ suggestions: ContextualSuggestion[]; confidence_score: number; }> { try { const response = await fetch(`${this.baseURL}/api/ai/suggestions/contextual`, { method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${this.getAuthToken()}` }, body: JSON.stringify(context) }); if (!response.ok) { throw new Error(`Failed to generate contextual suggestions: ${response.statusText}`); } return response.json(); } catch (error) { console.error('[FEATURE] Error generating contextual suggestions:', error); throw error; } } async requestImmediateSuggestion(context: SuggestionContext): Promise<ContextualSuggestion> { try { if (this.socket && this.isConnected) { // Request via WebSocket for immediate response return new Promise((resolve, reject) => { const timeout = setTimeout(() => { reject(new Error('Suggestion request timeout')); }, 5000); this.socket!.emit('request_suggestion', context, (response: any) => { clearTimeout(timeout); if (response.error) { reject(new Error(response.error)); } else { resolve(response.suggestion); } }); }); } else { // Fallback to HTTP const response = await fetch(`${this.baseURL}/api/ai/suggestions/immediate`, { method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${this.getAuthToken()}` }, body: JSON.stringify(context) }); if (!response.ok) { throw new Error(`Failed to get immediate suggestion: ${response.statusText}`); } const data = await response.json(); return data.suggestion; } } catch (error) { console.error('[FEATURE] Error requesting immediate suggestion:', error); throw error; } } // Sentiment analysis async analyzeSentiment(conversation: any[]): Promise<{ sentiment_analysis: SentimentAnalysis; recommendations: string[]; }> { try { const response = await fetch(`${this.baseURL}/api/ai/sentiment/analyze`, { method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${this.getAuthToken()}` }, body: JSON.stringify({ conversation }) }); if (!response.ok) { throw new Error(`Failed to analyze sentiment: ${response.statusText}`); } return response.json(); } catch (error) { console.error('[FEATURE] Error analyzing sentiment:', error); throw error; } } async trackSentimentTrend(ticketId: string): Promise<any> { try { const response = await fetch(`${this.baseURL}/api/ai/sentiment/trend/${ticketId}`, { headers: { 'Authorization': `Bearer ${this.getAuthToken()}` } }); if (!response.ok) { throw new Error(`Failed to track sentiment trend: ${response.statusText}`); } return response.json(); } catch (error) { console.error('[FEATURE] Error tracking sentiment trend:', error); throw error; } } // Escalation recommendations async getEscalationRecommendation(context: SuggestionContext): Promise<{ recommendation: EscalationRecommendation; confidence: number; }> { try { const response = await fetch(`${this.baseURL}/api/ai/escalation/recommend`, { method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${this.getAuthToken()}` }, body: JSON.stringify(context) }); if (!response.ok) { throw new Error(`Failed to get escalation recommendation: ${response.statusText}`); } return response.json(); } catch (error) { console.error('[FEATURE] Error getting escalation recommendation:', error); throw error; } } async monitorEscalationRisk(ticketId: string): Promise<void> { try { if (this.socket && this.isConnected) { this.socket.emit('monitor_escalation', { ticket_id: ticketId }); } } catch (error) { console.error('[FEATURE] Error monitoring escalation risk:', error); throw error; } } // Response templates async fetchResponseTemplates(agentId: string): Promise<{ templates: ResponseTemplate[]; personalized_templates: any[]; categories: string[]; }> { try { const response = await fetch(`${this.baseURL}/api/ai/templates/${agentId}`, { headers: { 'Authorization': `Bearer ${this.getAuthToken()}` } }); if (!response.ok) { throw new Error(`Failed to fetch response templates: ${response.statusText}`); } return response.json(); } catch (error) { console.error('[FEATURE] Error fetching response templates:', error); throw error; } } async createPersonalizedTemplate(templateData: { template_id: string; agent_id: string; customization: string; category: string; }): Promise<any> { try { const response = await fetch(`${this.baseURL}/api/ai/templates/personalize`, { method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${this.getAuthToken()}` }, body: JSON.stringify(templateData) }); if (!response.ok) { throw new Error(`Failed to create personalized template: ${response.statusText}`); } return response.json(); } catch (error) { console.error('[FEATURE] Error creating personalized template:', error); throw error; } } async optimizeTemplate(templateId: string, usageData: any): Promise<any> { try { const response = await fetch(`${this.baseURL}/api/ai/templates/optimize`, { method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${this.getAuthToken()}` }, body: JSON.stringify({ template_id: templateId, usage_data: usageData }) }); if (!response.ok) { throw new Error(`Failed to optimize template: ${response.statusText}`); } return response.json(); } catch (error) { console.error('[FEATURE] Error optimizing template:', error); throw error; } } // Learning and feedback async submitSuggestionFeedback(feedbackData: { suggestion_id: string; feedback: SuggestionFeedback; }): Promise<{ learning_update: any; performance: any; }> { try { const response = await fetch(`${this.baseURL}/api/ai/suggestions/feedback`, { method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${this.getAuthToken()}` }, body: JSON.stringify(feedbackData) }); if (!response.ok) { throw new Error(`Failed to submit suggestion feedback: ${response.statusText}`); } return response.json(); } catch (error) { console.error('[FEATURE] Error submitting suggestion feedback:', error); throw error; } } async updateAgentPreferences(agentId: string, preferences: any): Promise<void> { try { const response = await fetch(`${this.baseURL}/api/ai/agents/${agentId}/preferences`, { method: 'PUT', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${this.getAuthToken()}` }, body: JSON.stringify(preferences) }); if (!response.ok) { throw new Error(`Failed to update agent preferences: ${response.statusText}`); } } catch (error) { console.error('[FEATURE] Error updating agent preferences:', error); throw error; } } async fetchLearningData(agentId: string): Promise<any> { try { const response = await fetch(`${this.baseURL}/api/ai/agents/${agentId}/learning`, { headers: { 'Authorization': `Bearer ${this.getAuthToken()}` } }); if (!response.ok) { throw new Error(`Failed to fetch learning data: ${response.statusText}`); } return response.json(); } catch (error) { console.error('[FEATURE] Error fetching learning data:', error); throw error; } } // Context management async updateConversationContext(context: Partial<SuggestionContext>): Promise<void> { try { if (this.socket && this.isConnected) { this.socket.emit('update_context', context); } // Also update local store store.dispatch(updateContext(context)); } catch (error) { console.error('[FEATURE] Error updating conversation context:', error); throw error; } } startContextMonitoring(ticketId: string): void { if (this.contextUpdateInterval) { clearInterval(this.contextUpdateInterval); } // Monitor context changes every 30 seconds this.contextUpdateInterval = setInterval(async () => { try { await this.refreshContext(ticketId); } catch (error) { console.error('[FEATURE] Context monitoring error:', error); } }, 30000); } stopContextMonitoring(): void { if (this.contextUpdateInterval) { clearInterval(this.contextUpdateInterval); this.contextUpdateInterval = null; } } private async refreshContext(ticketId: string): Promise<void> { try { const response = await fetch(`${this.baseURL}/api/ai/context/${ticketId}`, { headers: { 'Authorization': `Bearer ${this.getAuthToken()}` } }); if (response.ok) { const context = await response.json(); store.dispatch(updateContext(context)); } } catch (error) { console.error('[FEATURE] Error refreshing context:', error); } } // Integration with existing services async integrateWithMultimodal(data: any): Promise<any> { try { const response = await fetch(`${this.baseURL}/api/ai/integration/multimodal`, { method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${this.getAuthToken()}` }, body: JSON.stringify(data) }); if (!response.ok) { throw new Error(`Failed to integrate with multimodal: ${response.statusText}`); } return response.json(); } catch (error) { console.error('[FEATURE] Error integrating with multimodal:', error); throw error; } } async integrateWithMLService(data: any): Promise<any> { try { const response = await fetch(`${this.baseURL}/api/ai/integration/ml`, { method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${this.getAuthToken()}` }, body: JSON.stringify(data) }); if (!response.ok) { throw new Error(`Failed to integrate with ML service: ${response.statusText}`); } return response.json(); } catch (error) { console.error('[FEATURE] Error integrating with ML service:', error); throw error; } } // Event handlers private handleEscalationAlert(alert: any): void { console.log('[FEATURE] Escalation alert received:', alert); // This could trigger notifications or UI updates } // Utility methods private getAuthToken(): string { const state = store.getState(); return state.auth?.token || ''; } private getCurrentAgentId(): string { const state = store.getState(); return state.auth?.user?.id || ''; } // Connection status isWebSocketConnected(): boolean { return this.isConnected; } getConnectionStatus(): string { if (!this.socket) return 'disconnected'; return this.socket.connected ? 'connected' : 'disconnected'; } } // Export singleton instance export const enhancedAIService = new EnhancedAIService(); export default enhancedAIService;