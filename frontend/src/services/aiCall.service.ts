/** * ============================================= * [AI] AI CALL SERVICE * Frontend service for AI-powered call management * Handles API communication for conversation analysis and escalation * ============================================= */ import axios, { AxiosResponse } from 'axios'; import { getAuthToken } from '../utils/auth'; const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000'; // Create axios instance with default config const apiClient = axios.create({ baseURL: `${API_BASE_URL}/api/ai-calls`, timeout: 30000, // 30 seconds timeout for AI operations headers: { 'Content-Type': 'application/json', }, }); // Request interceptor to add auth token apiClient.interceptors.request.use( (config) => { const token = getAuthToken(); if (token) { config.headers.Authorization = `Bearer ${token}`; } return config; }, (error) => { return Promise.reject(error); } ); // Response interceptor for error handling apiClient.interceptors.response.use( (response) => response, (error) => { if (error.response?.status === 401) { // Handle unauthorized access localStorage.removeItem('authToken'); window.location.href = '/login'; } return Promise.reject(error); } ); // Types interface ConversationAnalysisResponse { success: boolean; analysis: { needsCall: boolean; confidence: number; callScore: number; primaryReason: string; reasons: string[]; recommendation: { message: string; action: string; priority: string; }; analysis: { frustrationScore: number; complexityScore: number; conversationMetrics: any; userBehaviorAnalysis: any; issueResolutionProgress: any; }; analyzedAt: string; }; timestamp: string; } interface AICallInitiationResponse { success: boolean; callSession?: { callId: string; status: string; urgencyLevel: string; description: string; }; aiAssistance?: { message: string; suggestions: any[]; confidence: number; attemptNumber: number; }; recommendations?: any; reason?: string; confidence?: number; } interface AISuggestionsResponse { success: boolean; suggestions: any[]; confidence: number; analysis: any; generatedAt: string; } interface EscalationEvaluationResponse { success: boolean; evaluation: { shouldEscalate: boolean; confidence: number; reasons: string[]; urgency: string; }; agentRequirements?: any; evaluatedAt: string; } interface EscalationResponse { success: boolean; escalation: { escalated: boolean; agent?: { id: string; name: string; specialization: string; experience: string; }; estimatedResponseTime?: number; transferredAt: string; }; briefing?: any; } interface CallAnalyticsResponse { success: boolean; analytics: { totalCalls: number; aiResolvedCalls: number; escalatedCalls: number; averageResolutionTime: number; userSatisfactionScore: number; aiEffectivenessScore: number; topIssueCategories: string[]; timeframe: string; generatedAt: string; }; } class AICallService { /** * Analyze conversation to determine if a call is needed */ async analyzeConversation(conversationId: string): Promise<AxiosResponse<ConversationAnalysisResponse>> { try { console.log('[SEARCH] Analyzing conversation for call need:', conversationId); const response = await apiClient.post(`/analyze-conversation/${conversationId}`); console.log('[COMPLETE] Conversation analysis completed:', { needsCall: response.data.analysis.needsCall, confidence: response.data.analysis.confidence }); return response; } catch (error) { console.error('[FAILED] Failed to analyze conversation:', error); throw error; } } /** * Initiate AI-powered call */ async initiateAICall(params: { conversationId: string; forceCall?: boolean; }): Promise<AxiosResponse<AICallInitiationResponse>> { try { console.log('[AI] Initiating AI call:', params); const response = await apiClient.post('/initiate', params); console.log('[COMPLETE] AI call initiated:', { success: response.data.success, callId: response.data.callSession?.callId }); return response; } catch (error) { console.error('[FAILED] Failed to initiate AI call:', error); throw error; } } /** * Get AI suggestions for current call context */ async getAISuggestions(params: { callId: string; userInput?: string; context?: { sessionId?: string; conversationHistory?: any[]; }; }): Promise<AxiosResponse<AISuggestionsResponse>> { try { console.log(' Getting AI suggestions:', { callId: params.callId, hasInput: !!params.userInput }); const response = await apiClient.post(`/suggestions/${params.callId}`, { userInput: params.userInput, context: params.context }); console.log('[COMPLETE] AI suggestions received:', { suggestionsCount: response.data.suggestions.length, confidence: response.data.confidence }); return response; } catch (error) { console.error('[FAILED] Failed to get AI suggestions:', error); throw error; } } /** * Evaluate if escalation to human agent is needed */ async evaluateEscalation(params: { callId: string; userFeedback?: string; aiAttempts?: number; conversationHistory?: any[]; }): Promise<AxiosResponse<EscalationEvaluationResponse>> { try { console.log('[SEARCH] Evaluating escalation need:', { callId: params.callId, aiAttempts: params.aiAttempts }); const response = await apiClient.post(`/evaluate-escalation/${params.callId}`, { userFeedback: params.userFeedback, aiAttempts: params.aiAttempts, conversationHistory: params.conversationHistory }); console.log('[COMPLETE] Escalation evaluation completed:', { shouldEscalate: response.data.evaluation.shouldEscalate, confidence: response.data.evaluation.confidence }); return response; } catch (error) { console.error('[FAILED] Failed to evaluate escalation:', error); throw error; } } /** * Escalate call to human agent */ async escalateToHuman(params: { callId: string; escalationReason: string; agentRequirements?: { specialization?: string; experience?: string; skills?: string[]; language?: string; }; conversationHistory?: any[]; aiAttempts?: number; }): Promise<AxiosResponse<EscalationResponse>> { try { console.log(' Escalating to human agent:', { callId: params.callId, reason: params.escalationReason }); const response = await apiClient.post(`/escalate/${params.callId}`, { escalationReason: params.escalationReason, agentRequirements: params.agentRequirements, conversationHistory: params.conversationHistory, aiAttempts: params.aiAttempts }); console.log('[COMPLETE] Escalation completed:', { success: response.data.success, agentName: response.data.escalation.agent?.name }); return response; } catch (error) { console.error('[FAILED] Failed to escalate to human agent:', error); throw error; } } /** * Get call analytics and performance metrics */ async getCallAnalytics(timeframe: string = '24h'): Promise<AxiosResponse<CallAnalyticsResponse>> { try { console.log('[ANALYTICS] Getting call analytics:', { timeframe }); const response = await apiClient.get('/analytics', { params: { timeframe } }); console.log('[COMPLETE] Call analytics received:', { totalCalls: response.data.analytics.totalCalls, aiEffectiveness: response.data.analytics.aiEffectivenessScore }); return response; } catch (error) { console.error('[FAILED] Failed to get call analytics:', error); throw error; } } /** * Health check for AI call services */ async healthCheck(): Promise<AxiosResponse<any>> { try { const response = await apiClient.get('/health'); return response; } catch (error) { console.error('[FAILED] AI call health check failed:', error); throw error; } } /** * Submit feedback for AI suggestions */ async submitSuggestionFeedback(params: { callId: string; suggestionId: string; feedback: 'helpful' | 'not_helpful' | 'tried'; comment?: string; }): Promise<AxiosResponse<any>> { try { console.log(' Submitting suggestion feedback:', { callId: params.callId, feedback: params.feedback }); // This would be, implemented as a separate endpoint const response = await apiClient.post(`/feedback/${params.callId}`, { suggestionId: params.suggestionId, feedback: params.feedback, comment: params.comment }); return response; } catch (error) { console.error('[FAILED] Failed to submit suggestion feedback:', error); throw error; } } /** * Get real-time call status updates */ async getCallStatus(callId: string): Promise<AxiosResponse<any>> { try { const response = await apiClient.get(`/status/${callId}`); return response; } catch (error) { console.error('[FAILED] Failed to get call status:', error); throw error; } } /** * Cancel AI call session */ async cancelCall(callId: string, reason?: string): Promise<AxiosResponse<any>> { try { console.log('[FAILED] Cancelling AI call:', { callId, reason }); const response = await apiClient.post(`/cancel/${callId}`, { reason: reason || 'user_cancelled' }); return response; } catch (error) { console.error('[FAILED] Failed to cancel call:', error); throw error; } } } export const aiCallService = new AICallService(); export default aiCallService;