import axios from 'axios';
import { API_BASE_URL } from '../utils/constants';
import { User, RegisterData, LoginResponse } from '../types';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      // Use navigate instead of direct window.location to avoid React errors
      if (window.location.pathname !== '/login') {
        window.location.href = '/login';
      }
    }
    return Promise.reject(error);
  }
);

// API Service with authentication methods
const apiService = {
  // Auth methods
  async login(email: string, password: string): Promise<LoginResponse> {
    try {
      // Always use real API authentication
      const response = await api.post<LoginResponse>('/auth/login', {
        email,
        password,
      });
      return response.data;
    } catch (error: any) {
      console.error('Login API error:', error);
      throw new Error(error.response?.data?.error || error.response?.data?.message || 'Erreur de connexion');
    }
  },

  async register(data: RegisterData): Promise<LoginResponse> {
    try {
      // For development, simulate API call
      if (process.env.NODE_ENV === 'development') {
        await new Promise(resolve => setTimeout(resolve, 1000));
        const mockUser: User = {
          id: '2',
          email: data.email,
          role: data.role || 'user',
          profile: {
            firstName: data.firstName,
            lastName: data.lastName,
            phoneNumber: data.phoneNumber,
            customerId: 'FREE-' + Math.random().toString(36).substr(2, 8).toUpperCase(),
          },
          preferences: {
            language: 'fr',
            notifications: true,
          },
          createdAt: new Date(),
        };
        const mockResponse: LoginResponse = {
          user: mockUser,
          token: 'mock-jwt-token-' + Date.now(),
          message: 'Inscription réussie',
        };
        return mockResponse;
      }
      const response = await api.post<LoginResponse>('/auth/register', data);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur d\'inscription');
    }
  },

  async validateToken(): Promise<{ user: User }> {
    try {
      // For development, validate mock token
      if (process.env.NODE_ENV === 'development') {
        const token = localStorage.getItem('token');
        if (token && token.startsWith('mock-jwt-token-')) {
          const userStr = localStorage.getItem('user');
          if (userStr) {
            const user = JSON.parse(userStr);
            return { user };
          }
        }
        throw new Error('Token invalide');
      }
      const response = await api.get<{ user: User }>('/auth/validate');
      return response.data;
    } catch (error: any) {
      throw new Error('Token invalide');
    }
  },

  // Generic API methods
  get: api.get,
  post: api.post,
  put: api.put,
  delete: api.delete,
  patch: api.patch,
};

export default apiService;