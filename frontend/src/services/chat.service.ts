import api from './api'; import { Conversation, Message } from '../types'; interface StartConversationResponse { conversationId: string; sessionId: string; welcomeMessage: string; } interface SendMessageResponse { response: string; intent: { name: string; confidence: number; }; messageId: string; } class ChatService { async startConversation(channel: string = 'web'): Promise<StartConversationResponse> { const response = await api.post<StartConversationResponse>('/chat/conversations/start', { channel, }); return response.data; } async sendMessage(conversationId: string, message: string, attachments?: string[]): Promise<SendMessageResponse> { const response = await api.post<SendMessageResponse>('/chat/messages/send', { conversationId, message, attachments, }); return response.data; } async getConversationHistory(conversationId: string): Promise<{ conversation: Conversation; messages: Message[]; }> { const response = await api.get(`/chat/conversations/${conversationId}`); return response.data; } async getUserConversations(): Promise<Conversation[]> { const response = await api.get('/chat/conversations'); return response.data; } async escalateToAgent(conversationId: string, reason?: string): Promise<{ success: boolean; agentId: string; }> { const response = await api.post(`/chat/conversations/${conversationId}/escalate`, { reason, }); return response.data; } } const chatService = new ChatService(); export default chatService;