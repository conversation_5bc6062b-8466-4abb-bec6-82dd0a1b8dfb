import api from './api'; import { DashboardData, AnalyticsData, Conversation, Message } from '../types'; export interface ConversationsResponse { conversations: Conversation[]; pagination: { current: number; pages: number; total: number; }; } interface ConversationDetailsResponse { conversation: Conversation; messages: Message[]; } class AdminService { async getDashboard(): Promise<DashboardData> { const response = await api.get<DashboardData>('/admin/dashboard'); return response.data; } async getAllConversations( page: number = 1, limit: number = 20, status?: string, search?: string ): Promise<ConversationsResponse> { const params = new URLSearchParams({ page: page.toString(), limit: limit.toString(), }); if (status) params.append('status', status); if (search) params.append('search', search); const response = await api.get<ConversationsResponse>(`/admin/conversations?${params}`); return response.data; } async getConversationDetails(conversationId: string): Promise<ConversationDetailsResponse> { const response = await api.get<ConversationDetailsResponse>(`/admin/conversations/${conversationId}`); return response.data; } async assignAgent(conversationId: string, agentId: string): Promise<void> { await api.put(`/admin/conversations/${conversationId}/assign`, { agentId }); } async closeConversation( conversationId: string, satisfaction?: { rating: number; feedback: string } ): Promise<void> { await api.put(`/admin/conversations/${conversationId}/close`, { satisfaction }); } async getAnalytics(startDate?: string, endDate?: string): Promise<AnalyticsData> { const params = new URLSearchParams(); if (startDate) params.append('startDate', startDate); if (endDate) params.append('endDate', endDate); const response = await api.get<AnalyticsData>(`/admin/analytics?${params}`); return response.data; } } const adminService = new AdminService(); export default adminService;