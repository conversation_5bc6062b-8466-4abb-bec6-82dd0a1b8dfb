import { io, Socket } from 'socket.io-client'; import { store } from '../store'; import { addMessage, updateTypingUsers, setConnectionStatus } from '../store/slices/chatSlice'; import { showNotification } from '../store/slices/uiSlice'; interface WebSocketConfig { url: string; token: string; userId: string; } interface MessageData { message: any; intelligentFeatures?: { mode: string; hasButtons: boolean; hasQuickReplies: boolean; hasProactiveInsights: boolean; sentiment?: string; }; timestamp: Date; } interface TypingData { userId: string; typingUsers: string[]; timestamp: Date; } interface UserJoinedData { userId: string; user: { id: string; email: string; profile: any; role: string; }; timestamp: Date; } class WebSocketService { private chatSocket: Socket | null = null; private supportSocket: Socket | null = null; private analyticsSocket: Socket | null = null; private isConnected = false; private currentConversationId: string | null = null; private reconnectAttempts = 0; private maxReconnectAttempts = 5; private reconnectDelay = 1000; // Initialize chat WebSocket connection initializeChatSocket(config: WebSocketConfig): Promise<void> { return new Promise((resolve, reject) => { try { if (this.chatSocket?.connected) { resolve(); return; } this.chatSocket = io(`${config.url}/chat`, { auth: { token: config.token, userId: config.userId }, transports: ['websocket', 'polling'], timeout: 10000, forceNew: true }); this.setupChatEventHandlers(); this.chatSocket.on('connect', () => { console.log(' Chat WebSocket connected'); this.isConnected = true; this.reconnectAttempts = 0; // Authenticate this.chatSocket?.emit('authenticate', { token: config.token, userId: config.userId }); store.dispatch(setConnectionStatus({ connected: true, error: null })); resolve(); }); this.chatSocket.on('connect_error', (error) => { console.error(' Chat WebSocket connection error:', error); this.handleReconnection(); reject(error); }); } catch (error) { console.error(' Error initializing chat socket:', error); reject(error); } }); } // Initialize support WebSocket connection initializeSupportSocket(config: WebSocketConfig): Promise<void> { return new Promise((resolve, reject) => { try { if (this.supportSocket?.connected) { resolve(); return; } this.supportSocket = io(`${config.url}/support`, { auth: { token: config.token, userId: config.userId }, transports: ['websocket', 'polling'], timeout: 10000, forceNew: true }); this.setupSupportEventHandlers(); this.supportSocket.on('connect', () => { console.log(' Support WebSocket connected'); resolve(); }); this.supportSocket.on('connect_error', (error) => { console.error(' Support WebSocket connection error:', error); reject(error); }); } catch (error) { console.error(' Error initializing support socket:', error); reject(error); } }); } // Setup chat event handlers private setupChatEventHandlers(): void { if (!this.chatSocket) return; // Authentication events this.chatSocket.on('authenticated', (data) => { console.log(' Chat authenticated:', data); }); this.chatSocket.on('auth_error', (data) => { console.error(' Chat authentication error:', data); store.dispatch(showNotification({ message: 'Erreur d\'authentification du chat', severity: 'error' })); }); // Conversation events this.chatSocket.on('conversation_joined', (data) => { console.log(' Joined conversation:', data); this.currentConversationId = data.conversationId; }); this.chatSocket.on('message_received', (data: MessageData) => { console.log(' Message received:', data); store.dispatch(addMessage(data.message)); }); this.chatSocket.on('user_joined', (data: UserJoinedData) => { console.log(' User joined conversation:', data); store.dispatch(showNotification({ message: `${data.user.profile?.firstName || data.user.email} a rejoint la conversation`, severity: 'info' })); }); this.chatSocket.on('user_left', (data) => { console.log(' User left conversation:', data); }); this.chatSocket.on('agent_joined', (data) => { console.log(' Agent joined conversation:', data); store.dispatch(showNotification({ message: `Un agent ${data.agent.profile?.firstName || 'support'} a rejoint la conversation`, severity: 'success' })); }); // Typing indicators this.chatSocket.on('user_typing', (data: TypingData) => { store.dispatch(updateTypingUsers(data.typingUsers)); }); this.chatSocket.on('user_stopped_typing', (data: TypingData) => { store.dispatch(updateTypingUsers(data.typingUsers)); }); // Error handling this.chatSocket.on('error', (data) => { console.error(' Chat error:', data); store.dispatch(showNotification({ message: data.message || 'Erreur de chat', severity: 'error' })); }); // Connection events this.chatSocket.on('disconnect', (reason) => { console.log(' Chat disconnected:', reason); this.isConnected = false; store.dispatch(setConnectionStatus({ connected: false, error: reason })); if (reason === 'io server disconnect') { // Server disconnected, try to reconnect this.handleReconnection(); } }); } // Setup support event handlers private setupSupportEventHandlers(): void { if (!this.supportSocket) return; this.supportSocket.on('ticket_created', (data) => { console.log(' Ticket created:', data); store.dispatch(showNotification({ message: `Nouveau ticket créé: ${data.ticket.ticketNumber}`, severity: 'info' })); }); this.supportSocket.on('ticket_updated', (data) => { console.log(' Ticket updated:', data); }); this.supportSocket.on('ticket_message', (data) => { console.log(' Ticket message:', data); }); this.supportSocket.on('notification', (data) => { store.dispatch(showNotification({ message: data.message, severity: data.type || 'info' })); }); } // Join a conversation joinConversation(conversationId: string): void { if (this.chatSocket?.connected) { this.chatSocket.emit('join_conversation', { conversationId }); this.currentConversationId = conversationId; } } // Leave current conversation leaveConversation(): void { if (this.chatSocket?.connected && this.currentConversationId) { this.chatSocket.emit('leave_conversation', { conversationId: this.currentConversationId }); this.currentConversationId = null; } } // Send real-time message with optional attachments sendMessage(conversationId: string, message: string, type: string = 'text', attachments: string[] = []): void { if (this.chatSocket?.connected) { this.chatSocket.emit('send_message', { conversationId, message, type, attachments }); } } // Start typing indicator startTyping(conversationId: string): void { if (this.chatSocket?.connected) { this.chatSocket.emit('typing_start', { conversationId }); } } // Stop typing indicator stopTyping(conversationId: string): void { if (this.chatSocket?.connected) { this.chatSocket.emit('typing_stop', { conversationId }); } } // Agent takeover agentTakeover(conversationId: string): void { if (this.chatSocket?.connected) { this.chatSocket.emit('agent_takeover', { conversationId }); } } // Join support ticket room joinTicket(ticketId: string): void { if (this.supportSocket?.connected) { this.supportSocket.emit('join_ticket', { ticketId }); } } // Leave support ticket room leaveTicket(ticketId: string): void { if (this.supportSocket?.connected) { this.supportSocket.emit('leave_ticket', { ticketId }); } } // Handle reconnection private handleReconnection(): void { if (this.reconnectAttempts < this.maxReconnectAttempts) { this.reconnectAttempts++; const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); console.log(` Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms`); setTimeout(() => { if (this.chatSocket && !this.chatSocket.connected) { this.chatSocket.connect(); } }, delay); } else { console.error(' Max reconnection attempts reached'); store.dispatch(showNotification({ message: 'Connexion perdue. Veuillez actualiser la page.', severity: 'error' })); } } // Initialize analytics WebSocket connection async initializeAnalyticsSocket(config: WebSocketConfig): Promise<void> { try { if (this.analyticsSocket?.connected) { return; } this.analyticsSocket = io(`${config.url}/analytics`, { auth: { token: config.token }, transports: ['websocket'], timeout: 10000, reconnection: true, reconnectionAttempts: 5, reconnectionDelay: 2000 }); return new Promise((resolve, reject) => { const timeout = setTimeout(() => { reject(new Error('Analytics WebSocket connection timeout')); }, 10000); this.analyticsSocket!.on('connect', () => { clearTimeout(timeout); console.log('[ANALYTICS] Analytics WebSocket connected'); resolve(); }); this.analyticsSocket!.on('connect_error', (error) => { clearTimeout(timeout); console.error('[ANALYTICS] Analytics WebSocket connection error:', error); reject(error); }); }); } catch (error) { console.error('Error initializing analytics WebSocket:', error); throw error; } } // Analytics WebSocket methods requestDashboardData(params: any = {}): void { if (this.analyticsSocket?.connected) { this.analyticsSocket.emit('request_dashboard_data', { ...params, requestId: Date.now().toString() }); } } requestRealTimeMetrics(): void { if (this.analyticsSocket?.connected) { this.analyticsSocket.emit('request_realtime_metrics', { requestId: Date.now().toString() }); } } subscribeToRealTimeUpdates(): void { if (this.analyticsSocket?.connected) { this.analyticsSocket.emit('subscribe_realtime_updates', { requestId: Date.now().toString() }); } } // Disconnect all sockets disconnect(): void { if (this.chatSocket) { this.chatSocket.disconnect(); this.chatSocket = null; } if (this.supportSocket) { this.supportSocket.disconnect(); this.supportSocket = null; } if (this.analyticsSocket) { this.analyticsSocket.disconnect(); this.analyticsSocket = null; } this.isConnected = false; this.currentConversationId = null; this.reconnectAttempts = 0; } // Get connection status isConnectedToChat(): boolean { return this.chatSocket?.connected || false; } isConnectedToSupport(): boolean { return this.supportSocket?.connected || false; } isConnectedToAnalytics(): boolean { return this.analyticsSocket?.connected || false; } getCurrentConversationId(): string | null { return this.currentConversationId; } } // Export singleton instance export const webSocketService = new WebSocketService(); export default webSocketService;