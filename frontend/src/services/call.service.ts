/** * ============================================= * CALL SERVICE API CLIENT * Frontend service for call management API integration * Handles REST API calls and WebSocket connections * ============================================= */ import axios, { AxiosResponse } from 'axios'; import { io, Socket } from 'socket.io-client'; const CALL_SERVICE_URL = process.env.REACT_APP_CALL_SERVICE_URL || 'http://localhost:5004'; const API_BASE_URL = `${CALL_SERVICE_URL}/api`; // Create axios instance with default config const callApi = axios.create({ baseURL: API_BASE_URL, timeout: 30000, headers: { 'Content-Type': 'application/json' } }); // Add auth token to requests callApi.interceptors.request.use((config) => { const token = localStorage.getItem('authToken'); if (token) { config.headers.Authorization = `Bearer ${token}`; } return config; }); // Response interceptor for error handling callApi.interceptors.response.use( (response) => response, (error) => { console.error('Call API Error:', error.response?.data || error.message); return Promise.reject(error); } ); // Types interface CallInitiateParams { type: 'immediate' | 'scheduled' | 'callback'; userId: string; priority: 'low' | 'normal' | 'high' | 'urgent'; reason?: string; scheduledTime?: Date; preferredAgent?: string; } interface WebRTCParticipant { userId: string; role: 'customer' | 'agent' | 'supervisor'; socketId: string; } interface CallbackParams { userId: string; scheduledTime: Date; reason: string; priority: 'low' | 'normal' | 'high' | 'urgent'; preferences: any; } class CallService { private socket: Socket | null = null; private eventListeners: Map<string, Function[]> = new Map(); /** * Initialize WebSocket connection */ initializeSocket(userId: string): Socket { if (this.socket?.connected) { return this.socket; } this.socket = io(CALL_SERVICE_URL, { auth: { token: localStorage.getItem('authToken'), userId }, transports: ['websocket', 'polling'] }); // Connection events this.socket.on('connect', () => { console.log(' Connected to call service'); }); this.socket.on('disconnect', () => { console.log(' Disconnected from call service'); }); this.socket.on('connect_error', (error) => { console.error(' Call service connection error:', error); }); // Call events this.socket.on('call-status-update', (data) => { this.emit('call-status-update', data); }); this.socket.on('queue-position-update', (data) => { this.emit('queue-position-update', data); }); this.socket.on('agent-assigned', (data) => { this.emit('agent-assigned', data); }); this.socket.on('call-ended', (data) => { this.emit('call-ended', data); }); // WebRTC signaling events this.socket.on('webrtc-offer', (data) => { this.emit('webrtc-offer', data); }); this.socket.on('webrtc-answer', (data) => { this.emit('webrtc-answer', data); }); this.socket.on('webrtc-ice-candidate', (data) => { this.emit('webrtc-ice-candidate', data); }); this.socket.on('participant-joined', (data) => { this.emit('participant-joined', data); }); this.socket.on('participant-left', (data) => { this.emit('participant-left', data); }); this.socket.on('participant-muted', (data) => { this.emit('participant-muted', data); }); return this.socket; } /** * Event listener management */ on(event: string, callback: Function): void { if (!this.eventListeners.has(event)) { this.eventListeners.set(event, []); } this.eventListeners.get(event)!.push(callback); } off(event: string, callback: Function): void { const listeners = this.eventListeners.get(event); if (listeners) { const index = listeners.indexOf(callback); if (index > -1) { listeners.splice(index, 1); } } } private emit(event: string, data: any): void { const listeners = this.eventListeners.get(event); if (listeners) { listeners.forEach(callback => callback(data)); } } /** * API Methods */ // Health check async healthCheck(): Promise<AxiosResponse> { return callApi.get('/calls/health'); } // Initiate outgoing call async initiateCall(params: CallInitiateParams): Promise<AxiosResponse> { if (params.type === 'immediate') { return callApi.post('/calls/outgoing', { to: '+33123456789', // This would be the support number userId: params.userId, priority: params.priority, recordCall: true, transcribeCall: true }); } else { // Handle scheduled or callback calls return this.scheduleCallback({ userId: params.userId, scheduledTime: params.scheduledTime || new Date(), reason: params.reason || '', priority: params.priority, preferences: { type: params.type } }); } } // Start WebRTC call async startWebRTCCall(participants: WebRTCParticipant[]): Promise<AxiosResponse> { return callApi.post('/calls/webrtc/start', { participants }); } // Join WebRTC call async joinWebRTCCall(params: { callId: string; userId: string; role: string; socketId: string; }): Promise<AxiosResponse> { return callApi.post(`/calls/${params.callId}/join`, { userId: params.userId, role: params.role, socketId: params.socketId }); } // Leave WebRTC call async leaveWebRTCCall(callId: string, userId: string): Promise<AxiosResponse> { return callApi.post(`/calls/${callId}/leave`, { userId }); } // End call async endCall(callId: string, reason?: string): Promise<AxiosResponse> { return callApi.post(`/calls/${callId}/end`, { reason }); } // Transfer call async transferCall(callId: string, targetAgentId: string, reason?: string): Promise<AxiosResponse> { return callApi.post(`/calls/${callId}/transfer`, { targetAgentId, reason }); } // Get call details async getCall(callId: string): Promise<AxiosResponse> { return callApi.get(`/calls/${callId}`); } // Get calls list async getCalls(filters: { status?: string; direction?: string; userId?: string; limit?: number; offset?: number; } = {}): Promise<AxiosResponse> { return callApi.get('/calls', { params: filters }); } // Get call history for user async getCallHistory(params: { userId: string; limit?: number; offset?: number; }): Promise<AxiosResponse> { return callApi.get('/calls', { params: { userId: params.userId, limit: params.limit || 20, offset: params.offset || 0 } }); } // Get queue statistics async getQueueStats(): Promise<AxiosResponse> { return callApi.get('/calls/stats/overview'); } // Schedule callback async scheduleCallback(params: CallbackParams): Promise<AxiosResponse> { return callApi.post('/queue/callback', params); } // Get available time slots async getAvailableSlots(date: Date): Promise<AxiosResponse> { return callApi.get('/queue/available-slots', { params: { date: date.toISOString() } }); } /** * WebSocket Methods */ // Join call room joinCallRoom(callId: string, userId: string, userRole: string): void { if (this.socket) { this.socket.emit('join-call-room', { callId, userId, userRole }); } } // Send WebRTC offer sendWebRTCOffer(callId: string, offer: RTCSessionDescriptionInit): void { if (this.socket) { this.socket.emit('webrtc-offer', { callId, offer }); } } // Send WebRTC answer sendWebRTCAnswer(callId: string, answer: RTCSessionDescriptionInit): void { if (this.socket) { this.socket.emit('webrtc-answer', { callId, answer }); } } // Send ICE candidate sendICECandidate(callId: string, candidate: RTCIceCandidateInit): void { if (this.socket) { this.socket.emit('webrtc-ice-candidate', { callId, candidate }); } } // Call control events sendMuteEvent(callId: string, muted: boolean): void { if (this.socket) { this.socket.emit('call-mute', { callId, muted }); } } sendHoldEvent(callId: string, held: boolean): void { if (this.socket) { this.socket.emit('call-hold', { callId, held }); } } sendTransferEvent(callId: string, targetAgent: string): void { if (this.socket) { this.socket.emit('call-transfer', { callId, targetAgent }); } } /** * Cleanup */ disconnect(): void { if (this.socket) { this.socket.disconnect(); this.socket = null; } this.eventListeners.clear(); } } // Create singleton instance export const callService = new CallService(); export default callService;