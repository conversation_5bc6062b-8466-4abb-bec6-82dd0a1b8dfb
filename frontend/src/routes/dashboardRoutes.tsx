import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { ErrorBoundary } from 'react-error-boundary';
import { Box, Typography, Button } from '@mui/material';
import DashboardLayout from '../components/Dashboard/DashboardLayout';

// Implemented components
const DashboardOverview = React.lazy(() => import('../pages/Dashboard/DashboardOverview'));
const SupportFormPage = React.lazy(() => import('../pages/Dashboard/SupportFormPage'));
const AdminDashboard = React.lazy(() => import('../pages/Dashboard/AdminDashboard'));

// Error Fallback Component
const ErrorFallback: React.FC<{ error: Error; resetErrorBoundary: () => void }> = ({ error, resetErrorBoundary }) => (
  <Box sx={{ p: 4, textAlign: 'center' }}>
    <Typography variant="h5" color="error" gutterBottom>
      Une erreur s'est produite
    </Typography>
    <Typography variant="body1" color="text.secondary" paragraph>
      {error.message}
    </Typography>
    <Button variant="contained" onClick={resetErrorBoundary}>
      Réessayer
    </Button>
  </Box>
);

// Loading Component
const LoadingComponent: React.FC = () => (
  <Box sx={{ p: 4, textAlign: 'center' }}>
    <Typography variant="h6">Chargement...</Typography>
  </Box>
);

// Temporary placeholder component for development
const PlaceholderPage: React.FC<{ title: string }> = ({ title }) => (
  <Box sx={{ p: 4, textAlign: 'center' }}>
    <Typography variant="h4" gutterBottom>{title}</Typography>
    <Typography variant="body1" color="text.secondary">
      Cette page sera implémentée dans les prochaines phases.
    </Typography>
  </Box>
);

const DashboardRoutes: React.FC = () => {
  return (
    <ErrorBoundary
      FallbackComponent={ErrorFallback}
      onReset={() => window.location.reload()}
    >
      <Routes>
        <Route path="/" element={<DashboardLayout />}>
          {/* Default redirect */}
          <Route index element={<Navigate to="/dashboard/overview" replace />} />

          {/* Main dashboard pages */}
          <Route
            path="overview"
            element={
              <React.Suspense fallback={<LoadingComponent />}>
                <DashboardOverview />
              </React.Suspense>
            }
          />
          <Route
            path="support-form"
            element={
              <React.Suspense fallback={<LoadingComponent />}>
                <SupportFormPage />
              </React.Suspense>
            }
          />

          {/* Admin routes */}
          <Route
            path="admin"
            element={
              <React.Suspense fallback={<div>Chargement...</div>}>
                <AdminDashboard />
              </React.Suspense>
            }
          />

          {/* Placeholder routes */}
          <Route
            path="admin/conversations"
            element={
              <React.Suspense fallback={<div>Chargement...</div>}>
                <PlaceholderPage title="Gestion des Conversations" />
              </React.Suspense>
            }
          />
          <Route
            path="admin/clients"
            element={
              <React.Suspense fallback={<div>Chargement...</div>}>
                <PlaceholderPage title="Gestion des Clients" />
              </React.Suspense>
            }
          />
          <Route
            path="analytics"
            element={
              <React.Suspense fallback={<div>Chargement...</div>}>
                <PlaceholderPage title="Analytics" />
              </React.Suspense>
            }
          />
          <Route
            path="agent-copilot"
            element={
              <React.Suspense fallback={<div>Chargement...</div>}>
                <PlaceholderPage title="Agent Copilot" />
              </React.Suspense>
            }
          />

          {/* Catch all - redirect to overview */}
          <Route path="*" element={<Navigate to="/dashboard/overview" replace />} />
        </Route>
      </Routes>
    </ErrorBoundary>
  );
};

export default DashboardRoutes;