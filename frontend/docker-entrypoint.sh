#!/bin/sh
# =============================================
# 🚀 FRONTEND DOCKER ENTRYPOINT SCRIPT
# Free Mobile Chatbot Dashboard - Phase 4 Production
# Environment variable injection and Nginx startup
# =============================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting Free Mobile Chatbot Frontend...${NC}"

# Function to log messages
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

# Default environment variables
export REACT_APP_API_URL=${REACT_APP_API_URL:-"http://localhost:5000"}
export REACT_APP_WS_URL=${REACT_APP_WS_URL:-"http://localhost:5000"}
export REACT_APP_ENV=${REACT_APP_ENV:-"production"}
export REACT_APP_ML_ENABLED=${REACT_APP_ML_ENABLED:-"true"}

log "Environment Configuration:"
log "  API URL: $REACT_APP_API_URL"
log "  WebSocket URL: $REACT_APP_WS_URL"
log "  Environment: $REACT_APP_ENV"
log "  ML Enabled: $REACT_APP_ML_ENABLED"

# Create runtime environment configuration
log "Creating runtime environment configuration..."

# Create env-config.js file for runtime environment variables
cat > /usr/share/nginx/html/env-config.js << EOF
window._env_ = {
  REACT_APP_API_URL: "${REACT_APP_API_URL}",
  REACT_APP_WS_URL: "${REACT_APP_WS_URL}",
  REACT_APP_ENV: "${REACT_APP_ENV}",
  REACT_APP_ML_ENABLED: "${REACT_APP_ML_ENABLED}",
  REACT_APP_ML_WEBSOCKET_ENABLED: "${REACT_APP_ML_WEBSOCKET_ENABLED:-true}",
  REACT_APP_NOTIFICATIONS_ENABLED: "${REACT_APP_NOTIFICATIONS_ENABLED:-true}",
  REACT_APP_CACHE_ENABLED: "${REACT_APP_CACHE_ENABLED:-true}",
  REACT_APP_DEBUG_MODE: "${REACT_APP_DEBUG_MODE:-false}"
};
EOF

# Inject environment variables into index.html
log "Injecting environment variables into index.html..."

# Replace environment variables in built files
find /usr/share/nginx/html -name "*.js" -exec sed -i "s|REACT_APP_API_URL_PLACEHOLDER|${REACT_APP_API_URL}|g" {} \;
find /usr/share/nginx/html -name "*.js" -exec sed -i "s|REACT_APP_WS_URL_PLACEHOLDER|${REACT_APP_WS_URL}|g" {} \;

# Update Nginx configuration with environment variables
log "Updating Nginx configuration..."

# Replace backend upstream server if specified
if [ -n "$BACKEND_HOST" ]; then
    sed -i "s|server backend:5000|server ${BACKEND_HOST}:${BACKEND_PORT:-5000}|g" /etc/nginx/conf.d/default.conf
fi

if [ -n "$ML_SERVICE_HOST" ]; then
    sed -i "s|server ml-service:5001|server ${ML_SERVICE_HOST}:${ML_SERVICE_PORT:-5001}|g" /etc/nginx/conf.d/default.conf
fi

# Validate Nginx configuration
log "Validating Nginx configuration..."
if nginx -t; then
    log "✅ Nginx configuration is valid"
else
    error "❌ Nginx configuration is invalid"
    exit 1
fi

# Create necessary directories
log "Creating necessary directories..."
mkdir -p /var/log/nginx
mkdir -p /var/cache/nginx

# Set proper permissions
chown -R frontend:nginx-user /var/log/nginx
chown -R frontend:nginx-user /var/cache/nginx

# Health check function
health_check() {
    log "Performing health check..."
    
    # Check if static files exist
    if [ ! -f "/usr/share/nginx/html/index.html" ]; then
        error "index.html not found"
        return 1
    fi
    
    # Check if env-config.js was created
    if [ ! -f "/usr/share/nginx/html/env-config.js" ]; then
        error "env-config.js not found"
        return 1
    fi
    
    log "✅ Health check passed"
    return 0
}

# Perform health check
if ! health_check; then
    error "Health check failed"
    exit 1
fi

# Signal handling for graceful shutdown
trap 'log "Received SIGTERM, shutting down gracefully..."; nginx -s quit; exit 0' TERM
trap 'log "Received SIGINT, shutting down gracefully..."; nginx -s quit; exit 0' INT

log "🎉 Frontend startup complete!"
log "🌐 Frontend available at: http://localhost:3001"
log "📊 Dashboard available at: http://localhost:3001/dashboard"

# Start Nginx
if [ "$1" = "nginx" ]; then
    log "Starting Nginx..."
    exec "$@"
else
    log "Executing custom command: $@"
    exec "$@"
fi
