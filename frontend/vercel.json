{"version": 2, "name": "free-mobile-chatbot-frontend", "builds": [{"src": "package.json", "use": "@vercel/static-build", "config": {"distDir": "build"}}], "routes": [{"src": "/static/(.*)", "headers": {"cache-control": "s-maxage=31536000,immutable"}, "dest": "/static/$1"}, {"src": "/favicon.ico", "dest": "/favicon.ico"}, {"src": "/manifest.json", "dest": "/manifest.json"}, {"src": "/(.*)", "dest": "/index.html"}], "env": {"REACT_APP_API_URL": "https://api.chatbot-free-mobile.com", "REACT_APP_SOCKET_URL": "https://api.chatbot-free-mobile.com", "REACT_APP_ENVIRONMENT": "production"}, "build": {"env": {"CI": "false", "GENERATE_SOURCEMAP": "false"}}, "functions": {"app/api/**/*.js": {"runtime": "nodejs18.x"}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https://fonts.gstatic.com; connect-src 'self' https://api.chatbot-free-mobile.com wss://api.chatbot-free-mobile.com; frame-ancestors 'none';"}]}], "rewrites": [{"source": "/api/(.*)", "destination": "https://api.chatbot-free-mobile.com/api/$1"}]}