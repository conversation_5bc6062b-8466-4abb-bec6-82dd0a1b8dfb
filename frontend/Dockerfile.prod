# =============================================
# 🎨 FRONTEND REACT - PRODUCTION DOCKERFILE
# Free Mobile Chatbot Dashboard - Phase 4 Production
# Multi-stage build for optimized production image
# =============================================

# Stage 1: Build stage
FROM node:18-alpine as builder

# Set environment variables
ENV NODE_ENV=production \
    NPM_CONFIG_LOGLEVEL=warn \
    GENERATE_SOURCEMAP=false \
    INLINE_RUNTIME_CHUNK=false

# Install system dependencies
RUN apk add --no-cache \
    git \
    python3 \
    make \
    g++ \
    && rm -rf /var/cache/apk/*

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --silent && \
    npm cache clean --force

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Remove source maps and unnecessary files for production
RUN find build -name "*.map" -delete && \
    find build -name "*.txt" -delete

# Stage 2: Production runtime with Nginx
FROM nginx:1.25-alpine as production

# Set environment variables
ENV NGINX_WORKER_PROCESSES=auto \
    NGINX_WORKER_CONNECTIONS=1024 \
    NGINX_KEEPALIVE_TIMEOUT=65

# Install security updates
RUN apk update && apk upgrade && \
    apk add --no-cache \
    curl \
    && rm -rf /var/cache/apk/*

# Copy custom Nginx configuration
COPY nginx.prod.conf /etc/nginx/nginx.conf
COPY nginx-default.prod.conf /etc/nginx/conf.d/default.conf

# Copy built application from builder stage
COPY --from=builder /app/build /usr/share/nginx/html

# Copy environment configuration script
COPY docker-entrypoint.sh /docker-entrypoint.sh
RUN chmod +x /docker-entrypoint.sh

# Create non-root user for security
RUN addgroup -g 1001 -S nginx-user && \
    adduser -S frontend -u 1001 -G nginx-user

# Set proper permissions
RUN chown -R frontend:nginx-user /usr/share/nginx/html && \
    chown -R frontend:nginx-user /var/cache/nginx && \
    chown -R frontend:nginx-user /var/log/nginx && \
    chown -R frontend:nginx-user /etc/nginx/conf.d && \
    touch /var/run/nginx.pid && \
    chown -R frontend:nginx-user /var/run/nginx.pid

# Create necessary directories
RUN mkdir -p /var/cache/nginx/client_temp \
             /var/cache/nginx/proxy_temp \
             /var/cache/nginx/fastcgi_temp \
             /var/cache/nginx/uwsgi_temp \
             /var/cache/nginx/scgi_temp && \
    chown -R frontend:nginx-user /var/cache/nginx

# Switch to non-root user
USER frontend

# Expose port
EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:3001/health || exit 1

# Start Nginx with custom entrypoint
ENTRYPOINT ["/docker-entrypoint.sh"]
CMD ["nginx", "-g", "daemon off;"]
