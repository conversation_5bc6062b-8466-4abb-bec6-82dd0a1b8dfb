import { test, expect } from '@playwright/test'; import { TestHelpers } from './utils/test-helpers'; import path from 'path'; test.describe('Support Form', () => { let helpers: TestHelpers; test.beforeEach(async ({ page }) => { helpers = new TestHelpers(page); await page.goto('/dashboard/support-form'); await helpers.waitForDashboardLoad(); }); test('should display support form with all required fields', async ({ page }) => { // Check form title await expect(page.getByText('Contacter le support Free')).toBeVisible(); // Check all form fields are present await expect(page.getByLabel('Nom complet')).toBeVisible(); await expect(page.getByLabel('Adresse email')).toBeVisible(); await expect(page.getByLabel('Catégorie du problème')).toBeVisible(); await expect(page.getByLabel('Description du problème')).toBeVisible(); // Check submit button await expect(page.getByRole('button', { name: 'Envoyer ma demande' })).toBeVisible(); await expect(page.getByRole('button', { name: 'Envoyer ma demande' })).toBeDisabled(); }); test('should validate required fields', async ({ page }) => { const submitButton = page.getByRole('button', { name: 'Envoyer ma demande' }); // Initially disabled await expect(submitButton).toBeDisabled(); // Fill name only await helpers.fillField('input[name="fullName"], [label="Nom complet"] input', 'Jean Dupont'); await expect(submitButton).toBeDisabled(); // Fill email await helpers.fillField('input[name="email"], [label="Adresse email"] input', '<EMAIL>'); await expect(submitButton).toBeDisabled(); // Select category await page.getByLabel('Catégorie du problème').click(); await page.getByRole('option', { name: 'Problème technique' }).click(); await expect(submitButton).toBeDisabled(); // Fill description await helpers.fillField('textarea[name="description"], [label="Description du problème"] textarea', 'Mon WiFi ne fonctionne plus depuis ce matin'); // Now button should be enabled await expect(submitButton).toBeEnabled(); }); test('should validate email format', async ({ page }) => { const emailField = page.getByLabel('Adresse email'); // Enter invalid email await emailField.fill('invalid-email'); await emailField.blur(); // Check for validation error (HTML5 validation or custom) const isInvalid = await emailField.evaluate((el: HTMLInputElement) => !el.validity.valid); expect(isInvalid).toBeTruthy(); // Enter valid email await emailField.fill('<EMAIL>'); await emailField.blur(); const isValid = await emailField.evaluate((el: HTMLInputElement) => el.validity.valid); expect(isValid).toBeTruthy(); }); test('should display all support categories', async ({ page }) => { await page.getByLabel('Catégorie du problème').click(); // Check all expected categories await expect(page.getByRole('option', { name: 'Facturation' })).toBeVisible(); await expect(page.getByRole('option', { name: 'Problème technique' })).toBeVisible(); await expect(page.getByRole('option', { name: 'Abonnement' })).toBeVisible(); await expect(page.getByRole('option', { name: 'Réseau' })).toBeVisible(); await expect(page.getByRole('option', { name: 'Appareil' })).toBeVisible(); await expect(page.getByRole('option', { name: 'Autre' })).toBeVisible(); }); test('should handle file upload', async ({ page }) => { // Create a test file const testFilePath = path.join(__dirname, 'fixtures', 'test-document.txt'); // Mock file upload area const fileUploadArea = page.locator('#file-upload'); if (await fileUploadArea.count() > 0) { await fileUploadArea.setInputFiles(testFilePath); // Check if file is displayed await expect(page.getByText('test-document.txt')).toBeVisible(); } else { // Alternative: look for file input const fileInput = page.locator('input[type="file"]'); if (await fileInput.count() > 0) { await fileInput.setInputFiles(testFilePath); } } }); test('should submit form successfully', async ({ page }) => { // Mock API response await helpers.mockApiResponse(/\/api\/support\/tickets/, { success: true, ticketId: 'TICKET-12345', message: 'Ticket créé avec succès' }); const testData = helpers.generateTestData(); // Fill form await helpers.fillField('input[name="fullName"], [label="Nom complet"] input', testData.user.name); await helpers.fillField('input[name="email"], [label="Adresse email"] input', testData.user.email); await page.getByLabel('Catégorie du problème').click(); await page.getByRole('option', { name: 'Problème technique' }).click(); await helpers.fillField('textarea[name="description"], [label="Description du problème"] textarea', testData.ticket.description); // Submit form await page.getByRole('button', { name: 'Envoyer ma demande' }).click(); // Check for success message await expect(page.getByText(/demande a été envoyée avec succès/)).toBeVisible({ timeout: 10000 }); // Form should be reset await expect(page.getByLabel('Nom complet')).toHaveValue(''); await expect(page.getByLabel('Adresse email')).toHaveValue(''); }); test('should handle form submission errors', async ({ page }) => { // Mock API error response await helpers.mockApiResponse(/\/api\/support\/tickets/, { success: false, error: 'Service temporairement indisponible' }); const testData = helpers.generateTestData(); // Fill and submit form await helpers.fillField('input[name="fullName"], [label="Nom complet"] input', testData.user.name); await helpers.fillField('input[name="email"], [label="Adresse email"] input', testData.user.email); await page.getByLabel('Catégorie du problème').click(); await page.getByRole('option', { name: 'Problème technique' }).click(); await helpers.fillField('textarea[name="description"], [label="Description du problème"] textarea', testData.ticket.description); await page.getByRole('button', { name: 'Envoyer ma demande' }).click(); // Check for error message await expect(page.getByText(/erreur est survenue/)).toBeVisible({ timeout: 10000 }); }); test('should show loading state during submission', async ({ page }) => { // Mock delayed API response await page.route(/\/api\/support\/tickets/, async route => { await new Promise(resolve => setTimeout(resolve, 2000)); await route.fulfill({ status: 200, contentType: 'application/json', body: JSON.stringify({ success: true }) }); }); const testData = helpers.generateTestData(); // Fill form await helpers.fillField('input[name="fullName"], [label="Nom complet"] input', testData.user.name); await helpers.fillField('input[name="email"], [label="Adresse email"] input', testData.user.email); await page.getByLabel('Catégorie du problème').click(); await page.getByRole('option', { name: 'Problème technique' }).click(); await helpers.fillField('textarea[name="description"], [label="Description du problème"] textarea', testData.ticket.description); // Submit form await page.getByRole('button', { name: 'Envoyer ma demande' }).click(); // Check loading state await expect(page.getByText('Envoi en cours...')).toBeVisible(); await expect(page.getByRole('button', { name: /Envoi en cours/ })).toBeDisabled(); // Wait for completion await expect(page.getByText(/demande a été envoyée avec succès/)).toBeVisible({ timeout: 15000 }); }); test('should be accessible', async ({ page }) => { await helpers.checkAccessibility(); // Check form labels const nameField = page.getByLabel('Nom complet'); await expect(nameField).toBeVisible(); const emailField = page.getByLabel('Adresse email'); await expect(emailField).toBeVisible(); // Check required field indicators const requiredFields = page.locator('[required]'); const count = await requiredFields.count(); expect(count).toBeGreaterThan(0); // Check keyboard navigation await page.keyboard.press('Tab'); await expect(nameField).toBeFocused(); }); test('should work on mobile devices', async ({ page }) => { await page.setViewportSize({ width: 375, height: 667 }); // Form should be responsive await expect(page.getByText('Contacter le support Free')).toBeVisible(); // Fields should be stacked vertically on mobile const nameField = page.getByLabel('Nom complet'); const emailField = page.getByLabel('Adresse email'); const nameBox = await nameField.boundingBox(); const emailBox = await emailField.boundingBox(); // On mobile, email field should be below name field if (nameBox && emailBox) { expect(emailBox.y).toBeGreaterThan(nameBox.y); } }); });