import { Page, expect } from '@playwright/test'; /** * Test utilities for Free Mobile Dashboard E2E tests */ export class TestHelpers { constructor(private page: Page) {} /** * Wait for the dashboard to load completely */ async waitForDashboardLoad() { // Wait for main navigation to be visible await this.page.waitForSelector('[data-testid="top-navigation"], .MuiTabs-root', { timeout: 10000 }); // Wait for any loading spinners to disappear await this.page.waitForFunction(() => { const spinners = document.querySelectorAll('.MuiCircularProgress-root, [data-testid="loading"]'); return spinners.length === 0; }, { timeout: 10000 }); } /** * Navigate to a specific dashboard section */ async navigateToSection(section: 'overview' | 'support-form' | 'admin' | 'analytics') { const sectionMap = { 'overview': 'Support Client', 'support-form': 'Formulaire Support', 'admin': 'Panel Admin', 'analytics': 'Analytics' }; const tabName = sectionMap[section]; await this.page.getByRole('tab', { name: tabName }).click(); await this.waitForDashboardLoad(); } /** * Fill form field with validation */ async fillField(selector: string, value: string, options?: { validate?: boolean }) { const field = this.page.locator(selector); await field.fill(value); if (options?.validate) { await expect(field).toHaveValue(value); } } /** * Upload file to file input */ async uploadFile(selector: string, filePath: string) { const fileInput = this.page.locator(selector); await fileInput.setInputFiles(filePath); } /** * Wait for API response */ async waitForApiResponse(urlPattern: string | RegExp, timeout = 10000) { return await this.page.waitForResponse(urlPattern, { timeout }); } /** * Check if element is visible in viewport */ async isInViewport(selector: string): Promise<boolean> { return await this.page.locator(selector).isInViewport(); } /** * Scroll element into view */ async scrollIntoView(selector: string) { await this.page.locator(selector).scrollIntoViewIfNeeded(); } /** * Take screenshot with timestamp */ async takeScreenshot(name: string) { const timestamp = new Date().toISOString().replace(/[:.]/g, '-'); await this.page.screenshot({ path: `test-results/screenshots/${name}-${timestamp}.png`, fullPage: true }); } /** * Check accessibility violations */ async checkAccessibility() { // This would integrate with axe-core for accessibility testing // For now, we'll check basic accessibility features // Check for alt text on images const images = await this.page.locator('img').all(); for (const img of images) { const alt = await img.getAttribute('alt'); if (!alt) { console.warn('Image without alt text found'); } } // Check for proper heading hierarchy const headings = await this.page.locator('h1, h2, h3, h4, h5, h6').all(); console.log(`Found ${headings.length} headings on page`); // Check for form labels const inputs = await this.page.locator('input[type="text"], input[type="email"], textarea').all(); for (const input of inputs) { const id = await input.getAttribute('id'); const ariaLabel = await input.getAttribute('aria-label'); const ariaLabelledBy = await input.getAttribute('aria-labelledby'); if (!ariaLabel && !ariaLabelledBy && id) { const label = await this.page.locator(`label[for="${id}"]`).count(); if (label === 0) { console.warn('Input without proper label found'); } } } } /** * Mock API responses for testing */ async mockApiResponse(url: string | RegExp, response: any) { await this.page.route(url, async route => { await route.fulfill({ status: 200, contentType: 'application/json', body: JSON.stringify(response) }); }); } /** * Wait for element to be stable (not moving) */ async waitForStable(selector: string, timeout = 5000) { const element = this.page.locator(selector); await element.waitFor({ state: 'visible', timeout }); // Wait for element to stop moving let previousBox = await element.boundingBox(); let stableCount = 0; const requiredStableCount = 3; while (stableCount < requiredStableCount) { await this.page.waitForTimeout(100); const currentBox = await element.boundingBox(); if (previousBox && currentBox && previousBox.x === currentBox.x && previousBox.y === currentBox.y) { stableCount++; } else { stableCount = 0; } previousBox = currentBox; } } /** * Generate test data */ generateTestData() { const timestamp = Date.now(); return { user: { name: `Test User ${timestamp}`, email: `test${timestamp}@example.com`, phone: `06${Math.floor(Math.random() * 100000000).toString().padStart(8, '0')}`, }, ticket: { subject: `Test Issue ${timestamp}`, description: `This is a test description created at ${new Date().toISOString()}`, category: 'technique', } }; } } /** * Custom expect matchers for dashboard testing */ export const dashboardExpect = { async toBeInDashboardSection(page: Page, section: string) { const url = page.url(); expect(url).toContain(`/dashboard/${section}`); }, async toHaveValidForm(page: Page, formSelector: string) { const form = page.locator(formSelector); await expect(form).toBeVisible(); // Check for required fields const requiredFields = await form.locator('[required]').all(); expect(requiredFields.length).toBeGreaterThan(0); }, async toHaveAccessibleElements(page: Page) { // Check for skip links const skipLinks = await page.locator('a[href^="#"]').first(); if (await skipLinks.count() > 0) { expect(await skipLinks.isVisible()).toBeTruthy(); } // Check for proper ARIA labels const buttons = await page.locator('button').all(); for (const button of buttons) { const ariaLabel = await button.getAttribute('aria-label'); const text = await button.textContent(); expect(ariaLabel || text).toBeTruthy(); } } };