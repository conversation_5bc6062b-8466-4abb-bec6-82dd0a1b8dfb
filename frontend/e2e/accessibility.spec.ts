import { test, expect } from '@playwright/test'; import AxeBuilder from '@axe-core/playwright'; import { TestHelpers } from './utils/test-helpers'; test.describe('Accessibility Tests', () => { let helpers: TestHelpers; test.beforeEach(async ({ page }) => { helpers = new TestHelpers(page); }); test('should not have accessibility violations on dashboard overview', async ({ page }) => { await page.goto('/dashboard/overview'); await helpers.waitForDashboardLoad(); const accessibilityScanResults = await new AxeBuilder({ page }).analyze(); expect(accessibilityScanResults.violations).toEqual([]); }); test('should not have accessibility violations on support form', async ({ page }) => { await page.goto('/dashboard/support-form'); await helpers.waitForDashboardLoad(); const accessibilityScanResults = await new AxeBuilder({ page }).analyze(); expect(accessibilityScanResults.violations).toEqual([]); }); test('should not have accessibility violations on admin panel', async ({ page }) => { await page.goto('/dashboard/admin'); await helpers.waitForDashboardLoad(); const accessibilityScanResults = await new AxeBuilder({ page }).analyze(); expect(accessibilityScanResults.violations).toEqual([]); }); test('should not have accessibility violations on analytics dashboard', async ({ page }) => { await page.goto('/dashboard/analytics'); await helpers.waitForDashboardLoad(); const accessibilityScanResults = await new AxeBuilder({ page }).analyze(); expect(accessibilityScanResults.violations).toEqual([]); }); test('should have proper heading hierarchy', async ({ page }) => { await page.goto('/dashboard/overview'); await helpers.waitForDashboardLoad(); // Check for h1 element const h1Elements = await page.locator('h1').count(); expect(h1Elements).toBeGreaterThanOrEqual(1); // Check heading order const headings = await page.locator('h1, h2, h3, h4, h5, h6').all(); const headingLevels = await Promise.all( headings.map(async (heading) => { const tagName = await heading.evaluate(el => el.tagName.toLowerCase()); return parseInt(tagName.charAt(1)); }) ); // Verify heading hierarchy (no skipping levels) for (let i = 1; i < headingLevels.length; i++) { const currentLevel = headingLevels[i]; const previousLevel = headingLevels[i - 1]; // Allow same level, one level deeper, or any level shallower expect(currentLevel <= previousLevel + 1).toBeTruthy(); } }); test('should have proper form labels', async ({ page }) => { await page.goto('/dashboard/support-form'); await helpers.waitForDashboardLoad(); // Check all form inputs have labels const inputs = await page.locator('input[type="text"], input[type="email"], textarea, select').all(); for (const input of inputs) { const id = await input.getAttribute('id'); const ariaLabel = await input.getAttribute('aria-label'); const ariaLabelledBy = await input.getAttribute('aria-labelledby'); if (id) { const associatedLabel = await page.locator(`label[for="${id}"]`).count(); expect(associatedLabel > 0 || ariaLabel || ariaLabelledBy).toBeTruthy(); } else { expect(ariaLabel || ariaLabelledBy).toBeTruthy(); } } }); test('should have sufficient color contrast', async ({ page }) => { await page.goto('/dashboard/overview'); await helpers.waitForDashboardLoad(); // Use axe-core to check color contrast const accessibilityScanResults = await new AxeBuilder({ page }) .withTags(['wcag2a', 'wcag2aa', 'wcag21aa']) .analyze(); const colorContrastViolations = accessibilityScanResults.violations.filter( violation => violation.id === 'color-contrast' ); expect(colorContrastViolations).toEqual([]); }); test('should be keyboard navigable', async ({ page }) => { await page.goto('/dashboard/support-form'); await helpers.waitForDashboardLoad(); // Start keyboard navigation await page.keyboard.press('Tab'); // Check that focus is visible const focusedElement = await page.locator(':focus').first(); await expect(focusedElement).toBeVisible(); // Navigate through form elements const formElements = await page.locator('input, button, select, textarea, [tabindex="0"]').all(); for (let i = 0; i < Math.min(formElements.length, 10); i++) { await page.keyboard.press('Tab'); const currentFocused = await page.locator(':focus').first(); await expect(currentFocused).toBeVisible(); } }); test('should have proper ARIA attributes', async ({ page }) => { await page.goto('/dashboard/admin'); await helpers.waitForDashboardLoad(); // Check buttons have accessible names const buttons = await page.locator('button').all(); for (const button of buttons) { const ariaLabel = await button.getAttribute('aria-label'); const textContent = await button.textContent(); const ariaLabelledBy = await button.getAttribute('aria-labelledby'); expect(ariaLabel || textContent?.trim() || ariaLabelledBy).toBeTruthy(); } // Check interactive elements have proper roles const interactiveElements = await page.locator('[role="button"], [role="tab"], [role="menuitem"]').all(); for (const element of interactiveElements) { const role = await element.getAttribute('role'); expect(role).toBeTruthy(); } }); test('should have proper focus management', async ({ page }) => { await page.goto('/dashboard/admin'); await helpers.waitForDashboardLoad(); // Test modal focus management (if modals exist) const modalTriggers = await page.locator('[data-testid*="modal"], button').filter({ hasText: /ouvrir|voir|détails/i }).all(); if (modalTriggers.length > 0) { const trigger = modalTriggers[0]; await trigger.click(); // Focus should move to modal await page.waitForTimeout(500); const focusedElement = await page.locator(':focus').first(); // Check if focus is within modal const modal = await page.locator('[role="dialog"], .MuiModal-root, .MuiDialog-root').first(); if (await modal.count() > 0) { const isWithinModal = await focusedElement.evaluate((focused, modalEl) => { return modalEl.contains(focused); }, await modal.elementHandle()); expect(isWithinModal).toBeTruthy(); } } }); test('should have proper skip links', async ({ page }) => { await page.goto('/dashboard/overview'); await helpers.waitForDashboardLoad(); // Check for skip to main content link const skipLinks = await page.locator('a[href^="#"]').filter({ hasText: /skip|main|content/i }).all(); if (skipLinks.length > 0) { const skipLink = skipLinks[0]; await expect(skipLink).toBeVisible(); // Test skip link functionality await skipLink.click(); const targetId = await skipLink.getAttribute('href'); if (targetId) { const target = page.locator(targetId); await expect(target).toBeVisible(); } } }); test('should have proper table accessibility', async ({ page }) => { await page.goto('/dashboard/admin/clients'); await helpers.waitForDashboardLoad(); const tables = await page.locator('table').all(); for (const table of tables) { // Check for table headers const headers = await table.locator('th').count(); expect(headers).toBeGreaterThan(0); // Check for proper table structure const caption = await table.locator('caption').count(); const summary = await table.getAttribute('summary'); const ariaLabel = await table.getAttribute('aria-label'); const ariaLabelledBy = await table.getAttribute('aria-labelledby'); // Table should have some form of accessible description expect(caption > 0 || summary || ariaLabel || ariaLabelledBy).toBeTruthy(); } }); test('should handle screen reader announcements', async ({ page }) => { await page.goto('/dashboard/support-form'); await helpers.waitForDashboardLoad(); // Check for live regions const liveRegions = await page.locator('[aria-live], [role="status"], [role="alert"]').all(); // Fill form to trigger validation messages const nameField = page.getByLabel('Nom complet'); await nameField.fill('Test'); await nameField.clear(); await nameField.blur(); // Check if validation messages are announced const errorMessages = await page.locator('[role="alert"], .error, .MuiFormHelperText-root').all(); for (const message of errorMessages) { const ariaLive = await message.getAttribute('aria-live'); const role = await message.getAttribute('role'); // Error messages should be announced expect(ariaLive === 'polite' || ariaLive === 'assertive' || role === 'alert').toBeTruthy(); } }); test('should be usable with high contrast mode', async ({ page }) => { // Simulate high contrast mode await page.emulateMedia({ colorScheme: 'dark', reducedMotion: 'reduce' }); await page.goto('/dashboard/overview'); await helpers.waitForDashboardLoad(); // Check that content is still visible await expect(page.getByText('Bienvenue sur le support Free Mobile')).toBeVisible(); // Check that interactive elements are distinguishable const buttons = await page.locator('button').all(); for (const button of buttons) { await expect(button).toBeVisible(); } }); test('should support reduced motion preferences', async ({ page }) => { // Simulate reduced motion preference await page.emulateMedia({ reducedMotion: 'reduce' }); await page.goto('/dashboard/analytics'); await helpers.waitForDashboardLoad(); // Check that animations are reduced or disabled // This would depend on CSS implementation const animatedElements = await page.locator('[style*="transition"], [style*="animation"]').all(); // In a proper implementation, animations should be disabled or reduced // For now, we just verify the page loads correctly await expect(page.getByText('Analytics Dashboard')).toBeVisible(); }); test('should work with screen reader simulation', async ({ page }) => { await page.goto('/dashboard/overview'); await helpers.waitForDashboardLoad(); // Simulate screen reader navigation await page.keyboard.press('Tab'); // Check that all content is accessible via keyboard const interactiveElements = await page.locator('button, a, input, select, textarea, [tabindex="0"]').all(); let focusableCount = 0; for (let i = 0; i < Math.min(interactiveElements.length, 20); i++) { await page.keyboard.press('Tab'); const focused = await page.locator(':focus').first(); if (await focused.isVisible()) { focusableCount++; } } expect(focusableCount).toBeGreaterThan(0); }); });