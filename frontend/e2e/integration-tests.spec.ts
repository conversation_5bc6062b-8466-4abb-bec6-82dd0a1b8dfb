import { test, expect } from '@playwright/test'; test.describe('Integration Tests - End-to-End User Journeys', () => { test.beforeEach(async ({ page }) => { // Start fresh for each test await page.goto('/'); }); test.describe('Complete Support Ticket Journey', () => { test('should create, manage, and resolve a support ticket end-to-end', async ({ page, context }) => { // Step 1: Customer creates a support ticket await page.goto('/login'); await page.fill('[data-testid="email-input"]', '<EMAIL>'); await page.fill('[data-testid="password-input"]', 'TestCustomer123!'); await page.click('[data-testid="login-button"]'); await page.waitForURL('**/dashboard'); // Navigate to support form await page.goto('/support'); await page.waitForLoadState('networkidle'); // Fill out support ticket const ticketTitle = `Integration Test Ticket ${Date.now()}`; await page.fill('[data-testid="ticket-title"]', ticketTitle); await page.fill('[data-testid="ticket-description"]', 'This is an integration test ticket with file attachment'); await page.selectOption('[data-testid="ticket-category"]', 'technical'); await page.selectOption('[data-testid="ticket-priority"]', 'high'); // Upload a file const fileInput = page.locator('input[type="file"]'); await fileInput.setInputFiles({ name: 'test-document.txt', mimeType: 'text/plain', buffer: Buffer.from('This is a test document for integration testing') }); // Submit ticket await page.click('[data-testid="submit-ticket-button"]'); await expect(page.locator('text=*créé avec succès*')).toBeVisible(); // Step 2: Agent receives and processes the ticket const agentPage = await context.newPage(); await agentPage.goto('/login'); await agentPage.fill('[data-testid="email-input"]', '<EMAIL>'); await agentPage.fill('[data-testid="password-input"]', 'TestAgent123!'); await agentPage.click('[data-testid="login-button"]'); await agentPage.waitForURL('**/dashboard'); // Navigate to agent dashboard await agentPage.goto('/agent/tickets'); await agentPage.waitForLoadState('networkidle'); // Find the new ticket await expect(agentPage.locator(`text=${ticketTitle}`)).toBeVisible({ timeout: 10000 }); // Open the ticket await agentPage.click(`[data-testid="ticket-row"]:has-text("${ticketTitle}")`); // Update ticket status await agentPage.selectOption('[data-testid="ticket-status-select"]', 'in_progress'); await agentPage.click('[data-testid="update-ticket-button"]'); // Add a response await agentPage.fill('[data-testid="agent-response"]', 'Thank you for your ticket. We are investigating the issue.'); await agentPage.click('[data-testid="send-response-button"]'); // Step 3: Verify real-time updates on customer side await page.goto('/support/my-tickets'); await expect(page.locator('text=*en cours*')).toBeVisible({ timeout: 10000 }); await expect(page.locator('text=*investigating*')).toBeVisible(); // Step 4: Admin monitors via analytics const adminPage = await context.newPage(); await adminPage.goto('/login'); await adminPage.fill('[data-testid="email-input"]', '<EMAIL>'); await adminPage.fill('[data-testid="password-input"]', 'TestAdmin123!'); await adminPage.click('[data-testid="login-button"]'); await adminPage.waitForURL('**/dashboard'); await adminPage.goto('/analytics'); await adminPage.waitForLoadState('networkidle'); // Verify ticket appears in analytics const totalTickets = await adminPage.locator('[data-testid="kpi-total-tickets"] .value').textContent(); expect(parseInt(totalTickets || '0')).toBeGreaterThan(0); // Step 5: Resolve the ticket await agentPage.selectOption('[data-testid="ticket-status-select"]', 'resolved'); await agentPage.fill('[data-testid="resolution-notes"]', 'Issue has been resolved. Please test and confirm.'); await agentPage.click('[data-testid="resolve-ticket-button"]'); // Step 6: Customer confirms resolution await page.reload(); await expect(page.locator('text=*résolu*')).toBeVisible({ timeout: 10000 }); // Close additional pages await agentPage.close(); await adminPage.close(); }); }); test.describe('Real-time Chat Integration', () => { test('should handle multi-user chat with file sharing', async ({ page, context }) => { // Setup: Login as customer await page.goto('/login'); await page.fill('[data-testid="email-input"]', '<EMAIL>'); await page.fill('[data-testid="password-input"]', 'TestCustomer123!'); await page.click('[data-testid="login-button"]'); await page.waitForURL('**/dashboard'); // Navigate to chat await page.goto('/chat'); await page.waitForLoadState('networkidle'); // Verify WebSocket connection await expect(page.locator('[data-testid="connection-status"]')).toHaveText(/connecté|connected/i); // Setup: Agent joins chat const agentPage = await context.newPage(); await agentPage.goto('/login'); await agentPage.fill('[data-testid="email-input"]', '<EMAIL>'); await agentPage.fill('[data-testid="password-input"]', 'TestAgent123!'); await agentPage.click('[data-testid="login-button"]'); await agentPage.waitForURL('**/dashboard'); await agentPage.goto('/chat'); await agentPage.waitForLoadState('networkidle'); // Customer sends initial message const customerMessage = `Hello, I need help with my account ${Date.now()}`; await page.fill('[data-testid="message-input"]', customerMessage); await page.click('[data-testid="send-button"]'); // Verify message appears on both sides await expect(page.locator(`text=${customerMessage}`)).toBeVisible(); await expect(agentPage.locator(`text=${customerMessage}`)).toBeVisible({ timeout: 10000 }); // Agent responds const agentMessage = `Hello! I'm here to help you. Can you provide more details?`; await agentPage.fill('[data-testid="message-input"]', agentMessage); await agentPage.click('[data-testid="send-button"]'); // Verify agent message appears on customer side await expect(page.locator(`text=${agentMessage}`)).toBeVisible({ timeout: 10000 }); // Customer shares a file const fileInput = page.locator('input[type="file"]'); await fileInput.setInputFiles({ name: 'screenshot.png', mimeType: 'image/png', buffer: Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64') }); // Send message with file await page.fill('[data-testid="message-input"]', 'Here is a screenshot of the issue'); await page.click('[data-testid="send-button"]'); // Verify file attachment appears on both sides await expect(page.locator('[data-testid="message-with-attachment"]')).toBeVisible(); await expect(agentPage.locator('[data-testid="message-with-attachment"]')).toBeVisible({ timeout: 10000 }); // Test typing indicators await page.focus('[data-testid="message-input"]'); await page.type('[data-testid="message-input"]', 'Customer is typing...'); // Verify typing indicator on agent side await expect(agentPage.locator('[data-testid="typing-indicator"]')).toBeVisible({ timeout: 5000 }); await agentPage.close(); }); }); test.describe('Analytics and Monitoring Integration', () => { test('should track user activities across all features', async ({ page, context }) => { // Login as admin to monitor analytics await page.goto('/login'); await page.fill('[data-testid="email-input"]', '<EMAIL>'); await page.fill('[data-testid="password-input"]', 'TestAdmin123!'); await page.click('[data-testid="login-button"]'); await page.waitForURL('**/dashboard'); // Open analytics dashboard await page.goto('/analytics'); await page.waitForLoadState('networkidle'); // Record initial metrics const initialTickets = await page.locator('[data-testid="kpi-total-tickets"] .value').textContent(); // Simulate user activity in another tab const userPage = await context.newPage(); await userPage.goto('/login'); await userPage.fill('[data-testid="email-input"]', '<EMAIL>'); await userPage.fill('[data-testid="password-input"]', 'TestCustomer123!'); await userPage.click('[data-testid="login-button"]'); await userPage.waitForURL('**/dashboard'); // Create a support ticket await userPage.goto('/support'); await userPage.fill('[data-testid="ticket-title"]', 'Analytics Test Ticket'); await userPage.fill('[data-testid="ticket-description"]', 'Testing analytics tracking'); await userPage.selectOption('[data-testid="ticket-category"]', 'technical'); await userPage.click('[data-testid="submit-ticket-button"]'); await expect(userPage.locator('text=*créé avec succès*')).toBeVisible(); // Use chat feature await userPage.goto('/chat'); await userPage.fill('[data-testid="message-input"]', 'Analytics test message'); await userPage.click('[data-testid="send-button"]'); // Check if analytics are updated (may take time for real-time updates) await page.waitForTimeout(5000); await page.reload(); // Verify metrics have potentially changed const updatedTickets = await page.locator('[data-testid="kpi-total-tickets"] .value').textContent(); // Check for real-time metrics updates await expect(page.locator('[data-testid="real-time-metrics"]')).toBeVisible(); await expect(page.locator('[data-testid="last-updated"]')).toBeVisible(); await userPage.close(); }); }); test.describe('Error Handling and Recovery', () => { test('should handle network failures gracefully across all features', async ({ page }) => { // Login first await page.goto('/login'); await page.fill('[data-testid="email-input"]', '<EMAIL>'); await page.fill('[data-testid="password-input"]', 'TestAdmin123!'); await page.click('[data-testid="login-button"]'); await page.waitForURL('**/dashboard'); // Test chat resilience await page.goto('/chat'); await page.waitForLoadState('networkidle'); // Verify initial connection await expect(page.locator('[data-testid="connection-status"]')).toHaveText(/connecté|connected/i); // Simulate network failure await page.context().setOffline(true); // Try to send a message while offline await page.fill('[data-testid="message-input"]', 'Offline test message'); await page.click('[data-testid="send-button"]'); // Message should be queued await expect(page.locator('[data-testid="message-pending"]')).toBeVisible(); // Restore connection await page.context().setOffline(false); // Verify reconnection and message delivery await expect(page.locator('[data-testid="connection-status"]')).toHaveText(/connecté|connected/i, { timeout: 15000 }); await expect(page.locator('[data-testid="message-sent"]')).toBeVisible({ timeout: 10000 }); // Test analytics resilience await page.goto('/analytics'); // Mock API failure await page.route('**/api/analytics/**', route => { route.fulfill({ status: 500, contentType: 'application/json', body: JSON.stringify({ success: false, message: 'Service unavailable' }) }); }); await page.reload(); // Should show error state gracefully await expect(page.locator('[data-testid="analytics-error"]')).toBeVisible({ timeout: 10000 }); }); }); test.describe('Performance and Load Testing', () => { test('should handle multiple concurrent operations', async ({ page, context }) => { // Create multiple user sessions const pages = []; const userTypes = ['admin', 'agent', 'customer']; for (const userType of userTypes) { const userPage = await context.newPage(); await userPage.goto('/login'); await userPage.fill('[data-testid="email-input"]', `${userType}@test.com`); const password = userType === 'admin' ? 'TestAdmin123!' : userType === 'agent' ? 'TestAgent123!' : 'TestCustomer123!'; await userPage.fill('[data-testid="password-input"]', password); await userPage.click('[data-testid="login-button"]'); await userPage.waitForURL('**/dashboard'); pages.push(userPage); } // Perform concurrent operations const operations = pages.map(async (userPage, index) => { if (index === 0) { // Admin - monitor analytics await userPage.goto('/analytics'); await expect(userPage.locator('[data-testid="real-time-metrics"]')).toBeVisible(); } else if (index === 1) { // Agent - handle tickets await userPage.goto('/agent/tickets'); await expect(userPage.locator('[data-testid="ticket-list"]')).toBeVisible(); } else { // Customer - create ticket and chat await userPage.goto('/support'); await userPage.fill('[data-testid="ticket-title"]', `Concurrent Test ${index}`); await userPage.fill('[data-testid="ticket-description"]', 'Concurrent operation test'); await userPage.selectOption('[data-testid="ticket-category"]', 'technical'); await userPage.click('[data-testid="submit-ticket-button"]'); await userPage.goto('/chat'); await userPage.fill('[data-testid="message-input"]', `Concurrent chat message ${index}`); await userPage.click('[data-testid="send-button"]'); } }); // Wait for all operations to complete await Promise.all(operations); // Verify all operations succeeded for (const userPage of pages) { // Check that pages are still responsive await expect(userPage.locator('body')).toBeVisible(); } // Close all pages for (const userPage of pages) { await userPage.close(); } }); }); test.describe('Security Integration', () => { test('should enforce authentication across all features', async ({ page }) => { // Test that protected routes redirect to login const protectedRoutes = ['/analytics', '/admin', '/agent/tickets']; for (const route of protectedRoutes) { await page.goto(route); await expect(page).toHaveURL(/.*login.*/); } // Test that file uploads require authentication await page.goto('/chat'); await expect(page).toHaveURL(/.*login.*/); }); test('should validate file upload security', async ({ page }) => { // Login first await page.goto('/login'); await page.fill('[data-testid="email-input"]', '<EMAIL>'); await page.fill('[data-testid="password-input"]', 'TestCustomer123!'); await page.click('[data-testid="login-button"]'); await page.waitForURL('**/dashboard'); await page.goto('/chat'); await page.waitForLoadState('networkidle'); // Try to upload a potentially malicious file await page.evaluate(() => { const input = document.querySelector('input[type="file"]') as HTMLInputElement; if (input) { const file = new File(['<script>alert("xss")</script>'], 'malicious.html', { type: 'text/html' }); const dataTransfer = new DataTransfer(); dataTransfer.items.add(file); input.files = dataTransfer.files; input.dispatchEvent(new Event('change', { bubbles: true })); } }); // Should reject the file await expect(page.locator('text=*type*non supporté*')).toBeVisible(); }); }); });