import { test, expect } from '@playwright/test'; import { TestHelpers } from './utils/test-helpers'; test.describe('Live Chat Widget', () => { let helpers: TestHelpers; test.beforeEach(async ({ page }) => { helpers = new TestHelpers(page); await page.goto('/dashboard/support-form'); await helpers.waitForDashboardLoad(); }); test('should display chat widget', async ({ page }) => { // Check chat widget is visible await expect(page.getByText('Chat en direct')).toBeVisible(); await expect(page.getByText('Support Gratuit')).toBeVisible(); // Check online status await expect(page.getByText('En ligne')).toBeVisible(); // Check initial bot message await expect(page.getByText(/assistant virtuel Free Mobile/)).toBeVisible(); }); test('should send and receive messages', async ({ page }) => { const messageInput = page.locator('textarea[placeholder*="Tapez votre message"]'); const sendButton = page.getByRole('button', { name: /send/i }).last(); // Type a message const testMessage = 'Bonjour, j\'ai besoin d\'aide'; await messageInput.fill(testMessage); // Send button should be enabled await expect(sendButton).toBeEnabled(); // Send message await sendButton.click(); // Check message appears in chat await expect(page.getByText(testMessage)).toBeVisible(); // Input should be cleared await expect(messageInput).toHaveValue(''); // Should receive bot response await expect(page.getByText(/agent va vous répondre/)).toBeVisible({ timeout: 5000 }); }); test('should send message with Enter key', async ({ page }) => { const messageInput = page.locator('textarea[placeholder*="Tapez votre message"]'); const testMessage = 'Test message avec Enter'; await messageInput.fill(testMessage); // Press Enter to send await messageInput.press('Enter'); // Check message appears await expect(page.getByText(testMessage)).toBeVisible(); // Input should be cleared await expect(messageInput).toHaveValue(''); }); test('should not send empty messages', async ({ page }) => { const messageInput = page.locator('textarea[placeholder*="Tapez votre message"]'); const sendButton = page.getByRole('button', { name: /send/i }).last(); // Send button should be disabled when input is empty await expect(sendButton).toBeDisabled(); // Type spaces only await messageInput.fill(' '); await expect(sendButton).toBeDisabled(); // Clear input await messageInput.fill(''); await expect(sendButton).toBeDisabled(); }); test('should display message timestamps', async ({ page }) => { const messageInput = page.locator('textarea[placeholder*="Tapez votre message"]'); const sendButton = page.getByRole('button', { name: /send/i }).last(); // Send a message await messageInput.fill('Test timestamp'); await sendButton.click(); // Check timestamp format (HH:MM) const timestampRegex = /\d{2}:\d{2}/; await expect(page.locator('text=' + timestampRegex.source).first()).toBeVisible(); }); test('should handle multiple messages', async ({ page }) => { const messageInput = page.locator('textarea[placeholder*="Tapez votre message"]'); const sendButton = page.getByRole('button', { name: /send/i }).last(); const messages = [ 'Premier message', 'Deuxième message', 'Troisième message' ]; // Send multiple messages for (const message of messages) { await messageInput.fill(message); await sendButton.click(); await page.waitForTimeout(500); // Small delay between messages } // All messages should be visible for (const message of messages) { await expect(page.getByText(message)).toBeVisible(); } }); test('should scroll to bottom when new messages arrive', async ({ page }) => { const messageInput = page.locator('textarea[placeholder*="Tapez votre message"]'); const sendButton = page.getByRole('button', { name: /send/i }).last(); // Send many messages to create scroll for (let i = 1; i <= 10; i++) { await messageInput.fill(`Message ${i}`); await sendButton.click(); await page.waitForTimeout(200); } // Last message should be visible (auto-scroll) await expect(page.getByText('Message 10')).toBeVisible(); }); test('should show online/offline status', async ({ page }) => { // Initially should show online await expect(page.getByText('En ligne')).toBeVisible(); // Mock offline status (this would typically come from WebSocket) await page.evaluate(() => { // Simulate status change const statusElement = document.querySelector('[data-testid="chat-status"]'); if (statusElement) { statusElement.textContent = 'Hors ligne'; } }); // Note: In a real implementation, this would be tested with WebSocket mocking }); test('should handle multiline messages', async ({ page }) => { const messageInput = page.locator('textarea[placeholder*="Tapez votre message"]'); const sendButton = page.getByRole('button', { name: /send/i }).last(); const multilineMessage = 'Ligne 1\nLigne 2\nLigne 3'; await messageInput.fill(multilineMessage); // Should not send on Enter when Shift is held await messageInput.press('Shift+Enter'); await expect(messageInput).toHaveValue(multilineMessage + '\n'); // Send with button await sendButton.click(); // Message should appear with line breaks preserved await expect(page.getByText('Ligne 1')).toBeVisible(); await expect(page.getByText('Ligne 2')).toBeVisible(); await expect(page.getByText('Ligne 3')).toBeVisible(); }); test('should be responsive on mobile', async ({ page }) => { await page.setViewportSize({ width: 375, height: 667 }); // Chat widget should be visible and properly sized await expect(page.getByText('Chat en direct')).toBeVisible(); const chatWidget = page.locator('[data-testid="chat-widget"], .MuiCard-root').first(); const boundingBox = await chatWidget.boundingBox(); if (boundingBox) { // Should not exceed viewport width expect(boundingBox.width).toBeLessThanOrEqual(375); } // Message input should be accessible const messageInput = page.locator('textarea[placeholder*="Tapez votre message"]'); await expect(messageInput).toBeVisible(); // Should be able to type and send await messageInput.fill('Test mobile'); const sendButton = page.getByRole('button', { name: /send/i }).last(); await sendButton.click(); await expect(page.getByText('Test mobile')).toBeVisible(); }); test('should handle chat widget accessibility', async ({ page }) => { // Check ARIA labels const messageInput = page.locator('textarea[placeholder*="Tapez votre message"]'); const sendButton = page.getByRole('button', { name: /send/i }).last(); // Input should be accessible await expect(messageInput).toBeVisible(); // Button should have proper label await expect(sendButton).toBeVisible(); // Should be keyboard navigable await messageInput.focus(); await expect(messageInput).toBeFocused(); await page.keyboard.press('Tab'); await expect(sendButton).toBeFocused(); }); test('should persist chat history during session', async ({ page }) => { const messageInput = page.locator('textarea[placeholder*="Tapez votre message"]'); const sendButton = page.getByRole('button', { name: /send/i }).last(); // Send a message const testMessage = 'Message persistant'; await messageInput.fill(testMessage); await sendButton.click(); // Navigate away and back await page.goto('/dashboard/overview'); await helpers.waitForDashboardLoad(); await page.goto('/dashboard/support-form'); await helpers.waitForDashboardLoad(); // Message should still be visible (if chat history is persisted) // Note: This depends on implementation - might need localStorage or session storage // await expect(page.getByText(testMessage)).toBeVisible(); }); });