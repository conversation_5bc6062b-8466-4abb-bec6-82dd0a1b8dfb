import { test, expect, devices } from '@playwright/test'; import { TestHelpers } from './utils/test-helpers'; // Test across different browsers and devices const browsers = ['chromium', 'firefox', 'webkit']; const mobileDevices = ['iPhone 12', 'Pixel 5', 'iPad Pro']; test.describe('Cross-browser Compatibility', () => { let helpers: TestHelpers; browsers.forEach(browserName => { test.describe(`${browserName} browser`, () => { test.beforeEach(async ({ page }) => { helpers = new TestHelpers(page); }); test(`should load dashboard correctly in ${browserName}`, async ({ page }) => { await page.goto('/dashboard/overview'); await helpers.waitForDashboardLoad(); // Check basic functionality await expect(page.getByText('Bienvenue sur le support Free Mobile')).toBeVisible(); await expect(page.getByText('Actions rapides')).toBeVisible(); // Check navigation works await page.getByRole('tab', { name: 'Formulaire Support' }).click(); await expect(page.getByText('Contacter le support Free')).toBeVisible(); }); test(`should handle form submission in ${browserName}`, async ({ page }) => { await page.goto('/dashboard/support-form'); await helpers.waitForDashboardLoad(); // Mock API response await helpers.mockApiResponse(/\/api\/support\/tickets/, { success: true }); const testData = helpers.generateTestData(); // Fill form await helpers.fillField('input[name="fullName"], [label="Nom complet"] input', testData.user.name); await helpers.fillField('input[name="email"], [label="Adresse email"] input', testData.user.email); await page.getByLabel('Catégorie du problème').click(); await page.getByRole('option', { name: 'Problème technique' }).click(); await helpers.fillField('textarea[name="description"], [label="Description du problème"] textarea', testData.ticket.description); // Submit form await page.getByRole('button', { name: 'Envoyer ma demande' }).click(); // Should show success message await expect(page.getByText(/demande a été envoyée avec succès/)).toBeVisible({ timeout: 10000 }); }); test(`should display CSS styles correctly in ${browserName}`, async ({ page }) => { await page.goto('/dashboard/overview'); await helpers.waitForDashboardLoad(); // Check Free Mobile branding color const header = page.locator('.MuiAppBar-root'); const backgroundColor = await header.evaluate(el => window.getComputedStyle(el).backgroundColor ); // Should be Free Mobile red (rgb(230, 0, 0)) expect(backgroundColor).toBe('rgb(230, 0, 0)'); // Check typography const mainTitle = page.getByText('Bienvenue sur le support Free Mobile'); const fontSize = await mainTitle.evaluate(el => window.getComputedStyle(el).fontSize ); // Should have appropriate font size expect(parseInt(fontSize)).toBeGreaterThan(20); }); test(`should handle JavaScript interactions in ${browserName}`, async ({ page }) => { await page.goto('/dashboard/admin'); await helpers.waitForDashboardLoad(); // Test sidebar navigation await page.getByText('Clients').click(); await expect(page).toHaveURL(/\/dashboard\/admin\/clients/); // Test table interactions const searchInput = page.getByPlaceholder('Rechercher...'); await searchInput.fill('Jean'); // Should filter results await page.waitForTimeout(500); // Test dropdown interactions await page.getByLabel('Statut').click(); await page.getByRole('option', { name: 'Résolu' }).click(); }); }); }); // Mobile device testing mobileDevices.forEach(deviceName => { test.describe(`${deviceName} device`, () => { test.use({ ...devices[deviceName] }); test.beforeEach(async ({ page }) => { helpers = new TestHelpers(page); }); test(`should be responsive on ${deviceName}`, async ({ page }) => { await page.goto('/dashboard/overview'); await helpers.waitForDashboardLoad(); // Check mobile navigation await expect(page.getByText('Free Mobile')).toBeVisible(); // Navigation tabs should be visible (might be scrollable on mobile) const tabs = page.locator('.MuiTabs-root'); await expect(tabs).toBeVisible(); // Content should be readable await expect(page.getByText('Bienvenue sur le support Free Mobile')).toBeVisible(); }); test(`should handle touch interactions on ${deviceName}`, async ({ page }) => { await page.goto('/dashboard/support-form'); await helpers.waitForDashboardLoad(); // Test touch interactions const nameField = page.getByLabel('Nom complet'); await nameField.tap(); await expect(nameField).toBeFocused(); // Test dropdown on mobile const categoryField = page.getByLabel('Catégorie du problème'); await categoryField.tap(); await expect(page.getByRole('option', { name: 'Problème technique' })).toBeVisible(); }); test(`should handle mobile chat widget on ${deviceName}`, async ({ page }) => { await page.goto('/dashboard/support-form'); await helpers.waitForDashboardLoad(); // Chat widget should be visible and usable on mobile await expect(page.getByText('Chat en direct')).toBeVisible(); const messageInput = page.locator('textarea[placeholder*="Tapez votre message"]'); if (await messageInput.count() > 0) { await messageInput.tap(); await messageInput.fill('Test mobile message'); const sendButton = page.getByRole('button', { name: /send/i }).last(); await sendButton.tap(); await expect(page.getByText('Test mobile message')).toBeVisible(); } }); }); }); test.describe('Feature Detection', () => { test('should handle browsers without modern features', async ({ page }) => { // Simulate older browser by disabling certain features await page.addInitScript(() => { // Disable some modern APIs delete (window as any).fetch; delete (window as any).Promise; }); await page.goto('/dashboard/overview'); // App should still load (with polyfills) await expect(page.getByText('Free Mobile')).toBeVisible({ timeout: 15000 }); }); test('should work without JavaScript', async ({ page }) => { // Disable JavaScript await page.context().addInitScript(() => { Object.defineProperty(navigator, 'javaEnabled', { value: () => false }); }); await page.goto('/dashboard/overview'); // Basic content should still be visible await expect(page.getByText('Free Mobile')).toBeVisible(); }); }); test.describe('Performance Across Browsers', () => { test('should load within acceptable time limits', async ({ page }) => { const startTime = Date.now(); await page.goto('/dashboard/overview'); await helpers.waitForDashboardLoad(); const loadTime = Date.now() - startTime; // Should load within 5 seconds expect(loadTime).toBeLessThan(5000); }); test('should handle large datasets efficiently', async ({ page }) => { // Mock large dataset const largeDataset = Array.from({ length: 1000 }, (_, i) => ({ id: `ticket-${i}`, client: `Client ${i}`, category: 'Technique', status: 'En cours', date: '2024-07-19' })); await helpers.mockApiResponse(/\/api\/admin\/tickets/, largeDataset); await page.goto('/dashboard/admin/clients'); await helpers.waitForDashboardLoad(); // Should handle large dataset without freezing await expect(page.getByText('Gestion des Clients')).toBeVisible(); // Pagination should work const pagination = page.locator('.MuiTablePagination-root'); if (await pagination.count() > 0) { await expect(pagination).toBeVisible(); } }); }); test.describe('Browser-specific Issues', () => { test('should handle Safari-specific issues', async ({ page, browserName }) => { test.skip(browserName !== 'webkit', 'Safari-specific test'); await page.goto('/dashboard/support-form'); await helpers.waitForDashboardLoad(); // Test file upload in Safari const fileInput = page.locator('input[type="file"]'); if (await fileInput.count() > 0) { // Safari has specific file upload behavior await expect(fileInput).toBeVisible(); } // Test date inputs in Safari const dateInputs = page.locator('input[type="date"]'); if (await dateInputs.count() > 0) { await expect(dateInputs.first()).toBeVisible(); } }); test('should handle Firefox-specific issues', async ({ page, browserName }) => { test.skip(browserName !== 'firefox', 'Firefox-specific test'); await page.goto('/dashboard/analytics'); await helpers.waitForDashboardLoad(); // Test CSS Grid support in Firefox const gridContainers = page.locator('.MuiGrid-container'); await expect(gridContainers.first()).toBeVisible(); // Test flexbox behavior in Firefox const flexContainers = page.locator('[style*="display: flex"], .MuiBox-root'); const count = await flexContainers.count(); expect(count).toBeGreaterThan(0); }); test('should handle Chrome-specific issues', async ({ page, browserName }) => { test.skip(browserName !== 'chromium', 'Chrome-specific test'); await page.goto('/dashboard/overview'); await helpers.waitForDashboardLoad(); // Test Chrome's autofill behavior const emailInputs = page.locator('input[type="email"]'); if (await emailInputs.count() > 0) { const emailInput = emailInputs.first(); await emailInput.click(); // Chrome might show autofill suggestions await page.waitForTimeout(500); } }); }); test.describe('Viewport Testing', () => { const viewports = [ { width: 320, height: 568, name: 'iPhone SE' }, { width: 375, height: 667, name: 'iPhone 8' }, { width: 768, height: 1024, name: 'iPad' }, { width: 1024, height: 768, name: 'iPad Landscape' }, { width: 1440, height: 900, name: 'Desktop' }, { width: 1920, height: 1080, name: 'Large Desktop' } ]; viewports.forEach(viewport => { test(`should work correctly at ${viewport.name} (${viewport.width}x${viewport.height})`, async ({ page }) => { await page.setViewportSize({ width: viewport.width, height: viewport.height }); await page.goto('/dashboard/overview'); await helpers.waitForDashboardLoad(); // Check that content is visible and properly laid out await expect(page.getByText('Free Mobile')).toBeVisible(); // Check that navigation is accessible const navigation = page.locator('.MuiTabs-root, .MuiAppBar-root'); await expect(navigation).toBeVisible(); // Check that content doesn't overflow const body = page.locator('body'); const bodyWidth = await body.evaluate(el => el.scrollWidth); expect(bodyWidth).toBeLessThanOrEqual(viewport.width + 20); // Allow small margin }); }); }); });