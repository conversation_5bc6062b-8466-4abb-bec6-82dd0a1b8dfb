import { test, expect } from '@playwright/test';

/**
 * =============================================
 * DASHBOARD FEATURES E2E TESTS
 * Comprehensive testing of analytics, admin panels, and navigation
 * =============================================
 */

test.describe('Dashboard Features', () => {
  const baseURL = 'http://localhost:3000';
  
  test.describe('Admin Dashboard Access', () => {
    test.beforeEach(async ({ page }) => {
      // Login as admin
      await page.goto(`${baseURL}/login`);
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'AdminPassword123!');
      await page.click('[data-testid="login-button"]');
      await expect(page).toHaveURL(/.*\/dashboard/);
    });

    test('should display admin dashboard correctly', async ({ page }) => {
      // Verify main dashboard elements
      await expect(page.locator('[data-testid="dashboard"]')).toBeVisible();
      await expect(page.locator('[data-testid="dashboard-header"]')).toBeVisible();
      await expect(page.locator('[data-testid="admin-menu"]')).toBeVisible();
      
      // Verify Free Mobile branding
      await expect(page.locator('text=Free Mobile')).toBeVisible();
      
      // Verify admin-specific elements
      await expect(page.locator('[data-testid="admin-panel"]')).toBeVisible();
      await expect(page.locator('[data-testid="user-management"]')).toBeVisible();
      
      // Take screenshot
      await page.screenshot({ 
        path: 'test-results/screenshots/admin-dashboard.png',
        fullPage: true 
      });
    });

    test('should display key metrics cards', async ({ page }) => {
      // Verify metrics cards are present
      await expect(page.locator('[data-testid="metrics-container"]')).toBeVisible();
      
      // Verify individual metric cards
      await expect(page.locator('[data-testid="total-users-metric"]')).toBeVisible();
      await expect(page.locator('[data-testid="active-chats-metric"]')).toBeVisible();
      await expect(page.locator('[data-testid="resolution-time-metric"]')).toBeVisible();
      await expect(page.locator('[data-testid="satisfaction-metric"]')).toBeVisible();
      
      // Verify metrics have values
      const totalUsers = await page.locator('[data-testid="total-users-value"]').textContent();
      expect(totalUsers).toMatch(/\d+/);
      
      const activeChats = await page.locator('[data-testid="active-chats-value"]').textContent();
      expect(activeChats).toMatch(/\d+/);
    });

    test('should navigate between dashboard tabs', async ({ page }) => {
      // Verify tab navigation
      await expect(page.locator('[data-testid="dashboard-tabs"]')).toBeVisible();
      
      // Test Overview tab
      await page.click('[data-testid="overview-tab"]');
      await expect(page.locator('[data-testid="overview-content"]')).toBeVisible();
      
      // Test Analytics tab
      await page.click('[data-testid="analytics-tab"]');
      await expect(page.locator('[data-testid="analytics-content"]')).toBeVisible();
      
      // Test Users tab
      await page.click('[data-testid="users-tab"]');
      await expect(page.locator('[data-testid="users-content"]')).toBeVisible();
      
      // Test Settings tab
      await page.click('[data-testid="settings-tab"]');
      await expect(page.locator('[data-testid="settings-content"]')).toBeVisible();
    });
  });

  test.describe('Analytics Dashboard', () => {
    test.beforeEach(async ({ page }) => {
      // Login as admin and navigate to analytics
      await page.goto(`${baseURL}/login`);
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'AdminPassword123!');
      await page.click('[data-testid="login-button"]');
      await expect(page).toHaveURL(/.*\/dashboard/);
      await page.click('[data-testid="analytics-tab"]');
    });

    test('should display analytics overview', async ({ page }) => {
      // Verify analytics interface
      await expect(page.locator('[data-testid="analytics-dashboard"]')).toBeVisible();
      await expect(page.locator('[data-testid="analytics-header"]')).toContainText(/Analytics|Analytiques/);
      
      // Verify time range selector
      await expect(page.locator('[data-testid="time-range-selector"]')).toBeVisible();
      
      // Verify chart containers
      await expect(page.locator('[data-testid="performance-chart"]')).toBeVisible();
      await expect(page.locator('[data-testid="satisfaction-chart"]')).toBeVisible();
      
      // Take screenshot
      await page.screenshot({ 
        path: 'test-results/screenshots/analytics-dashboard.png',
        fullPage: true 
      });
    });

    test('should filter data by time range', async ({ page }) => {
      // Test time range filtering
      await page.click('[data-testid="time-range-selector"]');
      
      // Select different time ranges
      await page.click('[data-testid="time-range-7d"]');
      await expect(page.locator('[data-testid="analytics-loading"]')).toBeVisible();
      await expect(page.locator('[data-testid="analytics-loading"]')).not.toBeVisible({ timeout: 10000 });
      
      // Verify data updates
      await expect(page.locator('[data-testid="performance-chart"]')).toBeVisible();
      
      // Test 30-day range
      await page.click('[data-testid="time-range-selector"]');
      await page.click('[data-testid="time-range-30d"]');
      await expect(page.locator('[data-testid="analytics-loading"]')).not.toBeVisible({ timeout: 10000 });
    });

    test('should display performance metrics', async ({ page }) => {
      // Verify performance metrics section
      await expect(page.locator('[data-testid="performance-metrics"]')).toBeVisible();
      
      // Verify individual performance indicators
      await expect(page.locator('[data-testid="response-time-metric"]')).toBeVisible();
      await expect(page.locator('[data-testid="resolution-rate-metric"]')).toBeVisible();
      await expect(page.locator('[data-testid="escalation-rate-metric"]')).toBeVisible();
      
      // Verify trend indicators
      const trendIndicators = page.locator('[data-testid="trend-indicator"]');
      const trendCount = await trendIndicators.count();
      expect(trendCount).toBeGreaterThan(0);
    });

    test('should display agent performance leaderboard', async ({ page }) => {
      // Verify agent leaderboard
      await expect(page.locator('[data-testid="agent-leaderboard"]')).toBeVisible();
      await expect(page.locator('[data-testid="leaderboard-header"]')).toContainText(/Top Performers|Meilleurs/);
      
      // Verify agent entries
      const agentEntries = page.locator('[data-testid="agent-entry"]');
      const entryCount = await agentEntries.count();
      
      if (entryCount > 0) {
        // Verify agent entry contains required information
        await expect(agentEntries.first()).toContainText(/\w+/); // Agent name
        await expect(agentEntries.first()).toContainText(/\d+/); // Score or metric
      }
    });

    test('should export analytics data', async ({ page }) => {
      // Verify export functionality
      await expect(page.locator('[data-testid="export-button"]')).toBeVisible();
      
      // Test export
      const downloadPromise = page.waitForEvent('download');
      await page.click('[data-testid="export-button"]');
      
      // Verify download starts
      const download = await downloadPromise;
      expect(download.suggestedFilename()).toMatch(/analytics.*\.(csv|xlsx|pdf)/);
    });
  });

  test.describe('User Management', () => {
    test.beforeEach(async ({ page }) => {
      // Login as admin and navigate to user management
      await page.goto(`${baseURL}/login`);
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'AdminPassword123!');
      await page.click('[data-testid="login-button"]');
      await expect(page).toHaveURL(/.*\/dashboard/);
      await page.click('[data-testid="users-tab"]');
    });

    test('should display user list', async ({ page }) => {
      // Verify user management interface
      await expect(page.locator('[data-testid="user-management"]')).toBeVisible();
      await expect(page.locator('[data-testid="user-list"]')).toBeVisible();
      
      // Verify user table headers
      await expect(page.locator('[data-testid="user-table-header"]')).toContainText(/Name|Email|Role|Status/);
      
      // Verify user entries
      const userEntries = page.locator('[data-testid="user-entry"]');
      const userCount = await userEntries.count();
      expect(userCount).toBeGreaterThan(0);
      
      // Take screenshot
      await page.screenshot({ 
        path: 'test-results/screenshots/user-management.png',
        fullPage: true 
      });
    });

    test('should search and filter users', async ({ page }) => {
      // Test user search
      await page.fill('[data-testid="user-search"]', 'admin');
      await page.press('[data-testid="user-search"]', 'Enter');
      
      // Verify search results
      await expect(page.locator('[data-testid="user-entry"]')).toContainText(/admin/i);
      
      // Test role filter
      await page.click('[data-testid="role-filter"]');
      await page.click('[data-testid="role-admin"]');
      
      // Verify filtered results
      const filteredEntries = page.locator('[data-testid="user-entry"]');
      const filteredCount = await filteredEntries.count();
      expect(filteredCount).toBeGreaterThan(0);
    });

    test('should create new user', async ({ page }) => {
      // Click add user button
      await page.click('[data-testid="add-user-button"]');
      
      // Verify user creation modal
      await expect(page.locator('[data-testid="user-modal"]')).toBeVisible();
      await expect(page.locator('[data-testid="user-form"]')).toBeVisible();
      
      // Fill user form
      const timestamp = Date.now();
      await page.fill('[data-testid="user-firstName"]', 'Test');
      await page.fill('[data-testid="user-lastName"]', 'User');
      await page.fill('[data-testid="user-email"]', `test${timestamp}@example.com`);
      await page.selectOption('[data-testid="user-role"]', 'user');
      
      // Submit form
      await page.click('[data-testid="save-user-button"]');
      
      // Verify success message
      await expect(page.locator('[data-testid="user-created-success"]')).toBeVisible();
      
      // Verify user appears in list
      await expect(page.locator(`[data-testid="user-entry"]:has-text("test${timestamp}@example.com")`)).toBeVisible();
    });

    test('should edit user details', async ({ page }) => {
      // Click on first user to edit
      await page.click('[data-testid="user-entry"]');
      await page.click('[data-testid="edit-user-button"]');
      
      // Verify edit modal
      await expect(page.locator('[data-testid="user-modal"]')).toBeVisible();
      
      // Update user details
      await page.fill('[data-testid="user-firstName"]', 'Updated');
      await page.click('[data-testid="save-user-button"]');
      
      // Verify success message
      await expect(page.locator('[data-testid="user-updated-success"]')).toBeVisible();
    });

    test('should manage user roles and permissions', async ({ page }) => {
      // Select a user
      await page.click('[data-testid="user-entry"]');
      await page.click('[data-testid="manage-permissions-button"]');
      
      // Verify permissions modal
      await expect(page.locator('[data-testid="permissions-modal"]')).toBeVisible();
      
      // Verify permission checkboxes
      await expect(page.locator('[data-testid="permission-chat"]')).toBeVisible();
      await expect(page.locator('[data-testid="permission-analytics"]')).toBeVisible();
      await expect(page.locator('[data-testid="permission-admin"]')).toBeVisible();
      
      // Update permissions
      await page.check('[data-testid="permission-analytics"]');
      await page.click('[data-testid="save-permissions-button"]');
      
      // Verify success
      await expect(page.locator('[data-testid="permissions-updated-success"]')).toBeVisible();
    });
  });

  test.describe('Regular User Dashboard', () => {
    test.beforeEach(async ({ page }) => {
      // Login as regular user
      await page.goto(`${baseURL}/login`);
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'UserPassword123!');
      await page.click('[data-testid="login-button"]');
      await expect(page).toHaveURL(/.*\/dashboard/);
    });

    test('should display user dashboard correctly', async ({ page }) => {
      // Verify user dashboard elements
      await expect(page.locator('[data-testid="dashboard"]')).toBeVisible();
      await expect(page.locator('[data-testid="user-welcome"]')).toBeVisible();
      
      // Verify admin menu is NOT visible
      await expect(page.locator('[data-testid="admin-menu"]')).not.toBeVisible();
      
      // Verify user-specific elements
      await expect(page.locator('[data-testid="my-tickets"]')).toBeVisible();
      await expect(page.locator('[data-testid="recent-activity"]')).toBeVisible();
      
      // Take screenshot
      await page.screenshot({ 
        path: 'test-results/screenshots/user-dashboard.png',
        fullPage: true 
      });
    });

    test('should display user tickets and history', async ({ page }) => {
      // Verify tickets section
      await expect(page.locator('[data-testid="my-tickets"]')).toBeVisible();
      
      // Verify ticket list
      const ticketEntries = page.locator('[data-testid="ticket-entry"]');
      const ticketCount = await ticketEntries.count();
      
      if (ticketCount > 0) {
        // Verify ticket information
        await expect(ticketEntries.first()).toContainText(/\d+/); // Ticket ID
        await expect(ticketEntries.first()).toContainText(/Open|Closed|Pending/); // Status
      }
      
      // Verify create new ticket button
      await expect(page.locator('[data-testid="create-ticket-button"]')).toBeVisible();
    });

    test('should allow ticket creation', async ({ page }) => {
      // Click create ticket button
      await page.click('[data-testid="create-ticket-button"]');
      
      // Verify ticket creation form
      await expect(page.locator('[data-testid="ticket-form"]')).toBeVisible();
      
      // Fill ticket form
      await page.fill('[data-testid="ticket-subject"]', 'Problème avec mon forfait');
      await page.fill('[data-testid="ticket-description"]', 'Je n\'arrive pas à accéder à mes données mobiles');
      await page.selectOption('[data-testid="ticket-category"]', 'technical');
      
      // Submit ticket
      await page.click('[data-testid="submit-ticket-button"]');
      
      // Verify success message
      await expect(page.locator('[data-testid="ticket-created-success"]')).toBeVisible();
      
      // Verify ticket appears in list
      await expect(page.locator('[data-testid="ticket-entry"]:has-text("Problème avec mon forfait")')).toBeVisible();
    });

    test('should display recent activity', async ({ page }) => {
      // Verify recent activity section
      await expect(page.locator('[data-testid="recent-activity"]')).toBeVisible();
      
      // Verify activity entries
      const activityEntries = page.locator('[data-testid="activity-entry"]');
      const activityCount = await activityEntries.count();
      
      if (activityCount > 0) {
        // Verify activity information
        await expect(activityEntries.first()).toContainText(/\d{1,2}\/\d{1,2}\/\d{4}/); // Date
        await expect(activityEntries.first()).toContainText(/Chat|Call|Ticket/); // Activity type
      }
    });
  });

  test.describe('Navigation and Layout', () => {
    test.beforeEach(async ({ page }) => {
      // Login as admin
      await page.goto(`${baseURL}/login`);
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'AdminPassword123!');
      await page.click('[data-testid="login-button"]');
      await expect(page).toHaveURL(/.*\/dashboard/);
    });

    test('should navigate between main sections', async ({ page }) => {
      // Test navigation to different sections
      const navigationItems = [
        { selector: '[data-testid="nav-dashboard"]', url: '/dashboard' },
        { selector: '[data-testid="nav-chat"]', url: '/chat' },
        { selector: '[data-testid="nav-analytics"]', url: '/analytics' },
        { selector: '[data-testid="nav-admin"]', url: '/admin' }
      ];
      
      for (const item of navigationItems) {
        await page.click(item.selector);
        await expect(page).toHaveURL(new RegExp(`.*${item.url}`));
        
        // Verify page loads correctly
        await expect(page.locator('[data-testid="page-content"]')).toBeVisible();
      }
    });

    test('should display responsive sidebar', async ({ page }) => {
      // Verify sidebar is visible on desktop
      await expect(page.locator('[data-testid="sidebar"]')).toBeVisible();
      
      // Test mobile view
      await page.setViewportSize({ width: 768, height: 1024 });
      
      // Verify mobile menu toggle
      await expect(page.locator('[data-testid="mobile-menu-toggle"]')).toBeVisible();
      
      // Test sidebar toggle
      await page.click('[data-testid="mobile-menu-toggle"]');
      await expect(page.locator('[data-testid="sidebar"]')).toBeVisible();
      
      // Close sidebar
      await page.click('[data-testid="mobile-menu-toggle"]');
      await expect(page.locator('[data-testid="sidebar"]')).not.toBeVisible();
    });

    test('should display user profile menu', async ({ page }) => {
      // Click user profile menu
      await page.click('[data-testid="user-menu"]');
      
      // Verify menu options
      await expect(page.locator('[data-testid="profile-option"]')).toBeVisible();
      await expect(page.locator('[data-testid="settings-option"]')).toBeVisible();
      await expect(page.locator('[data-testid="logout-option"]')).toBeVisible();
      
      // Test profile navigation
      await page.click('[data-testid="profile-option"]');
      await expect(page).toHaveURL(/.*\/profile/);
    });

    test('should handle breadcrumb navigation', async ({ page }) => {
      // Navigate to a nested page
      await page.click('[data-testid="nav-admin"]');
      await page.click('[data-testid="user-management-link"]');
      
      // Verify breadcrumbs
      await expect(page.locator('[data-testid="breadcrumb"]')).toBeVisible();
      await expect(page.locator('[data-testid="breadcrumb"]')).toContainText(/Dashboard.*Admin.*Users/);
      
      // Test breadcrumb navigation
      await page.click('[data-testid="breadcrumb-dashboard"]');
      await expect(page).toHaveURL(/.*\/dashboard/);
    });
  });

  test.describe('Real-time Updates', () => {
    test.beforeEach(async ({ page }) => {
      // Login as admin
      await page.goto(`${baseURL}/login`);
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'AdminPassword123!');
      await page.click('[data-testid="login-button"]');
      await expect(page).toHaveURL(/.*\/dashboard/);
    });

    test('should display real-time metrics updates', async ({ page }) => {
      // Verify real-time indicators
      await expect(page.locator('[data-testid="real-time-indicator"]')).toBeVisible();
      
      // Verify metrics update automatically
      const initialValue = await page.locator('[data-testid="active-chats-value"]').textContent();
      
      // Wait for potential update
      await page.waitForTimeout(5000);
      
      // Verify connection status
      await expect(page.locator('[data-testid="connection-status"]')).toContainText(/Connected|Connecté/);
    });

    test('should show live notifications', async ({ page }) => {
      // Verify notification area
      await expect(page.locator('[data-testid="notifications"]')).toBeVisible();
      
      // Verify notification bell
      await expect(page.locator('[data-testid="notification-bell"]')).toBeVisible();
      
      // Click notifications
      await page.click('[data-testid="notification-bell"]');
      
      // Verify notification panel
      await expect(page.locator('[data-testid="notification-panel"]')).toBeVisible();
    });

    test('should handle WebSocket connection status', async ({ page }) => {
      // Verify WebSocket connection indicator
      await expect(page.locator('[data-testid="websocket-status"]')).toBeVisible();
      
      // Should show connected status
      await expect(page.locator('[data-testid="websocket-status"]')).toContainText(/Connected|Online/);
      
      // Test connection interruption handling
      await page.route('**/socket.io/**', route => route.abort());
      
      // Verify disconnected status
      await expect(page.locator('[data-testid="websocket-status"]')).toContainText(/Disconnected|Offline/, { timeout: 10000 });
    });
  });
});
