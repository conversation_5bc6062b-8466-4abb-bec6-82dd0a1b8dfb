import { test, expect } from '@playwright/test';

/**
 * =============================================
 * AUTHENTICATION E2E TESTS
 * Comprehensive testing of login, register, and protected routes
 * =============================================
 */

test.describe('Authentication Flows', () => {
  const baseURL = 'http://localhost:3000';
  
  test.beforeEach(async ({ page }) => {
    // Clear any existing authentication state
    await page.context().clearCookies();
    await page.evaluate(() => {
      localStorage.clear();
      sessionStorage.clear();
    });
  });

  test.describe('Login Flow', () => {
    test('should display login form correctly', async ({ page }) => {
      await page.goto(`${baseURL}/login`);
      
      // Verify login form elements are present
      await expect(page.locator('[data-testid="login-form"]')).toBeVisible();
      await expect(page.locator('[data-testid="email-input"]')).toBeVisible();
      await expect(page.locator('[data-testid="password-input"]')).toBeVisible();
      await expect(page.locator('[data-testid="login-button"]')).toBeVisible();
      
      // Verify Free Mobile branding
      await expect(page.locator('text=Free Mobile')).toBeVisible();
      
      // Take screenshot for visual verification
      await page.screenshot({ 
        path: 'test-results/screenshots/login-form.png',
        fullPage: true 
      });
    });

    test('should login successfully with valid admin credentials', async ({ page }) => {
      await page.goto(`${baseURL}/login`);
      
      // Fill login form with admin credentials
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'AdminPassword123!');
      
      // Submit form and wait for navigation
      const responsePromise = page.waitForResponse(response => 
        response.url().includes('/api/auth/login') && response.status() === 200
      );
      
      await page.click('[data-testid="login-button"]');
      
      // Verify successful login response
      const response = await responsePromise;
      expect(response.status()).toBe(200);
      
      // Verify redirect to dashboard
      await expect(page).toHaveURL(/.*\/dashboard/);
      await expect(page.locator('[data-testid="dashboard"]')).toBeVisible();
      
      // Verify user menu is visible
      await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
      
      // Verify admin role access
      await expect(page.locator('[data-testid="admin-menu"]')).toBeVisible();
      
      // Take screenshot of successful login
      await page.screenshot({ 
        path: 'test-results/screenshots/admin-dashboard.png',
        fullPage: true 
      });
    });

    test('should login successfully with valid user credentials', async ({ page }) => {
      await page.goto(`${baseURL}/login`);
      
      // Fill login form with user credentials
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'UserPassword123!');
      
      await page.click('[data-testid="login-button"]');
      
      // Verify redirect to dashboard
      await expect(page).toHaveURL(/.*\/dashboard/);
      await expect(page.locator('[data-testid="dashboard"]')).toBeVisible();
      
      // Verify user role (no admin menu)
      await expect(page.locator('[data-testid="admin-menu"]')).not.toBeVisible();
    });

    test('should show error for invalid credentials', async ({ page }) => {
      await page.goto(`${baseURL}/login`);
      
      // Fill login form with invalid credentials
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'wrongpassword');
      
      await page.click('[data-testid="login-button"]');
      
      // Verify error message is displayed
      await expect(page.locator('[data-testid="login-error"]')).toBeVisible();
      await expect(page.locator('[data-testid="login-error"]')).toContainText(/Invalid credentials|Authentication failed/);
      
      // Verify user stays on login page
      await expect(page).toHaveURL(/.*\/login/);
    });

    test('should validate required fields', async ({ page }) => {
      await page.goto(`${baseURL}/login`);
      
      // Try to submit empty form
      await page.click('[data-testid="login-button"]');
      
      // Verify validation messages
      await expect(page.locator('[data-testid="email-error"]')).toBeVisible();
      await expect(page.locator('[data-testid="password-error"]')).toBeVisible();
    });

    test('should validate email format', async ({ page }) => {
      await page.goto(`${baseURL}/login`);
      
      // Fill invalid email format
      await page.fill('[data-testid="email-input"]', 'invalid-email');
      await page.fill('[data-testid="password-input"]', 'password123');
      
      await page.click('[data-testid="login-button"]');
      
      // Verify email validation error
      await expect(page.locator('[data-testid="email-error"]')).toBeVisible();
      await expect(page.locator('[data-testid="email-error"]')).toContainText(/Invalid email format/);
    });
  });

  test.describe('Registration Flow', () => {
    test('should display registration form correctly', async ({ page }) => {
      await page.goto(`${baseURL}/register`);
      
      // Verify registration form elements
      await expect(page.locator('[data-testid="register-form"]')).toBeVisible();
      await expect(page.locator('[data-testid="firstName-input"]')).toBeVisible();
      await expect(page.locator('[data-testid="lastName-input"]')).toBeVisible();
      await expect(page.locator('[data-testid="email-input"]')).toBeVisible();
      await expect(page.locator('[data-testid="password-input"]')).toBeVisible();
      await expect(page.locator('[data-testid="confirmPassword-input"]')).toBeVisible();
      await expect(page.locator('[data-testid="register-button"]')).toBeVisible();
      
      // Take screenshot
      await page.screenshot({ 
        path: 'test-results/screenshots/register-form.png',
        fullPage: true 
      });
    });

    test('should register new user successfully', async ({ page }) => {
      await page.goto(`${baseURL}/register`);
      
      const timestamp = Date.now();
      const testEmail = `test${timestamp}@example.com`;
      
      // Fill registration form
      await page.fill('[data-testid="firstName-input"]', 'Test');
      await page.fill('[data-testid="lastName-input"]', 'User');
      await page.fill('[data-testid="email-input"]', testEmail);
      await page.fill('[data-testid="password-input"]', 'TestPassword123!');
      await page.fill('[data-testid="confirmPassword-input"]', 'TestPassword123!');
      
      // Submit form
      const responsePromise = page.waitForResponse(response => 
        response.url().includes('/api/auth/register')
      );
      
      await page.click('[data-testid="register-button"]');
      
      // Verify successful registration
      const response = await responsePromise;
      expect(response.status()).toBe(201);
      
      // Verify redirect to dashboard or login
      await expect(page).toHaveURL(/.*\/(dashboard|login)/);
    });

    test('should validate password confirmation', async ({ page }) => {
      await page.goto(`${baseURL}/register`);
      
      // Fill form with mismatched passwords
      await page.fill('[data-testid="firstName-input"]', 'Test');
      await page.fill('[data-testid="lastName-input"]', 'User');
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'TestPassword123!');
      await page.fill('[data-testid="confirmPassword-input"]', 'DifferentPassword123!');
      
      await page.click('[data-testid="register-button"]');
      
      // Verify password mismatch error
      await expect(page.locator('[data-testid="confirmPassword-error"]')).toBeVisible();
      await expect(page.locator('[data-testid="confirmPassword-error"]')).toContainText(/Passwords do not match/);
    });

    test('should prevent registration with existing email', async ({ page }) => {
      await page.goto(`${baseURL}/register`);
      
      // Try to register with existing admin email
      await page.fill('[data-testid="firstName-input"]', 'Test');
      await page.fill('[data-testid="lastName-input"]', 'User');
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'TestPassword123!');
      await page.fill('[data-testid="confirmPassword-input"]', 'TestPassword123!');
      
      await page.click('[data-testid="register-button"]');
      
      // Verify error message for existing email
      await expect(page.locator('[data-testid="register-error"]')).toBeVisible();
      await expect(page.locator('[data-testid="register-error"]')).toContainText(/Email already exists|User already registered/);
    });
  });

  test.describe('Protected Routes', () => {
    test('should redirect unauthenticated users to login', async ({ page }) => {
      // Try to access protected dashboard route
      await page.goto(`${baseURL}/dashboard`);
      
      // Should redirect to login page
      await expect(page).toHaveURL(/.*\/login/);
      
      // Verify login form is displayed
      await expect(page.locator('[data-testid="login-form"]')).toBeVisible();
    });

    test('should allow authenticated users to access protected routes', async ({ page }) => {
      // First login
      await page.goto(`${baseURL}/login`);
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'AdminPassword123!');
      await page.click('[data-testid="login-button"]');
      
      // Wait for successful login
      await expect(page).toHaveURL(/.*\/dashboard/);
      
      // Now try to access other protected routes
      const protectedRoutes = ['/dashboard', '/chat', '/analytics', '/admin'];
      
      for (const route of protectedRoutes) {
        await page.goto(`${baseURL}${route}`);
        // Should not redirect to login
        await expect(page).not.toHaveURL(/.*\/login/);
      }
    });

    test('should enforce role-based access control', async ({ page }) => {
      // Login as regular user
      await page.goto(`${baseURL}/login`);
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'UserPassword123!');
      await page.click('[data-testid="login-button"]');
      
      await expect(page).toHaveURL(/.*\/dashboard/);
      
      // Try to access admin-only route
      await page.goto(`${baseURL}/admin`);
      
      // Should show access denied or redirect
      const isAccessDenied = await page.locator('[data-testid="access-denied"]').isVisible();
      const isRedirected = page.url().includes('/login') || page.url().includes('/dashboard');
      
      expect(isAccessDenied || isRedirected).toBeTruthy();
    });
  });

  test.describe('Logout Flow', () => {
    test('should logout successfully', async ({ page }) => {
      // First login
      await page.goto(`${baseURL}/login`);
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'AdminPassword123!');
      await page.click('[data-testid="login-button"]');
      
      await expect(page).toHaveURL(/.*\/dashboard/);
      
      // Logout
      await page.click('[data-testid="user-menu"]');
      await page.click('[data-testid="logout-button"]');
      
      // Verify redirect to login page
      await expect(page).toHaveURL(/.*\/login/);
      
      // Verify authentication state is cleared
      const authToken = await page.evaluate(() => localStorage.getItem('authToken'));
      expect(authToken).toBeNull();
      
      // Try to access protected route after logout
      await page.goto(`${baseURL}/dashboard`);
      await expect(page).toHaveURL(/.*\/login/);
    });
  });

  test.describe('Session Management', () => {
    test('should handle session expiration', async ({ page }) => {
      // Login first
      await page.goto(`${baseURL}/login`);
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'AdminPassword123!');
      await page.click('[data-testid="login-button"]');
      
      await expect(page).toHaveURL(/.*\/dashboard/);
      
      // Simulate session expiration by clearing auth token
      await page.evaluate(() => {
        localStorage.removeItem('authToken');
        sessionStorage.clear();
      });
      
      // Try to make an authenticated request
      await page.reload();
      
      // Should redirect to login
      await expect(page).toHaveURL(/.*\/login/);
    });

    test('should maintain session across page refreshes', async ({ page }) => {
      // Login
      await page.goto(`${baseURL}/login`);
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'AdminPassword123!');
      await page.click('[data-testid="login-button"]');
      
      await expect(page).toHaveURL(/.*\/dashboard/);
      
      // Refresh page
      await page.reload();
      
      // Should still be authenticated
      await expect(page).toHaveURL(/.*\/dashboard/);
      await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
    });
  });
});
