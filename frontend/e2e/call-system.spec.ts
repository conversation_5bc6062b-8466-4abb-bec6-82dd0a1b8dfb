import { test, expect } from '@playwright/test';

/**
 * =============================================
 * CALL SYSTEM E2E TESTS
 * Comprehensive testing of call initiation, controls, and scheduling
 * =============================================
 */

test.describe('Call System', () => {
  const baseURL = 'http://localhost:3000';
  
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto(`${baseURL}/login`);
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'UserPassword123!');
    await page.click('[data-testid="login-button"]');
    
    // Wait for successful login
    await expect(page).toHaveURL(/.*\/dashboard/);
  });

  test.describe('Call Button and Initiation', () => {
    test('should display call button in chat interface', async ({ page }) => {
      await page.goto(`${baseURL}/chat`);
      
      // Verify call button is present and visible
      await expect(page.locator('[data-testid="call-button"]')).toBeVisible();
      await expect(page.locator('[data-testid="call-button"]')).toBeEnabled();
      
      // Verify call button has proper styling and icon
      await expect(page.locator('[data-testid="call-button"] svg')).toBeVisible();
      
      // Take screenshot
      await page.screenshot({ 
        path: 'test-results/screenshots/call-button.png',
        fullPage: true 
      });
    });

    test('should show call options modal when call button clicked', async ({ page }) => {
      await page.goto(`${baseURL}/chat`);
      
      // Click call button
      await page.click('[data-testid="call-button"]');
      
      // Verify call options modal appears
      await expect(page.locator('[data-testid="call-modal"]')).toBeVisible();
      await expect(page.locator('[data-testid="call-modal-title"]')).toContainText(/Appel|Call/);
      
      // Verify call options are present
      await expect(page.locator('[data-testid="immediate-call-option"]')).toBeVisible();
      await expect(page.locator('[data-testid="schedule-call-option"]')).toBeVisible();
      await expect(page.locator('[data-testid="emergency-call-option"]')).toBeVisible();
      
      // Take screenshot of modal
      await page.screenshot({ 
        path: 'test-results/screenshots/call-modal.png',
        fullPage: true 
      });
    });

    test('should initiate immediate call successfully', async ({ page }) => {
      await page.goto(`${baseURL}/chat`);
      
      // Open call modal and select immediate call
      await page.click('[data-testid="call-button"]');
      await page.click('[data-testid="immediate-call-option"]');
      
      // Verify call initiation process
      await expect(page.locator('[data-testid="call-connecting"]')).toBeVisible();
      await expect(page.locator('[data-testid="call-connecting"]')).toContainText(/Connexion|Connecting/);
      
      // Verify call interface appears
      await expect(page.locator('[data-testid="call-interface"]')).toBeVisible({ timeout: 10000 });
      await expect(page.locator('[data-testid="call-status"]')).toContainText(/En cours|Active|Connected/);
    });

    test('should show agent availability status', async ({ page }) => {
      await page.goto(`${baseURL}/chat`);
      
      await page.click('[data-testid="call-button"]');
      
      // Verify agent availability information
      await expect(page.locator('[data-testid="agent-availability"]')).toBeVisible();
      
      const availabilityText = await page.locator('[data-testid="agent-availability"]').textContent();
      expect(availabilityText).toMatch(/disponible|agents|attente|available/i);
      
      // Verify estimated wait time
      await expect(page.locator('[data-testid="estimated-wait-time"]')).toBeVisible();
    });

    test('should handle emergency call priority', async ({ page }) => {
      await page.goto(`${baseURL}/chat`);
      
      await page.click('[data-testid="call-button"]');
      await page.click('[data-testid="emergency-call-option"]');
      
      // Verify emergency call confirmation
      await expect(page.locator('[data-testid="emergency-confirmation"]')).toBeVisible();
      await expect(page.locator('[data-testid="emergency-confirmation"]')).toContainText(/Urgence|Emergency/);
      
      // Confirm emergency call
      await page.click('[data-testid="confirm-emergency-call"]');
      
      // Verify priority handling
      await expect(page.locator('[data-testid="priority-indicator"]')).toBeVisible();
      await expect(page.locator('[data-testid="priority-indicator"]')).toContainText(/Priorité|Priority|Urgence/);
    });
  });

  test.describe('Call Controls', () => {
    test.beforeEach(async ({ page }) => {
      // Initiate a call before testing controls
      await page.goto(`${baseURL}/chat`);
      await page.click('[data-testid="call-button"]');
      await page.click('[data-testid="immediate-call-option"]');
      await expect(page.locator('[data-testid="call-interface"]')).toBeVisible({ timeout: 10000 });
    });

    test('should display all call control buttons', async ({ page }) => {
      // Verify all control buttons are present
      await expect(page.locator('[data-testid="mute-button"]')).toBeVisible();
      await expect(page.locator('[data-testid="speaker-button"]')).toBeVisible();
      await expect(page.locator('[data-testid="end-call-button"]')).toBeVisible();
      
      // Verify optional controls
      const videoButton = page.locator('[data-testid="video-button"]');
      const recordButton = page.locator('[data-testid="record-button"]');
      
      if (await videoButton.isVisible()) {
        await expect(videoButton).toBeEnabled();
      }
      
      if (await recordButton.isVisible()) {
        await expect(recordButton).toBeEnabled();
      }
      
      // Take screenshot of call controls
      await page.screenshot({ 
        path: 'test-results/screenshots/call-controls.png',
        fullPage: true 
      });
    });

    test('should toggle mute functionality', async ({ page }) => {
      const muteButton = page.locator('[data-testid="mute-button"]');
      
      // Verify initial state (unmuted)
      await expect(muteButton).not.toHaveClass(/muted|active/);
      
      // Click mute button
      await muteButton.click();
      
      // Verify muted state
      await expect(muteButton).toHaveClass(/muted|active/);
      await expect(page.locator('[data-testid="mute-indicator"]')).toBeVisible();
      
      // Click again to unmute
      await muteButton.click();
      
      // Verify unmuted state
      await expect(muteButton).not.toHaveClass(/muted|active/);
      await expect(page.locator('[data-testid="mute-indicator"]')).not.toBeVisible();
    });

    test('should toggle speaker functionality', async ({ page }) => {
      const speakerButton = page.locator('[data-testid="speaker-button"]');
      
      // Click speaker button
      await speakerButton.click();
      
      // Verify speaker state change
      await expect(speakerButton).toHaveClass(/active|speaker-on/);
      
      // Verify volume control appears if available
      const volumeControl = page.locator('[data-testid="volume-control"]');
      if (await volumeControl.isVisible()) {
        await expect(volumeControl).toBeVisible();
      }
    });

    test('should end call successfully', async ({ page }) => {
      // Click end call button
      await page.click('[data-testid="end-call-button"]');
      
      // Verify call end confirmation
      await expect(page.locator('[data-testid="call-ended"]')).toBeVisible();
      
      // Verify return to chat interface
      await expect(page.locator('[data-testid="chat-container"]')).toBeVisible();
      await expect(page.locator('[data-testid="call-interface"]')).not.toBeVisible();
      
      // Verify call summary if available
      const callSummary = page.locator('[data-testid="call-summary"]');
      if (await callSummary.isVisible()) {
        await expect(callSummary).toContainText(/Durée|Duration|Résumé/);
      }
    });

    test('should display call duration timer', async ({ page }) => {
      // Verify call timer is visible
      await expect(page.locator('[data-testid="call-timer"]')).toBeVisible();
      
      // Verify timer format (MM:SS)
      const timerText = await page.locator('[data-testid="call-timer"]').textContent();
      expect(timerText).toMatch(/\d{1,2}:\d{2}/);
      
      // Wait a moment and verify timer updates
      await page.waitForTimeout(2000);
      const updatedTimerText = await page.locator('[data-testid="call-timer"]').textContent();
      expect(updatedTimerText).not.toBe(timerText);
    });

    test('should handle call quality indicators', async ({ page }) => {
      // Verify call quality indicator is present
      await expect(page.locator('[data-testid="call-quality"]')).toBeVisible();
      
      // Verify quality status
      const qualityIndicator = page.locator('[data-testid="call-quality-indicator"]');
      if (await qualityIndicator.isVisible()) {
        const qualityClass = await qualityIndicator.getAttribute('class');
        expect(qualityClass).toMatch(/good|fair|poor|excellent/);
      }
    });
  });

  test.describe('Call Scheduling', () => {
    test('should open call scheduler', async ({ page }) => {
      await page.goto(`${baseURL}/chat`);
      
      await page.click('[data-testid="call-button"]');
      await page.click('[data-testid="schedule-call-option"]');
      
      // Verify scheduler interface
      await expect(page.locator('[data-testid="call-scheduler"]')).toBeVisible();
      await expect(page.locator('[data-testid="calendar-widget"]')).toBeVisible();
      await expect(page.locator('[data-testid="time-slots"]')).toBeVisible();
      
      // Take screenshot
      await page.screenshot({ 
        path: 'test-results/screenshots/call-scheduler.png',
        fullPage: true 
      });
    });

    test('should display available time slots', async ({ page }) => {
      await page.goto(`${baseURL}/chat`);
      
      await page.click('[data-testid="call-button"]');
      await page.click('[data-testid="schedule-call-option"]');
      
      // Verify time slots are displayed
      await expect(page.locator('[data-testid="time-slot"]').first()).toBeVisible();
      
      // Verify slot information
      const timeSlots = page.locator('[data-testid="time-slot"]');
      const slotCount = await timeSlots.count();
      expect(slotCount).toBeGreaterThan(0);
      
      // Verify slot contains time and availability info
      await expect(timeSlots.first()).toContainText(/\d{1,2}:\d{2}/); // Time format
    });

    test('should select date and time slot', async ({ page }) => {
      await page.goto(`${baseURL}/chat`);
      
      await page.click('[data-testid="call-button"]');
      await page.click('[data-testid="schedule-call-option"]');
      
      // Select a future date
      await page.click('[data-testid="calendar-next-day"]');
      
      // Select an available time slot
      await page.click('[data-testid="time-slot"]:not(.unavailable)');
      
      // Verify selection
      await expect(page.locator('[data-testid="selected-time-slot"]')).toBeVisible();
      await expect(page.locator('[data-testid="confirm-schedule-button"]')).toBeEnabled();
    });

    test('should schedule call successfully', async ({ page }) => {
      await page.goto(`${baseURL}/chat`);
      
      await page.click('[data-testid="call-button"]');
      await page.click('[data-testid="schedule-call-option"]');
      
      // Select date and time
      await page.click('[data-testid="calendar-next-day"]');
      await page.click('[data-testid="time-slot"]:not(.unavailable)');
      
      // Add optional notes
      await page.fill('[data-testid="schedule-notes"]', 'Problème avec mon forfait mobile');
      
      // Confirm scheduling
      await page.click('[data-testid="confirm-schedule-button"]');
      
      // Verify confirmation
      await expect(page.locator('[data-testid="schedule-confirmation"]')).toBeVisible();
      await expect(page.locator('[data-testid="schedule-confirmation"]')).toContainText(/confirmé|scheduled|planifié/i);
      
      // Verify calendar entry or notification
      await expect(page.locator('[data-testid="scheduled-call-notification"]')).toBeVisible();
    });

    test('should handle unavailable time slots', async ({ page }) => {
      await page.goto(`${baseURL}/chat`);
      
      await page.click('[data-testid="call-button"]');
      await page.click('[data-testid="schedule-call-option"]');
      
      // Try to click an unavailable slot
      const unavailableSlot = page.locator('[data-testid="time-slot"].unavailable');
      
      if (await unavailableSlot.count() > 0) {
        await unavailableSlot.first().click();
        
        // Verify slot cannot be selected
        await expect(page.locator('[data-testid="confirm-schedule-button"]')).toBeDisabled();
        
        // Verify unavailable message
        await expect(page.locator('[data-testid="slot-unavailable-message"]')).toBeVisible();
      }
    });

    test('should display agent preferences', async ({ page }) => {
      await page.goto(`${baseURL}/chat`);
      
      await page.click('[data-testid="call-button"]');
      await page.click('[data-testid="schedule-call-option"]');
      
      // Verify agent selection if available
      const agentSelector = page.locator('[data-testid="agent-selector"]');
      if (await agentSelector.isVisible()) {
        await expect(agentSelector).toBeVisible();
        
        // Verify agent options
        await agentSelector.click();
        await expect(page.locator('[data-testid="agent-option"]').first()).toBeVisible();
      }
    });
  });

  test.describe('Call History and Management', () => {
    test('should display call history', async ({ page }) => {
      await page.goto(`${baseURL}/dashboard`);
      
      // Navigate to call history section
      await page.click('[data-testid="call-history-tab"]');
      
      // Verify call history interface
      await expect(page.locator('[data-testid="call-history-list"]')).toBeVisible();
      
      // Verify history entries if any exist
      const historyEntries = page.locator('[data-testid="call-history-entry"]');
      const entryCount = await historyEntries.count();
      
      if (entryCount > 0) {
        // Verify entry contains required information
        await expect(historyEntries.first()).toContainText(/\d{1,2}\/\d{1,2}\/\d{4}/); // Date
        await expect(historyEntries.first()).toContainText(/\d{1,2}:\d{2}/); // Time
      }
    });

    test('should show scheduled calls', async ({ page }) => {
      await page.goto(`${baseURL}/dashboard`);
      
      // Navigate to scheduled calls
      await page.click('[data-testid="scheduled-calls-tab"]');
      
      // Verify scheduled calls interface
      await expect(page.locator('[data-testid="scheduled-calls-list"]')).toBeVisible();
      
      // Verify upcoming calls section
      await expect(page.locator('[data-testid="upcoming-calls"]')).toBeVisible();
    });

    test('should allow call rescheduling', async ({ page }) => {
      await page.goto(`${baseURL}/dashboard`);
      await page.click('[data-testid="scheduled-calls-tab"]');
      
      // Find a scheduled call and reschedule
      const scheduledCall = page.locator('[data-testid="scheduled-call-entry"]').first();
      
      if (await scheduledCall.isVisible()) {
        await scheduledCall.click();
        await page.click('[data-testid="reschedule-button"]');
        
        // Verify reschedule interface
        await expect(page.locator('[data-testid="reschedule-modal"]')).toBeVisible();
        await expect(page.locator('[data-testid="calendar-widget"]')).toBeVisible();
      }
    });

    test('should allow call cancellation', async ({ page }) => {
      await page.goto(`${baseURL}/dashboard`);
      await page.click('[data-testid="scheduled-calls-tab"]');
      
      // Find a scheduled call and cancel
      const scheduledCall = page.locator('[data-testid="scheduled-call-entry"]').first();
      
      if (await scheduledCall.isVisible()) {
        await scheduledCall.click();
        await page.click('[data-testid="cancel-call-button"]');
        
        // Verify cancellation confirmation
        await expect(page.locator('[data-testid="cancel-confirmation"]')).toBeVisible();
        
        // Confirm cancellation
        await page.click('[data-testid="confirm-cancel-button"]');
        
        // Verify call is removed or marked as cancelled
        await expect(page.locator('[data-testid="cancellation-success"]')).toBeVisible();
      }
    });
  });

  test.describe('Error Handling and Edge Cases', () => {
    test('should handle call connection failures', async ({ page }) => {
      // Mock call service failure
      await page.route('**/api/calls/**', route => route.fulfill({
        status: 503,
        body: JSON.stringify({ error: 'Service unavailable' })
      }));
      
      await page.goto(`${baseURL}/chat`);
      await page.click('[data-testid="call-button"]');
      await page.click('[data-testid="immediate-call-option"]');
      
      // Verify error handling
      await expect(page.locator('[data-testid="call-error"]')).toBeVisible();
      await expect(page.locator('[data-testid="call-error"]')).toContainText(/Erreur|Error|Indisponible/);
      
      // Verify retry option
      await expect(page.locator('[data-testid="retry-call-button"]')).toBeVisible();
    });

    test('should handle no available agents', async ({ page }) => {
      // Mock no agents available response
      await page.route('**/api/calls/availability', route => route.fulfill({
        status: 200,
        body: JSON.stringify({ availableAgents: 0, estimatedWaitTime: null })
      }));
      
      await page.goto(`${baseURL}/chat`);
      await page.click('[data-testid="call-button"]');
      
      // Verify no agents message
      await expect(page.locator('[data-testid="no-agents-available"]')).toBeVisible();
      await expect(page.locator('[data-testid="schedule-alternative"]')).toBeVisible();
    });

    test('should handle call drops gracefully', async ({ page }) => {
      await page.goto(`${baseURL}/chat`);
      await page.click('[data-testid="call-button"]');
      await page.click('[data-testid="immediate-call-option"]');
      
      // Wait for call to connect
      await expect(page.locator('[data-testid="call-interface"]')).toBeVisible({ timeout: 10000 });
      
      // Simulate call drop
      await page.route('**/api/calls/**', route => route.abort());
      
      // Verify call drop handling
      await expect(page.locator('[data-testid="call-dropped"]')).toBeVisible({ timeout: 5000 });
      await expect(page.locator('[data-testid="reconnect-button"]')).toBeVisible();
    });
  });
});
