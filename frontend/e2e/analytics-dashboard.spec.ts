import { test, expect } from '@playwright/test'; import { TestHelpers } from './utils/test-helpers'; test.describe('Analytics Dashboard', () => { let helpers: TestHelpers; test.beforeEach(async ({ page }) => { helpers = new TestHelpers(page); // Login as admin first await page.goto('/login'); await page.fill('[data-testid="email-input"]', '<EMAIL>'); await page.fill('[data-testid="password-input"]', 'TestAdmin123!'); await page.click('[data-testid="login-button"]'); await page.waitForURL('**/dashboard'); // Navigate to analytics await page.goto('/analytics'); await helpers.waitForDashboardLoad(); }); test('should display analytics dashboard with KPI cards', async ({ page }) => { // Check page title await expect(page.getByText('Analytics Dashboard')).toBeVisible(); await expect(page.getByText('Analyse des performances et insights')).toBeVisible(); // Check KPI cards await expect(page.getByText('Volume des tickets (30 jours)')).toBeVisible(); await expect(page.getByText('Satisfaction client')).toBeVisible(); await expect(page.getByText('Temps de réponse moyen')).toBeVisible(); await expect(page.getByText('Taux de résolution')).toBeVisible(); }); test('should display KPI values with proper formatting', async ({ page }) => { // Check specific KPI values from mockup await expect(page.getByText('1,247')).toBeVisible(); // Volume await expect(page.getByText('4.2/5')).toBeVisible(); // Satisfaction await expect(page.getByText('2.4h')).toBeVisible(); // Response time await expect(page.getByText('3.2%')).toBeVisible(); // Resolution rate // Check trend indicators const trendIcons = page.locator('.MuiSvgIcon-root').filter({ hasText: /trending/i }); // Check for trend indicators (up/down arrows) await expect(page.getByText(/\+12%|\+0\.3|-15min|\+0\.8%/)).toBeVisible(); }); test('should show time range and filter controls', async ({ page }) => { // Check time range selector await expect(page.getByLabel('Période')).toBeVisible(); // Check type filter await expect(page.getByLabel('Type')).toBeVisible(); // Test time range selection await page.getByLabel('Période').click(); await expect(page.getByRole('option', { name: '30 derniers jours' })).toBeVisible(); await expect(page.getByRole('option', { name: '7 derniers jours' })).toBeVisible(); await expect(page.getByRole('option', { name: '90 derniers jours' })).toBeVisible(); // Select different time range await page.getByRole('option', { name: '7 derniers jours' }).click(); // Test type filter await page.getByLabel('Type').click(); await expect(page.getByRole('option', { name: 'Tous les types' })).toBeVisible(); await expect(page.getByRole('option', { name: 'Technique' })).toBeVisible(); await expect(page.getByRole('option', { name: 'Facturation' })).toBeVisible(); await expect(page.getByRole('option', { name: 'Réseau' })).toBeVisible(); }); test('should display volume chart placeholder', async ({ page }) => { // Check chart section await expect(page.getByText('Volume des tickets (30 jours)')).toBeVisible(); // Check chart placeholder await expect(page.getByText(/Graphique des volumes/)).toBeVisible(); // In a real implementation, this would test actual chart rendering // For now, we verify the placeholder is shown const chartContainer = page.locator('[data-testid="volume-chart"], .MuiCardContent-root').filter({ hasText: /Graphique/ }); await expect(chartContainer).toBeVisible(); }); test('should show category distribution', async ({ page }) => { // Check category distribution section await expect(page.getByText('Répartition par catégorie')).toBeVisible(); // Check categories await expect(page.getByText('Technique')).toBeVisible(); await expect(page.getByText('Facturation')).toBeVisible(); await expect(page.getByText('Réseau')).toBeVisible(); await expect(page.getByText('Abonnement')).toBeVisible(); // Check percentages await expect(page.getByText(/\d+\.\d+% du total/)).toBeVisible(); // Check color indicators const colorIndicators = page.locator('[style*="background-color"], .MuiBox-root').filter({ hasText: /border-radius.*50%/ }); // Color indicators should be present for each category }); test('should display top reasons for issues', async ({ page }) => { // Check top reasons section await expect(page.getByText('Top 5 raisons de résiliation')).toBeVisible(); // Check reason entries await expect(page.getByText('Problème de connexion WiFi')).toBeVisible(); await expect(page.getByText('Question sur la facture')).toBeVisible(); await expect(page.getByText('Changement de forfait')).toBeVisible(); // Check occurrence counts await expect(page.getByText(/\d+ occurrences/)).toBeVisible(); // Check trend indicators await expect(page.getByText(/↗|↘|→/)).toBeVisible(); }); test('should show AI improvement suggestions', async ({ page }) => { // Check AI suggestions section await expect(page.getByText('Suggestions d\'amélioration IA')).toBeVisible(); // Check suggestion entries await expect(page.getByText('Optimiser les réponses automatiques')).toBeVisible(); await expect(page.getByText('Formation agents sur WiFi')).toBeVisible(); await expect(page.getByText('Chatbot proactif facturation')).toBeVisible(); // Check impact indicators await expect(page.getByText(/Impact Élevé|Impact Moyen|Impact Faible/)).toBeVisible(); // Check effort indicators await expect(page.getByText(/Effort requis: Élevé|Effort requis: Moyen|Effort requis: Faible/)).toBeVisible(); }); test('should handle filter changes', async ({ page }) => { // Mock API response for filtered data await helpers.mockApiResponse(/\/api\/analytics/, { kpis: { volume: '856', satisfaction: '4.1/5', responseTime: '2.1h', resolution: '3.8%' } }); // Change time range await page.getByLabel('Période').click(); await page.getByRole('option', { name: '7 derniers jours' }).click(); // Wait for potential data update await page.waitForTimeout(1000); // Change type filter await page.getByLabel('Type').click(); await page.getByRole('option', { name: 'Technique' }).click(); // Wait for potential data update await page.waitForTimeout(1000); // Verify filters are applied (in real implementation, data would change) await expect(page.getByLabel('Période')).toHaveValue('7'); await expect(page.getByLabel('Type')).toHaveValue('technique'); }); test('should be responsive on different screen sizes', async ({ page }) => { // Test tablet view await page.setViewportSize({ width: 768, height: 1024 }); // KPI cards should be responsive await expect(page.getByText('Analytics Dashboard')).toBeVisible(); await expect(page.getByText('Volume des tickets')).toBeVisible(); // Test mobile view await page.setViewportSize({ width: 375, height: 667 }); // Content should stack vertically on mobile await expect(page.getByText('Analytics Dashboard')).toBeVisible(); // Filters should be accessible await expect(page.getByLabel('Période')).toBeVisible(); await expect(page.getByLabel('Type')).toBeVisible(); // KPI cards should stack const kpiCards = page.locator('.MuiCard-root').filter({ hasText: /Volume|Satisfaction|Temps|Taux/ }); const cardCount = await kpiCards.count(); expect(cardCount).toBeGreaterThanOrEqual(4); }); test('should handle KPI card interactions', async ({ page }) => { // KPI cards should be interactive (hover effects) const volumeCard = page.locator('.MuiCard-root').filter({ hasText: 'Volume des tickets' }); // Hover over card await volumeCard.hover(); // Check for hover effects (transform, shadow, etc.) // This would depend on the CSS implementation // Cards might be clickable for drill-down // await volumeCard.click(); // In a real implementation, this might open detailed views }); test('should display proper data visualization elements', async ({ page }) => { // Check for chart containers const chartSections = page.locator('.MuiCard-root').filter({ hasText: /Volume|Répartition/ }); const chartCount = await chartSections.count(); expect(chartCount).toBeGreaterThan(0); // Check for data tables/lists const dataSections = page.locator('.MuiList-root, .MuiTable-root'); const dataCount = await dataSections.count(); expect(dataCount).toBeGreaterThan(0); // Check for proper spacing and layout const mainContainer = page.locator('.MuiContainer-root, .MuiBox-root').first(); await expect(mainContainer).toBeVisible(); }); test('should handle accessibility requirements', async ({ page }) => { await helpers.checkAccessibility(); // Check heading hierarchy await expect(page.getByRole('heading', { level: 1 })).toBeVisible(); // Main title // Check filter labels await expect(page.getByLabel('Période')).toBeVisible(); await expect(page.getByLabel('Type')).toBeVisible(); // Check keyboard navigation await page.keyboard.press('Tab'); // Check color contrast for data visualization // This would require more sophisticated testing tools // Check ARIA labels for charts (when implemented) // const charts = page.locator('[role="img"], [aria-label*="chart"]'); }); test('should handle loading states', async ({ page }) => { // Mock slow API response await page.route(/\/api\/analytics/, async route => { await new Promise(resolve => setTimeout(resolve, 2000)); await route.fulfill({ status: 200, contentType: 'application/json', body: JSON.stringify({ success: true }) }); }); // Change filter to trigger loading await page.getByLabel('Période').click(); await page.getByRole('option', { name: '90 derniers jours' }).click(); // Check for loading indicators const loadingIndicators = page.locator('.MuiCircularProgress-root, [data-testid="loading"]'); // In a real implementation, loading states would be shown // Wait for completion await page.waitForTimeout(3000); }); test('should export analytics data', async ({ page }) => { // Look for export functionality const exportButton = page.getByRole('button', { name: /export|télécharger/i }); if (await exportButton.count() > 0) { // Test export functionality await exportButton.click(); // Check for export options await expect(page.getByText(/PDF|Excel|CSV/)).toBeVisible(); } // This feature might not be implemented yet }); test.describe('Real-time Analytics Features', () => { test('should display real-time metrics updates', async ({ page }) => { // Check for real-time metrics section await expect(page.locator('[data-testid="real-time-metrics"]')).toBeVisible(); // Check for live KPIs await expect(page.locator('[data-testid="kpi-total-tickets"]')).toBeVisible(); await expect(page.locator('[data-testid="kpi-response-time"]')).toBeVisible(); await expect(page.locator('[data-testid="kpi-satisfaction"]')).toBeVisible(); // Verify metrics have numeric values const totalTickets = await page.locator('[data-testid="kpi-total-tickets"] .value').textContent(); expect(totalTickets).toMatch(/\d+/); // Check for last updated timestamp await expect(page.locator('[data-testid="last-updated"]')).toBeVisible(); }); test('should update metrics in real-time', async ({ page }) => { // Get initial timestamp const initialTimestamp = await page.locator('[data-testid="last-updated"]').textContent(); // Wait for real-time update (should happen every 30 seconds) await page.waitForTimeout(35000); // Check if timestamp has updated const updatedTimestamp = await page.locator('[data-testid="last-updated"]').textContent(); expect(updatedTimestamp).not.toBe(initialTimestamp); }); test('should show WebSocket connection status', async ({ page }) => { // Check for WebSocket connection indicator const connectionStatus = page.locator('[data-testid="analytics-connection-status"]'); await expect(connectionStatus).toHaveText(/connecté|connected/i); }); test('should handle real-time data via WebSocket', async ({ page }) => { // Mock WebSocket message for new analytics data await page.evaluate(() => { window.dispatchEvent(new CustomEvent('websocket-message', { detail: { type: 'analytics_update', data: { totalTickets: 1250, responseTime: '2.1h', satisfaction: '4.3/5', timestamp: new Date().toISOString() } } })); }); // Verify metrics are updated await expect(page.locator('text=1250')).toBeVisible({ timeout: 5000 }); await expect(page.locator('text=2.1h')).toBeVisible({ timeout: 5000 }); await expect(page.locator('text=4.3/5')).toBeVisible({ timeout: 5000 }); }); test('should display time series charts with real data', async ({ page }) => { // Check for time series chart containers await expect(page.locator('[data-testid="tickets-chart"]')).toBeVisible(); await expect(page.locator('[data-testid="response-time-chart"]')).toBeVisible(); // Verify charts are rendered (look for SVG or canvas elements) const chartElements = page.locator('[data-testid="tickets-chart"] svg, [data-testid="tickets-chart"] canvas'); await expect(chartElements.first()).toBeVisible(); }); test('should show agent performance metrics', async ({ page }) => { // Check for agent performance section await expect(page.locator('[data-testid="agent-performance"]')).toBeVisible(); // Check for agent metrics await expect(page.locator('[data-testid="agent-list"]')).toBeVisible(); // Verify agent data is displayed const agentRows = page.locator('[data-testid="agent-row"]'); const agentCount = await agentRows.count(); expect(agentCount).toBeGreaterThan(0); }); test('should display category distribution with real data', async ({ page }) => { // Check for category distribution chart await expect(page.locator('[data-testid="category-distribution"]')).toBeVisible(); // Verify categories are shown await expect(page.locator('text=Technique')).toBeVisible(); await expect(page.locator('text=Facturation')).toBeVisible(); await expect(page.locator('text=Réseau')).toBeVisible(); // Check for percentage values const percentages = page.locator('text=/\\d+\\.\\d+%/'); const percentageCount = await percentages.count(); expect(percentageCount).toBeGreaterThan(0); }); test('should handle analytics API errors gracefully', async ({ page }) => { // Mock API error await page.route('**/api/analytics/**', route => { route.fulfill({ status: 500, contentType: 'application/json', body: JSON.stringify({ success: false, message: 'Analytics service unavailable' }) }); }); // Refresh page to trigger API call await page.reload(); // Check for error handling await expect(page.locator('[data-testid="analytics-error"]')).toBeVisible({ timeout: 10000 }); await expect(page.locator('text=*erreur*')).toBeVisible(); }); test('should support time range filtering for analytics', async ({ page }) => { // Test different time ranges const timeRanges = ['7d', '30d', '90d']; for (const range of timeRanges) { // Select time range await page.selectOption('[data-testid="time-range-select"]', range); // Wait for data to update await page.waitForTimeout(2000); // Verify data is filtered (check for loading and then data) await expect(page.locator('[data-testid="kpi-total-tickets"]')).toBeVisible(); } }); test('should show insights and recommendations', async ({ page }) => { // Check for insights section await expect(page.locator('[data-testid="analytics-insights"]')).toBeVisible(); // Check for AI-generated insights await expect(page.locator('[data-testid="insight-item"]')).toBeVisible(); // Verify insights have meaningful content const insights = page.locator('[data-testid="insight-item"]'); const insightCount = await insights.count(); expect(insightCount).toBeGreaterThan(0); }); test('should handle concurrent analytics requests', async ({ page, context }) => { // Open multiple analytics pages const pages = [page]; for (let i = 0; i < 2; i++) { const newPage = await context.newPage(); await newPage.goto('/login'); await newPage.fill('[data-testid="email-input"]', '<EMAIL>'); await newPage.fill('[data-testid="password-input"]', 'TestAdmin123!'); await newPage.click('[data-testid="login-button"]'); await newPage.waitForURL('**/dashboard'); await newPage.goto('/analytics'); pages.push(newPage); } // Verify all pages load analytics data for (const p of pages) { await expect(p.locator('[data-testid="kpi-total-tickets"]')).toBeVisible(); await expect(p.locator('[data-testid="real-time-metrics"]')).toBeVisible(); } // Close additional pages for (let i = 1; i < pages.length; i++) { await pages[i].close(); } }); }); });