import { test, expect } from '@playwright/test';

/**
 * =============================================
 * CHAT FUNCTIONALITY E2E TESTS
 * Comprehensive testing of message sending, receiving, and real-time updates
 * =============================================
 */

test.describe('Chat Functionality', () => {
  const baseURL = 'http://localhost:3000';
  
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto(`${baseURL}/login`);
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'UserPassword123!');
    await page.click('[data-testid="login-button"]');
    
    // Wait for successful login and navigate to chat
    await expect(page).toHaveURL(/.*\/dashboard/);
    await page.goto(`${baseURL}/chat`);
    await expect(page.locator('[data-testid="chat-container"]')).toBeVisible();
  });

  test.describe('Chat Interface', () => {
    test('should display chat interface correctly', async ({ page }) => {
      // Verify main chat elements are present
      await expect(page.locator('[data-testid="chat-container"]')).toBeVisible();
      await expect(page.locator('[data-testid="message-list"]')).toBeVisible();
      await expect(page.locator('[data-testid="message-input"]')).toBeVisible();
      await expect(page.locator('[data-testid="send-button"]')).toBeVisible();
      
      // Verify Free Mobile branding
      await expect(page.locator('text=Free Mobile')).toBeVisible();
      
      // Verify chat header
      await expect(page.locator('[data-testid="chat-header"]')).toBeVisible();
      await expect(page.locator('[data-testid="chat-status"]')).toBeVisible();
      
      // Take screenshot
      await page.screenshot({ 
        path: 'test-results/screenshots/chat-interface.png',
        fullPage: true 
      });
    });

    test('should show welcome message on chat start', async ({ page }) => {
      // Verify welcome message is displayed
      await expect(page.locator('[data-testid="welcome-message"]')).toBeVisible();
      await expect(page.locator('[data-testid="welcome-message"]')).toContainText(/Bonjour|Bienvenue|Hello/);
      
      // Verify bot avatar is present
      await expect(page.locator('[data-testid="bot-avatar"]')).toBeVisible();
    });

    test('should display connection status', async ({ page }) => {
      // Verify connection status indicator
      await expect(page.locator('[data-testid="connection-status"]')).toBeVisible();
      
      // Should show connected status
      const statusText = await page.locator('[data-testid="connection-status"]').textContent();
      expect(statusText).toMatch(/Connecté|Connected|En ligne/);
    });
  });

  test.describe('Message Sending', () => {
    test('should send text message successfully', async ({ page }) => {
      const testMessage = 'Bonjour, j\'ai besoin d\'aide avec mon forfait';
      
      // Type message
      await page.fill('[data-testid="message-input"]', testMessage);
      
      // Verify send button is enabled
      await expect(page.locator('[data-testid="send-button"]')).toBeEnabled();
      
      // Send message
      await page.click('[data-testid="send-button"]');
      
      // Verify message appears in chat
      await expect(page.locator('[data-testid="user-message"]').last()).toContainText(testMessage);
      
      // Verify message input is cleared
      await expect(page.locator('[data-testid="message-input"]')).toHaveValue('');
      
      // Verify message timestamp
      await expect(page.locator('[data-testid="message-timestamp"]').last()).toBeVisible();
    });

    test('should send message with Enter key', async ({ page }) => {
      const testMessage = 'Test message avec Enter';
      
      await page.fill('[data-testid="message-input"]', testMessage);
      await page.press('[data-testid="message-input"]', 'Enter');
      
      // Verify message was sent
      await expect(page.locator('[data-testid="user-message"]').last()).toContainText(testMessage);
    });

    test('should prevent sending empty messages', async ({ page }) => {
      // Try to send empty message
      await page.click('[data-testid="send-button"]');
      
      // Verify no new message was added
      const messageCount = await page.locator('[data-testid="user-message"]').count();
      
      // Send button should be disabled for empty input
      await expect(page.locator('[data-testid="send-button"]')).toBeDisabled();
    });

    test('should handle long messages', async ({ page }) => {
      const longMessage = 'A'.repeat(1000); // 1000 character message
      
      await page.fill('[data-testid="message-input"]', longMessage);
      await page.click('[data-testid="send-button"]');
      
      // Verify long message is sent and displayed properly
      await expect(page.locator('[data-testid="user-message"]').last()).toContainText(longMessage);
      
      // Verify message is properly wrapped/truncated in UI
      const messageElement = page.locator('[data-testid="user-message"]').last();
      const boundingBox = await messageElement.boundingBox();
      expect(boundingBox?.width).toBeLessThan(800); // Should not exceed container width
    });

    test('should handle special characters and unicode', async ({ page }) => {
      const specialMessage = 'Message avec caractères spéciaux: @#$%^&*() et unicode';

      await page.fill('[data-testid="message-input"]', specialMessage);
      await page.click('[data-testid="send-button"]');

      // Verify special characters are preserved
      await expect(page.locator('[data-testid="user-message"]').last()).toContainText(specialMessage);
    });
  });

  test.describe('Message Receiving', () => {
    test('should receive bot response after sending message', async ({ page }) => {
      const testMessage = 'Bonjour';
      
      await page.fill('[data-testid="message-input"]', testMessage);
      await page.click('[data-testid="send-button"]');
      
      // Wait for bot response
      await expect(page.locator('[data-testid="bot-message"]').last()).toBeVisible({ timeout: 10000 });
      
      // Verify bot response has content
      const botResponse = await page.locator('[data-testid="bot-message"]').last().textContent();
      expect(botResponse).toBeTruthy();
      expect(botResponse?.length).toBeGreaterThan(0);
      
      // Verify bot avatar is present
      await expect(page.locator('[data-testid="bot-avatar"]')).toBeVisible();
    });

    test('should display typing indicator during bot response', async ({ page }) => {
      const testMessage = 'Question complexe nécessitant une réponse détaillée';
      
      await page.fill('[data-testid="message-input"]', testMessage);
      await page.click('[data-testid="send-button"]');
      
      // Verify typing indicator appears
      await expect(page.locator('[data-testid="typing-indicator"]')).toBeVisible({ timeout: 5000 });
      
      // Verify typing indicator disappears when response arrives
      await expect(page.locator('[data-testid="bot-message"]').last()).toBeVisible({ timeout: 10000 });
      await expect(page.locator('[data-testid="typing-indicator"]')).not.toBeVisible();
    });

    test('should handle multiple quick messages', async ({ page }) => {
      const messages = ['Message 1', 'Message 2', 'Message 3'];
      
      // Send multiple messages quickly
      for (const message of messages) {
        await page.fill('[data-testid="message-input"]', message);
        await page.click('[data-testid="send-button"]');
        await page.waitForTimeout(500); // Small delay between messages
      }
      
      // Verify all messages are displayed
      for (const message of messages) {
        await expect(page.locator(`[data-testid="user-message"]:has-text("${message}")`)).toBeVisible();
      }
      
      // Verify bot responses are received
      await expect(page.locator('[data-testid="bot-message"]')).toHaveCount(3, { timeout: 15000 });
    });
  });

  test.describe('Real-time Features', () => {
    test('should maintain real-time connection', async ({ page }) => {
      // Verify WebSocket connection status
      await expect(page.locator('[data-testid="connection-status"]')).toContainText(/Connecté|Connected/);
      
      // Send a message to test real-time functionality
      await page.fill('[data-testid="message-input"]', 'Test de connexion temps réel');
      await page.click('[data-testid="send-button"]');
      
      // Verify immediate message display (real-time)
      await expect(page.locator('[data-testid="user-message"]').last()).toBeVisible({ timeout: 1000 });
    });

    test('should handle connection interruption gracefully', async ({ page }) => {
      // Simulate network interruption
      await page.route('**/socket.io/**', route => route.abort());
      
      // Try to send a message
      await page.fill('[data-testid="message-input"]', 'Message pendant interruption');
      await page.click('[data-testid="send-button"]');
      
      // Verify connection status shows disconnected
      await expect(page.locator('[data-testid="connection-status"]')).toContainText(/Déconnecté|Disconnected|Hors ligne/, { timeout: 5000 });
      
      // Verify retry mechanism
      await expect(page.locator('[data-testid="reconnecting-indicator"]')).toBeVisible();
    });

    test('should auto-reconnect after connection loss', async ({ page }) => {
      // First verify connected state
      await expect(page.locator('[data-testid="connection-status"]')).toContainText(/Connecté|Connected/);
      
      // Simulate temporary disconnection
      await page.route('**/socket.io/**', route => route.abort());
      await page.waitForTimeout(2000);
      
      // Remove route to allow reconnection
      await page.unroute('**/socket.io/**');
      
      // Verify reconnection
      await expect(page.locator('[data-testid="connection-status"]')).toContainText(/Connecté|Connected/, { timeout: 10000 });
    });
  });

  test.describe('Message History', () => {
    test('should display message history correctly', async ({ page }) => {
      const messages = ['Premier message', 'Deuxième message', 'Troisième message'];
      
      // Send multiple messages
      for (const message of messages) {
        await page.fill('[data-testid="message-input"]', message);
        await page.click('[data-testid="send-button"]');
        await page.waitForTimeout(1000);
      }
      
      // Verify all messages are in correct order
      const messageElements = page.locator('[data-testid="user-message"]');
      const messageCount = await messageElements.count();
      expect(messageCount).toBeGreaterThanOrEqual(3);
      
      // Verify chronological order
      for (let i = 0; i < messages.length; i++) {
        await expect(messageElements.nth(i)).toContainText(messages[i]);
      }
    });

    test('should scroll to latest message automatically', async ({ page }) => {
      // Send multiple messages to create scrollable content
      for (let i = 1; i <= 10; i++) {
        await page.fill('[data-testid="message-input"]', `Message ${i}`);
        await page.click('[data-testid="send-button"]');
        await page.waitForTimeout(500);
      }
      
      // Verify latest message is visible
      await expect(page.locator('[data-testid="user-message"]').last()).toBeInViewport();
    });

    test('should persist chat history on page refresh', async ({ page }) => {
      const testMessage = 'Message à persister';
      
      // Send a message
      await page.fill('[data-testid="message-input"]', testMessage);
      await page.click('[data-testid="send-button"]');
      
      // Wait for message to be displayed
      await expect(page.locator('[data-testid="user-message"]').last()).toContainText(testMessage);
      
      // Refresh page
      await page.reload();
      await expect(page.locator('[data-testid="chat-container"]')).toBeVisible();
      
      // Verify message history is restored
      await expect(page.locator(`[data-testid="user-message"]:has-text("${testMessage}")`)).toBeVisible();
    });
  });

  test.describe('Chat Features', () => {
    test('should support file upload functionality', async ({ page }) => {
      // Verify file upload button is present
      await expect(page.locator('[data-testid="file-upload-button"]')).toBeVisible();
      
      // Click file upload button
      await page.click('[data-testid="file-upload-button"]');
      
      // Verify file upload dialog or area appears
      await expect(page.locator('[data-testid="file-upload-area"]')).toBeVisible();
    });

    test('should support voice recording', async ({ page }) => {
      // Verify voice recording button is present
      await expect(page.locator('[data-testid="voice-record-button"]')).toBeVisible();
      
      // Click voice recording button
      await page.click('[data-testid="voice-record-button"]');
      
      // Verify recording state changes
      await expect(page.locator('[data-testid="voice-record-button"]')).toHaveClass(/recording|active/);
    });

    test('should display message status indicators', async ({ page }) => {
      const testMessage = 'Message avec statut';
      
      await page.fill('[data-testid="message-input"]', testMessage);
      await page.click('[data-testid="send-button"]');
      
      // Verify message status indicators
      await expect(page.locator('[data-testid="message-status"]').last()).toBeVisible();
      
      // Should show sent status
      await expect(page.locator('[data-testid="message-status"]').last()).toHaveClass(/sent|delivered/);
    });

    test('should support message reactions/feedback', async ({ page }) => {
      // Send a message first
      await page.fill('[data-testid="message-input"]', 'Message pour feedback');
      await page.click('[data-testid="send-button"]');
      
      // Wait for bot response
      await expect(page.locator('[data-testid="bot-message"]').last()).toBeVisible({ timeout: 10000 });
      
      // Verify feedback buttons are present
      await expect(page.locator('[data-testid="message-feedback"]').last()).toBeVisible();
      await expect(page.locator('[data-testid="thumbs-up"]').last()).toBeVisible();
      await expect(page.locator('[data-testid="thumbs-down"]').last()).toBeVisible();
    });
  });

  test.describe('Error Handling', () => {
    test('should handle API errors gracefully', async ({ page }) => {
      // Mock API error
      await page.route('**/api/chat/**', route => route.fulfill({
        status: 500,
        body: JSON.stringify({ error: 'Internal server error' })
      }));
      
      await page.fill('[data-testid="message-input"]', 'Message avec erreur API');
      await page.click('[data-testid="send-button"]');
      
      // Verify error message is displayed
      await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
      await expect(page.locator('[data-testid="error-message"]')).toContainText(/Erreur|Error/);
    });

    test('should show retry option on message failure', async ({ page }) => {
      // Mock network failure
      await page.route('**/api/chat/**', route => route.abort());
      
      await page.fill('[data-testid="message-input"]', 'Message qui échoue');
      await page.click('[data-testid="send-button"]');
      
      // Verify retry button appears
      await expect(page.locator('[data-testid="retry-button"]')).toBeVisible({ timeout: 5000 });
      
      // Remove route and test retry
      await page.unroute('**/api/chat/**');
      await page.click('[data-testid="retry-button"]');
      
      // Verify message is sent successfully on retry
      await expect(page.locator('[data-testid="user-message"]').last()).toBeVisible();
    });
  });
});
