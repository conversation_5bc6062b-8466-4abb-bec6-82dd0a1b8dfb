import { chromium, FullConfig } from '@playwright/test'; async function globalSetup(config: FullConfig) { console.log('[DEPLOY] Starting global setup for ChatbotRNCP E2E tests...'); // Launch browser for setup const browser = await chromium.launch(); const context = await browser.newContext(); const page = await context.newPage(); try { // Wait for frontend to be ready console.log('⏳ Waiting for frontend server...'); await page.goto(config.projects[0].use.baseURL || 'http://localhost:3001'); await page.waitForSelector('[data-testid="app-root"], #root', { timeout: 30000 }); console.log('[COMPLETE] Frontend server is ready'); // Wait for backend to be ready console.log('⏳ Waiting for backend server...'); try { const backendResponse = await page.request.get('http://localhost:5000/health'); if (backendResponse.ok()) { console.log('[COMPLETE] Backend server is ready'); } else { console.warn(' Backend server health check failed, continuing...'); } } catch (error) { console.warn(' Backend server not accessible, continuing...', error); } // Create test users if needed console.log('[USER] Setting up test users...'); await setupTestUsers(page); // Setup test data console.log('[ANALYTICS] Setting up test data...'); await setupTestData(page); // Store authentication state for tests await setupAuthenticationStates(context); console.log('[COMPLETE] Global setup completed successfully'); } catch (error) { console.error('[FAILED] Global setup failed:', error); throw error; } finally { await browser.close(); } } async function setupTestUsers(page: any) { try { // Test users configuration const testUsers = [ { email: '<EMAIL>', password: 'TestAdmin123!', firstName: 'Test', lastName: 'Admin', role: 'admin' }, { email: '<EMAIL>', password: 'TestAgent123!', firstName: 'Test', lastName: 'Agent', role: 'agent' }, { email: '<EMAIL>', password: 'TestCustomer123!', firstName: 'Test', lastName: 'Customer', role: 'user' } ]; // Register users via API for (const user of testUsers) { try { const response = await page.request.post('http://localhost:5000/api/auth/register', { data: user }); if (response.ok()) { console.log(`[COMPLETE] Created test user: ${user.email}`); } else { const error = await response.json(); if (error.message?.includes('already exists')) { console.log(`ℹ Test user already exists: ${user.email}`); } else { console.warn(` Failed to create user ${user.email}:`, error); } } } catch (error) { console.warn(` Error creating user ${user.email}:`, error); } } } catch (error) { console.error('[FAILED] Failed to setup test users:', error); } } async function setupTestData(page: any) { try { // Login as admin to create test data const loginResponse = await page.request.post('http://localhost:5000/api/auth/login', { data: { email: '<EMAIL>', password: 'TestAdmin123!' } }); if (!loginResponse.ok()) { console.warn(' Could not login as admin for test data setup'); return; } const loginData = await loginResponse.json(); const token = loginData.token; // Create test support ticket const testTicket = { title: 'Test Support Ticket for E2E', description: 'This is a test support ticket created for end-to-end testing', category: 'technical', priority: 'medium', customerEmail: '<EMAIL>' }; try { const ticketResponse = await page.request.post('http://localhost:5000/api/support/tickets', { data: testTicket, headers: { 'Authorization': `Bearer ${token}` } }); if (ticketResponse.ok()) { console.log('[COMPLETE] Created test support ticket'); } } catch (error) { console.warn(' Failed to create test support ticket:', error); } // Create test conversation try { const testConversation = { message: 'Hello, this is a test conversation for E2E testing', type: 'text' }; const conversationResponse = await page.request.post('http://localhost:5000/api/chat/conversations', { data: testConversation, headers: { 'Authorization': `Bearer ${token}` } }); if (conversationResponse.ok()) { console.log('[COMPLETE] Created test conversation'); } } catch (error) { console.warn(' Failed to create test conversation:', error); } } catch (error) { console.error('[FAILED] Failed to setup test data:', error); } } async function setupAuthenticationStates(context: any) { try { // Store authentication states for different user types const authStates = ['admin', 'agent', 'customer']; for (const userType of authStates) { const page = await context.newPage(); try { await page.goto('http://localhost:3000/login'); // Login with test credentials const email = `${userType}@test.com`; const password = userType === 'admin' ? 'TestAdmin123!' : userType === 'agent' ? 'TestAgent123!' : 'TestCustomer123!'; await page.fill('[data-testid="email-input"], input[type="email"]', email); await page.fill('[data-testid="password-input"], input[type="password"]', password); await page.click('[data-testid="login-button"], button[type="submit"]'); // Wait for successful login await page.waitForURL('**/dashboard', { timeout: 10000 }); // Store the authentication state await context.storageState({ path: `e2e/auth-${userType}.json` }); console.log(`[COMPLETE] Stored authentication state for ${userType}`); } catch (error) { console.warn(` Failed to setup auth state for ${userType}:`, error); } finally { await page.close(); } } } catch (error) { console.error('[FAILED] Failed to setup authentication states:', error); } } export default globalSetup;