import { test, expect } from '@playwright/test'; import { TestHelpers } from './utils/test-helpers'; test.describe('Dashboard Navigation', () => { let helpers: TestHelpers; test.beforeEach(async ({ page }) => { helpers = new TestHelpers(page); await page.goto('/dashboard'); await helpers.waitForDashboardLoad(); }); test('should display main navigation tabs', async ({ page }) => { // Check all main navigation tabs are present await expect(page.getByRole('tab', { name: 'Support Client' })).toBeVisible(); await expect(page.getByRole('tab', { name: 'Formulaire Support' })).toBeVisible(); await expect(page.getByRole('tab', { name: 'Panel Admin' })).toBeVisible(); await expect(page.getByRole('tab', { name: 'Analytics' })).toBeVisible(); }); test('should navigate to Support Client section by default', async ({ page }) => { // Should redirect to overview by default await expect(page).toHaveURL(/\/dashboard\/overview/); // Should show welcome message await expect(page.getByText('Bienvenue sur le support Free Mobile')).toBeVisible(); // Should show quick actions await expect(page.getByText('Actions rapides')).toBeVisible(); await expect(page.getByText('Nouveau ticket')).toBeVisible(); await expect(page.getByText('Chat en direct')).toBeVisible(); }); test('should navigate to Formulaire Support section', async ({ page }) => { await helpers.navigateToSection('support-form'); // Check URL await expect(page).toHaveURL(/\/dashboard\/support-form/); // Check page content await expect(page.getByText('Contacter le support Free')).toBeVisible(); await expect(page.getByText('Questions fréquentes')).toBeVisible(); await expect(page.getByText('Chat en direct')).toBeVisible(); }); test('should navigate to Panel Admin section', async ({ page }) => { await helpers.navigateToSection('admin'); // Check URL await expect(page).toHaveURL(/\/dashboard\/admin/); // Check page content await expect(page.getByText('Panel Admin - Dashboard')).toBeVisible(); // Check sidebar is visible await expect(page.getByText('Dashboard')).toBeVisible(); await expect(page.getByText('Conversations')).toBeVisible(); await expect(page.getByText('Clients')).toBeVisible(); }); test('should navigate to Analytics section', async ({ page }) => { await helpers.navigateToSection('analytics'); // Check URL await expect(page).toHaveURL(/\/dashboard\/analytics/); // Check page content await expect(page.getByText('Analytics Dashboard')).toBeVisible(); await expect(page.getByText('Volume des tickets')).toBeVisible(); await expect(page.getByText('Satisfaction client')).toBeVisible(); }); test('should show admin sidebar navigation', async ({ page }) => { await helpers.navigateToSection('admin'); // Check sidebar items await expect(page.getByText('Dashboard')).toBeVisible(); await expect(page.getByText('Conversations')).toBeVisible(); await expect(page.getByText('Notifications')).toBeVisible(); await expect(page.getByText('Suggestions')).toBeVisible(); await expect(page.getByText('Simulations')).toBeVisible(); await expect(page.getByText('Clients')).toBeVisible(); }); test('should navigate within admin sections', async ({ page }) => { await helpers.navigateToSection('admin'); // Navigate to Clients section await page.getByText('Clients').click(); await expect(page).toHaveURL(/\/dashboard\/admin\/clients/); await expect(page.getByText('Gestion des Clients')).toBeVisible(); // Navigate to Conversations section await page.getByText('Conversations').click(); await expect(page).toHaveURL(/\/dashboard\/admin\/conversations/); await expect(page.getByText('Gestion des Conversations')).toBeVisible(); }); test('should maintain navigation state on page refresh', async ({ page }) => { // Navigate to a specific section await helpers.navigateToSection('analytics'); await expect(page).toHaveURL(/\/dashboard\/analytics/); // Refresh page await page.reload(); await helpers.waitForDashboardLoad(); // Should maintain the same section await expect(page).toHaveURL(/\/dashboard\/analytics/); await expect(page.getByText('Analytics Dashboard')).toBeVisible(); }); test('should handle browser back/forward navigation', async ({ page }) => { // Start at overview await expect(page).toHaveURL(/\/dashboard\/overview/); // Navigate to support form await helpers.navigateToSection('support-form'); await expect(page).toHaveURL(/\/dashboard\/support-form/); // Navigate to admin await helpers.navigateToSection('admin'); await expect(page).toHaveURL(/\/dashboard\/admin/); // Use browser back button await page.goBack(); await expect(page).toHaveURL(/\/dashboard\/support-form/); // Use browser forward button await page.goForward(); await expect(page).toHaveURL(/\/dashboard\/admin/); }); test('should show Free Mobile branding', async ({ page }) => { // Check logo/branding await expect(page.getByText('Free Mobile')).toBeVisible(); // Check color scheme (Free Mobile red) const header = page.locator('.MuiAppBar-root'); await expect(header).toHaveCSS('background-color', 'rgb(230, 0, 0)'); }); test('should be responsive on mobile devices', async ({ page }) => { // Set mobile viewport await page.setViewportSize({ width: 375, height: 667 }); // Check that navigation is still accessible await expect(page.getByRole('tab', { name: 'Support Client' })).toBeVisible(); // On mobile, admin sidebar should be collapsible await helpers.navigateToSection('admin'); // Check if hamburger menu is visible on mobile const menuButton = page.getByRole('button', { name: 'open drawer' }); if (await menuButton.isVisible()) { await menuButton.click(); await expect(page.getByText('Dashboard')).toBeVisible(); } }); });