import { test, expect } from '@playwright/test'; import { TestHelpers } from './utils/test-helpers'; test.describe('Admin Panel', () => { let helpers: TestHelpers; test.beforeEach(async ({ page }) => { helpers = new TestHelpers(page); await page.goto('/dashboard/admin'); await helpers.waitForDashboardLoad(); }); test('should display admin dashboard overview', async ({ page }) => { // Check page title await expect(page.getByText('Panel Admin - Dashboard')).toBeVisible(); // Check KPI cards await expect(page.getByText('Tickets actifs')).toBeVisible(); await expect(page.getByText('Satisfaction client')).toBeVisible(); await expect(page.getByText('Temps de réponse')).toBeVisible(); await expect(page.getByText('Agents en ligne')).toBeVisible(); // Check recent tickets section await expect(page.getByText('Tickets récents')).toBeVisible(); // Check agent performance section await expect(page.getByText('Performance des agents')).toBeVisible(); }); test('should display KPI metrics with proper formatting', async ({ page }) => { // Check KPI values are displayed const kpiCards = page.locator('.MuiCard-root').filter({ hasText: /\d+/ }); const count = await kpiCards.count(); expect(count).toBeGreaterThan(0); // Check specific KPI formats await expect(page.getByText('1,247')).toBeVisible(); // Ticket count await expect(page.getByText('4.2/5')).toBeVisible(); // Satisfaction rating await expect(page.getByText('2.4h')).toBeVisible(); // Response time await expect(page.getByText(/\d+\/\d+/)).toBeVisible(); // Agents ratio }); test('should show recent tickets with proper information', async ({ page }) => { // Check tickets list const ticketsList = page.locator('[data-testid="recent-tickets"], .MuiList-root').first(); await expect(ticketsList).toBeVisible(); // Check ticket information await expect(page.getByText(/#\d+/)).toBeVisible(); // Ticket ID await expect(page.getByText(/Marie|Jean|Sophie/)).toBeVisible(); // Client names await expect(page.getByText(/Facturation|Technique|Réseau/)).toBeVisible(); // Categories await expect(page.getByText(/En attente|En cours|Résolu/)).toBeVisible(); // Status }); test('should display agent performance metrics', async ({ page }) => { // Check agent performance section await expect(page.getByText('Performance des agents')).toBeVisible(); // Check agent entries await expect(page.getByText(/Agent \d+/)).toBeVisible(); // Check efficiency bars const progressBars = page.locator('.MuiLinearProgress-root'); const progressCount = await progressBars.count(); expect(progressCount).toBeGreaterThan(0); // Check satisfaction ratings await expect(page.getByText(/\d+\.\d+\/5/)).toBeVisible(); }); test('should show system status indicators', async ({ page }) => { // Check system status section await expect(page.getByText('État du système')).toBeVisible(); // Check service status indicators await expect(page.getByText('API Backend')).toBeVisible(); await expect(page.getByText('Base de données')).toBeVisible(); await expect(page.getByText('IA Rasa')).toBeVisible(); // Check status icons (success/warning/error) const statusIcons = page.locator('.MuiSvgIcon-root').filter({ hasText: /check|warning|error/i }); // Icons might not have text, so we check for their presence const iconCount = await page.locator('.MuiSvgIcon-colorSuccess, .MuiSvgIcon-colorWarning, .MuiSvgIcon-colorError').count(); expect(iconCount).toBeGreaterThan(0); }); test('should navigate to clients management', async ({ page }) => { // Click on Clients in sidebar await page.getByText('Clients').click(); // Check URL change await expect(page).toHaveURL(/\/dashboard\/admin\/clients/); // Check page content await expect(page.getByText('Gestion des Clients')).toBeVisible(); await expect(page.getByText('Demandes clients')).toBeVisible(); await expect(page.getByText('Profil Client')).toBeVisible(); }); test('should display client requests table', async ({ page }) => { await page.getByText('Clients').click(); // Check table headers await expect(page.getByText('Client')).toBeVisible(); await expect(page.getByText('Catégorie')).toBeVisible(); await expect(page.getByText('Statut')).toBeVisible(); await expect(page.getByText('Date')).toBeVisible(); await expect(page.getByText('Tags')).toBeVisible(); // Check search functionality const searchInput = page.getByPlaceholder('Rechercher...'); await expect(searchInput).toBeVisible(); // Check filter dropdowns await expect(page.getByText('Tous les statuts')).toBeVisible(); await expect(page.getByText('Toutes catégories')).toBeVisible(); }); test('should filter client requests', async ({ page }) => { await page.getByText('Clients').click(); // Test search filter const searchInput = page.getByPlaceholder('Rechercher...'); await searchInput.fill('Jean'); // Should filter results (assuming there's a Jean in the data) await page.waitForTimeout(500); // Wait for filter to apply // Test status filter await page.getByLabel('Statut').click(); await page.getByRole('option', { name: 'Résolu' }).click(); // Should show only resolved tickets await page.waitForTimeout(500); // Clear filters await searchInput.clear(); await page.getByLabel('Statut').click(); await page.getByRole('option', { name: 'Tous les statuts' }).click(); }); test('should display client profile panel', async ({ page }) => { await page.getByText('Clients').click(); // Check profile panel await expect(page.getByText('Profil Client')).toBeVisible(); // Check client information await expect(page.getByText(/Jean|Marie|Sophie/)).toBeVisible(); // Client name await expect(page.getByText(/FREE-\d+/)).toBeVisible(); // Client ID await expect(page.getByText(/Actif|Inactif/)).toBeVisible(); // Status // Check contact information await expect(page.getByText(/@/)).toBeVisible(); // Email await expect(page.getByText(/06|07/)).toBeVisible(); // Phone // Check statistics await expect(page.getByText('Tickets total')).toBeVisible(); await expect(page.getByText('Satisfaction')).toBeVisible(); // Check quick actions await expect(page.getByText('Appeler')).toBeVisible(); await expect(page.getByText('Chat')).toBeVisible(); }); test('should navigate to conversations management', async ({ page }) => { await page.getByText('Conversations').click(); // Check URL await expect(page).toHaveURL(/\/dashboard\/admin\/conversations/); // Check page content await expect(page.getByText('Gestion des Conversations')).toBeVisible(); await expect(page.getByText('Conversations')).toBeVisible(); await expect(page.getByText('Statistiques temps réel')).toBeVisible(); }); test('should display conversation statistics', async ({ page }) => { await page.getByText('Conversations').click(); // Check real-time stats await expect(page.getByText('Conversations actives')).toBeVisible(); await expect(page.getByText('En attente')).toBeVisible(); await expect(page.getByText('Agents en ligne')).toBeVisible(); await expect(page.getByText('Escaladées')).toBeVisible(); // Check conversation tabs await expect(page.getByText('Toutes')).toBeVisible(); await expect(page.getByText('Actives')).toBeVisible(); await expect(page.getByText('En attente')).toBeVisible(); await expect(page.getByText('Escaladées')).toBeVisible(); }); test('should handle conversation filtering', async ({ page }) => { await page.getByText('Conversations').click(); // Test search const searchInput = page.getByPlaceholder('Rechercher...'); await searchInput.fill('Marie'); await page.waitForTimeout(500); // Test status filter await page.getByLabel('Statut').click(); await page.getByRole('option', { name: 'Actif' }).click(); // Test channel filter await page.getByLabel('Canal').click(); await page.getByRole('option', { name: 'Web' }).click(); // Clear filters await searchInput.clear(); }); test('should be responsive on mobile', async ({ page }) => { await page.setViewportSize({ width: 375, height: 667 }); // Sidebar should be collapsible on mobile const menuButton = page.getByRole('button', { name: 'open drawer' }); if (await menuButton.isVisible()) { await menuButton.click(); await expect(page.getByText('Dashboard')).toBeVisible(); // Close sidebar const closeButton = page.getByRole('button', { name: 'close' }); if (await closeButton.isVisible()) { await closeButton.click(); } } // Content should be responsive await expect(page.getByText('Panel Admin - Dashboard')).toBeVisible(); // KPI cards should stack vertically on mobile const kpiCards = page.locator('.MuiCard-root').first(); await expect(kpiCards).toBeVisible(); }); test('should handle admin panel accessibility', async ({ page }) => { await helpers.checkAccessibility(); // Check keyboard navigation await page.keyboard.press('Tab'); // Check ARIA labels on interactive elements const buttons = page.getByRole('button'); const buttonCount = await buttons.count(); expect(buttonCount).toBeGreaterThan(0); // Check table accessibility await page.getByText('Clients').click(); const table = page.getByRole('table'); if (await table.count() > 0) { await expect(table.first()).toBeVisible(); // Check table headers const headers = page.getByRole('columnheader'); const headerCount = await headers.count(); expect(headerCount).toBeGreaterThan(0); } }); });