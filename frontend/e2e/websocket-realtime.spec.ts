import { test, expect } from '@playwright/test'; test.describe('WebSocket and Real-time Features', () => { test.beforeEach(async ({ page }) => { // Login as admin await page.goto('/login'); await page.fill('[data-testid="email-input"]', '<EMAIL>'); await page.fill('[data-testid="password-input"]', 'TestAdmin123!'); await page.click('[data-testid="login-button"]'); await page.waitForURL('**/dashboard'); }); test.describe('Real-time Chat', () => { test('should establish WebSocket connection', async ({ page }) => { await page.goto('/chat'); await page.waitForLoadState('networkidle'); // Check for WebSocket connection indicator const connectionStatus = page.locator('[data-testid="connection-status"]'); await expect(connectionStatus).toHaveText(/connecté|connected/i); }); test('should send and receive messages in real-time', async ({ page, context }) => { // Open two pages to simulate real-time communication const page1 = page; const page2 = await context.newPage(); // Login on both pages await page1.goto('/chat'); await page2.goto('/login'); await page2.fill('[data-testid="email-input"]', '<EMAIL>'); await page2.fill('[data-testid="password-input"]', 'TestAgent123!'); await page2.click('[data-testid="login-button"]'); await page2.waitForURL('**/dashboard'); await page2.goto('/chat'); await page1.waitForLoadState('networkidle'); await page2.waitForLoadState('networkidle'); // Send message from page1 const testMessage = `Test message ${Date.now()}`; await page1.fill('[data-testid="message-input"]', testMessage); await page1.click('[data-testid="send-button"]'); // Verify message appears on page1 await expect(page1.locator(`text=${testMessage}`)).toBeVisible(); // Verify message appears on page2 in real-time await expect(page2.locator(`text=${testMessage}`)).toBeVisible({ timeout: 10000 }); await page2.close(); }); test('should show typing indicators', async ({ page, context }) => { const page1 = page; const page2 = await context.newPage(); // Setup both pages await page1.goto('/chat'); await page2.goto('/login'); await page2.fill('[data-testid="email-input"]', '<EMAIL>'); await page2.fill('[data-testid="password-input"]', 'TestAgent123!'); await page2.click('[data-testid="login-button"]'); await page2.waitForURL('**/dashboard'); await page2.goto('/chat'); await page1.waitForLoadState('networkidle'); await page2.waitForLoadState('networkidle'); // Start typing on page1 await page1.focus('[data-testid="message-input"]'); await page1.type('[data-testid="message-input"]', 'User is typing...'); // Check for typing indicator on page2 await expect(page2.locator('[data-testid="typing-indicator"]')).toBeVisible({ timeout: 5000 }); // Stop typing and verify indicator disappears await page1.fill('[data-testid="message-input"]', ''); await expect(page2.locator('[data-testid="typing-indicator"]')).not.toBeVisible({ timeout: 5000 }); await page2.close(); }); test('should handle connection loss and reconnection', async ({ page }) => { await page.goto('/chat'); await page.waitForLoadState('networkidle'); // Verify initial connection await expect(page.locator('[data-testid="connection-status"]')).toHaveText(/connecté|connected/i); // Simulate network disconnection await page.context().setOffline(true); // Check for disconnection indicator await expect(page.locator('[data-testid="connection-status"]')).toHaveText(/déconnecté|disconnected/i, { timeout: 10000 }); // Restore connection await page.context().setOffline(false); // Verify reconnection await expect(page.locator('[data-testid="connection-status"]')).toHaveText(/connecté|connected/i, { timeout: 15000 }); }); test('should queue messages during disconnection', async ({ page }) => { await page.goto('/chat'); await page.waitForLoadState('networkidle'); // Disconnect await page.context().setOffline(true); await expect(page.locator('[data-testid="connection-status"]')).toHaveText(/déconnecté|disconnected/i, { timeout: 10000 }); // Try to send message while offline const offlineMessage = `Offline message ${Date.now()}`; await page.fill('[data-testid="message-input"]', offlineMessage); await page.click('[data-testid="send-button"]'); // Message should be queued (show pending status) await expect(page.locator('[data-testid="message-pending"]')).toBeVisible(); // Reconnect await page.context().setOffline(false); await expect(page.locator('[data-testid="connection-status"]')).toHaveText(/connecté|connected/i, { timeout: 15000 }); // Queued message should be sent await expect(page.locator('[data-testid="message-sent"]')).toBeVisible({ timeout: 10000 }); }); }); test.describe('Real-time Support Tickets', () => { test('should update ticket status in real-time', async ({ page, context }) => { // Create a test ticket first await page.goto('/support'); await page.fill('[data-testid="ticket-title"]', 'Real-time Test Ticket'); await page.fill('[data-testid="ticket-description"]', 'Testing real-time updates'); await page.selectOption('[data-testid="ticket-category"]', 'technical'); await page.click('[data-testid="submit-ticket-button"]'); await expect(page.locator('text=*créé avec succès*')).toBeVisible(); // Open admin panel in another page const adminPage = await context.newPage(); await adminPage.goto('/login'); await adminPage.fill('[data-testid="email-input"]', '<EMAIL>'); await adminPage.fill('[data-testid="password-input"]', 'TestAdmin123!'); await adminPage.click('[data-testid="login-button"]'); await adminPage.waitForURL('**/dashboard'); await adminPage.goto('/admin/tickets'); // Find the ticket and update its status await adminPage.click('[data-testid="ticket-row"]:has-text("Real-time Test Ticket")'); await adminPage.selectOption('[data-testid="ticket-status-select"]', 'in_progress'); await adminPage.click('[data-testid="update-ticket-button"]'); // Go back to customer view and check for real-time update await page.goto('/support/my-tickets'); await expect(page.locator('text=*en cours*')).toBeVisible({ timeout: 10000 }); await adminPage.close(); }); test('should show real-time ticket notifications', async ({ page }) => { await page.goto('/dashboard'); await page.waitForLoadState('networkidle'); // Mock a new ticket notification await page.evaluate(() => { // Simulate WebSocket message for new ticket window.dispatchEvent(new CustomEvent('websocket-message', { detail: { type: 'new_ticket', data: { id: 'test-ticket-123', title: 'Urgent Support Request', priority: 'high' } } })); }); // Check for notification await expect(page.locator('[data-testid="notification"]')).toBeVisible(); await expect(page.locator('text=*Urgent Support Request*')).toBeVisible(); }); }); test.describe('Real-time Analytics', () => { test('should update analytics dashboard in real-time', async ({ page }) => { await page.goto('/analytics'); await page.waitForLoadState('networkidle'); // Check for real-time metrics const metricsContainer = page.locator('[data-testid="real-time-metrics"]'); await expect(metricsContainer).toBeVisible(); // Verify metrics are updating (check for timestamp changes) const initialTimestamp = await page.locator('[data-testid="last-updated"]').textContent(); // Wait for update await page.waitForTimeout(5000); const updatedTimestamp = await page.locator('[data-testid="last-updated"]').textContent(); expect(updatedTimestamp).not.toBe(initialTimestamp); }); test('should show live KPI updates', async ({ page }) => { await page.goto('/analytics'); await page.waitForLoadState('networkidle'); // Check for KPI widgets await expect(page.locator('[data-testid="kpi-total-tickets"]')).toBeVisible(); await expect(page.locator('[data-testid="kpi-response-time"]')).toBeVisible(); await expect(page.locator('[data-testid="kpi-satisfaction"]')).toBeVisible(); // Verify KPIs have numeric values const totalTickets = await page.locator('[data-testid="kpi-total-tickets"] .value').textContent(); expect(totalTickets).toMatch(/\d+/); }); test('should update charts in real-time', async ({ page }) => { await page.goto('/analytics'); await page.waitForLoadState('networkidle'); // Check for chart containers await expect(page.locator('[data-testid="tickets-chart"]')).toBeVisible(); await expect(page.locator('[data-testid="response-time-chart"]')).toBeVisible(); // Verify charts are rendered (check for SVG or canvas elements) const chartElements = page.locator('[data-testid="tickets-chart"] svg, [data-testid="tickets-chart"] canvas'); await expect(chartElements.first()).toBeVisible(); }); }); test.describe('WebSocket Performance', () => { test('should handle multiple concurrent connections', async ({ page, context }) => { const pages = [page]; // Create multiple pages for (let i = 0; i < 3; i++) { const newPage = await context.newPage(); await newPage.goto('/login'); await newPage.fill('[data-testid="email-input"]', '<EMAIL>'); await newPage.fill('[data-testid="password-input"]', 'TestAdmin123!'); await newPage.click('[data-testid="login-button"]'); await newPage.waitForURL('**/dashboard'); await newPage.goto('/chat'); pages.push(newPage); } // Verify all connections are established for (const p of pages) { await expect(p.locator('[data-testid="connection-status"]')).toHaveText(/connecté|connected/i); } // Send messages from all pages simultaneously const promises = pages.map(async (p, index) => { const message = `Concurrent message ${index} ${Date.now()}`; await p.fill('[data-testid="message-input"]', message); await p.click('[data-testid="send-button"]'); return message; }); const messages = await Promise.all(promises); // Verify all messages appear on all pages for (const p of pages) { for (const message of messages) { await expect(p.locator(`text=${message}`)).toBeVisible({ timeout: 15000 }); } } // Close additional pages for (let i = 1; i < pages.length; i++) { await pages[i].close(); } }); test('should handle rapid message sending', async ({ page }) => { await page.goto('/chat'); await page.waitForLoadState('networkidle'); // Send multiple messages rapidly const messages = []; for (let i = 0; i < 10; i++) { const message = `Rapid message ${i} ${Date.now()}`; messages.push(message); await page.fill('[data-testid="message-input"]', message); await page.click('[data-testid="send-button"]'); await page.waitForTimeout(100); // Small delay between messages } // Verify all messages are sent and displayed for (const message of messages) { await expect(page.locator(`text=${message}`)).toBeVisible({ timeout: 10000 }); } }); test('should maintain connection during page navigation', async ({ page }) => { await page.goto('/chat'); await page.waitForLoadState('networkidle'); // Verify initial connection await expect(page.locator('[data-testid="connection-status"]')).toHaveText(/connecté|connected/i); // Navigate to different pages await page.goto('/dashboard'); await page.waitForLoadState('networkidle'); await page.goto('/analytics'); await page.waitForLoadState('networkidle'); // Return to chat await page.goto('/chat'); await page.waitForLoadState('networkidle'); // Verify connection is still active await expect(page.locator('[data-testid="connection-status"]')).toHaveText(/connecté|connected/i); // Test sending a message to confirm functionality const testMessage = `Navigation test ${Date.now()}`; await page.fill('[data-testid="message-input"]', testMessage); await page.click('[data-testid="send-button"]'); await expect(page.locator(`text=${testMessage}`)).toBeVisible(); }); }); test.describe('WebSocket Error Handling', () => { test('should handle WebSocket server errors gracefully', async ({ page }) => { await page.goto('/chat'); await page.waitForLoadState('networkidle'); // Mock WebSocket error await page.evaluate(() => { // Simulate WebSocket error window.dispatchEvent(new CustomEvent('websocket-error', { detail: { error: 'Connection failed' } })); }); // Check for error handling await expect(page.locator('[data-testid="connection-error"]')).toBeVisible({ timeout: 5000 }); }); test('should retry connection on failure', async ({ page }) => { await page.goto('/chat'); await page.waitForLoadState('networkidle'); // Simulate connection failure and retry await page.evaluate(() => { window.dispatchEvent(new CustomEvent('websocket-disconnect')); }); // Should show reconnecting status await expect(page.locator('[data-testid="connection-status"]')).toHaveText(/reconnexion|reconnecting/i, { timeout: 5000 }); // Should eventually reconnect await expect(page.locator('[data-testid="connection-status"]')).toHaveText(/connecté|connected/i, { timeout: 15000 }); }); }); });