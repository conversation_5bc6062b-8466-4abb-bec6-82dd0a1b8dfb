import { test, expect } from '@playwright/test'; import path from 'path'; test.describe('File Upload Functionality', () => { test.beforeEach(async ({ page }) => { // Use admin authentication state await page.goto('/login'); await page.fill('[data-testid="email-input"]', '<EMAIL>'); await page.fill('[data-testid="password-input"]', 'TestAdmin123!'); await page.click('[data-testid="login-button"]'); await page.waitForURL('**/dashboard'); }); test.describe('Chat File Upload', () => { test('should upload image files in chat', async ({ page }) => { // Navigate to chat page await page.goto('/chat'); await page.waitForLoadState('networkidle'); // Check if file upload button exists const fileUploadButton = page.locator('[data-testid="file-upload-button"], input[type="file"]'); await expect(fileUploadButton).toBeVisible(); // Create a test image file const testImagePath = path.join(__dirname, 'fixtures', 'test-image.png'); // Upload file await fileUploadButton.setInputFiles(testImagePath); // Verify file is selected and preview is shown await expect(page.locator('[data-testid="file-preview"]')).toBeVisible(); await expect(page.locator('text=test-image.png')).toBeVisible(); // Send message with file await page.fill('[data-testid="message-input"]', 'Test message with image'); await page.click('[data-testid="send-button"]'); // Verify message with attachment is sent await expect(page.locator('[data-testid="message-with-attachment"]')).toBeVisible(); }); test('should validate file size limits', async ({ page }) => { await page.goto('/chat'); await page.waitForLoadState('networkidle'); // Try to upload a large file (simulate by creating a large file) const fileUploadButton = page.locator('input[type="file"]'); // Mock a large file upload await page.evaluate(() => { const input = document.querySelector('input[type="file"]') as HTMLInputElement; if (input) { const file = new File(['x'.repeat(11 * 1024 * 1024)], 'large-file.txt', { type: 'text/plain' }); const dataTransfer = new DataTransfer(); dataTransfer.items.add(file); input.files = dataTransfer.files; input.dispatchEvent(new Event('change', { bubbles: true })); } }); // Verify error message for file too large await expect(page.locator('text=*trop volumineux*')).toBeVisible(); }); test('should validate file types', async ({ page }) => { await page.goto('/chat'); await page.waitForLoadState('networkidle'); // Try to upload an unsupported file type await page.evaluate(() => { const input = document.querySelector('input[type="file"]') as HTMLInputElement; if (input) { const file = new File(['test'], 'test.exe', { type: 'application/x-executable' }); const dataTransfer = new DataTransfer(); dataTransfer.items.add(file); input.files = dataTransfer.files; input.dispatchEvent(new Event('change', { bubbles: true })); } }); // Verify error message for invalid file type await expect(page.locator('text=*type*non supporté*')).toBeVisible(); }); test('should support multiple file uploads', async ({ page }) => { await page.goto('/chat'); await page.waitForLoadState('networkidle'); // Upload multiple files const testFiles = [ path.join(__dirname, 'fixtures', 'test-document.txt'), path.join(__dirname, 'fixtures', 'test-image.png') ]; const fileUploadButton = page.locator('input[type="file"]'); await fileUploadButton.setInputFiles(testFiles); // Verify multiple files are selected await expect(page.locator('[data-testid="file-preview"]')).toHaveCount(2); await expect(page.locator('text=test-document.txt')).toBeVisible(); await expect(page.locator('text=test-image.png')).toBeVisible(); }); test('should allow removing selected files', async ({ page }) => { await page.goto('/chat'); await page.waitForLoadState('networkidle'); // Upload a file const testFilePath = path.join(__dirname, 'fixtures', 'test-document.txt'); const fileUploadButton = page.locator('input[type="file"]'); await fileUploadButton.setInputFiles(testFilePath); // Verify file is selected await expect(page.locator('[data-testid="file-preview"]')).toBeVisible(); // Remove the file await page.click('[data-testid="remove-file-button"]'); // Verify file is removed await expect(page.locator('[data-testid="file-preview"]')).not.toBeVisible(); }); }); test.describe('Support Ticket File Upload', () => { test('should upload files when creating support ticket', async ({ page }) => { // Navigate to support form await page.goto('/support'); await page.waitForLoadState('networkidle'); // Fill support ticket form await page.fill('[data-testid="ticket-title"]', 'Test Ticket with Attachment'); await page.fill('[data-testid="ticket-description"]', 'This is a test ticket with file attachment'); await page.selectOption('[data-testid="ticket-category"]', 'technical'); await page.selectOption('[data-testid="ticket-priority"]', 'medium'); // Upload file const testFilePath = path.join(__dirname, 'fixtures', 'test-document.txt'); const fileUploadButton = page.locator('input[type="file"]'); await fileUploadButton.setInputFiles(testFilePath); // Verify file is attached await expect(page.locator('[data-testid="attached-file"]')).toBeVisible(); await expect(page.locator('text=test-document.txt')).toBeVisible(); // Submit ticket await page.click('[data-testid="submit-ticket-button"]'); // Verify ticket is created with attachment await expect(page.locator('text=*créé avec succès*')).toBeVisible(); }); test('should support drag and drop file upload', async ({ page }) => { await page.goto('/support'); await page.waitForLoadState('networkidle'); // Simulate drag and drop const dropZone = page.locator('[data-testid="file-drop-zone"]'); await expect(dropZone).toBeVisible(); // Create a test file and simulate drop await page.evaluate(() => { const dropZone = document.querySelector('[data-testid="file-drop-zone"]') as HTMLElement; if (dropZone) { const file = new File(['test content'], 'dropped-file.txt', { type: 'text/plain' }); const dataTransfer = new DataTransfer(); dataTransfer.items.add(file); const dropEvent = new DragEvent('drop', { bubbles: true, dataTransfer }); dropZone.dispatchEvent(dropEvent); } }); // Verify file is added await expect(page.locator('text=dropped-file.txt')).toBeVisible(); }); test('should show upload progress', async ({ page }) => { await page.goto('/support'); await page.waitForLoadState('networkidle'); // Upload a larger file to see progress const testFilePath = path.join(__dirname, 'fixtures', 'test-document.txt'); const fileUploadButton = page.locator('input[type="file"]'); await fileUploadButton.setInputFiles(testFilePath); // Check for progress indicator (may be brief) const progressIndicator = page.locator('[data-testid="upload-progress"], .MuiLinearProgress-root'); // The progress might be too fast to catch, so we'll check if the file appears await expect(page.locator('text=test-document.txt')).toBeVisible(); }); }); test.describe('File Upload API Integration', () => { test('should handle upload API errors gracefully', async ({ page }) => { // Mock API to return error await page.route('**/api/upload/**', route => { route.fulfill({ status: 500, contentType: 'application/json', body: JSON.stringify({ success: false, message: 'Upload failed' }) }); }); await page.goto('/chat'); await page.waitForLoadState('networkidle'); // Try to upload file const testFilePath = path.join(__dirname, 'fixtures', 'test-document.txt'); const fileUploadButton = page.locator('input[type="file"]'); await fileUploadButton.setInputFiles(testFilePath); // Send message to trigger upload await page.fill('[data-testid="message-input"]', 'Test message'); await page.click('[data-testid="send-button"]'); // Verify error handling await expect(page.locator('text=*erreur*')).toBeVisible(); }); test('should retry failed uploads', async ({ page }) => { let uploadAttempts = 0; // Mock API to fail first time, succeed second time await page.route('**/api/upload/**', route => { uploadAttempts++; if (uploadAttempts === 1) { route.fulfill({ status: 500, contentType: 'application/json', body: JSON.stringify({ success: false, message: 'Upload failed' }) }); } else { route.fulfill({ status: 200, contentType: 'application/json', body: JSON.stringify({ success: true, files: [{ url: '/uploads/test-file.txt', filename: 'test-file.txt' }] }) }); } }); await page.goto('/chat'); await page.waitForLoadState('networkidle'); // Upload file const testFilePath = path.join(__dirname, 'fixtures', 'test-document.txt'); const fileUploadButton = page.locator('input[type="file"]'); await fileUploadButton.setInputFiles(testFilePath); // Send message await page.fill('[data-testid="message-input"]', 'Test message'); await page.click('[data-testid="send-button"]'); // Look for retry button and click it const retryButton = page.locator('[data-testid="retry-upload-button"]'); if (await retryButton.isVisible()) { await retryButton.click(); } // Verify eventual success await expect(page.locator('[data-testid="message-sent"]')).toBeVisible(); }); }); test.describe('File Upload Security', () => { test('should prevent malicious file uploads', async ({ page }) => { await page.goto('/chat'); await page.waitForLoadState('networkidle'); // Try to upload a potentially malicious file await page.evaluate(() => { const input = document.querySelector('input[type="file"]') as HTMLInputElement; if (input) { const file = new File(['<script>alert("xss")</script>'], 'malicious.html', { type: 'text/html' }); const dataTransfer = new DataTransfer(); dataTransfer.items.add(file); input.files = dataTransfer.files; input.dispatchEvent(new Event('change', { bubbles: true })); } }); // Verify file is rejected await expect(page.locator('text=*type*non supporté*')).toBeVisible(); }); test('should sanitize file names', async ({ page }) => { await page.goto('/chat'); await page.waitForLoadState('networkidle'); // Upload file with special characters in name await page.evaluate(() => { const input = document.querySelector('input[type="file"]') as HTMLInputElement; if (input) { const file = new File(['test'], '../../../etc/passwd', { type: 'text/plain' }); const dataTransfer = new DataTransfer(); dataTransfer.items.add(file); input.files = dataTransfer.files; input.dispatchEvent(new Event('change', { bubbles: true })); } }); // File should be accepted but name should be sanitized await expect(page.locator('[data-testid="file-preview"]')).toBeVisible(); // The displayed name should not contain path traversal characters const fileName = await page.locator('[data-testid="file-name"]').textContent(); expect(fileName).not.toContain('../'); }); }); });