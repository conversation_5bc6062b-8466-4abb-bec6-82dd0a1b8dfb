import { test, expect } from '@playwright/test'; import { TestHelpers } from './utils/test-helpers'; test.describe('Performance Tests', () => { let helpers: TestHelpers; test.beforeEach(async ({ page }) => { helpers = new TestHelpers(page); }); test('should load dashboard within performance budget', async ({ page }) => { const startTime = Date.now(); await page.goto('/dashboard/overview'); await helpers.waitForDashboardLoad(); const loadTime = Date.now() - startTime; // Should load within 3 seconds expect(loadTime).toBeLessThan(3000); console.log(`Dashboard loaded in ${loadTime}ms`); }); test('should have acceptable Core Web Vitals', async ({ page }) => { await page.goto('/dashboard/overview'); // Measure Core Web Vitals const webVitals = await page.evaluate(() => { return new Promise((resolve) => { const vitals: any = {}; // Largest Contentful Paint (LCP) new PerformanceObserver((list) => { const entries = list.getEntries(); const lastEntry = entries[entries.length - 1]; vitals.lcp = lastEntry.startTime; }).observe({ entryTypes: ['largest-contentful-paint'] }); // First Input Delay (FID) - simulated vitals.fid = 0; // Would be measured on actual user interaction // Cumulative Layout Shift (CLS) let clsValue = 0; new PerformanceObserver((list) => { for (const entry of list.getEntries()) { if (!(entry as any).hadRecentInput) { clsValue += (entry as any).value; } } vitals.cls = clsValue; }).observe({ entryTypes: ['layout-shift'] }); // Wait a bit for measurements setTimeout(() => resolve(vitals), 2000); }); }); // Core Web Vitals thresholds expect((webVitals as any).lcp).toBeLessThan(2500); // LCP < 2.5s expect((webVitals as any).cls).toBeLessThan(0.1); // CLS < 0.1 console.log('Core Web Vitals:', webVitals); }); test('should handle concurrent users efficiently', async ({ page, context }) => { // Simulate multiple concurrent sessions const pages = await Promise.all([ context.newPage(), context.newPage(), context.newPage() ]); const startTime = Date.now(); // Load dashboard on all pages simultaneously await Promise.all(pages.map(async (p) => { const helper = new TestHelpers(p); await p.goto('/dashboard/overview'); await helper.waitForDashboardLoad(); })); const totalTime = Date.now() - startTime; // Should handle concurrent loads efficiently expect(totalTime).toBeLessThan(5000); // Clean up await Promise.all(pages.map(p => p.close())); }); test('should have efficient memory usage', async ({ page }) => { await page.goto('/dashboard/overview'); await helpers.waitForDashboardLoad(); // Get initial memory usage const initialMemory = await page.evaluate(() => { return (performance as any).memory ? { usedJSHeapSize: (performance as any).memory.usedJSHeapSize, totalJSHeapSize: (performance as any).memory.totalJSHeapSize } : null; }); // Navigate through different sections await helpers.navigateToSection('support-form'); await helpers.navigateToSection('admin'); await helpers.navigateToSection('analytics'); await helpers.navigateToSection('overview'); // Check memory after navigation const finalMemory = await page.evaluate(() => { return (performance as any).memory ? { usedJSHeapSize: (performance as any).memory.usedJSHeapSize, totalJSHeapSize: (performance as any).memory.totalJSHeapSize } : null; }); if (initialMemory && finalMemory) { const memoryIncrease = finalMemory.usedJSHeapSize - initialMemory.usedJSHeapSize; // Memory increase should be reasonable (less than 10MB) expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024); console.log(`Memory usage: ${initialMemory.usedJSHeapSize} -> ${finalMemory.usedJSHeapSize} (${memoryIncrease} bytes increase)`); } }); test('should handle large datasets efficiently', async ({ page }) => { // Mock large dataset const largeDataset = Array.from({ length: 1000 }, (_, i) => ({ id: `client-${i}`, name: `Client ${i}`, email: `client${i}@example.com`, status: i % 3 === 0 ? 'Actif' : 'Inactif', tickets: Math.floor(Math.random() * 50) })); await helpers.mockApiResponse(/\/api\/clients/, largeDataset); const startTime = Date.now(); await page.goto('/dashboard/admin/clients'); await helpers.waitForDashboardLoad(); const renderTime = Date.now() - startTime; // Should render large dataset within reasonable time expect(renderTime).toBeLessThan(2000); // Check that pagination is working const pagination = page.locator('.MuiTablePagination-root'); if (await pagination.count() > 0) { await expect(pagination).toBeVisible(); // Test pagination performance const paginationStartTime = Date.now(); const nextButton = page.getByRole('button', { name: /next/i }); if (await nextButton.isEnabled()) { await nextButton.click(); await page.waitForTimeout(100); } const paginationTime = Date.now() - paginationStartTime; expect(paginationTime).toBeLessThan(500); } }); test('should have fast form interactions', async ({ page }) => { await page.goto('/dashboard/support-form'); await helpers.waitForDashboardLoad(); // Measure form field response time const nameField = page.getByLabel('Nom complet'); const startTime = Date.now(); await nameField.fill('Test User'); const responseTime = Date.now() - startTime; // Form should respond immediately expect(responseTime).toBeLessThan(100); // Test dropdown performance const categoryField = page.getByLabel('Catégorie du problème'); const dropdownStartTime = Date.now(); await categoryField.click(); await expect(page.getByRole('option', { name: 'Problème technique' })).toBeVisible(); const dropdownTime = Date.now() - dropdownStartTime; expect(dropdownTime).toBeLessThan(300); }); test('should handle network throttling gracefully', async ({ page }) => { // Simulate slow 3G connection await page.route('**/*', async (route) => { await new Promise(resolve => setTimeout(resolve, 100)); // Add 100ms delay await route.continue(); }); const startTime = Date.now(); await page.goto('/dashboard/overview'); await helpers.waitForDashboardLoad(); const loadTime = Date.now() - startTime; // Should still load within reasonable time on slow connection expect(loadTime).toBeLessThan(10000); // Check that loading states are shown // This would depend on implementation }); test('should have efficient bundle sizes', async ({ page }) => { // Monitor network requests const requests: any[] = []; page.on('request', request => { if (request.url().includes('.js') || request.url().includes('.css')) { requests.push({ url: request.url(), resourceType: request.resourceType() }); } }); await page.goto('/dashboard/overview'); await helpers.waitForDashboardLoad(); // Check JavaScript bundle sizes const jsRequests = requests.filter(r => r.url.includes('.js')); const cssRequests = requests.filter(r => r.url.includes('.css')); console.log(`Loaded ${jsRequests.length} JS files and ${cssRequests.length} CSS files`); // Should not load excessive number of files expect(jsRequests.length).toBeLessThan(20); expect(cssRequests.length).toBeLessThan(10); }); test('should handle rapid navigation efficiently', async ({ page }) => { await page.goto('/dashboard/overview'); await helpers.waitForDashboardLoad(); const navigationTimes: number[] = []; // Rapidly navigate between sections const sections = ['support-form', 'admin', 'analytics', 'overview']; for (const section of sections) { const startTime = Date.now(); await helpers.navigateToSection(section as any); const navTime = Date.now() - startTime; navigationTimes.push(navTime); } // Each navigation should be fast navigationTimes.forEach(time => { expect(time).toBeLessThan(1000); }); const averageTime = navigationTimes.reduce((a, b) => a + b, 0) / navigationTimes.length; console.log(`Average navigation time: ${averageTime}ms`); expect(averageTime).toBeLessThan(500); }); test('should handle chat widget performance', async ({ page }) => { await page.goto('/dashboard/support-form'); await helpers.waitForDashboardLoad(); const messageInput = page.locator('textarea[placeholder*="Tapez votre message"]'); const sendButton = page.getByRole('button', { name: /send/i }).last(); // Send multiple messages rapidly const messageTimes: number[] = []; for (let i = 1; i <= 10; i++) { const startTime = Date.now(); await messageInput.fill(`Message ${i}`); await sendButton.click(); // Wait for message to appear await expect(page.getByText(`Message ${i}`)).toBeVisible(); const messageTime = Date.now() - startTime; messageTimes.push(messageTime); await page.waitForTimeout(100); // Small delay between messages } // Each message should be processed quickly messageTimes.forEach(time => { expect(time).toBeLessThan(1000); }); const averageMessageTime = messageTimes.reduce((a, b) => a + b, 0) / messageTimes.length; console.log(`Average message processing time: ${averageMessageTime}ms`); }); test('should maintain performance with multiple components', async ({ page }) => { await page.goto('/dashboard/admin'); await helpers.waitForDashboardLoad(); // Measure rendering time with multiple components const startTime = Date.now(); // Navigate to clients page (has table + profile panel) await page.getByText('Clients').click(); await expect(page.getByText('Gestion des Clients')).toBeVisible(); const renderTime = Date.now() - startTime; // Should render complex page quickly expect(renderTime).toBeLessThan(1500); // Test interaction performance const searchInput = page.getByPlaceholder('Rechercher...'); const searchStartTime = Date.now(); await searchInput.fill('Jean'); await page.waitForTimeout(300); // Wait for debounced search const searchTime = Date.now() - searchStartTime; expect(searchTime).toBeLessThan(500); }); });