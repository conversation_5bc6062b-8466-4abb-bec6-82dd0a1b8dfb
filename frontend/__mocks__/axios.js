// Mock axios for Jest tests - placed in __mocks__ directory for automatic mocking const mockAxios = jest.fn(() => Promise.resolve({ data: {} })); // Add all HTTP methods mockAxios.get = jest.fn(() => Promise.resolve({ data: {} })); mockAxios.post = jest.fn(() => Promise.resolve({ data: {} })); mockAxios.put = jest.fn(() => Promise.resolve({ data: {} })); mockAxios.delete = jest.fn(() => Promise.resolve({ data: {} })); mockAxios.patch = jest.fn(() => Promise.resolve({ data: {} })); mockAxios.head = jest.fn(() => Promise.resolve({ data: {} })); mockAxios.options = jest.fn(() => Promise.resolve({ data: {} })); mockAxios.request = jest.fn(() => Promise.resolve({ data: {} })); // Add create method mockAxios.create = jest.fn(() => ({ get: jest.fn(() => Promise.resolve({ data: {} })), post: jest.fn(() => Promise.resolve({ data: {} })), put: jest.fn(() => Promise.resolve({ data: {} })), delete: jest.fn(() => Promise.resolve({ data: {} })), patch: jest.fn(() => Promise.resolve({ data: {} })), head: jest.fn(() => Promise.resolve({ data: {} })), options: jest.fn(() => Promise.resolve({ data: {} })), request: jest.fn(() => Promise.resolve({ data: {} })), interceptors: { request: { use: jest.fn(), eject: jest.fn(), clear: jest.fn(), }, response: { use: jest.fn(), eject: jest.fn(), clear: jest.fn(), }, }, defaults: { headers: { common: {}, delete: {}, get: {}, head: {}, post: {}, put: {}, patch: {}, }, timeout: 0, baseURL: '', }, })); // Add interceptors mockAxios.interceptors = { request: { use: jest.fn(), eject: jest.fn(), clear: jest.fn(), }, response: { use: jest.fn(), eject: jest.fn(), clear: jest.fn(), }, }; // Add defaults mockAxios.defaults = { headers: { common: {}, delete: {}, get: {}, head: {}, post: {}, put: {}, patch: {}, }, timeout: 0, baseURL: '', transformRequest: [], transformResponse: [], paramsSerializer: null, adapter: null, responseType: 'json', xsrfCookieName: 'XSRF-TOKEN', xsrfHeaderName: 'X-XSRF-TOKEN', maxContentLength: -1, maxBodyLength: -1, validateStatus: jest.fn(() => true), }; // Add utility functions and properties mockAxios.isAxiosError = jest.fn(() => false); mockAxios.CancelToken = { source: jest.fn(() => ({ token: {}, cancel: jest.fn(), })), }; mockAxios.Cancel = jest.fn(); mockAxios.isCancel = jest.fn(() => false); // Add all axios methods and properties to the mock mockAxios.Axios = jest.fn(); mockAxios.AxiosError = jest.fn(); mockAxios.AxiosHeaders = jest.fn(); mockAxios.formToJSON = jest.fn(); mockAxios.getAdapter = jest.fn(); mockAxios.HttpStatusCode = { Continue: 100, SwitchingProtocols: 101, Processing: 102, EarlyHints: 103, Ok: 200, Created: 201, Accepted: 202, NonAuthoritativeInformation: 203, NoContent: 204, ResetContent: 205, PartialContent: 206, MultiStatus: 207, AlreadyReported: 208, ImUsed: 226, MultipleChoices: 300, MovedPermanently: 301, Found: 302, SeeOther: 303, NotModified: 304, UseProxy: 305, Unused: 306, TemporaryRedirect: 307, PermanentRedirect: 308, BadRequest: 400, Unauthorized: 401, PaymentRequired: 402, Forbidden: 403, NotFound: 404, MethodNotAllowed: 405, NotAcceptable: 406, ProxyAuthenticationRequired: 407, RequestTimeout: 408, Conflict: 409, Gone: 410, LengthRequired: 411, PreconditionFailed: 412, PayloadTooLarge: 413, UriTooLong: 414, UnsupportedMediaType: 415, RangeNotSatisfiable: 416, ExpectationFailed: 417, ImATeapot: 418, MisdirectedRequest: 421, UnprocessableEntity: 422, Locked: 423, FailedDependency: 424, TooEarly: 425, UpgradeRequired: 426, PreconditionRequired: 428, TooManyRequests: 429, RequestHeaderFieldsTooLarge: 431, UnavailableForLegalReasons: 451, InternalServerError: 500, NotImplemented: 501, BadGateway: 502, ServiceUnavailable: 503, GatewayTimeout: 504, HttpVersionNotSupported: 505, VariantAlsoNegotiates: 506, InsufficientStorage: 507, LoopDetected: 508, NotExtended: 510, NetworkAuthenticationRequired: 511, }; mockAxios.mergeConfig = jest.fn(); mockAxios.toFormData = jest.fn(); mockAxios.VERSION = '1.0.0'; // Support both CommonJS and ES module imports module.exports = mockAxios; module.exports.default = mockAxios; // For ES module compatibility - this is crucial for newer axios versions Object.defineProperty(module.exports, '__esModule', { value: true }); module.exports.default = mockAxios;