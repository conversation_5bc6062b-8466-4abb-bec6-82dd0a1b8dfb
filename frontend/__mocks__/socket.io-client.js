// Mock for socket.io-client to prevent ES module issues in Jest const mockSocket = { on: jest.fn(), off: jest.fn(), emit: jest.fn(), connect: jest.fn(), disconnect: jest.fn(), connected: false, id: 'mock-socket-id', }; const mockIo = jest.fn(() => mockSocket); // Support both CommonJS and ES module imports module.exports = { io: mockIo, Socket: jest.fn(), default: mockIo, }; // For ES module compatibility Object.defineProperty(module.exports, '__esModule', { value: true }); module.exports.default = mockIo;