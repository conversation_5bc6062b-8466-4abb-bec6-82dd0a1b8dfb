import { defineConfig, devices } from '@playwright/test'; export default defineConfig({ testDir: './tests', fullyParallel: false, forbidOnly: !!process.env.CI, retries: 0, workers: 1, reporter: [ ['list'] ], use: { baseURL: 'http://localhost:51692', trace: 'on', screenshot: 'on', video: 'on', actionTimeout: 10000, navigationTimeout: 10000, }, projects: [ { name: 'chromium', use: { ...devices['Desktop Chrome'] }, }, ], // No webServer configuration - use existing running servers });