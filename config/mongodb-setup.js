/** * MongoDB Database Setup Script for Free Mobile Chatbot ML Intelligence Dashboard * Run this script to initialize the production database with all required collections and indexes */ const { MongoClient, ServerApiVersion } = require('mongodb'); // Production MongoDB Atlas connection const MONGODB_URI = "mongodb+srv://Anderson-Archimed01:<EMAIL>/?retryWrites=true&w=majority&appName=ChatbotRNCP"; const DATABASE_NAME = "freemobile_chatbot_prod"; const client = new MongoClient(MONGODB_URI, { serverApi: { version: ServerApiVersion.v1, strict: true, deprecationErrors: true, }, maxPoolSize: 10, serverSelectionTimeoutMS: 5000, socketTimeoutMS: 45000, }); async function setupDatabase() { console.log('[DEPLOY] Starting MongoDB database setup for Free Mobile Chatbot ML Intelligence Dashboard...'); try { await client.connect(); console.log('[COMPLETE] Connected to MongoDB Atlas'); const db = client.db(DATABASE_NAME); // Create collections and indexes await createUsersCollection(db); await createSimulationCollections(db); await createPredictiveAnalyticsCollections(db); await createAIAssistanceCollections(db); await createAnalyticsCollections(db); await createSystemCollections(db); console.log(' Database setup completed successfully!'); } catch (error) { console.error('[FAILED] Database setup failed:', error); throw error; } finally { await client.close(); } } async function createUsersCollection(db) { console.log(' Setting up Users collection...'); const users = db.collection('users'); // Create indexes await users.createIndex({ email: 1 }, { unique: true }); await users.createIndex({ role: 1 }); await users.createIndex({ department: 1 }); await users.createIndex({ "skills.overall": -1 }); await users.createIndex({ lastLoginAt: -1 }); await users.createIndex({ createdAt: -1 }); console.log('[COMPLETE] Users collection configured'); } async function createSimulationCollections(db) { console.log(' Setting up Simulation Training collections...'); // Simulation Scenarios const scenarios = db.collection('simulation_scenarios'); await scenarios.createIndex({ category: 1 }); await scenarios.createIndex({ difficulty: 1 }); await scenarios.createIndex({ tags: 1 }); await scenarios.createIndex({ isActive: 1 }); await scenarios.createIndex({ createdAt: -1 }); // Simulation Sessions const sessions = db.collection('simulation_sessions'); await sessions.createIndex({ agentId: 1, createdAt: -1 }); await sessions.createIndex({ scenarioId: 1 }); await sessions.createIndex({ status: 1 }); await sessions.createIndex({ completedAt: -1 }); await sessions.createIndex({ "scores.overall": -1 }); // Agent Progress const progress = db.collection('agent_progress'); await progress.createIndex({ agentId: 1 }, { unique: true }); await progress.createIndex({ "skills.empathy": -1 }); await progress.createIndex({ "skills.efficiency": -1 }); await progress.createIndex({ totalSessions: -1 }); await progress.createIndex({ currentStreak: -1 }); // Gamification const gamification = db.collection('gamification'); await gamification.createIndex({ agentId: 1 }); await gamification.createIndex({ type: 1 }); await gamification.createIndex({ earnedAt: -1 }); await gamification.createIndex({ points: -1 }); console.log('[COMPLETE] Simulation Training collections configured'); } async function createPredictiveAnalyticsCollections(db) { console.log(' Setting up Predictive Analytics collections...'); // Churn Predictions const churnPredictions = db.collection('churn_predictions'); await churnPredictions.createIndex({ customerId: 1, predictedAt: -1 }); await churnPredictions.createIndex({ riskLevel: 1 }); await churnPredictions.createIndex({ probability: -1 }); await churnPredictions.createIndex({ predictedAt: -1 }); // Demand Forecasts const demandForecasts = db.collection('demand_forecasts'); await demandForecasts.createIndex({ forecastDate: 1 }); await demandForecasts.createIndex({ timeSlot: 1 }); await demandForecasts.createIndex({ createdAt: -1 }); // Escalation Risks const escalationRisks = db.collection('escalation_risks'); await escalationRisks.createIndex({ ticketId: 1 }, { unique: true }); await escalationRisks.createIndex({ riskLevel: 1 }); await escalationRisks.createIndex({ agentId: 1 }); await escalationRisks.createIndex({ predictedAt: -1 }); // Anomaly Detection const anomalies = db.collection('anomalies'); await anomalies.createIndex({ detectedAt: -1 }); await anomalies.createIndex({ severity: 1 }); await anomalies.createIndex({ type: 1 }); await anomalies.createIndex({ resolved: 1 }); console.log('[COMPLETE] Predictive Analytics collections configured'); } async function createAIAssistanceCollections(db) { console.log('[FEATURE] Setting up AI Assistance collections...'); // AI Suggestions const suggestions = db.collection('ai_suggestions'); await suggestions.createIndex({ conversationId: 1, createdAt: -1 }); await suggestions.createIndex({ agentId: 1 }); await suggestions.createIndex({ confidence: -1 }); await suggestions.createIndex({ category: 1 }); await suggestions.createIndex({ used: 1 }); // Sentiment Analysis const sentimentAnalysis = db.collection('sentiment_analysis'); await sentimentAnalysis.createIndex({ conversationId: 1, timestamp: -1 }); await sentimentAnalysis.createIndex({ agentId: 1 }); await sentimentAnalysis.createIndex({ overallSentiment: 1 }); await sentimentAnalysis.createIndex({ timestamp: -1 }); // Agent Personalization const personalization = db.collection('agent_personalization'); await personalization.createIndex({ agentId: 1 }, { unique: true }); await personalization.createIndex({ "preferences.communicationStyle": 1 }); await personalization.createIndex({ lastUpdated: -1 }); // Knowledge Base Interactions const kbInteractions = db.collection('kb_interactions'); await kbInteractions.createIndex({ agentId: 1, timestamp: -1 }); await kbInteractions.createIndex({ articleId: 1 }); await kbInteractions.createIndex({ relevanceScore: -1 }); console.log('[COMPLETE] AI Assistance collections configured'); } async function createAnalyticsCollections(db) { console.log('[ANALYTICS] Setting up Analytics collections...'); // Performance Metrics const performanceMetrics = db.collection('performance_metrics'); await performanceMetrics.createIndex({ agentId: 1, date: -1 }); await performanceMetrics.createIndex({ department: 1, date: -1 }); await performanceMetrics.createIndex({ date: -1 }); await performanceMetrics.createIndex({ "metrics.customerSatisfaction": -1 }); // Real-time KPIs const realtimeKpis = db.collection('realtime_kpis'); await realtimeKpis.createIndex({ timestamp: -1 }); await realtimeKpis.createIndex({ metricType: 1, timestamp: -1 }); await realtimeKpis.createIndex({ agentId: 1, timestamp: -1 }); // Dashboard Configurations const dashboardConfigs = db.collection('dashboard_configs'); await dashboardConfigs.createIndex({ userId: 1 }); await dashboardConfigs.createIndex({ dashboardType: 1 }); await dashboardConfigs.createIndex({ isDefault: 1 }); // Report Templates const reportTemplates = db.collection('report_templates'); await reportTemplates.createIndex({ createdBy: 1 }); await reportTemplates.createIndex({ category: 1 }); await reportTemplates.createIndex({ isPublic: 1 }); console.log('[COMPLETE] Analytics collections configured'); } async function createSystemCollections(db) { console.log(' Setting up System collections...'); // System Logs const systemLogs = db.collection('system_logs'); await systemLogs.createIndex({ timestamp: -1 }); await systemLogs.createIndex({ level: 1, timestamp: -1 }); await systemLogs.createIndex({ service: 1, timestamp: -1 }); // API Usage const apiUsage = db.collection('api_usage'); await apiUsage.createIndex({ endpoint: 1, timestamp: -1 }); await apiUsage.createIndex({ userId: 1, timestamp: -1 }); await apiUsage.createIndex({ timestamp: -1 }); // System Health const systemHealth = db.collection('system_health'); await systemHealth.createIndex({ timestamp: -1 }); await systemHealth.createIndex({ service: 1, timestamp: -1 }); await systemHealth.createIndex({ status: 1 }); console.log('[COMPLETE] System collections configured'); } // Run the setup if (require.main === module) { setupDatabase() .then(() => { console.log(' MongoDB setup completed successfully!'); process.exit(0); }) .catch((error) => { console.error('[FAILED] Setup failed:', error); process.exit(1); }); } module.exports = { setupDatabase };