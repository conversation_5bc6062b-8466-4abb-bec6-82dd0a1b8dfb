const { defineConfig } = require('@playwright/test'); module.exports = defineConfig({ testDir: './tests', testMatch: '**/frontend-validation.test.js', // Test de validation complet fullyParallel: false, forbidOnly: !!process.env.CI, retries: process.env.CI ? 2 : 0, workers: 1, reporter: [ ['html', { open: 'never', outputFolder: 'playwright-report-frontend' }], ['json', { outputFile: 'test-results-frontend.json' }], ['list'] ], timeout: 60000, expect: { timeout: 15000 }, use: { baseURL: 'http://localhost:3000', trace: 'on-first-retry', screenshot: 'only-on-failure', video: 'retain-on-failure', extraHTTPHeaders: { 'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8', 'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8', } }, projects: [ { name: 'chromium', use: { ...require('@playwright/test').devices['Desktop Chrome'], viewport: { width: 1280, height: 720 } }, }, ], });