module.exports = { ci: { collect: { url: [ 'http://localhost:3001/dashboard/overview', 'http://localhost:3001/dashboard/support-form', 'http://localhost:3001/dashboard/admin', 'http://localhost:3001/dashboard/analytics' ], startServerCommand: 'npm run start', startServerReadyPattern: 'webpack compiled', startServerReadyTimeout: 60000, }, assert: { assertions: { 'categories:performance': ['warn', { minScore: 0.8 }], 'categories:accessibility': ['error', { minScore: 0.9 }], 'categories:best-practices': ['warn', { minScore: 0.8 }], 'categories:seo': ['warn', { minScore: 0.8 }], 'categories:pwa': 'off', // Performance budgets 'first-contentful-paint': ['warn', { maxNumericValue: 2000 }], 'largest-contentful-paint': ['warn', { maxNumericValue: 2500 }], 'cumulative-layout-shift': ['error', { maxNumericValue: 0.1 }], 'total-blocking-time': ['warn', { maxNumericValue: 300 }], // Accessibility requirements 'color-contrast': 'error', 'heading-order': 'error', 'html-has-lang': 'error', 'image-alt': 'error', 'label': 'error', 'link-name': 'error', // Best practices 'uses-https': 'off', // Disabled for local testing 'is-on-https': 'off', // Disabled for local testing 'uses-http2': 'off', 'no-vulnerable-libraries': 'warn', // SEO 'document-title': 'error', 'meta-description': 'warn', 'viewport': 'error', } }, upload: { target: 'temporary-public-storage', }, }, };