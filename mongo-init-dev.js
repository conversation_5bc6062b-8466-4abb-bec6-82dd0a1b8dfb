// Script d'initialisation MongoDB - Développement // Ce script configure la base de données pour l'environnement de développement // Configuration de la base de données const dbName = 'freemobile_chatbot'; const username = 'freemobile'; const password = 'freemobile123'; // Connexion à la base de données const db = db.getSiblingDB(dbName); // Création de l'utilisateur de l'application db.createUser({ user: username, pwd: password, roles: [ { role: 'readWrite', db: dbName }, { role: 'dbAdmin', db: dbName } ] }); print('[COMPLETE] Utilisateur créé: ' + username); // Création des collections avec validation db.createCollection('users', { validator: { $jsonSchema: { bsonType: 'object', required: ['email', 'password', 'role'], properties: { email: { bsonType: 'string', pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$' }, password: { bsonType: 'string', minLength: 6 }, role: { bsonType: 'string', enum: ['user', 'agent', 'admin'] } } } } }); db.createCollection('conversations', { validator: { $jsonSchema: { bsonType: 'object', required: ['sessionId', 'status', 'channel', 'startedAt'], properties: { sessionId: { bsonType: 'string' }, status: { bsonType: 'string', enum: ['active', 'resolved', 'escalated', 'abandoned'] }, channel: { bsonType: 'string', enum: ['web', 'mobile', 'voice'] } } } } }); db.createCollection('messages', { validator: { $jsonSchema: { bsonType: 'object', required: ['conversationId', 'sender', 'content', 'timestamp'], properties: { conversationId: { bsonType: 'objectId' }, sender: { bsonType: 'string', enum: ['user', 'bot', 'agent'] }, content: { bsonType: 'object', required: ['text', 'type'], properties: { text: { bsonType: 'string' }, type: { bsonType: 'string', enum: ['text', 'button', 'card', 'image', 'file'] } } } } } } }); print('[COMPLETE] Collections créées avec validation'); // Création des index pour les performances db.users.createIndex({ 'email': 1 }, { unique: true }); db.users.createIndex({ 'role': 1 }); db.users.createIndex({ 'createdAt': 1 }); db.conversations.createIndex({ 'sessionId': 1 }, { unique: true }); db.conversations.createIndex({ 'userId': 1 }); db.conversations.createIndex({ 'status': 1 }); db.conversations.createIndex({ 'startedAt': -1 }); db.conversations.createIndex({ 'agentId': 1 }); db.messages.createIndex({ 'conversationId': 1 }); db.messages.createIndex({ 'timestamp': -1 }); db.messages.createIndex({ 'sender': 1 }); print('[COMPLETE] Index créés pour les performances'); // Insertion de données de test pour le développement const adminUser = { email: '<EMAIL>', password: '$2a$10$rOZ8vPQvgOV7Lv9X8.2HFuK8c2YZOIr8.Pf9K1L2E3T4U5I6O7P8Q9', // password: admin123 role: 'admin', profile: { firstName: 'Admin', lastName: 'Free Mobile', phoneNumber: '+33123456789' }, createdAt: new Date(), lastLogin: new Date() }; const agentUser = { email: '<EMAIL>', password: '$2a$10$rOZ8vPQvgOV7Lv9X8.2HFuK8c2YZOIr8.Pf9K1L2E3T4U5I6O7P8Q9', // password: agent123 role: 'agent', profile: { firstName: 'Agent', lastName: 'Support', phoneNumber: '+33123456788' }, createdAt: new Date(), lastLogin: new Date() }; const testUser = { email: '<EMAIL>', password: '$2a$10$rOZ8vPQvgOV7Lv9X8.2HFuK8c2YZOIr8.Pf9K1L2E3T4U5I6O7P8Q9', // password: test123 role: 'user', profile: { firstName: 'Test', lastName: 'User', phoneNumber: '+33123456787', customerId: 'FREE001' }, createdAt: new Date(), lastLogin: new Date() }; // Insertion des utilisateurs de test try { db.users.insertOne(adminUser); print('[COMPLETE] Utilisateur admin créé'); } catch (e) { print(' Utilisateur admin existe déjà'); } try { db.users.insertOne(agentUser); print('[COMPLETE] Utilisateur agent créé'); } catch (e) { print(' Utilisateur agent existe déjà'); } try { db.users.insertOne(testUser); print('[COMPLETE] Utilisateur test créé'); } catch (e) { print(' Utilisateur test existe déjà'); } // Création d'une conversation de test const testConversation = { userId: testUser._id, sessionId: 'dev-session-001', status: 'active', channel: 'web', startedAt: new Date(), metadata: { userAgent: 'Mozilla/5.0 (Development)', ipAddress: '127.0.0.1' } }; const conversationResult = db.conversations.insertOne(testConversation); print('[COMPLETE] Conversation de test créée'); // Messages de test const testMessages = [ { conversationId: conversationResult.insertedId, sender: 'bot', content: { text: 'Bonjour ! Je suis votre assistant Free Mobile. Comment puis-je vous aider ?', type: 'text' }, timestamp: new Date(Date.now() - 60000), intent: { name: 'greet', confidence: 0.99 } }, { conversationId: conversationResult.insertedId, sender: 'user', content: { text: 'Bonjour, j\'ai un problème avec ma facture', type: 'text' }, timestamp: new Date(Date.now() - 30000) }, { conversationId: conversationResult.insertedId, sender: 'bot', content: { text: 'Je comprends votre préoccupation concernant votre facture. Pouvez-vous me préciser quel est le problème ?', type: 'text' }, timestamp: new Date(), intent: { name: 'billing_issue', confidence: 0.85 } } ]; db.messages.insertMany(testMessages); print('[COMPLETE] Messages de test créés'); // Configuration des paramètres de la base db.adminCommand('setParameter', { logLevel: 1 }); print(' Initialisation MongoDB terminée avec succès !'); print(''); print(' Comptes de test créés :'); print(' Admin: <EMAIL> / admin123'); print(' Agent: <EMAIL> / agent123'); print(' User: <EMAIL> / test123'); print(''); print(' Connexion: *********************************************************************');