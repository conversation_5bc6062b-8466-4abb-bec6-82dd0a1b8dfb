#!/bin/bash

echo "🚀 Démarrage du Chatbot Free Mobile..."
echo "======================================"

# Vérifier Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker n'est pas installé. Veuillez installer Docker Desktop."
    exit 1
fi

# Vérifier Docker Compose
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose n'est pas installé."
    exit 1
fi

# Démarrer les services Docker
echo "📦 Démarrage des services Docker..."
docker-compose up -d

# Attendre que les services soient prêts
echo "⏳ Attente du démarrage des services..."
sleep 10

# Vérifier MongoDB
echo "🔍 Vérification de MongoDB..."
docker exec free-mobile-mongodb mongo --eval "db.version()" > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ MongoDB est prêt"
else
    echo "❌ MongoDB n'est pas prêt"
fi

# Vérifier Redis
echo "🔍 Vérification de Redis..."
docker exec free-mobile-redis redis-cli ping > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ Redis est prêt"
else
    echo "❌ Redis n'est pas prêt"
fi

# Installer les dépendances backend
echo "📦 Installation des dépendances backend..."
cd backend
npm install

# Démarrer le backend
echo "🚀 Démarrage du backend..."
npm run dev &
BACKEND_PID=$!

cd ..

# Instructions pour le frontend
echo ""
echo "======================================"
echo "✅ Backend démarré avec succès!"
echo ""
echo "📋 Services disponibles:"
echo "   - Backend API: http://localhost:5000"
echo "   - MongoDB: localhost:27017"
echo "   - Redis: localhost:6379"
echo "   - Rasa: http://localhost:5005"
echo ""
echo "👤 Compte administrateur:"
echo "   - Email: <EMAIL>"
echo "   - Password: Password131"
echo ""
echo "📝 Pour démarrer le frontend:"
echo "   cd frontend"
echo "   npm install"
echo "   npm start"
echo ""
echo "🛑 Pour arrêter tous les services:"
echo "   ./stop-all.sh"
echo "======================================"

# Garder le script en cours d'exécution
wait $BACKEND_PID 