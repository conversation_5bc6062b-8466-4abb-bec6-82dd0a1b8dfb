/** * ============================================= * PLAYWRIGHT EMERGENCY CALL TEST CONFIGURATION * Specialized configuration for emergency call E2E testing * Includes cross-browser, accessibility, and performance testing * ============================================= */ import { defineConfig, devices } from '@playwright/test'; export default defineConfig({ testDir: './tests', testMatch: '**/emergency-call-e2e.spec.ts', /* Run tests in files in parallel */ fullyParallel: true, /* Fail the build on CI if you accidentally left test.only in the source code. */ forbidOnly: !!process.env.CI, /* Retry on CI only */ retries: process.env.CI ? 2 : 0, /* Opt out of parallel tests on CI. */ workers: process.env.CI ? 1 : undefined, /* Reporter to use. See https://playwright.dev/docs/test-reporters */ reporter: [ ['html', { outputFolder: 'test-results/emergency-call-report' }], ['json', { outputFile: 'test-results/emergency-call-results.json' }], ['junit', { outputFile: 'test-results/emergency-call-junit.xml' }], ['list'] ], /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */ use: { /* Base URL to use in actions like `await page.goto('/')`. */ baseURL: process.env.PLAYWRIGHT_BASE_URL || 'http://localhost:3001', /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */ trace: 'on-first-retry', /* Take screenshot on failure */ screenshot: 'only-on-failure', /* Record video on failure */ video: 'retain-on-failure', /* Global timeout for each test */ actionTimeout: 30000, navigationTimeout: 30000, /* Ignore HTTPS errors for local testing */ ignoreHTTPSErrors: true, /* Extra HTTP headers */ extraHTTPHeaders: { 'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8' } }, /* Configure projects for major browsers */ projects: [ { name: 'chromium-desktop', use: { ...devices['Desktop Chrome'], viewport: { width: 1920, height: 1080 } }, }, { name: 'firefox-desktop', use: { ...devices['Desktop Firefox'], viewport: { width: 1920, height: 1080 } }, }, { name: 'webkit-desktop', use: { ...devices['Desktop Safari'], viewport: { width: 1920, height: 1080 } }, }, /* Test against mobile viewports. */ { name: 'mobile-chrome', use: { ...devices['Pixel 5'] }, }, { name: 'mobile-safari', use: { ...devices['iPhone 12'] }, }, /* Test against branded browsers. */ { name: 'microsoft-edge', use: { ...devices['Desktop Edge'], channel: 'msedge', viewport: { width: 1920, height: 1080 } }, }, { name: 'google-chrome', use: { ...devices['Desktop Chrome'], channel: 'chrome', viewport: { width: 1920, height: 1080 } }, }, /* Accessibility testing project */ { name: 'accessibility-testing', use: { ...devices['Desktop Chrome'], viewport: { width: 1920, height: 1080 }, // Enable accessibility tree in DevTools launchOptions: { args: ['--enable-accessibility-object-model'] } }, testMatch: '**/emergency-call-e2e.spec.ts', grep: /accessibility|keyboard|WCAG/ }, /* Performance testing project */ { name: 'performance-testing', use: { ...devices['Desktop Chrome'], viewport: { width: 1920, height: 1080 }, // Enable performance metrics launchOptions: { args: ['--enable-precise-memory-info'] } }, testMatch: '**/emergency-call-e2e.spec.ts', grep: /performance|response time|concurrent/ }, /* Security testing project */ { name: 'security-testing', use: { ...devices['Desktop Chrome'], viewport: { width: 1920, height: 1080 } }, testMatch: '**/emergency-call-e2e.spec.ts', grep: /security|rate limiting|XSS|authentication/ } ], /* Global setup and teardown */ globalSetup: require.resolve('./tests/global-setup.ts'), globalTeardown: require.resolve('./tests/global-teardown.ts'), /* Run your local dev server before starting the tests */ webServer: [ { command: 'npm run dev', port: 3001, timeout: 120 * 1000, reuseExistingServer: !process.env.CI, cwd: './frontend' }, { command: 'npm run dev', port: 5000, timeout: 120 * 1000, reuseExistingServer: !process.env.CI, cwd: './backend' } ], /* Test timeout */ timeout: 60000, /* Expect timeout */ expect: { timeout: 10000 }, /* Output directory */ outputDir: 'test-results/emergency-call-artifacts', /* Test metadata */ metadata: { testSuite: 'Emergency Call Feature E2E Tests', version: '1.0.0', environment: process.env.NODE_ENV || 'test', compliance: { wcag: '2.1 AA', gdpr: 'compliant', frenchTelecom: 'ARCEP-2024' }, features: [ 'Emergency Call Button', 'AI Urgency Assessment', 'Queue Management', 'Agent Integration', 'WebRTC Emergency Calls', 'Security & Rate Limiting', 'Accessibility Compliance', 'Cross-browser Support' ] } });