{"name": "freemobile-call-service", "version": "1.0.0", "description": "Bidirectional Call System for Free Mobile Chatbot - Twilio & WebRTC Integration", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["call-service", "twi<PERSON>", "webrtc", "voip", "telephony", "free-mobile", "chatbot"], "author": "Free Mobile DevOps Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "twilio": "^4.19.0", "socket.io": "^4.7.4", "redis": "^4.6.10", "mongodb": "^6.3.0", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "joi": "^17.11.0", "winston": "^3.11.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "uuid": "^9.0.1", "moment": "^2.29.4", "node-cron": "^3.0.3"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.54.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/freemobile/chatbot-dashboard.git", "directory": "call-service"}}