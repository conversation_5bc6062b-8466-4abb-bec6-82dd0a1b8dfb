/** * ============================================= * LOGGER UTILITY * Winston-based logging configuration for Call Service * Supports console and file logging with different levels * ============================================= */ const winston = require('winston'); const path = require('path'); // Define log levels const logLevels = { error: 0, warn: 1, info: 2, http: 3, debug: 4 }; // Define colors for each log level const logColors = { error: 'red', warn: 'yellow', info: 'green', http: 'magenta', debug: 'blue' }; // Add colors to winston winston.addColors(logColors); // Create log format const logFormat = winston.format.combine( winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), winston.format.errors({ stack: true }), winston.format.json(), winston.format.printf(({ timestamp, level, message, stack, ...meta }) => { let log = `${timestamp} [${level.toUpperCase()}]: ${message}`; // Add stack trace for errors if (stack) { log += `\n${stack}`; } // Add metadata if present if (Object.keys(meta).length > 0) { log += `\n${JSON.stringify(meta, null, 2)}`; } return log; }) ); // Console format with colors const consoleFormat = winston.format.combine( winston.format.timestamp({ format: 'HH:mm:ss' }), winston.format.colorize({ all: true }), winston.format.printf(({ timestamp, level, message, stack }) => { let log = `${timestamp} [${level}]: ${message}`; if (stack) { log += `\n${stack}`; } return log; }) ); // Create transports const transports = [ // Console transport new winston.transports.Console({ level: process.env.LOG_LEVEL || 'info', format: consoleFormat, handleExceptions: true, handleRejections: true }) ]; // Add file transports in production or when LOG_TO_FILE is enabled if (process.env.NODE_ENV === 'production' || process.env.LOG_TO_FILE === 'true') { const logDir = process.env.LOG_DIR || '/logs'; // Error log file transports.push( new winston.transports.File({ filename: path.join(logDir, 'error.log'), level: 'error', format: logFormat, maxsize: 10 * 1024 * 1024, // 10MB maxFiles: 5, handleExceptions: true, handleRejections: true }) ); // Combined log file transports.push( new winston.transports.File({ filename: path.join(logDir, 'combined.log'), level: 'info', format: logFormat, maxsize: 10 * 1024 * 1024, // 10MB maxFiles: 10 }) ); // HTTP requests log file transports.push( new winston.transports.File({ filename: path.join(logDir, 'http.log'), level: 'http', format: logFormat, maxsize: 5 * 1024 * 1024, // 5MB maxFiles: 5 }) ); } // Create logger instance const logger = winston.createLogger({ levels: logLevels, level: process.env.LOG_LEVEL || 'info', format: logFormat, transports, exitOnError: false }); // Add custom methods for specific use cases logger.callEvent = (event, data) => { logger.info(` ${event}`, { event, ...data }); }; logger.queueEvent = (event, data) => { logger.info(` ${event}`, { event, ...data }); }; logger.webrtcEvent = (event, data) => { logger.info(` ${event}`, { event, ...data }); }; logger.twilioEvent = (event, data) => { logger.info(`[MOBILE] ${event}`, { event, ...data }); }; logger.securityEvent = (event, data) => { logger.warn(` ${event}`, { event, ...data }); }; logger.performanceEvent = (event, data) => { logger.info(`[PERFORMANCE] ${event}`, { event, ...data }); }; // Handle uncaught exceptions and unhandled rejections process.on('uncaughtException', (error) => { logger.error('Uncaught Exception:', error); process.exit(1); }); process.on('unhandledRejection', (reason, promise) => { logger.error('Unhandled Rejection at:', promise, 'reason:', reason); process.exit(1); }); module.exports = logger;