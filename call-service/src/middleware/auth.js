/** * ============================================= * [SECURITY] AUTHENTICATION MIDDLEWARE * JWT token validation for Call Service API endpoints * Integrates with existing Free Mobile authentication system * ============================================= */ const jwt = require('jsonwebtoken'); const logger = require('../utils/logger'); /** * JWT Authentication Middleware */ const authMiddleware = (req, res, next) => { try { // Get token from Authorization header const authHeader = req.headers.authorization; if (!authHeader) { logger.securityEvent('Missing Authorization Header', { ip: req.ip, userAgent: req.get('User-Agent'), endpoint: req.originalUrl }); return res.status(401).json({ error: 'Access denied', message: 'No authorization token provided' }); } // Extract token from "Bearer <token>" format const token = authHeader.startsWith('Bearer ') ? authHeader.slice(7) : authHeader; if (!token) { logger.securityEvent('Invalid Authorization Format', { ip: req.ip, userAgent: req.get('User-Agent'), endpoint: req.originalUrl }); return res.status(401).json({ error: 'Access denied', message: 'Invalid authorization token format' }); } // Verify JWT token const decoded = jwt.verify(token, process.env.JWT_SECRET); // Add user information to request object req.user = { id: decoded.id || decoded.userId, email: decoded.email, role: decoded.role || 'user', permissions: decoded.permissions || [], iat: decoded.iat, exp: decoded.exp }; // Log successful authentication logger.info('User authenticated successfully', { userId: req.user.id, email: req.user.email, role: req.user.role, endpoint: req.originalUrl, ip: req.ip }); next(); } catch (error) { // Handle different JWT errors let errorMessage = 'Invalid token'; let statusCode = 401; if (error.name === 'TokenExpiredError') { errorMessage = 'Token has expired'; logger.securityEvent('Expired Token', { ip: req.ip, userAgent: req.get('User-Agent'), endpoint: req.originalUrl, expiredAt: error.expiredAt }); } else if (error.name === 'JsonWebTokenError') { errorMessage = 'Invalid token format'; logger.securityEvent('Invalid Token Format', { ip: req.ip, userAgent: req.get('User-Agent'), endpoint: req.originalUrl, error: error.message }); } else if (error.name === 'NotBeforeError') { errorMessage = 'Token not active yet'; logger.securityEvent('Token Not Active', { ip: req.ip, userAgent: req.get('User-Agent'), endpoint: req.originalUrl, notBefore: error.date }); } else { logger.error('Authentication error:', error); statusCode = 500; errorMessage = 'Authentication service error'; } return res.status(statusCode).json({ error: 'Authentication failed', message: errorMessage }); } }; /** * Role-based authorization middleware */ const requireRole = (requiredRoles) => { return (req, res, next) => { if (!req.user) { return res.status(401).json({ error: 'Authentication required', message: 'User not authenticated' }); } const userRole = req.user.role; const allowedRoles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles]; if (!allowedRoles.includes(userRole)) { logger.securityEvent('Insufficient Permissions', { userId: req.user.id, userRole, requiredRoles: allowedRoles, endpoint: req.originalUrl, ip: req.ip }); return res.status(403).json({ error: 'Access forbidden', message: 'Insufficient permissions for this operation' }); } next(); }; }; /** * Permission-based authorization middleware */ const requirePermission = (requiredPermissions) => { return (req, res, next) => { if (!req.user) { return res.status(401).json({ error: 'Authentication required', message: 'User not authenticated' }); } const userPermissions = req.user.permissions || []; const requiredPerms = Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions]; const hasPermission = requiredPerms.some(perm => userPermissions.includes(perm) ); if (!hasPermission) { logger.securityEvent('Missing Required Permission', { userId: req.user.id, userPermissions, requiredPermissions: requiredPerms, endpoint: req.originalUrl, ip: req.ip }); return res.status(403).json({ error: 'Access forbidden', message: 'Required permissions not found' }); } next(); }; }; /** * Optional authentication middleware * Adds user info if token is present but doesn't require it */ const optionalAuth = (req, res, next) => { const authHeader = req.headers.authorization; if (!authHeader) { return next(); } try { const token = authHeader.startsWith('Bearer ') ? authHeader.slice(7) : authHeader; if (token) { const decoded = jwt.verify(token, process.env.JWT_SECRET); req.user = { id: decoded.id || decoded.userId, email: decoded.email, role: decoded.role || 'user', permissions: decoded.permissions || [] }; } } catch (error) { // Ignore token errors for optional auth logger.debug('Optional auth token invalid:', error.message); } next(); }; module.exports = { authMiddleware, requireRole, requirePermission, optionalAuth };