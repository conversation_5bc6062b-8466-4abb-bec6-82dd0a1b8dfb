/** * ============================================= * [FAILED] ERROR HANDLER MIDDLEWARE * Centralized error handling for Call Service * Provides consistent error responses and logging * ============================================= */ const logger = require('../utils/logger'); /** * Custom error classes */ class CallServiceError extends Error { constructor(message, statusCode = 500, code = 'CALL_SERVICE_ERROR') { super(message); this.name = 'CallServiceError'; this.statusCode = statusCode; this.code = code; this.isOperational = true; } } class ValidationError extends CallServiceError { constructor(message, details = []) { super(message, 400, 'VALIDATION_ERROR'); this.name = 'ValidationError'; this.details = details; } } class AuthenticationError extends CallServiceError { constructor(message = 'Authentication failed') { super(message, 401, 'AUTHENTICATION_ERROR'); this.name = 'AuthenticationError'; } } class AuthorizationError extends CallServiceError { constructor(message = 'Access forbidden') { super(message, 403, 'AUTHORIZATION_ERROR'); this.name = 'AuthorizationError'; } } class NotFoundError extends CallServiceError { constructor(message = 'Resource not found') { super(message, 404, 'NOT_FOUND_ERROR'); this.name = 'NotFoundError'; } } class TwilioError extends CallServiceError { constructor(message, twilioCode = null) { super(message, 502, 'TWILIO_ERROR'); this.name = 'TwilioError'; this.twilioCode = twilioCode; } } class WebRTCError extends CallServiceError { constructor(message) { super(message, 500, 'WEBRTC_ERROR'); this.name = 'WebRTCError'; } } class QueueError extends CallServiceError { constructor(message) { super(message, 500, 'QUEUE_ERROR'); this.name = 'QueueError'; } } /** * Error handler middleware */ const errorHandler = (error, req, res, next) => { // If response was already sent, delegate to default Express error handler if (res.headersSent) { return next(error); } // Default error properties let statusCode = 500; let message = 'Internal server error'; let code = 'INTERNAL_ERROR'; let details = null; // Handle known error types if (error instanceof CallServiceError) { statusCode = error.statusCode; message = error.message; code = error.code; if (error instanceof ValidationError) { details = error.details; } else if (error instanceof TwilioError) { details = { twilioCode: error.twilioCode }; } } else if (error.name === 'ValidationError') { // Express-validator errors statusCode = 400; message = 'Validation failed'; code = 'VALIDATION_ERROR'; details = error.array ? error.array() : error.details; } else if (error.name === 'MongoError' || error.name === 'MongoServerError') { // MongoDB errors statusCode = 500; message = 'Database operation failed'; code = 'DATABASE_ERROR'; if (error.code === 11000) { statusCode = 409; message = 'Duplicate entry'; code = 'DUPLICATE_ERROR'; } } else if (error.name === 'JsonWebTokenError') { // JWT errors statusCode = 401; message = 'Invalid authentication token'; code = 'INVALID_TOKEN'; } else if (error.name === 'TokenExpiredError') { statusCode = 401; message = 'Authentication token expired'; code = 'TOKEN_EXPIRED'; } else if (error.code === 'ECONNREFUSED') { // Connection errors statusCode = 503; message = 'Service temporarily unavailable'; code = 'SERVICE_UNAVAILABLE'; } else if (error.type === 'entity.too.large') { // Request too large statusCode = 413; message = 'Request entity too large'; code = 'PAYLOAD_TOO_LARGE'; } // Log error based on severity const errorInfo = { error: { name: error.name, message: error.message, code: error.code, stack: error.stack }, request: { method: req.method, url: req.originalUrl, headers: req.headers, body: req.body, params: req.params, query: req.query, ip: req.ip, userAgent: req.get('User-Agent') }, user: req.user ? { id: req.user.id, email: req.user.email, role: req.user.role } : null, timestamp: new Date().toISOString() }; if (statusCode >= 500) { logger.error('Server Error:', errorInfo); } else if (statusCode >= 400) { logger.warn('Client Error:', errorInfo); } // Prepare error response const errorResponse = { error: { code, message, timestamp: new Date().toISOString() } }; // Add details for validation errors if (details) { errorResponse.error.details = details; } // Add request ID if available if (req.id) { errorResponse.error.requestId = req.id; } // Add stack trace in development if (process.env.NODE_ENV === 'development') { errorResponse.error.stack = error.stack; } // Send error response res.status(statusCode).json(errorResponse); }; /** * 404 handler for unmatched routes */ const notFoundHandler = (req, res) => { const error = { code: 'ENDPOINT_NOT_FOUND', message: `Cannot ${req.method} ${req.originalUrl}`, timestamp: new Date().toISOString() }; logger.warn('Endpoint not found:', { method: req.method, url: req.originalUrl, ip: req.ip, userAgent: req.get('User-Agent') }); res.status(404).json({ error }); }; /** * Async error wrapper * Wraps async route handlers to catch errors */ const asyncHandler = (fn) => { return (req, res, next) => { Promise.resolve(fn(req, res, next)).catch(next); }; }; module.exports = { errorHandler, notFoundHandler, asyncHandler, // Error classes CallServiceError, ValidationError, AuthenticationError, AuthorizationError, NotFoundError, TwilioError, WebRTCError, QueueError };