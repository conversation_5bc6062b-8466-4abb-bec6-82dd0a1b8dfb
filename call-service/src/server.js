/** * ============================================= * FREE MOBILE CALL SERVICE * Bidirectional Call System with Twilio & WebRTC Integration * Handles incoming and outgoing calls with queue management * ============================================= */ const express = require('express'); const http = require('http'); const socketIo = require('socket.io'); const cors = require('cors'); const helmet = require('helmet'); const morgan = require('morgan'); const rateLimit = require('express-rate-limit'); require('dotenv').config(); // Import routes and middleware const callRoutes = require('./routes/callRoutes'); const queueRoutes = require('./routes/queueRoutes'); const webhookRoutes = require('./routes/webhookRoutes'); const authMiddleware = require('./middleware/auth'); const errorHandler = require('./middleware/errorHandler'); const logger = require('./utils/logger'); // Import services const CallService = require('./services/CallService'); const QueueManager = require('./services/QueueManager'); const WebRTCService = require('./services/WebRTCService'); const TwilioService = require('./services/TwilioService'); // Initialize Express app const app = express(); const server = http.createServer(app); // Initialize Socket.IO with CORS const io = socketIo(server, { cors: { origin: process.env.FRONTEND_URL || "http://localhost:3001", methods: ["GET", "POST"], credentials: true } }); // Security middleware app.use(helmet({ contentSecurityPolicy: { directives: { defaultSrc: ["'self'"], scriptSrc: ["'self'", "'unsafe-inline'", "https://sdk.twilio.com"], styleSrc: ["'self'", "'unsafe-inline'"], connectSrc: ["'self'", "wss:", "https://api.twilio.com"], mediaSrc: ["'self'", "blob:"] } } })); // CORS configuration app.use(cors({ origin: [ process.env.FRONTEND_URL || 'http://localhost:3001', process.env.BACKEND_URL || 'http://localhost:5000' ], credentials: true, methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'], allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'] })); // Rate limiting const limiter = rateLimit({ windowMs: 15 * 60 * 1000, // 15 minutes max: 100, // limit each IP to 100 requests per windowMs message: 'Too many requests from this IP, please try again later.', standardHeaders: true, legacyHeaders: false }); app.use('/api/', limiter); // Body parsing middleware app.use(express.json({ limit: '10mb' })); app.use(express.urlencoded({ extended: true, limit: '10mb' })); // Logging middleware app.use(morgan('combined', { stream: { write: message => logger.info(message.trim()) } })); // Health check endpoint app.get('/health', (req, res) => { res.json({ status: 'healthy', service: 'call-service', version: '1.0.0', timestamp: new Date().toISOString(), uptime: process.uptime(), memory: process.memoryUsage(), services: { twilio: TwilioService.isHealthy(), webrtc: WebRTCService.isHealthy(), queue: QueueManager.isHealthy() } }); }); // Initialize services const callService = new CallService(); const queueManager = new QueueManager(); const webrtcService = new WebRTCService(); const twilioService = new TwilioService(); // Make services available to routes app.locals.callService = callService; app.locals.queueManager = queueManager; app.locals.webrtcService = webrtcService; app.locals.twilioService = twilioService; // Routes app.use('/api/calls', authMiddleware, callRoutes); app.use('/api/queue', authMiddleware, queueRoutes); app.use('/api/webhooks', webhookRoutes); // No auth for Twilio webhooks // WebSocket connection handling io.on('connection', (socket) => { logger.info(`Client connected: ${socket.id}`); // Handle call events socket.on('join-call-room', (data) => { const { callId, userId, userRole } = data; socket.join(`call-${callId}`); socket.callId = callId; socket.userId = userId; socket.userRole = userRole; logger.info(`User ${userId} joined call room: call-${callId}`); // Notify other participants socket.to(`call-${callId}`).emit('participant-joined', { userId, userRole, socketId: socket.id }); }); // Handle WebRTC signaling socket.on('webrtc-offer', (data) => { socket.to(`call-${socket.callId}`).emit('webrtc-offer', { ...data, from: socket.id }); }); socket.on('webrtc-answer', (data) => { socket.to(`call-${socket.callId}`).emit('webrtc-answer', { ...data, from: socket.id }); }); socket.on('webrtc-ice-candidate', (data) => { socket.to(`call-${socket.callId}`).emit('webrtc-ice-candidate', { ...data, from: socket.id }); }); // Handle call control events socket.on('call-mute', (data) => { socket.to(`call-${socket.callId}`).emit('participant-muted', { userId: socket.userId, muted: data.muted }); }); socket.on('call-hold', (data) => { socket.to(`call-${socket.callId}`).emit('call-held', { userId: socket.userId, held: data.held }); }); socket.on('call-transfer', async (data) => { try { const result = await callService.transferCall(socket.callId, data.targetAgent); socket.to(`call-${socket.callId}`).emit('call-transferred', result); } catch (error) { socket.emit('call-error', { message: error.message }); } }); // Handle disconnection socket.on('disconnect', () => { logger.info(`Client disconnected: ${socket.id}`); if (socket.callId) { socket.to(`call-${socket.callId}`).emit('participant-left', { userId: socket.userId, socketId: socket.id }); } }); }); // Error handling middleware app.use(errorHandler); // 404 handler app.use('*', (req, res) => { res.status(404).json({ error: 'Endpoint not found', message: `Cannot ${req.method} ${req.originalUrl}`, service: 'call-service' }); }); // Start server const PORT = process.env.PORT || 5004; const HOST = process.env.HOST || '0.0.0.0'; server.listen(PORT, HOST, async () => { logger.info(`[DEPLOY] Call Service started on ${HOST}:${PORT}`); logger.info(` Environment: ${process.env.NODE_ENV || 'development'}`); try { // Initialize services await callService.initialize(); await queueManager.initialize(); await webrtcService.initialize(); await twilioService.initialize(); logger.info('[COMPLETE] All services initialized successfully'); } catch (error) { logger.error('[FAILED] Service initialization failed:', error); process.exit(1); } }); // Graceful shutdown process.on('SIGTERM', async () => { logger.info('SIGTERM received, shutting down gracefully'); server.close(async () => { try { await callService.cleanup(); await queueManager.cleanup(); await webrtcService.cleanup(); await twilioService.cleanup(); logger.info('[COMPLETE] Graceful shutdown completed'); process.exit(0); } catch (error) { logger.error('[FAILED] Error during shutdown:', error); process.exit(1); } }); }); process.on('SIGINT', () => { logger.info('SIGINT received, shutting down gracefully'); process.emit('SIGTERM'); }); module.exports = { app, server, io };