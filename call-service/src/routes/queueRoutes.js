/** * ============================================= * QUEUE ROUTES * API endpoints for call queue management * Handles queue statistics, agent management, and callbacks * ============================================= */ const express = require('express'); const { body, param, query, validationResult } = require('express-validator'); const logger = require('../utils/logger'); const { asyncHandler } = require('../middleware/errorHandler'); const router = express.Router(); /** * Validation middleware */ const handleValidationErrors = (req, res, next) => { const errors = validationResult(req); if (!errors.isEmpty()) { return res.status(400).json({ error: 'Validation failed', details: errors.array() }); } next(); }; /** * GET /api/queue/stats * Get queue statistics overview */ router.get('/stats', asyncHandler(async (req, res) => { const { queueManager } = req.app.locals; try { const queues = queueManager.getAllQueues(); const agents = queueManager.getAllAgents(); const stats = { queues: queues.map(queue => ({ id: queue.id, name: queue.name, currentCalls: queue.calls.length, totalCalls: queue.stats.totalCalls, answeredCalls: queue.stats.answeredCalls, abandonedCalls: queue.stats.abandonedCalls, averageWaitTime: queue.stats.averageWaitTime, averageHandleTime: queue.stats.averageHandleTime })), agents: { total: agents.length, available: agents.filter(a => a.status === 'available').length, busy: agents.filter(a => a.status === 'busy').length, offline: agents.filter(a => a.status === 'offline').length, onBreak: agents.filter(a => a.status === 'break').length }, overview: { totalCallsInQueue: queues.reduce((sum, q) => sum + q.calls.length, 0), longestWaitTime: Math.max(...queues.map(q => q.calls.length > 0 ? Date.now() - new Date(q.calls[0].enqueuedAt).getTime() : 0 ), 0) } }; res.json({ success: true, stats, timestamp: new Date().toISOString() }); } catch (error) { logger.error('Failed to get queue stats:', error); res.status(500).json({ error: 'Failed to get queue statistics', message: error.message }); } })); /** * GET /api/queue/:queueId * Get specific queue details */ router.get('/:queueId', [ param('queueId').isString().withMessage('Valid queue ID required') ], handleValidationErrors, asyncHandler(async (req, res) => { const { queueId } = req.params; const { queueManager } = req.app.locals; try { const queue = queueManager.queues.get(queueId); if (!queue) { return res.status(404).json({ error: 'Queue not found', queueId }); } const queueDetails = { id: queue.id, name: queue.name, priority: queue.priority, maxWaitTime: queue.maxWaitTime, skillsRequired: queue.skillsRequired, language: queue.language, currentCalls: queue.calls.map(call => ({ callId: call.callId, from: call.from, priority: call.priority, enqueuedAt: call.enqueuedAt, estimatedWaitTime: call.estimatedWaitTime, waitTime: Date.now() - new Date(call.enqueuedAt).getTime() })), stats: queue.stats, createdAt: queue.createdAt }; res.json({ success: true, queue: queueDetails }); } catch (error) { logger.error('Failed to get queue details:', error); res.status(500).json({ error: 'Failed to get queue details', message: error.message }); } }) ); /** * POST /api/queue/agents/register * Register new agent */ router.post('/agents/register', [ body('id').isUUID().withMessage('Valid agent ID required'), body('name').isString().isLength({ min: 2 }).withMessage('Agent name required'), body('email').isEmail().withMessage('Valid email required'), body('skills').isArray().withMessage('Skills array required'), body('languages').optional().isArray().withMessage('Languages must be array'), body('maxConcurrentCalls').optional().isInt({ min: 1, max: 10 }).withMessage('Invalid max concurrent calls') ], handleValidationErrors, asyncHandler(async (req, res) => { const agentData = req.body; const { queueManager } = req.app.locals; try { const agent = await queueManager.registerAgent(agentData); logger.info(`Agent registered: ${agent.id} - ${agent.name}`); res.status(201).json({ success: true, agent: { id: agent.id, name: agent.name, email: agent.email, skills: agent.skills, languages: agent.languages, status: agent.status, maxConcurrentCalls: agent.maxConcurrentCalls, registeredAt: agent.registeredAt } }); } catch (error) { logger.error('Failed to register agent:', error); res.status(500).json({ error: 'Failed to register agent', message: error.message }); } }) ); /** * PUT /api/queue/agents/:agentId/status * Update agent status */ router.put('/agents/:agentId/status', [ param('agentId').isUUID().withMessage('Valid agent ID required'), body('status').isIn(['available', 'busy', 'offline', 'break']).withMessage('Valid status required'), body('reason').optional().isString().withMessage('Reason must be string') ], handleValidationErrors, asyncHandler(async (req, res) => { const { agentId } = req.params; const { status, reason } = req.body; const { queueManager } = req.app.locals; try { const agent = await queueManager.updateAgentStatus(agentId, status, { reason }); logger.info(`Agent status updated: ${agentId} -> ${status}`); res.json({ success: true, agent: { id: agent.id, name: agent.name, status: agent.status, lastActivity: agent.lastActivity } }); } catch (error) { logger.error('Failed to update agent status:', error); res.status(500).json({ error: 'Failed to update agent status', message: error.message }); } }) ); /** * GET /api/queue/agents * Get all agents */ router.get('/agents', asyncHandler(async (req, res) => { const { queueManager } = req.app.locals; try { const agents = queueManager.getAllAgents().map(agent => ({ id: agent.id, name: agent.name, email: agent.email, skills: agent.skills, languages: agent.languages, status: agent.status, activeCalls: agent.activeCalls.length, maxConcurrentCalls: agent.maxConcurrentCalls, stats: agent.stats, lastActivity: agent.lastActivity })); res.json({ success: true, agents, total: agents.length }); } catch (error) { logger.error('Failed to get agents:', error); res.status(500).json({ error: 'Failed to get agents', message: error.message }); } })); /** * POST /api/queue/callback * Schedule callback request */ router.post('/callback', [ body('userId').isUUID().withMessage('Valid user ID required'), body('reason').isString().isLength({ min: 5 }).withMessage('Callback reason required (min 5 chars)'), body('priority').isIn(['low', 'normal', 'high', 'urgent']).withMessage('Valid priority required'), body('scheduledTime').optional().isISO8601().withMessage('Valid scheduled time required'), body('phoneNumber').optional().isMobilePhone().withMessage('Valid phone number required'), body('preferredLanguage').optional().isIn(['fr', 'en']).withMessage('Valid language required') ], handleValidationErrors, asyncHandler(async (req, res) => { const { userId, reason, priority, scheduledTime, phoneNumber, preferredLanguage = 'fr' } = req.body; try { // Create callback request const callbackId = require('uuid').v4(); const callback = { id: callbackId, userId, reason, priority, scheduledTime: scheduledTime ? new Date(scheduledTime) : new Date(), phoneNumber, preferredLanguage, status: 'pending', createdAt: new Date() }; // Store callback request (in a real implementation, this would go to database) logger.info('Callback request created:', callback); res.status(201).json({ success: true, callback: { id: callback.id, scheduledTime: callback.scheduledTime, estimatedCallTime: new Date(Date.now() + 30 * 60 * 1000), // 30 minutes from now status: callback.status }, message: 'Votre demande de rappel a été enregistrée. Vous serez contacté dans les plus brefs délais.' }); } catch (error) { logger.error('Failed to create callback request:', error); res.status(500).json({ error: 'Failed to create callback request', message: error.message }); } }) ); /** * GET /api/queue/available-slots * Get available callback time slots */ router.get('/available-slots', [ query('date').isISO8601().withMessage('Valid date required'), query('duration').optional().isInt({ min: 15, max: 120 }).withMessage('Duration must be between 15-120 minutes') ], handleValidationErrors, asyncHandler(async (req, res) => { const { date, duration = 30 } = req.query; try { const requestedDate = new Date(date); const slots = []; // Generate available slots (9 AM to 6 PM, every 30 minutes) for (let hour = 9; hour < 18; hour++) { for (let minute = 0; minute < 60; minute += 30) { const slotTime = new Date(requestedDate); slotTime.setHours(hour, minute, 0, 0); // Skip past slots if (slotTime > new Date()) { slots.push({ time: slotTime.toISOString(), available: Math.random() > 0.3, // Simulate availability estimatedWaitTime: Math.floor(Math.random() * 60) + 15 // 15-75 minutes }); } } } res.json({ success: true, date: requestedDate.toISOString().split('T')[0], slots: slots.filter(slot => slot.available), duration: parseInt(duration) }); } catch (error) { logger.error('Failed to get available slots:', error); res.status(500).json({ error: 'Failed to get available slots', message: error.message }); } }) ); /** * GET /api/queue/position/:callId * Get call position in queue */ router.get('/position/:callId', [ param('callId').isUUID().withMessage('Valid call ID required') ], handleValidationErrors, asyncHandler(async (req, res) => { const { callId } = req.params; const { queueManager } = req.app.locals; try { let position = null; let queueId = null; let estimatedWaitTime = 0; // Find call in queues for (const [id, queue] of queueManager.queues) { const callIndex = queue.calls.findIndex(call => call.callId === callId); if (callIndex !== -1) { position = callIndex + 1; queueId = id; estimatedWaitTime = queueManager.calculateEstimatedWaitTime(id); break; } } if (position === null) { return res.status(404).json({ error: 'Call not found in queue', callId }); } res.json({ success: true, callId, queueId, position, estimatedWaitTime, timestamp: new Date().toISOString() }); } catch (error) { logger.error('Failed to get call position:', error); res.status(500).json({ error: 'Failed to get call position', message: error.message }); } }) ); module.exports = router;