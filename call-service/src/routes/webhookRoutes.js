/** * ============================================= * WEBHOOK ROUTES * Twilio webhook endpoints for call events * Handles incoming calls, status updates, and TwiML generation * ============================================= */ const express = require('express'); const twilio = require('twilio'); const logger = require('../utils/logger'); const { asyncHandler } = require('../middleware/errorHandler'); const router = express.Router(); /** * POST /api/webhooks/incoming * Handle incoming call webhook from Twilio */ router.post('/incoming', asyncHandler(async (req, res) => { const { CallSid, From, To, CallStatus } = req.body; const { twilioService, queueManager, callService } = req.app.locals; logger.twilioEvent('Incoming Call', { callSid: CallSid, from: From, to: To, status: CallStatus }); try { // Handle incoming call const callData = await twilioService.handleIncomingCall(CallSid, From, To); // Create call record await callService.createCall(callData); // Add to queue const queueResult = await queueManager.enqueueCall(callData, 'support-queue'); // Generate TwiML response const twiml = twilioService.generateIncomingTwiML({ queueName: 'support-queue', waitMessage: `Bonjour et merci d'appeler Free Mobile. Vous êtes actuellement en position ${queueResult.position} dans la file d'attente. Temps d'attente estimé: ${Math.ceil(queueResult.estimatedWaitTime / 60)} minutes.` }); res.type('text/xml'); res.send(twiml); } catch (error) { logger.error('Failed to handle incoming call:', error); // Return error TwiML const twiml = new twilio.twiml.VoiceResponse(); twiml.say({ voice: 'alice', language: 'fr-FR' }, 'Nous rencontrons actuellement des difficultés techniques. Veuillez rappeler plus tard.'); twiml.hangup(); res.type('text/xml'); res.send(twiml.toString()); } })); /** * POST /api/webhooks/status * Handle call status updates from Twilio */ router.post('/status', asyncHandler(async (req, res) => { const { CallSid, CallStatus, CallDuration, From, To } = req.body; const { callService, queueManager } = req.app.locals; logger.twilioEvent('Call Status Update', { callSid: CallSid, status: CallStatus, duration: CallDuration, from: From, to: To }); try { // Find call by Twilio SID const calls = await callService.getCalls({ twilioCallSid: CallSid }); const call = calls[0]; if (call) { // Update call status const updateData = { status: CallStatus }; if (CallDuration) { updateData.duration = parseInt(CallDuration); } if (CallStatus === 'completed' || CallStatus === 'failed') { updateData.endTime = new Date(); // Remove from queue if still queued await queueManager.dequeueCall(call.callId, CallStatus === 'completed' ? 'answered' : 'failed'); } await callService.updateCall(call.callId, updateData); } res.status(200).send('OK'); } catch (error) { logger.error('Failed to handle status update:', error); res.status(500).send('Error'); } })); /** * POST /api/webhooks/twiml/outgoing * Generate TwiML for outgoing calls */ router.post('/twiml/outgoing', asyncHandler(async (req, res) => { const { CallSid, From, To } = req.body; const { twilioService } = req.app.locals; logger.twilioEvent('Outgoing Call TwiML Request', { callSid: CallSid, from: From, to: To }); try { const twiml = twilioService.generateOutgoingTwiML({ message: "Bonjour, vous êtes en communication avec le service client Free Mobile. Un conseiller va prendre votre appel.", waitMusic: true }); res.type('text/xml'); res.send(twiml); } catch (error) { logger.error('Failed to generate outgoing TwiML:', error); const twiml = new twilio.twiml.VoiceResponse(); twiml.say({ voice: 'alice', language: 'fr-FR' }, 'Erreur technique. Veuillez raccrocher et rappeler.'); twiml.hangup(); res.type('text/xml'); res.send(twiml.toString()); } })); /** * POST /api/webhooks/twiml/wait-music * Generate wait music TwiML */ router.post('/twiml/wait-music', asyncHandler(async (req, res) => { const { twilioService } = req.app.locals; try { const twiml = twilioService.generateWaitMusicTwiML(); res.type('text/xml'); res.send(twiml); } catch (error) { logger.error('Failed to generate wait music TwiML:', error); res.status(500).send('Error'); } })); /** * POST /api/webhooks/twiml/queue-result * Handle queue result (answered or abandoned) */ router.post('/twiml/queue-result', asyncHandler(async (req, res) => { const { QueueResult, CallSid } = req.body; const { callService } = req.app.locals; logger.twilioEvent('Queue Result', { callSid: CallSid, result: QueueResult }); try { const twiml = new twilio.twiml.VoiceResponse(); if (QueueResult === 'hangup') { // Call was abandoned const calls = await callService.getCalls({ twilioCallSid: CallSid }); if (calls[0]) { await callService.updateCall(calls[0].callId, { status: 'abandoned', endTime: new Date(), endReason: 'customer_hangup' }); } } else if (QueueResult === 'system-hangup') { // System ended the call (max wait time exceeded) twiml.say({ voice: 'alice', language: 'fr-FR' }, 'Nous nous excusons pour cette attente prolongée. Veuillez rappeler plus tard ou utiliser notre service de rappel automatique.'); twiml.hangup(); } res.type('text/xml'); res.send(twiml.toString()); } catch (error) { logger.error('Failed to handle queue result:', error); res.status(500).send('Error'); } })); /** * POST /api/webhooks/twiml/transfer * Handle call transfer TwiML */ router.post('/twiml/transfer', asyncHandler(async (req, res) => { const { agent, conference } = req.query; const { CallSid } = req.body; logger.twilioEvent('Call Transfer TwiML', { callSid: CallSid, agent, conference }); try { const twiml = new twilio.twiml.VoiceResponse(); if (conference) { // Transfer to conference room twiml.say({ voice: 'alice', language: 'fr-FR' }, 'Transfert vers un conseiller en cours. Veuillez patienter.'); twiml.dial().conference({ startConferenceOnEnter: true, endConferenceOnExit: false }, conference); } else { // Direct transfer to agent twiml.say({ voice: 'alice', language: 'fr-FR' }, 'Transfert vers un conseiller spécialisé en cours.'); twiml.dial({ timeout: 30, record: 'record-from-answer' }, agent); } res.type('text/xml'); res.send(twiml.toString()); } catch (error) { logger.error('Failed to generate transfer TwiML:', error); res.status(500).send('Error'); } })); /** * POST /api/webhooks/recording * Handle recording completion webhook */ router.post('/recording', asyncHandler(async (req, res) => { const { CallSid, RecordingUrl, RecordingDuration } = req.body; const { callService } = req.app.locals; logger.twilioEvent('Recording Completed', { callSid: CallSid, recordingUrl: RecordingUrl, duration: RecordingDuration }); try { // Find call and update with recording info const calls = await callService.getCalls({ twilioCallSid: CallSid }); if (calls[0]) { await callService.updateCall(calls[0].callId, { recordingUrl: RecordingUrl, recordingDuration: parseInt(RecordingDuration) }); } res.status(200).send('OK'); } catch (error) { logger.error('Failed to handle recording webhook:', error); res.status(500).send('Error'); } })); /** * POST /api/webhooks/transcription * Handle transcription completion webhook */ router.post('/transcription', asyncHandler(async (req, res) => { const { CallSid, TranscriptionText, TranscriptionUrl } = req.body; const { callService } = req.app.locals; logger.twilioEvent('Transcription Completed', { callSid: CallSid, transcriptionUrl: TranscriptionUrl, textLength: TranscriptionText ? TranscriptionText.length : 0 }); try { // Find call and update with transcription info const calls = await callService.getCalls({ twilioCallSid: CallSid }); if (calls[0]) { await callService.updateCall(calls[0].callId, { transcriptionText: TranscriptionText, transcriptionUrl: TranscriptionUrl }); } res.status(200).send('OK'); } catch (error) { logger.error('Failed to handle transcription webhook:', error); res.status(500).send('Error'); } })); /** * POST /api/webhooks/conference-status * Handle conference status updates */ router.post('/conference-status', asyncHandler(async (req, res) => { const { ConferenceSid, StatusCallbackEvent, CallSid } = req.body; logger.twilioEvent('Conference Status Update', { conferenceSid: ConferenceSid, event: StatusCallbackEvent, callSid: CallSid }); // Handle conference events (join, leave, start, end, etc.) // This can be used for advanced call analytics and monitoring res.status(200).send('OK'); })); module.exports = router;