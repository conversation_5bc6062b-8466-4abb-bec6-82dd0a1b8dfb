/** * ============================================= * CALL ROUTES * API endpoints for call management * Handles incoming/outgoing calls, WebRTC, and call controls * ============================================= */ const express = require('express'); const { body, param, query, validationResult } = require('express-validator'); const logger = require('../utils/logger'); const { v4: uuidv4 } = require('uuid'); const router = express.Router(); /** * Validation middleware */ const handleValidationErrors = (req, res, next) => { const errors = validationResult(req); if (!errors.isEmpty()) { return res.status(400).json({ error: 'Validation failed', details: errors.array() }); } next(); }; /** * GET /api/calls/health * Health check for call service */ router.get('/health', (req, res) => { const { callService, queueManager, webrtcService, twilioService } = req.app.locals; res.json({ status: 'healthy', services: { callService: callService.isHealthy(), queueManager: queueManager.isHealthy(), webrtcService: webrtcService.isHealthy(), twilioService: twilioService.isHealthy() }, timestamp: new Date().toISOString() }); }); /** * POST /api/calls/outgoing * Initiate outgoing call */ router.post('/outgoing', [ body('to').isMobilePhone().withMessage('Valid phone number required'), body('userId').isUUID().withMessage('Valid user ID required'), body('priority').optional().isIn(['low', 'normal', 'high', 'urgent']).withMessage('Invalid priority'), body('recordCall').optional().isBoolean().withMessage('Record call must be boolean'), body('transcribeCall').optional().isBoolean().withMessage('Transcribe call must be boolean') ], handleValidationErrors, async (req, res) => { try { const { to, userId, priority = 'normal', recordCall = true, transcribeCall = true } = req.body; const { twilioService, callService } = req.app.locals; logger.info(` Outgoing call request: ${userId} -> ${to}`); // Create call record const callId = uuidv4(); const callOptions = { to, callId, userId, priority, recordCall, transcribeCall }; // Initiate Twilio call const { call, callData } = await twilioService.makeOutgoingCall(callOptions); // Store call in database await callService.createCall(callData); res.status(201).json({ success: true, callId, twilioCallSid: call.sid, status: call.status, to: call.to, from: call.from, createdAt: new Date().toISOString() }); } catch (error) { logger.error('[FAILED] Outgoing call failed:', error); res.status(500).json({ error: 'Failed to initiate outgoing call', message: error.message }); } } ); /** * POST /api/calls/webrtc/start * Start WebRTC call session */ router.post('/webrtc/start', [ body('participants').isArray().withMessage('Participants array required'), body('participants.*.userId').isUUID().withMessage('Valid user ID required for each participant'), body('participants.*.role').isIn(['customer', 'agent', 'supervisor']).withMessage('Valid role required') ], handleValidationErrors, async (req, res) => { try { const { participants } = req.body; const { webrtcService } = req.app.locals; const callId = uuidv4(); logger.info(` Starting WebRTC call: ${callId}`); // Create WebRTC session const session = webrtcService.createCallSession(callId, participants); // Get WebRTC configuration for clients const webrtcConfig = webrtcService.getWebRTCConfig(); res.status(201).json({ success: true, callId, session: { callId: session.callId, status: session.status, participants: Array.from(session.participants.values()), createdAt: session.createdAt }, webrtcConfig }); } catch (error) { logger.error('[FAILED] WebRTC call start failed:', error); res.status(500).json({ error: 'Failed to start WebRTC call', message: error.message }); } } ); /** * POST /api/calls/:callId/join * Join existing call */ router.post('/:callId/join', [ param('callId').isUUID().withMessage('Valid call ID required'), body('userId').isUUID().withMessage('Valid user ID required'), body('role').isIn(['customer', 'agent', 'supervisor']).withMessage('Valid role required'), body('socketId').isString().withMessage('Socket ID required') ], handleValidationErrors, async (req, res) => { try { const { callId } = req.params; const { userId, role, socketId } = req.body; const { webrtcService } = req.app.locals; logger.info(`[USER] User joining call: ${userId} -> ${callId}`); // Add participant to call const session = webrtcService.addParticipant(callId, { userId, role, socketId }); res.json({ success: true, callId, participant: session.participants.get(userId), webrtcConfig: webrtcService.getWebRTCConfig() }); } catch (error) { logger.error('[FAILED] Failed to join call:', error); res.status(500).json({ error: 'Failed to join call', message: error.message }); } } ); /** * POST /api/calls/:callId/leave * Leave call */ router.post('/:callId/leave', [ param('callId').isUUID().withMessage('Valid call ID required'), body('userId').isUUID().withMessage('Valid user ID required') ], handleValidationErrors, async (req, res) => { try { const { callId } = req.params; const { userId } = req.body; const { webrtcService } = req.app.locals; logger.info(`[USER] User leaving call: ${userId} <- ${callId}`); // Remove participant from call const session = webrtcService.removeParticipant(callId, userId); res.json({ success: true, callId, remainingParticipants: Array.from(session.participants.values()) }); } catch (error) { logger.error('[FAILED] Failed to leave call:', error); res.status(500).json({ error: 'Failed to leave call', message: error.message }); } } ); /** * POST /api/calls/:callId/transfer * Transfer call to another agent */ router.post('/:callId/transfer', [ param('callId').isUUID().withMessage('Valid call ID required'), body('targetAgentId').isUUID().withMessage('Valid target agent ID required'), body('reason').optional().isString().withMessage('Transfer reason must be string') ], handleValidationErrors, async (req, res) => { try { const { callId } = req.params; const { targetAgentId, reason } = req.body; const { callService, queueManager } = req.app.locals; logger.info(` Transferring call: ${callId} -> Agent ${targetAgentId}`); // Get target agent const targetAgent = queueManager.agents.get(targetAgentId); if (!targetAgent) { return res.status(404).json({ error: 'Target agent not found' }); } if (targetAgent.status !== 'available') { return res.status(400).json({ error: 'Target agent is not available' }); } // Perform transfer const result = await callService.transferCall(callId, targetAgentId, reason); res.json({ success: true, callId, targetAgent: { id: targetAgent.id, name: targetAgent.name }, transferredAt: new Date().toISOString() }); } catch (error) { logger.error('[FAILED] Call transfer failed:', error); res.status(500).json({ error: 'Failed to transfer call', message: error.message }); } } ); /** * POST /api/calls/:callId/end * End call */ router.post('/:callId/end', [ param('callId').isUUID().withMessage('Valid call ID required'), body('reason').optional().isString().withMessage('End reason must be string') ], handleValidationErrors, async (req, res) => { try { const { callId } = req.params; const { reason = 'completed' } = req.body; const { callService, webrtcService } = req.app.locals; logger.info(` Ending call: ${callId} (${reason})`); // End WebRTC session if exists try { webrtcService.endCall(callId); } catch (error) { logger.warn('WebRTC session not found or already ended'); } // End call in database const result = await callService.endCall(callId, reason); res.json({ success: true, callId, endedAt: new Date().toISOString(), reason }); } catch (error) { logger.error('[FAILED] Failed to end call:', error); res.status(500).json({ error: 'Failed to end call', message: error.message }); } } ); /** * GET /api/calls/:callId * Get call details */ router.get('/:callId', [ param('callId').isUUID().withMessage('Valid call ID required') ], handleValidationErrors, async (req, res) => { try { const { callId } = req.params; const { callService, webrtcService } = req.app.locals; // Get call from database const call = await callService.getCall(callId); if (!call) { return res.status(404).json({ error: 'Call not found' }); } // Get WebRTC session if exists const webrtcSession = webrtcService.getCallSession(callId); res.json({ success: true, call, webrtcSession }); } catch (error) { logger.error('[FAILED] Failed to get call details:', error); res.status(500).json({ error: 'Failed to get call details', message: error.message }); } } ); /** * GET /api/calls * Get calls list with filtering */ router.get('/', [ query('status').optional().isIn(['initiated', 'ringing', 'answered', 'completed', 'failed']), query('direction').optional().isIn(['inbound', 'outbound']), query('userId').optional().isUUID(), query('limit').optional().isInt({ min: 1, max: 100 }), query('offset').optional().isInt({ min: 0 }) ], handleValidationErrors, async (req, res) => { try { const { status, direction, userId, limit = 20, offset = 0 } = req.query; const { callService } = req.app.locals; const filters = {}; if (status) filters.status = status; if (direction) filters.direction = direction; if (userId) filters.userId = userId; const calls = await callService.getCalls(filters, { limit, offset }); res.json({ success: true, calls, pagination: { limit: parseInt(limit), offset: parseInt(offset), total: calls.length } }); } catch (error) { logger.error('[FAILED] Failed to get calls list:', error); res.status(500).json({ error: 'Failed to get calls list', message: error.message }); } } ); /** * GET /api/calls/stats/overview * Get call statistics overview */ router.get('/stats/overview', async (req, res) => { try { const { callService, queueManager, webrtcService } = req.app.locals; const stats = { activeCalls: webrtcService.getActiveCalls().length, totalAgents: queueManager.getAllAgents().length, availableAgents: queueManager.getAllAgents().filter(a => a.status === 'available').length, queues: queueManager.getAllQueues().map(q => ({ id: q.id, name: q.name, waitingCalls: q.calls.length, stats: q.stats })), webrtcStats: webrtcService.getStats() }; res.json({ success: true, stats, timestamp: new Date().toISOString() }); } catch (error) { logger.error('[FAILED] Failed to get call stats:', error); res.status(500).json({ error: 'Failed to get call statistics', message: error.message }); } }); /** * @route POST /calls/emergency/initiate * @desc Initiate emergency WebRTC call * @access Private */ router.post('/emergency/initiate', [ body('emergencyCallId').isUUID(4).withMessage('Invalid emergency call ID'), body('customerId').isMongoId().withMessage('Invalid customer ID'), body('agentId').isMongoId().withMessage('Invalid agent ID'), body('urgencyLevel').optional().isIn(['low', 'normal', 'medium', 'high', 'urgent', 'critical']), body('priority').optional().isIn(['normal', 'high', 'emergency', 'critical']) ], handleValidationErrors, async (req, res) => { try { const { emergencyCallId, customerId, agentId, urgencyLevel, priority } = req.body; const { webrtcService } = req.app.locals; logger.info(` Initiating emergency WebRTC call`, { emergencyCallId, customerId, agentId, urgencyLevel }); // Create emergency WebRTC session const session = webrtcService.createEmergencyCallSession( emergencyCallId, customerId, agentId, { urgencyLevel, priority: priority || 'emergency' } ); res.status(201).json({ success: true, message: 'Emergency WebRTC call initiated', session: { sessionId: session.sessionId, callId: session.callId, emergencyCallId: session.emergencyCallId, participants: session.participants, emergencyConfig: session.emergencyConfig }, webrtcConfig: session.webrtcConfig }); } catch (error) { logger.error('Failed to initiate emergency WebRTC call:', error); res.status(500).json({ success: false, error: 'Failed to initiate emergency call', message: error.message }); } } ); /** * @route POST /calls/emergency/:callId/escalate * @desc Escalate emergency call to supervisor * @access Private */ router.post('/emergency/:callId/escalate', [ param('callId').notEmpty().withMessage('Call ID is required'), body('supervisorId').isMongoId().withMessage('Invalid supervisor ID'), body('reason').optional().isLength({ min: 5, max: 200 }).withMessage('Reason must be 5-200 characters') ], handleValidationErrors, async (req, res) => { try { const { callId } = req.params; const { supervisorId, reason } = req.body; const { webrtcService } = req.app.locals; logger.info(` Escalating emergency call`, { callId, supervisorId, reason }); const result = webrtcService.escalateEmergencyCall(callId, supervisorId, reason); res.json({ success: true, message: 'Emergency call escalated successfully', escalation: result }); } catch (error) { logger.error('Failed to escalate emergency call:', error); res.status(500).json({ success: false, error: 'Failed to escalate emergency call', message: error.message }); } } ); /** * @route GET /calls/emergency/stats * @desc Get emergency call statistics * @access Private (Admin/Supervisor only) */ router.get('/emergency/stats', async (req, res) => { try { const { webrtcService } = req.app.locals; const stats = webrtcService.getEmergencyCallStats(); res.json({ success: true, stats, timestamp: new Date() }); } catch (error) { logger.error('Failed to get emergency call stats:', error); res.status(500).json({ success: false, error: 'Failed to get emergency call statistics', message: error.message }); } } ); /** * @route POST /calls/emergency/:callId/end * @desc End emergency call * @access Private */ router.post('/emergency/:callId/end', [ param('callId').notEmpty().withMessage('Call ID is required'), body('reason').optional().isLength({ max: 200 }).withMessage('Reason must not exceed 200 characters') ], handleValidationErrors, async (req, res) => { try { const { callId } = req.params; const { reason } = req.body; const { webrtcService } = req.app.locals; logger.info(` Ending emergency call`, { callId, reason }); const result = webrtcService.endEmergencyCall(callId, reason); res.json({ success: true, message: 'Emergency call ended successfully', callEnd: result }); } catch (error) { logger.error('Failed to end emergency call:', error); res.status(500).json({ success: false, error: 'Failed to end emergency call', message: error.message }); } } ); module.exports = router;