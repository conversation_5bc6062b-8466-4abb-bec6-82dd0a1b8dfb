/** * ============================================= * WEBRTC SERVICE * Handles browser-based voice calls using WebRTC * Supports peer-to-peer and server-mediated calls * ============================================= */ const logger = require('../utils/logger'); const { v4: uuidv4 } = require('uuid'); class WebRTCService { constructor() { this.activeCalls = new Map(); this.iceServers = [ { urls: 'stun:stun.l.google.com:19302' }, { urls: 'stun:stun1.l.google.com:19302' }, { urls: 'stun:stun2.l.google.com:19302' } ]; this.isInitialized = false; } /** * Initialize WebRTC service */ async initialize() { try { // Add TURN servers if configured if (process.env.TURN_SERVER_URL) { this.iceServers.push({ urls: process.env.TURN_SERVER_URL, username: process.env.TURN_USERNAME, credential: process.env.TURN_PASSWORD }); } this.isInitialized = true; logger.info('[COMPLETE] WebRTC Service initialized successfully'); } catch (error) { logger.error('[FAILED] WebRTC Service initialization failed:', error); throw error; } } /** * Create new WebRTC call session */ createCallSession(callId, participants = [], options = {}) { try { const session = { callId, participants: new Map(), createdAt: new Date(), status: 'created', // Emergency call specific properties isEmergencyCall: options.isEmergencyCall || false, emergencyCallId: options.emergencyCallId || null, priority: options.priority || 'normal', urgencyLevel: options.urgencyLevel || null, recordingEnabled: options.isEmergencyCall || false, // Auto-record emergency calls transcriptionEnabled: options.isEmergencyCall || false }; // Add initial participants participants.forEach(participant => { session.participants.set(participant.userId, { userId: participant.userId, socketId: participant.socketId, role: participant.role, status: 'connecting', joinedAt: new Date(), // Emergency call participant info isEmergencyParticipant: participant.role === 'customer' && session.isEmergencyCall }); }); this.activeCalls.set(callId, session); if (session.isEmergencyCall) { logger.info(` Emergency WebRTC call session created: ${callId}`, { emergencyCallId: session.emergencyCallId, priority: session.priority, urgencyLevel: session.urgencyLevel }); } else { logger.info(`[COMPLETE] WebRTC call session created: ${callId}`); } return session; } catch (error) { logger.error('[FAILED] Failed to create WebRTC call session:', error); throw error; } } /** * Add participant to call */ addParticipant(callId, participant) { try { const session = this.activeCalls.get(callId); if (!session) { throw new Error(`Call session not found: ${callId}`); } session.participants.set(participant.userId, { userId: participant.userId, socketId: participant.socketId, role: participant.role, status: 'connecting', joinedAt: new Date(), muted: false, videoEnabled: false }); logger.info(`[COMPLETE] Participant added to call ${callId}: ${participant.userId}`); return session; } catch (error) { logger.error('[FAILED] Failed to add participant to call:', error); throw error; } } /** * Remove participant from call */ removeParticipant(callId, userId) { try { const session = this.activeCalls.get(callId); if (!session) { throw new Error(`Call session not found: ${callId}`); } session.participants.delete(userId); // End call if no participants left if (session.participants.size === 0) { this.endCall(callId); } logger.info(`[COMPLETE] Participant removed from call ${callId}: ${userId}`); return session; } catch (error) { logger.error('[FAILED] Failed to remove participant from call:', error); throw error; } } /** * Get WebRTC configuration for client */ getWebRTCConfig() { return { iceServers: this.iceServers, iceCandidatePoolSize: 10, bundlePolicy: 'balanced', rtcpMuxPolicy: 'require', iceTransportPolicy: 'all' }; } /** * Handle WebRTC offer */ handleOffer(callId, fromUserId, offer) { try { const session = this.activeCalls.get(callId); if (!session) { throw new Error(`Call session not found: ${callId}`); } const participant = session.participants.get(fromUserId); if (!participant) { throw new Error(`Participant not found: ${fromUserId}`); } participant.offer = offer; participant.status = 'offered'; logger.info(` WebRTC offer received from ${fromUserId} in call ${callId}`); return { success: true, callId, fromUserId }; } catch (error) { logger.error('[FAILED] Failed to handle WebRTC offer:', error); throw error; } } /** * Handle WebRTC answer */ handleAnswer(callId, fromUserId, answer) { try { const session = this.activeCalls.get(callId); if (!session) { throw new Error(`Call session not found: ${callId}`); } const participant = session.participants.get(fromUserId); if (!participant) { throw new Error(`Participant not found: ${fromUserId}`); } participant.answer = answer; participant.status = 'answered'; logger.info(` WebRTC answer received from ${fromUserId} in call ${callId}`); return { success: true, callId, fromUserId }; } catch (error) { logger.error('[FAILED] Failed to handle WebRTC answer:', error); throw error; } } /** * Handle ICE candidate */ handleIceCandidate(callId, fromUserId, candidate) { try { const session = this.activeCalls.get(callId); if (!session) { throw new Error(`Call session not found: ${callId}`); } const participant = session.participants.get(fromUserId); if (!participant) { throw new Error(`Participant not found: ${fromUserId}`); } if (!participant.iceCandidates) { participant.iceCandidates = []; } participant.iceCandidates.push(candidate); logger.debug(` ICE candidate received from ${fromUserId} in call ${callId}`); return { success: true, callId, fromUserId }; } catch (error) { logger.error('[FAILED] Failed to handle ICE candidate:', error); throw error; } } /** * Update participant status */ updateParticipantStatus(callId, userId, status, metadata = {}) { try { const session = this.activeCalls.get(callId); if (!session) { throw new Error(`Call session not found: ${callId}`); } const participant = session.participants.get(userId); if (!participant) { throw new Error(`Participant not found: ${userId}`); } participant.status = status; participant.lastUpdated = new Date(); // Update metadata Object.assign(participant, metadata); logger.info(`[COMPLETE] Participant status updated: ${userId} -> ${status}`); return participant; } catch (error) { logger.error('[FAILED] Failed to update participant status:', error); throw error; } } /** * Mute/unmute participant */ toggleParticipantMute(callId, userId, muted) { try { return this.updateParticipantStatus(callId, userId, 'connected', { muted }); } catch (error) { logger.error('[FAILED] Failed to toggle participant mute:', error); throw error; } } /** * Get call session details */ getCallSession(callId) { const session = this.activeCalls.get(callId); if (!session) { return null; } return { callId: session.callId, status: session.status, createdAt: session.createdAt, participants: Array.from(session.participants.values()), duration: Date.now() - session.createdAt.getTime() }; } /** * Get all active calls */ getActiveCalls() { const calls = []; for (const [callId, session] of this.activeCalls) { calls.push(this.getCallSession(callId)); } return calls; } /** * End call session */ endCall(callId) { try { const session = this.activeCalls.get(callId); if (!session) { throw new Error(`Call session not found: ${callId}`); } session.status = 'ended'; session.endedAt = new Date(); // Clean up after a delay to allow final signaling setTimeout(() => { this.activeCalls.delete(callId); }, 5000); logger.info(`[COMPLETE] WebRTC call ended: ${callId}`); return session; } catch (error) { logger.error('[FAILED] Failed to end WebRTC call:', error); throw error; } } /** * Health check */ isHealthy() { return this.isInitialized; } /** * Get service statistics */ getStats() { return { activeCalls: this.activeCalls.size, totalParticipants: Array.from(this.activeCalls.values()) .reduce((total, session) => total + session.participants.size, 0), iceServers: this.iceServers.length }; } /** * Cleanup resources */ async cleanup() { try { // End all active calls for (const callId of this.activeCalls.keys()) { this.endCall(callId); } this.activeCalls.clear(); this.isInitialized = false; logger.info('[COMPLETE] WebRTC Service cleanup completed'); } catch (error) { logger.error('[FAILED] WebRTC Service cleanup failed:', error); throw error; } } /** * Create emergency call session with priority handling */ createEmergencyCallSession(emergencyCallId, customerId, agentId, options = {}) { try { const callId = `emergency_${emergencyCallId}_${Date.now()}`; const participants = [ { userId: customerId, socketId: options.customerSocketId || null, role: 'customer' }, { userId: agentId, socketId: options.agentSocketId || null, role: 'agent' } ]; const emergencyOptions = { isEmergencyCall: true, emergencyCallId, priority: options.priority || 'emergency', urgencyLevel: options.urgencyLevel || 'high', recordingEnabled: true, transcriptionEnabled: true }; const session = this.createCallSession(callId, participants, emergencyOptions); // Set emergency-specific configurations session.emergencyConfig = { autoRecord: true, autoTranscribe: true, priorityRouting: true, supervisorNotification: options.urgencyLevel === 'critical', maxDuration: 3600000, // 1 hour max for emergency calls qualityMonitoring: true }; logger.info(` Emergency WebRTC session created`, { callId, emergencyCallId, customerId, agentId, urgencyLevel: options.urgencyLevel }); return { sessionId: callId, callId, emergencyCallId, participants: Array.from(session.participants.values()), webrtcConfig: this.getWebRTCConfig(), emergencyConfig: session.emergencyConfig }; } catch (error) { logger.error('[FAILED] Failed to create emergency call session:', error); throw error; } } /** * Handle emergency call escalation */ escalateEmergencyCall(callId, supervisorId, reason) { try { const session = this.activeCalls.get(callId); if (!session || !session.isEmergencyCall) { throw new Error(`Emergency call session not found: ${callId}`); } // Add supervisor as participant const supervisor = { userId: supervisorId, socketId: null, role: 'supervisor', status: 'connecting', joinedAt: new Date(), escalationReason: reason }; session.participants.set(supervisorId, supervisor); session.escalated = true; session.escalationTime = new Date(); session.escalationReason = reason; logger.info(` Emergency call escalated`, { callId, emergencyCallId: session.emergencyCallId, supervisorId, reason }); return { success: true, callId, supervisorId, participants: Array.from(session.participants.values()) }; } catch (error) { logger.error('[FAILED] Failed to escalate emergency call:', error); throw error; } } /** * Get emergency call statistics */ getEmergencyCallStats() { try { const emergencyCalls = Array.from(this.activeCalls.values()) .filter(session => session.isEmergencyCall); const stats = { totalEmergencyCalls: emergencyCalls.length, byUrgencyLevel: {}, byStatus: {}, averageDuration: 0, escalatedCalls: 0 }; emergencyCalls.forEach(call => { // Count by urgency level const urgency = call.urgencyLevel || 'unknown'; stats.byUrgencyLevel[urgency] = (stats.byUrgencyLevel[urgency] || 0) + 1; // Count by status stats.byStatus[call.status] = (stats.byStatus[call.status] || 0) + 1; // Count escalated calls if (call.escalated) { stats.escalatedCalls++; } // Calculate duration const duration = Date.now() - call.createdAt.getTime(); stats.averageDuration += duration; }); if (emergencyCalls.length > 0) { stats.averageDuration = Math.round(stats.averageDuration / emergencyCalls.length); } return stats; } catch (error) { logger.error('[FAILED] Failed to get emergency call stats:', error); return { totalEmergencyCalls: 0, byUrgencyLevel: {}, byStatus: {}, averageDuration: 0, escalatedCalls: 0, error: error.message }; } } /** * End emergency call with proper cleanup */ endEmergencyCall(callId, reason = 'Call ended') { try { const session = this.activeCalls.get(callId); if (!session || !session.isEmergencyCall) { throw new Error(`Emergency call session not found: ${callId}`); } // Mark call as ended session.status = 'ended'; session.endTime = new Date(); session.endReason = reason; session.duration = session.endTime.getTime() - session.createdAt.getTime(); // Log emergency call completion logger.info(` Emergency call ended`, { callId, emergencyCallId: session.emergencyCallId, duration: session.duration, reason, participantCount: session.participants.size }); // Clean up after delay to allow final signaling setTimeout(() => { this.activeCalls.delete(callId); logger.info(` Emergency call session cleaned up: ${callId}`); }, 5000); return { success: true, callId, emergencyCallId: session.emergencyCallId, duration: session.duration, endReason: reason }; } catch (error) { logger.error('[FAILED] Failed to end emergency call:', error); throw error; } } } module.exports = WebRTCService;