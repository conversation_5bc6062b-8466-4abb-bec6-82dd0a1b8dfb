/** * ============================================= * TWILIO SERVICE * Handles Twilio Voice API integration for bidirectional calls * Supports incoming/outgoing calls, recording, transcription * ============================================= */ const twilio = require('twilio'); const logger = require('../utils/logger'); const { v4: uuidv4 } = require('uuid'); class TwilioService { constructor() { this.client = null; this.accountSid = process.env.TWILIO_ACCOUNT_SID; this.authToken = process.env.TWILIO_AUTH_TOKEN; this.phoneNumber = process.env.TWILIO_PHONE_NUMBER; this.webhookUrl = process.env.TWILIO_WEBHOOK_URL; this.isInitialized = false; } /** * Initialize Twilio client */ async initialize() { try { if (!this.accountSid || !this.authToken) { throw new Error('Twilio credentials not configured'); } this.client = twilio(this.accountSid, this.authToken); // Test connection await this.client.api.accounts(this.accountSid).fetch(); this.isInitialized = true; logger.info('[COMPLETE] Twilio Service initialized successfully'); } catch (error) { logger.error('[FAILED] Twilio Service initialization failed:', error); throw error; } } /** * Make outgoing call */ async makeOutgoingCall(options) { try { const { to, from = this.phoneNumber, callId = uuidv4(), userId, priority = 'normal', recordCall = true, transcribeCall = true } = options; logger.info(` Making outgoing call: ${from} -> ${to}`); const call = await this.client.calls.create({ to, from, url: `${this.webhookUrl}/twiml/outgoing`, statusCallback: `${this.webhookUrl}/status`, statusCallbackEvent: ['initiated', 'ringing', 'answered', 'completed'], statusCallbackMethod: 'POST', record: recordCall, recordingStatusCallback: `${this.webhookUrl}/recording`, transcribe: transcribeCall, transcribeCallback: `${this.webhookUrl}/transcription`, timeout: 30, machineDetection: 'Enable', machineDetectionTimeout: 5, // Custom parameters sendDigits: '', ifMachine: 'Hangup' }); // Store call metadata const callData = { twilioCallSid: call.sid, callId, userId, direction: 'outbound', from, to, status: 'initiated', priority, recordCall, transcribeCall, createdAt: new Date(), updatedAt: new Date() }; logger.info(`[COMPLETE] Outgoing call initiated: ${call.sid}`); return { call, callData }; } catch (error) { logger.error('[FAILED] Failed to make outgoing call:', error); throw error; } } /** * Handle incoming call */ async handleIncomingCall(callSid, from, to) { try { logger.info(` Handling incoming call: ${from} -> ${to}`); const callId = uuidv4(); const callData = { twilioCallSid: callSid, callId, direction: 'inbound', from, to, status: 'ringing', createdAt: new Date(), updatedAt: new Date() }; return callData; } catch (error) { logger.error('[FAILED] Failed to handle incoming call:', error); throw error; } } /** * Generate TwiML for outgoing calls */ generateOutgoingTwiML(options = {}) { const { message = "Bonjour, vous êtes en communication avec le service client Free Mobile. Veuillez patienter pendant que nous vous mettons en relation avec un conseiller.", waitMusic = true, conferenceRoom = null } = options; const twiml = new twilio.twiml.VoiceResponse(); // Play greeting message twiml.say({ voice: 'alice', language: 'fr-FR' }, message); if (conferenceRoom) { // Join conference for agent handoff twiml.dial().conference({ startConferenceOnEnter: true, endConferenceOnExit: false, waitUrl: waitMusic ? 'http://com.twilio.music.classical.s3.amazonaws.com/BusyStrings.wav' : '', statusCallback: `${this.webhookUrl}/conference-status`, statusCallbackEvent: 'start end join leave mute hold' }, conferenceRoom); } else { // Hold with music if (waitMusic) { twiml.play('http://com.twilio.music.classical.s3.amazonaws.com/BusyStrings.wav'); } twiml.pause({ length: 30 }); twiml.redirect(`${this.webhookUrl}/twiml/outgoing`); } return twiml.toString(); } /** * Generate TwiML for incoming calls */ generateIncomingTwiML(options = {}) { const { queueName = 'support-queue', waitMessage = "Merci d'appeler Free Mobile. Tous nos conseillers sont actuellement occupés. Veuillez patienter, nous allons vous répondre dans les plus brefs délais.", waitMusic = true, maxWaitTime = 300 // 5 minutes } = options; const twiml = new twilio.twiml.VoiceResponse(); // Welcome message twiml.say({ voice: 'alice', language: 'fr-FR' }, waitMessage); // Enqueue the call const enqueue = twiml.enqueue({ waitUrl: `${this.webhookUrl}/twiml/wait-music`, waitUrlMethod: 'POST', action: `${this.webhookUrl}/twiml/queue-result`, method: 'POST' }, queueName); return twiml.toString(); } /** * Generate wait music TwiML */ generateWaitMusicTwiML() { const twiml = new twilio.twiml.VoiceResponse(); twiml.say({ voice: 'alice', language: 'fr-FR' }, 'Veuillez patienter, un conseiller va vous répondre.'); twiml.play('http://com.twilio.music.classical.s3.amazonaws.com/BusyStrings.wav'); return twiml.toString(); } /** * Transfer call to agent */ async transferCall(callSid, agentNumber, conferenceRoom) { try { logger.info(` Transferring call ${callSid} to agent ${agentNumber}`); // Update call with new TwiML const call = await this.client.calls(callSid).update({ url: `${this.webhookUrl}/twiml/transfer?agent=${agentNumber}&conference=${conferenceRoom}`, method: 'POST' }); logger.info(`[COMPLETE] Call transfer initiated: ${call.sid}`); return call; } catch (error) { logger.error('[FAILED] Failed to transfer call:', error); throw error; } } /** * End call */ async endCall(callSid) { try { logger.info(` Ending call: ${callSid}`); const call = await this.client.calls(callSid).update({ status: 'completed' }); logger.info(`[COMPLETE] Call ended: ${call.sid}`); return call; } catch (error) { logger.error('[FAILED] Failed to end call:', error); throw error; } } /** * Get call details */ async getCallDetails(callSid) { try { const call = await this.client.calls(callSid).fetch(); return call; } catch (error) { logger.error('[FAILED] Failed to get call details:', error); throw error; } } /** * Get call recordings */ async getCallRecordings(callSid) { try { const recordings = await this.client.recordings.list({ callSid: callSid }); return recordings; } catch (error) { logger.error('[FAILED] Failed to get call recordings:', error); throw error; } } /** * Create conference room */ async createConference(friendlyName, options = {}) { try { const conference = await this.client.conferences.create({ friendlyName, statusCallback: `${this.webhookUrl}/conference-status`, statusCallbackEvent: ['start', 'end', 'join', 'leave'], ...options }); logger.info(`[COMPLETE] Conference created: ${conference.sid}`); return conference; } catch (error) { logger.error('[FAILED] Failed to create conference:', error); throw error; } } /** * Health check */ isHealthy() { return this.isInitialized && this.client !== null; } /** * Cleanup resources */ async cleanup() { try { this.client = null; this.isInitialized = false; logger.info('[COMPLETE] Twilio Service cleanup completed'); } catch (error) { logger.error('[FAILED] Twilio Service cleanup failed:', error); throw error; } } } module.exports = TwilioService;