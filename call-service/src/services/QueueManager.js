/** * ============================================= * QUEUE MANAGER SERVICE * Intelligent call queue management with priority routing * Handles agent availability, wait times, and call distribution * ============================================= */ const Redis = require('redis'); const logger = require('../utils/logger'); const { v4: uuidv4 } = require('uuid'); class QueueManager { constructor() { this.redis = null; this.queues = new Map(); this.agents = new Map(); this.isInitialized = false; this.queueProcessingInterval = null; } /** * Initialize Queue Manager */ async initialize() { try { // Initialize Redis connection this.redis = Redis.createClient({ url: process.env.REDIS_URL || 'redis://localhost:6379', retry_strategy: (options) => { if (options.error && options.error.code === 'ECONNREFUSED') { return new Error('Redis server connection refused'); } if (options.total_retry_time > 1000 * 60 * 60) { return new Error('Redis retry time exhausted'); } if (options.attempt > 10) { return undefined; } return Math.min(options.attempt * 100, 3000); } }); await this.redis.connect(); // Initialize default queues await this.createQueue('support-queue', { name: 'Support Client', priority: 1, maxWaitTime: 300, // 5 minutes skillsRequired: ['support'], language: 'fr' }); await this.createQueue('technical-queue', { name: 'Support Technique', priority: 2, maxWaitTime: 600, // 10 minutes skillsRequired: ['technical', 'support'], language: 'fr' }); await this.createQueue('billing-queue', { name: 'Facturation', priority: 1, maxWaitTime: 300, skillsRequired: ['billing'], language: 'fr' }); // Start queue processing this.startQueueProcessing(); this.isInitialized = true; logger.info('[COMPLETE] Queue Manager initialized successfully'); } catch (error) { logger.error('[FAILED] Queue Manager initialization failed:', error); throw error; } } /** * Create new queue */ async createQueue(queueId, config) { try { const queue = { id: queueId, name: config.name, priority: config.priority || 1, maxWaitTime: config.maxWaitTime || 300, skillsRequired: config.skillsRequired || [], language: config.language || 'fr', calls: [], stats: { totalCalls: 0, answeredCalls: 0, abandonedCalls: 0, averageWaitTime: 0, averageHandleTime: 0 }, createdAt: new Date() }; this.queues.set(queueId, queue); // Store in Redis await this.redis.hSet('queues', queueId, JSON.stringify(queue)); logger.info(`[COMPLETE] Queue created: ${queueId} - ${config.name}`); return queue; } catch (error) { logger.error('[FAILED] Failed to create queue:', error); throw error; } } /** * Add call to queue */ async enqueueCall(callData, queueId = 'support-queue') { try { const queue = this.queues.get(queueId); if (!queue) { throw new Error(`Queue not found: ${queueId}`); } const queueEntry = { callId: callData.callId, twilioCallSid: callData.twilioCallSid, from: callData.from, to: callData.to, priority: this.calculatePriority(callData), enqueuedAt: new Date(), estimatedWaitTime: this.calculateEstimatedWaitTime(queueId), customerData: callData.customerData || {}, skills: callData.skillsRequired || [] }; // Insert call in priority order const insertIndex = this.findInsertPosition(queue.calls, queueEntry.priority); queue.calls.splice(insertIndex, 0, queueEntry); // Update queue stats queue.stats.totalCalls++; // Store in Redis await this.redis.hSet('queues', queueId, JSON.stringify(queue)); await this.redis.hSet('queue_calls', callData.callId, JSON.stringify(queueEntry)); logger.info(` Call enqueued: ${callData.callId} -> ${queueId} (position: ${insertIndex + 1})`); return { queueId, position: insertIndex + 1, estimatedWaitTime: queueEntry.estimatedWaitTime, queueEntry }; } catch (error) { logger.error('[FAILED] Failed to enqueue call:', error); throw error; } } /** * Remove call from queue */ async dequeueCall(callId, reason = 'answered') { try { let removedEntry = null; let queueId = null; // Find and remove call from appropriate queue for (const [id, queue] of this.queues) { const callIndex = queue.calls.findIndex(call => call.callId === callId); if (callIndex !== -1) { removedEntry = queue.calls.splice(callIndex, 1)[0]; queueId = id; // Update stats if (reason === 'answered') { queue.stats.answeredCalls++; const waitTime = Date.now() - removedEntry.enqueuedAt.getTime(); queue.stats.averageWaitTime = this.updateAverage( queue.stats.averageWaitTime, waitTime, queue.stats.answeredCalls ); } else if (reason === 'abandoned') { queue.stats.abandonedCalls++; } // Update Redis await this.redis.hSet('queues', id, JSON.stringify(queue)); await this.redis.hDel('queue_calls', callId); break; } } if (removedEntry) { logger.info(` Call dequeued: ${callId} from ${queueId} (reason: ${reason})`); } return removedEntry; } catch (error) { logger.error('[FAILED] Failed to dequeue call:', error); throw error; } } /** * Register agent */ async registerAgent(agentData) { try { const agent = { id: agentData.id, name: agentData.name, email: agentData.email, skills: agentData.skills || ['support'], languages: agentData.languages || ['fr'], status: 'available', // available, busy, offline, break currentCall: null, maxConcurrentCalls: agentData.maxConcurrentCalls || 1, activeCalls: [], stats: { totalCalls: 0, totalTalkTime: 0, averageHandleTime: 0, customerSatisfaction: 0 }, lastActivity: new Date(), registeredAt: new Date() }; this.agents.set(agentData.id, agent); // Store in Redis await this.redis.hSet('agents', agentData.id, JSON.stringify(agent)); logger.info(`[USER] Agent registered: ${agentData.id} - ${agentData.name}`); return agent; } catch (error) { logger.error('[FAILED] Failed to register agent:', error); throw error; } } /** * Update agent status */ async updateAgentStatus(agentId, status, metadata = {}) { try { const agent = this.agents.get(agentId); if (!agent) { throw new Error(`Agent not found: ${agentId}`); } agent.status = status; agent.lastActivity = new Date(); Object.assign(agent, metadata); // Store in Redis await this.redis.hSet('agents', agentId, JSON.stringify(agent)); logger.info(`[USER] Agent status updated: ${agentId} -> ${status}`); return agent; } catch (error) { logger.error('[FAILED] Failed to update agent status:', error); throw error; } } /** * Find best available agent for call */ findBestAgent(queueEntry) { const availableAgents = Array.from(this.agents.values()).filter(agent => { return agent.status === 'available' && agent.activeCalls.length < agent.maxConcurrentCalls && this.hasRequiredSkills(agent.skills, queueEntry.skills) && agent.languages.includes('fr'); // Assuming French support }); if (availableAgents.length === 0) { return null; } // Sort by priority: skills match, experience, current load availableAgents.sort((a, b) => { const aSkillScore = this.calculateSkillScore(a.skills, queueEntry.skills); const bSkillScore = this.calculateSkillScore(b.skills, queueEntry.skills); if (aSkillScore !== bSkillScore) { return bSkillScore - aSkillScore; // Higher skill score first } // Then by experience (total calls handled) if (a.stats.totalCalls !== b.stats.totalCalls) { return b.stats.totalCalls - a.stats.totalCalls; } // Finally by current load (fewer active calls first) return a.activeCalls.length - b.activeCalls.length; }); return availableAgents[0]; } /** * Calculate call priority */ calculatePriority(callData) { let priority = 1; // Default priority // VIP customers get higher priority if (callData.customerData?.isVip) { priority += 3; } // Callback requests get higher priority if (callData.isCallback) { priority += 2; } // Escalated calls get higher priority if (callData.isEscalated) { priority += 2; } // Long wait time increases priority if (callData.previousWaitTime > 300) { // 5 minutes priority += 1; } return Math.min(priority, 5); // Max priority is 5 } /** * Calculate estimated wait time */ calculateEstimatedWaitTime(queueId) { const queue = this.queues.get(queueId); if (!queue) return 0; const availableAgents = Array.from(this.agents.values()).filter( agent => agent.status === 'available' && agent.activeCalls.length < agent.maxConcurrentCalls ); if (availableAgents.length === 0) { return queue.stats.averageHandleTime || 180; // Default 3 minutes } const callsInQueue = queue.calls.length; const averageHandleTime = queue.stats.averageHandleTime || 180; return Math.ceil((callsInQueue * averageHandleTime) / availableAgents.length); } /** * Start queue processing */ startQueueProcessing() { this.queueProcessingInterval = setInterval(async () => { try { await this.processQueues(); } catch (error) { logger.error('[FAILED] Queue processing error:', error); } }, 5000); // Process every 5 seconds logger.info(' Queue processing started'); } /** * Process all queues */ async processQueues() { for (const [queueId, queue] of this.queues) { if (queue.calls.length === 0) continue; const nextCall = queue.calls[0]; const agent = this.findBestAgent(nextCall); if (agent) { // Assign call to agent await this.assignCallToAgent(nextCall, agent, queueId); } else { // Check for abandoned calls (wait time exceeded) const waitTime = Date.now() - nextCall.enqueuedAt.getTime(); if (waitTime > queue.maxWaitTime * 1000) { await this.handleAbandonedCall(nextCall, queueId); } } } } /** * Assign call to agent */ async assignCallToAgent(queueEntry, agent, queueId) { try { // Remove from queue await this.dequeueCall(queueEntry.callId, 'answered'); // Update agent agent.status = 'busy'; agent.currentCall = queueEntry.callId; agent.activeCalls.push(queueEntry.callId); agent.stats.totalCalls++; await this.redis.hSet('agents', agent.id, JSON.stringify(agent)); logger.info(` Call assigned: ${queueEntry.callId} -> Agent ${agent.id}`); return { agent, queueEntry }; } catch (error) { logger.error('[FAILED] Failed to assign call to agent:', error); throw error; } } /** * Handle abandoned call */ async handleAbandonedCall(queueEntry, queueId) { try { await this.dequeueCall(queueEntry.callId, 'abandoned'); logger.warn(` Call abandoned: ${queueEntry.callId} (exceeded max wait time)`); } catch (error) { logger.error('[FAILED] Failed to handle abandoned call:', error); } } /** * Helper methods */ findInsertPosition(calls, priority) { for (let i = 0; i < calls.length; i++) { if (calls[i].priority < priority) { return i; } } return calls.length; } hasRequiredSkills(agentSkills, requiredSkills) { return requiredSkills.every(skill => agentSkills.includes(skill)); } calculateSkillScore(agentSkills, requiredSkills) { const matchingSkills = requiredSkills.filter(skill => agentSkills.includes(skill)); return matchingSkills.length / Math.max(requiredSkills.length, 1); } updateAverage(currentAverage, newValue, count) { return ((currentAverage * (count - 1)) + newValue) / count; } /** * Get queue statistics */ getQueueStats(queueId) { const queue = this.queues.get(queueId); if (!queue) return null; return { ...queue.stats, currentCalls: queue.calls.length, averageWaitTime: this.calculateEstimatedWaitTime(queueId) }; } /** * Get all queues */ getAllQueues() { return Array.from(this.queues.values()); } /** * Get all agents */ getAllAgents() { return Array.from(this.agents.values()); } /** * Health check */ isHealthy() { return this.isInitialized && this.redis && this.redis.isOpen; } /** * Cleanup resources */ async cleanup() { try { if (this.queueProcessingInterval) { clearInterval(this.queueProcessingInterval); } if (this.redis) { await this.redis.quit(); } this.queues.clear(); this.agents.clear(); this.isInitialized = false; logger.info('[COMPLETE] Queue Manager cleanup completed'); } catch (error) { logger.error('[FAILED] Queue Manager cleanup failed:', error); throw error; } } } module.exports = QueueManager;