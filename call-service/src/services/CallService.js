/** * ============================================= * CALL SERVICE * Main call management service * Handles call lifecycle, database operations, and coordination * ============================================= */ const { MongoClient } = require('mongodb'); const logger = require('../utils/logger'); const { v4: uuidv4 } = require('uuid'); class CallService { constructor() { this.db = null; this.client = null; this.isInitialized = false; } /** * Initialize Call Service */ async initialize() { try { // Connect to MongoDB this.client = new MongoClient(process.env.MONGODB_URI); await this.client.connect(); this.db = this.client.db(); // Create indexes for better performance await this.createIndexes(); this.isInitialized = true; logger.info('[COMPLETE] Call Service initialized successfully'); } catch (error) { logger.error('[FAILED] Call Service initialization failed:', error); throw error; } } /** * Create database indexes */ async createIndexes() { try { const callsCollection = this.db.collection('calls'); // Create indexes for better query performance await callsCollection.createIndex({ callId: 1 }, { unique: true }); await callsCollection.createIndex({ twilioCallSid: 1 }); await callsCollection.createIndex({ userId: 1 }); await callsCollection.createIndex({ status: 1 }); await callsCollection.createIndex({ direction: 1 }); await callsCollection.createIndex({ createdAt: -1 }); await callsCollection.createIndex({ userId: 1, createdAt: -1 }); logger.info('[COMPLETE] Database indexes created successfully'); } catch (error) { logger.error('[FAILED] Failed to create database indexes:', error); throw error; } } /** * Create new call record */ async createCall(callData) { try { const call = { ...callData, createdAt: new Date(), updatedAt: new Date() }; const result = await this.db.collection('calls').insertOne(call); logger.info(`[COMPLETE] Call record created: ${callData.callId}`); return { ...call, _id: result.insertedId }; } catch (error) { logger.error('[FAILED] Failed to create call record:', error); throw error; } } /** * Update call record */ async updateCall(callId, updateData) { try { const update = { ...updateData, updatedAt: new Date() }; const result = await this.db.collection('calls').updateOne( { callId }, { $set: update } ); if (result.matchedCount === 0) { throw new Error(`Call not found: ${callId}`); } logger.info(`[COMPLETE] Call updated: ${callId}`); return result; } catch (error) { logger.error('[FAILED] Failed to update call:', error); throw error; } } /** * Get call by ID */ async getCall(callId) { try { const call = await this.db.collection('calls').findOne({ callId }); return call; } catch (error) { logger.error('[FAILED] Failed to get call:', error); throw error; } } /** * Get calls with filters */ async getCalls(filters = {}, options = {}) { try { const { limit = 20, offset = 0 } = options; const query = {}; if (filters.status) query.status = filters.status; if (filters.direction) query.direction = filters.direction; if (filters.userId) query.userId = filters.userId; const calls = await this.db.collection('calls') .find(query) .sort({ createdAt: -1 }) .skip(offset) .limit(limit) .toArray(); return calls; } catch (error) { logger.error('[FAILED] Failed to get calls:', error); throw error; } } /** * End call */ async endCall(callId, reason = 'completed') { try { const endTime = new Date(); const call = await this.getCall(callId); if (!call) { throw new Error(`Call not found: ${callId}`); } // Calculate duration const duration = Math.floor((endTime - new Date(call.createdAt)) / 1000); const updateData = { status: 'completed', endTime, duration, endReason: reason }; await this.updateCall(callId, updateData); logger.info(`[COMPLETE] Call ended: ${callId} (${reason})`); return { callId, endTime, duration, reason }; } catch (error) { logger.error('[FAILED] Failed to end call:', error); throw error; } } /** * Transfer call to another agent */ async transferCall(callId, targetAgentId, reason = 'transfer') { try { const call = await this.getCall(callId); if (!call) { throw new Error(`Call not found: ${callId}`); } if (call.status !== 'connected') { throw new Error(`Cannot transfer call in status: ${call.status}`); } const updateData = { previousAgentId: call.agentId, agentId: targetAgentId, transferredAt: new Date(), transferReason: reason, status: 'transferring' }; await this.updateCall(callId, updateData); logger.info(` Call transferred: ${callId} -> Agent ${targetAgentId}`); return { callId, targetAgentId, transferredAt: updateData.transferredAt }; } catch (error) { logger.error('[FAILED] Failed to transfer call:', error); throw error; } } /** * Get call statistics */ async getCallStats(filters = {}) { try { const pipeline = []; // Match filters if (Object.keys(filters).length > 0) { pipeline.push({ $match: filters }); } // Group and calculate stats pipeline.push({ $group: { _id: null, totalCalls: { $sum: 1 }, completedCalls: { $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] } }, failedCalls: { $sum: { $cond: [{ $eq: ['$status', 'failed'] }, 1, 0] } }, averageDuration: { $avg: '$duration' }, totalDuration: { $sum: '$duration' } } }); const result = await this.db.collection('calls') .aggregate(pipeline) .toArray(); return result[0] || { totalCalls: 0, completedCalls: 0, failedCalls: 0, averageDuration: 0, totalDuration: 0 }; } catch (error) { logger.error('[FAILED] Failed to get call stats:', error); throw error; } } /** * Get active calls count */ async getActiveCallsCount() { try { const count = await this.db.collection('calls').countDocuments({ status: { $in: ['connecting', 'ringing', 'connected'] } }); return count; } catch (error) { logger.error('[FAILED] Failed to get active calls count:', error); throw error; } } /** * Health check */ isHealthy() { return this.isInitialized && this.client && this.db; } /** * Cleanup resources */ async cleanup() { try { if (this.client) { await this.client.close(); } this.db = null; this.client = null; this.isInitialized = false; logger.info('[COMPLETE] Call Service cleanup completed'); } catch (error) { logger.error('[FAILED] Call Service cleanup failed:', error); throw error; } } } module.exports = CallService;