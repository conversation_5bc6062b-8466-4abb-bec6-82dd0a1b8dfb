# =============================================
# 📞 CALL SERVICE DOCKERFILE
# Multi-stage build for Free Mobile Call Service
# Optimized for development and production
# =============================================

# Base image with Node.js 18 LTS
FROM node:18-alpine AS base

# Install system dependencies
RUN apk add --no-cache \
    curl \
    dumb-init \
    && rm -rf /var/cache/apk/*

# Create app directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# =============================================
# DEVELOPMENT DEPENDENCIES STAGE
# =============================================
FROM base AS dev-dependencies

# Install all dependencies (including dev dependencies)
RUN npm ci --include=dev

# Copy source code
COPY . .

# Create logs directory
RUN mkdir -p /logs && chown -R node:node /logs

# Switch to non-root user
USER node

# Expose port
EXPOSE 5004

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:5004/health || exit 1

# Development command with nodemon
CMD ["dumb-init", "npm", "run", "dev"]

# =============================================
# PRODUCTION DEPENDENCIES STAGE
# =============================================
FROM base AS prod-dependencies

# Set production environment
ENV NODE_ENV=production

# Install only production dependencies
RUN npm ci --only=production && npm cache clean --force

# =============================================
# PRODUCTION BUILD STAGE
# =============================================
FROM base AS production

# Set production environment
ENV NODE_ENV=production

# Copy production dependencies
COPY --from=prod-dependencies /app/node_modules ./node_modules

# Copy source code
COPY . .

# Create logs directory
RUN mkdir -p /logs && chown -R node:node /logs

# Switch to non-root user
USER node

# Expose port
EXPOSE 5004

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:5004/health || exit 1

# Production command
CMD ["dumb-init", "npm", "start"]

# =============================================
# DEFAULT TARGET (DEVELOPMENT)
# =============================================
FROM dev-dependencies AS default
