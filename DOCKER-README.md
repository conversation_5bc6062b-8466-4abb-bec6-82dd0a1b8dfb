# Guide Docker Complet - Chatbot Free Mobile ## Vue d'ensemble Ce guide détaille l'utilisation de Docker pour déployer le Chatbot Free Mobile dans différents environnements. L'architecture Docker est optimisée pour la performance, la sécurité et la facilité de déploiement. ## [ARCHITECTURE] Architecture Docker ``` ┌─────────────────────────────────────────────────────────────┐ │ STACK DOCKER │ ├─────────────────────────────────────────────────────────────┤ │ Frontend (React + Nginx) Port: 3000 │ │ [DEPLOY] Backend (Node.js + Express) Port: 5000 │ │ [AI] Rasa (Python + ML) Port: 5005 │ │ MongoDB (Base de données) Port: 27017 │ │ Redis (Cache) Port: 6379 │ │ [ANALYTICS] Adminer (Dev uniquement) Port: 8080 │ │ [METRICS] Redis Commander (Dev) Port: 8081 │ └─────────────────────────────────────────────────────────────┘ ``` ## [DEPLOY] Démarrage Rapide ### 1. Prérequis ```bash # Vérifier Docker docker --version # >= 20.10.0 docker-compose --version # >= 2.0.0 # Vérifier que Docker fonctionne docker info ``` ### 2. Configuration Initiale ```bash # Cloner le projet git clone https://github.com/Anderson-Archimede/ChatbotRNCP.git cd ChatbotRNCP/free-mobile-chatbot # Copier le template d'environnement cp env.template .env # Éditer les variables d'environnement nano .env # ou votre éditeur préféré ``` ### 3. Lancement Développement ```bash # Démarrage automatique avec le script ./deploy-docker.sh dev # OU manuellement docker-compose up -d ``` ### 4. Lancement Production ```bash # Démarrage production avec le script ./deploy-docker.sh prod # OU manuellement docker-compose -f docker-compose.prod.yml up -d ``` ## Structure des Fichiers Docker ``` free-mobile-chatbot/ ├── Docker Configuration │ ├── docker-compose.yml # Environnement développement │ ├── docker-compose.prod.yml # Environnement production │ ├── deploy-docker.sh # Script de déploiement intelligent │ └── env.template # Template variables environnement │ ├── Frontend Docker │ ├── Dockerfile # Multi-stage React + Nginx │ ├── nginx.conf # Configuration Nginx optimisée │ └── .dockerignore # Exclusions build │ ├── [DEPLOY] Backend Docker │ ├── Dockerfile # Multi-stage Node.js │ ├── docker-healthcheck.js # Script de santé │ └── .dockerignore # Exclusions build │ ├── [AI] Rasa Docker │ ├── Dockerfile # Multi-stage Python + Rasa │ ├── credentials.yml # Configuration canaux │ ├── endpoints.yml # Configuration endpoints │ └── .dockerignore # Exclusions build │ └── Données ├── mongo-init-dev.js # Initialisation MongoDB dev ├── data/ # Volumes persistants ├── logs/ # Logs application └── backups/ # Sauvegardes automatiques ``` ## Commandes du Script de Déploiement ### Commandes Principales ```bash # [CONFIG] DÉVELOPPEMENT ./deploy-docker.sh dev # Démarrer environnement dev ./deploy-docker.sh status # Voir le statut des services # [DEPLOY] PRODUCTION ./deploy-docker.sh prod # Démarrer environnement prod ./deploy-docker.sh update # Mise à jour sans interruption # GESTION DES DONNÉES ./deploy-docker.sh backup # Créer sauvegarde ./deploy-docker.sh restore backup.tar.gz # Restaurer sauvegarde # [ANALYTICS] MONITORING ./deploy-docker.sh logs # Tous les logs ./deploy-docker.sh logs backend # Logs spécifique service # MAINTENANCE ./deploy-docker.sh clean # Nettoyer système Docker ``` ### Exemples d'Usage ```bash # Workflow développement typique ./deploy-docker.sh dev # Démarrer ./deploy-docker.sh logs backend # Voir logs en cas de problème ./deploy-docker.sh clean # Nettoyer avant commit # Workflow production ./deploy-docker.sh backup # Sauvegarde avant déploiement ./deploy-docker.sh prod # Déploiement production ./deploy-docker.sh status # Vérifier santé ``` ## [CONFIG] Configuration Détaillée ### Variables d'Environnement Critiques ```bash # [SECURITY] SÉCURITÉ (OBLIGATOIRE EN PRODUCTION) JWT_SECRET=votre-secret-32-caracteres-minimum MONGO_PASSWORD=mot-de-passe-securise OPENAI_API_KEY=sk-votre-cle-openai # RÉSEAU FRONTEND_URL=http://localhost:3000 # Dev FRONTEND_URL=https://votredomaine.com # Prod # BASE DE DONNÉES MONGODB_URI=************************************ REDIS_URL=redis://redis:6379 ``` ### Personnalisation des Services #### Frontend (React + Nginx) ```dockerfile # Dockerfile multi-stage optimisé FROM node:18-alpine AS builder # ... étapes de build FROM nginx:alpine AS production # Configuration Nginx avec proxy API ``` **Fonctionnalités :** - [COMPLETE] Build optimisé multi-stage - [COMPLETE] Compression Gzip automatique - [COMPLETE] Proxy API vers backend - [COMPLETE] Headers de sécurité - [COMPLETE] Cache statique intelligent #### Backend (Node.js + Express) ```dockerfile # Dockerfile avec sécurité renforcée FROM node:18-alpine AS production USER nodeuser # Utilisateur non-root HEALTHCHECK --interval=30s CMD node docker-healthcheck.js ``` **Fonctionnalités :** - [COMPLETE] Health checks avancés - [COMPLETE] Utilisateur non-root - [COMPLETE] Signal handling proper - [COMPLETE] Logs structurés #### Rasa (Python + ML) ```dockerfile # Dockerfile avec modèle pré-entraîné FROM python:3.9-slim AS trainer RUN rasa train --fixed-model-name chatbot-model FROM python:3.9-slim AS production COPY --from=trainer /app/models ./models ``` **Fonctionnalités :** - [COMPLETE] Modèle pré-entraîné - [COMPLETE] Optimisation mémoire - [COMPLETE] Persistance Redis - [COMPLETE] API endpoints sécurisés ## [ANALYTICS] Monitoring et Santé ### Health Checks Automatiques Le système surveille automatiquement : ```bash # Vérifications backend [COMPLETE] Serveur HTTP (port 5000) [COMPLETE] Connexion MongoDB [COMPLETE] Connexion Redis [COMPLETE] Utilisation mémoire # Vérifications frontend [COMPLETE] Service Nginx (port 3000) [COMPLETE] Ressources statiques [COMPLETE] Proxy API fonctionnel # Vérifications Rasa [COMPLETE] API NLP (port 5005) [COMPLETE] Modèle chargé [COMPLETE] Endpoints actifs ``` ### Logs Structurés ```bash # Voir logs en temps réel ./deploy-docker.sh logs # Logs par service ./deploy-docker.sh logs frontend ./deploy-docker.sh logs backend ./deploy-docker.sh logs rasa ./deploy-docker.sh logs mongodb ./deploy-docker.sh logs redis ``` ### Métriques de Performance ```bash # Utilisation ressources docker stats # Espace disque docker system df # Statut détaillé ./deploy-docker.sh status ``` ## Gestion des Données ### Sauvegardes Automatiques ```bash # Sauvegarde complète ./deploy-docker.sh backup # Créé: ./backups/backup_YYYYMMDD_HHMMSS.tar.gz # Contenu sauvegardé: backup_20241218_143022.tar.gz ├── mongodb/ # Export MongoDB complet ├── redis_dump.rdb # Snapshot Redis └── logs/ # Logs application ``` ### Restauration de Données ```bash # Restaurer depuis sauvegarde ./deploy-docker.sh restore ./backups/backup_20241218_143022.tar.gz # Le script: 1. [COMPLETE] Extrait la sauvegarde 2. [COMPLETE] Arrête les services si nécessaire 3. [COMPLETE] Restaure MongoDB 4. [COMPLETE] Restaure Redis 5. [COMPLETE] Redémarre les services ``` ### Volumes Persistants ```yaml # Configuration volumes volumes: mongodb_data: # Données MongoDB redis_data: # Données Redis # Montages locaux ./logs:/app/logs # Logs accessibles ./backups:/backups # Sauvegardes locales ``` ## Sécurité Docker ### Bonnes Pratiques Appliquées ```dockerfile # [COMPLETE] Utilisateurs non-root RUN adduser -S nodeuser -u 1001 USER nodeuser # [COMPLETE] Images minimales FROM node:18-alpine # Pas d'outils dev # [COMPLETE] Secrets gérés ENV JWT_SECRET=$JWT_SECRET # Pas hardcodé # [COMPLETE] Health checks HEALTHCHECK --interval=30s CMD curl -f http://localhost/health # [COMPLETE] Mise à jour sécurité RUN apk update && apk upgrade ``` ### Configuration Nginx Sécurisée ```nginx # Headers de sécurité add_header X-Frame-Options "DENY"; add_header X-Content-Type-Options "nosniff"; add_header X-XSS-Protection "1; mode=block"; # Rate limiting limit_req zone=api burst=20 nodelay; # CORS restreint add_header Access-Control-Allow-Origin "$FRONTEND_URL"; ``` ### Réseau Isolé ```yaml # Réseau privé Docker networks: chatbot-network: driver: bridge # Services isolés services: mongodb: networks: [chatbot-network] # Pas d'exposition externe sauf dev ``` ## [DEPLOY] Déploiement Production ### Optimisations Production ```yaml # docker-compose.prod.yml services: frontend: restart: always deploy: resources: limits: memory: 256M reservations: memory: 128M backend: restart: always deploy: resources: limits: memory: 512M reservations: memory: 256M ``` ### SSL/HTTPS avec Let's Encrypt ```yaml # Configuration SSL automatique nginx-proxy: image: nginxproxy/nginx-proxy environment: ENABLE_IPV6: 'true' letsencrypt: image: nginxproxy/acme-companion environment: LETSENCRYPT_EMAIL: <EMAIL> ``` ### Mise à Jour Sans Interruption ```bash # Le script gère automatiquement: ./deploy-docker.sh update # Processus: 1. Sauvegarde automatique 2. Pull nouvelles images 3. Redémarrage progressif services 4. Vérification santé 5. ↩ Rollback automatique si échec ``` ## Tests et Validation ### Tests de Santé ```bash # Test connexions curl http://localhost:3000/health # Frontend curl http://localhost:5000/health # Backend curl http://localhost:5005/status # Rasa # Test API curl -X POST http://localhost:5000/api/auth/login \ -H "Content-Type: application/json" \ -d '{"email":"<EMAIL>","password":"admin123"}' ``` ### Validation Environnement ```bash # Le script valide automatiquement: [COMPLETE] Docker installé et fonctionnel [COMPLETE] Fichiers de configuration présents [COMPLETE] Variables d'environnement définies [COMPLETE] Ports disponibles [COMPLETE] Espace disque suffisant ``` ## Dépannage ### Problèmes Fréquents #### 1. Service ne démarre pas ```bash # Vérifier logs ./deploy-docker.sh logs [service] # Vérifier configuration docker-compose config # Reconstruire image docker-compose build --no-cache [service] ``` #### 2. Problème de connexion base de données ```bash # Vérifier MongoDB docker exec -it $(docker-compose ps -q mongodb) mongo # Vérifier Redis docker exec -it $(docker-compose ps -q redis) redis-cli ping ``` #### 3. Problème de performance ```bash # Vérifier ressources docker stats # Nettoyer système ./deploy-docker.sh clean # Redémarrer services docker-compose restart ``` ### Solutions Rapides ```bash # Redémarrage complet docker-compose down && docker-compose up -d # Nettoyage complet docker system prune -af --volumes # [ANALYTICS] État détaillé docker-compose ps docker system df docker network ls ``` ## [METRICS] Optimisations Avancées ### Performance ```yaml # Optimisation mémoire Redis redis: command: redis-server --maxmemory 256mb --maxmemory-policy allkeys-lru # Optimisation MongoDB mongodb: command: mongod --wiredTigerCacheSizeGB 0.5 ``` ### Monitoring Avancé ```yaml # Prometheus + Grafana (optionnel) prometheus: image: prom/prometheus ports: ["9090:9090"] grafana: image: grafana/grafana ports: ["3001:3000"] ``` ## Support ### Ressources Utiles - **Documentation Docker :** https://docs.docker.com/ - **Docker Compose :** https://docs.docker.com/compose/ - **Logs :** `./logs/` - **Sauvegardes :** `./backups/` ### Contact - **Repository :** https://github.com/Anderson-Archimede/ChatbotRNCP - **Issues :** GitHub Issues - **Email :** <EMAIL> --- ## Conclusion Cette configuration Docker offre : - [COMPLETE] **Déploiement simplifié** avec un seul script - [COMPLETE] **Environnements séparés** dev/prod - [COMPLETE] **Sauvegardes automatiques** et restauration - [COMPLETE] **Monitoring intégré** et health checks - [COMPLETE] **Sécurité renforcée** et bonnes pratiques - [COMPLETE] **Performance optimisée** et scalabilité - [COMPLETE] **Maintenance facilitée** et mises à jour **Le Chatbot Free Mobile est maintenant prêt pour tous les environnements !** [DEPLOY]