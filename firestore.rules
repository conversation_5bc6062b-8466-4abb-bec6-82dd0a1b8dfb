rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function getUserRole() {
      return get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role;
    }
    
    function isAdmin() {
      return isAuthenticated() && getUserRole() == 'admin';
    }
    
    function isAgent() {
      return isAuthenticated() && getUserRole() in ['admin', 'agent'];
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isValidUser() {
      return isAuthenticated() && exists(/databases/$(database)/documents/users/$(request.auth.uid));
    }
    
    // Users collection
    match /users/{userId} {
      // Users can read/write their own data
      allow read, write: if isOwner(userId);
      
      // Admins and agents can read user data
      allow read: if isAgent();
      
      // Only admins can create/delete users
      allow create, delete: if isAdmin();
      
      // Validate user data structure
      allow write: if isOwner(userId) && 
        request.resource.data.keys().hasAll(['email', 'role', 'profile', 'preferences', 'metadata']) &&
        request.resource.data.role in ['user', 'agent', 'admin'] &&
        request.resource.data.email is string &&
        request.resource.data.profile is map &&
        request.resource.data.preferences is map;
    }
    
    // Conversations collection
    match /conversations/{conversationId} {
      // Users can access their own conversations
      allow read, write: if isAuthenticated() && (
        resource.data.userId == request.auth.uid ||
        isAgent()
      );
      
      // Agents can create conversations for users
      allow create: if isAgent() || (
        isAuthenticated() && 
        request.resource.data.userId == request.auth.uid
      );
      
      // Validate conversation data
      allow write: if request.resource.data.keys().hasAll(['userId', 'sessionId', 'status', 'channel']) &&
        request.resource.data.status in ['active', 'resolved', 'escalated', 'abandoned'] &&
        request.resource.data.channel in ['web', 'mobile', 'voice'];
    }
    
    // Messages collection
    match /messages/{messageId} {
      // Users can access messages from their conversations
      allow read, write: if isAuthenticated() && (
        // Check if user owns the conversation
        exists(/databases/$(database)/documents/conversations/$(resource.data.conversationId)) &&
        get(/databases/$(database)/documents/conversations/$(resource.data.conversationId)).data.userId == request.auth.uid ||
        // Or if user is an agent
        isAgent()
      );
      
      // Validate message data
      allow write: if request.resource.data.keys().hasAll(['conversationId', 'sender', 'content', 'timing']) &&
        request.resource.data.sender in ['user', 'bot', 'agent'] &&
        request.resource.data.content is map &&
        request.resource.data.timing is map;
    }
    
    // Notifications collection
    match /notifications/{notificationId} {
      // Users can read their own notifications
      allow read: if isAuthenticated() && resource.data.userId == request.auth.uid;
      
      // Users can update notification status (mark as read)
      allow update: if isAuthenticated() && 
        resource.data.userId == request.auth.uid &&
        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['status', 'timing']);
      
      // Agents and admins can create notifications
      allow create: if isAgent();
      
      // Admins can delete notifications
      allow delete: if isAdmin();
    }
    
    // Analytics collection - Admin only
    match /analytics/{analyticsId} {
      allow read, write: if isAdmin();
    }
    
    // System configuration - Admin only
    match /config/{configId} {
      allow read: if isValidUser();
      allow write: if isAdmin();
    }
    
    // ML predictions and insights - Agent and Admin access
    match /ml_insights/{insightId} {
      allow read: if isAgent();
      allow write: if isAdmin();
    }
    
    // Customer profiles - Agent and Admin access
    match /customer_profiles/{profileId} {
      allow read: if isAgent() || (
        isAuthenticated() && 
        resource.data.userId == request.auth.uid
      );
      allow write: if isAgent();
    }
    
    // Audit logs - Admin only
    match /audit_logs/{logId} {
      allow read: if isAdmin();
      allow write: if false; // Logs are write-only via server
    }
    
    // Performance metrics - Admin only
    match /performance_metrics/{metricId} {
      allow read: if isAdmin();
      allow write: if false; // Metrics are write-only via server
    }
  }
}
