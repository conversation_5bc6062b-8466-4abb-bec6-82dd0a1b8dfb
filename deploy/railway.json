{"$schema": "https://schema.railway.app/railway.schema.json", "build": {"builder": "NIXPACKS", "buildCommand": "npm run build:production"}, "deploy": {"numReplicas": 1, "restartPolicyType": "ON_FAILURE", "sleepApplication": false, "cronSchedule": null}, "services": [{"name": "backend", "source": {"type": "local", "path": "./backend"}, "build": {"dockerfilePath": "Dockerfile.prod"}, "variables": {"NODE_ENV": "production", "PORT": "5000", "MONGODB_URI": "${{MongoDB.DATABASE_URL}}", "REDIS_URL": "${{Redis.REDIS_URL}}", "JWT_SECRET": "${{secrets.JWT_SECRET}}", "OPENAI_API_KEY": "${{secrets.OPENAI_API_KEY}}", "FRONTEND_URL": "https://chatbot-free-mobile.vercel.app", "RASA_URL": "${{Rasa.RASA_URL}}"}, "domains": [{"domain": "api.chatbot-free-mobile.com"}]}, {"name": "rasa", "source": {"type": "local", "path": "./rasa"}, "build": {"dockerfilePath": "Dockerfile.prod"}, "variables": {"PORT": "5005"}, "domains": [{"domain": "rasa.chatbot-free-mobile.com"}]}, {"name": "mongodb", "source": {"type": "image", "image": "mongo:6.0"}, "variables": {"MONGO_INITDB_ROOT_USERNAME": "freemobile", "MONGO_INITDB_ROOT_PASSWORD": "${{secrets.MONGO_PASSWORD}}", "MONGO_INITDB_DATABASE": "freemobile-chatbot"}, "volumes": [{"name": "mongodb-data", "path": "/data/db"}]}, {"name": "redis", "source": {"type": "image", "image": "redis:7-alpine"}, "variables": {"REDIS_PASSWORD": "${{secrets.REDIS_PASSWORD}}"}, "volumes": [{"name": "redis-data", "path": "/data"}]}], "plugins": [{"name": "monitoring", "source": {"type": "github", "repo": "railwayapp/monitoring-plugin"}}], "environments": {"production": {"variables": {"LOG_LEVEL": "info"}}, "staging": {"variables": {"LOG_LEVEL": "debug"}}}}