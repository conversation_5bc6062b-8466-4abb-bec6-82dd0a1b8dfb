"""
Tests pour le service de classification ML
Free Mobile Chatbot Dashboard - Tests unitaires et d'intégration
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock

from app.services.classification_service import MLClassificationService
from app.models.types import (
    ClassificationRequest, Message, CustomerProfile, ConversationMetadata,
    ConversationCategory, SentimentTrend
)


@pytest.fixture
async def ml_service():
    """Fixture pour le service ML"""
    service = MLClassificationService()
    
    # Mock des modèles pour les tests
    service.models = {
        "intent": Mock(),
        "sentiment": Mock(),
        "churn": Mock(),
        "opportunity": Mock()
    }
    
    service.tokenizers = {
        "intent": Mock(),
        "sentiment": Mock()
    }
    
    service.scalers = {
        "churn": Mock()
    }
    
    service.model_versions = {
        "intent": "1.0.0",
        "sentiment": "1.0.0",
        "churn": "2.1.0",
        "opportunity": "1.5.0"
    }
    
    return service


@pytest.fixture
def sample_messages():
    """Messages d'exemple pour les tests"""
    return [
        Message(
            id="msg-1",
            sender="user",
            content="Bonjour, je veux résilier mon abonnement",
            timestamp=datetime.utcnow()
        ),
        Message(
            id="msg-2",
            sender="bot",
            content="Je comprends votre demande. Puis-je connaître la raison?",
            timestamp=datetime.utcnow()
        ),
        Message(
            id="msg-3",
            sender="user",
            content="C'est trop cher et le service ne marche pas bien",
            timestamp=datetime.utcnow()
        )
    ]


@pytest.fixture
def sample_customer():
    """Profil client d'exemple"""
    return CustomerProfile(
        customer_id="cust-123",
        plan_type="20€",
        monthly_revenue=20.0,
        tenure_months=18,
        support_tickets_count=3,
        satisfaction_score=2.5,
        lifetime_value=360.0
    )


@pytest.fixture
def sample_metadata():
    """Métadonnées de conversation d'exemple"""
    return ConversationMetadata(
        conversation_id="conv-456",
        channel="web",
        duration_minutes=5,
        previous_interactions=2
    )


@pytest.fixture
def classification_request(sample_messages, sample_customer, sample_metadata):
    """Requête de classification complète"""
    return ClassificationRequest(
        conversation_id="conv-456",
        messages=sample_messages,
        customer_data=sample_customer,
        conversation_metadata=sample_metadata
    )


class TestMLClassificationService:
    """Tests pour le service de classification ML"""
    
    @pytest.mark.asyncio
    async def test_classify_intent_resiliation(self, ml_service, sample_messages):
        """Test de classification d'intention - résiliation"""
        
        # Mock du modèle d'intention
        mock_outputs = Mock()
        mock_outputs.logits = Mock()
        mock_outputs.logits.shape = (1, 5)  # 5 catégories
        
        # Simulation d'une prédiction "RESILIATION_CRITIQUE" (index 1)
        import torch
        mock_logits = torch.tensor([[0.1, 0.8, 0.05, 0.03, 0.02]])
        mock_outputs.logits = mock_logits
        
        ml_service.models["intent"].return_value = mock_outputs
        ml_service.tokenizers["intent"].return_value = {
            "input_ids": torch.tensor([[1, 2, 3]]),
            "attention_mask": torch.tensor([[1, 1, 1]])
        }
        
        # Extraction des features textuelles
        text_features = {"combined_text": "je veux résilier mon abonnement"}
        
        # Test de classification
        result = await ml_service._classify_intent(text_features)
        
        # Vérifications
        assert result["category"] == ConversationCategory.RESILIATION_CRITIQUE
        assert result["confidence"] > 0.7
        assert len(result["probabilities"]) == 5
    
    @pytest.mark.asyncio
    async def test_analyze_sentiment_negative(self, ml_service):
        """Test d'analyse de sentiment négatif"""
        
        # Mock du modèle de sentiment
        mock_outputs = Mock()
        import torch
        # Simulation sentiment très négatif (index 0 = très négatif)
        mock_logits = torch.tensor([[0.7, 0.2, 0.05, 0.03, 0.02]])
        mock_outputs.logits = mock_logits
        
        ml_service.models["sentiment"].return_value = mock_outputs
        ml_service.tokenizers["sentiment"].return_value = {
            "input_ids": torch.tensor([[1, 2, 3]]),
            "attention_mask": torch.tensor([[1, 1, 1]])
        }
        
        text_features = {"combined_text": "c'est nul, ça ne marche jamais"}
        
        result = await ml_service._analyze_sentiment(text_features)
        
        # Vérifications
        assert result["analysis"].score < -0.5  # Sentiment négatif
        assert result["analysis"].trend == SentimentTrend.DECLINING
        assert result["confidence"] > 0.6
    
    @pytest.mark.asyncio
    async def test_predict_churn_high_risk(self, ml_service):
        """Test de prédiction de churn - risque élevé"""
        
        # Mock du modèle de churn
        ml_service.models["churn"].predict_proba.return_value = [[0.2, 0.8]]  # 80% de risque
        ml_service.scalers["churn"].fit_transform.return_value = [[0.5, 0.8, 0.3, 0.6, 0.4]]
        
        customer_features = {
            "tenure_months": 3,  # Nouveau client
            "support_tickets_count": 5,  # Beaucoup de tickets
            "satisfaction_score": 2.0,  # Satisfaction faible
            "monthly_revenue": 8.0,  # Client low-cost
            "recent_sentiment_trend": -0.6  # Sentiment dégradé
        }
        
        result = await ml_service._predict_churn(customer_features)
        
        # Vérifications
        assert result["churn_probability"] > 0.7
        assert result["risk_level"] == "HIGH"
        assert result["confidence"] > 0.5
    
    @pytest.mark.asyncio
    async def test_score_opportunities_upgrade(self, ml_service, sample_customer):
        """Test de scoring d'opportunités - upgrade"""
        
        # Client avec forfait bas de gamme = opportunité d'upgrade
        low_value_customer = CustomerProfile(
            customer_id="cust-low",
            plan_type="2€",
            monthly_revenue=2.0,
            tenure_months=6,
            support_tickets_count=1,
            satisfaction_score=4.0,
            lifetime_value=48.0
        )
        
        features = {"text_features": {"combined_text": "plus de data"}}
        
        opportunities = await ml_service._score_opportunities(low_value_customer, features)
        
        # Vérifications
        assert len(opportunities) > 0
        upgrade_opp = next((opp for opp in opportunities if opp["type"] == "UPGRADE_MOBILE"), None)
        assert upgrade_opp is not None
        assert upgrade_opp["confidence"] > 0.5
        assert upgrade_opp["value"] > 0
    
    @pytest.mark.asyncio
    async def test_calculate_business_priority_critical(self, ml_service, sample_customer):
        """Test de calcul de priorité business - cas critique"""
        
        # Résultats simulés pour un cas critique
        intent_result = {
            "category": ConversationCategory.RESILIATION_CRITIQUE,
            "confidence": 0.9
        }
        
        sentiment_result = {
            "analysis": Mock(score=-0.8),  # Très négatif
            "confidence": 0.85
        }
        
        churn_result = {
            "churn_probability": 0.85,  # Risque élevé
            "confidence": 0.8
        }
        
        # Client à forte valeur
        high_value_customer = CustomerProfile(
            customer_id="cust-premium",
            plan_type="50€",
            monthly_revenue=50.0,
            tenure_months=24,
            support_tickets_count=8,  # Beaucoup de tickets
            lifetime_value=1200.0
        )
        
        priority = ml_service._calculate_business_priority(
            intent_result, sentiment_result, churn_result, high_value_customer
        )
        
        # Vérifications - doit être très prioritaire
        assert priority >= 80  # Priorité très élevée
        assert priority <= 100
    
    @pytest.mark.asyncio
    async def test_full_classification_workflow(self, ml_service, classification_request):
        """Test du workflow complet de classification"""
        
        # Mock de tous les composants
        with patch.object(ml_service, '_classify_intent') as mock_intent, \
             patch.object(ml_service, '_analyze_sentiment') as mock_sentiment, \
             patch.object(ml_service, '_predict_churn') as mock_churn, \
             patch.object(ml_service, '_score_opportunities') as mock_opportunities:
            
            # Configuration des mocks
            mock_intent.return_value = {
                "category": ConversationCategory.RESILIATION_CRITIQUE,
                "confidence": 0.9
            }
            
            mock_sentiment.return_value = {
                "analysis": Mock(score=-0.6, trend=SentimentTrend.DECLINING, confidence=0.8, key_emotions=["frustration"]),
                "confidence": 0.8
            }
            
            mock_churn.return_value = {
                "churn_probability": 0.75,
                "confidence": 0.85,
                "risk_level": "HIGH"
            }
            
            mock_opportunities.return_value = []
            
            # Mock du feature engineer
            with patch.object(ml_service.feature_engineer, 'extract_features') as mock_features:
                mock_features.return_value = {
                    "text_features": {"combined_text": "test"},
                    "customer_features": {"tenure_months": 18},
                    "conversation_features": {"total_messages": 3},
                    "temporal_features": {"hour_of_day": 14},
                    "linguistic_features": {"linguistic_features_available": True},
                    "business_features": {"wants_to_cancel": True}
                }
                
                # Exécution de la classification
                result = await ml_service.classify_conversation(classification_request)
                
                # Vérifications
                assert result.conversation_id == "conv-456"
                assert result.category == ConversationCategory.RESILIATION_CRITIQUE
                assert result.priority_score > 70  # Priorité élevée pour résiliation
                assert result.confidence > 0.7
                assert result.business_impact.revenue_at_risk > 0
                assert len(result.recommended_actions) > 0
                
                # Vérification des actions recommandées
                retention_action = next(
                    (action for action in result.recommended_actions 
                     if action.type.value == "RETENTION_CALL"), 
                    None
                )
                assert retention_action is not None
                assert retention_action.priority >= 8
    
    @pytest.mark.asyncio
    async def test_performance_metrics_tracking(self, ml_service):
        """Test du suivi des métriques de performance"""
        
        # Simulation de plusieurs classifications
        processing_times = [150.0, 180.0, 120.0, 200.0, 160.0]
        
        for time_ms in processing_times:
            ml_service._update_performance_metrics(time_ms)
        
        # Récupération des métriques
        metrics = ml_service.get_performance_metrics()
        
        # Vérifications
        assert metrics["total_classifications"] == 5
        assert metrics["average_processing_time_ms"] == 162.0  # Moyenne
        assert metrics["throughput_per_second"] > 0
        assert metrics["models_loaded"] == 4
    
    @pytest.mark.asyncio
    async def test_error_handling_model_failure(self, ml_service):
        """Test de gestion d'erreur - échec de modèle"""
        
        # Simulation d'une erreur dans le modèle d'intention
        ml_service.models["intent"].side_effect = Exception("Model error")
        
        text_features = {"combined_text": "test error"}
        
        # La méthode doit gérer l'erreur gracieusement
        result = await ml_service._classify_intent(text_features)
        
        # Vérifications - fallback vers catégorie par défaut
        assert result["category"] == ConversationCategory.INFO_SIMPLE
        assert result["confidence"] == 0.5  # Confiance réduite
    
    @pytest.mark.asyncio
    async def test_cache_integration(self, ml_service, classification_request):
        """Test d'intégration avec le cache"""
        
        # Mock du service de cache
        ml_service.cache_service = AsyncMock()
        ml_service.cache_service.get_classification.return_value = None  # Cache miss
        ml_service.cache_service.cache_classification.return_value = True
        
        # Mock des méthodes de classification
        with patch.object(ml_service, '_classify_conversation_internal') as mock_classify:
            mock_classification = Mock()
            mock_classification.conversation_id = "conv-456"
            mock_classification.category = ConversationCategory.INFO_SIMPLE
            mock_classification.processing_time_ms = 150.0
            
            mock_classify.return_value = mock_classification
            
            # Exécution
            result = await ml_service.classify_conversation(classification_request)
            
            # Vérifications
            assert result == mock_classification
            ml_service.cache_service.get_classification.assert_called_once_with("conv-456")
            ml_service.cache_service.cache_classification.assert_called_once()


@pytest.mark.integration
class TestMLServiceIntegration:
    """Tests d'intégration pour le service ML"""
    
    @pytest.mark.asyncio
    async def test_real_model_loading(self):
        """Test de chargement réel des modèles (si disponibles)"""
        
        service = MLClassificationService()
        
        try:
            await service.initialize()
            
            # Vérifications de base
            assert len(service.models) > 0
            assert len(service.tokenizers) > 0
            assert len(service.model_versions) > 0
            
        except Exception as e:
            # En cas d'échec (modèles non disponibles), on skip le test
            pytest.skip(f"Real models not available: {e}")
    
    @pytest.mark.asyncio
    async def test_performance_benchmark(self, ml_service, classification_request):
        """Test de performance - benchmark de vitesse"""
        
        import time
        
        # Mock rapide pour le benchmark
        with patch.object(ml_service, '_classify_conversation_internal') as mock_classify:
            mock_classify.return_value = Mock(
                conversation_id="conv-456",
                processing_time_ms=0.0
            )
            
            # Mesure du temps de traitement
            start_time = time.time()
            
            # Exécution de 100 classifications
            tasks = []
            for i in range(100):
                task = ml_service.classify_conversation(classification_request)
                tasks.append(task)
            
            results = await asyncio.gather(*tasks)
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # Vérifications de performance
            assert len(results) == 100
            assert total_time < 10.0  # Moins de 10 secondes pour 100 classifications
            
            throughput = 100 / total_time
            assert throughput > 10  # Au moins 10 classifications/seconde
