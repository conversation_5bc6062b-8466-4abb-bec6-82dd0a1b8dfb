"""
=============================================
🚀 ML MODEL MANAGER
Model deployment, versioning, and lifecycle management
Integrates with existing ML service infrastructure
=============================================
"""

import os
import json
import logging
import threading
import schedule
import time
from datetime import datetime, timedelta
from pathlib import Path
import pandas as pd
import numpy as np

from models.churn_predictor import ChurnPredictor
from models.demand_forecaster import DemandForecaster
from models.escalation_predictor import EscalationPredictor
from models.anomaly_detector import AnomalyDetector

logger = logging.getLogger(__name__)

class ModelManager:
    def __init__(self, model_dir='models', data_dir='data'):
        self.model_dir = Path(model_dir)
        self.data_dir = Path(data_dir)
        self.model_dir.mkdir(exist_ok=True)
        self.data_dir.mkdir(exist_ok=True)
        
        # Initialize models
        self.models = {
            'churn_predictor': ChurnPredictor(),
            'demand_forecaster': DemandForecaster(),
            'escalation_predictor': EscalationPredictor(),
            'anomaly_detector': AnomalyDetector()
        }
        
        # Model metadata
        self.model_metadata = {}
        self.load_metadata()
        
        # Performance tracking
        self.performance_history = {}
        
        # Auto-retraining scheduler
        self.scheduler_thread = None
        self.stop_scheduler = False
        
        # Load existing models
        self.load_all_models()
        
        # Start scheduler
        self.start_scheduler()
    
    def load_metadata(self):
        """Load model metadata from file"""
        metadata_file = self.model_dir / 'metadata.json'
        if metadata_file.exists():
            with open(metadata_file, 'r') as f:
                self.model_metadata = json.load(f)
        else:
            self.model_metadata = {
                'models': {},
                'deployment_history': [],
                'performance_thresholds': {
                    'churn_predictor': 0.85,
                    'demand_forecaster': 0.80,
                    'escalation_predictor': 0.85,
                    'anomaly_detector': 0.80
                }
            }
    
    def save_metadata(self):
        """Save model metadata to file"""
        metadata_file = self.model_dir / 'metadata.json'
        with open(metadata_file, 'w') as f:
            json.dump(self.model_metadata, f, indent=2, default=str)
    
    def train_model(self, model_name, training_data, force_retrain=False):
        """
        Train a specific model with new data
        """
        try:
            if model_name not in self.models:
                raise ValueError(f"Unknown model: {model_name}")
            
            logger.info(f"Starting training for {model_name}")
            
            # Check if retraining is needed
            if not force_retrain and self._should_skip_training(model_name):
                logger.info(f"Skipping training for {model_name} - recent model exists")
                return self.get_model_info(model_name)
            
            # Prepare training data
            if isinstance(training_data, str):
                # Load from file path
                training_data = pd.read_csv(training_data)
            
            # Train the model
            model = self.models[model_name]
            
            if model_name == 'anomaly_detector':
                # Train all anomaly types
                results = {}
                anomaly_types = ['performance', 'volume', 'satisfaction', 'system']
                for anomaly_type in anomaly_types:
                    result = model.train_model(training_data, anomaly_type)
                    results[anomaly_type] = result
                training_result = results
            else:
                training_result = model.train_model(training_data)
            
            # Save the trained model
            model_path = self._get_model_path(model_name)
            if model_name == 'anomaly_detector':
                model.save_models(str(model_path.with_suffix('')))
            else:
                model.save_model(str(model_path))
            
            # Update metadata
            self._update_model_metadata(model_name, training_result)
            
            # Log deployment
            self._log_deployment(model_name, 'trained', training_result)
            
            logger.info(f"Training completed for {model_name}")
            return training_result
            
        except Exception as e:
            logger.error(f"Error training {model_name}: {e}")
            raise
    
    def deploy_model(self, model_name, version=None):
        """
        Deploy a trained model to production
        """
        try:
            if model_name not in self.models:
                raise ValueError(f"Unknown model: {model_name}")
            
            # Load the model if not already loaded
            if not self._is_model_loaded(model_name):
                self.load_model(model_name, version)
            
            # Validate model performance
            if not self._validate_model_performance(model_name):
                raise ValueError(f"Model {model_name} does not meet performance thresholds")
            
            # Mark as deployed
            self.model_metadata['models'][model_name]['status'] = 'deployed'
            self.model_metadata['models'][model_name]['deployed_at'] = datetime.now().isoformat()
            
            # Log deployment
            self._log_deployment(model_name, 'deployed')
            
            self.save_metadata()
            
            logger.info(f"Model {model_name} deployed successfully")
            
            return {
                'model_name': model_name,
                'status': 'deployed',
                'deployed_at': datetime.now().isoformat(),
                'version': self.model_metadata['models'][model_name].get('version', '1.0')
            }
            
        except Exception as e:
            logger.error(f"Error deploying {model_name}: {e}")
            raise
    
    def load_model(self, model_name, version=None):
        """
        Load a trained model from disk
        """
        try:
            if model_name not in self.models:
                raise ValueError(f"Unknown model: {model_name}")
            
            model_path = self._get_model_path(model_name, version)
            
            if not model_path.exists():
                logger.warning(f"Model file not found: {model_path}")
                return False
            
            model = self.models[model_name]
            
            if model_name == 'anomaly_detector':
                anomaly_types = ['performance', 'volume', 'satisfaction', 'system']
                model.load_models(str(model_path.with_suffix('')), anomaly_types)
            else:
                model.load_model(str(model_path))
            
            logger.info(f"Model {model_name} loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error loading {model_name}: {e}")
            return False
    
    def load_all_models(self):
        """
        Load all available trained models
        """
        for model_name in self.models.keys():
            try:
                self.load_model(model_name)
            except Exception as e:
                logger.warning(f"Could not load {model_name}: {e}")
    
    def get_model_info(self, model_name=None):
        """
        Get information about models
        """
        if model_name:
            if model_name not in self.models:
                raise ValueError(f"Unknown model: {model_name}")
            
            model_info = self.models[model_name].get_model_info()
            metadata = self.model_metadata.get('models', {}).get(model_name, {})
            
            return {
                **model_info,
                'metadata': metadata,
                'performance_history': self.performance_history.get(model_name, [])
            }
        else:
            # Return info for all models
            all_info = {}
            for name in self.models.keys():
                all_info[name] = self.get_model_info(name)
            
            return all_info
    
    def predict(self, model_name, data, **kwargs):
        """
        Make predictions using a deployed model
        """
        try:
            if model_name not in self.models:
                raise ValueError(f"Unknown model: {model_name}")
            
            if not self._is_model_loaded(model_name):
                raise ValueError(f"Model {model_name} is not loaded")
            
            model = self.models[model_name]
            
            # Make prediction based on model type
            if model_name == 'churn_predictor':
                predictions = model.predict_churn(data)
            elif model_name == 'demand_forecaster':
                predictions = model.forecast_demand(**kwargs)
            elif model_name == 'escalation_predictor':
                predictions = model.predict_escalation(data)
            elif model_name == 'anomaly_detector':
                predictions = model.detect_anomalies(data, **kwargs)
            else:
                raise ValueError(f"Prediction not implemented for {model_name}")
            
            # Log prediction for monitoring
            self._log_prediction(model_name, len(predictions) if isinstance(predictions, list) else 1)
            
            return predictions
            
        except Exception as e:
            logger.error(f"Error making prediction with {model_name}: {e}")
            raise
    
    def rollback_model(self, model_name, target_version):
        """
        Rollback model to a previous version
        """
        try:
            # Load the target version
            if self.load_model(model_name, target_version):
                # Update metadata
                self.model_metadata['models'][model_name]['status'] = 'rolled_back'
                self.model_metadata['models'][model_name]['rolled_back_at'] = datetime.now().isoformat()
                self.model_metadata['models'][model_name]['active_version'] = target_version
                
                # Log rollback
                self._log_deployment(model_name, 'rolled_back', {'target_version': target_version})
                
                self.save_metadata()
                
                logger.info(f"Model {model_name} rolled back to version {target_version}")
                return True
            else:
                raise ValueError(f"Could not load version {target_version} of {model_name}")
                
        except Exception as e:
            logger.error(f"Error rolling back {model_name}: {e}")
            raise
    
    def get_model_health(self):
        """
        Get health status of all models
        """
        health_status = {
            'overall_health': 'healthy',
            'models': {},
            'system_resources': self._get_system_resources(),
            'active_alerts': []
        }
        
        unhealthy_count = 0
        
        for model_name in self.models.keys():
            model_health = self._check_model_health(model_name)
            health_status['models'][model_name] = model_health
            
            if model_health['status'] != 'healthy':
                unhealthy_count += 1
        
        # Determine overall health
        if unhealthy_count == 0:
            health_status['overall_health'] = 'healthy'
        elif unhealthy_count <= len(self.models) / 2:
            health_status['overall_health'] = 'degraded'
        else:
            health_status['overall_health'] = 'unhealthy'
        
        return health_status
    
    def start_scheduler(self):
        """
        Start the automatic retraining scheduler
        """
        if self.scheduler_thread is not None:
            return
        
        # Schedule retraining jobs
        schedule.every().day.at("02:00").do(self._scheduled_retraining)
        schedule.every().hour.do(self._health_check)
        
        def run_scheduler():
            while not self.stop_scheduler:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
        
        self.scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
        self.scheduler_thread.start()
        
        logger.info("Model scheduler started")
    
    def stop_scheduler_service(self):
        """
        Stop the scheduler service
        """
        self.stop_scheduler = True
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=5)
        logger.info("Model scheduler stopped")
    
    def _should_skip_training(self, model_name):
        """
        Check if training should be skipped based on recent training
        """
        metadata = self.model_metadata.get('models', {}).get(model_name, {})
        last_trained = metadata.get('last_trained')
        
        if not last_trained:
            return False
        
        last_trained_dt = datetime.fromisoformat(last_trained.replace('Z', '+00:00'))
        time_since_training = datetime.now() - last_trained_dt.replace(tzinfo=None)
        
        # Skip if trained within last 6 hours
        return time_since_training < timedelta(hours=6)
    
    def _get_model_path(self, model_name, version=None):
        """
        Get the file path for a model
        """
        if version:
            filename = f"{model_name}_v{version}.joblib"
        else:
            filename = f"{model_name}.joblib"
        
        return self.model_dir / filename
    
    def _is_model_loaded(self, model_name):
        """
        Check if a model is loaded and ready
        """
        model = self.models[model_name]
        
        if model_name == 'anomaly_detector':
            return len(model.models) > 0
        else:
            return model.model is not None
    
    def _validate_model_performance(self, model_name):
        """
        Validate that model meets performance thresholds
        """
        threshold = self.model_metadata['performance_thresholds'].get(model_name, 0.8)
        model_info = self.models[model_name].get_model_info()
        
        accuracy = model_info.get('accuracy', 0)
        return accuracy >= threshold
    
    def _update_model_metadata(self, model_name, training_result):
        """
        Update model metadata after training
        """
        if 'models' not in self.model_metadata:
            self.model_metadata['models'] = {}
        
        self.model_metadata['models'][model_name] = {
            'version': self.models[model_name].model_version,
            'last_trained': datetime.now().isoformat(),
            'training_result': training_result,
            'status': 'trained'
        }
        
        self.save_metadata()
    
    def _log_deployment(self, model_name, action, details=None):
        """
        Log deployment actions
        """
        deployment_log = {
            'model_name': model_name,
            'action': action,
            'timestamp': datetime.now().isoformat(),
            'details': details or {}
        }
        
        if 'deployment_history' not in self.model_metadata:
            self.model_metadata['deployment_history'] = []
        
        self.model_metadata['deployment_history'].append(deployment_log)
        
        # Keep only last 100 entries
        self.model_metadata['deployment_history'] = self.model_metadata['deployment_history'][-100:]
        
        self.save_metadata()
    
    def _log_prediction(self, model_name, prediction_count):
        """
        Log prediction for monitoring
        """
        if model_name not in self.performance_history:
            self.performance_history[model_name] = []
        
        self.performance_history[model_name].append({
            'timestamp': datetime.now().isoformat(),
            'prediction_count': prediction_count
        })
        
        # Keep only last 1000 entries
        self.performance_history[model_name] = self.performance_history[model_name][-1000:]
    
    def _check_model_health(self, model_name):
        """
        Check health of a specific model
        """
        try:
            model_info = self.models[model_name].get_model_info()
            
            health = {
                'model_name': model_name,
                'status': 'healthy',
                'last_prediction': None,
                'error_rate': 0.0,
                'avg_response_time': 0.0,
                'issues': []
            }
            
            # Check if model is loaded
            if not self._is_model_loaded(model_name):
                health['status'] = 'unhealthy'
                health['issues'].append('Model not loaded')
            
            # Check performance
            if model_info.get('accuracy', 0) < self.model_metadata['performance_thresholds'].get(model_name, 0.8):
                health['status'] = 'degraded'
                health['issues'].append('Performance below threshold')
            
            # Check last training time
            last_trained = model_info.get('last_trained')
            if last_trained:
                last_trained_dt = datetime.fromisoformat(last_trained.replace('Z', '+00:00'))
                days_since_training = (datetime.now() - last_trained_dt.replace(tzinfo=None)).days
                
                if days_since_training > 7:
                    health['status'] = 'degraded'
                    health['issues'].append('Model needs retraining')
            
            return health
            
        except Exception as e:
            return {
                'model_name': model_name,
                'status': 'unhealthy',
                'issues': [f'Health check failed: {str(e)}']
            }
    
    def _get_system_resources(self):
        """
        Get system resource usage
        """
        try:
            import psutil
            
            return {
                'cpu_percent': psutil.cpu_percent(),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_percent': psutil.disk_usage('/').percent,
                'load_average': os.getloadavg()[0] if hasattr(os, 'getloadavg') else 0
            }
        except ImportError:
            return {
                'cpu_percent': 0,
                'memory_percent': 0,
                'disk_percent': 0,
                'load_average': 0
            }
    
    def _scheduled_retraining(self):
        """
        Perform scheduled retraining
        """
        logger.info("Starting scheduled retraining")
        
        for model_name in self.models.keys():
            try:
                # Check if retraining is needed
                if self._needs_retraining(model_name):
                    logger.info(f"Retraining {model_name}")
                    
                    # Load fresh training data
                    training_data = self._load_training_data(model_name)
                    
                    if training_data is not None:
                        self.train_model(model_name, training_data)
                        self.deploy_model(model_name)
                    
            except Exception as e:
                logger.error(f"Error in scheduled retraining for {model_name}: {e}")
    
    def _health_check(self):
        """
        Perform periodic health check
        """
        health = self.get_model_health()
        
        if health['overall_health'] != 'healthy':
            logger.warning(f"System health: {health['overall_health']}")
            
            # Log unhealthy models
            for model_name, model_health in health['models'].items():
                if model_health['status'] != 'healthy':
                    logger.warning(f"Model {model_name} is {model_health['status']}: {model_health['issues']}")
    
    def _needs_retraining(self, model_name):
        """
        Check if a model needs retraining
        """
        metadata = self.model_metadata.get('models', {}).get(model_name, {})
        last_trained = metadata.get('last_trained')
        
        if not last_trained:
            return True
        
        last_trained_dt = datetime.fromisoformat(last_trained.replace('Z', '+00:00'))
        days_since_training = (datetime.now() - last_trained_dt.replace(tzinfo=None)).days
        
        # Retrain if more than 7 days old
        return days_since_training > 7
    
    def _load_training_data(self, model_name):
        """
        Load training data for a model
        """
        try:
            data_file = self.data_dir / f"{model_name}_training_data.csv"
            
            if data_file.exists():
                return pd.read_csv(data_file)
            else:
                logger.warning(f"Training data file not found: {data_file}")
                return None
                
        except Exception as e:
            logger.error(f"Error loading training data for {model_name}: {e}")
            return None
