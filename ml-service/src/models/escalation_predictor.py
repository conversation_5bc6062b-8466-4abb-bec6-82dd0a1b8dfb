"""
=============================================
⚠️ ESCALATION PREDICTION MODEL
Ticket escalation risk prediction and prevention
ML-powered early warning system
=============================================
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import classification_report, roc_auc_score, precision_recall_curve
import joblib
import logging
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

class EscalationPredictor:
    def __init__(self):
        self.model = None
        self.scaler = StandardScaler()
        self.label_encoders = {}
        self.feature_importance = None
        self.model_version = "1.0"
        self.accuracy = 0.0
        self.last_trained = None
        
    def prepare_features(self, df):
        """
        Prepare features for escalation prediction
        """
        try:
            features = df.copy()
            
            # Ticket characteristics
            features['ticket_age_hours'] = (
                pd.to_datetime('now') - pd.to_datetime(features['created_at'])
            ).dt.total_seconds() / 3600
            
            features['response_time_hours'] = (
                pd.to_datetime(features['first_response_at']) - pd.to_datetime(features['created_at'])
            ).dt.total_seconds() / 3600
            
            features['time_since_last_update'] = (
                pd.to_datetime('now') - pd.to_datetime(features['last_updated_at'])
            ).dt.total_seconds() / 3600
            
            # Customer characteristics
            features['customer_tier_score'] = features['customer_tier'].map({
                'platinum': 4, 'gold': 3, 'silver': 2, 'bronze': 1, 'standard': 0
            }).fillna(0)
            
            features['customer_satisfaction_history'] = features['avg_customer_satisfaction'].fillna(7.5)
            features['previous_escalations'] = features['customer_escalation_count'].fillna(0)
            features['customer_tenure_months'] = features['customer_account_age_months'].fillna(12)
            
            # Agent characteristics
            features['agent_experience_months'] = features['agent_tenure_months'].fillna(6)
            features['agent_skill_level'] = features['agent_skill_score'].fillna(75)
            features['agent_current_workload'] = features['agent_active_tickets'].fillna(5)
            features['agent_escalation_rate'] = features['agent_historical_escalation_rate'].fillna(0.1)
            
            # Ticket content analysis
            features['message_count'] = features['total_messages'].fillna(1)
            features['customer_message_count'] = features['customer_messages'].fillna(1)
            features['agent_message_count'] = features['agent_messages'].fillna(0)
            features['avg_message_length'] = features['total_message_length'] / features['message_count']
            
            # Sentiment and emotion features
            features['customer_sentiment_score'] = features['latest_customer_sentiment'].fillna(0)
            features['sentiment_trend'] = features['sentiment_change'].fillna(0)
            features['frustration_indicators'] = features['frustration_keywords_count'].fillna(0)
            features['urgency_keywords'] = features['urgency_keywords_count'].fillna(0)
            
            # Time-based features
            features['created_hour'] = pd.to_datetime(features['created_at']).dt.hour
            features['created_day_of_week'] = pd.to_datetime(features['created_at']).dt.dayofweek
            features['is_weekend'] = (features['created_day_of_week'] >= 5).astype(int)
            features['is_business_hours'] = ((features['created_hour'] >= 8) & (features['created_hour'] <= 18)).astype(int)
            
            # Priority and category features
            features['priority_score'] = features['priority'].map({
                'critical': 4, 'high': 3, 'medium': 2, 'low': 1
            }).fillna(2)
            
            # Interaction patterns
            features['response_delay_ratio'] = features['response_time_hours'] / (features['ticket_age_hours'] + 1)
            features['message_frequency'] = features['message_count'] / (features['ticket_age_hours'] + 1)
            features['customer_engagement'] = features['customer_message_count'] / (features['message_count'] + 1)
            
            # System and external factors
            features['system_issues_count'] = features['concurrent_system_issues'].fillna(0)
            features['team_workload'] = features['team_average_workload'].fillna(5)
            features['time_to_sla_breach'] = features['sla_deadline_hours'].fillna(24) - features['ticket_age_hours']
            
            # Historical patterns
            features['similar_ticket_escalation_rate'] = features['category_escalation_rate'].fillna(0.15)
            features['customer_complaint_history'] = features['customer_previous_complaints'].fillna(0)
            
            # Encode categorical variables
            categorical_columns = ['category', 'subcategory', 'channel', 'product_type']
            for col in categorical_columns:
                if col in features.columns:
                    if col not in self.label_encoders:
                        self.label_encoders[col] = LabelEncoder()
                        features[f'{col}_encoded'] = self.label_encoders[col].fit_transform(features[col].fillna('unknown'))
                    else:
                        features[f'{col}_encoded'] = self.label_encoders[col].transform(features[col].fillna('unknown'))
            
            # Select final feature set
            feature_columns = [
                'ticket_age_hours', 'response_time_hours', 'time_since_last_update',
                'customer_tier_score', 'customer_satisfaction_history', 'previous_escalations', 'customer_tenure_months',
                'agent_experience_months', 'agent_skill_level', 'agent_current_workload', 'agent_escalation_rate',
                'message_count', 'customer_message_count', 'agent_message_count', 'avg_message_length',
                'customer_sentiment_score', 'sentiment_trend', 'frustration_indicators', 'urgency_keywords',
                'created_hour', 'created_day_of_week', 'is_weekend', 'is_business_hours',
                'priority_score', 'response_delay_ratio', 'message_frequency', 'customer_engagement',
                'system_issues_count', 'team_workload', 'time_to_sla_breach',
                'similar_ticket_escalation_rate', 'customer_complaint_history'
            ]
            
            # Add encoded categorical features
            for col in categorical_columns:
                if f'{col}_encoded' in features.columns:
                    feature_columns.append(f'{col}_encoded')
            
            # Handle missing values
            features = features.fillna(0)
            
            return features[feature_columns]
            
        except Exception as e:
            logger.error(f"Error preparing features: {e}")
            raise
    
    def train_model(self, training_data):
        """
        Train the escalation prediction model
        """
        try:
            logger.info("Starting escalation prediction model training...")
            
            # Prepare features
            X = self.prepare_features(training_data)
            y = training_data['escalated']
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
            
            # Scale features
            X_train_scaled = self.scaler.fit_transform(X_train)
            X_test_scaled = self.scaler.transform(X_test)
            
            # Train ensemble model
            rf_model = RandomForestClassifier(
                n_estimators=200,
                max_depth=15,
                min_samples_split=10,
                min_samples_leaf=5,
                class_weight='balanced',
                random_state=42,
                n_jobs=-1
            )
            
            gb_model = GradientBoostingClassifier(
                n_estimators=150,
                learning_rate=0.1,
                max_depth=8,
                random_state=42
            )
            
            # Train models
            rf_model.fit(X_train_scaled, y_train)
            gb_model.fit(X_train_scaled, y_train)
            
            # Ensemble predictions
            rf_pred_proba = rf_model.predict_proba(X_test_scaled)[:, 1]
            gb_pred_proba = gb_model.predict_proba(X_test_scaled)[:, 1]
            
            # Weighted ensemble
            ensemble_pred_proba = 0.6 * rf_pred_proba + 0.4 * gb_pred_proba
            
            # Calculate metrics
            self.accuracy = roc_auc_score(y_test, ensemble_pred_proba)
            
            # Store models
            self.model = {
                'rf_model': rf_model,
                'gb_model': gb_model,
                'ensemble_weights': [0.6, 0.4]
            }
            
            # Feature importance
            self.feature_importance = pd.DataFrame({
                'feature': X.columns,
                'importance': rf_model.feature_importances_
            }).sort_values('importance', ascending=False)
            
            self.last_trained = datetime.now()
            
            logger.info(f"Model training completed. AUC: {self.accuracy:.4f}")
            
            return {
                'accuracy': self.accuracy,
                'feature_importance': self.feature_importance.to_dict('records'),
                'model_version': self.model_version,
                'training_samples': len(X_train),
                'test_samples': len(X_test),
                'class_distribution': y.value_counts().to_dict()
            }
            
        except Exception as e:
            logger.error(f"Error training escalation model: {e}")
            raise
    
    def predict_escalation(self, ticket_data):
        """
        Predict escalation probability for tickets
        """
        try:
            if self.model is None:
                raise ValueError("Model not trained yet")
            
            # Prepare features
            X = self.prepare_features(ticket_data)
            X_scaled = self.scaler.transform(X)
            
            # Get predictions from both models
            rf_pred_proba = self.model['rf_model'].predict_proba(X_scaled)[:, 1]
            gb_pred_proba = self.model['gb_model'].predict_proba(X_scaled)[:, 1]
            
            # Ensemble prediction
            weights = self.model['ensemble_weights']
            escalation_probability = weights[0] * rf_pred_proba + weights[1] * gb_pred_proba
            
            # Risk categorization
            risk_levels = []
            for prob in escalation_probability:
                if prob >= 0.8:
                    risk_levels.append('critical')
                elif prob >= 0.6:
                    risk_levels.append('high')
                elif prob >= 0.4:
                    risk_levels.append('medium')
                else:
                    risk_levels.append('low')
            
            # Generate risk factors and predictions
            predictions = []
            for i, (prob, risk) in enumerate(zip(escalation_probability, risk_levels)):
                risk_factors = self._generate_risk_factors(ticket_data.iloc[i], X.iloc[i])
                
                predictions.append({
                    'ticket_id': ticket_data.iloc[i]['ticket_id'],
                    'escalation_probability': float(prob),
                    'risk_level': risk,
                    'risk_factors': risk_factors,
                    'confidence': self.accuracy,
                    'predicted_escalation_time': self._estimate_escalation_time(prob),
                    'customer_sentiment': ticket_data.iloc[i].get('latest_customer_sentiment', 'neutral'),
                    'agent_experience': self._categorize_agent_experience(ticket_data.iloc[i]),
                    'ticket_complexity': self._assess_ticket_complexity(ticket_data.iloc[i])
                })
            
            return predictions
            
        except Exception as e:
            logger.error(f"Error predicting escalation: {e}")
            raise
    
    def _generate_risk_factors(self, ticket_row, feature_row):
        """
        Generate risk factors for escalation
        """
        factors = []
        
        # Time-based factors
        if feature_row.get('ticket_age_hours', 0) > 24:
            factors.append('long_resolution_time')
        
        if feature_row.get('response_time_hours', 0) > 4:
            factors.append('slow_initial_response')
        
        if feature_row.get('time_since_last_update', 0) > 8:
            factors.append('stale_ticket')
        
        # Customer factors
        if feature_row.get('customer_sentiment_score', 0) < -0.5:
            factors.append('negative_customer_sentiment')
        
        if feature_row.get('previous_escalations', 0) > 0:
            factors.append('customer_escalation_history')
        
        if feature_row.get('customer_tier_score', 0) >= 3:
            factors.append('high_value_customer')
        
        # Agent factors
        if feature_row.get('agent_experience_months', 0) < 6:
            factors.append('inexperienced_agent')
        
        if feature_row.get('agent_current_workload', 0) > 10:
            factors.append('agent_overload')
        
        if feature_row.get('agent_escalation_rate', 0) > 0.2:
            factors.append('agent_high_escalation_rate')
        
        # Communication factors
        if feature_row.get('message_count', 0) > 15:
            factors.append('extended_conversation')
        
        if feature_row.get('frustration_indicators', 0) > 2:
            factors.append('customer_frustration')
        
        # System factors
        if feature_row.get('system_issues_count', 0) > 0:
            factors.append('concurrent_system_issues')
        
        if feature_row.get('time_to_sla_breach', 0) < 2:
            factors.append('sla_breach_risk')
        
        # Priority factors
        if feature_row.get('priority_score', 0) >= 3:
            factors.append('high_priority_ticket')
        
        return factors
    
    def _estimate_escalation_time(self, escalation_probability):
        """
        Estimate when escalation might occur
        """
        if escalation_probability >= 0.8:
            hours_ahead = 2  # Critical - within 2 hours
        elif escalation_probability >= 0.6:
            hours_ahead = 6  # High - within 6 hours
        elif escalation_probability >= 0.4:
            hours_ahead = 24  # Medium - within 24 hours
        else:
            hours_ahead = 72  # Low - within 72 hours
        
        return (datetime.now() + timedelta(hours=hours_ahead)).isoformat()
    
    def _categorize_agent_experience(self, ticket_row):
        """
        Categorize agent experience level
        """
        experience_months = ticket_row.get('agent_tenure_months', 6)
        
        if experience_months < 3:
            return 'novice'
        elif experience_months < 12:
            return 'intermediate'
        elif experience_months < 36:
            return 'experienced'
        else:
            return 'expert'
    
    def _assess_ticket_complexity(self, ticket_row):
        """
        Assess ticket complexity
        """
        complexity_score = 0
        
        # Message count indicator
        message_count = ticket_row.get('total_messages', 1)
        if message_count > 20:
            complexity_score += 2
        elif message_count > 10:
            complexity_score += 1
        
        # Priority indicator
        priority = ticket_row.get('priority', 'medium')
        if priority == 'critical':
            complexity_score += 2
        elif priority == 'high':
            complexity_score += 1
        
        # Category indicator
        category = ticket_row.get('category', '')
        if category in ['technical', 'billing', 'account']:
            complexity_score += 1
        
        # Customer tier indicator
        tier = ticket_row.get('customer_tier', 'standard')
        if tier in ['platinum', 'gold']:
            complexity_score += 1
        
        if complexity_score >= 4:
            return 'high'
        elif complexity_score >= 2:
            return 'medium'
        else:
            return 'low'
    
    def save_model(self, filepath):
        """
        Save the trained model
        """
        try:
            model_data = {
                'model': self.model,
                'scaler': self.scaler,
                'label_encoders': self.label_encoders,
                'feature_importance': self.feature_importance,
                'accuracy': self.accuracy,
                'model_version': self.model_version,
                'last_trained': self.last_trained
            }
            
            joblib.dump(model_data, filepath)
            logger.info(f"Escalation prediction model saved to {filepath}")
            
        except Exception as e:
            logger.error(f"Error saving model: {e}")
            raise
    
    def load_model(self, filepath):
        """
        Load a trained model
        """
        try:
            model_data = joblib.load(filepath)
            
            self.model = model_data['model']
            self.scaler = model_data['scaler']
            self.label_encoders = model_data['label_encoders']
            self.feature_importance = model_data['feature_importance']
            self.accuracy = model_data['accuracy']
            self.model_version = model_data['model_version']
            self.last_trained = model_data['last_trained']
            
            logger.info(f"Escalation prediction model loaded from {filepath}")
            
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            raise
    
    def get_model_info(self):
        """
        Get model information
        """
        return {
            'model_version': self.model_version,
            'accuracy': self.accuracy,
            'last_trained': self.last_trained.isoformat() if self.last_trained else None,
            'feature_count': len(self.feature_importance) if self.feature_importance is not None else 0,
            'model_type': 'ensemble_rf_gb',
            'status': 'trained' if self.model is not None else 'not_trained'
        }
    
    def analyze_escalation_patterns(self, historical_data):
        """
        Analyze historical escalation patterns
        """
        try:
            patterns = {}
            
            # Time-based patterns
            escalated_tickets = historical_data[historical_data['escalated'] == 1]
            
            patterns['hourly_distribution'] = escalated_tickets.groupby(
                pd.to_datetime(escalated_tickets['created_at']).dt.hour
            ).size().to_dict()
            
            patterns['daily_distribution'] = escalated_tickets.groupby(
                pd.to_datetime(escalated_tickets['created_at']).dt.dayofweek
            ).size().to_dict()
            
            # Category patterns
            patterns['category_escalation_rates'] = (
                historical_data.groupby('category')['escalated'].mean().to_dict()
            )
            
            # Agent patterns
            patterns['agent_escalation_rates'] = (
                historical_data.groupby('agent_id')['escalated'].mean().to_dict()
            )
            
            # Customer tier patterns
            patterns['tier_escalation_rates'] = (
                historical_data.groupby('customer_tier')['escalated'].mean().to_dict()
            )
            
            return patterns
            
        except Exception as e:
            logger.error(f"Error analyzing escalation patterns: {e}")
            return {}
    
    def get_prevention_recommendations(self, high_risk_tickets):
        """
        Get prevention recommendations for high-risk tickets
        """
        recommendations = []
        
        for ticket in high_risk_tickets:
            ticket_recommendations = []
            
            # Based on risk factors
            risk_factors = ticket.get('risk_factors', [])
            
            if 'slow_initial_response' in risk_factors:
                ticket_recommendations.append({
                    'action': 'prioritize_response',
                    'description': 'Prioritize immediate response to customer',
                    'urgency': 'high'
                })
            
            if 'inexperienced_agent' in risk_factors:
                ticket_recommendations.append({
                    'action': 'supervisor_review',
                    'description': 'Assign supervisor to review and guide',
                    'urgency': 'medium'
                })
            
            if 'customer_frustration' in risk_factors:
                ticket_recommendations.append({
                    'action': 'empathy_response',
                    'description': 'Use empathetic communication approach',
                    'urgency': 'high'
                })
            
            if 'high_value_customer' in risk_factors:
                ticket_recommendations.append({
                    'action': 'vip_treatment',
                    'description': 'Apply VIP customer handling procedures',
                    'urgency': 'high'
                })
            
            if 'sla_breach_risk' in risk_factors:
                ticket_recommendations.append({
                    'action': 'escalate_proactively',
                    'description': 'Consider proactive escalation to prevent SLA breach',
                    'urgency': 'critical'
                })
            
            recommendations.append({
                'ticket_id': ticket['ticket_id'],
                'escalation_probability': ticket['escalation_probability'],
                'recommendations': ticket_recommendations
            })
        
        return recommendations
