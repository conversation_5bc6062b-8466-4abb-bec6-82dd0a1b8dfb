"""
=============================================
📈 DEMAND FORECASTING MODEL
Support ticket volume prediction for staffing optimization
Time series forecasting with seasonal patterns
=============================================
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import joblib
import logging
from datetime import datetime, timedelta
from statsmodels.tsa.seasonal import seasonal_decompose
from statsmodels.tsa.holtwinters import ExponentialSmoothing
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

class DemandForecaster:
    def __init__(self):
        self.model = None
        self.scaler = StandardScaler()
        self.seasonal_model = None
        self.accuracy = 0.0
        self.model_version = "1.0"
        self.last_trained = None
        self.feature_importance = None
        
    def prepare_time_features(self, df):
        """
        Create time-based features for demand forecasting
        """
        try:
            features = df.copy()
            
            # Ensure datetime index
            if 'timestamp' in features.columns:
                features['timestamp'] = pd.to_datetime(features['timestamp'])
                features = features.set_index('timestamp')
            
            # Time-based features
            features['hour'] = features.index.hour
            features['day_of_week'] = features.index.dayofweek
            features['day_of_month'] = features.index.day
            features['month'] = features.index.month
            features['quarter'] = features.index.quarter
            features['year'] = features.index.year
            
            # Cyclical encoding for time features
            features['hour_sin'] = np.sin(2 * np.pi * features['hour'] / 24)
            features['hour_cos'] = np.cos(2 * np.pi * features['hour'] / 24)
            features['day_sin'] = np.sin(2 * np.pi * features['day_of_week'] / 7)
            features['day_cos'] = np.cos(2 * np.pi * features['day_of_week'] / 7)
            features['month_sin'] = np.sin(2 * np.pi * features['month'] / 12)
            features['month_cos'] = np.cos(2 * np.pi * features['month'] / 12)
            
            # Business hours indicator
            features['is_business_hours'] = ((features['hour'] >= 8) & (features['hour'] <= 18)).astype(int)
            features['is_weekend'] = (features['day_of_week'] >= 5).astype(int)
            
            # Holiday indicators (simplified)
            features['is_holiday'] = 0  # Would be populated with actual holiday data
            
            # Lag features (previous periods)
            for lag in [1, 2, 3, 6, 12, 24, 48, 168]:  # 1h, 2h, 3h, 6h, 12h, 1d, 2d, 1w
                features[f'volume_lag_{lag}'] = features['ticket_volume'].shift(lag)
            
            # Rolling statistics
            for window in [3, 6, 12, 24]:
                features[f'volume_rolling_mean_{window}'] = features['ticket_volume'].rolling(window).mean()
                features[f'volume_rolling_std_{window}'] = features['ticket_volume'].rolling(window).std()
                features[f'volume_rolling_max_{window}'] = features['ticket_volume'].rolling(window).max()
                features[f'volume_rolling_min_{window}'] = features['ticket_volume'].rolling(window).min()
            
            # Trend features
            features['volume_diff_1'] = features['ticket_volume'].diff(1)
            features['volume_diff_24'] = features['ticket_volume'].diff(24)
            features['volume_pct_change_1'] = features['ticket_volume'].pct_change(1)
            features['volume_pct_change_24'] = features['ticket_volume'].pct_change(24)
            
            # External factors (if available)
            if 'marketing_campaigns' in features.columns:
                features['campaign_active'] = features['marketing_campaigns'].fillna(0)
            else:
                features['campaign_active'] = 0
                
            if 'system_issues' in features.columns:
                features['system_issues_count'] = features['system_issues'].fillna(0)
            else:
                features['system_issues_count'] = 0
            
            # Weather impact (simplified)
            features['weather_impact'] = 0  # Would be populated with weather data
            
            # Special events
            features['special_event'] = 0  # Would be populated with event data
            
            # Fill missing values
            features = features.fillna(method='forward').fillna(0)
            
            return features
            
        except Exception as e:
            logger.error(f"Error preparing time features: {e}")
            raise
    
    def train_model(self, training_data):
        """
        Train the demand forecasting model
        """
        try:
            logger.info("Starting demand forecasting model training...")
            
            # Prepare features
            features_df = self.prepare_time_features(training_data)
            
            # Select feature columns
            feature_columns = [
                'hour', 'day_of_week', 'day_of_month', 'month', 'quarter',
                'hour_sin', 'hour_cos', 'day_sin', 'day_cos', 'month_sin', 'month_cos',
                'is_business_hours', 'is_weekend', 'is_holiday',
                'volume_lag_1', 'volume_lag_2', 'volume_lag_3', 'volume_lag_6',
                'volume_lag_12', 'volume_lag_24', 'volume_lag_48', 'volume_lag_168',
                'volume_rolling_mean_3', 'volume_rolling_mean_6', 'volume_rolling_mean_12', 'volume_rolling_mean_24',
                'volume_rolling_std_3', 'volume_rolling_std_6', 'volume_rolling_std_12', 'volume_rolling_std_24',
                'volume_diff_1', 'volume_diff_24', 'volume_pct_change_1', 'volume_pct_change_24',
                'campaign_active', 'system_issues_count', 'weather_impact', 'special_event'
            ]
            
            # Prepare training data
            X = features_df[feature_columns].dropna()
            y = features_df.loc[X.index, 'ticket_volume']
            
            # Split data (time series split)
            split_point = int(len(X) * 0.8)
            X_train, X_test = X[:split_point], X[split_point:]
            y_train, y_test = y[:split_point], y[split_point:]
            
            # Scale features
            X_train_scaled = self.scaler.fit_transform(X_train)
            X_test_scaled = self.scaler.transform(X_test)
            
            # Train Random Forest model
            self.model = RandomForestRegressor(
                n_estimators=200,
                max_depth=20,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                n_jobs=-1
            )
            
            self.model.fit(X_train_scaled, y_train)
            
            # Train seasonal model for trend decomposition
            ts_data = training_data.set_index('timestamp')['ticket_volume']
            if len(ts_data) >= 168:  # At least 1 week of hourly data
                self.seasonal_model = ExponentialSmoothing(
                    ts_data,
                    trend='add',
                    seasonal='add',
                    seasonal_periods=24  # Daily seasonality
                ).fit()
            
            # Evaluate model
            y_pred = self.model.predict(X_test_scaled)
            
            self.accuracy = r2_score(y_test, y_pred)
            mae = mean_absolute_error(y_test, y_pred)
            rmse = np.sqrt(mean_squared_error(y_test, y_pred))
            
            # Feature importance
            self.feature_importance = pd.DataFrame({
                'feature': feature_columns,
                'importance': self.model.feature_importances_
            }).sort_values('importance', ascending=False)
            
            self.last_trained = datetime.now()
            
            logger.info(f"Model training completed. R²: {self.accuracy:.4f}, MAE: {mae:.2f}, RMSE: {rmse:.2f}")
            
            return {
                'accuracy': self.accuracy,
                'mae': mae,
                'rmse': rmse,
                'feature_importance': self.feature_importance.to_dict('records'),
                'model_version': self.model_version,
                'training_samples': len(X_train),
                'test_samples': len(X_test)
            }
            
        except Exception as e:
            logger.error(f"Error training demand forecasting model: {e}")
            raise
    
    def forecast_demand(self, hours_ahead=24, granularity='hourly', include_confidence=True):
        """
        Forecast demand for specified hours ahead
        """
        try:
            if self.model is None:
                raise ValueError("Model not trained yet")
            
            # Generate future timestamps
            current_time = datetime.now().replace(minute=0, second=0, microsecond=0)
            future_timestamps = [
                current_time + timedelta(hours=i) for i in range(1, hours_ahead + 1)
            ]
            
            # Create future dataframe
            future_df = pd.DataFrame({
                'timestamp': future_timestamps,
                'ticket_volume': 0  # Placeholder
            })
            
            # Get recent historical data for lag features
            # This would typically come from your database
            recent_data = self._get_recent_historical_data()
            
            # Combine recent data with future timestamps
            combined_df = pd.concat([recent_data, future_df], ignore_index=True)
            
            # Prepare features
            features_df = self.prepare_time_features(combined_df)
            
            # Select feature columns
            feature_columns = [
                'hour', 'day_of_week', 'day_of_month', 'month', 'quarter',
                'hour_sin', 'hour_cos', 'day_sin', 'day_cos', 'month_sin', 'month_cos',
                'is_business_hours', 'is_weekend', 'is_holiday',
                'volume_lag_1', 'volume_lag_2', 'volume_lag_3', 'volume_lag_6',
                'volume_lag_12', 'volume_lag_24', 'volume_lag_48', 'volume_lag_168',
                'volume_rolling_mean_3', 'volume_rolling_mean_6', 'volume_rolling_mean_12', 'volume_rolling_mean_24',
                'volume_rolling_std_3', 'volume_rolling_std_6', 'volume_rolling_std_12', 'volume_rolling_std_24',
                'volume_diff_1', 'volume_diff_24', 'volume_pct_change_1', 'volume_pct_change_24',
                'campaign_active', 'system_issues_count', 'weather_impact', 'special_event'
            ]
            
            # Get future features
            future_features = features_df.tail(hours_ahead)[feature_columns]
            future_features_scaled = self.scaler.transform(future_features.fillna(0))
            
            # Make predictions
            predictions = self.model.predict(future_features_scaled)
            
            # Calculate confidence intervals if requested
            confidence_intervals = None
            if include_confidence:
                confidence_intervals = self._calculate_confidence_intervals(
                    future_features_scaled, predictions
                )
            
            # Detect peaks
            peak_probabilities = self._detect_peaks(predictions, future_timestamps)
            
            # Format results
            forecast_results = []
            for i, (timestamp, pred) in enumerate(zip(future_timestamps, predictions)):
                result = {
                    'hour': timestamp.hour,
                    'timestamp': timestamp.isoformat(),
                    'predicted_volume': max(0, int(pred)),  # Ensure non-negative
                    'confidence': self.accuracy,
                    'peak_probability': peak_probabilities[i],
                    'contributing_factors': self._identify_contributing_factors(
                        future_features.iloc[i], timestamp
                    )
                }
                
                if confidence_intervals:
                    result['confidence_interval'] = confidence_intervals[i]
                    result['variance'] = confidence_intervals[i]['upper'] - confidence_intervals[i]['lower']
                
                forecast_results.append(result)
            
            return forecast_results
            
        except Exception as e:
            logger.error(f"Error forecasting demand: {e}")
            raise
    
    def _get_recent_historical_data(self):
        """
        Get recent historical data for lag features
        This would typically query your database
        """
        # Simulate recent data - in production, this would query the database
        current_time = datetime.now().replace(minute=0, second=0, microsecond=0)
        historical_timestamps = [
            current_time - timedelta(hours=i) for i in range(168, 0, -1)  # Last week
        ]
        
        # Generate realistic historical data
        historical_data = []
        for ts in historical_timestamps:
            # Simulate realistic ticket volume based on time patterns
            base_volume = 50
            
            # Hour of day pattern
            if 8 <= ts.hour <= 18:
                base_volume *= 1.5  # Business hours
            elif 22 <= ts.hour or ts.hour <= 6:
                base_volume *= 0.3  # Night hours
            
            # Day of week pattern
            if ts.weekday() >= 5:  # Weekend
                base_volume *= 0.7
            
            # Add some randomness
            volume = int(base_volume * (0.8 + 0.4 * np.random.random()))
            
            historical_data.append({
                'timestamp': ts,
                'ticket_volume': volume
            })
        
        return pd.DataFrame(historical_data)
    
    def _calculate_confidence_intervals(self, features, predictions):
        """
        Calculate confidence intervals for predictions
        """
        # Use prediction intervals from Random Forest
        # This is a simplified approach - in production, you might use quantile regression
        
        confidence_intervals = []
        for pred in predictions:
            # Estimate uncertainty based on model accuracy
            uncertainty = pred * (1 - self.accuracy) * 0.5
            
            confidence_intervals.append({
                'lower': max(0, pred - uncertainty),
                'upper': pred + uncertainty,
                'confidence_level': 0.8
            })
        
        return confidence_intervals
    
    def _detect_peaks(self, predictions, timestamps):
        """
        Detect peak demand periods
        """
        peak_probabilities = []
        
        for i, (pred, ts) in enumerate(zip(predictions, timestamps)):
            peak_prob = 0.0
            
            # Higher probability during business hours
            if 8 <= ts.hour <= 18:
                peak_prob += 0.3
            
            # Higher probability on weekdays
            if ts.weekday() < 5:
                peak_prob += 0.2
            
            # Volume-based probability
            if pred > np.mean(predictions) * 1.2:
                peak_prob += 0.4
            
            # Time-based patterns
            if ts.hour in [9, 10, 14, 15]:  # Common peak hours
                peak_prob += 0.3
            
            peak_probabilities.append(min(1.0, peak_prob))
        
        return peak_probabilities
    
    def _identify_contributing_factors(self, features, timestamp):
        """
        Identify factors contributing to the prediction
        """
        factors = []
        
        if features.get('is_business_hours', 0) == 1:
            factors.append('business_hours')
        
        if features.get('is_weekend', 0) == 0:
            factors.append('weekday')
        
        if features.get('volume_lag_24', 0) > 60:
            factors.append('high_previous_day_volume')
        
        if features.get('campaign_active', 0) > 0:
            factors.append('marketing_campaign')
        
        if features.get('system_issues_count', 0) > 0:
            factors.append('system_issues')
        
        # Seasonal factors
        if timestamp.month in [11, 12, 1]:  # Holiday season
            factors.append('holiday_season')
        
        return factors
    
    def save_model(self, filepath):
        """
        Save the trained model
        """
        try:
            model_data = {
                'model': self.model,
                'scaler': self.scaler,
                'seasonal_model': self.seasonal_model,
                'feature_importance': self.feature_importance,
                'accuracy': self.accuracy,
                'model_version': self.model_version,
                'last_trained': self.last_trained
            }
            
            joblib.dump(model_data, filepath)
            logger.info(f"Demand forecasting model saved to {filepath}")
            
        except Exception as e:
            logger.error(f"Error saving model: {e}")
            raise
    
    def load_model(self, filepath):
        """
        Load a trained model
        """
        try:
            model_data = joblib.load(filepath)
            
            self.model = model_data['model']
            self.scaler = model_data['scaler']
            self.seasonal_model = model_data.get('seasonal_model')
            self.feature_importance = model_data['feature_importance']
            self.accuracy = model_data['accuracy']
            self.model_version = model_data['model_version']
            self.last_trained = model_data['last_trained']
            
            logger.info(f"Demand forecasting model loaded from {filepath}")
            
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            raise
    
    def get_model_info(self):
        """
        Get model information
        """
        return {
            'model_version': self.model_version,
            'accuracy': self.accuracy,
            'last_trained': self.last_trained.isoformat() if self.last_trained else None,
            'feature_count': len(self.feature_importance) if self.feature_importance is not None else 0,
            'model_type': 'random_forest_time_series',
            'status': 'trained' if self.model is not None else 'not_trained',
            'seasonal_model_available': self.seasonal_model is not None
        }
    
    def analyze_seasonality(self, data):
        """
        Analyze seasonal patterns in the data
        """
        try:
            ts_data = data.set_index('timestamp')['ticket_volume']
            
            if len(ts_data) < 168:  # Need at least 1 week of data
                return {'error': 'Insufficient data for seasonality analysis'}
            
            # Decompose time series
            decomposition = seasonal_decompose(
                ts_data.resample('H').mean(),
                model='additive',
                period=24
            )
            
            return {
                'trend': decomposition.trend.dropna().tolist(),
                'seasonal': decomposition.seasonal.dropna().tolist(),
                'residual': decomposition.resid.dropna().tolist(),
                'seasonal_strength': float(np.var(decomposition.seasonal.dropna()) / np.var(ts_data.dropna())),
                'trend_strength': float(np.var(decomposition.trend.dropna()) / np.var(ts_data.dropna()))
            }
            
        except Exception as e:
            logger.error(f"Error analyzing seasonality: {e}")
            return {'error': str(e)}
