"""
=============================================
🔍 ANOMALY DETECTION MODEL
System anomaly detection and health monitoring
Real-time detection of unusual patterns
=============================================
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import DBSCAN
from sklearn.decomposition import PCA
import joblib
import logging
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

class AnomalyDetector:
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.thresholds = {}
        self.model_version = "1.0"
        self.last_trained = None
        self.feature_names = {}
        
    def prepare_features(self, df, anomaly_type='performance'):
        """
        Prepare features for anomaly detection based on type
        """
        try:
            features = df.copy()
            
            if anomaly_type == 'performance':
                # Performance metrics
                feature_columns = [
                    'response_time_ms', 'cpu_usage_percent', 'memory_usage_percent',
                    'disk_usage_percent', 'network_latency_ms', 'error_rate_percent',
                    'throughput_requests_per_minute', 'active_connections',
                    'queue_length', 'database_response_time_ms'
                ]
                
                # Derived features
                features['response_time_z_score'] = (
                    features['response_time_ms'] - features['response_time_ms'].rolling(24).mean()
                ) / features['response_time_ms'].rolling(24).std()
                
                features['error_rate_spike'] = features['error_rate_percent'].diff()
                features['throughput_drop'] = -features['throughput_requests_per_minute'].diff()
                
            elif anomaly_type == 'volume':
                # Volume metrics
                feature_columns = [
                    'ticket_volume', 'chat_volume', 'call_volume', 'email_volume',
                    'total_interactions', 'unique_customers', 'peak_concurrent_users',
                    'session_duration_avg', 'bounce_rate_percent'
                ]
                
                # Time-based features
                features['hour'] = pd.to_datetime(features['timestamp']).dt.hour
                features['day_of_week'] = pd.to_datetime(features['timestamp']).dt.dayofweek
                features['is_weekend'] = (features['day_of_week'] >= 5).astype(int)
                features['is_business_hours'] = ((features['hour'] >= 8) & (features['hour'] <= 18)).astype(int)
                
                # Volume patterns
                features['volume_z_score'] = (
                    features['ticket_volume'] - features['ticket_volume'].rolling(24).mean()
                ) / features['ticket_volume'].rolling(24).std()
                
                features['volume_spike'] = features['ticket_volume'].diff()
                
            elif anomaly_type == 'satisfaction':
                # Customer satisfaction metrics
                feature_columns = [
                    'avg_satisfaction_score', 'satisfaction_trend', 'complaint_rate',
                    'resolution_rate', 'first_contact_resolution_rate',
                    'escalation_rate', 'repeat_contact_rate', 'nps_score'
                ]
                
                # Satisfaction patterns
                features['satisfaction_drop'] = -features['avg_satisfaction_score'].diff()
                features['complaint_spike'] = features['complaint_rate'].diff()
                
            elif anomaly_type == 'system':
                # System health metrics
                feature_columns = [
                    'server_uptime_percent', 'service_availability_percent',
                    'backup_success_rate', 'security_incidents', 'data_integrity_score',
                    'api_success_rate', 'cache_hit_rate', 'storage_usage_percent'
                ]
                
                # System health indicators
                features['availability_drop'] = -features['service_availability_percent'].diff()
                features['security_spike'] = features['security_incidents'].diff()
            
            # Add common derived features
            features['timestamp_hour'] = pd.to_datetime(features['timestamp']).dt.hour
            features['timestamp_minute'] = pd.to_datetime(features['timestamp']).dt.minute
            
            # Rolling statistics
            for col in feature_columns:
                if col in features.columns:
                    features[f'{col}_rolling_mean_6'] = features[col].rolling(6).mean()
                    features[f'{col}_rolling_std_6'] = features[col].rolling(6).std()
                    features[f'{col}_rolling_max_6'] = features[col].rolling(6).max()
                    features[f'{col}_rolling_min_6'] = features[col].rolling(6).min()
            
            # Select final features
            final_features = feature_columns.copy()
            
            # Add derived features
            derived_features = [col for col in features.columns if 
                             col.endswith('_z_score') or col.endswith('_spike') or 
                             col.endswith('_drop') or col.startswith('timestamp_') or
                             '_rolling_' in col]
            
            final_features.extend(derived_features)
            
            # Handle missing values
            features = features[final_features].fillna(method='forward').fillna(0)
            
            return features
            
        except Exception as e:
            logger.error(f"Error preparing features for {anomaly_type}: {e}")
            raise
    
    def train_model(self, training_data, anomaly_type='performance'):
        """
        Train anomaly detection model for specific type
        """
        try:
            logger.info(f"Training {anomaly_type} anomaly detection model...")
            
            # Prepare features
            X = self.prepare_features(training_data, anomaly_type)
            
            # Store feature names
            self.feature_names[anomaly_type] = X.columns.tolist()
            
            # Scale features
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)
            self.scalers[anomaly_type] = scaler
            
            # Train Isolation Forest
            isolation_forest = IsolationForest(
                contamination=0.1,  # Expect 10% anomalies
                random_state=42,
                n_jobs=-1
            )
            
            isolation_forest.fit(X_scaled)
            
            # Train DBSCAN for clustering-based anomaly detection
            dbscan = DBSCAN(eps=0.5, min_samples=5)
            cluster_labels = dbscan.fit_predict(X_scaled)
            
            # Calculate anomaly scores
            anomaly_scores = isolation_forest.decision_function(X_scaled)
            
            # Determine threshold (95th percentile of anomaly scores)
            threshold = np.percentile(anomaly_scores, 5)  # Lower scores are more anomalous
            
            # Store models and thresholds
            self.models[anomaly_type] = {
                'isolation_forest': isolation_forest,
                'dbscan': dbscan,
                'pca': None  # Will be added if needed
            }
            self.thresholds[anomaly_type] = threshold
            
            # Calculate performance metrics
            predicted_anomalies = anomaly_scores < threshold
            anomaly_rate = np.mean(predicted_anomalies)
            
            self.last_trained = datetime.now()
            
            logger.info(f"{anomaly_type} model training completed. Anomaly rate: {anomaly_rate:.3f}")
            
            return {
                'anomaly_type': anomaly_type,
                'anomaly_rate': anomaly_rate,
                'threshold': threshold,
                'feature_count': len(X.columns),
                'training_samples': len(X),
                'model_version': self.model_version
            }
            
        except Exception as e:
            logger.error(f"Error training {anomaly_type} anomaly detection model: {e}")
            raise
    
    def detect_anomalies(self, data, anomaly_types=None):
        """
        Detect anomalies in the provided data
        """
        try:
            if anomaly_types is None:
                anomaly_types = list(self.models.keys())
            
            all_anomalies = []
            
            for anomaly_type in anomaly_types:
                if anomaly_type not in self.models:
                    logger.warning(f"Model for {anomaly_type} not trained")
                    continue
                
                # Prepare features
                X = self.prepare_features(data, anomaly_type)
                
                # Scale features
                X_scaled = self.scalers[anomaly_type].transform(X)
                
                # Get anomaly scores
                isolation_scores = self.models[anomaly_type]['isolation_forest'].decision_function(X_scaled)
                
                # Identify anomalies
                threshold = self.thresholds[anomaly_type]
                is_anomaly = isolation_scores < threshold
                
                # Create anomaly records
                for i, (is_anom, score) in enumerate(zip(is_anomaly, isolation_scores)):
                    if is_anom:
                        anomaly = self._create_anomaly_record(
                            data.iloc[i], X.iloc[i], anomaly_type, score, threshold
                        )
                        all_anomalies.append(anomaly)
            
            # Sort by severity
            all_anomalies.sort(key=lambda x: x['confidence'], reverse=True)
            
            return all_anomalies
            
        except Exception as e:
            logger.error(f"Error detecting anomalies: {e}")
            raise
    
    def _create_anomaly_record(self, data_row, feature_row, anomaly_type, score, threshold):
        """
        Create detailed anomaly record
        """
        # Calculate severity based on how far below threshold
        severity_score = (threshold - score) / abs(threshold)
        
        if severity_score > 2:
            severity = 'critical'
        elif severity_score > 1:
            severity = 'high'
        elif severity_score > 0.5:
            severity = 'medium'
        else:
            severity = 'low'
        
        # Identify contributing factors
        contributing_factors = self._identify_contributing_factors(feature_row, anomaly_type)
        
        # Generate description
        description = self._generate_anomaly_description(anomaly_type, contributing_factors)
        
        # Estimate impact
        estimated_impact = self._estimate_impact(anomaly_type, severity)
        
        return {
            'id': f"anomaly_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{np.random.randint(1000, 9999)}",
            'type': anomaly_type,
            'severity': severity,
            'description': description,
            'detected_at': datetime.now().isoformat(),
            'confidence': min(1.0, severity_score / 2),  # Normalize to 0-1
            'affected_metrics': contributing_factors,
            'root_cause_probability': self._analyze_root_causes(feature_row, anomaly_type),
            'estimated_impact': estimated_impact,
            'auto_resolution_possible': self._can_auto_resolve(anomaly_type, severity),
            'anomaly_score': float(score),
            'threshold': float(threshold)
        }
    
    def _identify_contributing_factors(self, feature_row, anomaly_type):
        """
        Identify which metrics are contributing to the anomaly
        """
        factors = []
        
        if anomaly_type == 'performance':
            if feature_row.get('response_time_z_score', 0) > 2:
                factors.append('response_time')
            if feature_row.get('cpu_usage_percent', 0) > 90:
                factors.append('cpu_usage')
            if feature_row.get('memory_usage_percent', 0) > 85:
                factors.append('memory_usage')
            if feature_row.get('error_rate_spike', 0) > 5:
                factors.append('error_rate')
                
        elif anomaly_type == 'volume':
            if feature_row.get('volume_z_score', 0) > 3:
                factors.append('ticket_volume')
            if feature_row.get('volume_spike', 0) > 50:
                factors.append('volume_spike')
            if feature_row.get('peak_concurrent_users', 0) > 1000:
                factors.append('concurrent_users')
                
        elif anomaly_type == 'satisfaction':
            if feature_row.get('satisfaction_drop', 0) > 1:
                factors.append('satisfaction_score')
            if feature_row.get('complaint_spike', 0) > 0.1:
                factors.append('complaint_rate')
            if feature_row.get('escalation_rate', 0) > 0.2:
                factors.append('escalation_rate')
                
        elif anomaly_type == 'system':
            if feature_row.get('availability_drop', 0) > 5:
                factors.append('service_availability')
            if feature_row.get('security_spike', 0) > 0:
                factors.append('security_incidents')
            if feature_row.get('api_success_rate', 0) < 95:
                factors.append('api_success_rate')
        
        return factors
    
    def _generate_anomaly_description(self, anomaly_type, factors):
        """
        Generate human-readable anomaly description
        """
        descriptions = {
            'performance': {
                'response_time': 'Unusual increase in response times detected',
                'cpu_usage': 'High CPU usage detected',
                'memory_usage': 'High memory usage detected',
                'error_rate': 'Spike in error rate detected'
            },
            'volume': {
                'ticket_volume': 'Unusual ticket volume pattern detected',
                'volume_spike': 'Sudden spike in support requests',
                'concurrent_users': 'High concurrent user load detected'
            },
            'satisfaction': {
                'satisfaction_score': 'Drop in customer satisfaction detected',
                'complaint_rate': 'Increase in customer complaints',
                'escalation_rate': 'Higher than normal escalation rate'
            },
            'system': {
                'service_availability': 'Service availability degradation',
                'security_incidents': 'Security incident detected',
                'api_success_rate': 'API performance degradation'
            }
        }
        
        if factors and anomaly_type in descriptions:
            primary_factor = factors[0]
            if primary_factor in descriptions[anomaly_type]:
                return descriptions[anomaly_type][primary_factor]
        
        return f"Anomaly detected in {anomaly_type} metrics"
    
    def _analyze_root_causes(self, feature_row, anomaly_type):
        """
        Analyze potential root causes
        """
        root_causes = {}
        
        if anomaly_type == 'performance':
            if feature_row.get('cpu_usage_percent', 0) > 90:
                root_causes['resource_exhaustion'] = 0.8
            if feature_row.get('database_response_time_ms', 0) > 1000:
                root_causes['database_bottleneck'] = 0.7
            if feature_row.get('network_latency_ms', 0) > 100:
                root_causes['network_issues'] = 0.6
                
        elif anomaly_type == 'volume':
            if feature_row.get('is_business_hours', 0) == 0:
                root_causes['off_hours_spike'] = 0.7
            if feature_row.get('volume_spike', 0) > 100:
                root_causes['viral_issue'] = 0.8
                
        elif anomaly_type == 'satisfaction':
            if feature_row.get('resolution_rate', 0) < 0.8:
                root_causes['poor_resolution'] = 0.8
            if feature_row.get('first_contact_resolution_rate', 0) < 0.6:
                root_causes['inefficient_process'] = 0.7
                
        elif anomaly_type == 'system':
            if feature_row.get('backup_success_rate', 0) < 0.9:
                root_causes['backup_failure'] = 0.9
            if feature_row.get('cache_hit_rate', 0) < 0.8:
                root_causes['cache_issues'] = 0.6
        
        return root_causes
    
    def _estimate_impact(self, anomaly_type, severity):
        """
        Estimate business impact of anomaly
        """
        impact_levels = {
            'critical': 'high',
            'high': 'medium',
            'medium': 'low',
            'low': 'minimal'
        }
        
        return impact_levels.get(severity, 'minimal')
    
    def _can_auto_resolve(self, anomaly_type, severity):
        """
        Determine if anomaly can be auto-resolved
        """
        auto_resolvable = {
            'performance': severity in ['low', 'medium'],
            'volume': False,  # Usually requires human intervention
            'satisfaction': False,  # Requires human analysis
            'system': severity == 'low'
        }
        
        return auto_resolvable.get(anomaly_type, False)
    
    def save_models(self, filepath_prefix):
        """
        Save all trained models
        """
        try:
            for anomaly_type in self.models.keys():
                model_data = {
                    'model': self.models[anomaly_type],
                    'scaler': self.scalers[anomaly_type],
                    'threshold': self.thresholds[anomaly_type],
                    'feature_names': self.feature_names[anomaly_type],
                    'model_version': self.model_version,
                    'last_trained': self.last_trained
                }
                
                filepath = f"{filepath_prefix}_{anomaly_type}.joblib"
                joblib.dump(model_data, filepath)
                logger.info(f"{anomaly_type} anomaly detection model saved to {filepath}")
                
        except Exception as e:
            logger.error(f"Error saving models: {e}")
            raise
    
    def load_models(self, filepath_prefix, anomaly_types):
        """
        Load trained models
        """
        try:
            for anomaly_type in anomaly_types:
                filepath = f"{filepath_prefix}_{anomaly_type}.joblib"
                model_data = joblib.load(filepath)
                
                self.models[anomaly_type] = model_data['model']
                self.scalers[anomaly_type] = model_data['scaler']
                self.thresholds[anomaly_type] = model_data['threshold']
                self.feature_names[anomaly_type] = model_data['feature_names']
                self.model_version = model_data['model_version']
                self.last_trained = model_data['last_trained']
                
                logger.info(f"{anomaly_type} anomaly detection model loaded from {filepath}")
                
        except Exception as e:
            logger.error(f"Error loading models: {e}")
            raise
    
    def get_model_info(self):
        """
        Get information about all trained models
        """
        model_info = {
            'model_version': self.model_version,
            'last_trained': self.last_trained.isoformat() if self.last_trained else None,
            'trained_types': list(self.models.keys()),
            'models': {}
        }
        
        for anomaly_type in self.models.keys():
            model_info['models'][anomaly_type] = {
                'feature_count': len(self.feature_names.get(anomaly_type, [])),
                'threshold': self.thresholds.get(anomaly_type, 0),
                'status': 'trained'
            }
        
        return model_info
    
    def calculate_system_health_score(self, recent_anomalies):
        """
        Calculate overall system health score based on recent anomalies
        """
        if not recent_anomalies:
            return 100
        
        base_score = 100
        
        for anomaly in recent_anomalies:
            severity = anomaly.get('severity', 'low')
            
            # Deduct points based on severity
            if severity == 'critical':
                base_score -= 25
            elif severity == 'high':
                base_score -= 15
            elif severity == 'medium':
                base_score -= 8
            elif severity == 'low':
                base_score -= 3
        
        return max(0, base_score)
    
    def get_anomaly_trends(self, historical_anomalies, time_range='24h'):
        """
        Analyze trends in anomaly detection
        """
        try:
            df = pd.DataFrame(historical_anomalies)
            
            if df.empty:
                return {'trend': 'stable', 'anomaly_rate': 0}
            
            df['detected_at'] = pd.to_datetime(df['detected_at'])
            
            # Calculate time-based trends
            hourly_counts = df.groupby(df['detected_at'].dt.hour).size()
            daily_counts = df.groupby(df['detected_at'].dt.date).size()
            
            # Calculate trend
            if len(daily_counts) >= 2:
                recent_avg = daily_counts.tail(3).mean()
                older_avg = daily_counts.head(-3).mean() if len(daily_counts) > 3 else recent_avg
                
                if recent_avg > older_avg * 1.2:
                    trend = 'increasing'
                elif recent_avg < older_avg * 0.8:
                    trend = 'decreasing'
                else:
                    trend = 'stable'
            else:
                trend = 'stable'
            
            return {
                'trend': trend,
                'anomaly_rate': len(df) / 24,  # Anomalies per hour
                'severity_distribution': df['severity'].value_counts().to_dict(),
                'type_distribution': df['type'].value_counts().to_dict(),
                'hourly_pattern': hourly_counts.to_dict()
            }
            
        except Exception as e:
            logger.error(f"Error analyzing anomaly trends: {e}")
            return {'trend': 'unknown', 'anomaly_rate': 0}
