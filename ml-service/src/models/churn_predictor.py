"""
=============================================
🧠 CHURN PREDICTION MODEL
Customer churn prediction using ML algorithms
Integrates with existing 13+ million subscriber data
=============================================
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score
import joblib
import logging
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

class ChurnPredictor:
    def __init__(self):
        self.model = None
        self.scaler = StandardScaler()
        self.label_encoders = {}
        self.feature_importance = None
        self.model_version = "1.0"
        self.accuracy = 0.0
        self.last_trained = None
        
    def prepare_features(self, df):
        """
        Prepare features for churn prediction
        """
        try:
            # Create feature engineering pipeline
            features = df.copy()
            
            # Customer demographics
            features['account_age_months'] = (
                pd.to_datetime('today') - pd.to_datetime(features['account_created'])
            ).dt.days / 30.44
            
            # Usage patterns
            features['avg_monthly_usage'] = features['total_usage'] / features['account_age_months']
            features['usage_trend'] = features['recent_usage'] / features['avg_monthly_usage']
            
            # Billing patterns
            features['avg_monthly_bill'] = features['total_billed'] / features['account_age_months']
            features['payment_ratio'] = features['total_paid'] / features['total_billed']
            features['late_payment_ratio'] = features['late_payments'] / features['total_bills']
            
            # Support interactions
            features['support_frequency'] = features['support_tickets'] / features['account_age_months']
            features['avg_resolution_time'] = features['total_resolution_time'] / features['support_tickets']
            features['satisfaction_trend'] = features['recent_satisfaction'] - features['avg_satisfaction']
            
            # Service quality metrics
            features['service_interruption_ratio'] = features['service_interruptions'] / features['account_age_months']
            features['complaint_ratio'] = features['complaints'] / features['support_tickets']
            
            # Engagement metrics
            features['app_usage_frequency'] = features['app_sessions'] / features['account_age_months']
            features['feature_adoption_score'] = features['features_used'] / features['total_features']
            
            # Competitive factors
            features['price_sensitivity'] = features['price_complaints'] / features['total_complaints']
            features['competitor_mentions'] = features['competitor_inquiries'] / features['support_tickets']
            
            # Handle missing values
            features = features.fillna(0)
            
            # Select final feature set
            feature_columns = [
                'account_age_months', 'avg_monthly_usage', 'usage_trend',
                'avg_monthly_bill', 'payment_ratio', 'late_payment_ratio',
                'support_frequency', 'avg_resolution_time', 'satisfaction_trend',
                'service_interruption_ratio', 'complaint_ratio',
                'app_usage_frequency', 'feature_adoption_score',
                'price_sensitivity', 'competitor_mentions',
                'customer_tier_encoded', 'plan_type_encoded', 'region_encoded'
            ]
            
            # Encode categorical variables
            categorical_columns = ['customer_tier', 'plan_type', 'region']
            for col in categorical_columns:
                if col in features.columns:
                    if col not in self.label_encoders:
                        self.label_encoders[col] = LabelEncoder()
                        features[f'{col}_encoded'] = self.label_encoders[col].fit_transform(features[col])
                    else:
                        features[f'{col}_encoded'] = self.label_encoders[col].transform(features[col])
            
            return features[feature_columns]
            
        except Exception as e:
            logger.error(f"Error preparing features: {e}")
            raise
    
    def train_model(self, training_data):
        """
        Train the churn prediction model
        """
        try:
            logger.info("Starting churn prediction model training...")
            
            # Prepare features
            X = self.prepare_features(training_data)
            y = training_data['churned']
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
            
            # Scale features
            X_train_scaled = self.scaler.fit_transform(X_train)
            X_test_scaled = self.scaler.transform(X_test)
            
            # Train ensemble model
            rf_model = RandomForestClassifier(
                n_estimators=200,
                max_depth=15,
                min_samples_split=10,
                min_samples_leaf=5,
                random_state=42,
                n_jobs=-1
            )
            
            gb_model = GradientBoostingClassifier(
                n_estimators=150,
                learning_rate=0.1,
                max_depth=8,
                random_state=42
            )
            
            # Train models
            rf_model.fit(X_train_scaled, y_train)
            gb_model.fit(X_train_scaled, y_train)
            
            # Ensemble predictions
            rf_pred_proba = rf_model.predict_proba(X_test_scaled)[:, 1]
            gb_pred_proba = gb_model.predict_proba(X_test_scaled)[:, 1]
            
            # Weighted ensemble (Random Forest gets higher weight due to better interpretability)
            ensemble_pred_proba = 0.6 * rf_pred_proba + 0.4 * gb_pred_proba
            ensemble_pred = (ensemble_pred_proba > 0.5).astype(int)
            
            # Calculate metrics
            self.accuracy = roc_auc_score(y_test, ensemble_pred_proba)
            
            # Store models
            self.model = {
                'rf_model': rf_model,
                'gb_model': gb_model,
                'ensemble_weights': [0.6, 0.4]
            }
            
            # Feature importance
            self.feature_importance = pd.DataFrame({
                'feature': X.columns,
                'importance': rf_model.feature_importances_
            }).sort_values('importance', ascending=False)
            
            self.last_trained = datetime.now()
            
            logger.info(f"Model training completed. Accuracy: {self.accuracy:.4f}")
            
            # Generate detailed report
            report = {
                'accuracy': self.accuracy,
                'feature_importance': self.feature_importance.to_dict('records'),
                'model_version': self.model_version,
                'training_samples': len(X_train),
                'test_samples': len(X_test),
                'class_distribution': y.value_counts().to_dict(),
                'cross_validation_scores': cross_val_score(rf_model, X_train_scaled, y_train, cv=5).tolist()
            }
            
            return report
            
        except Exception as e:
            logger.error(f"Error training churn model: {e}")
            raise
    
    def predict_churn(self, customer_data):
        """
        Predict churn probability for customers
        """
        try:
            if self.model is None:
                raise ValueError("Model not trained yet")
            
            # Prepare features
            X = self.prepare_features(customer_data)
            X_scaled = self.scaler.transform(X)
            
            # Get predictions from both models
            rf_pred_proba = self.model['rf_model'].predict_proba(X_scaled)[:, 1]
            gb_pred_proba = self.model['gb_model'].predict_proba(X_scaled)[:, 1]
            
            # Ensemble prediction
            weights = self.model['ensemble_weights']
            churn_probability = weights[0] * rf_pred_proba + weights[1] * gb_pred_proba
            
            # Risk categorization
            risk_levels = []
            for prob in churn_probability:
                if prob >= 0.8:
                    risk_levels.append('critical')
                elif prob >= 0.6:
                    risk_levels.append('high')
                elif prob >= 0.4:
                    risk_levels.append('medium')
                else:
                    risk_levels.append('low')
            
            # Generate risk factors
            risk_factors = self._generate_risk_factors(customer_data, X)
            
            predictions = []
            for i, (prob, risk) in enumerate(zip(churn_probability, risk_levels)):
                predictions.append({
                    'customer_id': customer_data.iloc[i]['customer_id'],
                    'churn_probability': float(prob),
                    'risk_level': risk,
                    'risk_factors': risk_factors[i],
                    'confidence': self.accuracy,
                    'predicted_churn_date': self._estimate_churn_date(prob),
                    'customer_value': self._calculate_customer_value(customer_data.iloc[i]),
                    'customer_tier': customer_data.iloc[i].get('customer_tier', 'standard')
                })
            
            return predictions
            
        except Exception as e:
            logger.error(f"Error predicting churn: {e}")
            raise
    
    def _generate_risk_factors(self, customer_data, features):
        """
        Generate risk factors for each customer
        """
        risk_factors_list = []
        
        for i in range(len(customer_data)):
            factors = []
            
            # Check various risk indicators
            if customer_data.iloc[i].get('late_payment_ratio', 0) > 0.3:
                factors.append('billing_issues')
            
            if customer_data.iloc[i].get('satisfaction_trend', 0) < -1:
                factors.append('declining_satisfaction')
            
            if customer_data.iloc[i].get('usage_trend', 1) < 0.7:
                factors.append('decreasing_usage')
            
            if customer_data.iloc[i].get('support_frequency', 0) > 2:
                factors.append('frequent_support_contact')
            
            if customer_data.iloc[i].get('complaint_ratio', 0) > 0.5:
                factors.append('service_complaints')
            
            if customer_data.iloc[i].get('competitor_mentions', 0) > 0.1:
                factors.append('competitor_interest')
            
            if customer_data.iloc[i].get('price_sensitivity', 0) > 0.3:
                factors.append('price_concerns')
            
            risk_factors_list.append(factors)
        
        return risk_factors_list
    
    def _estimate_churn_date(self, churn_probability):
        """
        Estimate when customer might churn based on probability
        """
        if churn_probability >= 0.8:
            days_ahead = 30  # Critical - within 30 days
        elif churn_probability >= 0.6:
            days_ahead = 60  # High - within 60 days
        elif churn_probability >= 0.4:
            days_ahead = 90  # Medium - within 90 days
        else:
            days_ahead = 180  # Low - within 180 days
        
        return (datetime.now() + timedelta(days=days_ahead)).isoformat()
    
    def _calculate_customer_value(self, customer_row):
        """
        Calculate customer value tier
        """
        monthly_bill = customer_row.get('avg_monthly_bill', 0)
        account_age = customer_row.get('account_age_months', 0)
        
        total_value = monthly_bill * account_age
        
        if total_value > 2000:
            return 'high'
        elif total_value > 800:
            return 'medium'
        else:
            return 'low'
    
    def save_model(self, filepath):
        """
        Save the trained model
        """
        try:
            model_data = {
                'model': self.model,
                'scaler': self.scaler,
                'label_encoders': self.label_encoders,
                'feature_importance': self.feature_importance,
                'accuracy': self.accuracy,
                'model_version': self.model_version,
                'last_trained': self.last_trained
            }
            
            joblib.dump(model_data, filepath)
            logger.info(f"Model saved to {filepath}")
            
        except Exception as e:
            logger.error(f"Error saving model: {e}")
            raise
    
    def load_model(self, filepath):
        """
        Load a trained model
        """
        try:
            model_data = joblib.load(filepath)
            
            self.model = model_data['model']
            self.scaler = model_data['scaler']
            self.label_encoders = model_data['label_encoders']
            self.feature_importance = model_data['feature_importance']
            self.accuracy = model_data['accuracy']
            self.model_version = model_data['model_version']
            self.last_trained = model_data['last_trained']
            
            logger.info(f"Model loaded from {filepath}")
            
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            raise
    
    def get_model_info(self):
        """
        Get model information
        """
        return {
            'model_version': self.model_version,
            'accuracy': self.accuracy,
            'last_trained': self.last_trained.isoformat() if self.last_trained else None,
            'feature_count': len(self.feature_importance) if self.feature_importance is not None else 0,
            'model_type': 'ensemble_rf_gb',
            'status': 'trained' if self.model is not None else 'not_trained'
        }
    
    def retrain_with_feedback(self, feedback_data):
        """
        Retrain model with new feedback data
        """
        try:
            logger.info("Retraining model with feedback data...")
            
            # Combine with existing training data if available
            # This would typically involve loading historical data
            # and combining with new feedback
            
            # For now, just retrain with the feedback data
            return self.train_model(feedback_data)
            
        except Exception as e:
            logger.error(f"Error retraining model: {e}")
            raise
