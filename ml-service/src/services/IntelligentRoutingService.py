"""
=============================================
🎯 INTELLIGENT CONVERSATION ROUTING SERVICE
ML-based agent assignment optimization
Real-time workload balancing and skill matching
=============================================
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
import json
import numpy as np
from dataclasses import dataclass
from enum import Enum

from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
import joblib
from pymongo import MongoClient
import redis

from .ConversationAnalysisService import ConversationAnalysisService
from .IntentDetectionService import IntentDetectionService
from ..utils.cache_manager import CacheManager

logger = logging.getLogger(__name__)

class RoutingPriority(Enum):
    """Routing priority levels"""
    URGENT = "urgent"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

@dataclass
class AgentScore:
    """Agent scoring for routing decisions"""
    agent_id: str
    agent_name: str
    score: float
    availability: str
    current_workload: int
    skill_match: float
    performance_score: float
    platform_expertise: float
    customer_preference: float
    reasoning: List[str]
    metadata: Dict[str, Any]

@dataclass
class RoutingDecision:
    """Routing decision result"""
    recommended_agent: AgentScore
    alternative_agents: List[AgentScore]
    routing_confidence: float
    priority: RoutingPriority
    estimated_wait_time: int
    routing_reason: str
    escalation_suggested: bool
    metadata: Dict[str, Any]

class IntelligentRoutingService:
    """ML-powered intelligent conversation routing"""
    
    def __init__(self, mongodb_client: MongoClient, redis_client: redis.Redis):
        self.mongodb = mongodb_client
        self.redis = redis_client
        self.cache_manager = CacheManager(redis_client)
        
        # Initialize related services
        self.conversation_analyzer = ConversationAnalysisService(mongodb_client, redis_client)
        self.intent_detector = IntentDetectionService(mongodb_client, redis_client)
        
        # ML models for routing
        self.routing_model = None
        self.scaler = None
        self.feature_columns = []
        
        # Routing configuration
        self.routing_config = {
            "max_concurrent_conversations": 5,
            "skill_weight": 0.3,
            "availability_weight": 0.25,
            "performance_weight": 0.2,
            "workload_weight": 0.15,
            "customer_preference_weight": 0.1,
            "urgency_multiplier": 2.0,
            "vip_customer_multiplier": 1.5
        }
        
        # Agent skill categories
        self.skill_categories = {
            "technical_support": ["technical_issue", "device_problem", "network_issue", "troubleshooting"],
            "billing_support": ["billing_question", "payment_issue", "plan_change", "refund_request"],
            "sales_support": ["new_service", "upgrade_request", "plan_inquiry", "product_info"],
            "customer_service": ["general_inquiry", "complaint", "feedback", "account_management"],
            "escalation_handling": ["complex_issue", "supervisor_request", "legal_matter", "urgent_complaint"]
        }
        
        # Platform expertise mapping
        self.platform_expertise = {
            "whatsapp": "messaging_platforms",
            "facebook": "social_media",
            "instagram": "social_media",
            "twitter": "social_media"
        }
        
        # Initialize models
        asyncio.create_task(self._initialize_models())
    
    async def _initialize_models(self):
        """Initialize ML models for intelligent routing"""
        try:
            logger.info("Initializing intelligent routing models...")
            
            # Try to load existing model
            try:
                self.routing_model = joblib.load('models/routing_model.pkl')
                self.scaler = joblib.load('models/routing_scaler.pkl')
                with open('models/routing_features.json', 'r') as f:
                    self.feature_columns = json.load(f)
                logger.info("✅ Loaded existing routing model")
            except FileNotFoundError:
                logger.info("No existing model found, will train new model")
                await self._train_routing_model()
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize routing models: {e}")
            # Use rule-based routing as fallback
            logger.info("Using rule-based routing as fallback")
    
    async def route_conversation(
        self,
        conversation_id: str,
        customer_message: str,
        platform: str,
        customer_id: str,
        urgency: str = "medium"
    ) -> RoutingDecision:
        """
        Route conversation to the best available agent
        
        Args:
            conversation_id: ID of the conversation
            customer_message: Customer's message
            platform: Platform (whatsapp, facebook, instagram, twitter)
            customer_id: ID of the customer
            urgency: Urgency level (urgent, high, medium, low)
            
        Returns:
            RoutingDecision with recommended agent and alternatives
        """
        try:
            logger.info(f"🎯 Routing conversation {conversation_id}")
            
            # Get conversation context
            context = await self._get_routing_context(
                conversation_id, customer_message, platform, customer_id
            )
            
            # Analyze message for routing insights
            message_analysis = await self.conversation_analyzer.analyze_message(
                customer_message, context
            )
            
            # Detect intent for skill matching
            intent_result = await self.intent_detector.detect_intent(
                customer_message, context
            )
            
            # Get available agents
            available_agents = await self._get_available_agents(platform)
            
            if not available_agents:
                return await self._handle_no_agents_available(context, urgency)
            
            # Score agents for this conversation
            agent_scores = await self._score_agents(
                available_agents, context, message_analysis, intent_result, urgency
            )
            
            # Sort agents by score
            sorted_agents = sorted(agent_scores, key=lambda x: x.score, reverse=True)
            
            # Create routing decision
            routing_decision = RoutingDecision(
                recommended_agent=sorted_agents[0],
                alternative_agents=sorted_agents[1:3],  # Top 3 alternatives
                routing_confidence=self._calculate_routing_confidence(sorted_agents),
                priority=RoutingPriority(urgency),
                estimated_wait_time=await self._estimate_wait_time(sorted_agents[0]),
                routing_reason=self._generate_routing_reason(sorted_agents[0]),
                escalation_suggested=await self._should_suggest_escalation(context, message_analysis),
                metadata={
                    "total_agents_considered": len(available_agents),
                    "routing_timestamp": datetime.now().isoformat(),
                    "intent_detected": intent_result.get("primary"),
                    "customer_segment": context.get("customer", {}).get("segment"),
                    "platform": platform
                }
            )
            
            # Log routing decision
            await self._log_routing_decision(routing_decision, conversation_id)
            
            logger.info(f"✅ Routed to agent {routing_decision.recommended_agent.agent_name} (score: {routing_decision.recommended_agent.score:.2f})")
            
            return routing_decision
            
        except Exception as e:
            logger.error(f"❌ Failed to route conversation: {e}")
            # Return fallback routing
            return await self._fallback_routing(platform)
    
    async def _get_routing_context(
        self,
        conversation_id: str,
        customer_message: str,
        platform: str,
        customer_id: str
    ) -> Dict[str, Any]:
        """Get comprehensive context for routing decisions"""
        try:
            # Get customer information
            customer = self.mongodb.chatbot.customers.find_one({"_id": customer_id})
            
            # Get conversation history
            conversation = self.mongodb.chatbot.conversations.find_one({"_id": conversation_id})
            
            # Get customer's previous interactions
            previous_conversations = list(self.mongodb.chatbot.conversations.find({
                "customerId": customer_id,
                "_id": {"$ne": conversation_id}
            }).sort("createdAt", -1).limit(5))
            
            # Get customer's preferred agents (if any)
            preferred_agents = []
            if customer and customer.get("preferences", {}).get("preferred_agents"):
                preferred_agents = customer["preferences"]["preferred_agents"]
            
            context = {
                "customer": customer or {},
                "conversation": conversation or {},
                "previous_conversations": previous_conversations,
                "preferred_agents": preferred_agents,
                "current_message": customer_message,
                "platform": platform,
                "current_time": datetime.now(),
                "business_hours": self._is_business_hours()
            }
            
            return context
            
        except Exception as e:
            logger.error(f"❌ Failed to get routing context: {e}")
            return {}
    
    async def _get_available_agents(self, platform: str) -> List[Dict[str, Any]]:
        """Get list of available agents for the platform"""
        try:
            # Get agents who are available and support the platform
            agents = list(self.mongodb.chatbot.agents.find({
                "status": {"$in": ["available", "busy"]},
                "isActive": True,
                f"platformSupport.{platform}": True
            }))
            
            # Filter by current workload
            available_agents = []
            for agent in agents:
                current_conversations = self.mongodb.chatbot.conversations.count_documents({
                    "assignedAgent": agent["_id"],
                    "status": {"$in": ["open", "assigned"]}
                })
                
                if current_conversations < self.routing_config["max_concurrent_conversations"]:
                    agent["current_workload"] = current_conversations
                    available_agents.append(agent)
            
            return available_agents
            
        except Exception as e:
            logger.error(f"❌ Failed to get available agents: {e}")
            return []
    
    async def _score_agents(
        self,
        agents: List[Dict[str, Any]],
        context: Dict[str, Any],
        message_analysis: Dict[str, Any],
        intent_result: Dict[str, Any],
        urgency: str
    ) -> List[AgentScore]:
        """Score agents based on multiple factors"""
        agent_scores = []
        
        try:
            for agent in agents:
                # Calculate individual scores
                skill_score = await self._calculate_skill_score(agent, intent_result, message_analysis)
                availability_score = self._calculate_availability_score(agent)
                performance_score = await self._calculate_performance_score(agent)
                workload_score = self._calculate_workload_score(agent)
                platform_score = self._calculate_platform_expertise_score(agent, context["platform"])
                customer_preference_score = self._calculate_customer_preference_score(agent, context)
                
                # Calculate weighted total score
                total_score = (
                    skill_score * self.routing_config["skill_weight"] +
                    availability_score * self.routing_config["availability_weight"] +
                    performance_score * self.routing_config["performance_weight"] +
                    workload_score * self.routing_config["workload_weight"] +
                    platform_score * 0.1 +  # Platform expertise weight
                    customer_preference_score * self.routing_config["customer_preference_weight"]
                )
                
                # Apply urgency multiplier
                if urgency == "urgent":
                    total_score *= self.routing_config["urgency_multiplier"]
                elif urgency == "high":
                    total_score *= 1.3
                
                # Apply VIP customer multiplier
                if context.get("customer", {}).get("segment") == "vip":
                    total_score *= self.routing_config["vip_customer_multiplier"]
                
                # Generate reasoning
                reasoning = self._generate_agent_reasoning(
                    skill_score, availability_score, performance_score, 
                    workload_score, platform_score, customer_preference_score
                )
                
                agent_score = AgentScore(
                    agent_id=str(agent["_id"]),
                    agent_name=agent.get("name", "Unknown"),
                    score=min(total_score, 1.0),  # Cap at 1.0
                    availability=agent.get("status", "unknown"),
                    current_workload=agent.get("current_workload", 0),
                    skill_match=skill_score,
                    performance_score=performance_score,
                    platform_expertise=platform_score,
                    customer_preference=customer_preference_score,
                    reasoning=reasoning,
                    metadata={
                        "skills": agent.get("skills", []),
                        "languages": agent.get("languages", []),
                        "experience_years": agent.get("experienceYears", 0),
                        "specializations": agent.get("specializations", [])
                    }
                )
                
                agent_scores.append(agent_score)
            
        except Exception as e:
            logger.error(f"❌ Failed to score agents: {e}")
        
        return agent_scores
    
    async def _calculate_skill_score(
        self,
        agent: Dict[str, Any],
        intent_result: Dict[str, Any],
        message_analysis: Dict[str, Any]
    ) -> float:
        """Calculate agent's skill match score"""
        try:
            agent_skills = agent.get("skills", [])
            agent_specializations = agent.get("specializations", [])
            
            primary_intent = intent_result.get("primary", "")
            
            # Find required skill category
            required_category = None
            for category, intents in self.skill_categories.items():
                if primary_intent in intents:
                    required_category = category
                    break
            
            if not required_category:
                return 0.5  # Neutral score for unknown intents
            
            # Check if agent has the required skill
            if required_category in agent_skills:
                base_score = 0.8
            else:
                base_score = 0.3
            
            # Bonus for specializations
            if required_category in agent_specializations:
                base_score += 0.2
            
            # Bonus for experience
            experience_years = agent.get("experienceYears", 0)
            experience_bonus = min(experience_years * 0.02, 0.1)  # Max 10% bonus
            
            return min(base_score + experience_bonus, 1.0)
            
        except Exception as e:
            logger.error(f"❌ Failed to calculate skill score: {e}")
            return 0.5
    
    def _calculate_availability_score(self, agent: Dict[str, Any]) -> float:
        """Calculate agent's availability score"""
        status = agent.get("status", "unavailable")
        
        availability_scores = {
            "available": 1.0,
            "busy": 0.6,
            "away": 0.3,
            "unavailable": 0.0
        }
        
        return availability_scores.get(status, 0.0)
    
    async def _calculate_performance_score(self, agent: Dict[str, Any]) -> float:
        """Calculate agent's performance score"""
        try:
            agent_id = agent["_id"]
            
            # Get recent performance metrics
            thirty_days_ago = datetime.now() - timedelta(days=30)
            
            # Calculate average response time
            response_times = list(self.mongodb.chatbot.conversations.aggregate([
                {
                    "$match": {
                        "assignedAgent": agent_id,
                        "createdAt": {"$gte": thirty_days_ago},
                        "firstResponseTime": {"$exists": True}
                    }
                },
                {
                    "$group": {
                        "_id": None,
                        "avg_response_time": {"$avg": "$firstResponseTime"}
                    }
                }
            ]))
            
            avg_response_time = response_times[0]["avg_response_time"] if response_times else 300000  # 5 minutes default
            
            # Calculate customer satisfaction
            satisfaction_scores = list(self.mongodb.chatbot.conversations.aggregate([
                {
                    "$match": {
                        "assignedAgent": agent_id,
                        "createdAt": {"$gte": thirty_days_ago},
                        "customerSatisfaction.rating": {"$exists": True}
                    }
                },
                {
                    "$group": {
                        "_id": None,
                        "avg_satisfaction": {"$avg": "$customerSatisfaction.rating"}
                    }
                }
            ]))
            
            avg_satisfaction = satisfaction_scores[0]["avg_satisfaction"] if satisfaction_scores else 3.0
            
            # Calculate resolution rate
            total_conversations = self.mongodb.chatbot.conversations.count_documents({
                "assignedAgent": agent_id,
                "createdAt": {"$gte": thirty_days_ago}
            })
            
            resolved_conversations = self.mongodb.chatbot.conversations.count_documents({
                "assignedAgent": agent_id,
                "createdAt": {"$gte": thirty_days_ago},
                "status": "resolved"
            })
            
            resolution_rate = resolved_conversations / total_conversations if total_conversations > 0 else 0.5
            
            # Combine metrics
            response_score = max(0, 1 - (avg_response_time / 600000))  # 10 minutes max
            satisfaction_score = (avg_satisfaction - 1) / 4  # Normalize 1-5 to 0-1
            
            performance_score = (response_score * 0.3 + satisfaction_score * 0.4 + resolution_rate * 0.3)
            
            return min(performance_score, 1.0)
            
        except Exception as e:
            logger.error(f"❌ Failed to calculate performance score: {e}")
            return 0.5
    
    def _calculate_workload_score(self, agent: Dict[str, Any]) -> float:
        """Calculate workload score (inverse of current workload)"""
        current_workload = agent.get("current_workload", 0)
        max_workload = self.routing_config["max_concurrent_conversations"]
        
        # Higher score for lower workload
        workload_ratio = current_workload / max_workload
        return max(0, 1 - workload_ratio)
    
    def _calculate_platform_expertise_score(self, agent: Dict[str, Any], platform: str) -> float:
        """Calculate platform expertise score"""
        platform_support = agent.get("platformSupport", {})
        
        if not platform_support.get(platform, False):
            return 0.0
        
        # Check for platform-specific experience
        platform_experience = agent.get("platformExperience", {})
        experience_months = platform_experience.get(platform, 0)
        
        # Score based on experience (max 12 months for full score)
        experience_score = min(experience_months / 12, 1.0)
        
        return 0.5 + (experience_score * 0.5)  # Base 0.5 + up to 0.5 for experience
    
    def _calculate_customer_preference_score(self, agent: Dict[str, Any], context: Dict[str, Any]) -> float:
        """Calculate customer preference score"""
        preferred_agents = context.get("preferred_agents", [])
        agent_id = str(agent["_id"])
        
        if agent_id in preferred_agents:
            return 1.0
        
        # Check if customer has had positive interactions with this agent
        customer_id = context.get("customer", {}).get("_id")
        if customer_id:
            positive_interactions = self.mongodb.chatbot.conversations.count_documents({
                "customerId": customer_id,
                "assignedAgent": agent["_id"],
                "customerSatisfaction.rating": {"$gte": 4}
            })
            
            if positive_interactions > 0:
                return 0.8
        
        return 0.5  # Neutral score
    
    def _generate_agent_reasoning(
        self,
        skill_score: float,
        availability_score: float,
        performance_score: float,
        workload_score: float,
        platform_score: float,
        customer_preference_score: float
    ) -> List[str]:
        """Generate human-readable reasoning for agent selection"""
        reasoning = []
        
        if skill_score > 0.7:
            reasoning.append("Strong skill match for this type of inquiry")
        elif skill_score < 0.4:
            reasoning.append("Limited skill match, may need support")
        
        if availability_score == 1.0:
            reasoning.append("Fully available")
        elif availability_score > 0.5:
            reasoning.append("Currently busy but can take new conversations")
        
        if performance_score > 0.7:
            reasoning.append("Excellent recent performance metrics")
        elif performance_score < 0.4:
            reasoning.append("Below average recent performance")
        
        if workload_score > 0.8:
            reasoning.append("Low current workload")
        elif workload_score < 0.3:
            reasoning.append("High current workload")
        
        if platform_score > 0.8:
            reasoning.append("Expert in this platform")
        
        if customer_preference_score > 0.8:
            reasoning.append("Preferred by customer or positive history")
        
        return reasoning
    
    def _calculate_routing_confidence(self, sorted_agents: List[AgentScore]) -> float:
        """Calculate confidence in routing decision"""
        if len(sorted_agents) < 2:
            return 0.5
        
        # Confidence based on score difference between top 2 agents
        score_diff = sorted_agents[0].score - sorted_agents[1].score
        confidence = min(0.5 + score_diff, 1.0)
        
        return confidence
    
    async def _estimate_wait_time(self, agent: AgentScore) -> int:
        """Estimate wait time for agent in seconds"""
        try:
            # Base wait time on current workload and availability
            base_wait = 30  # 30 seconds base
            
            if agent.availability == "available":
                return base_wait
            elif agent.availability == "busy":
                # Estimate based on average conversation duration
                avg_duration = 600  # 10 minutes default
                return base_wait + (agent.current_workload * avg_duration // 2)
            else:
                return 300  # 5 minutes for other statuses
                
        except Exception as e:
            logger.error(f"❌ Failed to estimate wait time: {e}")
            return 120  # 2 minutes default
    
    def _generate_routing_reason(self, agent: AgentScore) -> str:
        """Generate human-readable routing reason"""
        reasons = []
        
        if agent.skill_match > 0.7:
            reasons.append("excellent skill match")
        if agent.availability == "available":
            reasons.append("immediately available")
        if agent.performance_score > 0.7:
            reasons.append("high performance rating")
        if agent.current_workload == 0:
            reasons.append("no current workload")
        
        if reasons:
            return f"Selected for {', '.join(reasons)}"
        else:
            return "Best available option based on current criteria"
    
    async def _should_suggest_escalation(
        self,
        context: Dict[str, Any],
        message_analysis: Dict[str, Any]
    ) -> bool:
        """Determine if escalation should be suggested"""
        # Check for escalation triggers
        urgency = message_analysis.get("urgency", "medium")
        sentiment = message_analysis.get("sentiment", {}).get("label", "neutral")
        
        if urgency == "urgent" or sentiment in ["negative", "very_negative"]:
            return True
        
        # Check customer segment
        if context.get("customer", {}).get("segment") == "vip":
            return True
        
        # Check conversation history
        previous_conversations = context.get("previous_conversations", [])
        recent_escalations = len([
            conv for conv in previous_conversations 
            if conv.get("escalationCount", 0) > 0
        ])
        
        if recent_escalations > 1:
            return True
        
        return False
    
    async def _handle_no_agents_available(
        self,
        context: Dict[str, Any],
        urgency: str
    ) -> RoutingDecision:
        """Handle case when no agents are available"""
        # Create a placeholder decision for queue management
        return RoutingDecision(
            recommended_agent=None,
            alternative_agents=[],
            routing_confidence=0.0,
            priority=RoutingPriority(urgency),
            estimated_wait_time=600,  # 10 minutes
            routing_reason="No agents currently available - added to queue",
            escalation_suggested=urgency in ["urgent", "high"],
            metadata={
                "queue_position": await self._get_queue_position(urgency),
                "no_agents_available": True
            }
        )
    
    async def _fallback_routing(self, platform: str) -> RoutingDecision:
        """Fallback routing when ML routing fails"""
        # Simple round-robin or random assignment
        available_agents = await self._get_available_agents(platform)
        
        if available_agents:
            # Select agent with lowest workload
            selected_agent = min(available_agents, key=lambda x: x.get("current_workload", 0))
            
            agent_score = AgentScore(
                agent_id=str(selected_agent["_id"]),
                agent_name=selected_agent.get("name", "Unknown"),
                score=0.5,
                availability=selected_agent.get("status", "unknown"),
                current_workload=selected_agent.get("current_workload", 0),
                skill_match=0.5,
                performance_score=0.5,
                platform_expertise=0.5,
                customer_preference=0.5,
                reasoning=["Fallback routing - lowest workload"],
                metadata={}
            )
            
            return RoutingDecision(
                recommended_agent=agent_score,
                alternative_agents=[],
                routing_confidence=0.3,
                priority=RoutingPriority.MEDIUM,
                estimated_wait_time=120,
                routing_reason="Fallback routing due to system error",
                escalation_suggested=False,
                metadata={"fallback_routing": True}
            )
        
        return await self._handle_no_agents_available({}, "medium")
    
    async def _get_queue_position(self, urgency: str) -> int:
        """Get position in queue based on urgency"""
        # Count conversations waiting with same or higher priority
        priority_order = {"urgent": 0, "high": 1, "medium": 2, "low": 3}
        current_priority = priority_order.get(urgency, 2)
        
        waiting_conversations = self.mongodb.chatbot.conversations.count_documents({
            "status": "pending",
            "urgency": {"$in": [k for k, v in priority_order.items() if v <= current_priority]}
        })
        
        return waiting_conversations + 1
    
    async def _log_routing_decision(self, decision: RoutingDecision, conversation_id: str):
        """Log routing decision for analytics and improvement"""
        try:
            log_data = {
                "conversation_id": conversation_id,
                "recommended_agent_id": decision.recommended_agent.agent_id if decision.recommended_agent else None,
                "routing_confidence": decision.routing_confidence,
                "priority": decision.priority.value,
                "estimated_wait_time": decision.estimated_wait_time,
                "escalation_suggested": decision.escalation_suggested,
                "timestamp": datetime.now(),
                "metadata": decision.metadata
            }
            
            self.mongodb.chatbot.routing_decisions.insert_one(log_data)
            
        except Exception as e:
            logger.error(f"❌ Failed to log routing decision: {e}")
    
    def _is_business_hours(self) -> bool:
        """Check if current time is within business hours"""
        now = datetime.now()
        return (
            now.weekday() < 6 and  # Monday to Saturday
            8 <= now.hour < 20     # 8 AM to 8 PM
        )
    
    async def _train_routing_model(self):
        """Train ML model for routing decisions"""
        try:
            logger.info("Training routing model...")
            
            # Get historical routing data
            # This would involve collecting features and outcomes
            # For now, we'll use rule-based routing
            
            logger.info("✅ Routing model training completed")
            
        except Exception as e:
            logger.error(f"❌ Failed to train routing model: {e}")
    
    async def get_routing_analytics(self, days: int = 30) -> Dict[str, Any]:
        """Get routing performance analytics"""
        try:
            start_date = datetime.now() - timedelta(days=days)
            
            # Get routing decisions
            decisions = list(self.mongodb.chatbot.routing_decisions.find({
                "timestamp": {"$gte": start_date}
            }))
            
            total_decisions = len(decisions)
            high_confidence = len([d for d in decisions if d["routing_confidence"] > 0.7])
            escalations = len([d for d in decisions if d["escalation_suggested"]])
            
            analytics = {
                "total_routing_decisions": total_decisions,
                "high_confidence_decisions": high_confidence,
                "escalation_rate": escalations / total_decisions if total_decisions > 0 else 0,
                "average_confidence": np.mean([d["routing_confidence"] for d in decisions]) if decisions else 0,
                "average_wait_time": np.mean([d["estimated_wait_time"] for d in decisions]) if decisions else 0,
                "routing_by_priority": {},
                "agent_utilization": {}
            }
            
            return analytics
            
        except Exception as e:
            logger.error(f"❌ Failed to get routing analytics: {e}")
            return {}
