"""
=============================================
🤖 AI MESSAGE SUGGESTION SERVICE
Context-aware message suggestions for agents
Real-time suggestions based on conversation history
=============================================
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import json
import re
from dataclasses import dataclass

import numpy as np
from transformers import pipeline, AutoTokenizer, AutoModel
import torch
from sklearn.metrics.pairwise import cosine_similarity
from pymongo import MongoClient
import redis

from .ConversationAnalysisService import ConversationAnalysisService
from .IntentDetectionService import IntentDetectionService
from ..utils.cache_manager import CacheManager
from ..utils.text_processor import TextProcessor

logger = logging.getLogger(__name__)

@dataclass
class MessageSuggestion:
    """Message suggestion data structure"""
    text: str
    confidence: float
    category: str
    intent: str
    platform_specific: bool
    context_relevance: float
    urgency_level: str
    suggested_actions: List[str]
    metadata: Dict[str, Any]

class MessageSuggestionService:
    """AI-powered message suggestion service"""
    
    def __init__(self, mongodb_client: MongoClient, redis_client: redis.Redis):
        self.mongodb = mongodb_client
        self.redis = redis_client
        self.cache_manager = CacheManager(redis_client)
        self.text_processor = TextProcessor()
        
        # Initialize AI models
        self.conversation_analyzer = ConversationAnalysisService(mongodb_client, redis_client)
        self.intent_detector = IntentDetectionService(mongodb_client, redis_client)
        
        # Initialize suggestion models
        self.suggestion_model = None
        self.tokenizer = None
        self.embedding_model = None
        
        # Suggestion templates by category
        self.suggestion_templates = {
            'greeting': [
                "Bonjour ! Comment puis-je vous aider aujourd'hui ?",
                "Bonsoir ! Je suis là pour vous assister.",
                "Salut ! Que puis-je faire pour vous ?",
                "Bonjour {customer_name}, ravi de vous retrouver !"
            ],
            'technical_support': [
                "Je comprends votre problème technique. Laissez-moi vous aider à le résoudre.",
                "Pour résoudre ce problème, pouvez-vous me dire quel appareil vous utilisez ?",
                "Je vais vérifier votre configuration. Un moment s'il vous plaît.",
                "Avez-vous essayé de redémarrer votre {device_type} ?"
            ],
            'billing_inquiry': [
                "Je vais vérifier votre facture immédiatement.",
                "Concernant votre facturation, laissez-moi consulter votre compte.",
                "Je comprends votre préoccupation concernant votre facture.",
                "Votre prochaine facture sera disponible le {next_billing_date}."
            ],
            'service_activation': [
                "Je vais activer ce service pour vous dès maintenant.",
                "L'activation de votre service prendra environ {activation_time}.",
                "Votre service sera actif dans les prochaines minutes.",
                "Je confirme l'activation de votre {service_name}."
            ],
            'complaint_resolution': [
                "Je comprends votre frustration et je vais tout faire pour résoudre cela.",
                "Merci de m'avoir signalé ce problème. Je vais l'escalader immédiatement.",
                "Je m'excuse pour ce désagrément. Laissez-moi corriger cela.",
                "Votre satisfaction est notre priorité. Je vais résoudre cela rapidement."
            ],
            'closing': [
                "Y a-t-il autre chose que je puisse faire pour vous ?",
                "J'espère avoir pu vous aider. Bonne journée !",
                "N'hésitez pas à nous recontacter si vous avez d'autres questions.",
                "Merci d'avoir choisi Free Mobile. À bientôt !"
            ],
            'escalation': [
                "Je vais transférer votre demande à un spécialiste.",
                "Laissez-moi vous mettre en relation avec un superviseur.",
                "Ce cas nécessite une expertise particulière. Je vous transfère.",
                "Un expert va prendre en charge votre demande."
            ]
        }
        
        # Platform-specific adaptations
        self.platform_adaptations = {
            'whatsapp': {
                'max_length': 4096,
                'supports_emojis': True,
                'supports_formatting': True,
                'supports_media': True
            },
            'facebook': {
                'max_length': 2000,
                'supports_emojis': True,
                'supports_quick_replies': True,
                'supports_buttons': True
            },
            'instagram': {
                'max_length': 1000,
                'supports_emojis': True,
                'supports_media': True,
                'casual_tone': True
            },
            'twitter': {
                'max_length': 280,
                'supports_hashtags': True,
                'supports_mentions': True,
                'concise_required': True
            }
        }
        
        # Initialize models
        asyncio.create_task(self._initialize_models())
    
    async def _initialize_models(self):
        """Initialize AI models for suggestions"""
        try:
            logger.info("Initializing message suggestion models...")
            
            # Load pre-trained models
            self.tokenizer = AutoTokenizer.from_pretrained('microsoft/DialoGPT-medium')
            self.suggestion_model = pipeline(
                'text-generation',
                model='microsoft/DialoGPT-medium',
                tokenizer=self.tokenizer,
                device=0 if torch.cuda.is_available() else -1
            )
            
            # Load embedding model for similarity
            self.embedding_model = AutoModel.from_pretrained('sentence-transformers/all-MiniLM-L6-v2')
            
            logger.info("✅ Message suggestion models initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize suggestion models: {e}")
            raise
    
    async def get_message_suggestions(
        self,
        conversation_id: str,
        customer_message: str,
        platform: str,
        agent_id: str,
        context: Optional[Dict[str, Any]] = None
    ) -> List[MessageSuggestion]:
        """
        Generate AI-powered message suggestions for agents
        
        Args:
            conversation_id: ID of the conversation
            customer_message: Latest customer message
            platform: Platform (whatsapp, facebook, instagram, twitter)
            agent_id: ID of the agent
            context: Additional context information
            
        Returns:
            List of message suggestions
        """
        try:
            logger.info(f"🤖 Generating suggestions for conversation {conversation_id}")
            
            # Get conversation context
            conversation_context = await self._get_conversation_context(conversation_id)
            
            # Analyze customer message
            message_analysis = await self.conversation_analyzer.analyze_message(
                customer_message, conversation_context
            )
            
            # Detect intent
            intent_result = await self.intent_detector.detect_intent(
                customer_message, conversation_context
            )
            
            # Generate suggestions based on different strategies
            suggestions = []
            
            # 1. Template-based suggestions
            template_suggestions = await self._generate_template_suggestions(
                intent_result, message_analysis, platform, conversation_context
            )
            suggestions.extend(template_suggestions)
            
            # 2. AI-generated suggestions
            if self.suggestion_model:
                ai_suggestions = await self._generate_ai_suggestions(
                    customer_message, conversation_context, platform, intent_result
                )
                suggestions.extend(ai_suggestions)
            
            # 3. Historical pattern suggestions
            pattern_suggestions = await self._generate_pattern_suggestions(
                agent_id, intent_result, platform, conversation_context
            )
            suggestions.extend(pattern_suggestions)
            
            # 4. Context-aware suggestions
            context_suggestions = await self._generate_context_suggestions(
                conversation_context, message_analysis, platform
            )
            suggestions.extend(context_suggestions)
            
            # Rank and filter suggestions
            ranked_suggestions = await self._rank_suggestions(
                suggestions, customer_message, conversation_context, platform
            )
            
            # Apply platform-specific adaptations
            adapted_suggestions = await self._adapt_for_platform(
                ranked_suggestions, platform
            )
            
            logger.info(f"✅ Generated {len(adapted_suggestions)} suggestions")
            return adapted_suggestions[:5]  # Return top 5 suggestions
            
        except Exception as e:
            logger.error(f"❌ Failed to generate suggestions: {e}")
            return []
    
    async def _get_conversation_context(self, conversation_id: str) -> Dict[str, Any]:
        """Get comprehensive conversation context"""
        try:
            # Check cache first
            cache_key = f"conversation_context:{conversation_id}"
            cached_context = await self.cache_manager.get(cache_key)
            if cached_context:
                return json.loads(cached_context)
            
            # Get conversation from database
            conversation = self.mongodb.chatbot.conversations.find_one(
                {"_id": conversation_id}
            )
            
            if not conversation:
                return {}
            
            # Get recent messages
            messages = list(self.mongodb.chatbot.messages.find(
                {"conversationId": conversation_id}
            ).sort("timestamp", -1).limit(20))
            
            # Get customer information
            customer = self.mongodb.chatbot.customers.find_one(
                {"_id": conversation.get("customerId")}
            )
            
            # Build context
            context = {
                "conversation_id": conversation_id,
                "customer": {
                    "id": customer.get("_id") if customer else None,
                    "name": customer.get("name") if customer else "Client",
                    "segment": customer.get("segment", "standard"),
                    "history": customer.get("analytics", {}) if customer else {},
                    "preferences": customer.get("preferences", {}) if customer else {}
                },
                "conversation": {
                    "status": conversation.get("status", "open"),
                    "urgency": conversation.get("urgency", "medium"),
                    "platform": conversation.get("platform"),
                    "duration": conversation.get("duration", 0),
                    "message_count": len(messages),
                    "sentiment": conversation.get("sentiment", "neutral"),
                    "escalation_count": conversation.get("escalationCount", 0)
                },
                "recent_messages": [
                    {
                        "text": msg.get("text", ""),
                        "direction": msg.get("direction"),
                        "timestamp": msg.get("timestamp"),
                        "type": msg.get("type", "text"),
                        "analysis": msg.get("analysis", {})
                    }
                    for msg in messages
                ],
                "current_time": datetime.now().isoformat(),
                "business_hours": self._is_business_hours()
            }
            
            # Cache context for 5 minutes
            await self.cache_manager.set(
                cache_key, json.dumps(context, default=str), ttl=300
            )
            
            return context
            
        except Exception as e:
            logger.error(f"❌ Failed to get conversation context: {e}")
            return {}
    
    async def _generate_template_suggestions(
        self,
        intent_result: Dict[str, Any],
        message_analysis: Dict[str, Any],
        platform: str,
        context: Dict[str, Any]
    ) -> List[MessageSuggestion]:
        """Generate suggestions based on predefined templates"""
        suggestions = []
        
        try:
            primary_intent = intent_result.get("primary", "general")
            confidence = intent_result.get("confidence", 0.5)
            
            # Map intent to template category
            category_mapping = {
                "greeting": "greeting",
                "technical_issue": "technical_support",
                "billing_question": "billing_inquiry",
                "service_request": "service_activation",
                "complaint": "complaint_resolution",
                "goodbye": "closing",
                "escalation_request": "escalation"
            }
            
            category = category_mapping.get(primary_intent, "general")
            templates = self.suggestion_templates.get(category, [])
            
            for template in templates:
                # Personalize template
                personalized_text = await self._personalize_template(
                    template, context
                )
                
                suggestion = MessageSuggestion(
                    text=personalized_text,
                    confidence=confidence * 0.8,  # Template confidence
                    category=category,
                    intent=primary_intent,
                    platform_specific=False,
                    context_relevance=0.7,
                    urgency_level=message_analysis.get("urgency", "medium"),
                    suggested_actions=self._get_suggested_actions(category),
                    metadata={
                        "source": "template",
                        "template_id": template,
                        "personalized": True
                    }
                )
                
                suggestions.append(suggestion)
            
        except Exception as e:
            logger.error(f"❌ Failed to generate template suggestions: {e}")
        
        return suggestions
    
    async def _generate_ai_suggestions(
        self,
        customer_message: str,
        context: Dict[str, Any],
        platform: str,
        intent_result: Dict[str, Any]
    ) -> List[MessageSuggestion]:
        """Generate AI-powered suggestions using language models"""
        suggestions = []
        
        try:
            if not self.suggestion_model:
                return suggestions
            
            # Prepare conversation history for context
            conversation_history = ""
            recent_messages = context.get("recent_messages", [])
            
            for msg in recent_messages[-5:]:  # Last 5 messages
                role = "Agent" if msg["direction"] == "outbound" else "Client"
                conversation_history += f"{role}: {msg['text']}\n"
            
            # Add current customer message
            conversation_history += f"Client: {customer_message}\nAgent:"
            
            # Generate response
            generated = self.suggestion_model(
                conversation_history,
                max_length=len(conversation_history) + 100,
                num_return_sequences=3,
                temperature=0.7,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id
            )
            
            for i, response in enumerate(generated):
                # Extract agent response
                full_text = response['generated_text']
                agent_response = full_text.split("Agent:")[-1].strip()
                
                # Clean up response
                agent_response = self._clean_generated_text(agent_response)
                
                if agent_response and len(agent_response) > 10:
                    suggestion = MessageSuggestion(
                        text=agent_response,
                        confidence=0.6 - (i * 0.1),  # Decreasing confidence
                        category="ai_generated",
                        intent=intent_result.get("primary", "general"),
                        platform_specific=False,
                        context_relevance=0.8,
                        urgency_level=intent_result.get("urgency", "medium"),
                        suggested_actions=["send_message"],
                        metadata={
                            "source": "ai_model",
                            "model": "DialoGPT",
                            "generation_rank": i + 1
                        }
                    )
                    
                    suggestions.append(suggestion)
            
        except Exception as e:
            logger.error(f"❌ Failed to generate AI suggestions: {e}")
        
        return suggestions
    
    async def _generate_pattern_suggestions(
        self,
        agent_id: str,
        intent_result: Dict[str, Any],
        platform: str,
        context: Dict[str, Any]
    ) -> List[MessageSuggestion]:
        """Generate suggestions based on historical patterns"""
        suggestions = []
        
        try:
            # Get agent's successful responses for similar intents
            similar_responses = self.mongodb.chatbot.messages.find({
                "agentId": agent_id,
                "direction": "outbound",
                "analysis.intent.primary": intent_result.get("primary"),
                "analysis.effectiveness": {"$gte": 0.7},  # Successful responses
                "platform": platform
            }).limit(10)
            
            for response in similar_responses:
                text = response.get("text", "")
                if text and len(text) > 10:
                    suggestion = MessageSuggestion(
                        text=text,
                        confidence=response.get("analysis", {}).get("effectiveness", 0.5),
                        category="historical_pattern",
                        intent=intent_result.get("primary", "general"),
                        platform_specific=True,
                        context_relevance=0.6,
                        urgency_level=intent_result.get("urgency", "medium"),
                        suggested_actions=["send_message"],
                        metadata={
                            "source": "historical_pattern",
                            "agent_id": agent_id,
                            "effectiveness": response.get("analysis", {}).get("effectiveness")
                        }
                    )
                    
                    suggestions.append(suggestion)
            
        except Exception as e:
            logger.error(f"❌ Failed to generate pattern suggestions: {e}")
        
        return suggestions
    
    async def _generate_context_suggestions(
        self,
        context: Dict[str, Any],
        message_analysis: Dict[str, Any],
        platform: str
    ) -> List[MessageSuggestion]:
        """Generate context-aware suggestions"""
        suggestions = []
        
        try:
            conversation = context.get("conversation", {})
            customer = context.get("customer", {})
            
            # Urgency-based suggestions
            if conversation.get("urgency") == "urgent":
                urgent_text = f"Je comprends l'urgence de votre situation, {customer.get('name', 'Monsieur/Madame')}. Je vais traiter cela en priorité."
                suggestions.append(MessageSuggestion(
                    text=urgent_text,
                    confidence=0.9,
                    category="urgency_response",
                    intent="urgent_handling",
                    platform_specific=False,
                    context_relevance=1.0,
                    urgency_level="urgent",
                    suggested_actions=["escalate", "prioritize"],
                    metadata={"source": "context_urgency"}
                ))
            
            # VIP customer suggestions
            if customer.get("segment") == "vip":
                vip_text = f"Bonjour {customer.get('name')}, en tant que client privilégié, je vais personnellement m'occuper de votre demande."
                suggestions.append(MessageSuggestion(
                    text=vip_text,
                    confidence=0.8,
                    category="vip_service",
                    intent="vip_greeting",
                    platform_specific=False,
                    context_relevance=0.9,
                    urgency_level="high",
                    suggested_actions=["personalize", "prioritize"],
                    metadata={"source": "context_vip"}
                ))
            
            # Time-based suggestions
            if not context.get("business_hours"):
                after_hours_text = "Merci de nous contacter. Bien que nous soyons en dehors des heures d'ouverture, je vais faire mon possible pour vous aider."
                suggestions.append(MessageSuggestion(
                    text=after_hours_text,
                    confidence=0.7,
                    category="after_hours",
                    intent="after_hours_service",
                    platform_specific=False,
                    context_relevance=0.8,
                    urgency_level="medium",
                    suggested_actions=["acknowledge_time"],
                    metadata={"source": "context_time"}
                ))
            
        except Exception as e:
            logger.error(f"❌ Failed to generate context suggestions: {e}")
        
        return suggestions
    
    async def _rank_suggestions(
        self,
        suggestions: List[MessageSuggestion],
        customer_message: str,
        context: Dict[str, Any],
        platform: str
    ) -> List[MessageSuggestion]:
        """Rank suggestions by relevance and quality"""
        try:
            if not suggestions:
                return []
            
            # Calculate relevance scores
            for suggestion in suggestions:
                # Base score from confidence
                score = suggestion.confidence
                
                # Context relevance bonus
                score += suggestion.context_relevance * 0.3
                
                # Platform-specific bonus
                if suggestion.platform_specific:
                    score += 0.1
                
                # Urgency alignment
                conversation_urgency = context.get("conversation", {}).get("urgency", "medium")
                if suggestion.urgency_level == conversation_urgency:
                    score += 0.2
                
                # Length appropriateness for platform
                platform_config = self.platform_adaptations.get(platform, {})
                max_length = platform_config.get("max_length", 1000)
                if len(suggestion.text) <= max_length:
                    score += 0.1
                else:
                    score -= 0.2
                
                # Update confidence with final score
                suggestion.confidence = min(score, 1.0)
            
            # Sort by confidence (descending)
            ranked_suggestions = sorted(
                suggestions, 
                key=lambda x: x.confidence, 
                reverse=True
            )
            
            # Remove duplicates
            unique_suggestions = []
            seen_texts = set()
            
            for suggestion in ranked_suggestions:
                normalized_text = self.text_processor.normalize_text(suggestion.text)
                if normalized_text not in seen_texts:
                    seen_texts.add(normalized_text)
                    unique_suggestions.append(suggestion)
            
            return unique_suggestions
            
        except Exception as e:
            logger.error(f"❌ Failed to rank suggestions: {e}")
            return suggestions
    
    async def _adapt_for_platform(
        self,
        suggestions: List[MessageSuggestion],
        platform: str
    ) -> List[MessageSuggestion]:
        """Adapt suggestions for specific platform requirements"""
        adapted_suggestions = []
        
        try:
            platform_config = self.platform_adaptations.get(platform, {})
            
            for suggestion in suggestions:
                adapted_text = suggestion.text
                
                # Length adaptation
                max_length = platform_config.get("max_length", 1000)
                if len(adapted_text) > max_length:
                    adapted_text = adapted_text[:max_length-3] + "..."
                
                # Platform-specific formatting
                if platform == "twitter" and platform_config.get("concise_required"):
                    adapted_text = self._make_concise(adapted_text)
                
                if platform == "instagram" and platform_config.get("casual_tone"):
                    adapted_text = self._make_casual(adapted_text)
                
                # Add emojis if supported
                if platform_config.get("supports_emojis"):
                    adapted_text = self._add_appropriate_emojis(adapted_text, suggestion.category)
                
                # Create adapted suggestion
                adapted_suggestion = MessageSuggestion(
                    text=adapted_text,
                    confidence=suggestion.confidence,
                    category=suggestion.category,
                    intent=suggestion.intent,
                    platform_specific=True,
                    context_relevance=suggestion.context_relevance,
                    urgency_level=suggestion.urgency_level,
                    suggested_actions=suggestion.suggested_actions,
                    metadata={
                        **suggestion.metadata,
                        "adapted_for": platform,
                        "original_length": len(suggestion.text),
                        "adapted_length": len(adapted_text)
                    }
                )
                
                adapted_suggestions.append(adapted_suggestion)
            
        except Exception as e:
            logger.error(f"❌ Failed to adapt suggestions for platform: {e}")
            return suggestions
        
        return adapted_suggestions
    
    def _personalize_template(self, template: str, context: Dict[str, Any]) -> str:
        """Personalize template with context information"""
        try:
            customer = context.get("customer", {})
            conversation = context.get("conversation", {})
            
            # Replace placeholders
            personalized = template.replace(
                "{customer_name}", customer.get("name", "Monsieur/Madame")
            )
            
            # Add more personalizations as needed
            return personalized
            
        except Exception as e:
            logger.error(f"❌ Failed to personalize template: {e}")
            return template
    
    def _get_suggested_actions(self, category: str) -> List[str]:
        """Get suggested actions for a category"""
        action_mapping = {
            "greeting": ["send_message", "check_customer_history"],
            "technical_support": ["send_message", "escalate_technical", "schedule_callback"],
            "billing_inquiry": ["send_message", "check_billing", "send_invoice"],
            "service_activation": ["send_message", "activate_service", "confirm_activation"],
            "complaint_resolution": ["send_message", "escalate", "offer_compensation"],
            "closing": ["send_message", "close_conversation", "send_survey"],
            "escalation": ["escalate", "transfer_to_supervisor", "schedule_callback"]
        }
        
        return action_mapping.get(category, ["send_message"])
    
    def _clean_generated_text(self, text: str) -> str:
        """Clean up AI-generated text"""
        # Remove unwanted patterns
        text = re.sub(r'\n+', ' ', text)  # Replace newlines with spaces
        text = re.sub(r'\s+', ' ', text)  # Normalize whitespace
        text = text.strip()
        
        # Remove incomplete sentences at the end
        sentences = text.split('.')
        if len(sentences) > 1 and len(sentences[-1].strip()) < 10:
            text = '.'.join(sentences[:-1]) + '.'
        
        return text
    
    def _make_concise(self, text: str) -> str:
        """Make text more concise for Twitter"""
        # Simple conciseness rules
        text = text.replace("s'il vous plaît", "svp")
        text = text.replace("Monsieur/Madame", "")
        text = re.sub(r'\s+', ' ', text)
        return text.strip()
    
    def _make_casual(self, text: str) -> str:
        """Make text more casual for Instagram"""
        # Simple casualization rules
        text = text.replace("Bonjour", "Salut")
        text = text.replace("Monsieur/Madame", "")
        return text
    
    def _add_appropriate_emojis(self, text: str, category: str) -> str:
        """Add appropriate emojis based on category"""
        emoji_mapping = {
            "greeting": "👋",
            "technical_support": "🔧",
            "billing_inquiry": "💰",
            "service_activation": "✅",
            "complaint_resolution": "🤝",
            "closing": "😊"
        }
        
        emoji = emoji_mapping.get(category)
        if emoji and not any(e in text for e in emoji_mapping.values()):
            return f"{emoji} {text}"
        
        return text
    
    def _is_business_hours(self) -> bool:
        """Check if current time is within business hours"""
        now = datetime.now()
        # Simple business hours check (8 AM to 8 PM, Monday to Saturday)
        return (
            now.weekday() < 6 and  # Monday to Saturday
            8 <= now.hour < 20     # 8 AM to 8 PM
        )
    
    async def learn_from_feedback(
        self,
        suggestion_id: str,
        feedback: str,
        agent_id: str,
        conversation_id: str
    ):
        """Learn from agent feedback on suggestions"""
        try:
            # Store feedback for model improvement
            feedback_data = {
                "suggestion_id": suggestion_id,
                "feedback": feedback,  # "accepted", "rejected", "modified"
                "agent_id": agent_id,
                "conversation_id": conversation_id,
                "timestamp": datetime.now()
            }
            
            self.mongodb.chatbot.suggestion_feedback.insert_one(feedback_data)
            
            logger.info(f"✅ Stored suggestion feedback: {feedback}")
            
        except Exception as e:
            logger.error(f"❌ Failed to store suggestion feedback: {e}")
    
    async def get_suggestion_analytics(self, agent_id: str, days: int = 30) -> Dict[str, Any]:
        """Get analytics on suggestion usage and effectiveness"""
        try:
            start_date = datetime.now() - timedelta(days=days)
            
            # Get feedback data
            feedback_data = list(self.mongodb.chatbot.suggestion_feedback.find({
                "agent_id": agent_id,
                "timestamp": {"$gte": start_date}
            }))
            
            total_suggestions = len(feedback_data)
            accepted_suggestions = len([f for f in feedback_data if f["feedback"] == "accepted"])
            
            analytics = {
                "total_suggestions": total_suggestions,
                "accepted_suggestions": accepted_suggestions,
                "acceptance_rate": accepted_suggestions / total_suggestions if total_suggestions > 0 else 0,
                "most_used_categories": {},
                "effectiveness_by_platform": {},
                "improvement_suggestions": []
            }
            
            return analytics
            
        except Exception as e:
            logger.error(f"❌ Failed to get suggestion analytics: {e}")
            return {}
