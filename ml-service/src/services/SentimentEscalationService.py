"""
=============================================
🚨 SENTIMENT ESCALATION SERVICE
Real-time sentiment monitoring with automated escalation
Proactive intervention for negative customer experiences
=============================================
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
import json
from dataclasses import dataclass
from enum import Enum

import numpy as np
from pymongo import MongoClient
import redis

from .ConversationAnalysisService import ConversationAnalysisService
from .NotificationService import NotificationService
from ..utils.cache_manager import CacheManager

logger = logging.getLogger(__name__)

class EscalationLevel(Enum):
    """Escalation severity levels"""
    IMMEDIATE = "immediate"    # Requires immediate supervisor intervention
    URGENT = "urgent"         # Escalate within 5 minutes
    HIGH = "high"            # Escalate within 15 minutes
    MEDIUM = "medium"        # Escalate within 30 minutes
    LOW = "low"             # Monitor but no immediate escalation

class EscalationTrigger(Enum):
    """Types of escalation triggers"""
    SENTIMENT_DECLINE = "sentiment_decline"
    NEGATIVE_THRESHOLD = "negative_threshold"
    ANGER_DETECTION = "anger_detection"
    FRUSTRATION_PATTERN = "frustration_pattern"
    THREAT_DETECTION = "threat_detection"
    LEGAL_MENTION = "legal_mention"
    COMPETITOR_MENTION = "competitor_mention"
    CANCELLATION_INTENT = "cancellation_intent"
    REPEATED_COMPLAINTS = "repeated_complaints"
    VIP_CUSTOMER_ISSUE = "vip_customer_issue"

@dataclass
class EscalationAlert:
    """Escalation alert data structure"""
    conversation_id: str
    customer_id: str
    trigger: EscalationTrigger
    level: EscalationLevel
    confidence: float
    sentiment_score: float
    message_text: str
    platform: str
    customer_segment: str
    escalation_reason: str
    recommended_actions: List[str]
    supervisor_notes: str
    timestamp: datetime
    metadata: Dict[str, Any]

class SentimentEscalationService:
    """Real-time sentiment monitoring and escalation service"""
    
    def __init__(self, mongodb_client: MongoClient, redis_client: redis.Redis):
        self.mongodb = mongodb_client
        self.redis = redis_client
        self.cache_manager = CacheManager(redis_client)
        
        # Initialize related services
        self.conversation_analyzer = ConversationAnalysisService(mongodb_client, redis_client)
        self.notification_service = NotificationService(mongodb_client, redis_client)
        
        # Escalation configuration
        self.escalation_config = {
            "sentiment_thresholds": {
                "very_negative": 0.2,
                "negative": 0.4,
                "neutral": 0.6,
                "positive": 0.8
            },
            "escalation_cooldown": 300,  # 5 minutes between escalations
            "sentiment_decline_threshold": 0.3,  # 30% decline triggers escalation
            "consecutive_negative_limit": 3,
            "vip_customer_immediate_escalation": True,
            "business_hours_only": False
        }
        
        # Keyword patterns for different triggers
        self.trigger_patterns = {
            EscalationTrigger.THREAT_DETECTION: [
                "lawsuit", "sue", "legal action", "lawyer", "attorney",
                "court", "tribunal", "complaint authority", "regulator"
            ],
            EscalationTrigger.ANGER_DETECTION: [
                "furious", "outraged", "disgusted", "livid", "enraged",
                "absolutely terrible", "worst service", "hate this"
            ],
            EscalationTrigger.FRUSTRATION_PATTERN: [
                "fed up", "tired of", "sick of", "enough", "ridiculous",
                "unacceptable", "pathetic", "joke", "waste of time"
            ],
            EscalationTrigger.COMPETITOR_MENTION: [
                "orange", "sfr", "bouygues", "switching to", "better with",
                "competitor", "other provider"
            ],
            EscalationTrigger.CANCELLATION_INTENT: [
                "cancel", "terminate", "end contract", "close account",
                "stop service", "unsubscribe", "leave free"
            ],
            EscalationTrigger.LEGAL_MENTION: [
                "dgccrf", "cnil", "arcep", "consumer protection",
                "ombudsman", "mediator", "regulatory complaint"
            ]
        }
        
        # Escalation level mapping
        self.trigger_escalation_levels = {
            EscalationTrigger.THREAT_DETECTION: EscalationLevel.IMMEDIATE,
            EscalationTrigger.LEGAL_MENTION: EscalationLevel.IMMEDIATE,
            EscalationTrigger.VIP_CUSTOMER_ISSUE: EscalationLevel.URGENT,
            EscalationTrigger.ANGER_DETECTION: EscalationLevel.URGENT,
            EscalationTrigger.CANCELLATION_INTENT: EscalationLevel.HIGH,
            EscalationTrigger.COMPETITOR_MENTION: EscalationLevel.HIGH,
            EscalationTrigger.FRUSTRATION_PATTERN: EscalationLevel.MEDIUM,
            EscalationTrigger.SENTIMENT_DECLINE: EscalationLevel.MEDIUM,
            EscalationTrigger.NEGATIVE_THRESHOLD: EscalationLevel.LOW,
            EscalationTrigger.REPEATED_COMPLAINTS: EscalationLevel.HIGH
        }
        
        # Start monitoring
        asyncio.create_task(self._start_monitoring())
    
    async def analyze_message_for_escalation(
        self,
        message_text: str,
        conversation_id: str,
        customer_id: str,
        platform: str
    ) -> Optional[EscalationAlert]:
        """
        Analyze message for escalation triggers
        
        Args:
            message_text: Customer's message text
            conversation_id: ID of the conversation
            customer_id: ID of the customer
            platform: Platform (whatsapp, facebook, instagram, twitter)
            
        Returns:
            EscalationAlert if escalation is needed, None otherwise
        """
        try:
            logger.info(f"🚨 Analyzing message for escalation: {conversation_id}")
            
            # Get conversation context
            context = await self._get_escalation_context(conversation_id, customer_id)
            
            # Analyze sentiment
            sentiment_analysis = await self.conversation_analyzer.analyze_sentiment(
                message_text, context
            )
            
            # Check for escalation triggers
            triggers = await self._detect_escalation_triggers(
                message_text, sentiment_analysis, context
            )
            
            if not triggers:
                return None
            
            # Select highest priority trigger
            primary_trigger = self._select_primary_trigger(triggers)
            
            # Check escalation cooldown
            if not await self._check_escalation_cooldown(conversation_id, primary_trigger):
                logger.info(f"Escalation cooldown active for {conversation_id}")
                return None
            
            # Create escalation alert
            escalation_alert = await self._create_escalation_alert(
                primary_trigger, message_text, sentiment_analysis, 
                conversation_id, customer_id, platform, context
            )
            
            # Process escalation
            await self._process_escalation(escalation_alert)
            
            logger.info(f"✅ Escalation alert created: {primary_trigger.value} - {escalation_alert.level.value}")
            
            return escalation_alert
            
        except Exception as e:
            logger.error(f"❌ Failed to analyze message for escalation: {e}")
            return None
    
    async def _get_escalation_context(self, conversation_id: str, customer_id: str) -> Dict[str, Any]:
        """Get context for escalation analysis"""
        try:
            # Get conversation
            conversation = self.mongodb.chatbot.conversations.find_one({"_id": conversation_id})
            
            # Get customer
            customer = self.mongodb.chatbot.customers.find_one({"_id": customer_id})
            
            # Get recent messages for sentiment trend
            recent_messages = list(self.mongodb.chatbot.messages.find({
                "conversationId": conversation_id,
                "direction": "inbound"
            }).sort("timestamp", -1).limit(10))
            
            # Get customer's escalation history
            escalation_history = list(self.mongodb.chatbot.escalation_alerts.find({
                "customer_id": customer_id
            }).sort("timestamp", -1).limit(5))
            
            # Get previous conversations for pattern analysis
            previous_conversations = list(self.mongodb.chatbot.conversations.find({
                "customerId": customer_id,
                "_id": {"$ne": conversation_id}
            }).sort("createdAt", -1).limit(3))
            
            context = {
                "conversation": conversation or {},
                "customer": customer or {},
                "recent_messages": recent_messages,
                "escalation_history": escalation_history,
                "previous_conversations": previous_conversations,
                "current_time": datetime.now(),
                "business_hours": self._is_business_hours()
            }
            
            return context
            
        except Exception as e:
            logger.error(f"❌ Failed to get escalation context: {e}")
            return {}
    
    async def _detect_escalation_triggers(
        self,
        message_text: str,
        sentiment_analysis: Dict[str, Any],
        context: Dict[str, Any]
    ) -> List[Tuple[EscalationTrigger, float]]:
        """Detect escalation triggers in message and context"""
        triggers = []
        
        try:
            message_lower = message_text.lower()
            sentiment_score = sentiment_analysis.get("score", 0.5)
            sentiment_label = sentiment_analysis.get("label", "neutral")
            
            # 1. Sentiment threshold triggers
            if sentiment_score <= self.escalation_config["sentiment_thresholds"]["very_negative"]:
                triggers.append((EscalationTrigger.NEGATIVE_THRESHOLD, 0.9))
            elif sentiment_score <= self.escalation_config["sentiment_thresholds"]["negative"]:
                triggers.append((EscalationTrigger.NEGATIVE_THRESHOLD, 0.6))
            
            # 2. Sentiment decline trigger
            sentiment_decline = await self._calculate_sentiment_decline(context)
            if sentiment_decline >= self.escalation_config["sentiment_decline_threshold"]:
                triggers.append((EscalationTrigger.SENTIMENT_DECLINE, sentiment_decline))
            
            # 3. Keyword-based triggers
            for trigger_type, keywords in self.trigger_patterns.items():
                for keyword in keywords:
                    if keyword in message_lower:
                        confidence = 0.8 + (len(keyword) / 100)  # Longer keywords = higher confidence
                        triggers.append((trigger_type, min(confidence, 1.0)))
                        break
            
            # 4. VIP customer trigger
            customer = context.get("customer", {})
            if customer.get("segment") == "vip" and sentiment_label in ["negative", "very_negative"]:
                triggers.append((EscalationTrigger.VIP_CUSTOMER_ISSUE, 1.0))
            
            # 5. Repeated complaints trigger
            if await self._detect_repeated_complaints(context):
                triggers.append((EscalationTrigger.REPEATED_COMPLAINTS, 0.8))
            
            # 6. Consecutive negative messages
            consecutive_negative = await self._count_consecutive_negative_messages(context)
            if consecutive_negative >= self.escalation_config["consecutive_negative_limit"]:
                triggers.append((EscalationTrigger.FRUSTRATION_PATTERN, 0.7))
            
        except Exception as e:
            logger.error(f"❌ Failed to detect escalation triggers: {e}")
        
        return triggers
    
    async def _calculate_sentiment_decline(self, context: Dict[str, Any]) -> float:
        """Calculate sentiment decline over recent messages"""
        try:
            recent_messages = context.get("recent_messages", [])
            
            if len(recent_messages) < 3:
                return 0.0
            
            # Get sentiment scores from recent messages
            sentiment_scores = []
            for message in recent_messages[:5]:  # Last 5 messages
                analysis = message.get("analysis", {})
                sentiment = analysis.get("sentiment", {})
                if sentiment.get("score") is not None:
                    sentiment_scores.append(sentiment["score"])
            
            if len(sentiment_scores) < 3:
                return 0.0
            
            # Calculate decline from first to last
            initial_sentiment = np.mean(sentiment_scores[-3:])  # First 3 messages
            recent_sentiment = np.mean(sentiment_scores[:3])    # Last 3 messages
            
            decline = initial_sentiment - recent_sentiment
            return max(0, decline)
            
        except Exception as e:
            logger.error(f"❌ Failed to calculate sentiment decline: {e}")
            return 0.0
    
    async def _detect_repeated_complaints(self, context: Dict[str, Any]) -> bool:
        """Detect if customer has repeated complaints"""
        try:
            escalation_history = context.get("escalation_history", [])
            
            # Check for escalations in the last 7 days
            week_ago = datetime.now() - timedelta(days=7)
            recent_escalations = [
                esc for esc in escalation_history 
                if esc.get("timestamp", datetime.min) > week_ago
            ]
            
            return len(recent_escalations) >= 2
            
        except Exception as e:
            logger.error(f"❌ Failed to detect repeated complaints: {e}")
            return False
    
    async def _count_consecutive_negative_messages(self, context: Dict[str, Any]) -> int:
        """Count consecutive negative messages"""
        try:
            recent_messages = context.get("recent_messages", [])
            consecutive_count = 0
            
            for message in recent_messages:
                analysis = message.get("analysis", {})
                sentiment = analysis.get("sentiment", {})
                
                if sentiment.get("label") in ["negative", "very_negative"]:
                    consecutive_count += 1
                else:
                    break
            
            return consecutive_count
            
        except Exception as e:
            logger.error(f"❌ Failed to count consecutive negative messages: {e}")
            return 0
    
    def _select_primary_trigger(self, triggers: List[Tuple[EscalationTrigger, float]]) -> EscalationTrigger:
        """Select the highest priority trigger"""
        if not triggers:
            return None
        
        # Sort by escalation level priority and confidence
        priority_order = {
            EscalationLevel.IMMEDIATE: 4,
            EscalationLevel.URGENT: 3,
            EscalationLevel.HIGH: 2,
            EscalationLevel.MEDIUM: 1,
            EscalationLevel.LOW: 0
        }
        
        def trigger_priority(trigger_conf):
            trigger, confidence = trigger_conf
            level = self.trigger_escalation_levels.get(trigger, EscalationLevel.LOW)
            return priority_order[level] * 10 + confidence
        
        sorted_triggers = sorted(triggers, key=trigger_priority, reverse=True)
        return sorted_triggers[0][0]
    
    async def _check_escalation_cooldown(self, conversation_id: str, trigger: EscalationTrigger) -> bool:
        """Check if escalation cooldown period has passed"""
        try:
            cooldown_key = f"escalation_cooldown:{conversation_id}:{trigger.value}"
            last_escalation = await self.cache_manager.get(cooldown_key)
            
            if last_escalation:
                last_time = datetime.fromisoformat(last_escalation)
                time_since = (datetime.now() - last_time).total_seconds()
                
                if time_since < self.escalation_config["escalation_cooldown"]:
                    return False
            
            # Set cooldown
            await self.cache_manager.set(
                cooldown_key, 
                datetime.now().isoformat(), 
                ttl=self.escalation_config["escalation_cooldown"]
            )
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to check escalation cooldown: {e}")
            return True  # Allow escalation if check fails
    
    async def _create_escalation_alert(
        self,
        trigger: EscalationTrigger,
        message_text: str,
        sentiment_analysis: Dict[str, Any],
        conversation_id: str,
        customer_id: str,
        platform: str,
        context: Dict[str, Any]
    ) -> EscalationAlert:
        """Create escalation alert"""
        try:
            customer = context.get("customer", {})
            conversation = context.get("conversation", {})
            
            level = self.trigger_escalation_levels.get(trigger, EscalationLevel.LOW)
            
            # Generate escalation reason
            escalation_reason = self._generate_escalation_reason(trigger, sentiment_analysis, context)
            
            # Generate recommended actions
            recommended_actions = self._generate_recommended_actions(trigger, level, context)
            
            # Generate supervisor notes
            supervisor_notes = self._generate_supervisor_notes(trigger, sentiment_analysis, context)
            
            escalation_alert = EscalationAlert(
                conversation_id=conversation_id,
                customer_id=customer_id,
                trigger=trigger,
                level=level,
                confidence=sentiment_analysis.get("confidence", 0.5),
                sentiment_score=sentiment_analysis.get("score", 0.5),
                message_text=message_text,
                platform=platform,
                customer_segment=customer.get("segment", "standard"),
                escalation_reason=escalation_reason,
                recommended_actions=recommended_actions,
                supervisor_notes=supervisor_notes,
                timestamp=datetime.now(),
                metadata={
                    "conversation_status": conversation.get("status"),
                    "assigned_agent": conversation.get("assignedAgent"),
                    "escalation_count": conversation.get("escalationCount", 0),
                    "customer_satisfaction": conversation.get("customerSatisfaction"),
                    "business_hours": context.get("business_hours")
                }
            )
            
            return escalation_alert
            
        except Exception as e:
            logger.error(f"❌ Failed to create escalation alert: {e}")
            raise
    
    def _generate_escalation_reason(
        self,
        trigger: EscalationTrigger,
        sentiment_analysis: Dict[str, Any],
        context: Dict[str, Any]
    ) -> str:
        """Generate human-readable escalation reason"""
        sentiment_score = sentiment_analysis.get("score", 0.5)
        sentiment_label = sentiment_analysis.get("label", "neutral")
        
        reason_templates = {
            EscalationTrigger.THREAT_DETECTION: "Customer mentioned legal action or threats",
            EscalationTrigger.ANGER_DETECTION: f"High anger detected in customer message (sentiment: {sentiment_label})",
            EscalationTrigger.FRUSTRATION_PATTERN: "Customer showing signs of frustration",
            EscalationTrigger.COMPETITOR_MENTION: "Customer mentioned competitor or switching services",
            EscalationTrigger.CANCELLATION_INTENT: "Customer expressed intent to cancel service",
            EscalationTrigger.LEGAL_MENTION: "Customer mentioned regulatory bodies or legal action",
            EscalationTrigger.VIP_CUSTOMER_ISSUE: "VIP customer experiencing negative sentiment",
            EscalationTrigger.SENTIMENT_DECLINE: f"Significant sentiment decline detected (score: {sentiment_score:.2f})",
            EscalationTrigger.NEGATIVE_THRESHOLD: f"Very negative sentiment detected (score: {sentiment_score:.2f})",
            EscalationTrigger.REPEATED_COMPLAINTS: "Customer has multiple recent escalations"
        }
        
        return reason_templates.get(trigger, f"Escalation triggered: {trigger.value}")
    
    def _generate_recommended_actions(
        self,
        trigger: EscalationTrigger,
        level: EscalationLevel,
        context: Dict[str, Any]
    ) -> List[str]:
        """Generate recommended actions for escalation"""
        actions = []
        
        # Level-based actions
        if level == EscalationLevel.IMMEDIATE:
            actions.extend([
                "Notify supervisor immediately",
                "Escalate to senior management",
                "Consider offering immediate compensation"
            ])
        elif level == EscalationLevel.URGENT:
            actions.extend([
                "Assign to senior agent",
                "Supervisor review within 5 minutes",
                "Prepare retention offer"
            ])
        elif level == EscalationLevel.HIGH:
            actions.extend([
                "Escalate to team lead",
                "Review customer history",
                "Consider service recovery"
            ])
        
        # Trigger-specific actions
        trigger_actions = {
            EscalationTrigger.THREAT_DETECTION: [
                "Document all communications",
                "Involve legal team if necessary",
                "De-escalation training required"
            ],
            EscalationTrigger.CANCELLATION_INTENT: [
                "Activate retention protocol",
                "Offer service alternatives",
                "Schedule callback with retention specialist"
            ],
            EscalationTrigger.COMPETITOR_MENTION: [
                "Competitive analysis review",
                "Highlight Free Mobile advantages",
                "Consider loyalty incentives"
            ],
            EscalationTrigger.VIP_CUSTOMER_ISSUE: [
                "VIP service protocol",
                "Personal account manager contact",
                "Priority resolution"
            ]
        }
        
        actions.extend(trigger_actions.get(trigger, []))
        
        return actions
    
    def _generate_supervisor_notes(
        self,
        trigger: EscalationTrigger,
        sentiment_analysis: Dict[str, Any],
        context: Dict[str, Any]
    ) -> str:
        """Generate notes for supervisor"""
        customer = context.get("customer", {})
        conversation = context.get("conversation", {})
        
        notes = f"Escalation Trigger: {trigger.value}\n"
        notes += f"Customer Segment: {customer.get('segment', 'standard')}\n"
        notes += f"Sentiment Score: {sentiment_analysis.get('score', 0.5):.2f}\n"
        notes += f"Platform: {conversation.get('platform', 'unknown')}\n"
        
        if customer.get("segment") == "vip":
            notes += "⚠️ VIP CUSTOMER - Priority handling required\n"
        
        escalation_history = context.get("escalation_history", [])
        if len(escalation_history) > 0:
            notes += f"Previous escalations: {len(escalation_history)} in recent history\n"
        
        return notes
    
    async def _process_escalation(self, escalation_alert: EscalationAlert):
        """Process escalation alert"""
        try:
            # Store escalation in database
            escalation_data = {
                "conversation_id": escalation_alert.conversation_id,
                "customer_id": escalation_alert.customer_id,
                "trigger": escalation_alert.trigger.value,
                "level": escalation_alert.level.value,
                "confidence": escalation_alert.confidence,
                "sentiment_score": escalation_alert.sentiment_score,
                "message_text": escalation_alert.message_text,
                "platform": escalation_alert.platform,
                "customer_segment": escalation_alert.customer_segment,
                "escalation_reason": escalation_alert.escalation_reason,
                "recommended_actions": escalation_alert.recommended_actions,
                "supervisor_notes": escalation_alert.supervisor_notes,
                "timestamp": escalation_alert.timestamp,
                "status": "pending",
                "metadata": escalation_alert.metadata
            }
            
            result = self.mongodb.chatbot.escalation_alerts.insert_one(escalation_data)
            escalation_id = result.inserted_id
            
            # Send notifications
            await self._send_escalation_notifications(escalation_alert, escalation_id)
            
            # Update conversation status
            await self._update_conversation_for_escalation(escalation_alert)
            
            logger.info(f"✅ Escalation processed: {escalation_id}")
            
        except Exception as e:
            logger.error(f"❌ Failed to process escalation: {e}")
    
    async def _send_escalation_notifications(self, escalation_alert: EscalationAlert, escalation_id: str):
        """Send notifications for escalation"""
        try:
            # Determine notification recipients based on escalation level
            recipients = await self._get_escalation_recipients(escalation_alert.level)
            
            notification_data = {
                "type": "escalation_alert",
                "title": f"🚨 Escalation Alert - {escalation_alert.level.value.upper()}",
                "message": escalation_alert.escalation_reason,
                "priority": escalation_alert.level.value,
                "data": {
                    "escalation_id": str(escalation_id),
                    "conversation_id": escalation_alert.conversation_id,
                    "customer_id": escalation_alert.customer_id,
                    "trigger": escalation_alert.trigger.value,
                    "platform": escalation_alert.platform,
                    "recommended_actions": escalation_alert.recommended_actions
                }
            }
            
            # Send notifications to recipients
            for recipient in recipients:
                await self.notification_service.send_notification(
                    recipient, notification_data
                )
            
        except Exception as e:
            logger.error(f"❌ Failed to send escalation notifications: {e}")
    
    async def _get_escalation_recipients(self, level: EscalationLevel) -> List[str]:
        """Get notification recipients based on escalation level"""
        try:
            recipients = []
            
            if level == EscalationLevel.IMMEDIATE:
                # Notify all supervisors and managers
                supervisors = self.mongodb.chatbot.agents.find({
                    "role": {"$in": ["supervisor", "manager", "admin"]},
                    "isActive": True
                })
                recipients.extend([str(agent["_id"]) for agent in supervisors])
                
            elif level == EscalationLevel.URGENT:
                # Notify available supervisors
                supervisors = self.mongodb.chatbot.agents.find({
                    "role": {"$in": ["supervisor", "manager"]},
                    "status": {"$in": ["available", "busy"]},
                    "isActive": True
                })
                recipients.extend([str(agent["_id"]) for agent in supervisors])
                
            elif level in [EscalationLevel.HIGH, EscalationLevel.MEDIUM]:
                # Notify team leads
                team_leads = self.mongodb.chatbot.agents.find({
                    "role": "team_lead",
                    "status": {"$in": ["available", "busy"]},
                    "isActive": True
                })
                recipients.extend([str(agent["_id"]) for agent in team_leads])
            
            return recipients
            
        except Exception as e:
            logger.error(f"❌ Failed to get escalation recipients: {e}")
            return []
    
    async def _update_conversation_for_escalation(self, escalation_alert: EscalationAlert):
        """Update conversation status for escalation"""
        try:
            update_data = {
                "$inc": {"escalationCount": 1},
                "$set": {
                    "urgency": "urgent" if escalation_alert.level in [EscalationLevel.IMMEDIATE, EscalationLevel.URGENT] else "high",
                    "lastEscalation": escalation_alert.timestamp,
                    "escalationReason": escalation_alert.escalation_reason
                }
            }
            
            self.mongodb.chatbot.conversations.update_one(
                {"_id": escalation_alert.conversation_id},
                update_data
            )
            
        except Exception as e:
            logger.error(f"❌ Failed to update conversation for escalation: {e}")
    
    async def _start_monitoring(self):
        """Start continuous sentiment monitoring"""
        try:
            logger.info("🚨 Starting sentiment escalation monitoring...")
            
            while True:
                try:
                    # Monitor active conversations
                    await self._monitor_active_conversations()
                    
                    # Sleep for monitoring interval
                    await asyncio.sleep(30)  # Check every 30 seconds
                    
                except Exception as e:
                    logger.error(f"❌ Error in monitoring loop: {e}")
                    await asyncio.sleep(60)  # Wait longer on error
                    
        except Exception as e:
            logger.error(f"❌ Failed to start monitoring: {e}")
    
    async def _monitor_active_conversations(self):
        """Monitor active conversations for sentiment changes"""
        try:
            # Get active conversations
            active_conversations = self.mongodb.chatbot.conversations.find({
                "status": {"$in": ["open", "assigned"]},
                "lastActivity": {"$gte": datetime.now() - timedelta(hours=1)}
            })
            
            for conversation in active_conversations:
                # Check for recent negative sentiment patterns
                await self._check_conversation_sentiment_pattern(conversation)
                
        except Exception as e:
            logger.error(f"❌ Failed to monitor active conversations: {e}")
    
    async def _check_conversation_sentiment_pattern(self, conversation: Dict[str, Any]):
        """Check conversation for concerning sentiment patterns"""
        try:
            conversation_id = str(conversation["_id"])
            
            # Get recent messages
            recent_messages = list(self.mongodb.chatbot.messages.find({
                "conversationId": conversation_id,
                "direction": "inbound",
                "timestamp": {"$gte": datetime.now() - timedelta(minutes=30)}
            }).sort("timestamp", -1).limit(5))
            
            if not recent_messages:
                return
            
            # Analyze sentiment trend
            sentiment_scores = []
            for message in recent_messages:
                analysis = message.get("analysis", {})
                sentiment = analysis.get("sentiment", {})
                if sentiment.get("score") is not None:
                    sentiment_scores.append(sentiment["score"])
            
            if len(sentiment_scores) >= 3:
                # Check for declining sentiment
                recent_avg = np.mean(sentiment_scores[:2])  # Last 2 messages
                earlier_avg = np.mean(sentiment_scores[-2:])  # Earlier messages
                
                decline = earlier_avg - recent_avg
                
                if decline >= 0.3:  # 30% decline
                    # Trigger escalation check
                    latest_message = recent_messages[0]
                    await self.analyze_message_for_escalation(
                        latest_message.get("text", ""),
                        conversation_id,
                        str(conversation.get("customerId")),
                        conversation.get("platform", "unknown")
                    )
                    
        except Exception as e:
            logger.error(f"❌ Failed to check conversation sentiment pattern: {e}")
    
    def _is_business_hours(self) -> bool:
        """Check if current time is within business hours"""
        now = datetime.now()
        return (
            now.weekday() < 6 and  # Monday to Saturday
            8 <= now.hour < 20     # 8 AM to 8 PM
        )
    
    async def get_escalation_analytics(self, days: int = 30) -> Dict[str, Any]:
        """Get escalation analytics"""
        try:
            start_date = datetime.now() - timedelta(days=days)
            
            # Get escalation alerts
            alerts = list(self.mongodb.chatbot.escalation_alerts.find({
                "timestamp": {"$gte": start_date}
            }))
            
            total_escalations = len(alerts)
            
            # Group by trigger
            by_trigger = {}
            for alert in alerts:
                trigger = alert["trigger"]
                by_trigger[trigger] = by_trigger.get(trigger, 0) + 1
            
            # Group by level
            by_level = {}
            for alert in alerts:
                level = alert["level"]
                by_level[level] = by_level.get(level, 0) + 1
            
            # Calculate resolution rate
            resolved_escalations = len([a for a in alerts if a.get("status") == "resolved"])
            resolution_rate = resolved_escalations / total_escalations if total_escalations > 0 else 0
            
            analytics = {
                "total_escalations": total_escalations,
                "escalations_by_trigger": by_trigger,
                "escalations_by_level": by_level,
                "resolution_rate": resolution_rate,
                "average_escalations_per_day": total_escalations / days,
                "most_common_trigger": max(by_trigger.items(), key=lambda x: x[1])[0] if by_trigger else None
            }
            
            return analytics
            
        except Exception as e:
            logger.error(f"❌ Failed to get escalation analytics: {e}")
            return {}
