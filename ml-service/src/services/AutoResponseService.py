"""
=============================================
🤖 AUTOMATED RESPONSE SERVICE
Intelligent automated responses for common inquiries
Cross-platform support with context awareness
=============================================
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
import json
import re
from dataclasses import dataclass
from enum import Enum

import numpy as np
from transformers import pipeline, AutoTokenizer, AutoModel
import torch
from pymongo import MongoClient
import redis

from .IntentDetectionService import IntentDetectionService
from .ConversationAnalysisService import ConversationAnalysisService
from .MessageSuggestionService import MessageSuggestionService
from ..utils.cache_manager import CacheManager
from ..utils.text_processor import TextProcessor

logger = logging.getLogger(__name__)

class ResponseConfidence(Enum):
    """Response confidence levels"""
    HIGH = "high"      # >0.8 - Auto-send
    MEDIUM = "medium"  # 0.5-0.8 - Suggest to agent
    LOW = "low"        # <0.5 - Don't use

@dataclass
class AutoResponse:
    """Automated response data structure"""
    text: str
    confidence: float
    confidence_level: ResponseConfidence
    intent: str
    category: str
    platform: str
    requires_approval: bool
    escalation_triggers: List[str]
    follow_up_actions: List[str]
    personalization_data: Dict[str, Any]
    metadata: Dict[str, Any]

class AutoResponseService:
    """Intelligent automated response service"""
    
    def __init__(self, mongodb_client: MongoClient, redis_client: redis.Redis):
        self.mongodb = mongodb_client
        self.redis = redis_client
        self.cache_manager = CacheManager(redis_client)
        self.text_processor = TextProcessor()
        
        # Initialize related services
        self.intent_detector = IntentDetectionService(mongodb_client, redis_client)
        self.conversation_analyzer = ConversationAnalysisService(mongodb_client, redis_client)
        self.message_suggester = MessageSuggestionService(mongodb_client, redis_client)
        
        # Auto-response configuration
        self.auto_response_config = {
            "enabled": True,
            "confidence_threshold": 0.8,
            "max_auto_responses_per_conversation": 3,
            "auto_response_cooldown": 300,  # 5 minutes
            "escalation_keywords": [
                "urgent", "emergency", "complaint", "angry", "frustrated",
                "supervisor", "manager", "cancel", "refund", "legal"
            ],
            "safe_intents": [
                "greeting", "hours_inquiry", "location_inquiry", "basic_info",
                "service_status", "account_balance", "simple_faq"
            ]
        }
        
        # Response templates by intent
        self.response_templates = {
            "greeting": {
                "responses": [
                    "Bonjour ! Je suis votre assistant virtuel Free Mobile. Comment puis-je vous aider aujourd'hui ?",
                    "Salut ! Ravi de vous accueillir. Que puis-je faire pour vous ?",
                    "Bonjour {customer_name} ! Comment allez-vous ? En quoi puis-je vous assister ?"
                ],
                "confidence": 0.9,
                "auto_send": True
            },
            "hours_inquiry": {
                "responses": [
                    "Nos services client sont disponibles du lundi au samedi de 8h à 20h. Comment puis-je vous aider ?",
                    "Nous sommes ouverts du lundi au samedi, de 8h00 à 20h00. Y a-t-il quelque chose que je puisse faire pour vous ?"
                ],
                "confidence": 0.95,
                "auto_send": True
            },
            "account_balance": {
                "responses": [
                    "Je vais vérifier votre solde immédiatement. Un instant s'il vous plaît...",
                    "Consultation de votre compte en cours... Je reviens vers vous dans quelques secondes."
                ],
                "confidence": 0.8,
                "auto_send": True,
                "follow_up_action": "check_balance"
            },
            "service_status": {
                "responses": [
                    "Je vérifie l'état de nos services dans votre région...",
                    "Contrôle du réseau en cours pour votre zone..."
                ],
                "confidence": 0.85,
                "auto_send": True,
                "follow_up_action": "check_service_status"
            },
            "basic_info": {
                "responses": [
                    "Je serais ravi de vous renseigner. Quelle information recherchez-vous exactement ?",
                    "Bien sûr ! Pouvez-vous préciser votre question pour que je puisse mieux vous aider ?"
                ],
                "confidence": 0.7,
                "auto_send": False
            },
            "technical_issue": {
                "responses": [
                    "Je comprends que vous rencontrez un problème technique. Laissez-moi vous aider à le résoudre.",
                    "Problème technique noté. Je vais vous guider étape par étape pour le résoudre."
                ],
                "confidence": 0.6,
                "auto_send": False,
                "escalation_trigger": "complex_technical"
            },
            "billing_question": {
                "responses": [
                    "Concernant votre facturation, je vais examiner votre compte. Un moment s'il vous plaît.",
                    "Question de facturation reçue. Je consulte vos informations..."
                ],
                "confidence": 0.7,
                "auto_send": False,
                "follow_up_action": "check_billing"
            },
            "goodbye": {
                "responses": [
                    "Merci d'avoir contacté Free Mobile ! Passez une excellente journée ! 😊",
                    "Au revoir ! N'hésitez pas à nous recontacter si besoin. Bonne journée ! 👋"
                ],
                "confidence": 0.9,
                "auto_send": True
            }
        }
        
        # Platform-specific adaptations
        self.platform_adaptations = {
            "whatsapp": {
                "supports_emojis": True,
                "supports_formatting": True,
                "casual_tone": True,
                "max_length": 4096
            },
            "facebook": {
                "supports_emojis": True,
                "supports_quick_replies": True,
                "formal_tone": True,
                "max_length": 2000
            },
            "instagram": {
                "supports_emojis": True,
                "casual_tone": True,
                "visual_focus": True,
                "max_length": 1000
            },
            "twitter": {
                "supports_hashtags": True,
                "concise_required": True,
                "public_visibility": True,
                "max_length": 280
            }
        }
        
        # Initialize models
        asyncio.create_task(self._initialize_models())
    
    async def _initialize_models(self):
        """Initialize AI models for auto-responses"""
        try:
            logger.info("Initializing auto-response models...")
            
            # Load response generation model
            self.response_generator = pipeline(
                'text-generation',
                model='microsoft/DialoGPT-medium',
                device=0 if torch.cuda.is_available() else -1
            )
            
            logger.info("✅ Auto-response models initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize auto-response models: {e}")
            raise
    
    async def generate_auto_response(
        self,
        customer_message: str,
        conversation_id: str,
        platform: str,
        customer_id: str
    ) -> Optional[AutoResponse]:
        """
        Generate automated response for customer message
        
        Args:
            customer_message: Customer's message text
            conversation_id: ID of the conversation
            platform: Platform (whatsapp, facebook, instagram, twitter)
            customer_id: ID of the customer
            
        Returns:
            AutoResponse object or None if no suitable response
        """
        try:
            logger.info(f"🤖 Generating auto-response for conversation {conversation_id}")
            
            # Check if auto-responses are enabled
            if not self.auto_response_config["enabled"]:
                return None
            
            # Check conversation limits
            if not await self._check_auto_response_limits(conversation_id):
                logger.info("Auto-response limits exceeded for conversation")
                return None
            
            # Get conversation context
            context = await self._get_conversation_context(conversation_id, customer_id)
            
            # Detect intent
            intent_result = await self.intent_detector.detect_intent(
                customer_message, context
            )
            
            # Check for escalation triggers
            if await self._should_escalate(customer_message, intent_result, context):
                logger.info("Escalation triggered, skipping auto-response")
                return None
            
            # Generate response based on intent
            auto_response = await self._generate_response_for_intent(
                intent_result, customer_message, platform, context
            )
            
            if auto_response:
                # Validate response quality
                if await self._validate_response_quality(auto_response, context):
                    # Log auto-response generation
                    await self._log_auto_response(auto_response, conversation_id)
                    
                    logger.info(f"✅ Generated auto-response with confidence {auto_response.confidence}")
                    return auto_response
                else:
                    logger.info("Auto-response failed quality validation")
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Failed to generate auto-response: {e}")
            return None
    
    async def _check_auto_response_limits(self, conversation_id: str) -> bool:
        """Check if auto-response limits are exceeded"""
        try:
            # Check conversation auto-response count
            auto_response_count = self.mongodb.chatbot.messages.count_documents({
                "conversationId": conversation_id,
                "direction": "outbound",
                "metadata.auto_generated": True
            })
            
            if auto_response_count >= self.auto_response_config["max_auto_responses_per_conversation"]:
                return False
            
            # Check cooldown period
            last_auto_response = self.mongodb.chatbot.messages.find_one(
                {
                    "conversationId": conversation_id,
                    "direction": "outbound",
                    "metadata.auto_generated": True
                },
                sort=[("timestamp", -1)]
            )
            
            if last_auto_response:
                last_response_time = last_auto_response.get("timestamp")
                if last_response_time:
                    time_since_last = (datetime.now() - last_response_time).total_seconds()
                    if time_since_last < self.auto_response_config["auto_response_cooldown"]:
                        return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to check auto-response limits: {e}")
            return False
    
    async def _get_conversation_context(self, conversation_id: str, customer_id: str) -> Dict[str, Any]:
        """Get conversation context for auto-response generation"""
        try:
            # Get conversation
            conversation = self.mongodb.chatbot.conversations.find_one(
                {"_id": conversation_id}
            )
            
            # Get customer
            customer = self.mongodb.chatbot.customers.find_one(
                {"_id": customer_id}
            )
            
            # Get recent messages
            recent_messages = list(self.mongodb.chatbot.messages.find(
                {"conversationId": conversation_id}
            ).sort("timestamp", -1).limit(10))
            
            context = {
                "conversation": conversation or {},
                "customer": customer or {},
                "recent_messages": recent_messages,
                "message_count": len(recent_messages),
                "auto_response_count": len([
                    msg for msg in recent_messages 
                    if msg.get("metadata", {}).get("auto_generated")
                ]),
                "current_time": datetime.now(),
                "business_hours": self._is_business_hours()
            }
            
            return context
            
        except Exception as e:
            logger.error(f"❌ Failed to get conversation context: {e}")
            return {}
    
    async def _should_escalate(
        self,
        customer_message: str,
        intent_result: Dict[str, Any],
        context: Dict[str, Any]
    ) -> bool:
        """Determine if message should be escalated to human agent"""
        try:
            # Check for escalation keywords
            message_lower = customer_message.lower()
            for keyword in self.auto_response_config["escalation_keywords"]:
                if keyword in message_lower:
                    return True
            
            # Check intent confidence
            if intent_result.get("confidence", 0) < 0.5:
                return True
            
            # Check if intent is not in safe list
            primary_intent = intent_result.get("primary", "")
            if primary_intent not in self.auto_response_config["safe_intents"]:
                return True
            
            # Check conversation complexity
            if context.get("message_count", 0) > 10:
                return True
            
            # Check customer sentiment
            conversation = context.get("conversation", {})
            if conversation.get("sentiment") in ["negative", "very_negative"]:
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ Failed to check escalation criteria: {e}")
            return True  # Err on the side of caution
    
    async def _generate_response_for_intent(
        self,
        intent_result: Dict[str, Any],
        customer_message: str,
        platform: str,
        context: Dict[str, Any]
    ) -> Optional[AutoResponse]:
        """Generate response based on detected intent"""
        try:
            primary_intent = intent_result.get("primary", "")
            confidence = intent_result.get("confidence", 0)
            
            # Get template for intent
            template_config = self.response_templates.get(primary_intent)
            if not template_config:
                return None
            
            # Check confidence threshold
            template_confidence = template_config.get("confidence", 0.5)
            if confidence < template_confidence:
                return None
            
            # Select response template
            responses = template_config.get("responses", [])
            if not responses:
                return None
            
            # Choose best response (for now, just pick first one)
            selected_response = responses[0]
            
            # Personalize response
            personalized_response = await self._personalize_response(
                selected_response, context
            )
            
            # Adapt for platform
            adapted_response = await self._adapt_response_for_platform(
                personalized_response, platform
            )
            
            # Determine confidence level
            final_confidence = min(confidence * template_confidence, 1.0)
            confidence_level = self._get_confidence_level(final_confidence)
            
            # Create auto-response
            auto_response = AutoResponse(
                text=adapted_response,
                confidence=final_confidence,
                confidence_level=confidence_level,
                intent=primary_intent,
                category=template_config.get("category", "general"),
                platform=platform,
                requires_approval=not template_config.get("auto_send", False),
                escalation_triggers=template_config.get("escalation_triggers", []),
                follow_up_actions=template_config.get("follow_up_actions", []),
                personalization_data=self._extract_personalization_data(context),
                metadata={
                    "template_used": selected_response,
                    "intent_confidence": confidence,
                    "template_confidence": template_confidence,
                    "generation_timestamp": datetime.now().isoformat(),
                    "platform_adapted": True
                }
            )
            
            return auto_response
            
        except Exception as e:
            logger.error(f"❌ Failed to generate response for intent: {e}")
            return None
    
    async def _personalize_response(
        self,
        response_template: str,
        context: Dict[str, Any]
    ) -> str:
        """Personalize response template with customer data"""
        try:
            customer = context.get("customer", {})
            conversation = context.get("conversation", {})
            
            personalized = response_template
            
            # Replace customer name
            customer_name = customer.get("name", "")
            if customer_name:
                personalized = personalized.replace("{customer_name}", customer_name)
            else:
                personalized = personalized.replace("{customer_name}", "")
                personalized = personalized.replace("  ", " ")  # Clean up extra spaces
            
            # Add time-based greetings
            current_hour = datetime.now().hour
            if "{greeting}" in personalized:
                if current_hour < 12:
                    greeting = "Bonjour"
                elif current_hour < 18:
                    greeting = "Bon après-midi"
                else:
                    greeting = "Bonsoir"
                personalized = personalized.replace("{greeting}", greeting)
            
            # Add business hours context
            if not context.get("business_hours"):
                if "Comment puis-je vous aider" in personalized:
                    personalized += " Bien que nous soyons en dehors des heures d'ouverture, je ferai de mon mieux pour vous assister."
            
            return personalized.strip()
            
        except Exception as e:
            logger.error(f"❌ Failed to personalize response: {e}")
            return response_template
    
    async def _adapt_response_for_platform(
        self,
        response: str,
        platform: str
    ) -> str:
        """Adapt response for specific platform requirements"""
        try:
            platform_config = self.platform_adaptations.get(platform, {})
            adapted_response = response
            
            # Length adaptation
            max_length = platform_config.get("max_length", 1000)
            if len(adapted_response) > max_length:
                adapted_response = adapted_response[:max_length-3] + "..."
            
            # Tone adaptation
            if platform_config.get("casual_tone"):
                adapted_response = adapted_response.replace("Monsieur/Madame", "")
                adapted_response = adapted_response.replace("s'il vous plaît", "svp")
            
            if platform_config.get("concise_required"):
                # Make more concise for Twitter
                adapted_response = re.sub(r'\s+', ' ', adapted_response)
                adapted_response = adapted_response.replace("Comment puis-je vous aider aujourd'hui ?", "Comment puis-je aider ?")
            
            # Add emojis if supported
            if platform_config.get("supports_emojis") and not any(emoji in adapted_response for emoji in ["😊", "👋", "🤖"]):
                if "Bonjour" in adapted_response or "Salut" in adapted_response:
                    adapted_response += " 😊"
                elif "Au revoir" in adapted_response:
                    adapted_response += " 👋"
            
            return adapted_response
            
        except Exception as e:
            logger.error(f"❌ Failed to adapt response for platform: {e}")
            return response
    
    def _get_confidence_level(self, confidence: float) -> ResponseConfidence:
        """Determine confidence level from numeric confidence"""
        if confidence >= 0.8:
            return ResponseConfidence.HIGH
        elif confidence >= 0.5:
            return ResponseConfidence.MEDIUM
        else:
            return ResponseConfidence.LOW
    
    def _extract_personalization_data(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Extract personalization data from context"""
        customer = context.get("customer", {})
        conversation = context.get("conversation", {})
        
        return {
            "customer_name": customer.get("name"),
            "customer_segment": customer.get("segment"),
            "conversation_platform": conversation.get("platform"),
            "conversation_urgency": conversation.get("urgency"),
            "business_hours": context.get("business_hours"),
            "message_count": context.get("message_count", 0)
        }
    
    async def _validate_response_quality(
        self,
        auto_response: AutoResponse,
        context: Dict[str, Any]
    ) -> bool:
        """Validate the quality of generated auto-response"""
        try:
            # Check minimum length
            if len(auto_response.text.strip()) < 10:
                return False
            
            # Check for inappropriate content (basic check)
            inappropriate_words = ["stupid", "idiot", "hate", "terrible"]
            text_lower = auto_response.text.lower()
            if any(word in text_lower for word in inappropriate_words):
                return False
            
            # Check confidence threshold
            if auto_response.confidence < self.auto_response_config["confidence_threshold"]:
                return False
            
            # Check platform-specific requirements
            platform_config = self.platform_adaptations.get(auto_response.platform, {})
            max_length = platform_config.get("max_length", 1000)
            if len(auto_response.text) > max_length:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to validate response quality: {e}")
            return False
    
    async def _log_auto_response(self, auto_response: AutoResponse, conversation_id: str):
        """Log auto-response generation for analytics"""
        try:
            log_data = {
                "conversation_id": conversation_id,
                "response_text": auto_response.text,
                "confidence": auto_response.confidence,
                "confidence_level": auto_response.confidence_level.value,
                "intent": auto_response.intent,
                "platform": auto_response.platform,
                "requires_approval": auto_response.requires_approval,
                "timestamp": datetime.now(),
                "metadata": auto_response.metadata
            }
            
            self.mongodb.chatbot.auto_response_logs.insert_one(log_data)
            
        except Exception as e:
            logger.error(f"❌ Failed to log auto-response: {e}")
    
    def _is_business_hours(self) -> bool:
        """Check if current time is within business hours"""
        now = datetime.now()
        # Business hours: Monday to Saturday, 8 AM to 8 PM
        return (
            now.weekday() < 6 and  # Monday to Saturday
            8 <= now.hour < 20     # 8 AM to 8 PM
        )
    
    async def should_auto_send(self, auto_response: AutoResponse) -> bool:
        """Determine if auto-response should be sent automatically"""
        return (
            auto_response.confidence_level == ResponseConfidence.HIGH and
            not auto_response.requires_approval and
            auto_response.confidence >= self.auto_response_config["confidence_threshold"]
        )
    
    async def get_auto_response_analytics(self, days: int = 30) -> Dict[str, Any]:
        """Get analytics on auto-response performance"""
        try:
            start_date = datetime.now() - timedelta(days=days)
            
            # Get auto-response logs
            logs = list(self.mongodb.chatbot.auto_response_logs.find({
                "timestamp": {"$gte": start_date}
            }))
            
            total_responses = len(logs)
            high_confidence = len([log for log in logs if log["confidence_level"] == "high"])
            auto_sent = len([log for log in logs if not log["requires_approval"]])
            
            # Calculate analytics
            analytics = {
                "total_auto_responses": total_responses,
                "high_confidence_responses": high_confidence,
                "auto_sent_responses": auto_sent,
                "average_confidence": np.mean([log["confidence"] for log in logs]) if logs else 0,
                "response_by_intent": {},
                "response_by_platform": {},
                "success_rate": high_confidence / total_responses if total_responses > 0 else 0
            }
            
            # Group by intent
            for log in logs:
                intent = log["intent"]
                if intent not in analytics["response_by_intent"]:
                    analytics["response_by_intent"][intent] = 0
                analytics["response_by_intent"][intent] += 1
            
            # Group by platform
            for log in logs:
                platform = log["platform"]
                if platform not in analytics["response_by_platform"]:
                    analytics["response_by_platform"][platform] = 0
                analytics["response_by_platform"][platform] += 1
            
            return analytics
            
        except Exception as e:
            logger.error(f"❌ Failed to get auto-response analytics: {e}")
            return {}
    
    async def update_response_templates(self, new_templates: Dict[str, Any]):
        """Update response templates (for admin use)"""
        try:
            # Validate new templates
            for intent, config in new_templates.items():
                if "responses" not in config or not config["responses"]:
                    raise ValueError(f"Invalid template config for intent: {intent}")
            
            # Update templates
            self.response_templates.update(new_templates)
            
            # Store in database for persistence
            self.mongodb.chatbot.auto_response_templates.replace_one(
                {"_id": "current"},
                {"_id": "current", "templates": self.response_templates, "updated_at": datetime.now()},
                upsert=True
            )
            
            logger.info("✅ Auto-response templates updated successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to update response templates: {e}")
            raise
