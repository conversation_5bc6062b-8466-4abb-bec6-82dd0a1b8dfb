"""
API FastAPI principale pour le service ML
Free Mobile Chatbot Dashboard - Classification intelligente
Performance: < 200ms par classification, 1000+ req/sec
"""

import asyncio
import logging
import time
from contextlib import asynccontextmanager
from typing import Dict, Any, List

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from prometheus_client import Counter, Histogram, generate_latest, CONTENT_TYPE_LATEST
import structlog

from .config import settings, LOGGING_CONFIG
from .models.types import (
    ClassificationRequest, ClassificationResponse, ConversationClassification,
    ModelPerformanceMetrics, HealthCheckResponse
)
from .services.classification_service import MLClassificationService
from .services.cache_service import CacheService
from .middleware.auth import verify_api_key
from .middleware.rate_limiting import RateLimitMiddleware
from .middleware.monitoring import PrometheusMiddleware


# Configuration du logging structuré
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# Métriques Prometheus
CLASSIFICATION_REQUESTS = Counter(
    'ml_classification_requests_total',
    'Total number of classification requests',
    ['status', 'category']
)

CLASSIFICATION_DURATION = Histogram(
    'ml_classification_duration_seconds',
    'Time spent on classification requests',
    buckets=[0.1, 0.2, 0.5, 1.0, 2.0, 5.0]
)

MODEL_INFERENCE_DURATION = Histogram(
    'ml_model_inference_duration_seconds',
    'Time spent on model inference',
    ['model_name']
)

# Services globaux
ml_service: MLClassificationService = None
cache_service: CacheService = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Gestionnaire de cycle de vie de l'application"""
    global ml_service, cache_service
    
    logger.info("Starting ML Classification Service", version=settings.VERSION)
    
    try:
        # Initialisation des services
        cache_service = CacheService()
        await cache_service.initialize()
        
        ml_service = MLClassificationService()
        await ml_service.initialize()
        
        logger.info("ML Classification Service started successfully")
        
        yield
        
    except Exception as e:
        logger.error("Failed to start ML Classification Service", error=str(e))
        raise
    finally:
        # Nettoyage
        if cache_service:
            await cache_service.close()
        logger.info("ML Classification Service stopped")


# Application FastAPI
app = FastAPI(
    title="Free Mobile ML Classification Service",
    description="Service de classification intelligente des conversations pour Free Mobile Chatbot Dashboard",
    version=settings.VERSION,
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan
)

# Middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

app.add_middleware(GZipMiddleware, minimum_size=1000)
app.add_middleware(PrometheusMiddleware)
app.add_middleware(RateLimitMiddleware, max_requests=1000, window_seconds=60)


@app.get("/health", response_model=HealthCheckResponse)
async def health_check():
    """Health check endpoint"""
    try:
        # Vérification des services
        models_status = {}
        if ml_service:
            models_status = {
                "intent_classifier": "intent" in ml_service.models,
                "sentiment_analyzer": "sentiment" in ml_service.models,
                "churn_predictor": "churn" in ml_service.models,
                "opportunity_scorer": "opportunity" in ml_service.models
            }
        
        redis_connected = cache_service and cache_service.redis is not None
        
        # Métriques système (simplifiées)
        import psutil
        memory_usage = psutil.virtual_memory().used / (1024 * 1024)  # MB
        cpu_usage = psutil.cpu_percent()
        
        health_status = HealthCheckResponse(
            status="healthy" if all(models_status.values()) and redis_connected else "degraded",
            timestamp=time.time(),
            models_loaded=models_status,
            redis_connected=redis_connected,
            database_connected=True,  # Simplifié pour le moment
            memory_usage_mb=memory_usage,
            cpu_usage_percent=cpu_usage
        )
        
        # Mise à jour du cache de santé
        if cache_service:
            await cache_service.update_health_status(health_status.dict())
        
        return health_status
        
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        raise HTTPException(status_code=503, detail="Service unhealthy")


@app.post("/api/v1/classify", response_model=ClassificationResponse)
async def classify_conversation(
    request: ClassificationRequest,
    background_tasks: BackgroundTasks,
    api_key: str = Depends(verify_api_key)
):
    """
    Classification d'une conversation
    Performance target: < 200ms
    """
    start_time = time.time()
    
    try:
        logger.info(
            "Classification request received",
            conversation_id=request.conversation_id,
            messages_count=len(request.messages),
            customer_id=request.customer_data.customer_id
        )
        
        # Validation des données
        if not request.messages:
            raise HTTPException(status_code=400, detail="No messages provided")
        
        if not request.customer_data:
            raise HTTPException(status_code=400, detail="Customer data required")
        
        # Classification ML
        classification = await ml_service.classify_conversation(request)
        
        # Métriques
        duration = time.time() - start_time
        CLASSIFICATION_DURATION.observe(duration)
        CLASSIFICATION_REQUESTS.labels(
            status="success",
            category=classification.category.value
        ).inc()
        
        # Tâches en arrière-plan
        background_tasks.add_task(
            update_priority_queue,
            classification.conversation_id,
            classification.priority_score,
            {
                "category": classification.category.value,
                "customer_id": request.customer_data.customer_id,
                "business_impact": classification.business_impact.dict()
            }
        )
        
        logger.info(
            "Classification completed",
            conversation_id=request.conversation_id,
            category=classification.category.value,
            priority_score=classification.priority_score,
            duration_ms=duration * 1000
        )
        
        return ClassificationResponse(
            success=True,
            classification=classification,
            cached=False
        )
        
    except Exception as e:
        duration = time.time() - start_time
        CLASSIFICATION_REQUESTS.labels(status="error", category="unknown").inc()
        
        logger.error(
            "Classification failed",
            conversation_id=request.conversation_id,
            error=str(e),
            duration_ms=duration * 1000
        )
        
        raise HTTPException(
            status_code=500,
            detail=f"Classification failed: {str(e)}"
        )


@app.get("/api/v1/queue/priority")
async def get_priority_queue(
    limit: int = 50,
    min_priority: int = 0,
    category: str = None,
    api_key: str = Depends(verify_api_key)
):
    """Récupération de la queue de priorité"""
    try:
        if not cache_service:
            raise HTTPException(status_code=503, detail="Cache service unavailable")
        
        queue_items = await cache_service.get_priority_queue(limit)
        
        # Filtrage
        if min_priority > 0:
            queue_items = [item for item in queue_items if item.get("priority_score", 0) >= min_priority]
        
        if category:
            queue_items = [item for item in queue_items if item.get("category") == category]
        
        return {
            "success": True,
            "queue": queue_items,
            "total_count": len(queue_items)
        }
        
    except Exception as e:
        logger.error("Priority queue retrieval failed", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/v1/metrics/performance")
async def get_performance_metrics(api_key: str = Depends(verify_api_key)):
    """Métriques de performance des modèles"""
    try:
        if not ml_service:
            raise HTTPException(status_code=503, detail="ML service unavailable")
        
        metrics = ml_service.get_performance_metrics()
        
        # Ajout des métriques de cache
        if cache_service:
            cache_stats = cache_service.get_cache_stats()
            metrics.update(cache_stats)
        
        return {
            "success": True,
            "metrics": metrics,
            "timestamp": time.time()
        }
        
    except Exception as e:
        logger.error("Performance metrics retrieval failed", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/v1/models/retrain")
async def trigger_model_retrain(
    model_name: str,
    background_tasks: BackgroundTasks,
    api_key: str = Depends(verify_api_key)
):
    """Déclenchement du réentraînement d'un modèle"""
    try:
        # Validation du nom de modèle
        valid_models = ["intent_classifier", "sentiment_analyzer", "churn_predictor", "opportunity_scorer"]
        if model_name not in valid_models:
            raise HTTPException(status_code=400, detail=f"Invalid model name. Valid options: {valid_models}")
        
        # Tâche en arrière-plan pour le réentraînement
        background_tasks.add_task(retrain_model, model_name)
        
        logger.info("Model retrain triggered", model_name=model_name)
        
        return {
            "success": True,
            "message": f"Retrain triggered for model: {model_name}",
            "model_name": model_name
        }
        
    except Exception as e:
        logger.error("Model retrain failed", model_name=model_name, error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/v1/cache/stats")
async def get_cache_stats(api_key: str = Depends(verify_api_key)):
    """Statistiques du cache Redis"""
    try:
        if not cache_service:
            raise HTTPException(status_code=503, detail="Cache service unavailable")
        
        stats = cache_service.get_cache_stats()
        
        return {
            "success": True,
            "cache_stats": stats
        }
        
    except Exception as e:
        logger.error("Cache stats retrieval failed", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/api/v1/cache/classification/{conversation_id}")
async def invalidate_classification_cache(
    conversation_id: str,
    api_key: str = Depends(verify_api_key)
):
    """Invalidation du cache pour une conversation"""
    try:
        if not cache_service:
            raise HTTPException(status_code=503, detail="Cache service unavailable")
        
        success = await cache_service.invalidate_classification(conversation_id)
        
        return {
            "success": success,
            "conversation_id": conversation_id,
            "message": "Cache invalidated" if success else "No cache entry found"
        }
        
    except Exception as e:
        logger.error("Cache invalidation failed", conversation_id=conversation_id, error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/metrics")
async def prometheus_metrics():
    """Endpoint pour les métriques Prometheus"""
    return Response(generate_latest(), media_type=CONTENT_TYPE_LATEST)


# Tâches en arrière-plan
async def update_priority_queue(
    conversation_id: str,
    priority_score: int,
    metadata: Dict[str, Any]
):
    """Mise à jour de la queue de priorité"""
    try:
        if cache_service:
            await cache_service.add_to_priority_queue(
                conversation_id, priority_score, metadata
            )
    except Exception as e:
        logger.error("Priority queue update failed", error=str(e))


async def retrain_model(model_name: str):
    """Réentraînement d'un modèle (placeholder)"""
    try:
        logger.info("Starting model retrain", model_name=model_name)
        
        # Simulation du réentraînement
        await asyncio.sleep(5)
        
        logger.info("Model retrain completed", model_name=model_name)
        
    except Exception as e:
        logger.error("Model retrain failed", model_name=model_name, error=str(e))


# Gestionnaire d'erreurs global
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Gestionnaire d'erreurs global"""
    logger.error(
        "Unhandled exception",
        path=request.url.path,
        method=request.method,
        error=str(exc)
    )
    
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "error": "Internal server error",
            "detail": str(exc) if settings.DEBUG else "An error occurred"
        }
    )


if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        workers=settings.WORKERS,
        log_config=LOGGING_CONFIG,
        access_log=True,
        reload=settings.DEBUG
    )
