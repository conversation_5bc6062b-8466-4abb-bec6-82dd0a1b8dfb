"""
Middleware de monitoring Prometheus pour le service ML
Free Mobile Chatbot Dashboard - Métriques et observabilité
"""

import time
import psutil
from typing import Dict, Any

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from prometheus_client import Counter, Histogram, Gauge, Info


# Métriques HTTP
HTTP_REQUESTS_TOTAL = Counter(
    'http_requests_total',
    'Total HTTP requests',
    ['method', 'endpoint', 'status_code']
)

HTTP_REQUEST_DURATION = Histogram(
    'http_request_duration_seconds',
    'HTTP request duration',
    ['method', 'endpoint'],
    buckets=[0.01, 0.05, 0.1, 0.2, 0.5, 1.0, 2.0, 5.0, 10.0]
)

HTTP_REQUEST_SIZE = Histogram(
    'http_request_size_bytes',
    'HTTP request size in bytes',
    ['method', 'endpoint']
)

HTTP_RESPONSE_SIZE = Histogram(
    'http_response_size_bytes',
    'HTTP response size in bytes',
    ['method', 'endpoint']
)

# Métriques ML spécifiques
ML_CLASSIFICATIONS_TOTAL = Counter(
    'ml_classifications_total',
    'Total ML classifications performed',
    ['category', 'confidence_level']
)

ML_CLASSIFICATION_DURATION = Histogram(
    'ml_classification_duration_seconds',
    'ML classification processing time',
    ['model_type'],
    buckets=[0.05, 0.1, 0.2, 0.5, 1.0, 2.0, 5.0]
)

ML_MODEL_ACCURACY = Gauge(
    'ml_model_accuracy',
    'Current model accuracy',
    ['model_name', 'version']
)

ML_CACHE_HITS = Counter(
    'ml_cache_hits_total',
    'Total cache hits',
    ['cache_type']
)

ML_CACHE_MISSES = Counter(
    'ml_cache_misses_total',
    'Total cache misses',
    ['cache_type']
)

# Métriques système
SYSTEM_CPU_USAGE = Gauge('system_cpu_usage_percent', 'System CPU usage percentage')
SYSTEM_MEMORY_USAGE = Gauge('system_memory_usage_bytes', 'System memory usage in bytes')
SYSTEM_MEMORY_AVAILABLE = Gauge('system_memory_available_bytes', 'System available memory in bytes')

# Métriques business
BUSINESS_REVENUE_IMPACT = Counter(
    'business_revenue_impact_euros',
    'Business revenue impact from ML predictions',
    ['impact_type']  # 'opportunity', 'churn_prevention', 'cost_saving'
)

BUSINESS_PRIORITY_QUEUE_SIZE = Gauge(
    'business_priority_queue_size',
    'Current size of priority conversation queue'
)

BUSINESS_CHURN_PREDICTIONS = Counter(
    'business_churn_predictions_total',
    'Total churn predictions made',
    ['risk_level']  # 'low', 'medium', 'high'
)

# Info sur le service
SERVICE_INFO = Info('ml_service_info', 'ML Service information')


class PrometheusMiddleware(BaseHTTPMiddleware):
    """Middleware pour collecter les métriques Prometheus"""
    
    def __init__(self, app):
        super().__init__(app)
        self._setup_service_info()
        self._start_system_metrics_collection()
    
    def _setup_service_info(self):
        """Configuration des informations du service"""
        SERVICE_INFO.info({
            'version': '1.0.0',
            'service': 'free-mobile-ml-service',
            'environment': 'development'  # À adapter selon l'environnement
        })
    
    def _start_system_metrics_collection(self):
        """Démarrage de la collecte des métriques système"""
        # Les métriques système seront mises à jour périodiquement
        # via un background task ou un scheduler
        pass
    
    async def dispatch(self, request: Request, call_next):
        """Traitement des requêtes avec collecte de métriques"""
        
        start_time = time.time()
        
        # Informations sur la requête
        method = request.method
        path = request.url.path
        endpoint = self._normalize_endpoint(path)
        
        # Taille de la requête
        request_size = int(request.headers.get('content-length', 0))
        if request_size > 0:
            HTTP_REQUEST_SIZE.labels(method=method, endpoint=endpoint).observe(request_size)
        
        # Traitement de la requête
        response = await call_next(request)
        
        # Calcul de la durée
        duration = time.time() - start_time
        
        # Collecte des métriques
        status_code = str(response.status_code)
        
        HTTP_REQUESTS_TOTAL.labels(
            method=method,
            endpoint=endpoint,
            status_code=status_code
        ).inc()
        
        HTTP_REQUEST_DURATION.labels(
            method=method,
            endpoint=endpoint
        ).observe(duration)
        
        # Taille de la réponse
        response_size = len(response.body) if hasattr(response, 'body') else 0
        if response_size > 0:
            HTTP_RESPONSE_SIZE.labels(
                method=method,
                endpoint=endpoint
            ).observe(response_size)
        
        # Mise à jour des métriques système
        self._update_system_metrics()
        
        return response
    
    def _normalize_endpoint(self, path: str) -> str:
        """Normalisation des endpoints pour éviter la cardinalité élevée"""
        
        # Remplacer les IDs par des placeholders
        import re
        
        # UUIDs
        path = re.sub(r'/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', '/{uuid}', path)
        
        # IDs numériques
        path = re.sub(r'/\d+', '/{id}', path)
        
        # Conversation IDs (format spécifique)
        path = re.sub(r'/conv_[a-zA-Z0-9]+', '/conv_{id}', path)
        
        return path
    
    def _update_system_metrics(self):
        """Mise à jour des métriques système"""
        try:
            # CPU
            cpu_percent = psutil.cpu_percent(interval=None)
            SYSTEM_CPU_USAGE.set(cpu_percent)
            
            # Mémoire
            memory = psutil.virtual_memory()
            SYSTEM_MEMORY_USAGE.set(memory.used)
            SYSTEM_MEMORY_AVAILABLE.set(memory.available)
            
        except Exception:
            # Ignorer les erreurs de collecte système
            pass


class MLMetricsCollector:
    """Collecteur de métriques ML spécialisé"""
    
    @staticmethod
    def record_classification(
        category: str,
        confidence: float,
        processing_time: float,
        model_type: str = "ensemble"
    ):
        """Enregistrement d'une classification ML"""
        
        # Niveau de confiance
        confidence_level = "high" if confidence > 0.8 else "medium" if confidence > 0.6 else "low"
        
        ML_CLASSIFICATIONS_TOTAL.labels(
            category=category,
            confidence_level=confidence_level
        ).inc()
        
        ML_CLASSIFICATION_DURATION.labels(
            model_type=model_type
        ).observe(processing_time)
    
    @staticmethod
    def record_cache_hit(cache_type: str = "classification"):
        """Enregistrement d'un cache hit"""
        ML_CACHE_HITS.labels(cache_type=cache_type).inc()
    
    @staticmethod
    def record_cache_miss(cache_type: str = "classification"):
        """Enregistrement d'un cache miss"""
        ML_CACHE_MISSES.labels(cache_type=cache_type).inc()
    
    @staticmethod
    def update_model_accuracy(model_name: str, version: str, accuracy: float):
        """Mise à jour de la précision d'un modèle"""
        ML_MODEL_ACCURACY.labels(
            model_name=model_name,
            version=version
        ).set(accuracy)
    
    @staticmethod
    def record_business_impact(impact_type: str, amount: float):
        """Enregistrement d'un impact business"""
        BUSINESS_REVENUE_IMPACT.labels(impact_type=impact_type).inc(amount)
    
    @staticmethod
    def update_priority_queue_size(size: int):
        """Mise à jour de la taille de la queue de priorité"""
        BUSINESS_PRIORITY_QUEUE_SIZE.set(size)
    
    @staticmethod
    def record_churn_prediction(risk_level: str):
        """Enregistrement d'une prédiction de churn"""
        BUSINESS_CHURN_PREDICTIONS.labels(risk_level=risk_level).inc()


class CustomMetricsRegistry:
    """Registre personnalisé pour les métriques métier"""
    
    def __init__(self):
        self.custom_counters: Dict[str, Counter] = {}
        self.custom_gauges: Dict[str, Gauge] = {}
        self.custom_histograms: Dict[str, Histogram] = {}
    
    def create_counter(self, name: str, description: str, labels: list = None):
        """Création d'un compteur personnalisé"""
        if name not in self.custom_counters:
            self.custom_counters[name] = Counter(
                f'custom_{name}',
                description,
                labels or []
            )
        return self.custom_counters[name]
    
    def create_gauge(self, name: str, description: str, labels: list = None):
        """Création d'une jauge personnalisée"""
        if name not in self.custom_gauges:
            self.custom_gauges[name] = Gauge(
                f'custom_{name}',
                description,
                labels or []
            )
        return self.custom_gauges[name]
    
    def create_histogram(self, name: str, description: str, labels: list = None, buckets: list = None):
        """Création d'un histogramme personnalisé"""
        if name not in self.custom_histograms:
            self.custom_histograms[name] = Histogram(
                f'custom_{name}',
                description,
                labels or [],
                buckets=buckets
            )
        return self.custom_histograms[name]
    
    def get_all_metrics(self) -> Dict[str, Any]:
        """Récupération de toutes les métriques personnalisées"""
        return {
            'counters': list(self.custom_counters.keys()),
            'gauges': list(self.custom_gauges.keys()),
            'histograms': list(self.custom_histograms.keys())
        }


# Instance globale du registre personnalisé
custom_metrics = CustomMetricsRegistry()


def setup_business_metrics():
    """Configuration des métriques business spécifiques Free Mobile"""
    
    # Métriques de satisfaction client
    custom_metrics.create_counter(
        'customer_satisfaction_events',
        'Customer satisfaction events',
        ['event_type', 'satisfaction_level']
    )
    
    # Métriques de résolution de problèmes
    custom_metrics.create_histogram(
        'problem_resolution_time',
        'Time to resolve customer problems',
        ['problem_category', 'resolution_type'],
        buckets=[60, 300, 900, 1800, 3600, 7200]  # 1min à 2h
    )
    
    # Métriques d'opportunités commerciales
    custom_metrics.create_counter(
        'sales_opportunities_identified',
        'Sales opportunities identified by ML',
        ['opportunity_type', 'customer_segment']
    )
    
    # Métriques de rétention client
    custom_metrics.create_gauge(
        'churn_risk_distribution',
        'Distribution of churn risk levels',
        ['risk_level', 'customer_segment']
    )


# Initialisation des métriques business
setup_business_metrics()
