"""
Middleware de rate limiting pour le service ML
Free Mobile Chatbot Dashboard - Protection contre les abus
"""

import time
from typing import Dict, <PERSON><PERSON>
from collections import defaultdict, deque

from fastapi import Request, Response, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware


class RateLimitMiddleware(BaseHTTPMiddleware):
    """
    Middleware de limitation du taux de requêtes
    Implémentation sliding window avec Redis optionnel
    """
    
    def __init__(self, app, max_requests: int = 1000, window_seconds: int = 60):
        super().__init__(app)
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        
        # Stockage en mémoire pour le développement
        # En production, utiliser Redis pour la scalabilité
        self.request_counts: Dict[str, deque] = defaultdict(deque)
    
    async def dispatch(self, request: Request, call_next):
        """Traitement des requêtes avec rate limiting"""
        
        # Identifier le client (IP + API key si disponible)
        client_id = self._get_client_id(request)
        
        # Vérifier le rate limit
        if not await self._check_rate_limit(client_id):
            return Response(
                content='{"error": "Rate limit exceeded", "detail": "Too many requests"}',
                status_code=429,
                headers={
                    "Content-Type": "application/json",
                    "Retry-After": str(self.window_seconds),
                    "X-RateLimit-Limit": str(self.max_requests),
                    "X-RateLimit-Window": str(self.window_seconds)
                }
            )
        
        # Enregistrer la requête
        await self._record_request(client_id)
        
        # Traiter la requête
        response = await call_next(request)
        
        # Ajouter les headers de rate limiting
        remaining = await self._get_remaining_requests(client_id)
        response.headers["X-RateLimit-Limit"] = str(self.max_requests)
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        response.headers["X-RateLimit-Window"] = str(self.window_seconds)
        
        return response
    
    def _get_client_id(self, request: Request) -> str:
        """Identification du client pour le rate limiting"""
        
        # Priorité à l'API key si disponible
        api_key = request.headers.get("X-API-Key") or request.headers.get("Authorization", "").replace("Bearer ", "")
        if api_key:
            return f"api_key:{api_key[:16]}"  # Tronquer pour la sécurité
        
        # Fallback sur l'IP
        client_ip = request.client.host
        
        # Vérifier les headers de proxy
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            client_ip = forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            client_ip = real_ip
        
        return f"ip:{client_ip}"
    
    async def _check_rate_limit(self, client_id: str) -> bool:
        """Vérification du rate limit pour un client"""
        
        current_time = time.time()
        window_start = current_time - self.window_seconds
        
        # Nettoyer les anciennes requêtes
        client_requests = self.request_counts[client_id]
        while client_requests and client_requests[0] < window_start:
            client_requests.popleft()
        
        # Vérifier si le limite est dépassé
        return len(client_requests) < self.max_requests
    
    async def _record_request(self, client_id: str):
        """Enregistrement d'une nouvelle requête"""
        current_time = time.time()
        self.request_counts[client_id].append(current_time)
    
    async def _get_remaining_requests(self, client_id: str) -> int:
        """Calcul du nombre de requêtes restantes"""
        current_time = time.time()
        window_start = current_time - self.window_seconds
        
        # Nettoyer les anciennes requêtes
        client_requests = self.request_counts[client_id]
        while client_requests and client_requests[0] < window_start:
            client_requests.popleft()
        
        return max(0, self.max_requests - len(client_requests))


class AdaptiveRateLimitMiddleware(BaseHTTPMiddleware):
    """
    Middleware de rate limiting adaptatif
    Ajuste les limites selon la charge système et le type de client
    """
    
    def __init__(
        self,
        app,
        base_max_requests: int = 1000,
        window_seconds: int = 60,
        premium_multiplier: float = 2.0,
        admin_multiplier: float = 5.0
    ):
        super().__init__(app)
        self.base_max_requests = base_max_requests
        self.window_seconds = window_seconds
        self.premium_multiplier = premium_multiplier
        self.admin_multiplier = admin_multiplier
        
        self.request_counts: Dict[str, deque] = defaultdict(deque)
        self.client_tiers: Dict[str, str] = {}  # Cache des tiers clients
    
    async def dispatch(self, request: Request, call_next):
        """Traitement avec rate limiting adaptatif"""
        
        client_id = self._get_client_id(request)
        client_tier = await self._get_client_tier(request, client_id)
        
        # Calcul de la limite adaptée
        max_requests = self._calculate_adaptive_limit(client_tier)
        
        # Vérification du rate limit
        if not await self._check_adaptive_rate_limit(client_id, max_requests):
            return Response(
                content=f'{{"error": "Rate limit exceeded", "tier": "{client_tier}", "limit": {max_requests}}}',
                status_code=429,
                headers={
                    "Content-Type": "application/json",
                    "Retry-After": str(self.window_seconds),
                    "X-RateLimit-Limit": str(max_requests),
                    "X-RateLimit-Tier": client_tier
                }
            )
        
        # Enregistrer la requête
        await self._record_request(client_id)
        
        # Traiter la requête
        response = await call_next(request)
        
        # Headers informatifs
        remaining = await self._get_remaining_requests(client_id, max_requests)
        response.headers["X-RateLimit-Limit"] = str(max_requests)
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        response.headers["X-RateLimit-Tier"] = client_tier
        
        return response
    
    def _get_client_id(self, request: Request) -> str:
        """Identification du client (même logique que RateLimitMiddleware)"""
        api_key = request.headers.get("X-API-Key") or request.headers.get("Authorization", "").replace("Bearer ", "")
        if api_key:
            return f"api_key:{api_key[:16]}"
        
        client_ip = request.client.host
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            client_ip = forwarded_for.split(",")[0].strip()
        
        return f"ip:{client_ip}"
    
    async def _get_client_tier(self, request: Request, client_id: str) -> str:
        """Détermination du tier client"""
        
        # Cache lookup
        if client_id in self.client_tiers:
            return self.client_tiers[client_id]
        
        # Détection basée sur l'API key
        api_key = request.headers.get("X-API-Key") or request.headers.get("Authorization", "").replace("Bearer ", "")
        
        if api_key:
            if "admin" in api_key.lower():
                tier = "admin"
            elif "premium" in api_key.lower():
                tier = "premium"
            else:
                tier = "standard"
        else:
            tier = "basic"  # Clients sans API key
        
        # Mise en cache
        self.client_tiers[client_id] = tier
        
        return tier
    
    def _calculate_adaptive_limit(self, client_tier: str) -> int:
        """Calcul de la limite adaptée selon le tier"""
        
        base_limit = self.base_max_requests
        
        if client_tier == "admin":
            return int(base_limit * self.admin_multiplier)
        elif client_tier == "premium":
            return int(base_limit * self.premium_multiplier)
        elif client_tier == "standard":
            return base_limit
        else:  # basic
            return int(base_limit * 0.5)
    
    async def _check_adaptive_rate_limit(self, client_id: str, max_requests: int) -> bool:
        """Vérification du rate limit adaptatif"""
        current_time = time.time()
        window_start = current_time - self.window_seconds
        
        client_requests = self.request_counts[client_id]
        while client_requests and client_requests[0] < window_start:
            client_requests.popleft()
        
        return len(client_requests) < max_requests
    
    async def _record_request(self, client_id: str):
        """Enregistrement d'une requête"""
        current_time = time.time()
        self.request_counts[client_id].append(current_time)
    
    async def _get_remaining_requests(self, client_id: str, max_requests: int) -> int:
        """Calcul des requêtes restantes"""
        current_time = time.time()
        window_start = current_time - self.window_seconds
        
        client_requests = self.request_counts[client_id]
        while client_requests and client_requests[0] < window_start:
            client_requests.popleft()
        
        return max(0, max_requests - len(client_requests))
