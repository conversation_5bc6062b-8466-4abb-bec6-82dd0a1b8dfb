"""
Middleware d'authentification pour le service ML
Free Mobile Chatbot Dashboard - Sécurité API
"""

import hashlib
import hmac
import time
from typing import Optional

from fastapi import HTTPEx<PERSON>, Header, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from ..config import settings


security = HTTPBearer(auto_error=False)


async def verify_api_key(
    x_api_key: Optional[str] = Header(None, alias=settings.API_KEY_HEADER),
    authorization: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> str:
    """
    Vérification de la clé API
    Support pour header X-API-Key ou Authorization Bearer
    """
    
    # Vérification via header X-API-Key
    if x_api_key:
        if await validate_api_key(x_api_key):
            return x_api_key
    
    # Vérification via Authorization Bearer
    if authorization:
        if await validate_api_key(authorization.credentials):
            return authorization.credentials
    
    # Aucune authentification valide
    raise HTTPException(
        status_code=401,
        detail="Invalid or missing API key",
        headers={"WWW-Authenticate": "Bearer"}
    )


async def validate_api_key(api_key: str) -> bool:
    """
    Validation de la clé API
    En production, vérifier contre une base de données
    """
    
    # Pour le développement, accepter une clé simple
    if settings.DEBUG:
        return api_key in ["dev-api-key", "free-mobile-ml-key"]
    
    # En production, implémenter une validation plus robuste
    # Exemple avec hash HMAC
    try:
        # Format attendu: timestamp:hash
        parts = api_key.split(':')
        if len(parts) != 2:
            return False
        
        timestamp_str, provided_hash = parts
        timestamp = int(timestamp_str)
        
        # Vérifier que la clé n'est pas trop ancienne (5 minutes)
        current_time = int(time.time())
        if current_time - timestamp > 300:
            return False
        
        # Calculer le hash attendu
        secret_key = settings.JWT_SECRET.encode()
        message = f"{timestamp_str}:free-mobile-ml".encode()
        expected_hash = hmac.new(secret_key, message, hashlib.sha256).hexdigest()
        
        # Comparaison sécurisée
        return hmac.compare_digest(provided_hash, expected_hash)
        
    except (ValueError, TypeError):
        return False


def generate_api_key() -> str:
    """
    Génération d'une clé API temporaire
    Utile pour les tests et le développement
    """
    timestamp = int(time.time())
    secret_key = settings.JWT_SECRET.encode()
    message = f"{timestamp}:free-mobile-ml".encode()
    hash_value = hmac.new(secret_key, message, hashlib.sha256).hexdigest()
    
    return f"{timestamp}:{hash_value}"


# Décorateur pour les endpoints admin uniquement
async def verify_admin_access(api_key: str = Depends(verify_api_key)) -> str:
    """
    Vérification des droits administrateur
    """
    # En production, vérifier les permissions dans la base de données
    if settings.DEBUG:
        return api_key
    
    # Logique de vérification des droits admin
    # À implémenter selon les besoins
    admin_keys = ["admin-key", "free-mobile-admin"]
    
    if api_key not in admin_keys:
        raise HTTPException(
            status_code=403,
            detail="Admin access required"
        )
    
    return api_key
