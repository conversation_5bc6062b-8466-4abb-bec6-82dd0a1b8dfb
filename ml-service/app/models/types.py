"""
Types et modèles Pydantic pour le service ML
Free Mobile Chatbot Dashboard - Classification intelligente
"""

from datetime import datetime
from enum import Enum
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, validator


class ConversationCategory(str, Enum):
    """Catégories de classification des conversations"""
    VENTE_OPPORTUNITE = "VENTE_OPPORTUNITE"
    RESILIATION_CRITIQUE = "RESILIATION_CRITIQUE"
    SUPPORT_URGENT = "SUPPORT_URGENT"
    RECLAMATION = "RECLAMATION"
    INFO_SIMPLE = "INFO_SIMPLE"


class SentimentTrend(str, Enum):
    """Tendance du sentiment client"""
    IMPROVING = "improving"
    DECLINING = "declining"
    STABLE = "stable"


class ActionType(str, Enum):
    """Types d'actions recommandées"""
    ESCALATE_TO_AGENT = "ESCALATE_TO_AGENT"
    OFFER_UPGRADE = "OFFER_UPGRADE"
    RETENTION_CALL = "RETENTION_CALL"
    TECHNICAL_SUPPORT = "TECHNICAL_SUPPORT"
    BILLING_REVIEW = "BILLING_REVIEW"
    SATISFACTION_SURVEY = "SATISFACTION_SURVEY"


class Message(BaseModel):
    """Message dans une conversation"""
    id: str
    sender: str  # 'user' | 'bot' | 'agent'
    content: str
    timestamp: datetime
    metadata: Optional[Dict[str, Any]] = None


class CustomerProfile(BaseModel):
    """Profil client pour contexte ML"""
    customer_id: str
    plan_type: str
    monthly_revenue: float
    tenure_months: int
    support_tickets_count: int
    satisfaction_score: Optional[float] = None
    churn_risk_score: Optional[float] = None
    lifetime_value: float


class ConversationMetadata(BaseModel):
    """Métadonnées de conversation"""
    conversation_id: str
    channel: str  # 'web' | 'mobile' | 'phone'
    duration_minutes: Optional[int] = None
    agent_id: Optional[str] = None
    previous_interactions: int = 0


class RecommendedAction(BaseModel):
    """Action recommandée par le ML"""
    type: ActionType
    priority: int = Field(..., ge=1, le=10)
    script: str
    expected_outcome: str
    confidence: float = Field(..., ge=0.0, le=1.0)
    estimated_revenue_impact: Optional[float] = None


class BusinessImpact(BaseModel):
    """Impact business calculé"""
    revenue_at_risk: float = 0.0
    opportunity_value: float = 0.0
    retention_probability: float = Field(..., ge=0.0, le=1.0)
    lifetime_value_impact: float = 0.0


class SentimentAnalysis(BaseModel):
    """Analyse de sentiment"""
    score: float = Field(..., ge=-1.0, le=1.0)
    trend: SentimentTrend
    confidence: float = Field(..., ge=0.0, le=1.0)
    key_emotions: List[str] = []


class ConversationClassification(BaseModel):
    """Résultat complet de classification"""
    conversation_id: str
    category: ConversationCategory
    priority_score: int = Field(..., ge=0, le=100)
    confidence: float = Field(..., ge=0.0, le=1.0)
    
    business_impact: BusinessImpact
    sentiment: SentimentAnalysis
    recommended_actions: List[RecommendedAction]
    
    ml_model_version: str
    processing_time_ms: float
    processed_at: datetime = Field(default_factory=datetime.utcnow)
    
    # Métadonnées ML
    features_used: List[str] = []
    model_latency_ms: float = 0.0


class ClassificationRequest(BaseModel):
    """Requête de classification"""
    conversation_id: str
    messages: List[Message]
    customer_data: CustomerProfile
    conversation_metadata: ConversationMetadata
    force_reprocess: bool = False


class ClassificationResponse(BaseModel):
    """Réponse de classification"""
    success: bool
    classification: Optional[ConversationClassification] = None
    error: Optional[str] = None
    cached: bool = False


class ModelPerformanceMetrics(BaseModel):
    """Métriques de performance des modèles"""
    model_name: str
    version: str
    accuracy: float
    precision: float
    recall: float
    f1_score: float
    avg_latency_ms: float
    throughput_per_second: float
    last_updated: datetime


class HealthCheckResponse(BaseModel):
    """Réponse health check"""
    status: str
    timestamp: datetime
    models_loaded: Dict[str, bool]
    redis_connected: bool
    database_connected: bool
    memory_usage_mb: float
    cpu_usage_percent: float


# Validation personnalisée
@validator('priority_score')
def validate_priority_score(cls, v):
    if not 0 <= v <= 100:
        raise ValueError('Priority score must be between 0 and 100')
    return v


@validator('confidence')
def validate_confidence(cls, v):
    if not 0.0 <= v <= 1.0:
        raise ValueError('Confidence must be between 0.0 and 1.0')
    return v
