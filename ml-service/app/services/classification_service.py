"""
Service de classification ML principal
Free Mobile Chatbot Dashboard - Classification intelligente des conversations
Performance: < 200ms par classification, 1000+ req/sec
"""

import asyncio
import time
from typing import List, Dict, Any, Optional, Tuple
import logging
from datetime import datetime

import torch
# from transformers import AutoTokenizer, AutoModelForSequenceClassification
# Temporarily disabled for demo - using mock classification
import numpy as np
from sklearn.preprocessing import StandardScaler
import joblib

from ..models.types import (
    ConversationClassification, ClassificationRequest, Message,
    CustomerProfile, ConversationMetadata, ConversationCategory,
    BusinessImpact, SentimentAnalysis, RecommendedAction,
    SentimentTrend, ActionType
)
from ..config import settings, ML_MODELS_CONFIG
from .cache_service import CacheService
from .feature_engineering import FeatureEngineer


logger = logging.getLogger(__name__)


class MLClassificationService:
    """
    Service de classification ML avec modèles pré-entraînés
    Performance requise: < 200ms par classification
    Throughput: 1000+ classifications/seconde
    """
    
    def __init__(self):
        self.models = {}
        self.tokenizers = {}
        self.scalers = {}
        self.cache_service = CacheService()
        self.feature_engineer = FeatureEngineer()
        self.model_versions = {}
        self.performance_metrics = {}
        
        # Compteurs pour métriques
        self.classification_count = 0
        self.total_processing_time = 0.0
        
    async def initialize(self):
        """Initialisation asynchrone des modèles ML"""
        logger.info("Initializing ML Classification Service...")
        
        try:
            # Chargement parallèle des modèles
            await asyncio.gather(
                self._load_intent_classifier(),
                self._load_sentiment_analyzer(),
                self._load_churn_predictor(),
                self._load_opportunity_scorer()
            )
            
            # Warmup des modèles si activé
            if settings.MODEL_WARMUP_ENABLED:
                await self._warmup_models()
                
            logger.info("ML Classification Service initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize ML service: {e}")
            raise
    
    async def _load_intent_classifier(self):
        """Chargement du classificateur d'intentions CamemBERT"""
        config = ML_MODELS_CONFIG["intent_classifier"]
        
        try:
            # Chargement du tokenizer et modèle
            tokenizer = AutoTokenizer.from_pretrained(settings.CAMEMBERT_MODEL)
            model = AutoModelForSequenceClassification.from_pretrained(
                settings.CAMEMBERT_MODEL,
                num_labels=len(config["categories"])
            )
            
            # Mode évaluation pour l'inférence
            model.eval()
            
            self.tokenizers["intent"] = tokenizer
            self.models["intent"] = model
            self.model_versions["intent"] = config["version"]
            
            logger.info(f"Intent classifier loaded: {config['model_name']} v{config['version']}")
            
        except Exception as e:
            logger.error(f"Failed to load intent classifier: {e}")
            raise
    
    async def _load_sentiment_analyzer(self):
        """Chargement de l'analyseur de sentiment"""
        config = ML_MODELS_CONFIG["sentiment_analyzer"]
        
        try:
            tokenizer = AutoTokenizer.from_pretrained(settings.SENTIMENT_MODEL)
            model = AutoModelForSequenceClassification.from_pretrained(
                settings.SENTIMENT_MODEL
            )
            
            model.eval()
            
            self.tokenizers["sentiment"] = tokenizer
            self.models["sentiment"] = model
            self.model_versions["sentiment"] = config["version"]
            
            logger.info(f"Sentiment analyzer loaded: {config['model_name']} v{config['version']}")
            
        except Exception as e:
            logger.error(f"Failed to load sentiment analyzer: {e}")
            raise
    
    async def _load_churn_predictor(self):
        """Chargement du prédicteur de churn"""
        config = ML_MODELS_CONFIG["churn_predictor"]
        
        try:
            # Modèle scikit-learn pour le churn (plus rapide pour features tabulaires)
            model_path = f"{config['path']}/churn_model.joblib"
            scaler_path = f"{config['path']}/churn_scaler.joblib"
            
            # En production, charger depuis les fichiers
            # Pour le développement, créer un modèle mock
            self.models["churn"] = self._create_mock_churn_model()
            self.scalers["churn"] = StandardScaler()
            self.model_versions["churn"] = config["version"]
            
            logger.info(f"Churn predictor loaded: {config['model_name']} v{config['version']}")
            
        except Exception as e:
            logger.error(f"Failed to load churn predictor: {e}")
            raise
    
    async def _load_opportunity_scorer(self):
        """Chargement du scorer d'opportunités"""
        config = ML_MODELS_CONFIG["opportunity_scorer"]
        
        try:
            # Modèle d'opportunités (règles business + ML)
            self.models["opportunity"] = self._create_mock_opportunity_model()
            self.model_versions["opportunity"] = config["version"]
            
            logger.info(f"Opportunity scorer loaded: {config['model_name']} v{config['version']}")
            
        except Exception as e:
            logger.error(f"Failed to load opportunity scorer: {e}")
            raise
    
    def _create_mock_churn_model(self):
        """Modèle mock pour le churn (remplacer par modèle réel en production)"""
        class MockChurnModel:
            def predict_proba(self, X):
                # Simulation basée sur les features
                probabilities = []
                for features in X:
                    # Logique business simple
                    tenure, tickets, satisfaction, revenue = features[:4]
                    
                    risk_score = 0.0
                    if tenure < 6:  # Nouveaux clients plus à risque
                        risk_score += 0.3
                    if tickets > 3:  # Beaucoup de tickets = insatisfaction
                        risk_score += 0.4
                    if satisfaction < 3.0:  # Satisfaction faible
                        risk_score += 0.5
                    if revenue < 10:  # Clients low-value plus volatiles
                        risk_score += 0.2
                    
                    risk_score = min(risk_score, 0.95)  # Cap à 95%
                    probabilities.append([1 - risk_score, risk_score])
                
                return np.array(probabilities)
        
        return MockChurnModel()
    
    def _create_mock_opportunity_model(self):
        """Modèle mock pour les opportunités"""
        class MockOpportunityModel:
            def score_opportunities(self, customer_data, conversation_features):
                opportunities = []
                
                # Règles business Free Mobile
                if customer_data.monthly_revenue < 20:
                    opportunities.append({
                        "type": "UPGRADE_MOBILE",
                        "confidence": 0.7,
                        "value": 10.0,
                        "script": "Je vois que vous avez le forfait 2€. Souhaiteriez-vous découvrir nos forfaits avec plus de data?"
                    })
                
                if customer_data.tenure_months > 12 and customer_data.satisfaction_score > 4.0:
                    opportunities.append({
                        "type": "FAMILY_PLAN",
                        "confidence": 0.6,
                        "value": 30.0,
                        "script": "En tant que client fidèle, vous pourriez bénéficier de nos offres famille."
                    })
                
                return opportunities
        
        return MockOpportunityModel()
    
    async def _warmup_models(self):
        """Warmup des modèles avec des données factices"""
        logger.info("Warming up ML models...")
        
        # Données de test pour warmup
        dummy_messages = [
            Message(
                id="warmup-1",
                sender="user",
                content="Je veux résilier mon abonnement",
                timestamp=datetime.utcnow()
            )
        ]
        
        dummy_customer = CustomerProfile(
            customer_id="warmup-customer",
            plan_type="2€",
            monthly_revenue=2.0,
            tenure_months=6,
            support_tickets_count=1,
            lifetime_value=48.0
        )
        
        dummy_metadata = ConversationMetadata(
            conversation_id="warmup-conv",
            channel="web"
        )
        
        # Exécution warmup
        start_time = time.time()
        await self._classify_conversation_internal(
            dummy_messages, dummy_customer, dummy_metadata
        )
        warmup_time = time.time() - start_time
        
        logger.info(f"Model warmup completed in {warmup_time:.2f}s")
    
    async def classify_conversation(self, request: ClassificationRequest) -> ConversationClassification:
        """
        Classification complète d'une conversation
        Performance target: < 200ms
        """
        start_time = time.time()
        
        try:
            # Vérifier le cache d'abord
            if settings.ENABLE_CACHING and not request.force_reprocess:
                cached_result = await self.cache_service.get_classification(
                    request.conversation_id
                )
                if cached_result:
                    logger.info(f"Classification served from cache: {request.conversation_id}")
                    return cached_result
            
            # Classification ML
            classification = await self._classify_conversation_internal(
                request.messages,
                request.customer_data,
                request.conversation_metadata
            )
            
            # Mise en cache du résultat
            if settings.ENABLE_CACHING:
                await self.cache_service.cache_classification(
                    request.conversation_id,
                    classification
                )
            
            # Métriques de performance
            processing_time = (time.time() - start_time) * 1000  # ms
            classification.processing_time_ms = processing_time
            
            self._update_performance_metrics(processing_time)
            
            logger.info(
                f"Conversation classified: {request.conversation_id} "
                f"({classification.category}) in {processing_time:.1f}ms"
            )
            
            return classification
            
        except Exception as e:
            logger.error(f"Classification failed for {request.conversation_id}: {e}")
            raise
    
    async def _classify_conversation_internal(
        self,
        messages: List[Message],
        customer_data: CustomerProfile,
        metadata: ConversationMetadata
    ) -> ConversationClassification:
        """Classification interne avec tous les modèles"""
        
        # Feature engineering
        features = await self.feature_engineer.extract_features(
            messages, customer_data, metadata
        )
        
        # Exécution parallèle des modèles ML
        tasks = [
            self._classify_intent(features["text_features"]),
            self._analyze_sentiment(features["text_features"]),
            self._predict_churn(features["customer_features"]),
            self._score_opportunities(customer_data, features)
        ]
        
        intent_result, sentiment_result, churn_result, opportunities = await asyncio.gather(*tasks)
        
        # Calcul du score de priorité business
        priority_score = self._calculate_business_priority(
            intent_result, sentiment_result, churn_result, customer_data
        )
        
        # Impact business
        business_impact = self._calculate_business_impact(
            churn_result, opportunities, customer_data
        )
        
        # Actions recommandées
        recommended_actions = self._generate_recommended_actions(
            intent_result, sentiment_result, churn_result, opportunities
        )
        
        return ConversationClassification(
            conversation_id=metadata.conversation_id,
            category=intent_result["category"],
            priority_score=priority_score,
            confidence=min([
                intent_result["confidence"],
                sentiment_result["confidence"],
                churn_result["confidence"]
            ]),
            business_impact=business_impact,
            sentiment=sentiment_result["analysis"],
            recommended_actions=recommended_actions,
            ml_model_version=f"ensemble-{self.model_versions['intent']}",
            processing_time_ms=0.0,  # Sera mis à jour par le caller
            features_used=list(features.keys())
        )

    async def _classify_intent(self, text_features: Dict[str, Any]) -> Dict[str, Any]:
        """Classification d'intention avec CamemBERT"""
        try:
            # Préparation du texte
            text = text_features.get("combined_text", "")

            # Tokenisation
            tokenizer = self.tokenizers["intent"]
            inputs = tokenizer(
                text,
                return_tensors="pt",
                truncation=True,
                padding=True,
                max_length=512
            )

            # Inférence
            with torch.no_grad():
                outputs = self.models["intent"](**inputs)
                probabilities = torch.softmax(outputs.logits, dim=-1)
                predicted_class = torch.argmax(probabilities, dim=-1).item()
                confidence = probabilities[0][predicted_class].item()

            # Mapping vers catégories
            categories = ML_MODELS_CONFIG["intent_classifier"]["categories"]
            category = ConversationCategory(categories[predicted_class])

            return {
                "category": category,
                "confidence": confidence,
                "probabilities": probabilities[0].tolist()
            }

        except Exception as e:
            logger.error(f"Intent classification failed: {e}")
            # Fallback
            return {
                "category": ConversationCategory.INFO_SIMPLE,
                "confidence": 0.5,
                "probabilities": [0.2, 0.2, 0.2, 0.2, 0.2]
            }

    async def _analyze_sentiment(self, text_features: Dict[str, Any]) -> Dict[str, Any]:
        """Analyse de sentiment multilingue"""
        try:
            text = text_features.get("combined_text", "")

            tokenizer = self.tokenizers["sentiment"]
            inputs = tokenizer(
                text,
                return_tensors="pt",
                truncation=True,
                padding=True,
                max_length=256
            )

            with torch.no_grad():
                outputs = self.models["sentiment"](**inputs)
                probabilities = torch.softmax(outputs.logits, dim=-1)

                # Conversion en score -1 à 1
                # Assuming 5-class sentiment: [very_negative, negative, neutral, positive, very_positive]
                scores = [-1.0, -0.5, 0.0, 0.5, 1.0]
                sentiment_score = sum(prob * score for prob, score in zip(probabilities[0], scores))
                confidence = torch.max(probabilities).item()

            # Détermination de la tendance (simplifiée pour le mock)
            if sentiment_score > 0.2:
                trend = SentimentTrend.IMPROVING
            elif sentiment_score < -0.2:
                trend = SentimentTrend.DECLINING
            else:
                trend = SentimentTrend.STABLE

            analysis = SentimentAnalysis(
                score=float(sentiment_score),
                trend=trend,
                confidence=confidence,
                key_emotions=self._extract_key_emotions(text)
            )

            return {
                "analysis": analysis,
                "confidence": confidence
            }

        except Exception as e:
            logger.error(f"Sentiment analysis failed: {e}")
            # Fallback
            return {
                "analysis": SentimentAnalysis(
                    score=0.0,
                    trend=SentimentTrend.STABLE,
                    confidence=0.5,
                    key_emotions=[]
                ),
                "confidence": 0.5
            }

    async def _predict_churn(self, customer_features: Dict[str, Any]) -> Dict[str, Any]:
        """Prédiction du risque de churn"""
        try:
            # Préparation des features
            features = [
                customer_features.get("tenure_months", 0),
                customer_features.get("support_tickets_count", 0),
                customer_features.get("satisfaction_score", 3.0),
                customer_features.get("monthly_revenue", 0.0),
                customer_features.get("recent_sentiment_trend", 0.0)
            ]

            # Normalisation
            features_scaled = self.scalers["churn"].fit_transform([features])

            # Prédiction
            probabilities = self.models["churn"].predict_proba(features_scaled)
            churn_probability = probabilities[0][1]  # Probabilité de churn

            return {
                "churn_probability": float(churn_probability),
                "confidence": 0.8,  # Confiance du modèle
                "risk_level": "HIGH" if churn_probability > settings.CHURN_RISK_THRESHOLD else "LOW"
            }

        except Exception as e:
            logger.error(f"Churn prediction failed: {e}")
            return {
                "churn_probability": 0.3,
                "confidence": 0.5,
                "risk_level": "LOW"
            }

    async def _score_opportunities(self, customer_data: CustomerProfile, features: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Scoring des opportunités de vente"""
        try:
            opportunities = self.models["opportunity"].score_opportunities(
                customer_data, features
            )

            return opportunities

        except Exception as e:
            logger.error(f"Opportunity scoring failed: {e}")
            return []

    def _extract_key_emotions(self, text: str) -> List[str]:
        """Extraction des émotions clés (version simplifiée)"""
        emotion_keywords = {
            "frustration": ["énervé", "frustré", "agacé", "marre"],
            "satisfaction": ["content", "satisfait", "parfait", "merci"],
            "urgence": ["urgent", "rapidement", "vite", "immédiatement"],
            "confusion": ["comprends pas", "confus", "bizarre", "étrange"]
        }

        detected_emotions = []
        text_lower = text.lower()

        for emotion, keywords in emotion_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                detected_emotions.append(emotion)

        return detected_emotions

    def _calculate_business_priority(
        self,
        intent_result: Dict[str, Any],
        sentiment_result: Dict[str, Any],
        churn_result: Dict[str, Any],
        customer_data: CustomerProfile
    ) -> int:
        """Calcul du score de priorité business (0-100)"""

        priority = 0

        # Priorité basée sur la catégorie
        category_weights = {
            ConversationCategory.RESILIATION_CRITIQUE: 40,
            ConversationCategory.SUPPORT_URGENT: 30,
            ConversationCategory.VENTE_OPPORTUNITE: 25,
            ConversationCategory.RECLAMATION: 20,
            ConversationCategory.INFO_SIMPLE: 10
        }
        priority += category_weights.get(intent_result["category"], 10)

        # Priorité basée sur le sentiment
        sentiment_score = sentiment_result["analysis"].score
        if sentiment_score < -0.5:  # Très négatif
            priority += 25
        elif sentiment_score < 0:  # Négatif
            priority += 15

        # Priorité basée sur le risque de churn
        churn_prob = churn_result["churn_probability"]
        if churn_prob > 0.7:
            priority += 20
        elif churn_prob > 0.5:
            priority += 10

        # Priorité basée sur la valeur client
        if customer_data.monthly_revenue > settings.HIGH_VALUE_CUSTOMER_THRESHOLD:
            priority += 15

        # Priorité basée sur l'historique de tickets
        if customer_data.support_tickets_count > 5:
            priority += 10

        return min(priority, 100)

    def _calculate_business_impact(
        self,
        churn_result: Dict[str, Any],
        opportunities: List[Dict[str, Any]],
        customer_data: CustomerProfile
    ) -> BusinessImpact:
        """Calcul de l'impact business"""

        # Revenus à risque (churn)
        churn_prob = churn_result["churn_probability"]
        revenue_at_risk = customer_data.lifetime_value * churn_prob

        # Valeur des opportunités
        opportunity_value = sum(opp.get("value", 0) for opp in opportunities)

        # Probabilité de rétention
        retention_probability = 1.0 - churn_prob

        # Impact sur la lifetime value
        ltv_impact = opportunity_value - (revenue_at_risk * settings.CHURN_COST_MULTIPLIER)

        return BusinessImpact(
            revenue_at_risk=revenue_at_risk,
            opportunity_value=opportunity_value,
            retention_probability=retention_probability,
            lifetime_value_impact=ltv_impact
        )

    def _generate_recommended_actions(
        self,
        intent_result: Dict[str, Any],
        sentiment_result: Dict[str, Any],
        churn_result: Dict[str, Any],
        opportunities: List[Dict[str, Any]]
    ) -> List[RecommendedAction]:
        """Génération des actions recommandées"""

        actions = []

        # Actions basées sur la catégorie
        category = intent_result["category"]

        if category == ConversationCategory.RESILIATION_CRITIQUE:
            actions.append(RecommendedAction(
                type=ActionType.RETENTION_CALL,
                priority=9,
                script="Escalader immédiatement vers un conseiller rétention. Proposer une offre personnalisée.",
                expected_outcome="Réduction du risque de churn de 60%",
                confidence=0.8,
                estimated_revenue_impact=200.0
            ))

        elif category == ConversationCategory.SUPPORT_URGENT:
            actions.append(RecommendedAction(
                type=ActionType.ESCALATE_TO_AGENT,
                priority=8,
                script="Transférer vers un technicien spécialisé dans les 5 minutes.",
                expected_outcome="Résolution rapide du problème technique",
                confidence=0.9
            ))

        # Actions basées sur les opportunités
        for opp in opportunities:
            if opp.get("confidence", 0) > settings.OPPORTUNITY_THRESHOLD:
                actions.append(RecommendedAction(
                    type=ActionType.OFFER_UPGRADE,
                    priority=6,
                    script=opp.get("script", "Proposer une offre adaptée"),
                    expected_outcome=f"Revenus additionnels: {opp.get('value', 0)}€/mois",
                    confidence=opp.get("confidence", 0.5),
                    estimated_revenue_impact=opp.get("value", 0) * 12  # Annuel
                ))

        # Actions basées sur le sentiment
        if sentiment_result["analysis"].score < -0.5:
            actions.append(RecommendedAction(
                type=ActionType.SATISFACTION_SURVEY,
                priority=7,
                script="Proposer un suivi satisfaction et identifier les points d'amélioration.",
                expected_outcome="Amélioration de la satisfaction client",
                confidence=0.7
            ))

        # Tri par priorité
        actions.sort(key=lambda x: x.priority, reverse=True)

        return actions[:5]  # Maximum 5 actions

    def _update_performance_metrics(self, processing_time_ms: float):
        """Mise à jour des métriques de performance"""
        self.classification_count += 1
        self.total_processing_time += processing_time_ms

        # Log des métriques périodiquement
        if self.classification_count % 100 == 0:
            avg_time = self.total_processing_time / self.classification_count
            logger.info(
                f"Performance metrics - Classifications: {self.classification_count}, "
                f"Avg time: {avg_time:.1f}ms"
            )

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Récupération des métriques de performance"""
        if self.classification_count == 0:
            return {"status": "no_data"}

        avg_processing_time = self.total_processing_time / self.classification_count
        throughput = 1000 / avg_processing_time if avg_processing_time > 0 else 0

        return {
            "total_classifications": self.classification_count,
            "average_processing_time_ms": avg_processing_time,
            "throughput_per_second": throughput,
            "models_loaded": len(self.models),
            "cache_hit_rate": self.cache_service.get_hit_rate() if hasattr(self.cache_service, 'get_hit_rate') else 0.0
        }
