"""
Service de cache Redis pour optimiser les performances ML
Free Mobile Chatbot Dashboard - Classification intelligente
"""

import json
import logging
from typing import Optional, Dict, Any
from datetime import datetime, timedelta

import aioredis
from aioredis import Redis

from ..models.types import ConversationClassification
from ..config import settings, REDIS_KEYS


logger = logging.getLogger(__name__)


class CacheService:
    """Service de cache Redis pour les classifications ML"""
    
    def __init__(self):
        self.redis: Optional[Redis] = None
        self.hit_count = 0
        self.miss_count = 0
    
    async def initialize(self):
        """Initialisation de la connexion Redis"""
        try:
            self.redis = await aioredis.from_url(
                settings.REDIS_URL,
                db=settings.REDIS_DB,
                password=settings.REDIS_PASSWORD,
                encoding="utf-8",
                decode_responses=True,
                socket_timeout=5.0,
                socket_connect_timeout=5.0,
                retry_on_timeout=True,
                health_check_interval=30
            )
            
            # Test de connexion
            await self.redis.ping()
            logger.info("Redis cache service initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Redis cache: {e}")
            self.redis = None
            raise
    
    async def get_classification(self, conversation_id: str) -> Optional[ConversationClassification]:
        """Récupération d'une classification depuis le cache"""
        if not self.redis or not settings.ENABLE_CACHING:
            return None
        
        try:
            cache_key = REDIS_KEYS["classification_cache"].format(
                conversation_id=conversation_id
            )
            
            cached_data = await self.redis.get(cache_key)
            
            if cached_data:
                self.hit_count += 1
                classification_dict = json.loads(cached_data)
                
                # Reconstruction de l'objet ConversationClassification
                classification = ConversationClassification.parse_obj(classification_dict)
                
                logger.debug(f"Cache hit for conversation: {conversation_id}")
                return classification
            else:
                self.miss_count += 1
                logger.debug(f"Cache miss for conversation: {conversation_id}")
                return None
                
        except Exception as e:
            logger.error(f"Cache retrieval failed for {conversation_id}: {e}")
            self.miss_count += 1
            return None
    
    async def cache_classification(
        self, 
        conversation_id: str, 
        classification: ConversationClassification
    ) -> bool:
        """Mise en cache d'une classification"""
        if not self.redis or not settings.ENABLE_CACHING:
            return False
        
        try:
            cache_key = REDIS_KEYS["classification_cache"].format(
                conversation_id=conversation_id
            )
            
            # Sérialisation en JSON
            classification_json = classification.json()
            
            # Mise en cache avec TTL
            await self.redis.setex(
                cache_key,
                settings.CACHE_TTL_SECONDS,
                classification_json
            )
            
            logger.debug(f"Classification cached for conversation: {conversation_id}")
            return True
            
        except Exception as e:
            logger.error(f"Cache storage failed for {conversation_id}: {e}")
            return False
    
    async def invalidate_classification(self, conversation_id: str) -> bool:
        """Invalidation d'une classification en cache"""
        if not self.redis:
            return False
        
        try:
            cache_key = REDIS_KEYS["classification_cache"].format(
                conversation_id=conversation_id
            )
            
            deleted = await self.redis.delete(cache_key)
            
            if deleted:
                logger.debug(f"Cache invalidated for conversation: {conversation_id}")
                return True
            else:
                logger.debug(f"No cache entry found for conversation: {conversation_id}")
                return False
                
        except Exception as e:
            logger.error(f"Cache invalidation failed for {conversation_id}: {e}")
            return False
    
    async def cache_model_result(
        self, 
        model_name: str, 
        input_hash: str, 
        result: Dict[str, Any],
        ttl_seconds: int = 1800  # 30 minutes
    ) -> bool:
        """Cache des résultats de modèles ML individuels"""
        if not self.redis or not settings.ENABLE_CACHING:
            return False
        
        try:
            cache_key = f"{REDIS_KEYS['model_cache'].format(model_name=model_name)}:{input_hash}"
            
            result_json = json.dumps(result, default=str)
            
            await self.redis.setex(cache_key, ttl_seconds, result_json)
            
            logger.debug(f"Model result cached: {model_name}:{input_hash[:8]}")
            return True
            
        except Exception as e:
            logger.error(f"Model cache storage failed: {e}")
            return False
    
    async def get_model_result(
        self, 
        model_name: str, 
        input_hash: str
    ) -> Optional[Dict[str, Any]]:
        """Récupération d'un résultat de modèle depuis le cache"""
        if not self.redis or not settings.ENABLE_CACHING:
            return None
        
        try:
            cache_key = f"{REDIS_KEYS['model_cache'].format(model_name=model_name)}:{input_hash}"
            
            cached_data = await self.redis.get(cache_key)
            
            if cached_data:
                result = json.loads(cached_data)
                logger.debug(f"Model cache hit: {model_name}:{input_hash[:8]}")
                return result
            else:
                logger.debug(f"Model cache miss: {model_name}:{input_hash[:8]}")
                return None
                
        except Exception as e:
            logger.error(f"Model cache retrieval failed: {e}")
            return None
    
    async def store_metrics(self, date: str, metrics: Dict[str, Any]) -> bool:
        """Stockage des métriques quotidiennes"""
        if not self.redis:
            return False
        
        try:
            metrics_key = REDIS_KEYS["metrics"].format(date=date)
            
            metrics_json = json.dumps(metrics, default=str)
            
            # Stockage avec TTL de 30 jours
            await self.redis.setex(metrics_key, 30 * 24 * 3600, metrics_json)
            
            logger.debug(f"Metrics stored for date: {date}")
            return True
            
        except Exception as e:
            logger.error(f"Metrics storage failed for {date}: {e}")
            return False
    
    async def get_metrics(self, date: str) -> Optional[Dict[str, Any]]:
        """Récupération des métriques pour une date"""
        if not self.redis:
            return None
        
        try:
            metrics_key = REDIS_KEYS["metrics"].format(date=date)
            
            cached_metrics = await self.redis.get(metrics_key)
            
            if cached_metrics:
                return json.loads(cached_metrics)
            else:
                return None
                
        except Exception as e:
            logger.error(f"Metrics retrieval failed for {date}: {e}")
            return None
    
    async def add_to_priority_queue(
        self, 
        conversation_id: str, 
        priority_score: int,
        metadata: Dict[str, Any]
    ) -> bool:
        """Ajout d'une conversation à la queue de priorité"""
        if not self.redis:
            return False
        
        try:
            queue_key = REDIS_KEYS["queue"]
            
            # Données de la queue
            queue_data = {
                "conversation_id": conversation_id,
                "priority_score": priority_score,
                "timestamp": datetime.utcnow().isoformat(),
                **metadata
            }
            
            # Ajout avec score de priorité (Redis sorted set)
            await self.redis.zadd(
                queue_key,
                {json.dumps(queue_data): priority_score}
            )
            
            logger.debug(f"Added to priority queue: {conversation_id} (priority: {priority_score})")
            return True
            
        except Exception as e:
            logger.error(f"Priority queue addition failed: {e}")
            return False
    
    async def get_priority_queue(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Récupération des conversations prioritaires"""
        if not self.redis:
            return []
        
        try:
            queue_key = REDIS_KEYS["queue"]
            
            # Récupération des éléments avec le score le plus élevé
            queue_items = await self.redis.zrevrange(
                queue_key, 0, limit - 1, withscores=True
            )
            
            priority_conversations = []
            for item_json, score in queue_items:
                item_data = json.loads(item_json)
                item_data["priority_score"] = int(score)
                priority_conversations.append(item_data)
            
            return priority_conversations
            
        except Exception as e:
            logger.error(f"Priority queue retrieval failed: {e}")
            return []
    
    async def update_health_status(self, status: Dict[str, Any]) -> bool:
        """Mise à jour du statut de santé du service"""
        if not self.redis:
            return False
        
        try:
            health_key = REDIS_KEYS["health"]
            
            status_json = json.dumps(status, default=str)
            
            # TTL court pour le health check
            await self.redis.setex(health_key, 60, status_json)
            
            return True
            
        except Exception as e:
            logger.error(f"Health status update failed: {e}")
            return False
    
    async def get_health_status(self) -> Optional[Dict[str, Any]]:
        """Récupération du statut de santé"""
        if not self.redis:
            return None
        
        try:
            health_key = REDIS_KEYS["health"]
            
            status_json = await self.redis.get(health_key)
            
            if status_json:
                return json.loads(status_json)
            else:
                return None
                
        except Exception as e:
            logger.error(f"Health status retrieval failed: {e}")
            return None
    
    def get_hit_rate(self) -> float:
        """Calcul du taux de hit du cache"""
        total_requests = self.hit_count + self.miss_count
        if total_requests == 0:
            return 0.0
        
        return self.hit_count / total_requests
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Statistiques du cache"""
        return {
            "hit_count": self.hit_count,
            "miss_count": self.miss_count,
            "hit_rate": self.get_hit_rate(),
            "total_requests": self.hit_count + self.miss_count,
            "redis_connected": self.redis is not None
        }
    
    async def close(self):
        """Fermeture de la connexion Redis"""
        if self.redis:
            await self.redis.close()
            logger.info("Redis connection closed")
