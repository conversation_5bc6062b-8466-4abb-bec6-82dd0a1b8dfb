"""
Service d'ingénierie des features pour ML
Free Mobile Chatbot Dashboard - Extraction et transformation des features
"""

import re
import hashlib
import logging
from typing import List, Dict, Any, Tuple
from datetime import datetime, timedelta
from collections import Counter

import numpy as np
import spacy
from textstat import flesch_reading_ease

from ..models.types import Message, CustomerProfile, ConversationMetadata


logger = logging.getLogger(__name__)


class FeatureEngineer:
    """Service d'extraction et transformation des features pour ML"""
    
    def __init__(self):
        self.nlp = None
        self._load_nlp_model()
        
        # Mots-clés spécifiques Free Mobile
        self.free_mobile_keywords = {
            "resiliation": [
                "résilier", "résiliation", "arrêter", "stopper", "annuler",
                "fermer", "clôturer", "partir", "changer d'opérateur"
            ],
            "probleme_technique": [
                "bug", "problème", "panne", "dysfonctionnement", "erreur",
                "ne marche pas", "ne fonctionne pas", "cassé", "défaillant"
            ],
            "facturation": [
                "facture", "facturation", "paiement", "prélèvement", "coût",
                "prix", "tarif", "montant", "euros", "€"
            ],
            "satisfaction": [
                "content", "satisfait", "parfait", "excellent", "super",
                "génial", "top", "merci", "bravo"
            ],
            "insatisfaction": [
                "mécontent", "insatisfait", "déçu", "nul", "mauvais",
                "horrible", "catastrophe", "scandale", "inadmissible"
            ],
            "urgence": [
                "urgent", "rapidement", "vite", "immédiatement", "tout de suite",
                "maintenant", "pressé", "critique", "important"
            ],
            "vente": [
                "offre", "promotion", "upgrade", "améliorer", "plus de data",
                "forfait supérieur", "option", "service supplémentaire"
            ]
        }
        
        # Patterns regex pour extraction d'entités
        self.patterns = {
            "phone_number": r"0[1-9](?:[0-9]{8})",
            "email": r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}",
            "amount": r"(\d+(?:,\d{2})?)\s*€",
            "date": r"\d{1,2}[/-]\d{1,2}[/-]\d{2,4}",
            "time": r"\d{1,2}h\d{2}|\d{1,2}:\d{2}"
        }
    
    def _load_nlp_model(self):
        """Chargement du modèle spaCy français"""
        try:
            self.nlp = spacy.load("fr_core_news_sm")
            logger.info("French NLP model loaded successfully")
        except OSError:
            logger.warning("French NLP model not found, using basic features only")
            self.nlp = None
    
    async def extract_features(
        self,
        messages: List[Message],
        customer_data: CustomerProfile,
        metadata: ConversationMetadata
    ) -> Dict[str, Any]:
        """Extraction complète des features pour ML"""
        
        # Features textuelles
        text_features = self._extract_text_features(messages)
        
        # Features client
        customer_features = self._extract_customer_features(customer_data)
        
        # Features conversationnelles
        conversation_features = self._extract_conversation_features(messages, metadata)
        
        # Features temporelles
        temporal_features = self._extract_temporal_features(messages)
        
        # Features linguistiques
        linguistic_features = self._extract_linguistic_features(messages)
        
        # Features business Free Mobile
        business_features = self._extract_business_features(messages, customer_data)
        
        return {
            "text_features": text_features,
            "customer_features": customer_features,
            "conversation_features": conversation_features,
            "temporal_features": temporal_features,
            "linguistic_features": linguistic_features,
            "business_features": business_features
        }
    
    def _extract_text_features(self, messages: List[Message]) -> Dict[str, Any]:
        """Extraction des features textuelles"""
        
        # Concaténation de tous les messages utilisateur
        user_messages = [msg.content for msg in messages if msg.sender == "user"]
        combined_text = " ".join(user_messages)
        
        # Features de base
        features = {
            "combined_text": combined_text,
            "total_length": len(combined_text),
            "word_count": len(combined_text.split()),
            "sentence_count": len([s for s in combined_text.split('.') if s.strip()]),
            "avg_word_length": np.mean([len(word) for word in combined_text.split()]) if combined_text else 0,
            "exclamation_count": combined_text.count('!'),
            "question_count": combined_text.count('?'),
            "uppercase_ratio": sum(1 for c in combined_text if c.isupper()) / len(combined_text) if combined_text else 0
        }
        
        # Détection de mots-clés Free Mobile
        keyword_scores = {}
        for category, keywords in self.free_mobile_keywords.items():
            score = sum(1 for keyword in keywords if keyword.lower() in combined_text.lower())
            keyword_scores[f"keywords_{category}"] = score
        
        features.update(keyword_scores)
        
        # Extraction d'entités avec regex
        entities = {}
        for entity_type, pattern in self.patterns.items():
            matches = re.findall(pattern, combined_text, re.IGNORECASE)
            entities[f"entity_{entity_type}_count"] = len(matches)
            entities[f"entity_{entity_type}_found"] = len(matches) > 0
        
        features.update(entities)
        
        return features
    
    def _extract_customer_features(self, customer_data: CustomerProfile) -> Dict[str, Any]:
        """Extraction des features client"""
        
        return {
            "tenure_months": customer_data.tenure_months,
            "monthly_revenue": customer_data.monthly_revenue,
            "support_tickets_count": customer_data.support_tickets_count,
            "satisfaction_score": customer_data.satisfaction_score or 3.0,
            "lifetime_value": customer_data.lifetime_value,
            "churn_risk_score": customer_data.churn_risk_score or 0.3,
            
            # Features dérivées
            "is_high_value": customer_data.monthly_revenue > 30.0,
            "is_new_customer": customer_data.tenure_months < 6,
            "is_frequent_caller": customer_data.support_tickets_count > 5,
            "revenue_per_month_tenure": customer_data.monthly_revenue / max(customer_data.tenure_months, 1),
            "tickets_per_month": customer_data.support_tickets_count / max(customer_data.tenure_months, 1),
            
            # Segmentation client
            "customer_segment": self._get_customer_segment(customer_data)
        }
    
    def _extract_conversation_features(
        self,
        messages: List[Message],
        metadata: ConversationMetadata
    ) -> Dict[str, Any]:
        """Extraction des features conversationnelles"""
        
        user_messages = [msg for msg in messages if msg.sender == "user"]
        bot_messages = [msg for msg in messages if msg.sender == "bot"]
        agent_messages = [msg for msg in messages if msg.sender == "agent"]
        
        features = {
            "total_messages": len(messages),
            "user_messages_count": len(user_messages),
            "bot_messages_count": len(bot_messages),
            "agent_messages_count": len(agent_messages),
            "channel": metadata.channel,
            "has_agent_intervention": len(agent_messages) > 0,
            "previous_interactions": metadata.previous_interactions,
            
            # Ratios
            "user_bot_ratio": len(user_messages) / max(len(bot_messages), 1),
            "messages_per_participant": len(messages) / 2,  # User + Bot/Agent
            
            # Durée estimée
            "estimated_duration_minutes": metadata.duration_minutes or self._estimate_duration(messages)
        }
        
        # Analyse des patterns de conversation
        if len(user_messages) > 1:
            response_times = self._calculate_response_times(user_messages)
            features.update({
                "avg_response_time_seconds": np.mean(response_times),
                "max_response_time_seconds": np.max(response_times),
                "response_time_variance": np.var(response_times)
            })
        
        return features
    
    def _extract_temporal_features(self, messages: List[Message]) -> Dict[str, Any]:
        """Extraction des features temporelles"""
        
        if not messages:
            return {}
        
        timestamps = [msg.timestamp for msg in messages]
        first_message = min(timestamps)
        last_message = max(timestamps)
        
        # Heure de la première interaction
        hour_of_day = first_message.hour
        day_of_week = first_message.weekday()
        
        features = {
            "hour_of_day": hour_of_day,
            "day_of_week": day_of_week,
            "is_weekend": day_of_week >= 5,
            "is_business_hours": 8 <= hour_of_day <= 18,
            "is_evening": hour_of_day >= 19,
            "is_night": hour_of_day <= 6 or hour_of_day >= 22,
            
            # Durée de conversation
            "conversation_duration_minutes": (last_message - first_message).total_seconds() / 60,
            
            # Patterns temporels
            "messages_per_minute": len(messages) / max((last_message - first_message).total_seconds() / 60, 1)
        }
        
        return features
    
    def _extract_linguistic_features(self, messages: List[Message]) -> Dict[str, Any]:
        """Extraction des features linguistiques avec spaCy"""
        
        user_text = " ".join([msg.content for msg in messages if msg.sender == "user"])
        
        if not user_text or not self.nlp:
            return {"linguistic_features_available": False}
        
        doc = self.nlp(user_text)
        
        # Analyse POS (Part-of-Speech)
        pos_counts = Counter([token.pos_ for token in doc])
        
        # Entités nommées
        entities = Counter([ent.label_ for ent in doc.ents])
        
        # Sentiment basique (polarité des mots)
        positive_words = ["bien", "bon", "super", "parfait", "content", "satisfait"]
        negative_words = ["mal", "mauvais", "nul", "problème", "bug", "mécontent"]
        
        positive_count = sum(1 for token in doc if token.text.lower() in positive_words)
        negative_count = sum(1 for token in doc if token.text.lower() in negative_words)
        
        features = {
            "linguistic_features_available": True,
            "token_count": len(doc),
            "unique_tokens": len(set([token.text.lower() for token in doc])),
            "lexical_diversity": len(set([token.text.lower() for token in doc])) / max(len(doc), 1),
            
            # POS features
            "noun_count": pos_counts.get("NOUN", 0),
            "verb_count": pos_counts.get("VERB", 0),
            "adj_count": pos_counts.get("ADJ", 0),
            "adv_count": pos_counts.get("ADV", 0),
            
            # Entités
            "person_entities": entities.get("PER", 0),
            "org_entities": entities.get("ORG", 0),
            "location_entities": entities.get("LOC", 0),
            
            # Sentiment basique
            "positive_words_count": positive_count,
            "negative_words_count": negative_count,
            "sentiment_polarity": (positive_count - negative_count) / max(positive_count + negative_count, 1),
            
            # Lisibilité
            "readability_score": self._calculate_readability(user_text)
        }
        
        return features
    
    def _extract_business_features(
        self,
        messages: List[Message],
        customer_data: CustomerProfile
    ) -> Dict[str, Any]:
        """Features spécifiques au business Free Mobile"""
        
        user_text = " ".join([msg.content for msg in messages if msg.sender == "user"]).lower()
        
        # Détection d'intentions business
        business_intents = {
            "wants_to_cancel": any(keyword in user_text for keyword in self.free_mobile_keywords["resiliation"]),
            "has_technical_issue": any(keyword in user_text for keyword in self.free_mobile_keywords["probleme_technique"]),
            "billing_inquiry": any(keyword in user_text for keyword in self.free_mobile_keywords["facturation"]),
            "shows_satisfaction": any(keyword in user_text for keyword in self.free_mobile_keywords["satisfaction"]),
            "shows_dissatisfaction": any(keyword in user_text for keyword in self.free_mobile_keywords["insatisfaction"]),
            "expresses_urgency": any(keyword in user_text for keyword in self.free_mobile_keywords["urgence"]),
            "sales_opportunity": any(keyword in user_text for keyword in self.free_mobile_keywords["vente"])
        }
        
        # Calcul du risque de churn basé sur le texte
        churn_indicators = sum([
            business_intents["wants_to_cancel"] * 0.8,
            business_intents["shows_dissatisfaction"] * 0.6,
            business_intents["has_technical_issue"] * 0.3,
            (customer_data.support_tickets_count > 3) * 0.4
        ])
        
        # Calcul du potentiel de vente
        sales_potential = sum([
            business_intents["sales_opportunity"] * 0.7,
            business_intents["shows_satisfaction"] * 0.5,
            (customer_data.monthly_revenue < 20) * 0.6,  # Clients low-cost = potentiel upgrade
            (customer_data.tenure_months > 12) * 0.3  # Clients fidèles = potentiel cross-sell
        ])
        
        features = {
            **business_intents,
            "text_based_churn_risk": min(churn_indicators, 1.0),
            "text_based_sales_potential": min(sales_potential, 1.0),
            
            # Urgence calculée
            "calculated_urgency_score": self._calculate_urgency_score(user_text, customer_data),
            
            # Valeur client contextuelle
            "contextual_customer_value": self._calculate_contextual_value(customer_data, business_intents)
        }
        
        return features
    
    def _get_customer_segment(self, customer_data: CustomerProfile) -> str:
        """Segmentation des clients Free Mobile"""
        
        if customer_data.monthly_revenue >= 50:
            return "PREMIUM"
        elif customer_data.monthly_revenue >= 20:
            return "STANDARD"
        elif customer_data.monthly_revenue >= 10:
            return "BASIC"
        else:
            return "BUDGET"
    
    def _estimate_duration(self, messages: List[Message]) -> int:
        """Estimation de la durée de conversation en minutes"""
        if len(messages) < 2:
            return 1
        
        # Estimation basée sur le nombre de messages (1 message ≈ 30 secondes)
        estimated_seconds = len(messages) * 30
        return max(1, estimated_seconds // 60)
    
    def _calculate_response_times(self, messages: List[Message]) -> List[float]:
        """Calcul des temps de réponse entre messages"""
        response_times = []
        
        for i in range(1, len(messages)):
            time_diff = (messages[i].timestamp - messages[i-1].timestamp).total_seconds()
            response_times.append(time_diff)
        
        return response_times
    
    def _calculate_readability(self, text: str) -> float:
        """Calcul de la lisibilité du texte"""
        try:
            return flesch_reading_ease(text)
        except:
            return 50.0  # Score moyen par défaut
    
    def _calculate_urgency_score(self, text: str, customer_data: CustomerProfile) -> float:
        """Calcul du score d'urgence"""
        urgency_score = 0.0
        
        # Mots d'urgence
        urgency_words = self.free_mobile_keywords["urgence"]
        urgency_count = sum(1 for word in urgency_words if word in text)
        urgency_score += urgency_count * 0.3
        
        # Ponctuation expressive
        urgency_score += text.count('!') * 0.1
        urgency_score += text.count('?') * 0.05
        
        # Contexte client
        if customer_data.monthly_revenue > 30:  # Client premium
            urgency_score += 0.2
        
        if customer_data.support_tickets_count > 5:  # Client problématique
            urgency_score += 0.3
        
        return min(urgency_score, 1.0)
    
    def _calculate_contextual_value(
        self,
        customer_data: CustomerProfile,
        business_intents: Dict[str, bool]
    ) -> float:
        """Calcul de la valeur contextuelle du client"""
        
        base_value = customer_data.lifetime_value
        
        # Multiplicateurs contextuels
        if business_intents["wants_to_cancel"]:
            base_value *= 2.0  # Valeur élevée si risque de perte
        
        if business_intents["sales_opportunity"]:
            base_value *= 1.5  # Potentiel de revenus additionnels
        
        if business_intents["shows_satisfaction"]:
            base_value *= 1.2  # Client satisfait = plus de valeur
        
        return base_value
    
    def create_feature_hash(self, features: Dict[str, Any]) -> str:
        """Création d'un hash des features pour le cache"""
        feature_string = str(sorted(features.items()))
        return hashlib.md5(feature_string.encode()).hexdigest()
