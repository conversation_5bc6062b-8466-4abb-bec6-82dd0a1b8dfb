"""
Configuration du service ML
Free Mobile Chatbot Dashboard - Classification intelligente
"""

import os
from typing import List, Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Configuration du service ML avec validation"""
    
    # Service Configuration
    SERVICE_NAME: str = "free-mobile-ml-service"
    VERSION: str = "1.0.0"
    DEBUG: bool = Field(default=False, env="DEBUG")
    
    # Server Configuration
    HOST: str = Field(default="0.0.0.0", env="ML_HOST")
    PORT: int = Field(default=5001, env="ML_PORT")
    WORKERS: int = Field(default=4, env="ML_WORKERS")
    
    # Model Configuration
    MODEL_PATH: str = Field(default="/models", env="MODEL_PATH")
    CACHE_SIZE: int = Field(default=1000, env="CACHE_SIZE")
    MAX_SEQUENCE_LENGTH: int = Field(default=512, env="MAX_SEQUENCE_LENGTH")
    
    # Performance Settings
    MAX_CONCURRENT_REQUESTS: int = Field(default=100, env="MAX_CONCURRENT_REQUESTS")
    REQUEST_TIMEOUT_SECONDS: int = Field(default=30, env="REQUEST_TIMEOUT_SECONDS")
    MODEL_WARMUP_ENABLED: bool = Field(default=True, env="MODEL_WARMUP_ENABLED")
    
    # Redis Configuration
    REDIS_URL: str = Field(default="redis://localhost:6379", env="REDIS_URL")
    REDIS_DB: int = Field(default=0, env="REDIS_DB")
    REDIS_PASSWORD: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    CACHE_TTL_SECONDS: int = Field(default=3600, env="CACHE_TTL_SECONDS")  # 1 hour
    
    # Database Configuration (TimescaleDB)
    DATABASE_URL: str = Field(
        default="postgresql://user:password@localhost:5432/analytics",
        env="DATABASE_URL"
    )
    
    # MongoDB Configuration (pour lecture des conversations)
    MONGODB_URL: str = Field(
        default="mongodb://localhost:27017/free-mobile-chatbot",
        env="MONGODB_URL"
    )
    
    # Model Versions et Paths
    CAMEMBERT_MODEL: str = Field(
        default="camembert/camembert-base-ccnet",
        env="CAMEMBERT_MODEL"
    )
    SENTIMENT_MODEL: str = Field(
        default="nlptown/bert-base-multilingual-uncased-sentiment",
        env="SENTIMENT_MODEL"
    )
    
    # Business Logic Configuration
    HIGH_PRIORITY_THRESHOLD: float = Field(default=0.8, env="HIGH_PRIORITY_THRESHOLD")
    CHURN_RISK_THRESHOLD: float = Field(default=0.7, env="CHURN_RISK_THRESHOLD")
    OPPORTUNITY_THRESHOLD: float = Field(default=0.6, env="OPPORTUNITY_THRESHOLD")
    
    # Free Mobile Business Rules
    AVERAGE_CUSTOMER_LTV: float = Field(default=480.0, env="AVERAGE_CUSTOMER_LTV")  # 24 mois * 20€
    HIGH_VALUE_CUSTOMER_THRESHOLD: float = Field(default=50.0, env="HIGH_VALUE_CUSTOMER_THRESHOLD")
    CHURN_COST_MULTIPLIER: float = Field(default=3.0, env="CHURN_COST_MULTIPLIER")
    
    # Monitoring Configuration
    PROMETHEUS_PORT: int = Field(default=8000, env="PROMETHEUS_PORT")
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    SENTRY_DSN: Optional[str] = Field(default=None, env="SENTRY_DSN")
    
    # Security
    API_KEY_HEADER: str = Field(default="X-API-Key", env="API_KEY_HEADER")
    ALLOWED_ORIGINS: List[str] = Field(
        default=["http://localhost:3001", "http://localhost:5000"],
        env="ALLOWED_ORIGINS"
    )
    
    # Feature Flags
    ENABLE_CACHING: bool = Field(default=True, env="ENABLE_CACHING")
    ENABLE_METRICS: bool = Field(default=True, env="ENABLE_METRICS")
    ENABLE_ASYNC_PROCESSING: bool = Field(default=True, env="ENABLE_ASYNC_PROCESSING")
    ENABLE_MODEL_ENSEMBLE: bool = Field(default=False, env="ENABLE_MODEL_ENSEMBLE")
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Instance globale des settings
settings = Settings()


# Configuration des modèles ML
ML_MODELS_CONFIG = {
    "intent_classifier": {
        "model_name": "camembert-intent-classifier",
        "version": "1.0.0",
        "path": f"{settings.MODEL_PATH}/intent_classifier",
        "max_length": 512,
        "batch_size": 32,
        "categories": [
            "VENTE_OPPORTUNITE",
            "RESILIATION_CRITIQUE", 
            "SUPPORT_URGENT",
            "RECLAMATION",
            "INFO_SIMPLE"
        ]
    },
    "sentiment_analyzer": {
        "model_name": "multilingual-sentiment",
        "version": "1.0.0",
        "path": f"{settings.MODEL_PATH}/sentiment_analyzer",
        "max_length": 256,
        "batch_size": 64
    },
    "churn_predictor": {
        "model_name": "churn-predictor-v2",
        "version": "2.1.0",
        "path": f"{settings.MODEL_PATH}/churn_predictor",
        "features": [
            "tenure_months",
            "support_tickets_count",
            "satisfaction_score",
            "monthly_revenue",
            "recent_sentiment_trend"
        ]
    },
    "opportunity_scorer": {
        "model_name": "opportunity-scorer",
        "version": "1.5.0",
        "path": f"{settings.MODEL_PATH}/opportunity_scorer",
        "min_confidence": 0.6,
        "revenue_categories": {
            "UPGRADE_MOBILE": {"min_value": 5.0, "max_value": 15.0},
            "ADD_OPTION": {"min_value": 2.0, "max_value": 8.0},
            "FAMILY_PLAN": {"min_value": 20.0, "max_value": 60.0}
        }
    }
}


# Configuration Redis pour différents types de cache
REDIS_KEYS = {
    "classification_cache": "ml:classification:{conversation_id}",
    "model_cache": "ml:model:{model_name}:cache",
    "metrics": "ml:metrics:{date}",
    "queue": "ml:queue:priority",
    "health": "ml:health:status"
}


# Configuration logging structuré
LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "json": {
            "format": "%(asctime)s %(name)s %(levelname)s %(message)s",
            "class": "pythonjsonlogger.jsonlogger.JsonFormatter"
        }
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "formatter": "json",
            "level": settings.LOG_LEVEL
        }
    },
    "root": {
        "level": settings.LOG_LEVEL,
        "handlers": ["console"]
    },
    "loggers": {
        "uvicorn": {"level": "INFO"},
        "transformers": {"level": "WARNING"},
        "torch": {"level": "WARNING"}
    }
}
