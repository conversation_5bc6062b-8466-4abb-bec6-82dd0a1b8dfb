"""
Simple ML Service for Free Mobile Chatbot Dashboard
Mock implementation for development and testing
"""

from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, List, Optional
import random
import time
from datetime import datetime

# Initialize FastAPI app
app = FastAPI(
    title="Free Mobile ML Service",
    description="ML Intelligence Service for Chatbot Dashboard",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class ConversationInput(BaseModel):
    conversation_id: str
    customer_id: str
    messages: List[Dict]
    metadata: Optional[Dict] = {}

class ClassificationResult(BaseModel):
    conversation_id: str
    category: str
    priority_score: int
    confidence: float
    business_impact: Dict
    sentiment: Dict
    processing_time_ms: int

class HealthResponse(BaseModel):
    status: str
    timestamp: str
    version: str
    models_loaded: bool

# Mock data for responses
CATEGORIES = [
    "VENTE_OPPORTUNITE",
    "RESILIATION_CRITIQUE", 
    "SUPPORT_URGENT",
    "RECLAMATION",
    "INFO_SIMPLE"
]

@app.get("/")
async def root():
    return {"message": "Free Mobile ML Service is running"}

@app.get("/health", response_model=HealthResponse)
async def health_check():
    return HealthResponse(
        status="healthy",
        timestamp=datetime.now().isoformat(),
        version="1.0.0",
        models_loaded=True
    )

@app.post("/classify", response_model=ClassificationResult)
async def classify_conversation(conversation: ConversationInput):
    """
    Mock classification endpoint that returns realistic ML results
    """
    start_time = time.time()
    
    # Simulate processing time
    await asyncio.sleep(random.uniform(0.05, 0.2))
    
    # Mock classification logic
    category = random.choice(CATEGORIES)
    priority_score = random.randint(1, 100)
    confidence = random.uniform(0.7, 0.95)
    
    # Generate business impact based on category
    business_impact = generate_business_impact(category, priority_score)
    
    # Generate sentiment analysis
    sentiment = {
        "score": random.uniform(-0.5, 0.8),
        "trend": random.choice(["improving", "stable", "declining"]),
        "confidence": random.uniform(0.6, 0.9)
    }
    
    processing_time = int((time.time() - start_time) * 1000)
    
    return ClassificationResult(
        conversation_id=conversation.conversation_id,
        category=category,
        priority_score=priority_score,
        confidence=confidence,
        business_impact=business_impact,
        sentiment=sentiment,
        processing_time_ms=processing_time
    )

@app.get("/metrics")
async def get_metrics():
    """
    Mock metrics endpoint
    """
    return {
        "performance": {
            "processing_time_avg": random.randint(80, 150),
            "throughput": random.randint(50, 200),
            "cache_hit_rate": random.uniform(0.7, 0.9),
            "memory_usage": random.randint(200, 800)
        },
        "ml_metrics": {
            "accuracy": random.uniform(0.85, 0.95),
            "precision": random.uniform(0.80, 0.92),
            "recall": random.uniform(0.82, 0.94),
            "f1_score": random.uniform(0.83, 0.93)
        },
        "business_impact": {
            "revenue_at_risk": random.randint(10000, 50000),
            "opportunities_identified": random.randint(5, 25),
            "churn_prevented": random.randint(2, 15),
            "upsells_generated": random.randint(1, 10)
        }
    }

@app.get("/models/status")
async def get_models_status():
    """
    Mock model status endpoint
    """
    return {
        "models": {
            "intent_classifier": {
                "status": "loaded",
                "version": "1.0.0",
                "accuracy": 0.89,
                "last_updated": "2024-01-15T10:00:00Z"
            },
            "sentiment_analyzer": {
                "status": "loaded", 
                "version": "1.0.0",
                "accuracy": 0.92,
                "last_updated": "2024-01-15T10:00:00Z"
            },
            "churn_predictor": {
                "status": "loaded",
                "version": "1.0.0", 
                "accuracy": 0.87,
                "last_updated": "2024-01-15T10:00:00Z"
            }
        },
        "system": {
            "gpu_available": False,
            "memory_usage": "45%",
            "cpu_usage": "23%"
        }
    }

def generate_business_impact(category: str, priority_score: int) -> Dict:
    """Generate realistic business impact based on category"""
    
    base_impact = {
        "revenue_at_risk": 0,
        "opportunity_value": 0,
        "churn_probability": random.uniform(0.1, 0.3),
        "upsell_probability": random.uniform(0.1, 0.4)
    }
    
    if category == "RESILIATION_CRITIQUE":
        base_impact["revenue_at_risk"] = random.randint(500, 2000)
        base_impact["churn_probability"] = random.uniform(0.7, 0.95)
        
    elif category == "VENTE_OPPORTUNITE":
        base_impact["opportunity_value"] = random.randint(100, 1500)
        base_impact["upsell_probability"] = random.uniform(0.6, 0.9)
        
    elif category == "SUPPORT_URGENT":
        base_impact["revenue_at_risk"] = random.randint(100, 800)
        
    # Adjust based on priority score
    if priority_score > 80:
        base_impact["revenue_at_risk"] = int(base_impact["revenue_at_risk"] * 1.5)
        base_impact["opportunity_value"] = int(base_impact["opportunity_value"] * 1.3)
    
    return base_impact

# Import asyncio for sleep function
import asyncio

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=5001)
