-- Script d'initialisation TimescaleDB
-- Free Mobile Chatbot Dashboard - Analytics et métriques ML
-- Performance: Optimisé pour les séries temporelles et analytics

-- Activation de l'extension TimescaleDB
CREATE EXTENSION IF NOT EXISTS timescaledb;

-- Table des métriques ML en temps réel
CREATE TABLE IF NOT EXISTS ml_metrics (
    time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    metric_name VARCHAR(100) NOT NULL,
    metric_value DOUBLE PRECISION NOT NULL,
    labels JSONB,
    service_instance VARCHAR(50),
    INDEX (metric_name, time DESC)
);

-- Conversion en hypertable pour les performances
SELECT create_hypertable('ml_metrics', 'time', if_not_exists => TRUE);

-- Table des classifications ML
CREATE TABLE IF NOT EXISTS ml_classifications (
    time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    conversation_id VARCHAR(100) NOT NULL,
    customer_id VARCHAR(100),
    category VARCHAR(50) NOT NULL,
    priority_score INTEGER NOT NULL CHECK (priority_score >= 0 AND priority_score <= 100),
    confidence DOUBLE PRECISION NOT NULL CHECK (confidence >= 0.0 AND confidence <= 1.0),
    processing_time_ms DOUBLE PRECISION NOT NULL,
    model_version VARCHAR(50),
    business_impact JSONB,
    sentiment_analysis JSONB,
    recommended_actions JSONB,
    features_used TEXT[],
    cached BOOLEAN DEFAULT FALSE,
    INDEX (conversation_id, time DESC),
    INDEX (customer_id, time DESC),
    INDEX (category, time DESC),
    INDEX (priority_score DESC, time DESC)
);

SELECT create_hypertable('ml_classifications', 'time', if_not_exists => TRUE);

-- Table des performances des modèles
CREATE TABLE IF NOT EXISTS model_performance (
    time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    model_name VARCHAR(100) NOT NULL,
    model_version VARCHAR(50) NOT NULL,
    accuracy DOUBLE PRECISION,
    precision_score DOUBLE PRECISION,
    recall DOUBLE PRECISION,
    f1_score DOUBLE PRECISION,
    avg_latency_ms DOUBLE PRECISION,
    throughput_per_second DOUBLE PRECISION,
    total_predictions INTEGER DEFAULT 0,
    INDEX (model_name, time DESC)
);

SELECT create_hypertable('model_performance', 'time', if_not_exists => TRUE);

-- Table des événements business
CREATE TABLE IF NOT EXISTS business_events (
    time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    event_type VARCHAR(50) NOT NULL, -- 'churn_prediction', 'opportunity_identified', 'escalation'
    conversation_id VARCHAR(100),
    customer_id VARCHAR(100),
    event_data JSONB NOT NULL,
    revenue_impact DOUBLE PRECISION DEFAULT 0,
    confidence DOUBLE PRECISION,
    resolved BOOLEAN DEFAULT FALSE,
    resolved_at TIMESTAMPTZ,
    resolution_outcome VARCHAR(100),
    INDEX (event_type, time DESC),
    INDEX (customer_id, time DESC),
    INDEX (resolved, time DESC)
);

SELECT create_hypertable('business_events', 'time', if_not_exists => TRUE);

-- Table des métriques de cache
CREATE TABLE IF NOT EXISTS cache_metrics (
    time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    cache_type VARCHAR(50) NOT NULL,
    operation VARCHAR(20) NOT NULL, -- 'hit', 'miss', 'set', 'delete'
    key_pattern VARCHAR(100),
    response_time_ms DOUBLE PRECISION,
    data_size_bytes INTEGER,
    INDEX (cache_type, operation, time DESC)
);

SELECT create_hypertable('cache_metrics', 'time', if_not_exists => TRUE);

-- Table des alertes système
CREATE TABLE IF NOT EXISTS system_alerts (
    time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    alert_type VARCHAR(50) NOT NULL,
    severity VARCHAR(20) NOT NULL, -- 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL'
    service_name VARCHAR(50),
    message TEXT NOT NULL,
    details JSONB,
    resolved BOOLEAN DEFAULT FALSE,
    resolved_at TIMESTAMPTZ,
    acknowledged_by VARCHAR(100),
    INDEX (alert_type, severity, time DESC),
    INDEX (resolved, time DESC)
);

SELECT create_hypertable('system_alerts', 'time', if_not_exists => TRUE);

-- Vues matérialisées pour les dashboards

-- Vue des métriques ML agrégées par heure
CREATE MATERIALIZED VIEW IF NOT EXISTS ml_metrics_hourly AS
SELECT 
    time_bucket('1 hour', time) AS hour,
    metric_name,
    AVG(metric_value) AS avg_value,
    MAX(metric_value) AS max_value,
    MIN(metric_value) AS min_value,
    COUNT(*) AS sample_count,
    labels
FROM ml_metrics
GROUP BY hour, metric_name, labels
ORDER BY hour DESC;

-- Vue des classifications par catégorie et heure
CREATE MATERIALIZED VIEW IF NOT EXISTS classifications_hourly AS
SELECT 
    time_bucket('1 hour', time) AS hour,
    category,
    COUNT(*) AS total_classifications,
    AVG(confidence) AS avg_confidence,
    AVG(priority_score) AS avg_priority,
    AVG(processing_time_ms) AS avg_processing_time,
    COUNT(*) FILTER (WHERE cached = TRUE) AS cached_count
FROM ml_classifications
GROUP BY hour, category
ORDER BY hour DESC, total_classifications DESC;

-- Vue des performances des modèles
CREATE MATERIALIZED VIEW IF NOT EXISTS model_performance_daily AS
SELECT 
    time_bucket('1 day', time) AS day,
    model_name,
    model_version,
    AVG(accuracy) AS avg_accuracy,
    AVG(precision_score) AS avg_precision,
    AVG(recall) AS avg_recall,
    AVG(f1_score) AS avg_f1,
    AVG(avg_latency_ms) AS avg_latency,
    AVG(throughput_per_second) AS avg_throughput,
    SUM(total_predictions) AS total_predictions
FROM model_performance
GROUP BY day, model_name, model_version
ORDER BY day DESC;

-- Vue des événements business critiques
CREATE MATERIALIZED VIEW IF NOT EXISTS critical_business_events AS
SELECT 
    time_bucket('1 hour', time) AS hour,
    event_type,
    COUNT(*) AS event_count,
    SUM(revenue_impact) AS total_revenue_impact,
    AVG(confidence) AS avg_confidence,
    COUNT(*) FILTER (WHERE resolved = TRUE) AS resolved_count,
    COUNT(*) FILTER (WHERE resolved = FALSE) AS pending_count
FROM business_events
WHERE confidence > 0.7 OR revenue_impact > 50
GROUP BY hour, event_type
ORDER BY hour DESC, total_revenue_impact DESC;

-- Politiques de rétention des données

-- Rétention de 90 jours pour les métriques détaillées
SELECT add_retention_policy('ml_metrics', INTERVAL '90 days', if_not_exists => TRUE);
SELECT add_retention_policy('cache_metrics', INTERVAL '30 days', if_not_exists => TRUE);

-- Rétention de 1 an pour les classifications et événements business
SELECT add_retention_policy('ml_classifications', INTERVAL '1 year', if_not_exists => TRUE);
SELECT add_retention_policy('business_events', INTERVAL '1 year', if_not_exists => TRUE);

-- Rétention de 2 ans pour les performances des modèles
SELECT add_retention_policy('model_performance', INTERVAL '2 years', if_not_exists => TRUE);

-- Rétention de 6 mois pour les alertes système
SELECT add_retention_policy('system_alerts', INTERVAL '6 months', if_not_exists => TRUE);

-- Politiques de compression pour optimiser le stockage

-- Compression après 7 jours pour les métriques
SELECT add_compression_policy('ml_metrics', INTERVAL '7 days', if_not_exists => TRUE);
SELECT add_compression_policy('cache_metrics', INTERVAL '3 days', if_not_exists => TRUE);

-- Compression après 30 jours pour les classifications
SELECT add_compression_policy('ml_classifications', INTERVAL '30 days', if_not_exists => TRUE);

-- Index optimisés pour les requêtes fréquentes

-- Index pour les requêtes de dashboard temps réel
CREATE INDEX IF NOT EXISTS idx_ml_metrics_recent 
ON ml_metrics (metric_name, time DESC) 
WHERE time > NOW() - INTERVAL '24 hours';

-- Index pour les recherches par conversation
CREATE INDEX IF NOT EXISTS idx_classifications_conversation_recent
ON ml_classifications (conversation_id, time DESC)
WHERE time > NOW() - INTERVAL '7 days';

-- Index pour les événements business non résolus
CREATE INDEX IF NOT EXISTS idx_business_events_unresolved
ON business_events (event_type, time DESC)
WHERE resolved = FALSE;

-- Fonctions utilitaires

-- Fonction pour calculer les métriques de performance en temps réel
CREATE OR REPLACE FUNCTION get_ml_performance_summary(
    start_time TIMESTAMPTZ DEFAULT NOW() - INTERVAL '1 hour',
    end_time TIMESTAMPTZ DEFAULT NOW()
)
RETURNS TABLE (
    total_classifications BIGINT,
    avg_processing_time DOUBLE PRECISION,
    avg_confidence DOUBLE PRECISION,
    cache_hit_rate DOUBLE PRECISION,
    top_category TEXT
) AS $$
BEGIN
    RETURN QUERY
    WITH classification_stats AS (
        SELECT 
            COUNT(*) as total,
            AVG(processing_time_ms) as avg_time,
            AVG(confidence) as avg_conf,
            COUNT(*) FILTER (WHERE cached = TRUE) as cached_count,
            MODE() WITHIN GROUP (ORDER BY category) as top_cat
        FROM ml_classifications
        WHERE time BETWEEN start_time AND end_time
    )
    SELECT 
        cs.total,
        cs.avg_time,
        cs.avg_conf,
        CASE 
            WHEN cs.total > 0 THEN cs.cached_count::DOUBLE PRECISION / cs.total::DOUBLE PRECISION
            ELSE 0.0
        END as hit_rate,
        cs.top_cat
    FROM classification_stats cs;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour identifier les conversations à haute priorité
CREATE OR REPLACE FUNCTION get_high_priority_conversations(
    min_priority INTEGER DEFAULT 80,
    hours_back INTEGER DEFAULT 24
)
RETURNS TABLE (
    conversation_id VARCHAR(100),
    customer_id VARCHAR(100),
    category VARCHAR(50),
    priority_score INTEGER,
    confidence DOUBLE PRECISION,
    revenue_impact DOUBLE PRECISION,
    classification_time TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        c.conversation_id,
        c.customer_id,
        c.category,
        c.priority_score,
        c.confidence,
        (c.business_impact->>'revenue_at_risk')::DOUBLE PRECISION +
        (c.business_impact->>'opportunity_value')::DOUBLE PRECISION as revenue_impact,
        c.time
    FROM ml_classifications c
    WHERE c.priority_score >= min_priority
      AND c.time > NOW() - (hours_back || ' hours')::INTERVAL
    ORDER BY c.priority_score DESC, c.time DESC;
END;
$$ LANGUAGE plpgsql;

-- Triggers pour maintenir les vues matérialisées

-- Rafraîchissement automatique des vues matérialisées
CREATE OR REPLACE FUNCTION refresh_materialized_views()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW ml_metrics_hourly;
    REFRESH MATERIALIZED VIEW classifications_hourly;
    REFRESH MATERIALIZED VIEW model_performance_daily;
    REFRESH MATERIALIZED VIEW critical_business_events;
END;
$$ LANGUAGE plpgsql;

-- Programmation du rafraîchissement toutes les 15 minutes
-- (Nécessite l'extension pg_cron en production)
-- SELECT cron.schedule('refresh-ml-views', '*/15 * * * *', 'SELECT refresh_materialized_views();');

-- Insertion de données de test pour le développement
INSERT INTO ml_metrics (metric_name, metric_value, labels, service_instance) VALUES
('classification_requests_total', 1250, '{"status": "success", "category": "SUPPORT_URGENT"}', 'ml-service-1'),
('classification_duration_seconds', 0.15, '{"model_type": "ensemble"}', 'ml-service-1'),
('cache_hit_rate', 0.85, '{"cache_type": "classification"}', 'ml-service-1'),
('model_accuracy', 0.94, '{"model_name": "intent_classifier", "version": "1.0.0"}', 'ml-service-1');

-- Message de confirmation
DO $$
BEGIN
    RAISE NOTICE 'TimescaleDB initialization completed successfully for Free Mobile ML Service';
    RAISE NOTICE 'Created tables: ml_metrics, ml_classifications, model_performance, business_events, cache_metrics, system_alerts';
    RAISE NOTICE 'Created materialized views: ml_metrics_hourly, classifications_hourly, model_performance_daily, critical_business_events';
    RAISE NOTICE 'Configured retention and compression policies';
    RAISE NOTICE 'Ready for ML analytics and monitoring';
END $$;
