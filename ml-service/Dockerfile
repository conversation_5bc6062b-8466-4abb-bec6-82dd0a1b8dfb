# Dockerfile pour le service ML
# Free Mobile Chatbot Dashboard - Classification intelligente
# Multi-stage build pour optimiser la taille de l'image

# Stage 1: Base avec Python et dépendances système
FROM python:3.11-slim as base

# Métadonnées
LABEL maintainer="Free Mobile Chatbot Team"
LABEL version="1.0.0"
LABEL description="ML Classification Service for Free Mobile Chatbot Dashboard"

# Variables d'environnement
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    DEBIAN_FRONTEND=noninteractive

# Installation des dépendances système
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

# Création de l'utilisateur non-root
RUN groupadd -r mlservice && useradd -r -g mlservice mlservice

# Répertoire de travail
WORKDIR /app

# Stage 2: Installation des dépendances Python
FROM base as dependencies

# Copie des fichiers de dépendances
COPY requirements.txt .

# Installation des dépendances Python
RUN pip install --upgrade pip setuptools wheel && \
    pip install -r requirements.txt

# Téléchargement du modèle spaCy français
RUN python -m spacy download fr_core_news_sm

# Stage 3: Image de développement
FROM dependencies as development

# Installation des outils de développement
RUN pip install pytest pytest-asyncio pytest-cov black isort flake8

# Copie du code source
COPY . .

# Changement de propriétaire
RUN chown -R mlservice:mlservice /app

# Utilisateur non-root
USER mlservice

# Port d'exposition
EXPOSE 5001 8000

# Commande par défaut pour le développement
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "5001", "--reload"]

# Stage 4: Image de production
FROM dependencies as production

# Copie uniquement du code nécessaire
COPY app/ ./app/
COPY scripts/ ./scripts/

# Création des répertoires nécessaires
RUN mkdir -p /models /logs /tmp && \
    chown -R mlservice:mlservice /app /models /logs /tmp

# Utilisateur non-root
USER mlservice

# Ports d'exposition
EXPOSE 5001 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:5001/health || exit 1

# Commande par défaut pour la production
CMD ["gunicorn", "app.main:app", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:5001", "--timeout", "120", "--keep-alive", "5", "--max-requests", "1000", "--max-requests-jitter", "100"]

# Stage 5: Image optimisée pour GPU (optionnel)
FROM nvidia/cuda:11.8-runtime-ubuntu20.04 as gpu-production

# Variables d'environnement CUDA
ENV NVIDIA_VISIBLE_DEVICES=all \
    NVIDIA_DRIVER_CAPABILITIES=compute,utility

# Installation de Python et dépendances
RUN apt-get update && apt-get install -y \
    python3.11 \
    python3.11-pip \
    python3.11-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Lien symbolique pour python
RUN ln -s /usr/bin/python3.11 /usr/bin/python

# Copie des dépendances depuis l'étape précédente
COPY --from=dependencies /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=dependencies /usr/local/bin /usr/local/bin

# Copie du code
COPY app/ /app/app/
COPY scripts/ /app/scripts/

WORKDIR /app

# Utilisateur non-root
RUN groupadd -r mlservice && useradd -r -g mlservice mlservice
RUN mkdir -p /models /logs && chown -R mlservice:mlservice /app /models /logs
USER mlservice

# Ports
EXPOSE 5001 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:5001/health || exit 1

# Commande GPU
CMD ["gunicorn", "app.main:app", "-w", "2", "-k", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:5001", "--timeout", "180"]
