# ML Service Dependencies - Free Mobile Chatbot Dashboard # Performance: Classification < 200ms, Throughput: 1000+ req/sec # FastAPI Framework fastapi==0.104.1 uvicorn[standard]==0.24.0 pydantic==2.5.0 python-multipart==0.0.6 # Machine Learning Core torch==2.1.1 transformers==4.35.2 tokenizers==0.15.0 numpy==1.24.3 scikit-learn==1.3.2 pandas==2.1.4 # French NLP Models sentence-transformers==2.2.2 spacy==3.7.2 fr-core-news-sm @ https://github.com/explosion/spacy-models/releases/download/fr_core_news_sm-3.7.0/fr_core_news_sm-3.7.0-py3-none-any.whl # Redis for Caching & Queuing redis==5.0.1 aioredis==2.0.1 # Database Connections motor==3.3.2 # Async MongoDB asyncpg==0.29.0 # TimescaleDB # HTTP Client httpx==0.25.2 aiohttp==3.9.1 # Monitoring & Logging prometheus-client==0.19.0 structlog==23.2.0 sentry-sdk[fastapi]==1.38.0 # Performance & Optimization orjson==3.9.10 # Fast JSON asyncio-throttle==1.0.2 cachetools==5.3.2 # Development & Testing pytest==7.4.3 pytest-asyncio==0.21.1 pytest-cov==4.1.0 httpx==0.25.2 # For testing # Production gunicorn==21.2.0