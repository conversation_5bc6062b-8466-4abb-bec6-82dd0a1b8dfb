# =============================================
# 🤖 ML SERVICE - PRODUCTION DOCKERFILE
# Free Mobile Chatbot Dashboard - Phase 4 Production
# Multi-stage build for optimized production image
# =============================================

# Stage 1: Base Python image with system dependencies
FROM python:3.11-slim as base

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    DEBIAN_FRONTEND=noninteractive

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user for security
RUN groupadd -r mlservice && useradd -r -g mlservice mlservice

# Stage 2: Dependencies installation
FROM base as dependencies

# Copy requirements first for better caching
COPY requirements.txt requirements-prod.txt ./

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements-prod.txt

# Stage 3: Application build
FROM dependencies as builder

# Set working directory
WORKDIR /app

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p /app/models /app/logs /app/cache

# Download and prepare ML models (if needed)
RUN python scripts/download_models.py --production

# Stage 4: Production runtime
FROM python:3.11-slim as production

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    ENVIRONMENT=production \
    LOG_LEVEL=INFO \
    WORKERS=4 \
    MAX_REQUESTS=1000 \
    MAX_REQUESTS_JITTER=100 \
    TIMEOUT=30 \
    KEEPALIVE=5

# Install runtime dependencies only
RUN apt-get update && apt-get install -y \
    libpq5 \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user
RUN groupadd -r mlservice && useradd -r -g mlservice mlservice

# Set working directory
WORKDIR /app

# Copy Python dependencies from builder stage
COPY --from=dependencies /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=dependencies /usr/local/bin /usr/local/bin

# Copy application and models from builder stage
COPY --from=builder --chown=mlservice:mlservice /app .

# Create necessary directories with proper permissions
RUN mkdir -p /app/logs /app/cache /app/tmp \
    && chown -R mlservice:mlservice /app \
    && chmod -R 755 /app

# Switch to non-root user
USER mlservice

# Expose port
EXPOSE 5001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:5001/health || exit 1

# Production startup command with Gunicorn
CMD ["gunicorn", \
     "--bind", "0.0.0.0:5001", \
     "--workers", "${WORKERS}", \
     "--worker-class", "uvicorn.workers.UvicornWorker", \
     "--max-requests", "${MAX_REQUESTS}", \
     "--max-requests-jitter", "${MAX_REQUESTS_JITTER}", \
     "--timeout", "${TIMEOUT}", \
     "--keepalive", "${KEEPALIVE}", \
     "--access-logfile", "/app/logs/access.log", \
     "--error-logfile", "/app/logs/error.log", \
     "--log-level", "${LOG_LEVEL}", \
     "--preload", \
     "app.main:app"]
