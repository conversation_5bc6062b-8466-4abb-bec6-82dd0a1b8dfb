# 🔒 SECURE PRODUCTION ENVIRONMENT CONFIGURATION
# ⚠️  CRITICAL: Replace all placeholder values before deployment
# 📋 Use: cp .env.production.secure .env.production && nano .env.production

# ================================
# 🌍 ENVIRONMENT CONFIGURATION
# ================================
NODE_ENV=production
PORT=5000
FRONTEND_PORT=3001
FRONTEND_DOMAIN=chatbotrncp.vercel.app
BACKEND_DOMAIN=api.chatbotrncp.vercel.app

# ================================
# 🔐 SECURITY CREDENTIALS
# ================================
# Generate with: openssl rand -base64 64
JWT_SECRET=REPLACE_WITH_SECURE_JWT_SECRET_64_CHARS_MIN
JWT_EXPIRE=7d
JWT_REFRESH_SECRET=REPLACE_WITH_SECURE_REFRESH_SECRET_64_CHARS_MIN
JWT_REFRESH_EXPIRE=30d

# Generate with: openssl rand -base64 64
SESSION_SECRET=REPLACE_WITH_SECURE_SESSION_SECRET_64_CHARS_MIN
SESSION_NAME=freemobile.chatbot.sid
SESSION_SECURE=true
SESSION_HTTP_ONLY=true
SESSION_SAME_SITE=strict
SESSION_MAX_AGE=86400000

# ================================
# 🗄️ DATABASE CONFIGURATION
# ================================
# Generate with: openssl rand -base64 32
MONGO_USERNAME=freemobile_prod
MONGO_PASSWORD=REPLACE_WITH_SECURE_MONGO_PASSWORD_32_CHARS_MIN
MONGO_DB_NAME=freemobile-chatbot-prod
MONGO_HOST=mongodb-cluster.example.com
MONGO_PORT=27017

# Secure MongoDB URI with SSL
MONGODB_URI=mongodb://${MONGO_USERNAME}:${MONGO_PASSWORD}@${MONGO_HOST}:${MONGO_PORT}/${MONGO_DB_NAME}?authSource=admin&ssl=true&sslValidate=true&retryWrites=true

# SSL Certificate paths (if using custom certificates)
MONGO_SSL_CERT_PATH=/etc/ssl/certs/mongodb-client.crt
MONGO_SSL_KEY_PATH=/etc/ssl/private/mongodb-client.key
MONGO_SSL_CA_PATH=/etc/ssl/certs/mongodb-ca.crt

# ================================
# 🔥 REDIS CACHE CONFIGURATION
# ================================
# Generate with: openssl rand -base64 32
REDIS_PASSWORD=REPLACE_WITH_SECURE_REDIS_PASSWORD_32_CHARS_MIN
REDIS_HOST=redis-cluster.example.com
REDIS_PORT=6380
REDIS_DB=0
REDIS_TLS_ENABLED=true

# Secure Redis URL with SSL
REDIS_URL=rediss://:${REDIS_PASSWORD}@${REDIS_HOST}:${REDIS_PORT}/${REDIS_DB}

# Redis SSL Certificate paths
REDIS_TLS_CERT_PATH=/etc/ssl/certs/redis-client.crt
REDIS_TLS_KEY_PATH=/etc/ssl/private/redis-client.key
REDIS_TLS_CA_PATH=/etc/ssl/certs/redis-ca.crt

# ================================
# 🤖 AI SERVICES CONFIGURATION
# ================================
# Obtain from: https://platform.openai.com/api-keys
OPENAI_API_KEY=sk-proj-REPLACE_WITH_REAL_OPENAI_API_KEY
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=150
OPENAI_TEMPERATURE=0.7
OPENAI_TIMEOUT=30000

# Rasa NLP Configuration
RASA_SERVER_URL=https://rasa.chatbotrncp.internal:5005
RASA_MODEL_PATH=/app/models
RASA_WEBHOOK_URL=https://api.chatbotrncp.vercel.app/api/webhooks/rasa
RASA_TOKEN=REPLACE_WITH_SECURE_RASA_TOKEN

# ================================
# 📧 EMAIL CONFIGURATION
# ================================
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=REPLACE_WITH_SECURE_EMAIL_APP_PASSWORD
EMAIL_FROM=<EMAIL>
EMAIL_SECURE=true
EMAIL_TLS_ENABLED=true

# ================================
# 🔒 API SECURITY
# ================================
# Generate with: openssl rand -hex 32
API_KEY_ADMIN=REPLACE_WITH_SECURE_ADMIN_API_KEY_64_CHARS
API_KEY_AGENT=REPLACE_WITH_SECURE_AGENT_API_KEY_64_CHARS
API_KEY_PUBLIC=REPLACE_WITH_SECURE_PUBLIC_API_KEY_64_CHARS

# Comma-separated list of valid API keys
VALID_API_KEYS=${API_KEY_ADMIN},${API_KEY_AGENT},${API_KEY_PUBLIC}

# ================================
# 🛡️ RATE LIMITING
# ================================
API_RATE_LIMIT_ENABLED=true
API_RATE_LIMIT_REQUESTS=100
API_RATE_LIMIT_WINDOW=900000
API_RATE_LIMIT_SKIP_SUCCESSFUL=false
API_RATE_LIMIT_SKIP_FAILED=false

# Role-based rate limits (requests per 15 minutes)
RATE_LIMIT_ADMIN=1000
RATE_LIMIT_AGENT=200
RATE_LIMIT_USER=100
RATE_LIMIT_GUEST=50

# ================================
# 🔐 SECURITY HEADERS
# ================================
SECURITY_HEADERS_ENABLED=true
CSP_ENABLED=true
HSTS_ENABLED=true
HSTS_MAX_AGE=31536000
X_FRAME_OPTIONS=DENY
X_CONTENT_TYPE_OPTIONS=nosniff
REFERRER_POLICY=strict-origin-when-cross-origin

# CORS Configuration
ALLOWED_ORIGINS=https://chatbotrncp.vercel.app,https://freemobile.fr,https://admin.freemobile.fr
CORS_CREDENTIALS=true
CORS_MAX_AGE=86400

# ================================
# 📱 PUSH NOTIFICATIONS
# ================================
PUSH_NOTIFICATIONS_ENABLED=true
# Obtain from: Firebase Console
FCM_SERVER_KEY=REPLACE_WITH_REAL_FCM_SERVER_KEY
FCM_PROJECT_ID=freemobile-chatbot-prod
# Obtain from: Apple Developer Console
APNS_KEY_ID=REPLACE_WITH_REAL_APNS_KEY_ID
APNS_TEAM_ID=REPLACE_WITH_REAL_APNS_TEAM_ID
APNS_BUNDLE_ID=com.freemobile.chatbot

# ================================
# 📊 MONITORING & LOGGING
# ================================
LOG_LEVEL=info
LOG_FILE_ENABLED=true
LOG_FILE_PATH=/var/log/chatbot/app.log
LOG_MAX_SIZE=5242880
LOG_MAX_FILES=10
LOG_SANITIZE_FIELDS=password,token,apiKey,secret,authorization

# Health Check Configuration
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_MEMORY_THRESHOLD=536870912

# ================================
# 🎪 FEATURE FLAGS
# ================================
FEATURE_CHAT_HISTORY=true
FEATURE_FILE_UPLOAD=true
FEATURE_VOICE_MESSAGES=false
FEATURE_VIDEO_CALLS=false
FEATURE_MULTI_LANGUAGE=true
FEATURE_DARK_MODE=true
FEATURE_ANALYTICS=true
FEATURE_PREDICTIVE=true
FEATURE_SIMULATION=true

# ================================
# 🔧 PERFORMANCE CONFIGURATION
# ================================
MAX_CONCURRENT_REQUESTS=100
REQUEST_TIMEOUT_SECONDS=30
KEEP_ALIVE_TIMEOUT=5000
COMPRESSION_ENABLED=true
COMPRESSION_LEVEL=6

# Cache Configuration
CACHE_TTL_SECONDS=3600
CACHE_MAX_SIZE=100
CACHE_ENABLED=true

# ================================
# 🚀 DEPLOYMENT CONFIGURATION
# ================================
DEPLOYMENT_ENVIRONMENT=production
DEPLOYMENT_VERSION=1.0.0
DEPLOYMENT_REGION=eu-west-1
AUTO_SCALING_ENABLED=true
MIN_INSTANCES=2
MAX_INSTANCES=10

# ================================
# 🔍 EXTERNAL SERVICES
# ================================
# ML Service Configuration
ML_SERVICE_URL=https://ml.chatbotrncp.internal:5001
ML_SERVICE_TIMEOUT=30000
ML_SERVICE_RETRIES=3
ML_API_KEY=REPLACE_WITH_SECURE_ML_API_KEY

# Analytics Service
ANALYTICS_SERVICE_URL=https://analytics.chatbotrncp.internal:5002
ANALYTICS_API_KEY=REPLACE_WITH_SECURE_ANALYTICS_API_KEY

# ================================
# 🛡️ BACKUP & DISASTER RECOVERY
# ================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_ENCRYPTION_KEY=REPLACE_WITH_SECURE_BACKUP_ENCRYPTION_KEY
BACKUP_STORAGE_URL=s3://freemobile-chatbot-backups

# ================================
# 📋 COMPLIANCE & AUDIT
# ================================
AUDIT_LOGGING_ENABLED=true
AUDIT_LOG_RETENTION_DAYS=365
GDPR_COMPLIANCE_ENABLED=true
DATA_RETENTION_DAYS=1095
ANONYMIZATION_ENABLED=true

# ================================
# 🚨 SECURITY MONITORING
# ================================
SECURITY_MONITORING_ENABLED=true
INTRUSION_DETECTION_ENABLED=true
FAILED_LOGIN_THRESHOLD=5
FAILED_LOGIN_LOCKOUT_DURATION=900
SUSPICIOUS_ACTIVITY_THRESHOLD=10

# ================================
# 📞 EMERGENCY CONTACTS
# ================================
EMERGENCY_EMAIL=<EMAIL>
INCIDENT_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
ON_CALL_PHONE=+33123456789

# ================================
# ⚠️ DEPLOYMENT CHECKLIST
# ================================
# Before deploying to production, ensure:
# [ ] All REPLACE_WITH_* values have been updated
# [ ] SSL certificates are properly configured
# [ ] Database connections use encryption
# [ ] API keys are from production services
# [ ] Monitoring and alerting are configured
# [ ] Backup procedures are tested
# [ ] Security scanning has been performed
# [ ] Load testing has been completed
# [ ] Incident response procedures are documented
