# [DEPLOY] Guide de Déploiement Production - Chatbot Free Mobile ## Vue d'ensemble Ce guide détaille le processus de déploiement en production du Chatbot Free Mobile avec une architecture cloud scalable et sécurisée. ## [ARCHITECTURE] Architecture de Production ``` ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │ Vercel │ │ Railway │ │ Railway │ │ (Frontend) │◄──►│ (Backend) │◄──►│ (Rasa NLP) │ │ React App │ │ Node.js API │ │ Python │ └─────────────────┘ └─────────────────┘ └─────────────────┘ ▲ │ ┌─────────────────┐ │ Railway │ │ (MongoDB) │ │ + Redis │ └─────────────────┘ ``` ## [CONFIG] Prérequis ### Comptes Cloud Requis - **Vercel** : Pour le frontend React - **Railway** : Pour backend, Rasa, MongoDB, Redis - **OpenAI** : Pour l'API GPT (clé API) - **GitHub** : Repository Git ### Outils Locaux ```bash # Node.js et npm node --version # >= 18.0.0 npm --version # >= 8.0.0 # Docker (optionnel pour tests locaux) docker --version docker-compose --version # Git git --version ``` ## Déploiement Frontend (Vercel) ### 1. Connexion à Vercel ```bash # Installation CLI Vercel npm install -g vercel # Connexion vercel login ``` ### 2. Configuration Frontend ```bash cd frontend # Variables d'environnement Vercel vercel env add REACT_APP_API_URL # Valeur: https://api.chatbot-free-mobile.railway.app vercel env add REACT_APP_SOCKET_URL # Valeur: https://api.chatbot-free-mobile.railway.app vercel env add REACT_APP_ENVIRONMENT # Valeur: production ``` ### 3. Déploiement ```bash # Premier déploiement vercel --prod # Déploiements ultérieurs vercel --prod ``` ### 4. Domaine Personnalisé 1. Aller sur Vercel Dashboard 2. Settings → Domains 3. Ajouter : `chatbot-free-mobile.com` ## Déploiement Backend (Railway) ### 1. Préparation Railway 1. Créer un compte sur [Railway.app](https://railway.app) 2. Connecter votre repository GitHub 3. Créer un nouveau projet ### 2. Services à Déployer #### A. MongoDB ```bash # Template MongoDB Railway 1. New Project → Add MongoDB 2. Nom: mongodb-freemobile 3. Noter les variables : DATABASE_URL ``` #### B. Redis ```bash # Template Redis Railway 1. Add Service → Redis 2. Nom: redis-freemobile 3. Noter les variables : REDIS_URL ``` #### C. Backend API ```bash # Déploiement Backend 1. Add Service → GitHub Repo 2. Sélectionner: free-mobile-chatbot/backend 3. Build Command: npm install && npm run build 4. Start Command: npm start 5. Port: 5000 ``` **Variables d'environnement Backend :** ```env NODE_ENV=production PORT=5000 MONGODB_URI=${{MongoDB.DATABASE_URL}} REDIS_URL=${{Redis.REDIS_URL}} JWT_SECRET=votre-secret-jwt-super-securise-minimum-32-caracteres OPENAI_API_KEY=sk-votre-cle-openai FRONTEND_URL=https://chatbot-free-mobile.vercel.app RASA_URL=${{Rasa.RAILWAY_STATIC_URL}} ``` #### D. Rasa NLP ```bash # Déploiement Rasa 1. Add Service → GitHub Repo 2. Sélectionner: free-mobile-chatbot/rasa 3. Dockerfile: Dockerfile.prod 4. Port: 5005 ``` ### 3. Configuration Domaines 1. Backend : `api.chatbot-free-mobile.com` 2. Rasa : `rasa.chatbot-free-mobile.com` ## [SECURITY] Configuration Sécurité ### 1. Variables d'Environnement Sécurisées ```bash # JWT Secret (générer avec) openssl rand -base64 32 # Mots de passe MongoDB/Redis openssl rand -base64 16 ``` ### 2. CORS et Headers Le backend est configuré pour : - CORS strict vers le frontend Vercel - Headers de sécurité HTTP - Rate limiting API - Validation JWT ### 3. HTTPS/SSL - **Vercel** : SSL automatique - **Railway** : SSL automatique - **Nginx** : Configuration SSL/TLS 1.3 ## [ANALYTICS] Monitoring et Logs ### 1. Logs Railway ```bash # Accès logs en temps réel railway logs --service backend railway logs --service rasa ``` ### 2. Monitoring Intégré - Railway Dashboard : Métriques CPU/RAM - Vercel Analytics : Performance frontend - Logs centralisés ### 3. Health Checks ```bash # Vérification santé services curl https://api.chatbot-free-mobile.com/health curl https://rasa.chatbot-free-mobile.com/status ``` ## [DEPLOY] Script de Déploiement Automatisé ### Utilisation du Script ```bash # Rendre exécutable chmod +x scripts/deploy.sh # Déploiement production ./scripts/deploy.sh production # Vérification santé ./scripts/deploy.sh health # Rollback si nécessaire ./scripts/deploy.sh rollback ``` ## CI/CD avec GitHub Actions ### Configuration Automatisée ```yaml # .github/workflows/deploy.yml name: Deploy to Production on: push: branches: [main] jobs: deploy-frontend: runs-on: ubuntu-latest steps: - uses: actions/checkout@v3 - name: Deploy to Vercel uses: amondnet/vercel-action@v20 with: vercel-token: ${{ secrets.VERCEL_TOKEN }} vercel-org-id: ${{ secrets.ORG_ID }} vercel-project-id: ${{ secrets.PROJECT_ID }} working-directory: ./frontend deploy-backend: runs-on: ubuntu-latest steps: - uses: actions/checkout@v3 - name: Deploy to Railway uses: bervProject/railway-deploy@v1.2.0 with: railway_token: ${{ secrets.RAILWAY_TOKEN }} service: backend ``` ## Tests de Production ### 1. Tests Fonctionnels ```bash # Test API curl -X POST https://api.chatbot-free-mobile.com/api/auth/login \ -H "Content-Type: application/json" \ -d '{"email":"<EMAIL>","password":"test123"}' # Test Chat curl -X POST https://api.chatbot-free-mobile.com/api/chat/message \ -H "Authorization: Bearer TOKEN" \ -d '{"message":"Bonjour"}' ``` ### 2. Tests de Charge ```bash # Avec Apache Bench ab -n 100 -c 10 https://api.chatbot-free-mobile.com/health # Avec Artillery.io npm install -g artillery artillery quick --count 50 --num 10 https://chatbot-free-mobile.vercel.app ``` ## [METRICS] Performance et Optimisation ### Métriques Cibles - **Frontend** : Lighthouse Score > 90 - **Backend** : Réponse API < 200ms - **Rasa** : Réponse NLP < 1s - **Uptime** : > 99.9% ### Optimisations Appliquées - [COMPLETE] **Frontend** : Code splitting, lazy loading, compression - [COMPLETE] **Backend** : Connection pooling, caching Redis - [COMPLETE] **Base de données** : Index MongoDB optimisés - [COMPLETE] **CDN** : Vercel Edge Network - [COMPLETE] **Compression** : Gzip/Brotli activée ## Dépannage ### Problèmes Fréquents #### 1. Erreur 500 Backend ```bash # Vérifier logs railway logs --service backend # Vérifier variables environnement railway variables --service backend ``` #### 2. Rasa ne démarre pas ```bash # Vérifier build Rasa railway logs --service rasa # Redéployer si nécessaire railway up --service rasa ``` #### 3. Frontend ne se connecte pas ```bash # Vérifier CORS dans logs backend # Vérifier variables REACT_APP_* dans Vercel ``` ### Support - **Documentation** : `/docs` - **Issues GitHub** : Repository Issues - **Logs** : Railway/Vercel Dashboards ## [TARGET] Checklist de Déploiement ### Pré-déploiement - [ ] Tests locaux passent - [ ] Variables d'environnement configurées - [ ] Secrets sécurisés générés - [ ] Sauvegardes effectuées ### Déploiement - [ ] Frontend déployé sur Vercel - [ ] Backend déployé sur Railway - [ ] Rasa déployé et fonctionnel - [ ] Base de données migrée ### Post-déploiement - [ ] Health checks validés - [ ] Tests de bout en bout passent - [ ] Monitoring configuré - [ ] Performance vérifiée - [ ] Documentation mise à jour ## Résultat Final Une fois le déploiement terminé, vous aurez : - ** Frontend** : https://chatbot-free-mobile.vercel.app - **[CONFIG] API Backend** : https://api.chatbot-free-mobile.railway.app - **[AI] Rasa NLP** : https://rasa.chatbot-free-mobile.railway.app - **[ANALYTICS] Monitoring** : Dashboards Railway/Vercel - ** Sécurité** : HTTPS, JWT, Rate limiting - **[METRICS] Performance** : CDN, cache, optimisations Le chatbot Free Mobile est maintenant **LIVE EN PRODUCTION** ! [DEPLOY]