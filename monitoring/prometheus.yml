global: scrape_interval: 15s evaluation_interval: 15s rule_files: - "rules/*.yml" alerting: alertmanagers: - static_configs: - targets: - alertmanager:9093 scrape_configs: # Backend Node.js metrics - job_name: 'backend' static_configs: - targets: ['backend:5000'] metrics_path: '/metrics' scrape_interval: 30s scrape_timeout: 10s # MongoDB metrics - job_name: 'mongodb' static_configs: - targets: ['mongodb-exporter:9216'] scrape_interval: 30s # Redis metrics - job_name: 'redis' static_configs: - targets: ['redis-exporter:9121'] scrape_interval: 30s # Nginx metrics - job_name: 'nginx' static_configs: - targets: ['nginx:9091'] metrics_path: '/nginx_status' scrape_interval: 30s # Node exporter (system metrics) - job_name: 'node' static_configs: - targets: ['node-exporter:9100'] scrape_interval: 15s # ML Service metrics - job_name: 'ml-service' static_configs: - targets: ['ml-service:5001'] metrics_path: '/metrics' scrape_interval: 30s scrape_timeout: 15s honor_labels: true # Frontend metrics - job_name: 'frontend' static_configs: - targets: ['frontend:80'] metrics_path: '/metrics' scrape_interval: 30s # TimescaleDB/PostgreSQL metrics - job_name: 'postgres' static_configs: - targets: ['postgres-exporter:9187'] scrape_interval: 30s metrics_path: '/metrics' # Application-specific metrics - job_name: 'simulation-metrics' static_configs: - targets: ['backend:5000'] metrics_path: '/api/metrics/simulation' scrape_interval: 30s honor_labels: true - job_name: 'predictive-metrics' static_configs: - targets: ['ml-service:5001'] metrics_path: '/api/metrics/predictive' scrape_interval: 60s honor_labels: true - job_name: 'enhanced-ai-metrics' static_configs: - targets: ['backend:5000'] metrics_path: '/api/metrics/enhanced-ai' scrape_interval: 30s honor_labels: true - job_name: 'analytics-metrics' static_configs: - targets: ['backend:5000'] metrics_path: '/api/metrics/analytics' scrape_interval: 60s honor_labels: true # WebSocket connection metrics - job_name: 'websocket-metrics' static_configs: - targets: ['backend:5000'] metrics_path: '/api/metrics/websocket' scrape_interval: 15s honor_labels: true # Business metrics - job_name: 'business-metrics' static_configs: - targets: ['backend:5000'] metrics_path: '/api/metrics/business' scrape_interval: 300s # 5 minutes honor_labels: true params: include_predictions: ['true'] # Health check endpoints - job_name: 'health-checks' static_configs: - targets: - 'backend:5000' - 'ml-service:5001' - 'frontend:80' metrics_path: '/health' scrape_interval: 30s scrape_timeout: 10s # Prometheus self-monitoring - job_name: 'prometheus' static_configs: - targets: ['localhost:9090']