# ============================================= # PROMETHEUS ALERTING RULES # Free Mobile Chatbot Dashboard - Phase 4 Production # Critical alerts for system monitoring # ============================================= groups: # ============================================= # CRITICAL SYSTEM ALERTS # ============================================= - name: critical-system rules: # Service down alerts - alert: ServiceDown expr: up == 0 for: 1m labels: severity: critical category: availability annotations: summary: "Service {{ $labels.job }} is down" description: "Service {{ $labels.job }} on {{ $labels.instance }} has been down for more than 1 minute." runbook_url: "https://docs.freemobile.fr/runbooks/service-down" # High CPU usage - alert: HighCPUUsage expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80 for: 5m labels: severity: warning category: performance annotations: summary: "High CPU usage on {{ $labels.instance }}" description: "CPU usage is above 80% for more than 5 minutes on {{ $labels.instance }}." # High memory usage - alert: HighMemoryUsage expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85 for: 5m labels: severity: warning category: performance annotations: summary: "High memory usage on {{ $labels.instance }}" description: "Memory usage is above 85% for more than 5 minutes on {{ $labels.instance }}." # Disk space critical - alert: DiskSpaceCritical expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100 > 90 for: 2m labels: severity: critical category: storage annotations: summary: "Critical disk space on {{ $labels.instance }}" description: "Disk usage is above 90% on {{ $labels.instance }} {{ $labels.mountpoint }}." # ============================================= # [AI] ML SERVICE ALERTS # ============================================= - name: ml-service rules: # ML service high response time - alert: MLServiceHighLatency expr: histogram_quantile(0.95, rate(ml_request_duration_seconds_bucket[5m])) > 2 for: 3m labels: severity: warning category: performance service: ml-service annotations: summary: "ML Service high latency" description: "95th percentile latency is above 2 seconds for ML service." # ML service high error rate - alert: MLServiceHighErrorRate expr: rate(ml_requests_total{status=~"5.."}[5m]) / rate(ml_requests_total[5m]) > 0.05 for: 2m labels: severity: critical category: errors service: ml-service annotations: summary: "ML Service high error rate" description: "Error rate is above 5% for ML service." # ML model accuracy degradation - alert: MLModelAccuracyDegradation expr: ml_model_accuracy < 0.8 for: 10m labels: severity: warning category: ml-quality service: ml-service annotations: summary: "ML Model accuracy degradation" description: "Model accuracy has dropped below 80% for {{ $labels.model_name }}." # ML processing queue backup - alert: MLProcessingQueueBackup expr: ml_processing_queue_size > 1000 for: 5m labels: severity: warning category: performance service: ml-service annotations: summary: "ML processing queue backup" description: "ML processing queue has more than 1000 pending items." # ML cache hit rate low - alert: MLCacheHitRateLow expr: ml_cache_hit_rate < 0.7 for: 10m labels: severity: warning category: performance service: ml-service annotations: summary: "ML cache hit rate low" description: "ML cache hit rate is below 70%." # ============================================= # [CONFIG] BACKEND API ALERTS # ============================================= - name: backend-api rules: # API high response time - alert: APIHighLatency expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="backend-api"}[5m])) > 1 for: 3m labels: severity: warning category: performance service: backend-api annotations: summary: "Backend API high latency" description: "95th percentile latency is above 1 second for backend API." # API high error rate - alert: APIHighErrorRate expr: rate(http_requests_total{job="backend-api",status=~"5.."}[5m]) / rate(http_requests_total{job="backend-api"}[5m]) > 0.03 for: 2m labels: severity: critical category: errors service: backend-api annotations: summary: "Backend API high error rate" description: "Error rate is above 3% for backend API." # WebSocket connection issues - alert: WebSocketConnectionIssues expr: websocket_connections_active < websocket_connections_expected * 0.8 for: 5m labels: severity: warning category: connectivity service: backend-api annotations: summary: "WebSocket connection issues" description: "Active WebSocket connections are below 80% of expected." # ============================================= # DATABASE ALERTS # ============================================= - name: database rules: # MongoDB replica set issues - alert: MongoDBReplicaSetIssue expr: mongodb_replset_member_health != 1 for: 2m labels: severity: critical category: database service: mongodb annotations: summary: "MongoDB replica set member unhealthy" description: "MongoDB replica set member {{ $labels.member }} is unhealthy." # MongoDB high connections - alert: MongoDBHighConnections expr: mongodb_connections{state="current"} / mongodb_connections{state="available"} > 0.8 for: 5m labels: severity: warning category: database service: mongodb annotations: summary: "MongoDB high connection usage" description: "MongoDB connection usage is above 80%." # PostgreSQL/TimescaleDB connection issues - alert: PostgreSQLHighConnections expr: pg_stat_database_numbackends / pg_settings_max_connections > 0.8 for: 5m labels: severity: warning category: database service: postgresql annotations: summary: "PostgreSQL high connection usage" description: "PostgreSQL connection usage is above 80%." # Redis memory usage high - alert: RedisHighMemoryUsage expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.9 for: 5m labels: severity: warning category: database service: redis annotations: summary: "Redis high memory usage" description: "Redis memory usage is above 90%." # ============================================= # BUSINESS METRICS ALERTS # ============================================= - name: business-metrics rules: # High priority conversations backup - alert: HighPriorityConversationsBackup expr: business_high_priority_conversations_pending > 50 for: 10m labels: severity: warning category: business service: chatbot annotations: summary: "High priority conversations backup" description: "More than 50 high priority conversations are pending." # Customer satisfaction drop - alert: CustomerSatisfactionDrop expr: avg_over_time(business_customer_satisfaction_score[1h]) < 3.5 for: 30m labels: severity: warning category: business service: chatbot annotations: summary: "Customer satisfaction drop" description: "Average customer satisfaction score has dropped below 3.5." # Revenue at risk increase - alert: RevenueAtRiskIncrease expr: increase(business_revenue_at_risk_total[1h]) > 10000 for: 15m labels: severity: critical category: business service: chatbot annotations: summary: "Revenue at risk increase" description: "Revenue at risk has increased by more than €10,000 in the last hour." # Churn risk alerts spike - alert: ChurnRiskAlertsSpike expr: increase(ml_alerts_total{type="churn_risk"}[1h]) > 20 for: 10m labels: severity: warning category: business service: ml-service annotations: summary: "Churn risk alerts spike" description: "More than 20 churn risk alerts generated in the last hour." # ============================================= # INFRASTRUCTURE ALERTS # ============================================= - name: infrastructure rules: # Container restart frequency - alert: ContainerRestartingFrequently expr: increase(container_start_time_seconds[1h]) > 3 for: 5m labels: severity: warning category: infrastructure annotations: summary: "Container restarting frequently" description: "Container {{ $labels.name }} has restarted more than 3 times in the last hour." # Load balancer backend down - alert: LoadBalancerBackendDown expr: nginx_upstream_server_up == 0 for: 1m labels: severity: critical category: infrastructure service: nginx annotations: summary: "Load balancer backend down" description: "Nginx upstream server {{ $labels.server }} is down." # SSL certificate expiring - alert: SSLCertificateExpiring expr: ssl_certificate_expiry_days < 30 for: 1h labels: severity: warning category: security annotations: summary: "SSL certificate expiring soon" description: "SSL certificate for {{ $labels.domain }} expires in {{ $value }} days." # ============================================= # SECURITY ALERTS # ============================================= - name: security rules: # High rate of failed login attempts - alert: HighFailedLoginRate expr: rate(auth_login_attempts_total{status="failed"}[5m]) > 10 for: 2m labels: severity: warning category: security service: backend-api annotations: summary: "High rate of failed login attempts" description: "More than 10 failed login attempts per second detected." # Suspicious API activity - alert: SuspiciousAPIActivity expr: rate(http_requests_total{status="403"}[5m]) > 5 for: 5m labels: severity: warning category: security service: backend-api annotations: summary: "Suspicious API activity" description: "High rate of 403 Forbidden responses detected." # DDoS attack detection - alert: PossibleDDoSAttack expr: rate(nginx_http_requests_total[1m]) > 1000 for: 2m labels: severity: critical category: security service: nginx annotations: summary: "Possible DDoS attack" description: "Request rate is above 1000 requests per second."