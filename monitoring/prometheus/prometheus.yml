# ============================================= # [ANALYTICS] PROMETHEUS CONFIGURATION # Free Mobile Chatbot Dashboard - Phase 4 Production # Monitoring and metrics collection # ============================================= global: scrape_interval: 15s evaluation_interval: 15s external_labels: cluster: 'freemobile-chatbot' environment: 'production' # Alertmanager configuration alerting: alertmanagers: - static_configs: - targets: - alertmanager:9093 # Rules files rule_files: - "rules/*.yml" # Scrape configurations scrape_configs: # Prometheus self-monitoring - job_name: 'prometheus' static_configs: - targets: ['localhost:9090'] scrape_interval: 30s metrics_path: /metrics # Node Exporter (system metrics) - job_name: 'node-exporter' static_configs: - targets: ['node-exporter:9100'] scrape_interval: 15s metrics_path: /metrics # Backend API metrics - job_name: 'backend-api' static_configs: - targets: ['backend:5000'] scrape_interval: 15s metrics_path: /api/metrics scrape_timeout: 10s honor_labels: true params: format: ['prometheus'] # ML Service metrics - job_name: 'ml-service' static_configs: - targets: ['ml-service:5001'] scrape_interval: 30s metrics_path: /metrics scrape_timeout: 15s honor_labels: true # Frontend Nginx metrics - job_name: 'frontend-nginx' static_configs: - targets: ['frontend:3001'] scrape_interval: 30s metrics_path: /nginx-status scrape_timeout: 10s # Main Nginx reverse proxy metrics - job_name: 'nginx-proxy' static_configs: - targets: ['nginx:80'] scrape_interval: 30s metrics_path: /nginx-status scrape_timeout: 10s # MongoDB metrics (using mongodb_exporter) - job_name: 'mongodb' static_configs: - targets: ['mongodb-exporter:9216'] scrape_interval: 30s metrics_path: /metrics scrape_timeout: 15s # PostgreSQL/TimescaleDB metrics (using postgres_exporter) - job_name: 'postgresql' static_configs: - targets: ['postgres-exporter:9187'] scrape_interval: 30s metrics_path: /metrics scrape_timeout: 15s # Redis metrics (using redis_exporter) - job_name: 'redis' static_configs: - targets: ['redis-exporter:9121'] scrape_interval: 30s metrics_path: /metrics scrape_timeout: 10s # Docker container metrics (using cAdvisor) - job_name: 'cadvisor' static_configs: - targets: ['cadvisor:8080'] scrape_interval: 30s metrics_path: /metrics scrape_timeout: 15s # Application-specific metrics - job_name: 'ml-analytics' static_configs: - targets: ['ml-service:5001'] scrape_interval: 60s metrics_path: /analytics/metrics scrape_timeout: 30s params: format: ['prometheus'] metric_relabel_configs: - source_labels: [__name__] regex: 'ml_.*' target_label: component replacement: 'ml-service' # Business metrics - job_name: 'business-metrics' static_configs: - targets: ['backend:5000'] scrape_interval: 300s # 5 minutes for business metrics metrics_path: /api/metrics/business scrape_timeout: 30s params: format: ['prometheus'] # Health checks - job_name: 'health-checks' static_configs: - targets: - 'backend:5000' - 'ml-service:5001' - 'frontend:3001' scrape_interval: 30s metrics_path: /health scrape_timeout: 10s metric_relabel_configs: - source_labels: [__name__] regex: 'up' target_label: service_health replacement: 'healthy' # Remote write configuration (for long-term storage) # remote_write: # - url: "https://your-remote-storage/api/v1/write" # basic_auth: # username: "your-username" # password: "your-password" # write_relabel_configs: # - source_labels: [__name__] # regex: 'ml_.*|business_.*|system_.*' # action: keep # Remote read configuration (for querying remote storage) # remote_read: # - url: "https://your-remote-storage/api/v1/read" # basic_auth: # username: "your-username" # password: "your-password" # Storage configuration storage: tsdb: path: /prometheus retention.time: 30d retention.size: 10GB wal-compression: true # Web configuration web: console.templates: /etc/prometheus/consoles console.libraries: /etc/prometheus/console_libraries enable-lifecycle: true enable-admin-api: true max-connections: 512 read-timeout: 30s route-prefix: / # Log configuration log: level: info format: json