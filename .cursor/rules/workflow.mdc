---
alwaysApply: true
---
<!-- EDIT - ADAPT # Development Agent Workflow - Cursor Rules

## Primary Directive

You are a development agent implementing a project. Follow established documentation and maintain consistency.

## Core Workflow Process

### Before Starting Any Task

- Consult `/Documentations/Implementation.md` for current stage and available tasks
- Check task dependencies and prerequisites
- Verify scope understanding

### Task Execution Protocol

#### 1. Task Assessment

- Read subtask from `/Documentations/Implementation.md`
- Assess subtask complexity:
  - **Simple subtask:** Implement directly
  - **Complex subtask:** Create a todo list

#### 3. Documentation Research

- Check `/Documentations/Implementation.md` for relevant documentation links in the subtask
- Read and understand documentation before implementing

#### 4. UI/UX Implementation

- Consult `/Documentations/UI-UX-doc.mdc` before implementing any UI/UX elements
- Follow design system specifications and responsive requirements

#### 5. Project Structure Compliance

- Check `/Documentations/project_structure.md` before:
  - Running commands
  - Creating files/folders
  - Making structural changes
  - Adding dependencies

#### 6. Error Handling

- Check `/Documentations/Bug_tracking.md` for similar issues before fixing
- Document all errors and solutions in Bug_tracking.md
- Include error details, root cause, and resolution steps

#### 7. Task Completion

Mark tasks complete only when:

- All functionality implemented correctly
- Code follows project structure guidelines
- UI/UX matches specifications (if applicable)
- No errors or warnings remain
- All task list items completed (if applicable)

### File Reference Priority

1. `/Documentations/Bug_tracking.md` - Check for known issues first
2. `/Documentations/Implementation.md` - Main task reference
3. `/Documentations/project_structure.md` - Structure guidance
4. `/Documentations/UI_UX_doc.md` - Design requirements

## Critical Rules

- **NEVER** skip documentation consultation
- **NEVER** mark tasks complete without proper testing
- **NEVER** ignore project structure guidelines
- **NEVER** implement UI without checking UI_UX_doc.md
- **NEVER** fix errors without checking Bug_tracking.md first
- **ALWAYS** document errors and solutions
- **ALWAYS** follow the established workflow process

Remember: Build a cohesive, well-documented, and maintainable project. Every decision should support overall project goals and maintain consistency with established patterns.# Development Agent Workflow - Cursor Rules

## Primary Directive

You are a development agent implementing a project. Follow established documentation and maintain consistency.

## Core Workflow Process

### Before Starting Any Task

- Consult `/Documentations/Implementation.md` for current stage and available tasks
- Check task dependencies and prerequisites
- Verify scope understanding

### Task Execution Protocol

#### 1. Task Assessment

- Read subtask from `/Documentations/Implementation.md`
- Assess subtask complexity:
  - **Simple subtask:** Implement directly
  - **Complex subtask:** Create a todo list

#### 3. Documentation Research

- Check `/Documentations/Implementation.md` for relevant documentation links in the subtask
- Read and understand documentation before implementing

#### 4. UI/UX Implementation

- Consult `/Documentations/UI_UX_doc.md` before implementing any UI/UX elements
- Follow design system specifications and responsive requirements

#### 5. Project Structure Compliance

- Check `/Documentations/project_structure.md` before:
  - Running commands
  - Creating files/folders
  - Making structural changes
  - Adding dependencies

#### 6. Error Handling

- Check `/Documentations/Bug_tracking.md` for similar issues before fixing
- Document all errors and solutions in Bug_tracking.md
- Include error details, root cause, and resolution steps

#### 7. Task Completion

Mark tasks complete only when:

- All functionality implemented correctly
- Code follows project structure guidelines
- UI/UX matches specifications (if applicable)
- No errors or warnings remain
- All task list items completed (if applicable)

### File Reference Priority

1. `/Documentations/Bug_tracking.md` - Check for known issues first
2. `/Documentations/Implementation.md` - Main task reference
3. `/Documentations/project_structure.md` - Structure guidance
4. `/Documentations/UI_UX_doc.md` - Design requirements

## Critical Rules

- **NEVER** skip documentation consultation
- **NEVER** mark tasks complete without proper testing
- **NEVER** ignore project structure guidelines
- **NEVER** implement UI without checking UI_UX_doc.md
- **NEVER** fix errors without checking Bug_tracking.md first
- **ALWAYS** document errors and solutions
- **ALWAYS** follow the established workflow process

Remember: Build a cohesive, well-documented, and maintainable project. Every decision should support overall project goals and maintain consistency with established patterns.
TO YOU OWN FILE FOLDERS IF NEEDED. -->

