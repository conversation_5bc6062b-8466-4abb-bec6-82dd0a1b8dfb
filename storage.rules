rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function getUserRole() {
      return firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role;
    }
    
    function isAdmin() {
      return isAuthenticated() && getUserRole() == 'admin';
    }
    
    function isAgent() {
      return isAuthenticated() && getUserRole() in ['admin', 'agent'];
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    // User profile images
    match /users/{userId}/profile/{fileName} {
      // Users can upload their own profile images
      allow read, write: if isOwner(userId);
      
      // Agents can read profile images
      allow read: if isAgent();
      
      // Validate file type and size (max 5MB)
      allow write: if isOwner(userId) && 
        request.resource.contentType.matches('image/.*') &&
        request.resource.size < 5 * 1024 * 1024;
    }
    
    // Chat attachments
    match /conversations/{conversationId}/attachments/{fileName} {
      // Users can upload attachments to their conversations
      allow read, write: if isAuthenticated() && (
        // Check if user owns the conversation
        firestore.exists(/databases/(default)/documents/conversations/$(conversationId)) &&
        firestore.get(/databases/(default)/documents/conversations/$(conversationId)).data.userId == request.auth.uid ||
        // Or if user is an agent
        isAgent()
      );
      
      // Validate file type and size (max 10MB)
      allow write: if request.resource.contentType.matches('(image|application|text)/.*') &&
        request.resource.size < 10 * 1024 * 1024;
    }
    
    // System assets (logos, templates, etc.) - Admin only
    match /system/{allPaths=**} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }
    
    // ML model files - Admin only
    match /ml-models/{allPaths=**} {
      allow read: if isAgent();
      allow write: if isAdmin();
    }
    
    // Analytics exports - Admin only
    match /exports/{allPaths=**} {
      allow read, write: if isAdmin();
    }
    
    // Backup files - Admin only
    match /backups/{allPaths=**} {
      allow read, write: if isAdmin();
    }
    
    // Temporary files (auto-deleted after 24h)
    match /temp/{allPaths=**} {
      allow read, write: if isAuthenticated();
      
      // Files in temp folder should be automatically deleted
      // This is handled by Cloud Functions
    }
  }
}
