# [SECURITY] **SECURITY CONFIGURATION GUIDE** ## Free Mobile Chatbot RNCP - Complete Security Setup --- ## **CRITICAL SECURITY NOTICE** ** NEVER SHARE ACTUAL CREDENTIALS IN PLAIN TEXT OR VERSION CONTROL** This guide provides templates and secure methods for configuring your credentials. Always use the provided tools and follow security best practices. --- ## **QUICK START GUIDE** ### **[DEPLOY] Automated Setup (Recommended)** ```bash # Run the interactive setup script npm run setup:env # Or for specific environments npm run setup:env:dev # Development npm run setup:env:staging # Staging npm run setup:env:prod # Production # Validate configuration npm run validate:env ``` ### **[CONFIG] Manual Setup** ```bash # Copy template and edit manually cp .env.template .env nano .env # Edit with your actual values # Set secure permissions chmod 600 .env ``` --- ## **REDIS CONFIGURATION** ### **Local Development (Recommended for Dev)** ```bash # Install Redis locally # Windows: https://redis.io/download # macOS: brew install redis # Linux: sudo apt-get install redis-server # Configuration REDIS_URL=redis://localhost:6379 REDIS_HOST=localhost REDIS_PORT=6379 REDIS_PASSWORD= # Leave empty for local ``` ### **Production Redis Providers** #### **Option 1: Redis Cloud (Recommended)** 1. Sign up at https://redis.com/redis-enterprise-cloud/ 2. Create a new database 3. Get connection details: ```bash REDIS_URL=redis://default:<EMAIL>:12345 ``` #### **Option 2: AWS ElastiCache** ```bash REDIS_URL=redis://your-cluster.cache.amazonaws.com:6379 ``` #### **Option 3: Google Cloud Memorystore** ```bash REDIS_URL=redis://********:6379 ``` --- ## **MONGODB ATLAS CONFIGURATION** ### **Step-by-Step Setup** #### **1. Access Your MongoDB Atlas Account** - Go to https://cloud.mongodb.com - Login with your credentials - Navigate to your "ChatbotRNCP" project #### **2. Database User Management** ```bash # Current cluster details: Cluster: chatbotrncp.za6xmim.mongodb.net Username: Anderson-Archimed01 ``` **To get/reset your password:** 1. Click "Database Access" in left sidebar 2. Find user "Anderson-Archimed01" 3. Click "Edit" → "Edit Password" 4. Choose "Autogenerate Secure Password" or set custom 5. Copy the password securely #### **3. Connection String Configuration** ```bash # Template (replace <db_password> with actual password) MONGODB_URI=mongodb+srv://Anderson-Archimed01:<db_password>@chatbotrncp.za6xmim.mongodb.net/chatbotrncp?retryWrites=true&w=majority&appName=ChatbotRNCP # Environment-specific databases # Development MONGODB_URI=mongodb+srv://Anderson-Archimed01:<db_password>@chatbotrncp.za6xmim.mongodb.net/chatbotrncp_dev?retryWrites=true&w=majority&appName=ChatbotRNCP # Staging MONGODB_URI=mongodb+srv://Anderson-Archimed01:<db_password>@chatbotrncp.za6xmim.mongodb.net/chatbotrncp_staging?retryWrites=true&w=majority&appName=ChatbotRNCP # Production MONGODB_URI=mongodb+srv://Anderson-Archimed01:<db_password>@chatbotrncp.za6xmim.mongodb.net/chatbotrncp?retryWrites=true&w=majority&appName=ChatbotRNCP ``` #### **4. Network Access Configuration** 1. Go to "Network Access" in MongoDB Atlas 2. Click "Add IP Address" 3. For development: Add your current IP 4. For production: Add your server IPs only 5. **Never use 0.0.0.0/0 in production** --- ## **CIRCLECI TOKEN CONFIGURATION** ### **Generate Personal Access Token** #### **1. Access CircleCI Dashboard** - Go to https://circleci.com - Login with your GitHub account (Anderson-Archimede) #### **2. Create Personal API Token** 1. Click your profile picture (top right) 2. Select "Personal API Tokens" 3. Click "Create New Token" 4. Configure: ``` Token Name: ChatbotRNCP-Production-CI-CD Scope: All (for full project access) ``` 5. Copy token immediately (it won't be shown again) #### **3. Token Configuration** ```bash # Add to your .env file CIRCLECI_TOKEN=your_actual_token_here CIRCLECI_PROJECT_ID=github/Anderson-Archimede/ChatbotRNCP CIRCLECI_VCS_TYPE=github ``` #### **4. Project Configuration** 1. Go to https://app.circleci.com/projects/project-dashboard/github/Anderson-Archimede/ 2. Find "ChatbotRNCP" project 3. Click "Set Up Project" 4. Add environment variables in project settings --- ## [SECURITY] **JWT SECURITY CONFIGURATION** ### **Generate Secure JWT Secret** ```bash # Option 1: Use our setup script (recommended) npm run setup:env # Option 2: Generate manually node -e "console.log(require('crypto').randomBytes(64).toString('base64'))" # Option 3: Use OpenSSL openssl rand -base64 64 ``` ### **JWT Configuration** ```bash # Strong JWT secret (minimum 32 characters) JWT_SECRET=your_generated_secure_jwt_secret_here JWT_EXPIRES_IN=7d JWT_REFRESH_EXPIRES_IN=30d ``` --- ## **SECURITY BEST PRACTICES** ### **[COMPLETE] Environment File Security** ```bash # Set restrictive permissions chmod 600 .env # Verify permissions ls -la .env # Should show: -rw------- (owner read/write only) # Ensure .env is in .gitignore echo ".env" >> .gitignore echo ".env.*" >> .gitignore ``` ### **[COMPLETE] Credential Rotation Schedule** ```bash # Recommended rotation frequency: Database Passwords: Every 90 days API Tokens: Every 60 days JWT Secrets: Every 30 days (production) Redis Passwords: Every 90 days ``` ### **[COMPLETE] Multi-Environment Setup** ```bash # Separate credentials for each environment .env.development # Development credentials .env.staging # Staging credentials .env.production # Production credentials # Never use production credentials in development ``` --- ## **TESTING CONFIGURATION** ### **Connection Testing** ```bash # Test MongoDB connection node -e " require('dotenv').config(); const { MongoClient } = require('mongodb'); MongoClient.connect(process.env.MONGODB_URI) .then(() => console.log('[COMPLETE] MongoDB connected')) .catch(err => console.error('[FAILED] MongoDB failed:', err.message)); " # Test Redis connection node -e " require('dotenv').config(); const redis = require('redis'); const client = redis.createClient({ url: process.env.REDIS_URL }); client.connect() .then(() => client.ping()) .then(() => console.log('[COMPLETE] Redis connected')) .catch(err => console.error('[FAILED] Redis failed:', err.message)) .finally(() => client.quit()); " # Test CircleCI API curl -H "Circle-Token: $CIRCLECI_TOKEN" https://circleci.com/api/v2/me ``` ### **Automated Validation** ```bash # Run comprehensive validation npm run validate:env # Expected output: # [COMPLETE] MongoDB connection successful # [COMPLETE] Redis connection successful # [COMPLETE] All configurations validated successfully ``` --- ## **TROUBLESHOOTING** ### **Common MongoDB Issues** ```bash # Error: Authentication failed # Solution: Verify username/password in MongoDB Atlas # Error: IP not whitelisted # Solution: Add your IP to Network Access # Error: Database not found # Solution: Check database name in connection string ``` ### **Common Redis Issues** ```bash # Error: Connection refused # Solution: Ensure Redis server is running # Error: Authentication failed # Solution: Verify Redis password ``` ### **Common CircleCI Issues** ```bash # Error: Unauthorized # Solution: Verify token permissions # Error: Project not found # Solution: Check project ID and repository name ``` --- ## **EMERGENCY CONTACTS** ### **For Security Issues** - **Immediate Security Concerns:** Contact your security team - **Credential Compromise:** Rotate affected credentials immediately - **System Breach:** Follow incident response procedures ### **Support Resources** - **MongoDB Atlas:** Support through Atlas dashboard - **Redis Cloud:** Support through provider dashboard - **CircleCI:** Help center and community support - **GitHub:** GitHub support for repository issues --- ## **CREDENTIAL ROTATION PROCEDURE** ### **Monthly Rotation (Production)** 1. **Generate new credentials** 2. **Update staging environment first** 3. **Test thoroughly in staging** 4. **Update production during maintenance window** 5. **Verify all services operational** 6. **Revoke old credentials** 7. **Update documentation** 8. **Notify team of changes** --- ## **SECURITY CHECKLIST** ### **Before Deployment** - [ ] All credentials are unique and strong - [ ] Environment files have correct permissions (600) - [ ] No credentials in source code or version control - [ ] Network access properly configured - [ ] SSL/TLS enabled for all connections - [ ] Monitoring and alerting configured - [ ] Backup and recovery procedures tested - [ ] Incident response plan documented ### **Regular Maintenance** - [ ] Credentials rotated according to schedule - [ ] Access logs reviewed monthly - [ ] Security patches applied promptly - [ ] Backup integrity verified - [ ] Performance metrics monitored - [ ] Security scans performed quarterly --- **[SECURITY] Remember: Security is a shared responsibility. Always follow the principle of least privilege and maintain vigilance against security threats.**