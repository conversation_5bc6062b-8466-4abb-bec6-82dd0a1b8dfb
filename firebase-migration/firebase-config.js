// Firebase Configuration for ChatbotRNCP Migration
import { initializeApp } from "firebase/app";
import { getFirestore, connectFirestoreEmulator } from "firebase/firestore";
import { getAuth, connectAuthEmulator } from "firebase/auth";
import { getFunctions, connectFunctionsEmulator } from "firebase/functions";
import { getStorage, connectStorageEmulator } from "firebase/storage";
import { getAnalytics } from "firebase/analytics";

// Firebase configuration from your provided config
const firebaseConfig = {
  apiKey: "AIzaSyAYXC-69NjkXNs0k-nxyBOJBYtjAl0J6TQ",
  authDomain: "chatbotrncp.firebaseapp.com",
  projectId: "chatbotrncp",
  storageBucket: "chatbotrncp.firebasestorage.app",
  messagingSenderId: "277216775922",
  appId: "1:277216775922:web:44efa7834313cf771b8a6b",
  measurementId: "G-CJBCPGSMCE"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const db = getFirestore(app);
export const auth = getAuth(app);
export const functions = getFunctions(app);
export const storage = getStorage(app);

// Initialize Analytics (only in production)
export const analytics = typeof window !== 'undefined' && getAnalytics(app);

// Development emulator configuration
if (process.env.NODE_ENV === 'development') {
  // Connect to emulators if running locally
  if (!auth._delegate._config.emulator) {
    connectAuthEmulator(auth, "http://localhost:9099");
  }
  if (!db._delegate._databaseId.projectId.includes('demo-')) {
    connectFirestoreEmulator(db, 'localhost', 8080);
  }
  if (!functions._delegate.region.includes('demo-')) {
    connectFunctionsEmulator(functions, 'localhost', 5001);
  }
  if (!storage._delegate._bucket.includes('demo-')) {
    connectStorageEmulator(storage, 'localhost', 9199);
  }
}

export default app;
