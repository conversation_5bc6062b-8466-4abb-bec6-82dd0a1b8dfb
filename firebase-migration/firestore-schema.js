// Firestore Schema Design for ChatbotRNCP Migration
// Converting MongoDB schemas to Firestore collections structure

/**
 * FIRESTORE COLLECTIONS STRUCTURE
 * 
 * This file defines the Firestore collections structure that will replace
 * the current MongoDB database. Each collection is designed to maintain
 * data integrity while leveraging Firestore's strengths.
 */

// Collection: users
export const USERS_COLLECTION = {
  name: 'users',
  structure: {
    // Document ID will be the Firebase Auth UID
    email: 'string', // Required, unique
    role: 'string', // 'user' | 'agent' | 'admin'
    profile: {
      firstName: 'string',
      lastName: 'string',
      phoneNumber: 'string',
      customerId: 'string', // Free Mobile customer ID
    },
    preferences: {
      language: 'string', // Default: 'fr'
      notifications: 'boolean', // Default: true
      theme: 'string', // 'light' | 'dark'
    },
    metadata: {
      createdAt: 'timestamp',
      lastLogin: 'timestamp',
      lastActivity: 'timestamp',
      isActive: 'boolean',
    },
    // Security fields
    security: {
      twoFactorEnabled: 'boolean',
      backupCodes: 'array', // Encrypted backup codes
      lastPasswordChange: 'timestamp',
    }
  },
  indexes: [
    { fields: ['email'], unique: true },
    { fields: ['role', 'metadata.isActive'] },
    { fields: ['profile.customerId'] }
  ]
};

// Collection: conversations
export const CONVERSATIONS_COLLECTION = {
  name: 'conversations',
  structure: {
    // Document ID will be auto-generated
    userId: 'string', // Reference to users collection
    sessionId: 'string', // Unique session identifier
    status: 'string', // 'active' | 'resolved' | 'escalated' | 'abandoned'
    channel: 'string', // 'web' | 'mobile' | 'voice'
    metadata: {
      userAgent: 'string',
      ipAddress: 'string',
      location: {
        country: 'string',
        city: 'string',
        coordinates: 'geopoint'
      },
      deviceInfo: {
        type: 'string', // 'desktop' | 'mobile' | 'tablet'
        os: 'string',
        browser: 'string'
      }
    },
    timing: {
      startedAt: 'timestamp',
      endedAt: 'timestamp',
      lastActivity: 'timestamp',
      duration: 'number' // in seconds
    },
    assignment: {
      agentId: 'string', // Reference to users collection
      assignedAt: 'timestamp',
      escalatedFrom: 'string' // Previous agent ID if escalated
    },
    satisfaction: {
      rating: 'number', // 1-5 scale
      feedback: 'string',
      submittedAt: 'timestamp',
      npsScore: 'number' // Net Promoter Score
    },
    analytics: {
      messageCount: 'number',
      avgResponseTime: 'number',
      resolutionTime: 'number',
      escalationCount: 'number'
    }
  },
  indexes: [
    { fields: ['userId', 'timing.startedAt'] },
    { fields: ['status', 'timing.startedAt'] },
    { fields: ['agentId', 'timing.startedAt'] },
    { fields: ['sessionId'], unique: true }
  ]
};

// Collection: messages
export const MESSAGES_COLLECTION = {
  name: 'messages',
  structure: {
    // Document ID will be auto-generated
    conversationId: 'string', // Reference to conversations collection
    sender: 'string', // 'user' | 'bot' | 'agent'
    content: {
      text: 'string',
      type: 'string', // 'text' | 'button' | 'card' | 'image' | 'file'
      payload: 'object', // Additional data for rich content
      attachments: 'array' // File references
    },
    nlp: {
      intent: {
        name: 'string',
        confidence: 'number'
      },
      entities: 'array', // [{ entity, value, confidence }]
      sentiment: {
        score: 'number', // -1 to 1
        emotion: 'string',
        confidence: 'number'
      }
    },
    timing: {
      timestamp: 'timestamp',
      processingTime: 'number', // in milliseconds
      deliveredAt: 'timestamp',
      readAt: 'timestamp'
    },
    metadata: {
      nlpProvider: 'string', // 'rasa' | 'openai'
      fallback: 'boolean',
      edited: 'boolean',
      editedAt: 'timestamp',
      version: 'number'
    },
    analytics: {
      responseTime: 'number',
      userSatisfaction: 'number',
      escalationTrigger: 'boolean'
    }
  },
  indexes: [
    { fields: ['conversationId', 'timing.timestamp'] },
    { fields: ['sender', 'timing.timestamp'] },
    { fields: ['nlp.intent.name', 'timing.timestamp'] }
  ]
};

// Collection: notifications
export const NOTIFICATIONS_COLLECTION = {
  name: 'notifications',
  structure: {
    userId: 'string', // Reference to users collection
    type: 'string', // 'info' | 'warning' | 'error' | 'success'
    title: 'string',
    message: 'string',
    priority: 'string', // 'low' | 'medium' | 'high' | 'urgent'
    channels: 'array', // ['in_app', 'email', 'sms', 'push']
    status: 'string', // 'pending' | 'sent' | 'delivered' | 'read' | 'failed'
    actions: 'array', // [{ label, action, url }]
    context: {
      conversationId: 'string',
      ticketId: 'string',
      category: 'string'
    },
    timing: {
      createdAt: 'timestamp',
      scheduledFor: 'timestamp',
      sentAt: 'timestamp',
      readAt: 'timestamp',
      expiresAt: 'timestamp'
    },
    delivery: {
      attempts: 'number',
      lastAttempt: 'timestamp',
      errors: 'array'
    }
  },
  indexes: [
    { fields: ['userId', 'status', 'timing.createdAt'] },
    { fields: ['type', 'priority', 'timing.createdAt'] },
    { fields: ['timing.scheduledFor'] }
  ]
};

// Collection: analytics (for ML and reporting)
export const ANALYTICS_COLLECTION = {
  name: 'analytics',
  structure: {
    type: 'string', // 'conversation' | 'user_behavior' | 'system_performance'
    timestamp: 'timestamp',
    data: 'object', // Flexible structure for different analytics types
    metadata: {
      source: 'string',
      version: 'string',
      processed: 'boolean'
    }
  },
  indexes: [
    { fields: ['type', 'timestamp'] },
    { fields: ['metadata.source', 'timestamp'] }
  ]
};

// Security Rules Template
export const FIRESTORE_SECURITY_RULES = `
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow read: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'agent'];
    }
    
    // Conversations access control
    match /conversations/{conversationId} {
      allow read, write: if request.auth != null && (
        resource.data.userId == request.auth.uid ||
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'agent']
      );
    }
    
    // Messages access control
    match /messages/{messageId} {
      allow read, write: if request.auth != null && (
        exists(/databases/$(database)/documents/conversations/$(resource.data.conversationId)) &&
        get(/databases/$(database)/documents/conversations/$(resource.data.conversationId)).data.userId == request.auth.uid ||
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'agent']
      );
    }
    
    // Notifications access control
    match /notifications/{notificationId} {
      allow read, write: if request.auth != null && resource.data.userId == request.auth.uid;
      allow write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'agent'];
    }
    
    // Analytics - admin only
    match /analytics/{analyticsId} {
      allow read, write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
  }
}
`;

export const COLLECTIONS = {
  USERS: USERS_COLLECTION,
  CONVERSATIONS: CONVERSATIONS_COLLECTION,
  MESSAGES: MESSAGES_COLLECTION,
  NOTIFICATIONS: NOTIFICATIONS_COLLECTION,
  ANALYTICS: ANALYTICS_COLLECTION
};
