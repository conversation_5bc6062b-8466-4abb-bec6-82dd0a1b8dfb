# [DEPLOY] **GUIDE DE DÉPLOIEMENT PRODUCTION** ## Free Mobile Chatbot ML Intelligence Dashboard - Phase 4 --- ## **TABLE DES MATIÈRES** 1. [Prérequis](#prérequis) 2. [Architecture de Production](#architecture-de-production) 3. [Configuration des Environnements](#configuration-des-environnements) 4. [Déploiement Étape par Étape](#déploiement-étape-par-étape) 5. [Monitoring et Surveillance](#monitoring-et-surveillance) 6. [Sauvegardes et Récupération](#sauvegardes-et-récupération) 7. [Sécurité](#sécurité) 8. [Dépannage](#dépannage) 9. [Maintenance](#maintenance) --- ## [CONFIG] **PRÉREQUIS** ### **Système d'Exploitation** - Ubuntu 20.04 LTS ou plus récent - CentOS 8 ou plus récent - Debian 11 ou plus récent ### **Ressources Matérielles Minimales** - **CPU**: 8 cores (16 recommandés) - **RAM**: 16 GB (32 GB recommandés) - **Stockage**: 500 GB SSD (1 TB recommandé) - **Réseau**: 1 Gbps ### **Logiciels Requis** ```bash # Docker et Docker Compose sudo apt update sudo apt install -y docker.io docker-compose # Outils système sudo apt install -y curl wget git htop netstat-nat # Outils de monitoring sudo apt install -y prometheus-node-exporter ``` ### **Ports Réseau** | Service | Port | Description | |---------|------|-------------| | HTTP | 80 | Trafic web principal | | HTTPS | 443 | Trafic web sécurisé | | Backend API | 5000 | API Node.js | | ML Service | 5001 | Service ML FastAPI | | Frontend | 3001 | Interface React | | MongoDB | 27017 | Base de données principale | | TimescaleDB | 5432 | Analytics ML | | Redis | 6379 | Cache et sessions | | Prometheus | 9090 | Métriques | | Grafana | 3000 | Visualisation | | Elasticsearch | 9200 | Logs centralisés | | Kibana | 5601 | Interface logs | --- ## [ARCHITECTURE] **ARCHITECTURE DE PRODUCTION** ### **Vue d'Ensemble** ``` ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │ Load Balancer │ │ Reverse Proxy │ │ Application │ │ (Nginx) │────│ (Nginx) │────│ Services │ └─────────────────┘ └─────────────────┘ └─────────────────┘ │ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │ Monitoring │ │ Databases │ │ Caching │ │ (Prometheus + │ │ (MongoDB + │ │ (Redis) │ │ Grafana) │ │ TimescaleDB) │ │ │ └─────────────────┘ └─────────────────┘ └─────────────────┘ ``` ### **Services Déployés** 1. **Frontend**: Interface React avec Nginx 2. **Backend API**: Node.js avec Express 3. **ML Service**: FastAPI avec modèles ML 4. **MongoDB Replica Set**: Base de données principale (3 nœuds) 5. **TimescaleDB**: Analytics et métriques ML 6. **Redis Cluster**: Cache et gestion de sessions 7. **Nginx**: Reverse proxy et load balancer 8. **Prometheus + Grafana**: Monitoring 9. **ELK Stack**: Logs centralisés --- ## **CONFIGURATION DES ENVIRONNEMENTS** ### **1. Variables d'Environnement** Copiez et configurez le fichier d'environnement : ```bash cp .env.prod.example .env.prod ``` **Variables Critiques à Configurer :** ```bash # Sécurité JWT_SECRET=your-super-secure-jwt-secret-key-change-this-in-production-2025 ML_API_KEY=ml-service-api-key-change-this-in-production-2025 # Base de données MONGO_ROOT_PASSWORD=FreeMobile_MongoDB_2025_SecurePassword! TIMESCALE_PASSWORD=FreeMobile_TimescaleDB_2025_SecurePassword! REDIS_PASSWORD=FreeMobile_Redis_2025_SecurePassword! # Domaine DOMAIN=your-domain.com FRONTEND_URL=https://your-domain.com ``` ### **2. Certificats SSL** **Option A: Let's Encrypt (Recommandé)** ```bash # Installation Certbot sudo apt install -y certbot python3-certbot-nginx # Génération des certificats sudo certbot --nginx -d your-domain.com -d www.your-domain.com ``` **Option B: Certificats personnalisés** ```bash # Placez vos certificats dans mkdir -p ssl/ cp your-domain.crt ssl/ cp your-domain.key ssl/ cp ca-bundle.crt ssl/ ``` --- ## [DEPLOY] **DÉPLOIEMENT ÉTAPE PAR ÉTAPE** ### **Étape 1: Préparation** ```bash # Cloner le repository git clone https://github.com/your-org/free-mobile-chatbot.git cd free-mobile-chatbot # Vérifier les prérequis ./scripts/deploy/check-prerequisites.sh # Configurer les permissions chmod +x scripts/deploy/*.sh chmod +x scripts/backup/*.sh ``` ### **Étape 2: Configuration** ```bash # Copier et configurer l'environnement cp .env.prod.example .env.prod nano .env.prod # Valider la configuration ./scripts/deploy/validate-config.sh ``` ### **Étape 3: Déploiement Automatisé** ```bash # Déploiement complet ./scripts/deploy/deploy-production.sh # Ou déploiement étape par étape ./scripts/deploy/deploy-production.sh --step-by-step ``` ### **Étape 4: Vérification** ```bash # Vérifier le statut des services docker-compose -f docker-compose.production.yml ps # Tests de santé ./scripts/deploy/health-check.sh # Vérifier les logs docker-compose -f docker-compose.production.yml logs -f ``` --- ## [ANALYTICS] **MONITORING ET SURVEILLANCE** ### **Dashboards Grafana** **Accès**: `http://your-domain.com:3000` - **Utilisateur**: admin - **Mot de passe**: Configuré dans `.env.prod` **Dashboards Disponibles**: 1. **System Overview**: Métriques système globales 2. **ML Performance**: Performance des modèles ML 3. **API Metrics**: Métriques des APIs 4. **Database Health**: Santé des bases de données 5. **Business KPIs**: Indicateurs business ### **Alertes Prometheus** **Configuration des alertes** dans `monitoring/prometheus/rules/alerts.yml`: ```yaml # Exemple d'alerte critique - alert: ServiceDown expr: up == 0 for: 1m labels: severity: critical annotations: summary: "Service {{ $labels.job }} is down" ``` ### **Logs Centralisés (ELK)** **Accès Kibana**: `http://your-domain.com:5601` **Index patterns configurés**: - `application-logs-*`: Logs applicatifs - `nginx-logs-*`: Logs Nginx - `ml-analytics-*`: Analytics ML --- ## **SAUVEGARDES ET RÉCUPÉRATION** ### **Sauvegardes Automatisées** **Configuration Cron**: ```bash # Éditer crontab sudo crontab -e # Ajouter les tâches de sauvegarde 0 2 * * * /path/to/scripts/backup/mongodb-backup.sh daily 0 3 * * 0 /path/to/scripts/backup/mongodb-backup.sh weekly 0 4 1 * * /path/to/scripts/backup/mongodb-backup.sh monthly 0 2 * * * /path/to/scripts/backup/timescaledb-backup.sh daily ``` ### **Restauration** **MongoDB**: ```bash # Restaurer depuis une sauvegarde ./scripts/backup/restore-mongodb.sh /backups/mongodb/daily/mongodb_backup_20250119_020000.tar.gz ``` **TimescaleDB**: ```bash # Restaurer depuis une sauvegarde ./scripts/backup/restore-timescaledb.sh /backups/timescaledb/daily/timescaledb_backup_20250119_020000.tar.gz ``` --- ## **SÉCURITÉ** ### **Pare-feu (UFW)** ```bash # Activer UFW sudo ufw enable # Règles de base sudo ufw default deny incoming sudo ufw default allow outgoing # Ports autorisés sudo ufw allow 22/tcp # SSH sudo ufw allow 80/tcp # HTTP sudo ufw allow 443/tcp # HTTPS # Accès restreint pour monitoring sudo ufw allow from 10.0.0.0/8 to any port 9090 # Prometheus sudo ufw allow from 10.0.0.0/8 to any port 3000 # Grafana ``` ### **Fail2Ban** ```bash # Installation sudo apt install -y fail2ban # Configuration sudo cp /etc/fail2ban/jail.conf /etc/fail2ban/jail.local sudo nano /etc/fail2ban/jail.local # Redémarrage sudo systemctl restart fail2ban ``` ### **Rotation des Secrets** ```bash # Script de rotation des mots de passe ./scripts/security/rotate-secrets.sh # Mise à jour des certificats SSL sudo certbot renew --dry-run ``` --- ## [CONFIG] **DÉPANNAGE** ### **Problèmes Courants** **1. Service ne démarre pas** ```bash # Vérifier les logs docker-compose -f docker-compose.production.yml logs service-name # Vérifier la configuration docker-compose -f docker-compose.production.yml config # Redémarrer le service docker-compose -f docker-compose.production.yml restart service-name ``` **2. Base de données inaccessible** ```bash # Vérifier le statut MongoDB docker-compose -f docker-compose.production.yml exec mongodb-primary mongosh --eval "rs.status()" # Vérifier TimescaleDB docker-compose -f docker-compose.production.yml exec timescaledb pg_isready ``` **3. Problèmes de performance** ```bash # Vérifier les ressources docker stats # Analyser les métriques curl http://localhost:9090/api/v1/query?query=up ``` ### **Commandes de Diagnostic** ```bash # Statut complet du système ./scripts/deploy/system-status.sh # Test de connectivité ./scripts/deploy/connectivity-test.sh # Analyse des performances ./scripts/deploy/performance-analysis.sh ``` --- ## **MAINTENANCE** ### **Mises à Jour** **1. Mise à jour des images Docker** ```bash # Sauvegarder avant mise à jour ./scripts/backup/full-backup.sh # Mettre à jour docker-compose -f docker-compose.production.yml pull docker-compose -f docker-compose.production.yml up -d # Vérifier ./scripts/deploy/health-check.sh ``` **2. Mise à jour du système** ```bash # Mise à jour des packages sudo apt update && sudo apt upgrade -y # Redémarrage si nécessaire sudo reboot ``` ### **Nettoyage** ```bash # Nettoyer les images Docker inutilisées docker system prune -a # Nettoyer les logs anciens sudo journalctl --vacuum-time=30d # Nettoyer les sauvegardes anciennes find /backups -name "*.tar.gz" -mtime +30 -delete ``` ### **Monitoring de la Santé** ```bash # Script de vérification quotidienne ./scripts/maintenance/daily-health-check.sh # Rapport hebdomadaire ./scripts/maintenance/weekly-report.sh # Optimisation mensuelle ./scripts/maintenance/monthly-optimization.sh ``` --- ## **SUPPORT ET CONTACTS** ### **Équipe DevOps** - **Email**: <EMAIL> - **Slack**: #devops-support - **Téléphone**: +33 1 XX XX XX XX (urgences uniquement) ### **Documentation Technique** - **Wiki**: https://wiki.freemobile.fr/chatbot - **API Docs**: https://api-docs.freemobile.fr - **Runbooks**: https://runbooks.freemobile.fr ### **Escalade** 1. **Niveau 1**: Équipe DevOps 2. **Niveau 2**: Architecte Technique 3. **Niveau 3**: CTO --- ## [COMPLETE] **CHECKLIST DE DÉPLOIEMENT** ### **Pré-déploiement** - [ ] Prérequis système vérifiés - [ ] Variables d'environnement configurées - [ ] Certificats SSL installés - [ ] Sauvegardes effectuées - [ ] Tests en staging validés ### **Déploiement** - [ ] Images Docker pullées - [ ] Services déployés dans l'ordre - [ ] Migrations de base de données exécutées - [ ] Health checks validés - [ ] Monitoring configuré ### **Post-déploiement** - [ ] Tests de fumée exécutés - [ ] Dashboards Grafana fonctionnels - [ ] Alertes configurées - [ ] Documentation mise à jour - [ ] Équipe notifiée --- ** Félicitations ! Votre Free Mobile Chatbot ML Intelligence Dashboard est maintenant déployé en production !**