# 🧪 Guide de Tests - ChatbotRNCP

## Vue d'Ensemble des Tests

ChatbotRNCP utilise une stratégie de tests complète avec plusieurs niveaux de validation pour garantir la qualité et la fiabilité du système en production.

### Pyramide de Tests
```
    /\
   /  \     E2E Tests (Playwright)
  /____\    Integration Tests  
 /______\   Unit Tests (Jest)
/__________\ Static Analysis (ESLint, TypeScript)
```

## Types de Tests Implémentés

### 🔬 Tests Unitaires (Jest)
- **Couverture:** 85% du code
- **Framework:** Jest + React Testing Library
- **Localisation:** `frontend/src/**/*.test.js`

### 🔗 Tests d'Intégration
- **Couverture:** 92% des endpoints API
- **Framework:** Supertest + Chai
- **Localisation:** `backend/tests/integration/`

### 🌐 Tests End-to-End (Playwright)
- **Statut:** 27/82 tests passés (33% - en amélioration)
- **Framework:** Playwright
- **Localisation:** `tests/e2e/`

### 🛡️ Tests de Sécurité
- **Couverture:** 100% des vulnérabilités connues
- **Outils:** OWASP ZAP, npm audit
- **Automatisation:** CI/CD pipeline

## Configuration des Tests

### Installation des Dépendances de Test

```bash
# Installation globale
npm run test:install

# Installation Playwright
npm run test:e2e:install

# Installation des navigateurs
npx playwright install
```

### Variables d'Environnement de Test

```bash
# .env.test
NODE_ENV=test
MONGODB_URI=mongodb://localhost:27017/freemobile_chatbot_test
REDIS_URL=redis://localhost:6379/1
JWT_SECRET=test-secret-key
DISABLE_RATE_LIMITING=true
LOG_LEVEL=error
```

## Exécution des Tests

### Tests Unitaires Frontend

```bash
# Tous les tests unitaires
npm run test:frontend

# Tests avec couverture
npm run test:frontend -- --coverage

# Tests en mode watch
npm run test:frontend -- --watch

# Tests spécifiques
npm run test:frontend -- --testNamePattern="Dashboard"
```

### Tests d'Intégration Backend

```bash
# Tous les tests d'intégration
npm run test:backend

# Tests par module
npm run test:backend -- --grep "Auth"
npm run test:backend -- --grep "Tickets"
npm run test:backend -- --grep "Chat"
```

### Tests End-to-End

```bash
# Suite complète E2E
npm run test:e2e

# Tests avec interface graphique
npm run test:e2e:ui

# Tests en mode debug
npm run test:e2e:debug

# Tests par catégorie
npm run test:e2e:auth
npm run test:e2e:chat
npm run test:e2e:analytics
```

### Tests Cross-Browser

```bash
# Tous les navigateurs
npm run test:cross-browser

# Navigateurs spécifiques
npm run test:e2e -- --project=chromium
npm run test:e2e -- --project=firefox
npm run test:e2e -- --project=webkit
```

## Structure des Tests E2E

### Organisation des Fichiers

```
tests/
├── e2e/
│   ├── auth/
│   │   ├── login.spec.js
│   │   ├── registration.spec.js
│   │   └── 2fa.spec.js
│   ├── chat/
│   │   ├── conversation.spec.js
│   │   ├── file-upload.spec.js
│   │   └── real-time.spec.js
│   ├── analytics/
│   │   ├── dashboard.spec.js
│   │   ├── charts.spec.js
│   │   └── exports.spec.js
│   └── admin/
│       ├── user-management.spec.js
│       ├── system-config.spec.js
│       └── monitoring.spec.js
├── fixtures/
│   ├── users.json
│   ├── tickets.json
│   └── conversations.json
└── utils/
    ├── test-helpers.js
    ├── mock-data.js
    └── page-objects/
```

### Exemple de Test E2E

```javascript
// tests/e2e/auth/login.spec.js
import { test, expect } from '@playwright/test';

test.describe('Authentification', () => {
  test('Connexion agent avec credentials valides', async ({ page }) => {
    // Navigation vers la page de connexion
    await page.goto('/login');
    
    // Saisie des credentials
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'SecurePass123!');
    
    // Soumission du formulaire
    await page.click('[data-testid="login-button"]');
    
    // Vérification de la redirection
    await expect(page).toHaveURL('/dashboard');
    
    // Vérification de l'interface agent
    await expect(page.locator('[data-testid="agent-dashboard"]')).toBeVisible();
    await expect(page.locator('[data-testid="user-menu"]')).toContainText('<EMAIL>');
  });

  test('Échec de connexion avec credentials invalides', async ({ page }) => {
    await page.goto('/login');
    
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'wrongpassword');
    await page.click('[data-testid="login-button"]');
    
    // Vérification du message d'erreur
    await expect(page.locator('[data-testid="error-message"]'))
      .toContainText('Identifiants invalides');
    
    // Vérification que l'utilisateur reste sur la page de connexion
    await expect(page).toHaveURL('/login');
  });
});
```

## Tests de Performance

### Configuration Lighthouse

```javascript
// tests/performance/lighthouse.config.js
module.exports = {
  ci: {
    collect: {
      url: ['http://localhost:3000', 'http://localhost:3000/dashboard'],
      numberOfRuns: 3
    },
    assert: {
      assertions: {
        'categories:performance': ['error', { minScore: 0.8 }],
        'categories:accessibility': ['error', { minScore: 0.9 }],
        'categories:best-practices': ['error', { minScore: 0.9 }],
        'categories:seo': ['error', { minScore: 0.8 }]
      }
    }
  }
};
```

### Tests de Charge

```bash
# Tests de charge avec Artillery
npm run test:load

# Tests de stress
npm run test:stress

# Tests de performance API
npm run test:api-performance
```

## Tests de Sécurité

### Audit de Sécurité Automatisé

```bash
# Audit npm
npm audit

# Scan de vulnérabilités
npm run security:scan

# Tests de pénétration automatisés
npm run security:pentest
```

### Tests de Sécurité Spécifiques

```javascript
// tests/security/auth.spec.js
test.describe('Sécurité Authentification', () => {
  test('Protection contre les attaques par force brute', async ({ page }) => {
    // Tentatives multiples avec mauvais mot de passe
    for (let i = 0; i < 6; i++) {
      await page.goto('/login');
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'wrongpassword');
      await page.click('[data-testid="login-button"]');
    }
    
    // Vérification du rate limiting
    await expect(page.locator('[data-testid="rate-limit-message"]'))
      .toContainText('Trop de tentatives');
  });
});
```

## Rapports de Tests

### Génération des Rapports

```bash
# Rapport HTML Playwright
npm run test:e2e:report

# Rapport de couverture Jest
npm run test:coverage

# Rapport Allure (complet)
npm run test:allure:generate
npm run test:allure:serve
```

### Métriques de Qualité

```bash
# Analyse de code statique
npm run lint
npm run type-check

# Métriques de complexité
npm run complexity:report

# Audit de dépendances
npm run deps:audit
```

## CI/CD Integration

### GitHub Actions

```yaml
# .github/workflows/tests.yml
name: Tests
on: [push, pull_request]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test:frontend
      - run: npm run test:backend

  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: npx playwright install
      - run: npm run test:e2e
      - uses: actions/upload-artifact@v3
        if: always()
        with:
          name: playwright-report
          path: playwright-report/
```

## Debugging des Tests

### Debug Playwright

```bash
# Mode debug interactif
npm run test:e2e:debug

# Enregistrement des traces
npm run test:e2e -- --trace on

# Visualisation des traces
npx playwright show-trace trace.zip
```

### Debug Jest

```bash
# Debug avec Node.js inspector
node --inspect-brk node_modules/.bin/jest --runInBand

# Debug avec VS Code
# Ajouter configuration launch.json
```

## Bonnes Pratiques

### Écriture de Tests Robustes

1. **Utiliser des sélecteurs stables** (`data-testid`)
2. **Attendre les éléments** avec `waitFor`
3. **Isoler les tests** (pas de dépendances entre tests)
4. **Nettoyer après chaque test**
5. **Utiliser des données de test dédiées**

### Optimisation des Performances

1. **Parallélisation** des tests
2. **Réutilisation** des contextes navigateur
3. **Cache** des dépendances
4. **Tests conditionnels** selon l'environnement

---

**Tests Status:** 🟡 **En Amélioration Continue**  
*Objectif: 100% de couverture E2E d'ici Q2 2025*
