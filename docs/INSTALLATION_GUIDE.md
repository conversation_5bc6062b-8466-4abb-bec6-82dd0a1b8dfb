# 📋 Guide d'Installation Détaillé - ChatbotRNCP

## Prérequis Système

### Logiciels Requis
- **Node.js** ≥ 16.0.0 (recommandé: 18.x LTS)
- **npm** ≥ 8.0.0 ou **yarn** ≥ 1.22.0
- **Docker** ≥ 20.10.0
- **Docker Compose** ≥ 2.0.0
- **Git** ≥ 2.30.0

### Configuration Système Minimale
- **RAM:** 8GB minimum (16GB recommandé)
- **Stockage:** 20GB d'espace libre
- **CPU:** 4 cœurs minimum
- **OS:** Windows 10/11, macOS 10.15+, Ubuntu 18.04+

## Installation Étape par Étape

### 1. Clonage du Repository

```bash
# Cloner le repository principal
git clone https://github.com/freemobile/chatbotrncp.git
cd chatbotrncp

# Vérifier la branche
git checkout main
git pull origin main
```

### 2. Configuration de l'Environnement

```bash
# Naviguer vers le dossier principal
cd free-mobile-chatbot

# Copier les fichiers d'environnement
cp .env.example .env
cp frontend/.env.example frontend/.env
cp backend/.env.example backend/.env

# Configurer les variables d'environnement
npm run setup:env:dev
```

### 3. Installation des Dépendances

```bash
# Installation globale (recommandé)
npm run install:all

# Ou installation manuelle
npm install
cd frontend && npm install && cd ..
cd backend && npm install && cd ..
cd ml-service && pip install -r requirements.txt && cd ..
```

### 4. Configuration de la Base de Données

```bash
# Démarrer les services de base
docker-compose up -d mongodb redis timescaledb

# Attendre que les services soient prêts (30-60 secondes)
npm run validate:services

# Initialiser la base de données
npm run db:init
```

### 5. Démarrage des Services

```bash
# Démarrage complet (recommandé)
npm start

# Ou démarrage manuel par service
npm run start:backend    # Port 5000
npm run start:frontend   # Port 3000
npm run start:ml-service # Port 5001
```

## Configuration Avancée

### Variables d'Environnement Critiques

```bash
# .env principal
NODE_ENV=development
PORT=5000

# Base de données
MONGODB_URI=**************************************************************************************
REDIS_URL=redis://localhost:6379
TIMESCALE_URL=postgresql://postgres:password@localhost:5432/analytics

# Sécurité
JWT_SECRET=your-super-secure-jwt-secret-key-min-32-chars
JWT_REFRESH_SECRET=your-refresh-secret-key-min-32-chars
BCRYPT_ROUNDS=12

# Services externes
OPENAI_API_KEY=sk-your-openai-api-key
TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+***********

# URLs
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:5000
RASA_URL=http://localhost:5005
```

### Configuration Docker Personnalisée

```yaml
# docker-compose.override.yml
version: '3.8'
services:
  mongodb:
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: freemobile
      MONGO_INITDB_ROOT_PASSWORD: freemobile123
      
  redis:
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass yourredispassword
    
  backend:
    environment:
      LOG_LEVEL: debug
      ENABLE_CORS: true
      
  frontend:
    environment:
      REACT_APP_DEBUG: true
      GENERATE_SOURCEMAP: false
```

## Vérification de l'Installation

### Tests de Santé

```bash
# Vérifier tous les services
npm run health:check

# Tests individuels
curl http://localhost:5000/health    # Backend
curl http://localhost:3000           # Frontend
curl http://localhost:5001/health    # ML Service
```

### Tests Fonctionnels

```bash
# Tests unitaires
npm run test:unit

# Tests d'intégration
npm run test:integration

# Tests E2E (nécessite Playwright)
npm run test:e2e:install
npm run test:e2e
```

## Résolution des Problèmes Courants

### Erreur de Port Occupé

```bash
# Vérifier les ports utilisés
netstat -tulpn | grep :3000
netstat -tulpn | grep :5000

# Arrêter les processus
sudo kill -9 $(lsof -t -i:3000)
sudo kill -9 $(lsof -t -i:5000)
```

### Problèmes de Permissions Docker

```bash
# Linux/macOS
sudo usermod -aG docker $USER
newgrp docker

# Windows (PowerShell en tant qu'administrateur)
# Redémarrer Docker Desktop
```

### Erreurs de Base de Données

```bash
# Réinitialiser MongoDB
docker-compose down -v
docker volume prune -f
docker-compose up -d mongodb

# Vérifier la connexion
docker exec -it freemobile-mongodb-dev mongosh -u freemobile -p freemobile123
```

### Problèmes de Mémoire

```bash
# Augmenter la limite Node.js
export NODE_OPTIONS="--max-old-space-size=4096"

# Nettoyer le cache npm
npm cache clean --force
```

## Optimisations de Performance

### Configuration Développement

```bash
# .env.development
CHOKIDAR_USEPOLLING=false
FAST_REFRESH=true
GENERATE_SOURCEMAP=false
INLINE_RUNTIME_CHUNK=false
```

### Configuration Production

```bash
# Build optimisé
npm run build:production

# Variables d'environnement production
NODE_ENV=production
REACT_APP_ENVIRONMENT=production
ENABLE_HTTPS=true
COMPRESSION=true
```

## Support et Dépannage

### Logs et Debugging

```bash
# Logs en temps réel
npm run logs:all

# Logs par service
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f mongodb
```

### Contacts Support

- **Documentation:** [docs.freemobile-chatbot.com](https://docs.freemobile-chatbot.com)
- **Issues GitHub:** [github.com/freemobile/chatbotrncp/issues](https://github.com/freemobile/chatbotrncp/issues)
- **Email Support:** <EMAIL>
- **Slack:** #chatbot-rncp-support

---

**Installation réussie!** 🎉  
Votre environnement ChatbotRNCP est maintenant prêt pour le développement.
