# 🔌 Documentation API - ChatbotRNCP

## Vue d'Ensemble

L'API ChatbotRNCP est une API REST moderne construite avec Node.js et Express, offrant des endpoints sécurisés pour la gestion des conversations, tickets, utilisateurs et analytics.

**Base URL:** `http://localhost:5000/api/v1`  
**Production:** `https://api.freemobile-chatbot.com/v1`

## Authentification

### JWT Bearer Token

```bash
# Header requis pour les endpoints protégés
Authorization: Bearer <your-jwt-token>
```

### Endpoints d'Authentification

#### POST /auth/login
Connexion utilisateur avec email/mot de passe.

```json
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

**Réponse:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_id",
      "email": "<EMAIL>",
      "role": "agent",
      "firstName": "<PERSON>",
      "lastName": "Doe"
    },
    "tokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIs...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
      "expiresIn": 3600
    }
  }
}
```

#### POST /auth/refresh
Renouvellement du token d'accès.

```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIs..."
}
```

#### POST /auth/logout
Déconnexion et invalidation des tokens.

## Endpoints Principaux

### 💬 Chat & Messages

#### GET /chat/conversations
Récupérer les conversations de l'utilisateur.

**Paramètres de requête:**
- `page` (number): Page (défaut: 1)
- `limit` (number): Limite par page (défaut: 20)
- `status` (string): Statut de conversation

**Réponse:**
```json
{
  "success": true,
  "data": {
    "conversations": [
      {
        "id": "conv_123",
        "participants": ["user_1", "agent_2"],
        "lastMessage": {
          "content": "Bonjour, comment puis-je vous aider?",
          "timestamp": "2025-01-17T10:30:00Z",
          "sender": "agent_2"
        },
        "status": "active",
        "createdAt": "2025-01-17T10:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 45,
      "pages": 3
    }
  }
}
```

#### POST /chat/conversations/:id/messages
Envoyer un message dans une conversation.

```json
{
  "content": "Bonjour, j'ai un problème avec ma connexion",
  "type": "text",
  "attachments": []
}
```

### 🎫 Gestion des Tickets

#### GET /tickets
Lister les tickets avec filtres.

**Paramètres de requête:**
- `status` (string): open, in_progress, resolved, closed
- `priority` (string): low, medium, high, urgent
- `category` (string): technical, billing, general
- `assignedTo` (string): ID de l'agent assigné

#### POST /tickets
Créer un nouveau ticket.

```json
{
  "title": "Problème de connexion WiFi",
  "description": "Ma Freebox ne se connecte plus au WiFi depuis ce matin",
  "category": "technical",
  "priority": "medium",
  "customerInfo": {
    "name": "Marie Dupont",
    "email": "<EMAIL>",
    "phone": "+33123456789",
    "freeMobileId": "FREE-12345678"
  }
}
```

**Réponse:**
```json
{
  "success": true,
  "data": {
    "ticket": {
      "id": "FM-20250117-0001",
      "title": "Problème de connexion WiFi",
      "status": "open",
      "priority": "medium",
      "category": "technical",
      "createdAt": "2025-01-17T10:30:00Z",
      "estimatedResolution": "2025-01-17T14:30:00Z"
    }
  }
}
```

#### PUT /tickets/:id
Mettre à jour un ticket.

```json
{
  "status": "in_progress",
  "assignedTo": "agent_123",
  "internalNotes": "Diagnostic en cours, problème identifié"
}
```

### 📊 Analytics & Métriques

#### GET /analytics/dashboard
Récupérer les métriques du dashboard.

**Réponse:**
```json
{
  "success": true,
  "data": {
    "overview": {
      "totalTickets": 1247,
      "openTickets": 89,
      "resolvedToday": 156,
      "averageResponseTime": "2.3 minutes",
      "satisfactionScore": 4.6
    },
    "trends": {
      "ticketVolume": [
        {"date": "2025-01-17", "count": 45},
        {"date": "2025-01-16", "count": 52}
      ],
      "responseTime": [
        {"date": "2025-01-17", "avgTime": 138},
        {"date": "2025-01-16", "avgTime": 142}
      ]
    }
  }
}
```

#### GET /analytics/performance
Métriques de performance détaillées.

**Paramètres de requête:**
- `timeRange` (string): 24h, 7d, 30d, 90d
- `agentId` (string): Filtrer par agent
- `category` (string): Filtrer par catégorie

### 👥 Gestion des Utilisateurs

#### GET /users
Lister les utilisateurs (admin uniquement).

#### POST /users
Créer un nouvel utilisateur.

```json
{
  "email": "<EMAIL>",
  "firstName": "Jean",
  "lastName": "Martin",
  "role": "agent",
  "department": "support_technique"
}
```

#### PUT /users/:id
Mettre à jour un utilisateur.

#### DELETE /users/:id
Supprimer un utilisateur (soft delete).

## WebSocket Events

### Connexion
```javascript
const socket = io('http://localhost:5000', {
  auth: {
    token: 'your-jwt-token'
  }
});
```

### Events Disponibles

#### Chat Events
- `message:new` - Nouveau message reçu
- `message:typing` - Utilisateur en train de taper
- `conversation:join` - Rejoindre une conversation
- `conversation:leave` - Quitter une conversation

#### Ticket Events
- `ticket:created` - Nouveau ticket créé
- `ticket:updated` - Ticket mis à jour
- `ticket:assigned` - Ticket assigné à un agent

#### Analytics Events
- `metrics:update` - Mise à jour des métriques en temps réel

## Codes d'Erreur

### Codes HTTP Standards
- `200` - Succès
- `201` - Créé avec succès
- `400` - Requête invalide
- `401` - Non authentifié
- `403` - Accès interdit
- `404` - Ressource non trouvée
- `429` - Trop de requêtes (rate limiting)
- `500` - Erreur serveur interne

### Format d'Erreur
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Les données fournies sont invalides",
    "details": [
      {
        "field": "email",
        "message": "Format d'email invalide"
      }
    ]
  }
}
```

## Rate Limiting

### Limites par Endpoint
- **Authentification:** 5 tentatives/minute
- **API Générale:** 100 requêtes/minute
- **Upload de fichiers:** 10 uploads/minute
- **WebSocket:** 1000 messages/minute

### Headers de Rate Limiting
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642428000
```

## Exemples d'Utilisation

### JavaScript/Node.js
```javascript
const axios = require('axios');

const api = axios.create({
  baseURL: 'http://localhost:5000/api/v1',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
});

// Créer un ticket
const createTicket = async (ticketData) => {
  try {
    const response = await api.post('/tickets', ticketData);
    return response.data;
  } catch (error) {
    console.error('Erreur création ticket:', error.response.data);
  }
};
```

### cURL
```bash
# Connexion
curl -X POST http://localhost:5000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Créer un ticket
curl -X POST http://localhost:5000/api/v1/tickets \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"title":"Problème technique","description":"Description du problème"}'
```

## Environnements

### Développement
- **URL:** http://localhost:5000/api/v1
- **Documentation:** http://localhost:5000/api-docs

### Staging
- **URL:** https://staging-api.freemobile-chatbot.com/v1
- **Documentation:** https://staging-api.freemobile-chatbot.com/api-docs

### Production
- **URL:** https://api.freemobile-chatbot.com/v1
- **Documentation:** https://api.freemobile-chatbot.com/api-docs

---

**Documentation API complète disponible sur:** [docs.freemobile-chatbot.com/api](https://docs.freemobile-chatbot.com/api)
