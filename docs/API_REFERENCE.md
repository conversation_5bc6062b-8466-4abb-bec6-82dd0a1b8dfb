# Free Mobile Chatbot API Reference ## Base URL ``` Production: https://api.chatbot.freemobile.fr Development: http://localhost:5000 ``` ## Authentication All API endpoints require authentication using JWT tokens. ### Headers ```http Authorization: Bearer <jwt_token> Content-Type: application/json X-API-Key: <api_key> ``` ### Authentication Endpoints #### Login ```http POST /api/auth/login ``` **Request Body:** ```json { "email": "<EMAIL>", "password": "password123" } ``` **Response:** ```json { "success": true, "data": { "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "user": { "id": "user_123", "email": "<EMAIL>", "role": "agent", "firstName": "<PERSON>", "lastName": "Doe" } } } ``` ## Simulation API ### Scenarios #### List Scenarios ```http GET /api/simulation/scenarios ``` **Query Parameters:** | Parameter | Type | Description | Default | |-----------|------|-------------|---------| | difficulty | string | Filter by difficulty (beginner, intermediate, expert) | all | | category | string | Filter by category | all | | limit | integer | Number of scenarios to return | 50 | | offset | integer | Number of scenarios to skip | 0 | **Response:** ```json { "success": true, "data": { "scenarios": [ { "_id": "scenario_123", "title": "Billing Issue Resolution", "description": "Handle customer billing disputes effectively", "difficulty": "intermediate", "category": "billing", "tags": ["billing", "dispute", "resolution"], "estimated_duration": 15, "learning_objectives": ["empathy", "accuracy", "communication"], "customer_profile": { "tier": "gold", "communication_style": "direct", "issue_complexity": "moderate" }, "success_criteria": { "min_empathy_score": 75, "min_efficiency_score": 70, "min_accuracy_score": 80 } } ], "pagination": { "total": 25, "limit": 50, "offset": 0, "has_more": false } } } ``` #### Get Recommended Scenarios ```http GET /api/simulation/scenarios/recommended ``` **Query Parameters:** | Parameter | Type | Description | Default | |-----------|------|-------------|---------| | limit | integer | Number of recommendations | 5 | **Response:** ```json { "success": true, "data": [ { "_id": "scenario_456", "title": "Technical Support Advanced", "recommendation_reason": "Based on your recent performance in technical scenarios", "confidence": 0.89, "estimated_improvement": 12.5 } ] } ``` ### Sessions #### Create Simulation Session ```http POST /api/simulation/sessions ``` **Request Body:** ```json { "scenarioId": "scenario_123", "settings": { "difficulty_adjustment": true, "ai_coaching_enabled": true, "real_time_feedback": true, "language": "fr" } } ``` **Response:** ```json { "success": true, "data": { "session_id": "session_789", "scenario_id": "scenario_123", "agent_id": "agent_456", "status": "active", "scenario": { "title": "Billing Issue Resolution", "customer_profile": { "name": "Marie Dupont", "tier": "gold", "issue": "Incorrect billing amount" } }, "initial_message": { "id": "msg_001", "sender": "customer", "content": "Bonjour, j'ai un problème avec ma facture de ce mois.", "timestamp": "2024-01-15T10:30:00Z", "metadata": { "sentiment": -0.3, "urgency": "medium" } }, "created_at": "2024-01-15T10:30:00Z" } } ``` #### Get Active Session ```http GET /api/simulation/sessions/active ``` **Response:** ```json { "success": true, "data": { "session_id": "session_789", "scenario": { "title": "Billing Issue Resolution" }, "status": "active", "messages": [ { "id": "msg_001", "sender": "customer", "content": "Bonjour, j'ai un problème avec ma facture.", "timestamp": "2024-01-15T10:30:00Z" } ], "performance_metrics": { "empathy_score": 78, "efficiency_score": 82, "accuracy_score": 75 } } } ``` #### Send Message ```http POST /api/simulation/sessions/{sessionId}/messages ``` **Request Body:** ```json { "message": "Bonjour Marie, je comprends votre préoccupation concernant votre facture. Permettez-moi de vérifier les détails de votre compte.", "timestamp": "2024-01-15T10:31:00Z" } ``` **Response:** ```json { "success": true, "data": { "message": { "id": "msg_002", "sender": "agent", "content": "Bonjour Marie, je comprends votre préoccupation...", "timestamp": "2024-01-15T10:31:00Z", "metadata": { "empathy_rating": 8, "response_time": 45, "accuracy_score": 85 } }, "customer_response": { "id": "msg_003", "sender": "customer", "content": "Merci, j'espère que vous pourrez m'aider.", "timestamp": "2024-01-15T10:31:30Z", "metadata": { "sentiment": 0.2, "satisfaction": 6 } }, "ai_suggestions": [ { "id": "suggestion_001", "content": "Je vais examiner votre facture en détail et identifier toute anomalie.", "confidence": 0.92, "category": "next_step" } ], "performance_update": { "empathy_score": 82, "efficiency_score": 85, "accuracy_score": 78 } } } ``` #### End Session ```http PUT /api/simulation/sessions/{sessionId}/end ``` **Request Body:** ```json { "reason": "completed" } ``` **Response:** ```json { "success": true, "data": { "session_id": "session_789", "final_metrics": { "overall_score": 82, "empathy_score": 85, "efficiency_score": 78, "accuracy_score": 84, "communication_score": 80, "duration_minutes": 12 }, "comprehensive_feedback": { "strengths": [ "Excellent empathy and understanding", "Clear communication style" ], "improvements": [ "Could respond faster to customer queries", "More specific technical explanations needed" ], "specific_examples": [ { "message_id": "msg_002", "feedback": "Great empathetic opening", "score": 9 } ] }, "badges_earned": [ { "id": "empathy_master", "name": "Empathy Master", "description": "Achieved 85+ empathy score", "rarity": "rare" } ], "agent_progress": { "total_sessions": 11, "completed_sessions": 9, "average_score": 79.2, "skill_improvements": { "empathy": 3.2, "efficiency": -1.1, "accuracy": 2.8 } } } } ``` ### Progress and History #### Get Agent Progress ```http GET /api/simulation/progress ``` **Response:** ```json { "success": true, "data": { "agent_id": "agent_456", "total_sessions": 15, "completed_sessions": 12, "average_score": 78.5, "skill_levels": { "empathy": 82, "efficiency": 75, "accuracy": 79, "communication": 81, "problem_solving": 73 }, "badges_earned": [ { "id": "first_session", "name": "First Steps", "description": "Completed first simulation", "rarity": "common", "earned_at": "2024-01-10T14:20:00Z" } ], "current_streak": 5, "best_streak": 8, "recent_performance": [ { "date": "2024-01-15", "score": 82, "sessions": 2 } ] } } ``` #### Get Session History ```http GET /api/simulation/history ``` **Query Parameters:** | Parameter | Type | Description | Default | |-----------|------|-------------|---------| | limit | integer | Number of sessions to return | 20 | | offset | integer | Number of sessions to skip | 0 | | status | string | Filter by status (completed, abandoned) | all | **Response:** ```json { "success": true, "data": [ { "_id": "session_789", "scenario": { "title": "Billing Issue Resolution", "difficulty": "intermediate" }, "status": "completed", "final_metrics": { "overall_score": 82, "duration_minutes": 12 }, "created_at": "2024-01-15T10:30:00Z", "completed_at": "2024-01-15T10:42:00Z" } ], "pagination": { "total": 12, "limit": 20, "offset": 0, "has_more": false } } ``` ## Predictive Analytics API ### Churn Predictions #### Get Churn Predictions ```http GET /api/predictive/churn ``` **Query Parameters:** | Parameter | Type | Description | Default | |-----------|------|-------------|---------| | risk_level | string | Filter by risk level (low, medium, high, critical) | all | | time_range | string | Time range (1h, 4h, 24h, 7d, 30d) | 24h | | customer_segment | string | Filter by customer segment | all | | limit | integer | Number of predictions | 100 | **Response:** ```json { "success": true, "data": { "predictions": [ { "customer_id": "customer_123", "churn_probability": 0.85, "risk_level": "high", "customer_tier": "gold", "customer_value": "high", "risk_factors": [ "billing_issues", "support_contacts", "usage_decline" ], "confidence": 0.92, "prediction_date": "2024-01-15T10:30:00Z", "recommended_actions": [ { "action": "proactive_outreach", "priority": "high", "estimated_impact": 0.3 } ] } ], "summary": { "total_predictions": 150, "high_risk_count": 23, "critical_risk_count": 5 }, "trends": { "trend_direction": "increasing", "change_percentage": 12.5, "period_comparison": "vs_last_week" } } } ``` ### Demand Forecasting #### Get Demand Forecast ```http GET /api/predictive/demand ``` **Query Parameters:** | Parameter | Type | Description | Default | |-----------|------|-------------|---------| | hours_ahead | integer | Hours to forecast (1-168) | 24 | | granularity | string | Forecast granularity (hourly, daily) | hourly | | include_confidence | boolean | Include confidence intervals | true | **Response:** ```json { "success": true, "data": { "forecast": [ { "timestamp": "2024-01-15T14:00:00Z", "predicted_volume": 45, "confidence_lower": 38, "confidence_upper": 52, "factors": { "seasonal": 1.2, "trend": 0.95, "events": 1.1 } } ], "staffing_recommendations": { "recommended_agents": 8, "peak_hours": ["14:00", "16:00", "20:00"], "minimum_coverage": 5 }, "capacity_metrics": { "current_capacity": 50, "utilization_forecast": 0.78, "overflow_risk": 0.15 } } } ``` ### Escalation Predictions #### Get Escalation Predictions ```http GET /api/predictive/escalation ``` **Query Parameters:** | Parameter | Type | Description | Default | |-----------|------|-------------|---------| | agent_id | string | Filter by agent ID | current_user | | risk_level | string | Filter by risk level | all | | time_range | string | Time range | 24h | **Response:** ```json { "success": true, "data": { "predictions": [ { "ticket_id": "ticket_456", "agent_id": "agent_123", "escalation_probability": 0.75, "risk_level": "high", "predicted_escalation_time": "2024-01-15T15:30:00Z", "risk_factors": [ "customer_frustration", "complex_issue", "multiple_contacts" ], "confidence": 0.88, "customer_sentiment": -0.6 } ], "prevention_strategies": [ { "strategy": "immediate_supervisor_notification", "priority": "high", "estimated_effectiveness": 0.7 } ], "intervention_recommendations": [ { "action": "escalate_proactively", "timing": "within_30_minutes", "reason": "High frustration detected" } ] } } ``` ## [FEATURE] Enhanced AI API ### Contextual Suggestions #### Generate Suggestions ```http POST /api/enhanced-ai/suggestions/contextual ``` **Request Body:** ```json { "conversation_context": { "conversation_history": [ { "sender": "customer", "content": "Ma facture est incorrecte", "timestamp": "2024-01-15T10:30:00Z" } ], "current_message": "Le montant semble trop élevé" }, "customer_profile": { "tier": "gold", "communication_style": "direct", "language": "fr" }, "urgency_level": "medium", "platform": "whatsapp" } ``` **Response:** ```json { "success": true, "data": { "suggestions": [ { "id": "suggestion_123", "content": "Je comprends votre préoccupation concernant le montant de votre facture. Permettez-moi de vérifier les détails de votre compte pour identifier toute anomalie.", "confidence": 0.92, "category": "empathetic_response", "reasoning": "Shows empathy and takes immediate action", "estimated_effectiveness": 0.85 } ], "context": { "urgency_level": "medium", "platform": "whatsapp", "customer_tier": "gold" }, "generated_at": "2024-01-15T10:31:00Z" } } ``` ### Sentiment Analysis #### Analyze Sentiment ```http POST /api/enhanced-ai/sentiment/analyze ``` **Request Body:** ```json { "messages": [ { "content": "Je suis vraiment frustré par ce service", "sender": "customer", "timestamp": "2024-01-15T10:30:00Z" } ], "conversation_id": "conv_123", "real_time": true } ``` **Response:** ```json { "success": true, "data": { "sentiment_analysis": { "current_sentiment": -0.7, "sentiment_trend": "declining", "confidence_score": 0.89, "emotions": { "frustration": 0.8, "anger": 0.3, "satisfaction": 0.1, "neutral": 0.2 } }, "escalation_risk": 0.75, "recommendations": [ { "action": "acknowledge_frustration", "priority": "high", "template": "Je comprends que cette situation soit frustrante pour vous..." } ], "analyzed_at": "2024-01-15T10:31:00Z" } } ``` ## Error Handling ### Error Response Format ```json { "success": false, "error": "Error message", "code": "ERROR_CODE", "errorId": "uuid-error-id", "timestamp": "2024-01-15T10:30:00Z", "details": { "field": "validation error details" } } ``` ### HTTP Status Codes | Code | Description | |------|-------------| | 200 | Success | | 201 | Created | | 400 | Bad Request | | 401 | Unauthorized | | 403 | Forbidden | | 404 | Not Found | | 409 | Conflict | | 429 | Too Many Requests | | 500 | Internal Server Error | | 502 | Bad Gateway | | 503 | Service Unavailable | ### Rate Limiting API endpoints are rate limited based on user role: | Role | Requests per 15 minutes | |------|------------------------| | Admin | 1000 | | Supervisor | 500 | | Agent | 200 | | Guest | 50 | Rate limit headers are included in responses: ```http X-RateLimit-Limit: 200 X-RateLimit-Remaining: 150 X-RateLimit-Reset: 1642248000 ```