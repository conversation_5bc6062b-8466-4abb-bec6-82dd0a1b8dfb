# [DESIG<PERSON>] ROADMAP FRONTEND - CHATBOT FREE MOBILE **Date :** 18 Juillet 2025 **Phase :** Frontend React Development **Backend Status :** [COMPLETE] Opérationnel (14/25 tests) --- ## [TARGET] **OBJECTIF GLOBAL FRONTEND** C<PERSON>er une **interface moderne et intuitive** pour le chatbot Free Mobile, permettant aux clients d'interagir naturellement avec leur espace client, gérer leur forfait, et obtenir un support instantané. --- ## [ARCHITECTURE] **ARCHITECTURE FRONTEND** ### **Stack Technologique** ```json { "core": { "framework": "React 18.2+", "language": "TypeScript 5.0+", "bundler": "Vite ou Create React App" }, "ui_design": { "components": "Material-UI v5 (thème Free Mobile)", "styling": "Emotion + CSS-in-JS", "responsive": "Mobile-first approach", "animations": "Framer Motion" }, "state_management": { "global": "Redux Toolkit + RTK Query", "local": "React Hooks (useState, useEffect)", "forms": "React Hook Form + Yup validation" }, "networking": { "http": "RTK Query (built on fetchBaseQuery)", "websocket": "Socket.IO Client v4", "offline": "PWA + Service Worker" }, "routing": { "spa": "React Router v6", "protected": "PrivateRoute component", "lazy": "React.lazy pour code splitting" } } ``` ### **Structure de Dossiers** ``` frontend/ ├── public/ │ ├── manifest.json # PWA configuration │ └── sw.js # Service Worker ├── src/ │ ├── components/ # Composants réutilisables │ │ ├── ui/ # Composants UI de base │ │ ├── chat/ # Interface chatbot │ │ ├── auth/ # Authentification │ │ └── dashboard/ # Dashboard client │ ├── pages/ # Pages principales │ │ ├── LoginPage.tsx │ │ ├── ChatPage.tsx │ │ ├── DashboardPage.tsx │ │ └── SubscriptionPage.tsx │ ├── hooks/ # Custom hooks │ │ ├── useAuth.ts │ │ ├── useSocket.ts │ │ └── useNotifications.ts │ ├── store/ # Redux store │ │ ├── slices/ # RTK slices │ │ └── api/ # RTK Query endpoints │ ├── services/ # Services externes │ │ ├── api.ts # Configuration axios/fetch │ │ └── socket.ts # Socket.IO setup │ ├── types/ # TypeScript definitions │ ├── utils/ # Utilitaires │ └── styles/ # Thèmes et styles globaux └── package.json ``` --- ## [MOBILE] **PHASE 1 : AUTHENTIFICATION** (Priorité 1) ### **Composants à Développer** #### **1. LoginForm Component** ```tsx // src/components/auth/LoginForm.tsx interface LoginFormProps { onSuccess: (user: User, token: string) => void; onError: (error: string) => void; } // Fonctionnalités - Validation email Free Mobile (@free.fr) - Mot de passe sécurisé avec confirmation - Loading states et error handling - Responsive design mobile-first - Intégration RTK Query mutation ``` #### **2. RegisterForm Component** ```tsx // src/components/auth/RegisterForm.tsx interface RegisterFormProps { onSuccess: (user: User) => void; } // Fonctionnalités - Champs: prénom, nom, email, téléphone, mot de passe - Validation temps réel (React Hook Form) - Termes et conditions Free Mobile - CAPTCHA ou vérification anti-bot - Création automatique profil client ``` #### **3. AuthProvider Context** ```tsx // src/context/AuthContext.tsx interface AuthContextType { user: User | null; token: string | null; login: (credentials: LoginCredentials) => Promise<void>; logout: () => void; isAuthenticated: boolean; isLoading: boolean; } // Fonctionnalités - Persistance token localStorage - Auto-refresh JWT avant expiration - Protection routes privées - Gestion déconnexion automatique ``` ### **Écrans d'Authentification** 1. **Page de Connexion** - Design épuré Free Mobile 2. **Page d'Inscription** - Onboarding nouveau client 3. **Mot de passe oublié** - Reset par email/SMS 4. **Écran de Chargement** - Vérification token au démarrage --- ## **PHASE 2 : INTERFACE CHAT** (Priorité 1) ### **Composants Chat Avancés** #### **1. ChatWindow Component** ```tsx // src/components/chat/ChatWindow.tsx interface ChatWindowProps { conversationId: string; onNewMessage: (message: Message) => void; } // Fonctionnalités - Historique messages scrollable - Auto-scroll vers nouveau message - Indicateur de frappe (typing...) - Messages temps réel via Socket.IO - Support Rich Messages (boutons, cartes) ``` #### **2. MessageBubble Component** ```tsx // src/components/chat/MessageBubble.tsx interface MessageBubbleProps { message: Message; isUser: boolean; richContent?: RichMessageContent; } // Types de Messages - Texte simple utilisateur/bot - Quick Reply buttons - Carousel cards (forfaits) - Graphiques consommation - Liens vers espace client ``` #### **3. MessageInput Component** ```tsx // src/components/chat/MessageInput.tsx interface MessageInputProps { onSend: (content: string) => void; disabled?: boolean; placeholder?: string; } // Fonctionnalités - Saisie multiligne avec auto-resize - Bouton envoi/mic pour dictée vocale - Suggestions prédictives - Émojis Free Mobile personnalisés - Upload fichiers (factures, captures) ``` #### **4. QuickActions Component** ```tsx // src/components/chat/QuickActions.tsx const QUICK_ACTIONS = [ "[ANALYTICS] Ma consommation", " Ma facture", "[MOBILE] Changer forfait", " Problème réseau", "[USER] Parler à un agent" ]; ``` ### **Fonctionnalités Chat Métier Free Mobile** #### **Messages Prédéfinis** ```javascript // Templates messages bot Free Mobile { "welcome": "Bonjour ! Je suis votre assistant Free Mobile [AI]", "consumption": "Voici votre consommation actuelle...", "billing": "Votre facture du mois est disponible...", "network_issue": "Je vais diagnostiquer votre problème réseau...", "plan_change": "Quel forfait vous intéresse ?", "escalation": "Je vous met en relation avec un conseiller..." } ``` #### **Rich Messages Free Mobile** ```tsx // Composants spécialisés - PlanCarousel: Présentation forfaits 2€/100GB/5G - ConsumptionChart: Graphique data/appels/SMS - BillingCard: Résumé facture avec bouton paiement - NetworkDiagnostic: Test débit/couverture temps réel - AgentHandoff: Transition vers conseiller humain ``` --- ## [ANALYTICS] **PHASE 3 : DASHBOARD CLIENT** (Priorité 2) ### **Écrans Dashboard** #### **1. Vue d'Ensemble** ```tsx // src/pages/DashboardPage.tsx interface DashboardData { subscription: SubscriptionInfo; consumption: ConsumptionMetrics; billing: BillingInfo; notifications: NotificationItem[]; } // Widgets - Résumé forfait actuel - Consommation temps réel (progressbars) - Prochaine facture + montant - Alertes importantes - Accès rapide chat support ``` #### **2. Gestion Forfait** ```tsx // src/components/dashboard/PlanManager.tsx // Fonctionnalités - Visualisation forfait actuel détaillé - Simulateur changement forfait - Historique modifications - Options activées/désactivées - Calculateur économies potentielles ``` #### **3. Suivi Consommation** ```tsx // src/components/dashboard/ConsumptionTracker.tsx // Graphiques - Évolution data sur 30 jours - Répartition appels/SMS/data - Prédiction dépassement forfait - Comparaison mois précédent - Alertes seuils personnalisés ``` #### **4. Historique Factures** ```tsx // src/components/dashboard/BillingHistory.tsx // Fonctionnalités - Liste factures 12 derniers mois - Téléchargement PDF - Détail postes facturation - Graphique évolution montants - Export données comptabilité ``` --- ## **PHASE 4 : NOTIFICATIONS** (Priorité 3) ### **Système Notifications Complet** #### **1. NotificationCenter Component** ```tsx // src/components/notifications/NotificationCenter.tsx interface NotificationCenterProps { notifications: Notification[]; onMarkAsRead: (id: string) => void; onClearAll: () => void; } // Types notifications Free Mobile - Consommation: "90% data utilisée" - Facturation: "Facture disponible" - Réseau: "Maintenance réseau prévue" - Commerciale: "Nouvelle offre 5G" - Support: "Réponse conseiller" ``` #### **2. Push Notifications PWA** ```typescript // src/services/notificationService.ts class NotificationService { async requestPermission(): Promise<boolean>; async subscribeToPush(): Promise<void>; showLocalNotification(title: string, body: string): void; scheduleNotification(date: Date, content: NotificationContent): void; } ``` #### **3. AlertBanner Component** ```tsx // Alertes en temps réel - Dépassement forfait imminent - Problème réseau détecté - Paiement en attente - Nouveau message support - Maintenance en cours ``` --- ## **PHASE 5 : INTERFACE AGENT** (Priorité 4) ### **Dashboard Conseillers Free Mobile** #### **1. AgentDashboard** ```tsx // Vue d'ensemble conversations - Liste conversations actives - File d'attente clients - Métriques temps réponse - Statut disponibilité agent - Outils escalade rapide ``` #### **2. CustomerProfilePanel** ```tsx // Profil client détaillé - Historique forfaits - Consommation patterns - Tickets support précédents - Scoring satisfaction - Notes internes agent ``` #### **3. ConversationTools** ```tsx // Outils conseiller - Templates réponses fréquentes - Accès base connaissance - Calculateur forfaits - Outils diagnostic réseau - Escalade vers technique ``` --- ## [DEPLOY] **SETUP TECHNIQUE INITIAL** ### **1. Création Projet** ```bash # Option 1: Create React App + TypeScript npx create-react-app frontend --template typescript cd frontend # Option 2: Vite (plus rapide) npm create vite@latest frontend -- --template react-ts cd frontend ``` ### **2. Installation Dépendances** ```bash # Core dependencies npm install @mui/material @emotion/react @emotion/styled npm install @reduxjs/toolkit react-redux npm install react-router-dom@6 npm install socket.io-client npm install react-hook-form @hookform/resolvers yup # Dev dependencies npm install -D @types/node npm install -D eslint-plugin-react-hooks npm install -D prettier eslint-config-prettier ``` ### **3. Configuration de Base** ```typescript // src/config/api.ts export const API_CONFIG = { baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api', timeout: 10000, headers: { 'Content-Type': 'application/json', } }; // src/config/socket.ts export const SOCKET_CONFIG = { url: process.env.REACT_APP_SOCKET_URL || 'http://localhost:5000', options: { autoConnect: false, transports: ['websocket'] } }; ``` --- ## **CRITÈRES DE SUCCÈS FRONTEND** ### **Phase 1 - Authentification (Succès = 100%)** - [COMPLETE] Connexion/déconnexion fluide - [COMPLETE] Persistance session utilisateur - [COMPLETE] Validation formulaires temps réel - [COMPLETE] Gestion erreurs UX/UI ### **Phase 2 - Chat (Succès = 100%)** - [COMPLETE] Messages temps réel bidirectionnels - [COMPLETE] Rich Messages interactifs - [COMPLETE] Interface responsive mobile/desktop - [COMPLETE] Intégration complète API backend ### **Phase 3 - Dashboard (Succès = 80%)** - [COMPLETE] Visualisation données Free Mobile - [COMPLETE] Graphiques consommation interactifs - [COMPLETE] Gestion forfait complète - [COMPLETE] Export/téléchargement factures ### **Phase 4 - Notifications (Succès = 70%)** - [COMPLETE] Centre notifications fonctionnel - [COMPLETE] Push notifications PWA - [COMPLETE] Alertes temps réel ### **Phase 5 - Agent (Succès = 60%)** - [COMPLETE] Dashboard agents opérationnel - [COMPLETE] Outils support efficaces --- ## [DESIGN] **DESIGN SYSTEM FREE MOBILE** ### **Palette Couleurs** ```css :root { /* Couleurs principales Free */ --free-red: #d40e14; --free-dark: #1a1a1a; --free-white: #ffffff; /* Couleurs secondaires */ --free-gray-100: #f5f5f5; --free-gray-300: #e0e0e0; --free-gray-500: #9e9e9e; --free-gray-700: #424242; /* États */ --success: #4caf50; --warning: #ff9800; --error: #f44336; --info: #2196f3; } ``` ### **Typography** ```css /* Fonts Free Mobile */ @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap'); .typography { font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif; /* Tailles */ --text-xs: 0.75rem; /* 12px */ --text-sm: 0.875rem; /* 14px */ --text-base: 1rem; /* 16px */ --text-lg: 1.125rem; /* 18px */ --text-xl: 1.25rem; /* 20px */ --text-2xl: 1.5rem; /* 24px */ --text-3xl: 1.875rem; /* 30px */ } ``` --- ## [MOBILE] **PWA CONFIGURATION** ### **Manifest.json** ```json { "name": "Free Mobile Assistant", "short_name": "Free Mobile", "description": "Assistant chatbot pour clients Free Mobile", "start_url": "/", "display": "standalone", "background_color": "#1a1a1a", "theme_color": "#d40e14", "icons": [ { "src": "/icons/icon-192.png", "sizes": "192x192", "type": "image/png" }, { "src": "/icons/icon-512.png", "sizes": "512x512", "type": "image/png" } ], "categories": ["utilities", "productivity"], "orientation": "portrait-primary" } ``` --- ## **INTÉGRATION BACKEND-FRONTEND** ### **API Endpoints Mapping** ```typescript // src/store/api/freeMobileApi.ts export const freeMobileApi = createApi({ reducerPath: 'freeMobileApi', baseQuery: fetchBaseQuery({ baseUrl: '/api', prepareHeaders: (headers, { getState }) => { const token = (getState() as RootState).auth.token; if (token) { headers.set('authorization', `Bearer ${token}`); } return headers; }, }), tagTypes: ['User', 'Conversation', 'Subscription', 'Notification'], endpoints: (builder) => ({ // Auth endpoints login: builder.mutation<LoginResponse, LoginRequest>({ query: (credentials) => ({ url: 'auth/login', method: 'POST', body: credentials, }), }), // Chat endpoints startConversation: builder.mutation<ConversationResponse, void>({ query: () => ({ url: 'chat/conversations/start', method: 'POST', }), }), sendMessage: builder.mutation<MessageResponse, SendMessageRequest>({ query: (messageData) => ({ url: 'chat/messages/send', method: 'POST', body: messageData, }), }), // Subscription endpoints getAvailablePlans: builder.query<Plan[], void>({ query: () => 'subscription/plans/available', }), getCurrentPlan: builder.query<SubscriptionInfo, void>({ query: () => 'subscription/plans/current', }), // Notifications endpoints getNotifications: builder.query<Notification[], void>({ query: () => 'notifications', }), }), }); ``` ### **Socket.IO Integration** ```typescript // src/hooks/useSocket.ts export const useSocket = () => { const { user } = useAuth(); const socket = useRef<Socket>(); useEffect(() => { if (user) { socket.current = io(SOCKET_CONFIG.url, SOCKET_CONFIG.options); // Authentification socket socket.current.emit('authenticate', user.id); // Event listeners socket.current.on('newMessage', handleNewMessage); socket.current.on('notification', handleNotification); socket.current.on('agentAssigned', handleAgentAssigned); return () => { socket.current?.disconnect(); }; } }, [user]); return { socket: socket.current, emit: (event: string, data: any) => socket.current?.emit(event, data), }; }; ``` --- ## [TARGET] **ROADMAP TIMELINE** ### **Semaine 1-2 : Foundation** - [COMPLETE] Setup projet + dépendances - [COMPLETE] Configuration Redux + Router - [COMPLETE] Design system de base - [COMPLETE] Authentification complète ### **Semaine 3-4 : Chat Interface** - [COMPLETE] Interface chat responsive - [COMPLETE] Messages temps réel - [COMPLETE] Rich Messages Free Mobile - [COMPLETE] Intégration Socket.IO ### **Semaine 5-6 : Dashboard Client** - [COMPLETE] Vue d'ensemble compte - [COMPLETE] Gestion forfaits/options - [COMPLETE] Graphiques consommation - [COMPLETE] Historique factures ### **Semaine 7-8 : Polish & PWA** - [COMPLETE] Notifications push - [COMPLETE] Interface agent (basique) - [COMPLETE] PWA configuration - [COMPLETE] Tests + optimisations --- **[DEPLOY] Objectif Final :** Interface moderne, intuitive et complètement fonctionnelle pour le chatbot Free Mobile, prête pour la production et l'utilisation par les vrais clients Free.