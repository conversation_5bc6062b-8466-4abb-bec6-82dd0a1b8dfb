/** * Optimized MongoDB Connection Manager for Vercel Serverless Functions * Handles connection pooling, caching, and error recovery */ import { MongoClient, ServerApiVersion } from 'mongodb'; const MONGODB_URI = process.env.MONGODB_URI; const DATABASE_NAME = process.env.DATABASE_NAME || 'freemobile_chatbot_prod'; if (!MONGODB_URI) { throw new Error('Please define the MONGODB_URI environment variable inside .env.local'); } /** * Global is used here to maintain a cached connection across hot reloads * in development. This prevents connections growing exponentially * during API Route usage. */ let cached = global.mongo; if (!cached) { cached = global.mongo = { conn: null, promise: null }; } const clientOptions = { serverApi: { version: ServerApiVersion.v1, strict: true, deprecationErrors: true, }, maxPoolSize: 10, // Maintain up to 10 socket connections serverSelectionTimeoutMS: 5000, // Keep trying to send operations for 5 seconds socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity family: 4, // Use IPv4, skip trying IPv6 bufferMaxEntries: 0, // Disable mongoose buffering bufferCommands: false, // Disable mongoose buffering maxIdleTimeMS: 30000, // Close connections after 30 seconds of inactivity retryWrites: true, retryReads: true, compressors: ['zlib'], // Enable compression }; export async function connectToDatabase() { if (cached.conn) { return cached.conn; } if (!cached.promise) { cached.promise = MongoClient.connect(MONGODB_URI, clientOptions) .then((client) => { console.log('[COMPLETE] Connected to MongoDB Atlas'); return { client, db: client.db(DATABASE_NAME), }; }) .catch((error) => { console.error('[FAILED] MongoDB connection failed:', error); cached.promise = null; // Reset promise on failure throw error; }); } try { cached.conn = await cached.promise; } catch (error) { cached.promise = null; throw error; } return cached.conn; } /** * Get database instance */ export async function getDatabase() { const { db } = await connectToDatabase(); return db; } /** * Get specific collection with error handling */ export async function getCollection(collectionName) { try { const db = await getDatabase(); return db.collection(collectionName); } catch (error) { console.error(`[FAILED] Failed to get collection ${collectionName}:`, error); throw error; } } /** * Execute database operation with retry logic */ export async function executeWithRetry(operation, maxRetries = 3) { let lastError; for (let attempt = 1; attempt <= maxRetries; attempt++) { try { return await operation(); } catch (error) { lastError = error; console.warn(` Database operation failed (attempt ${attempt}/${maxRetries}):`, error.message); if (attempt === maxRetries) { break; } // Reset connection on certain errors if (error.name === 'MongoNetworkError' || error.name === 'MongoTimeoutError') { cached.conn = null; cached.promise = null; } // Wait before retry (exponential backoff) await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000)); } } throw lastError; } /** * Health check for database connection */ export async function healthCheck() { try { const { client } = await connectToDatabase(); await client.db('admin').command({ ping: 1 }); return { status: 'healthy', timestamp: new Date().toISOString() }; } catch (error) { console.error('[FAILED] Database health check failed:', error); return { status: 'unhealthy', error: error.message, timestamp: new Date().toISOString() }; } } /** * Close database connection (for cleanup) */ export async function closeConnection() { if (cached.conn) { await cached.conn.client.close(); cached.conn = null; cached.promise = null; console.log(' MongoDB connection closed'); } } /** * Collection-specific helpers */ export const Collections = { // User Management USERS: 'users', USER_SESSIONS: 'user_sessions', // Simulation Training SIMULATION_SCENARIOS: 'simulation_scenarios', SIMULATION_SESSIONS: 'simulation_sessions', AGENT_PROGRESS: 'agent_progress', GAMIFICATION: 'gamification', // Predictive Analytics CHURN_PREDICTIONS: 'churn_predictions', DEMAND_FORECASTS: 'demand_forecasts', ESCALATION_RISKS: 'escalation_risks', ANOMALIES: 'anomalies', // AI Assistance AI_SUGGESTIONS: 'ai_suggestions', SENTIMENT_ANALYSIS: 'sentiment_analysis', AGENT_PERSONALIZATION: 'agent_personalization', KB_INTERACTIONS: 'kb_interactions', // Analytics PERFORMANCE_METRICS: 'performance_metrics', REALTIME_KPIS: 'realtime_kpis', DASHBOARD_CONFIGS: 'dashboard_configs', REPORT_TEMPLATES: 'report_templates', // System SYSTEM_LOGS: 'system_logs', API_USAGE: 'api_usage', SYSTEM_HEALTH: 'system_health', }; /** * Common database operations with error handling */ export class DatabaseOperations { static async findOne(collectionName, query, options = {}) { return executeWithRetry(async () => { const collection = await getCollection(collectionName); return collection.findOne(query, options); }); } static async find(collectionName, query, options = {}) { return executeWithRetry(async () => { const collection = await getCollection(collectionName); return collection.find(query, options).toArray(); }); } static async insertOne(collectionName, document) { return executeWithRetry(async () => { const collection = await getCollection(collectionName); return collection.insertOne({ ...document, createdAt: new Date(), updatedAt: new Date(), }); }); } static async insertMany(collectionName, documents) { return executeWithRetry(async () => { const collection = await getCollection(collectionName); const now = new Date(); const documentsWithTimestamps = documents.map(doc => ({ ...doc, createdAt: now, updatedAt: now, })); return collection.insertMany(documentsWithTimestamps); }); } static async updateOne(collectionName, filter, update, options = {}) { return executeWithRetry(async () => { const collection = await getCollection(collectionName); return collection.updateOne(filter, { ...update, $set: { ...update.$set, updatedAt: new Date(), }, }, options); }); } static async updateMany(collectionName, filter, update, options = {}) { return executeWithRetry(async () => { const collection = await getCollection(collectionName); return collection.updateMany(filter, { ...update, $set: { ...update.$set, updatedAt: new Date(), }, }, options); }); } static async deleteOne(collectionName, filter) { return executeWithRetry(async () => { const collection = await getCollection(collectionName); return collection.deleteOne(filter); }); } static async deleteMany(collectionName, filter) { return executeWithRetry(async () => { const collection = await getCollection(collectionName); return collection.deleteMany(filter); }); } static async aggregate(collectionName, pipeline, options = {}) { return executeWithRetry(async () => { const collection = await getCollection(collectionName); return collection.aggregate(pipeline, options).toArray(); }); } static async countDocuments(collectionName, filter = {}) { return executeWithRetry(async () => { const collection = await getCollection(collectionName); return collection.countDocuments(filter); }); } } export default { connectToDatabase, getDatabase, getCollection, executeWithRetry, healthCheck, closeConnection, Collections, DatabaseOperations, };