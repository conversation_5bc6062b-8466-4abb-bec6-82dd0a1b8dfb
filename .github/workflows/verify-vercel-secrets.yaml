name: Verify Vercel Secrets

on:
  workflow_dispatch:

jobs:
  verify:
    name: Verify required Vercel secrets exist
    runs-on: ubuntu-latest
    steps:
      - name: Check Vercel secrets presence
        shell: bash
        run: |
          check() { if [ -z "$1" ]; then echo "::error::Missing required secret: $2"; exit 1; else echo "✅ Present: $2"; fi; }
          check "${{ secrets.VERCEL_TOKEN }}" "VERCEL_TOKEN"
          check "${{ secrets.VERCEL_ORG_ID }}" "VERCEL_ORG_ID"
          check "${{ secrets.VERCEL_PROJECT_ID }}" "VERCEL_PROJECT_ID"
      - name: Summary
        run: |
          echo "All required Vercel secrets are present." >> $GITHUB_STEP_SUMMARY

