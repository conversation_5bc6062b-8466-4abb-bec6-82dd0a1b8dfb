name: Automated Production Release

on:
  workflow_dispatch:
    inputs:
      release_id:
        description: 'Optional release/tag identifier (for summary only)'
        required: false
        default: ''

permissions:
  contents: read
  security-events: write

# Use project subdirectory containing vercel.json
defaults:
  run:
    working-directory: free-mobile-chatbot

env:
  DOMAIN: chatbotrncp.vercel.app
  BASE_URL: https://chatbotrncp.vercel.app

jobs:
  verify-secrets:
    name: Phase 1 • Verify Vercel Secrets
    runs-on: ubuntu-latest
    outputs:
      ok: ${{ steps.result.outputs.ok }}
    steps:
      - name: Validate required secrets
        id: result
        shell: bash
        run: |
          ok=true
          check() { if [ -z "$1" ]; then echo "::error::Missing required secret: $2"; ok=false; else echo "✅ Present: $2"; fi; }
          check "${{ secrets.VERCEL_TOKEN }}" "VERCEL_TOKEN"
          check "${{ secrets.VERCEL_ORG_ID }}" "VERCEL_ORG_ID"
          check "${{ secrets.VERCEL_PROJECT_ID }}" "VERCEL_PROJECT_ID"
          echo "ok=$ok" >> $GITHUB_OUTPUT

      - name: Summary
        if: always()
        run: |
          echo "## Phase 1: Secret Verification" >> $GITHUB_STEP_SUMMARY
          echo "- VERCEL_TOKEN: $([ -n "${{ secrets.VERCEL_TOKEN }}" ] && echo OK || echo MISSING)" >> $GITHUB_STEP_SUMMARY
          echo "- VERCEL_ORG_ID: $([ -n "${{ secrets.VERCEL_ORG_ID }}" ] && echo OK || echo MISSING)" >> $GITHUB_STEP_SUMMARY
          echo "- VERCEL_PROJECT_ID: $([ -n "${{ secrets.VERCEL_PROJECT_ID }}" ] && echo OK || echo MISSING)" >> $GITHUB_STEP_SUMMARY

  deploy-production:
    name: Phase 2 • Deploy to Production
    runs-on: ubuntu-latest
    needs: verify-secrets
    if: needs.verify-secrets.outputs.ok == 'true'
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          submodules: recursive
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Verify submodule initialization
        working-directory: .
        run: |
          echo "Checking submodule status..."
          git submodule status
          echo "Verifying package.json files exist..."
          ls -la free-mobile-chatbot/package.json
          ls -la free-mobile-chatbot/frontend/package.json
          ls -la free-mobile-chatbot/backend/package.json
          echo "Current working directory:"
          pwd
          echo "Submodule directory contents:"
          ls -la free-mobile-chatbot/

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'

      - name: Install Vercel CLI
        run: npm install --global vercel

      - name: Pull Vercel environment (production)
        id: pull
        env:
          VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}
        run: |
          set -euo pipefail
          vercel pull --yes --environment=production --token="$VERCEL_TOKEN"

      - name: Build project (prod)
        id: build
        env:
          VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}
        run: |
          set -euo pipefail
          vercel build --prod --token="$VERCEL_TOKEN"

      - name: Deploy to production
        id: deploy
        env:
          VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}
        run: |
          set -euo pipefail
          vercel deploy --prebuilt --prod --token="$VERCEL_TOKEN" | tee deploy.log
          # Extract URL if present
          url=$(grep -Eo 'https://[^ ]+\.vercel\.app' deploy.log | tail -1 || true)
          echo "url=${url}" >> $GITHUB_OUTPUT

      - name: Append deployment summary
        if: always()
        run: |
          echo "## Phase 2: Deployment" >> $GITHUB_STEP_SUMMARY
          echo "- Release ID: ${{ github.event.inputs.release_id }}" >> $GITHUB_STEP_SUMMARY
          echo "- Deployment URL (if provided): ${{ steps.deploy.outputs.url }}" >> $GITHUB_STEP_SUMMARY

  post-validate:
    name: Phase 3 • Post-Deployment Validation
    runs-on: ubuntu-latest
    needs: deploy-production
    steps:
      - name: Health checks
        shell: bash
        run: |
          set -euo pipefail
          BASE_URL=${BASE_URL}
          echo "Checking: $BASE_URL" | tee -a summary.txt
          curl -fsSL -o /dev/null "$BASE_URL" && echo "Homepage: OK" | tee -a summary.txt
          # Try common health endpoints
          ok=false
          for path in \
            "/api/health" \
            "/api/system/health" \
            "/api/status" \
            "/api/system/status"; do
            if curl -fsS "$BASE_URL$path" -o /dev/null; then
              echo "Health endpoint $path: OK" | tee -a summary.txt
              ok=true
            else
              echo "Health endpoint $path: not responding" | tee -a summary.txt
            fi
          done
          if [ "$ok" != "true" ]; then
            echo "::warning::No standard health endpoint responded with 200";
          fi

      - name: Core API probes (non-fatal)
        continue-on-error: true
        run: |
          set -euo pipefail
          BASE_URL=${BASE_URL}
          # Add lightweight probes here if needed
          echo "Probes executed against $BASE_URL" | tee -a summary.txt

      - name: Summary
        run: |
          echo "## Phase 3: Validation" >> $GITHUB_STEP_SUMMARY
          cat summary.txt >> $GITHUB_STEP_SUMMARY

