name: Vercel Production Deployment

on:
  push:
    branches:
      - main
  workflow_dispatch:

permissions:
  contents: read

# Run Vercel commands from the project subdirectory
defaults:
  run:
    working-directory: free-mobile-chatbot

env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}
  NODE_VERSION: '20.x'

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          submodules: recursive
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Verify submodule initialization
        working-directory: .
        run: |
          echo "Checking submodule status..."
          git submodule status
          echo "Verifying package.json files exist..."
          ls -la free-mobile-chatbot/package.json
          ls -la free-mobile-chatbot/frontend/package.json
          ls -la free-mobile-chatbot/backend/package.json
          echo "Current working directory:"
          pwd
          echo "Submodule directory contents:"
          ls -la free-mobile-chatbot/

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Cache root dependencies
        uses: actions/cache@v3
        with:
          path: free-mobile-chatbot/node_modules
          key: ${{ runner.os }}-npm-root-${{ hashFiles('free-mobile-chatbot/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-npm-root-

      - name: Cache frontend dependencies
        uses: actions/cache@v3
        with:
          path: free-mobile-chatbot/frontend/node_modules
          key: ${{ runner.os }}-npm-frontend-${{ hashFiles('free-mobile-chatbot/frontend/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-npm-frontend-

      - name: Cache backend dependencies
        uses: actions/cache@v3
        with:
          path: free-mobile-chatbot/backend/node_modules
          key: ${{ runner.os }}-npm-backend-${{ hashFiles('free-mobile-chatbot/backend/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-npm-backend-

      - name: Install dependencies
        run: npm ci

      - name: Install frontend dependencies
        working-directory: free-mobile-chatbot/frontend
        run: npm ci

      - name: Install backend dependencies
        working-directory: free-mobile-chatbot/backend
        run: npm ci

      - name: Run backend tests
        working-directory: free-mobile-chatbot/backend
        run: npm test
        env:
          CI: true

      - name: Run frontend tests
        working-directory: free-mobile-chatbot/frontend
        run: npm test -- --coverage --watchAll=false
        env:
          CI: true

      - name: Build frontend
        working-directory: free-mobile-chatbot/frontend
        run: npm run build
        env:
          CI: false
          GENERATE_SOURCEMAP: false

  deploy-production:
    name: Deploy to Vercel (Production)
    runs-on: ubuntu-latest
    needs: test
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          submodules: recursive
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Cache dependencies
        uses: actions/cache@v3
        with:
          path: free-mobile-chatbot/node_modules
          key: ${{ runner.os }}-npm-deploy-${{ hashFiles('free-mobile-chatbot/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-npm-deploy-

      - name: Validate required secrets
        run: |
          test -n "${{ secrets.VERCEL_TOKEN }}" || (echo "Missing secret: VERCEL_TOKEN" && exit 1)
          test -n "${{ secrets.VERCEL_ORG_ID }}" || (echo "Missing secret: VERCEL_ORG_ID (e.g. team_xxxxx)" && exit 1)
          test -n "${{ secrets.VERCEL_PROJECT_ID }}" || (echo "Missing secret: VERCEL_PROJECT_ID (e.g. prj_xxxxx)" && exit 1)

      - name: Install Vercel CLI
        run: npm install --global vercel@latest

      - name: Pull Vercel environment (production)
        run: vercel pull --yes --environment=production --token=${{ secrets.VERCEL_TOKEN }}
        env:
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

      - name: Build project (prebuilt)
        run: vercel build --prod --token=${{ secrets.VERCEL_TOKEN }}
        env:
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

      - name: Deploy to production
        id: deploy
        run: |
          url=$(vercel deploy --prebuilt --prod --token=${{ secrets.VERCEL_TOKEN }})
          echo "deployment-url=$url" >> $GITHUB_OUTPUT
        env:
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

      - name: Deployment summary
        run: |
          echo "## Production Deployment Successful! 🚀" >> $GITHUB_STEP_SUMMARY
          echo "- **Deployment URL**: ${{ steps.deploy.outputs.deployment-url }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Environment**: Production" >> $GITHUB_STEP_SUMMARY
          echo "- **Commit**: ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
