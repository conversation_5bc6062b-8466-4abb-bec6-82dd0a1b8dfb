name: Vercel Preview Deployment

on:
  push:
    branches-ignore:
      - main
      - develop
  pull_request:
    branches:
      - main
      - develop

permissions:
  contents: read

defaults:
  run:
    working-directory: free-mobile-chatbot

env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}
  NODE_VERSION: '20.x'

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          submodules: recursive
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Verify submodule initialization
        working-directory: .
        run: |
          echo "Checking submodule status..."
          git submodule status
          echo "Verifying package.json files exist..."
          ls -la free-mobile-chatbot/package.json
          ls -la free-mobile-chatbot/frontend/package.json
          ls -la free-mobile-chatbot/backend/package.json
          echo "Current working directory:"
          pwd
          echo "Submodule directory contents:"
          ls -la free-mobile-chatbot/

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Cache root dependencies
        uses: actions/cache@v3
        with:
          path: free-mobile-chatbot/node_modules
          key: ${{ runner.os }}-npm-root-${{ hashFiles('free-mobile-chatbot/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-npm-root-

      - name: Cache frontend dependencies
        uses: actions/cache@v3
        with:
          path: free-mobile-chatbot/frontend/node_modules
          key: ${{ runner.os }}-npm-frontend-${{ hashFiles('free-mobile-chatbot/frontend/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-npm-frontend-

      - name: Cache backend dependencies
        uses: actions/cache@v3
        with:
          path: free-mobile-chatbot/backend/node_modules
          key: ${{ runner.os }}-npm-backend-${{ hashFiles('free-mobile-chatbot/backend/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-npm-backend-

      - name: Install dependencies
        working-directory: free-mobile-chatbot
        run: npm ci

      - name: Install frontend dependencies
        working-directory: free-mobile-chatbot/frontend
        run: npm ci

      - name: Install backend dependencies
        working-directory: free-mobile-chatbot/backend
        run: npm ci

      - name: Run backend tests
        working-directory: free-mobile-chatbot/backend
        run: npm test
        env:
          CI: true

      - name: Run frontend tests
        working-directory: free-mobile-chatbot/frontend
        run: npm test -- --coverage --watchAll=false
        env:
          CI: true

      - name: Build frontend
        working-directory: free-mobile-chatbot/frontend
        run: npm run build
        env:
          CI: false
          GENERATE_SOURCEMAP: false

  deploy-preview:
    name: Deploy Preview
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          submodules: recursive
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install Vercel CLI
        run: npm install --global vercel@latest

      - name: Pull Vercel Environment Information
        run: vercel pull --yes --environment=preview --token=${{ secrets.VERCEL_TOKEN }}
        env:
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

      - name: Build Project Artifacts
        run: vercel build --token=${{ secrets.VERCEL_TOKEN }}
        env:
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

      - name: Deploy Project Artifacts
        id: deploy
        run: |
          url=$(vercel deploy --prebuilt --token=${{ secrets.VERCEL_TOKEN }})
          echo "preview-url=$url" >> $GITHUB_OUTPUT
        env:
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

      - name: Comment PR
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `## 🚀 Preview Deployment\n\n**Preview URL**: ${{ steps.deploy.outputs.preview-url }}\n\n*Deployed from commit: ${{ github.sha }}*`
            })
