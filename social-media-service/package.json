{"name": "freemobile-social-media-service", "version": "1.0.0", "description": "Social Media Integration Service for Free Mobile Chatbot - Multi-platform messaging and conversation management", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "build": "tsc", "build:watch": "tsc --watch", "webhook:test": "node scripts/test-webhooks.js"}, "keywords": ["social-media", "whatsapp", "facebook", "instagram", "twitter", "linkedin", "messaging", "chatbot", "free-mobile", "customer-service"], "author": "Free Mobile DevOps Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "socket.io": "^4.7.4", "redis": "^4.6.10", "mongodb": "^6.3.0", "mongoose": "^8.0.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "winston": "^3.11.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "uuid": "^9.0.1", "moment": "^2.29.4", "axios": "^1.6.2", "crypto": "^1.0.1", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "whatsapp-web.js": "^1.23.0", "qrcode": "^1.5.3", "facebook-nodejs-business-sdk": "^19.0.0", "twitter-api-v2": "^1.15.1", "linkedin-api-client": "^1.0.0", "node-cron": "^3.0.3", "bull": "^4.12.2", "ioredis": "^5.3.2", "cheerio": "^1.0.0-rc.12", "mime-types": "^2.1.35", "form-data": "^4.0.0", "node-fetch": "^3.3.2", "ws": "^8.14.2"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.54.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "typescript": "^5.3.2", "@types/node": "^20.9.0", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "ngrok": "^5.0.0-beta.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/freemobile/chatbot-dashboard.git", "directory": "social-media-service"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/server.js"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}, "nodemonConfig": {"watch": ["src"], "ext": "js,json", "ignore": ["src/**/*.test.js"], "exec": "node src/server.js"}}