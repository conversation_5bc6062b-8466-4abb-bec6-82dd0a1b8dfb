# =============================================
# 📱 SOCIAL MEDIA SERVICE DOCKERFILE
# Multi-stage build for social media integration
# WhatsApp, Facebook, Instagram, Twitter support
# =============================================

# Build stage
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Install build dependencies
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git \
    curl

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy source code
COPY . .

# Production stage
FROM node:18-alpine AS production

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S socialmedia -u 1001 -G nodejs

# Set working directory
WORKDIR /app

# Install production dependencies only
RUN apk add --no-cache \
    dumb-init \
    curl \
    tzdata \
    imagemagick \
    ffmpeg \
    && rm -rf /var/cache/apk/*

# Copy built application from builder stage
COPY --from=builder --chown=socialmedia:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=socialmedia:nodejs /app/package*.json ./
COPY --chown=socialmedia:nodejs . .

# Create necessary directories
RUN mkdir -p /app/logs /app/uploads /app/temp /app/media && \
    chown -R socialmedia:nodejs /app

# Set environment variables
ENV NODE_ENV=production
ENV PORT=5010
ENV TZ=Europe/Paris

# Platform-specific environment variables
ENV WHATSAPP_WEBHOOK_VERIFY_TOKEN=""
ENV FACEBOOK_PAGE_ACCESS_TOKEN=""
ENV INSTAGRAM_ACCESS_TOKEN=""
ENV TWITTER_BEARER_TOKEN=""

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:5010/health || exit 1

# Switch to non-root user
USER socialmedia

# Expose port
EXPOSE 5010

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start application
CMD ["node", "src/server.js"]

# Labels for metadata
LABEL maintainer="Free Mobile Chatbot Team"
LABEL version="1.0.0"
LABEL description="Social Media Service for Free Mobile Chatbot - Multi-platform Integration"
LABEL org.opencontainers.image.source="https://github.com/free-mobile/chatbot"
