/** * ============================================= * PLATFORM NORMALIZER SERVICE * Message format normalization across different social media platforms * Converts platform-specific formats to unified message structure * ============================================= */ const logger = require('../utils/logger'); const moment = require('moment'); class PlatformNormalizerService { constructor() { this.isInitialized = false; // Platform-specific configurations this.platformConfigs = { whatsapp: { maxTextLength: 4096, supportedMediaTypes: ['image', 'audio', 'video', 'document'], hasReadReceipts: true, hasTypingIndicators: true, hasInteractiveMessages: true }, facebook: { maxTextLength: 2000, supportedMediaTypes: ['image', 'video', 'audio', 'file'], hasReadReceipts: true, hasTypingIndicators: true, hasInteractiveMessages: true }, instagram: { maxTextLength: 1000, supportedMediaTypes: ['image', 'video'], hasReadReceipts: true, hasTypingIndicators: true, hasInteractiveMessages: false }, twitter: { maxTextLength: 280, supportedMediaTypes: ['image', 'video', 'gif'], hasReadReceipts: false, hasTypingIndicators: false, hasInteractiveMessages: false }, linkedin: { maxTextLength: 3000, supportedMediaTypes: ['image', 'video', 'document'], hasReadReceipts: false, hasTypingIndicators: false, hasInteractiveMessages: false } }; // Unified message structure this.unifiedMessageSchema = { id: null, platform: null, conversationId: null, customerId: null, agentId: null, direction: null, // 'inbound' | 'outbound' type: null, // 'text' | 'image' | 'audio' | 'video' | 'document' | 'location' | 'contact' | 'interactive' text: null, media: [], timestamp: null, status: null, // 'sent' | 'delivered' | 'read' | 'failed' metadata: {}, normalized: true }; } /** * Initialize Platform Normalizer Service */ async initialize() { try { this.isInitialized = true; logger.info('[COMPLETE] Platform Normalizer Service initialized successfully'); } catch (error) { logger.error('[FAILED] Platform Normalizer Service initialization failed:', error); throw error; } } /** * Normalize message from any platform to unified format */ async normalizeMessage(rawMessage, platform, additionalData = {}) { try { logger.debug(` Normalizing ${platform} message: ${rawMessage.id || 'unknown'}`); let normalizedMessage; switch (platform) { case 'whatsapp': normalizedMessage = await this.normalizeWhatsAppMessage(rawMessage, additionalData); break; case 'facebook': normalizedMessage = await this.normalizeFacebookMessage(rawMessage, additionalData); break; case 'instagram': normalizedMessage = await this.normalizeInstagramMessage(rawMessage, additionalData); break; case 'twitter': normalizedMessage = await this.normalizeTwitterMessage(rawMessage, additionalData); break; case 'linkedin': normalizedMessage = await this.normalizeLinkedInMessage(rawMessage, additionalData); break; default: throw new Error(`Unsupported platform: ${platform}`); } // Validate normalized message this.validateNormalizedMessage(normalizedMessage); // Add normalization metadata normalizedMessage.metadata.normalizedAt = new Date(); normalizedMessage.metadata.originalPlatform = platform; normalizedMessage.normalized = true; logger.debug(`[COMPLETE] Message normalized successfully: ${normalizedMessage.id}`); return normalizedMessage; } catch (error) { logger.error('[FAILED] Message normalization failed:', error); throw error; } } /** * Normalize WhatsApp message */ async normalizeWhatsAppMessage(whatsappMessage, additionalData) { const normalized = { ...this.unifiedMessageSchema }; normalized.id = whatsappMessage.id; normalized.platform = 'whatsapp'; normalized.customerId = additionalData.customerId; normalized.conversationId = additionalData.conversationId; normalized.direction = 'inbound'; normalized.timestamp = new Date(parseInt(whatsappMessage.timestamp) * 1000); normalized.type = whatsappMessage.type; normalized.status = 'received'; // Process different message types switch (whatsappMessage.type) { case 'text': normalized.text = whatsappMessage.text.body; break; case 'image': normalized.media.push({ type: 'image', id: whatsappMessage.image.id, mimeType: whatsappMessage.image.mime_type, caption: whatsappMessage.image.caption, url: additionalData.mediaUrl }); normalized.text = whatsappMessage.image.caption || null; break; case 'audio': normalized.media.push({ type: 'audio', id: whatsappMessage.audio.id, mimeType: whatsappMessage.audio.mime_type, url: additionalData.mediaUrl }); break; case 'video': normalized.media.push({ type: 'video', id: whatsappMessage.video.id, mimeType: whatsappMessage.video.mime_type, caption: whatsappMessage.video.caption, url: additionalData.mediaUrl }); normalized.text = whatsappMessage.video.caption || null; break; case 'document': normalized.media.push({ type: 'document', id: whatsappMessage.document.id, mimeType: whatsappMessage.document.mime_type, filename: whatsappMessage.document.filename, caption: whatsappMessage.document.caption, url: additionalData.mediaUrl }); normalized.text = whatsappMessage.document.caption || null; break; case 'location': normalized.type = 'location'; normalized.metadata.location = { latitude: whatsappMessage.location.latitude, longitude: whatsappMessage.location.longitude, name: whatsappMessage.location.name, address: whatsappMessage.location.address }; normalized.text = `Location: ${whatsappMessage.location.name || 'Shared location'}`; break; case 'contacts': normalized.type = 'contact'; normalized.metadata.contacts = whatsappMessage.contacts; normalized.text = `Contact: ${whatsappMessage.contacts[0]?.name?.formatted_name || 'Unknown'}`; break; case 'interactive': normalized.type = 'interactive'; if (whatsappMessage.interactive.type === 'button_reply') { normalized.text = whatsappMessage.interactive.button_reply.title; normalized.metadata.buttonId = whatsappMessage.interactive.button_reply.id; } else if (whatsappMessage.interactive.type === 'list_reply') { normalized.text = whatsappMessage.interactive.list_reply.title; normalized.metadata.listId = whatsappMessage.interactive.list_reply.id; } break; } // Add WhatsApp-specific metadata normalized.metadata.whatsapp = { messageId: whatsappMessage.id, from: whatsappMessage.from, context: whatsappMessage.context || null }; return normalized; } /** * Normalize Facebook message */ async normalizeFacebookMessage(facebookMessage, additionalData) { const normalized = { ...this.unifiedMessageSchema }; normalized.id = facebookMessage.mid; normalized.platform = 'facebook'; normalized.customerId = additionalData.customerId; normalized.conversationId = additionalData.conversationId; normalized.direction = 'inbound'; normalized.timestamp = new Date(facebookMessage.timestamp); normalized.status = 'received'; // Process message content if (facebookMessage.text) { normalized.type = 'text'; normalized.text = facebookMessage.text; } // Process attachments if (facebookMessage.attachments) { for (const attachment of facebookMessage.attachments) { const mediaItem = { type: attachment.type, url: attachment.payload.url }; if (attachment.type === 'image') { mediaItem.mimeType = 'image/jpeg'; } else if (attachment.type === 'video') { mediaItem.mimeType = 'video/mp4'; } else if (attachment.type === 'audio') { mediaItem.mimeType = 'audio/mpeg'; } else if (attachment.type === 'file') { mediaItem.mimeType = 'application/octet-stream'; mediaItem.filename = attachment.payload.title || 'file'; } normalized.media.push(mediaItem); normalized.type = attachment.type; } } // Process quick replies if (facebookMessage.quick_reply) { normalized.type = 'interactive'; normalized.text = facebookMessage.quick_reply.payload; normalized.metadata.quickReplyPayload = facebookMessage.quick_reply.payload; } // Process postback if (facebookMessage.postback) { normalized.type = 'interactive'; normalized.text = facebookMessage.postback.title || facebookMessage.postback.payload; normalized.metadata.postbackPayload = facebookMessage.postback.payload; } // Add Facebook-specific metadata normalized.metadata.facebook = { messageId: facebookMessage.mid, senderId: facebookMessage.sender?.id, recipientId: facebookMessage.recipient?.id }; return normalized; } /** * Normalize Instagram message */ async normalizeInstagramMessage(instagramMessage, additionalData) { const normalized = { ...this.unifiedMessageSchema }; normalized.id = instagramMessage.mid; normalized.platform = 'instagram'; normalized.customerId = additionalData.customerId; normalized.conversationId = additionalData.conversationId; normalized.direction = 'inbound'; normalized.timestamp = new Date(instagramMessage.timestamp); normalized.status = 'received'; // Process message content (similar to Facebook but with Instagram specifics) if (instagramMessage.text) { normalized.type = 'text'; normalized.text = instagramMessage.text; } // Process attachments if (instagramMessage.attachments) { for (const attachment of instagramMessage.attachments) { normalized.media.push({ type: attachment.type, url: attachment.payload.url, mimeType: attachment.type === 'image' ? 'image/jpeg' : 'video/mp4' }); normalized.type = attachment.type; } } // Process story replies if (instagramMessage.reply_to) { normalized.metadata.storyReply = { storyId: instagramMessage.reply_to.story?.id, mediaType: instagramMessage.reply_to.story?.media?.media_type }; } // Add Instagram-specific metadata normalized.metadata.instagram = { messageId: instagramMessage.mid, senderId: instagramMessage.sender?.id, recipientId: instagramMessage.recipient?.id }; return normalized; } /** * Normalize Twitter message */ async normalizeTwitterMessage(twitterMessage, additionalData) { const normalized = { ...this.unifiedMessageSchema }; normalized.id = twitterMessage.id; normalized.platform = 'twitter'; normalized.customerId = additionalData.customerId; normalized.conversationId = additionalData.conversationId; normalized.direction = 'inbound'; normalized.timestamp = new Date(twitterMessage.created_at); normalized.status = 'received'; // Determine message type if (twitterMessage.direct_message_events) { // Direct message const dmEvent = twitterMessage.direct_message_events[0]; normalized.type = 'text'; normalized.text = dmEvent.message_create.message_data.text; // Process media if (dmEvent.message_create.message_data.attachment) { const attachment = dmEvent.message_create.message_data.attachment; normalized.media.push({ type: attachment.type, url: attachment.media.media_url_https, mimeType: attachment.media.type }); normalized.type = attachment.type; } } else { // Tweet/mention normalized.type = 'text'; normalized.text = twitterMessage.text || twitterMessage.full_text; // Process media if (twitterMessage.entities?.media) { for (const media of twitterMessage.entities.media) { normalized.media.push({ type: media.type, url: media.media_url_https, mimeType: media.type === 'photo' ? 'image/jpeg' : 'video/mp4' }); } if (normalized.media.length > 0) { normalized.type = normalized.media[0].type; } } } // Add Twitter-specific metadata normalized.metadata.twitter = { tweetId: twitterMessage.id_str, userId: twitterMessage.user?.id_str, screenName: twitterMessage.user?.screen_name, isRetweet: !!twitterMessage.retweeted_status, isReply: !!twitterMessage.in_reply_to_status_id, hashtags: twitterMessage.entities?.hashtags || [], mentions: twitterMessage.entities?.user_mentions || [] }; return normalized; } /** * Normalize LinkedIn message */ async normalizeLinkedInMessage(linkedinMessage, additionalData) { const normalized = { ...this.unifiedMessageSchema }; normalized.id = linkedinMessage.id; normalized.platform = 'linkedin'; normalized.customerId = additionalData.customerId; normalized.conversationId = additionalData.conversationId; normalized.direction = 'inbound'; normalized.timestamp = new Date(linkedinMessage.createdAt); normalized.status = 'received'; normalized.type = 'text'; normalized.text = linkedinMessage.body; // Process attachments if (linkedinMessage.attachments) { for (const attachment of linkedinMessage.attachments) { normalized.media.push({ type: attachment.type, url: attachment.url, filename: attachment.name, mimeType: attachment.mediaType }); normalized.type = attachment.type; } } // Add LinkedIn-specific metadata normalized.metadata.linkedin = { messageId: linkedinMessage.id, conversationId: linkedinMessage.conversationId, senderId: linkedinMessage.from?.id, recipientId: linkedinMessage.to?.id }; return normalized; } /** * Convert unified message to platform-specific format */ async denormalizeMessage(unifiedMessage, targetPlatform) { try { logger.debug(` Denormalizing message for ${targetPlatform}: ${unifiedMessage.id}`); let platformMessage; switch (targetPlatform) { case 'whatsapp': platformMessage = await this.denormalizeToWhatsApp(unifiedMessage); break; case 'facebook': platformMessage = await this.denormalizeToFacebook(unifiedMessage); break; case 'instagram': platformMessage = await this.denormalizeToInstagram(unifiedMessage); break; case 'twitter': platformMessage = await this.denormalizeToTwitter(unifiedMessage); break; case 'linkedin': platformMessage = await this.denormalizeToLinkedIn(unifiedMessage); break; default: throw new Error(`Unsupported target platform: ${targetPlatform}`); } // Validate platform constraints this.validatePlatformConstraints(platformMessage, targetPlatform); logger.debug(`[COMPLETE] Message denormalized successfully for ${targetPlatform}`); return platformMessage; } catch (error) { logger.error('[FAILED] Message denormalization failed:', error); throw error; } } /** * Validate normalized message structure */ validateNormalizedMessage(message) { const requiredFields = ['id', 'platform', 'direction', 'timestamp', 'type']; const missing = requiredFields.filter(field => !message[field]); if (missing.length > 0) { throw new Error(`Missing required fields in normalized message: ${missing.join(', ')}`); } if (!['inbound', 'outbound'].includes(message.direction)) { throw new Error(`Invalid message direction: ${message.direction}`); } if (!['text', 'image', 'audio', 'video', 'document', 'location', 'contact', 'interactive'].includes(message.type)) { throw new Error(`Invalid message type: ${message.type}`); } } /** * Validate platform-specific constraints */ validatePlatformConstraints(message, platform) { const config = this.platformConfigs[platform]; if (!config) { throw new Error(`Unknown platform: ${platform}`); } // Check text length if (message.text && message.text.length > config.maxTextLength) { throw new Error(`Message text exceeds ${platform} limit of ${config.maxTextLength} characters`); } // Check media support if (message.media && message.media.length > 0) { for (const media of message.media) { if (!config.supportedMediaTypes.includes(media.type)) { throw new Error(`Media type ${media.type} not supported on ${platform}`); } } } } /** * Get platform capabilities */ getPlatformCapabilities(platform) { return this.platformConfigs[platform] || null; } /** * Truncate text to platform limits */ truncateTextForPlatform(text, platform) { const config = this.platformConfigs[platform]; if (!config || !text) return text; if (text.length <= config.maxTextLength) return text; return text.substring(0, config.maxTextLength - 3) + '...'; } /** * Split long messages for platforms with character limits */ splitMessageForPlatform(text, platform) { const config = this.platformConfigs[platform]; if (!config || !text || text.length <= config.maxTextLength) { return [text]; } const chunks = []; let remaining = text; while (remaining.length > 0) { if (remaining.length <= config.maxTextLength) { chunks.push(remaining); break; } // Find a good break point (space, punctuation) let breakPoint = config.maxTextLength - 3; const lastSpace = remaining.lastIndexOf(' ', breakPoint); const lastPunctuation = Math.max( remaining.lastIndexOf('.', breakPoint), remaining.lastIndexOf('!', breakPoint), remaining.lastIndexOf('?', breakPoint) ); if (lastSpace > breakPoint * 0.8) { breakPoint = lastSpace; } else if (lastPunctuation > breakPoint * 0.8) { breakPoint = lastPunctuation + 1; } chunks.push(remaining.substring(0, breakPoint).trim()); remaining = remaining.substring(breakPoint).trim(); } return chunks; } /** * Health check */ isHealthy() { return this.isInitialized; } /** * Cleanup resources */ async cleanup() { try { this.isInitialized = false; logger.info('[COMPLETE] Platform Normalizer Service cleanup completed'); } catch (error) { logger.error('[FAILED] Platform Normalizer Service cleanup failed:', error); throw error; } } } module.exports = PlatformNormalizerService;