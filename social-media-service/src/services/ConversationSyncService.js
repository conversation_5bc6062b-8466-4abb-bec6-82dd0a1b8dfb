/** * ============================================= * CONVERSATION SYNC SERVICE * Real-time conversation history synchronization in MongoDB * Cross-platform conversation management and analytics * ============================================= */ const logger = require('../utils/logger'); const Conversation = require('../models/Conversation'); const Message = require('../models/Message'); const Customer = require('../models/Customer'); const Agent = require('../models/Agent'); class ConversationSyncService { constructor(io, redis) { this.io = io; this.redis = redis; this.isInitialized = false; // Sync configuration this.syncConfig = { batchSize: 100, syncInterval: 30000, // 30 seconds retryAttempts: 3, retryDelay: 5000, maxSyncAge: 86400000 // 24 hours }; // Sync queues this.syncQueues = { conversations: new Set(), messages: new Set(), customers: new Set(), agents: new Set() }; // Performance metrics this.metrics = { totalSyncs: 0, successfulSyncs: 0, failedSyncs: 0, averageSyncTime: 0, lastSyncTime: null }; } /** * Initialize Conversation Sync Service */ async initialize() { try { // Start periodic sync this.startPeriodicSync(); // Initialize metrics tracking this.initializeMetrics(); this.isInitialized = true; logger.info('[COMPLETE] Conversation Sync Service initialized successfully'); } catch (error) { logger.error('[FAILED] Conversation Sync Service initialization failed:', error); throw error; } } /** * Sync conversation across all platforms */ async syncConversation(conversationId, options = {}) { try { const startTime = Date.now(); // Get conversation with related data const conversation = await Conversation.findById(conversationId) .populate('customerId', 'name email phone platformIds') .populate('assignedAgent', 'name email status') .populate('lastMessage'); if (!conversation) { throw new Error(`Conversation not found: ${conversationId}`); } // Sync conversation data const syncResult = await this.performConversationSync(conversation, options); // Update metrics this.updateSyncMetrics(startTime, true); // Emit sync event this.emitSyncEvent('conversation_synced', { conversationId, platform: conversation.platform, syncResult, timestamp: new Date() }); logger.debug(`[COMPLETE] Conversation synced: ${conversationId}`); return syncResult; } catch (error) { this.updateSyncMetrics(Date.now(), false); logger.error('[FAILED] Conversation sync failed:', error); throw error; } } /** * Perform conversation sync */ async performConversationSync(conversation, options) { try { const syncData = { conversationId: conversation._id, platform: conversation.platform, status: conversation.status, lastActivity: conversation.lastActivity, messageCount: conversation.messageCount, customer: { id: conversation.customerId._id, name: conversation.customerId.name, platforms: Object.keys(conversation.customerId.platformIds || {}) }, agent: conversation.assignedAgent ? { id: conversation.assignedAgent._id, name: conversation.assignedAgent.name, status: conversation.assignedAgent.status } : null, analytics: await this.calculateConversationAnalytics(conversation), lastSync: new Date() }; // Store sync data in Redis for real-time access await this.storeSyncData(conversation._id, syncData); // Update conversation analytics await this.updateConversationAnalytics(conversation, syncData.analytics); // Sync related messages if requested if (options.includeMessages) { syncData.messages = await this.syncConversationMessages(conversation._id); } return syncData; } catch (error) { logger.error('[FAILED] Failed to perform conversation sync:', error); throw error; } } /** * Sync conversation messages */ async syncConversationMessages(conversationId, limit = 50) { try { const messages = await Message.find({ conversationId }) .sort({ timestamp: -1 }) .limit(limit) .populate('agentId', 'name'); const syncedMessages = messages.map(message => ({ id: message._id, platform: message.platform, direction: message.direction, type: message.type, text: message.text, timestamp: message.timestamp, status: message.status, agent: message.agentId ? { id: message.agentId._id, name: message.agentId.name } : null, analysis: message.analysis || null })); // Store in Redis for quick access await this.redis.setex( `conversation_messages:${conversationId}`, 3600, // 1 hour JSON.stringify(syncedMessages) ); return syncedMessages; } catch (error) { logger.error('[FAILED] Failed to sync conversation messages:', error); throw error; } } /** * Calculate conversation analytics */ async calculateConversationAnalytics(conversation) { try { const analytics = { duration: conversation.duration, responseTime: { first: conversation.firstResponseTime, average: conversation.averageResponseTime }, messageStats: { total: conversation.messageCount, inbound: 0, outbound: 0 }, sentiment: { overall: conversation.sentiment, trend: 'stable' }, urgency: conversation.urgency, escalations: conversation.escalationCount, satisfaction: conversation.customerSatisfaction?.rating || null }; // Get detailed message statistics const messageStats = await Message.aggregate([ { $match: { conversationId: conversation._id } }, { $group: { _id: '$direction', count: { $sum: 1 }, avgResponseTime: { $avg: '$responseTime' } } } ]); messageStats.forEach(stat => { analytics.messageStats[stat._id] = stat.count; }); // Calculate sentiment trend const recentMessages = await Message.find({ conversationId: conversation._id, 'analysis.sentiment': { $exists: true } }) .sort({ timestamp: -1 }) .limit(10); if (recentMessages.length > 0) { analytics.sentiment.trend = this.calculateSentimentTrend(recentMessages); } return analytics; } catch (error) { logger.error('[FAILED] Failed to calculate conversation analytics:', error); return {}; } } /** * Calculate sentiment trend */ calculateSentimentTrend(messages) { if (messages.length < 2) return 'stable'; const sentimentScores = messages.map(msg => { const sentiment = msg.analysis?.sentiment?.label; const scoreMap = { 'very_positive': 2, 'positive': 1, 'neutral': 0, 'negative': -1, 'very_negative': -2 }; return scoreMap[sentiment] || 0; }); const firstHalf = sentimentScores.slice(0, Math.floor(sentimentScores.length / 2)); const secondHalf = sentimentScores.slice(Math.floor(sentimentScores.length / 2)); const firstAvg = firstHalf.reduce((sum, score) => sum + score, 0) / firstHalf.length; const secondAvg = secondHalf.reduce((sum, score) => sum + score, 0) / secondHalf.length; const difference = secondAvg - firstAvg; if (difference > 0.5) return 'improving'; if (difference < -0.5) return 'declining'; return 'stable'; } /** * Store sync data in Redis */ async storeSyncData(conversationId, syncData) { try { const key = `conversation_sync:${conversationId}`; await this.redis.setex(key, 3600, JSON.stringify(syncData)); // 1 hour TTL // Also store in global sync index await this.redis.zadd( 'conversation_sync_index', Date.now(), conversationId.toString() ); } catch (error) { logger.error('[FAILED] Failed to store sync data:', error); } } /** * Update conversation analytics */ async updateConversationAnalytics(conversation, analytics) { try { // Update conversation with calculated analytics await Conversation.findByIdAndUpdate(conversation._id, { $set: { 'metadata.analytics': analytics, 'metadata.lastAnalyticsUpdate': new Date() } }); // Update customer analytics await this.updateCustomerAnalytics(conversation.customerId._id, analytics); // Update agent analytics if assigned if (conversation.assignedAgent) { await this.updateAgentAnalytics(conversation.assignedAgent._id, analytics); } } catch (error) { logger.error('[FAILED] Failed to update conversation analytics:', error); } } /** * Update customer analytics */ async updateCustomerAnalytics(customerId, conversationAnalytics) { try { const customer = await Customer.findById(customerId); if (!customer) return; // Update customer analytics customer.analytics.totalConversations = (customer.analytics.totalConversations || 0) + 1; customer.analytics.lastInteractionAt = new Date(); // Update platform-specific stats const platform = conversationAnalytics.platform; if (platform && customer.analytics.platformStats[platform]) { customer.analytics.platformStats[platform].conversations += 1; customer.analytics.platformStats[platform].lastActive = new Date(); } await customer.save(); } catch (error) { logger.error('[FAILED] Failed to update customer analytics:', error); } } /** * Update agent analytics */ async updateAgentAnalytics(agentId, conversationAnalytics) { try { const agent = await Agent.findById(agentId); if (!agent) return; // Update agent performance metrics agent.performance.totalConversations = (agent.performance.totalConversations || 0) + 1; if (conversationAnalytics.responseTime.first) { const currentAvg = agent.performance.averageFirstResponseTime || 0; const totalConversations = agent.performance.totalConversations; agent.performance.averageFirstResponseTime = (currentAvg * (totalConversations - 1) + conversationAnalytics.responseTime.first) / totalConversations; } agent.performance.lastPerformanceUpdate = new Date(); await agent.save(); } catch (error) { logger.error('[FAILED] Failed to update agent analytics:', error); } } /** * Update conversation status */ async updateConversationStatus(conversationId, status, agentId) { try { const conversation = await Conversation.findById(conversationId); if (!conversation) { throw new Error('Conversation not found'); } const oldStatus = conversation.status; conversation.status = status; conversation.updatedAt = new Date(); // Handle status-specific updates switch (status) { case 'assigned': if (agentId) { conversation.assignedAgent = agentId; conversation.assignedAt = new Date(); } break; case 'resolved': conversation.resolvedAt = new Date(); conversation.resolutionTime = conversation.resolvedAt - conversation.createdAt; break; case 'closed': conversation.closedAt = new Date(); break; } await conversation.save(); // Sync the updated conversation await this.syncConversation(conversationId); // Emit status update event this.emitSyncEvent('conversation_status_updated', { conversationId, oldStatus, newStatus: status, agentId, timestamp: new Date() }); logger.info(`[COMPLETE] Conversation status updated: ${conversationId} -> ${status}`); } catch (error) { logger.error('[FAILED] Failed to update conversation status:', error); throw error; } } /** * Sync all active conversations */ async syncAllActiveConversations() { try { const activeConversations = await Conversation.find({ status: { $in: ['open', 'assigned', 'pending'] }, lastActivity: { $gte: new Date(Date.now() - this.syncConfig.maxSyncAge) } }).select('_id'); logger.info(` Syncing ${activeConversations.length} active conversations`); const syncPromises = activeConversations.map(conv => this.syncConversation(conv._id).catch(error => { logger.error(`Failed to sync conversation ${conv._id}:`, error); }) ); await Promise.all(syncPromises); logger.info(`[COMPLETE] Completed sync of ${activeConversations.length} conversations`); } catch (error) { logger.error('[FAILED] Failed to sync all active conversations:', error); } } /** * Get conversation sync status */ async getConversationSyncStatus(conversationId) { try { const key = `conversation_sync:${conversationId}`; const syncData = await this.redis.get(key); if (syncData) { return JSON.parse(syncData); } return null; } catch (error) { logger.error('[FAILED] Failed to get conversation sync status:', error); return null; } } /** * Start periodic sync */ startPeriodicSync() { setInterval(async () => { try { await this.syncAllActiveConversations(); } catch (error) { logger.error('[FAILED] Periodic sync failed:', error); } }, this.syncConfig.syncInterval); logger.info(` Periodic sync started (interval: ${this.syncConfig.syncInterval}ms)`); } /** * Initialize metrics tracking */ initializeMetrics() { // Reset metrics every hour setInterval(() => { this.metrics = { totalSyncs: 0, successfulSyncs: 0, failedSyncs: 0, averageSyncTime: 0, lastSyncTime: this.metrics.lastSyncTime }; }, 3600000); // 1 hour } /** * Update sync metrics */ updateSyncMetrics(startTime, success) { const syncTime = Date.now() - startTime; this.metrics.totalSyncs++; this.metrics.lastSyncTime = new Date(); if (success) { this.metrics.successfulSyncs++; } else { this.metrics.failedSyncs++; } // Update average sync time this.metrics.averageSyncTime = (this.metrics.averageSyncTime * (this.metrics.totalSyncs - 1) + syncTime) / this.metrics.totalSyncs; } /** * Emit sync event */ emitSyncEvent(eventType, data) { try { if (this.io) { this.io.emit(eventType, data); } } catch (error) { logger.error('[FAILED] Failed to emit sync event:', error); } } /** * Get sync metrics */ getSyncMetrics() { return { ...this.metrics, successRate: this.metrics.totalSyncs > 0 ? (this.metrics.successfulSyncs / this.metrics.totalSyncs) * 100 : 0 }; } /** * Health check */ isHealthy() { return this.isInitialized; } /** * Cleanup resources */ async cleanup() { try { this.isInitialized = false; logger.info('[COMPLETE] Conversation Sync Service cleanup completed'); } catch (error) { logger.error('[FAILED] Conversation Sync Service cleanup failed:', error); throw error; } } } module.exports = ConversationSyncService;