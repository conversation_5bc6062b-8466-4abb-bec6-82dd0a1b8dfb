/** * ============================================= * MESSAGE ROUTER SERVICE * Intelligent routing based on customer preferences, agent availability, and urgency * Handles message prioritization, agent assignment, and escalation logic * ============================================= */ const axios = require('axios'); const logger = require('../utils/logger'); const Conversation = require('../models/Conversation'); const Agent = require('../models/Agent'); const Customer = require('../models/Customer'); class MessageRouterService { constructor(io, redis) { this.io = io; this.redis = redis; this.isInitialized = false; // Routing configuration this.routingConfig = { maxConcurrentConversations: 5, urgencyWeights: { low: 1, medium: 2, high: 3, urgent: 5 }, platformPriorities: { whatsapp: 4, facebook: 3, instagram: 3, twitter: 2, linkedin: 1 }, skillBasedRouting: true, roundRobinFallback: true }; // Agent availability cache this.agentAvailabilityCache = new Map(); this.conversationQueues = new Map(); // Services URLs this.multimodalServiceUrl = process.env.MULTIMODAL_SERVICE_URL || 'http://localhost:5009'; this.mlServiceUrl = process.env.ML_SERVICE_URL || 'http://localhost:5001'; this.callServiceUrl = process.env.CALL_SERVICE_URL || 'http://localhost:5004'; } /** * Initialize Message Router Service */ async initialize() { try { // Load agent availability from database await this.loadAgentAvailability(); // Start periodic tasks this.startPeriodicTasks(); this.isInitialized = true; logger.info('[COMPLETE] Message Router Service initialized successfully'); } catch (error) { logger.error('[FAILED] Message Router Service initialization failed:', error); throw error; } } /** * Route incoming message to appropriate agent */ async routeMessage(message, platform, conversationId = null) { try { logger.info(` Routing message from ${platform}`, { messageId: message.id, conversationId, customerId: message.customerId }); // Analyze message content for routing decisions const messageAnalysis = await this.analyzeMessage(message); // Get or create conversation const conversation = await this.getOrCreateConversation( conversationId, message.customerId, platform, messageAnalysis ); // Determine routing strategy const routingDecision = await this.determineRoutingStrategy( conversation, message, messageAnalysis ); // Execute routing decision const result = await this.executeRouting(conversation, message, routingDecision); logger.info('[COMPLETE] Message routed successfully', { conversationId: conversation._id, agentId: result.agentId, strategy: routingDecision.strategy, urgency: messageAnalysis.urgency }); return result; } catch (error) { logger.error('[FAILED] Message routing failed:', error); throw error; } } /** * Analyze message content using multimodal and ML services */ async analyzeMessage(message) { try { const analysis = { intent: 'general', sentiment: 'neutral', urgency: 'medium', language: 'fr', entities: {}, confidence: 0.5, requiresHuman: false, suggestedSkills: [] }; // Analyze text content if (message.text) { try { const textAnalysis = await axios.post(`${this.multimodalServiceUrl}/api/text/process`, { text: message.text, options: { language: 'fr' } }); if (textAnalysis.data) { analysis.intent = textAnalysis.data.intent?.primary || 'general'; analysis.sentiment = textAnalysis.data.sentiment?.label || 'neutral'; analysis.urgency = textAnalysis.data.urgency?.level || 'medium'; analysis.entities = textAnalysis.data.entities || {}; analysis.confidence = textAnalysis.data.confidence || 0.5; } } catch (error) { logger.warn('Text analysis failed, using defaults:', error.message); } } // Analyze media content if present if (message.media && message.media.length > 0) { try { for (const media of message.media) { if (media.type === 'image') { const imageAnalysis = await this.analyzeImage(media); if (imageAnalysis.products?.length > 0) { analysis.intent = 'technical'; analysis.suggestedSkills.push('product_support'); } } else if (media.type === 'audio') { const voiceAnalysis = await this.analyzeVoice(media); if (voiceAnalysis.emotion?.dominant === 'angry') { analysis.urgency = 'high'; analysis.requiresHuman = true; } } } } catch (error) { logger.warn('Media analysis failed:', error.message); } } // Determine required skills based on intent analysis.suggestedSkills = this.mapIntentToSkills(analysis.intent); // Assess if human intervention is required analysis.requiresHuman = this.assessHumanRequirement(analysis); return analysis; } catch (error) { logger.error('[FAILED] Message analysis failed:', error); // Return default analysis return { intent: 'general', sentiment: 'neutral', urgency: 'medium', language: 'fr', entities: {}, confidence: 0.5, requiresHuman: true, suggestedSkills: ['general_support'] }; } } /** * Get or create conversation */ async getOrCreateConversation(conversationId, customerId, platform, analysis) { try { let conversation; if (conversationId) { conversation = await Conversation.findById(conversationId); if (conversation) { // Update conversation with new analysis conversation.lastActivity = new Date(); conversation.urgency = analysis.urgency; conversation.sentiment = analysis.sentiment; await conversation.save(); return conversation; } } // Create new conversation conversation = new Conversation({ customerId, platform, status: 'open', urgency: analysis.urgency, intent: analysis.intent, sentiment: analysis.sentiment, language: analysis.language, requiresHuman: analysis.requiresHuman, suggestedSkills: analysis.suggestedSkills, createdAt: new Date(), lastActivity: new Date(), metadata: { confidence: analysis.confidence, entities: analysis.entities } }); await conversation.save(); return conversation; } catch (error) { logger.error('[FAILED] Failed to get or create conversation:', error); throw error; } } /** * Determine routing strategy */ async determineRoutingStrategy(conversation, message, analysis) { try { const strategy = { type: 'skill_based', // skill_based, round_robin, priority, escalation targetSkills: analysis.suggestedSkills, urgencyLevel: analysis.urgency, requiresImmediate: false, fallbackStrategy: 'round_robin', maxWaitTime: this.getMaxWaitTime(analysis.urgency), escalationTriggers: [] }; // Check for immediate routing requirements if (analysis.urgency === 'urgent' || analysis.sentiment === 'very_negative') { strategy.requiresImmediate = true; strategy.type = 'priority'; } // Check for escalation triggers if (conversation.escalationCount > 2) { strategy.type = 'escalation'; strategy.escalationTriggers.push('multiple_escalations'); } // Check customer preferences const customer = await Customer.findById(conversation.customerId); if (customer?.preferences?.preferredAgent) { strategy.preferredAgent = customer.preferences.preferredAgent; strategy.type = 'preferred_agent'; } // Platform-specific routing if (conversation.platform === 'whatsapp' && analysis.intent === 'technical') { strategy.targetSkills.push('whatsapp_support'); } return strategy; } catch (error) { logger.error('[FAILED] Failed to determine routing strategy:', error); return { type: 'round_robin', targetSkills: ['general_support'], urgencyLevel: 'medium', requiresImmediate: false, fallbackStrategy: 'round_robin', maxWaitTime: 300000 // 5 minutes }; } } /** * Execute routing decision */ async executeRouting(conversation, message, strategy) { try { let selectedAgent = null; switch (strategy.type) { case 'preferred_agent': selectedAgent = await this.routeToPreferredAgent(strategy.preferredAgent); break; case 'skill_based': selectedAgent = await this.routeBySkills(strategy.targetSkills, strategy.urgencyLevel); break; case 'priority': selectedAgent = await this.routeByPriority(strategy.urgencyLevel); break; case 'escalation': selectedAgent = await this.routeForEscalation(conversation, strategy); break; case 'round_robin': default: selectedAgent = await this.routeRoundRobin(); break; } // If no agent available, add to queue if (!selectedAgent) { return await this.addToQueue(conversation, message, strategy); } // Assign conversation to agent await this.assignConversationToAgent(conversation, selectedAgent, message); // Send notification to agent await this.notifyAgent(selectedAgent, conversation, message); return { success: true, agentId: selectedAgent._id, agentName: selectedAgent.name, strategy: strategy.type, assignedAt: new Date(), estimatedResponseTime: this.calculateEstimatedResponseTime(selectedAgent) }; } catch (error) { logger.error('[FAILED] Failed to execute routing:', error); throw error; } } /** * Route to preferred agent */ async routeToPreferredAgent(agentId) { try { const agent = await Agent.findById(agentId); if (!agent || agent.status !== 'available') { return null; } const availability = await this.checkAgentAvailability(agent); return availability.available ? agent : null; } catch (error) { logger.error('[FAILED] Failed to route to preferred agent:', error); return null; } } /** * Route by skills */ async routeBySkills(requiredSkills, urgencyLevel) { try { const agents = await Agent.find({ status: 'available', skills: { $in: requiredSkills } }).sort({ lastAssigned: 1 }); for (const agent of agents) { const availability = await this.checkAgentAvailability(agent); if (availability.available) { return agent; } } return null; } catch (error) { logger.error('[FAILED] Failed to route by skills:', error); return null; } } /** * Route by priority (for urgent messages) */ async routeByPriority(urgencyLevel) { try { const agents = await Agent.find({ status: 'available', canHandleUrgent: true }).sort({ currentLoad: 1, lastAssigned: 1 }); for (const agent of agents) { const availability = await this.checkAgentAvailability(agent); if (availability.available) { return agent; } } return null; } catch (error) { logger.error('[FAILED] Failed to route by priority:', error); return null; } } /** * Route for escalation */ async routeForEscalation(conversation, strategy) { try { // Try to route to supervisor or senior agent const supervisors = await Agent.find({ status: 'available', role: { $in: ['supervisor', 'senior'] }, skills: { $in: strategy.targetSkills } }).sort({ currentLoad: 1 }); for (const supervisor of supervisors) { const availability = await this.checkAgentAvailability(supervisor); if (availability.available) { // Mark conversation as escalated conversation.escalationCount += 1; conversation.escalatedAt = new Date(); await conversation.save(); return supervisor; } } // If no supervisor available, try call escalation if (strategy.escalationTriggers.includes('multiple_escalations')) { await this.escalateToCall(conversation); } return null; } catch (error) { logger.error('[FAILED] Failed to route for escalation:', error); return null; } } /** * Route using round robin */ async routeRoundRobin() { try { const agents = await Agent.find({ status: 'available' }).sort({ lastAssigned: 1 }); for (const agent of agents) { const availability = await this.checkAgentAvailability(agent); if (availability.available) { return agent; } } return null; } catch (error) { logger.error('[FAILED] Failed to route round robin:', error); return null; } } /** * Check agent availability */ async checkAgentAvailability(agent) { try { // Check cache first const cacheKey = `agent_availability:${agent._id}`; const cached = await this.redis.get(cacheKey); if (cached) { return JSON.parse(cached); } // Calculate availability const currentConversations = await Conversation.countDocuments({ assignedAgent: agent._id, status: { $in: ['open', 'pending'] } }); const availability = { available: currentConversations < this.routingConfig.maxConcurrentConversations, currentLoad: currentConversations, maxLoad: this.routingConfig.maxConcurrentConversations, lastChecked: new Date() }; // Cache for 30 seconds await this.redis.setex(cacheKey, 30, JSON.stringify(availability)); return availability; } catch (error) { logger.error('[FAILED] Failed to check agent availability:', error); return { available: false, currentLoad: 999, maxLoad: 0 }; } } /** * Add conversation to queue */ async addToQueue(conversation, message, strategy) { try { const queueKey = `queue:${strategy.urgencyLevel}`; const queueItem = { conversationId: conversation._id, messageId: message.id, addedAt: new Date(), strategy, priority: this.routingConfig.urgencyWeights[strategy.urgencyLevel] || 1 }; await this.redis.zadd(queueKey, queueItem.priority, JSON.stringify(queueItem)); // Notify about queue addition this.io.emit('conversation-queued', { conversationId: conversation._id, position: await this.redis.zcard(queueKey), estimatedWait: this.calculateEstimatedWaitTime(strategy.urgencyLevel) }); return { success: true, queued: true, queuePosition: await this.redis.zcard(queueKey), estimatedWait: this.calculateEstimatedWaitTime(strategy.urgencyLevel) }; } catch (error) { logger.error('[FAILED] Failed to add to queue:', error); throw error; } } /** * Assign conversation to agent */ async assignConversationToAgent(conversation, agent, message) { try { conversation.assignedAgent = agent._id; conversation.assignedAt = new Date(); conversation.status = 'assigned'; await conversation.save(); // Update agent agent.lastAssigned = new Date(); agent.currentLoad = (agent.currentLoad || 0) + 1; await agent.save(); // Clear availability cache await this.redis.del(`agent_availability:${agent._id}`); logger.info(`[COMPLETE] Conversation ${conversation._id} assigned to agent ${agent._id}`); } catch (error) { logger.error('[FAILED] Failed to assign conversation to agent:', error); throw error; } } /** * Notify agent about new assignment */ async notifyAgent(agent, conversation, message) { try { const notification = { type: 'new_conversation', conversationId: conversation._id, customerId: conversation.customerId, platform: conversation.platform, urgency: conversation.urgency, intent: conversation.intent, message: { id: message.id, text: message.text, timestamp: message.timestamp }, assignedAt: new Date() }; // Send real-time notification this.io.to(`agent-${agent._id}`).emit('new-conversation-assigned', notification); // Store notification in Redis for offline agents await this.redis.lpush(`notifications:${agent._id}`, JSON.stringify(notification)); await this.redis.expire(`notifications:${agent._id}`, 86400); // 24 hours } catch (error) { logger.error('[FAILED] Failed to notify agent:', error); } } /** * Helper methods */ mapIntentToSkills(intent) { const skillMap = { 'billing': ['billing_support', 'account_management'], 'technical': ['technical_support', 'product_support'], 'complaint': ['complaint_handling', 'customer_retention'], 'general': ['general_support'], 'sales': ['sales_support', 'product_information'] }; return skillMap[intent] || ['general_support']; } assessHumanRequirement(analysis) { // Require human for high urgency, negative sentiment, or low confidence return analysis.urgency === 'urgent' || analysis.sentiment === 'very_negative' || analysis.confidence < 0.6; } getMaxWaitTime(urgency) { const waitTimes = { low: 900000, // 15 minutes medium: 300000, // 5 minutes high: 120000, // 2 minutes urgent: 30000 // 30 seconds }; return waitTimes[urgency] || 300000; } calculateEstimatedResponseTime(agent) { // Base response time on agent's current load and historical performance const baseTime = 60000; // 1 minute const loadMultiplier = (agent.currentLoad || 0) * 30000; // 30 seconds per conversation return baseTime + loadMultiplier; } calculateEstimatedWaitTime(urgency) { const baseTimes = { low: 600000, // 10 minutes medium: 300000, // 5 minutes high: 120000, // 2 minutes urgent: 30000 // 30 seconds }; return baseTimes[urgency] || 300000; } async analyzeImage(media) { try { const response = await axios.post(`${this.multimodalServiceUrl}/api/image/process`, { imageUrl: media.url }); return response.data; } catch (error) { logger.warn('Image analysis failed:', error.message); return {}; } } async analyzeVoice(media) { try { const response = await axios.post(`${this.multimodalServiceUrl}/api/voice/process`, { audioUrl: media.url }); return response.data; } catch (error) { logger.warn('Voice analysis failed:', error.message); return {}; } } async escalateToCall(conversation) { try { await axios.post(`${this.callServiceUrl}/api/calls/escalate`, { conversationId: conversation._id, customerId: conversation.customerId, urgency: conversation.urgency, reason: 'social_media_escalation' }); conversation.escalatedToCall = true; conversation.escalatedAt = new Date(); await conversation.save(); logger.info(`[COMPLETE] Conversation ${conversation._id} escalated to call system`); } catch (error) { logger.error('[FAILED] Failed to escalate to call system:', error); } } async loadAgentAvailability() { try { const agents = await Agent.find({ status: 'available' }); for (const agent of agents) { await this.checkAgentAvailability(agent); } logger.info(`[COMPLETE] Loaded availability for ${agents.length} agents`); } catch (error) { logger.error('[FAILED] Failed to load agent availability:', error); } } startPeriodicTasks() { // Process queued conversations every 30 seconds setInterval(async () => { await this.processQueues(); }, 30000); // Update agent availability every minute setInterval(async () => { await this.updateAgentAvailability(); }, 60000); } async processQueues() { try { const urgencyLevels = ['urgent', 'high', 'medium', 'low']; for (const urgency of urgencyLevels) { const queueKey = `queue:${urgency}`; const queuedItems = await this.redis.zrevrange(queueKey, 0, 4); // Process top 5 for (const itemStr of queuedItems) { const item = JSON.parse(itemStr); const conversation = await Conversation.findById(item.conversationId); if (conversation && conversation.status === 'open') { const routingResult = await this.executeRouting(conversation, { id: item.messageId }, item.strategy); if (routingResult.success && !routingResult.queued) { await this.redis.zrem(queueKey, itemStr); } } else { // Remove invalid items from queue await this.redis.zrem(queueKey, itemStr); } } } } catch (error) { logger.error('[FAILED] Failed to process queues:', error); } } async updateAgentAvailability() { try { const agents = await Agent.find({}); for (const agent of agents) { await this.checkAgentAvailability(agent); } } catch (error) { logger.error('[FAILED] Failed to update agent availability:', error); } } /** * Health check */ isHealthy() { return this.isInitialized; } /** * Cleanup resources */ async cleanup() { try { this.isInitialized = false; logger.info('[COMPLETE] Message Router Service cleanup completed'); } catch (error) { logger.error('[FAILED] Message Router Service cleanup failed:', error); throw error; } } } module.exports = MessageRouterService;