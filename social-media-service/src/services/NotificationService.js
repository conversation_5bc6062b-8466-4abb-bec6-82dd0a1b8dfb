/** * ============================================= * NOTIFICATION SERVICE * Real-time notification system for agents with WebSocket integration * Multi-channel notifications with priority handling * ============================================= */ const logger = require('../utils/logger'); const Agent = require('../models/Agent'); const Conversation = require('../models/Conversation'); const Customer = require('../models/Customer'); class NotificationService { constructor(io, redis) { this.io = io; this.redis = redis; this.isInitialized = false; // Notification configuration this.config = { maxRetries: 3, retryDelay: 5000, batchSize: 50, ttl: 86400, // 24 hours priorities: { urgent: 1, high: 2, medium: 3, low: 4 } }; // Notification types this.notificationTypes = { NEW_MESSAGE: 'new_message', NEW_CONVERSATION: 'new_conversation', CONVERSATION_ASSIGNED: 'conversation_assigned', CONVERSATION_ESCALATED: 'conversation_escalated', URGENT_MESSAGE: 'urgent_message', CUSTOMER_SATISFACTION: 'customer_satisfaction', SYSTEM_ALERT: 'system_alert', AGENT_MENTION: 'agent_mention' }; // Delivery channels this.channels = { WEBSOCKET: 'websocket', EMAIL: 'email', SMS: 'sms', PUSH: 'push' }; // Notification queues this.queues = { realtime: [], email: [], sms: [], push: [] }; // Performance metrics this.metrics = { totalNotifications: 0, deliveredNotifications: 0, failedNotifications: 0, averageDeliveryTime: 0 }; } /** * Initialize Notification Service */ async initialize() { try { // Start notification processors this.startNotificationProcessors(); // Initialize metrics tracking this.initializeMetrics(); this.isInitialized = true; logger.info('[COMPLETE] Notification Service initialized successfully'); } catch (error) { logger.error('[FAILED] Notification Service initialization failed:', error); throw error; } } /** * Send notification */ async sendNotification(notificationData) { try { const startTime = Date.now(); // Validate notification data this.validateNotificationData(notificationData); // Enrich notification with additional data const enrichedNotification = await this.enrichNotification(notificationData); // Determine recipients const recipients = await this.determineRecipients(enrichedNotification); // Send to each recipient const deliveryResults = await Promise.all( recipients.map(recipient => this.deliverNotification(enrichedNotification, recipient)) ); // Update metrics this.updateMetrics(startTime, deliveryResults); logger.debug(`[COMPLETE] Notification sent to ${recipients.length} recipients`); return { success: true, recipients: recipients.length, deliveryResults }; } catch (error) { logger.error('[FAILED] Failed to send notification:', error); this.metrics.failedNotifications++; throw error; } } /** * Validate notification data */ validateNotificationData(data) { const required = ['type', 'platform']; const missing = required.filter(field => !data[field]); if (missing.length > 0) { throw new Error(`Missing required notification fields: ${missing.join(', ')}`); } if (!Object.values(this.notificationTypes).includes(data.type)) { throw new Error(`Invalid notification type: ${data.type}`); } } /** * Enrich notification with additional data */ async enrichNotification(notificationData) { try { const enriched = { ...notificationData, id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`, timestamp: new Date(), priority: this.determinePriority(notificationData), channels: this.determineChannels(notificationData) }; // Add conversation details if available if (notificationData.conversationId) { const conversation = await Conversation.findById(notificationData.conversationId) .populate('customerId', 'name email phone') .populate('assignedAgent', 'name email'); if (conversation) { enriched.conversation = { id: conversation._id, platform: conversation.platform, status: conversation.status, urgency: conversation.urgency, customer: conversation.customerId ? { id: conversation.customerId._id, name: conversation.customerId.name, email: conversation.customerId.email } : null, agent: conversation.assignedAgent ? { id: conversation.assignedAgent._id, name: conversation.assignedAgent.name } : null }; } } // Add customer details if available if (notificationData.customerId) { const customer = await Customer.findById(notificationData.customerId); if (customer) { enriched.customer = { id: customer._id, name: customer.name, platform: customer.primaryPlatform, segment: customer.segment }; } } // Generate notification content enriched.content = this.generateNotificationContent(enriched); return enriched; } catch (error) { logger.error('[FAILED] Failed to enrich notification:', error); return notificationData; } } /** * Determine notification priority */ determinePriority(notificationData) { // Priority based on type and urgency if (notificationData.type === this.notificationTypes.URGENT_MESSAGE) { return 'urgent'; } if (notificationData.type === this.notificationTypes.CONVERSATION_ESCALATED) { return 'high'; } if (notificationData.urgency === 'urgent') { return 'urgent'; } if (notificationData.urgency === 'high') { return 'high'; } return 'medium'; } /** * Determine delivery channels */ determineChannels(notificationData) { const channels = [this.channels.WEBSOCKET]; // Always include WebSocket // Add additional channels based on priority and type if (notificationData.priority === 'urgent') { channels.push(this.channels.PUSH); if (notificationData.type === this.notificationTypes.CONVERSATION_ESCALATED) { channels.push(this.channels.EMAIL); } } return channels; } /** * Determine notification recipients */ async determineRecipients(notification) { try { const recipients = []; // If specific agent is mentioned if (notification.agentId) { const agent = await Agent.findById(notification.agentId); if (agent) { recipients.push({ type: 'agent', id: agent._id, name: agent.name, email: agent.email, preferences: agent.preferences?.notificationSettings || {} }); } } // If conversation is assigned if (notification.conversation?.agent) { const agent = await Agent.findById(notification.conversation.agent.id); if (agent && !recipients.find(r => r.id.equals(agent._id))) { recipients.push({ type: 'agent', id: agent._id, name: agent.name, email: agent.email, preferences: agent.preferences?.notificationSettings || {} }); } } // For unassigned urgent messages, notify available supervisors if (notification.priority === 'urgent' && recipients.length === 0) { const supervisors = await Agent.find({ role: { $in: ['supervisor', 'manager'] }, status: 'available', 'preferences.notificationSettings.urgentMessage': { $ne: false } }); supervisors.forEach(supervisor => { recipients.push({ type: 'agent', id: supervisor._id, name: supervisor.name, email: supervisor.email, preferences: supervisor.preferences?.notificationSettings || {} }); }); } // For escalations, notify managers if (notification.type === this.notificationTypes.CONVERSATION_ESCALATED) { const managers = await Agent.find({ role: { $in: ['manager', 'admin'] }, 'preferences.notificationSettings.escalation': { $ne: false } }); managers.forEach(manager => { if (!recipients.find(r => r.id.equals(manager._id))) { recipients.push({ type: 'agent', id: manager._id, name: manager.name, email: manager.email, preferences: manager.preferences?.notificationSettings || {} }); } }); } return recipients; } catch (error) { logger.error('[FAILED] Failed to determine notification recipients:', error); return []; } } /** * Deliver notification to recipient */ async deliverNotification(notification, recipient) { try { const deliveryResults = {}; // WebSocket delivery (real-time) if (notification.channels.includes(this.channels.WEBSOCKET)) { deliveryResults.websocket = await this.deliverWebSocketNotification(notification, recipient); } // Email delivery if (notification.channels.includes(this.channels.EMAIL) && recipient.preferences.email !== false) { deliveryResults.email = await this.deliverEmailNotification(notification, recipient); } // SMS delivery if (notification.channels.includes(this.channels.SMS) && recipient.preferences.sms !== false) { deliveryResults.sms = await this.deliverSMSNotification(notification, recipient); } // Push notification delivery if (notification.channels.includes(this.channels.PUSH) && recipient.preferences.push !== false) { deliveryResults.push = await this.deliverPushNotification(notification, recipient); } // Store notification in Redis for offline agents await this.storeNotificationForOfflineAgent(notification, recipient); return { recipientId: recipient.id, success: true, deliveryResults }; } catch (error) { logger.error('[FAILED] Failed to deliver notification:', error); return { recipientId: recipient.id, success: false, error: error.message }; } } /** * Deliver WebSocket notification */ async deliverWebSocketNotification(notification, recipient) { try { const socketData = { id: notification.id, type: notification.type, priority: notification.priority, timestamp: notification.timestamp, title: notification.content.title, message: notification.content.message, data: { platform: notification.platform, conversationId: notification.conversationId, customerId: notification.customerId, conversation: notification.conversation, customer: notification.customer } }; // Send to specific agent room this.io.to(`agent-${recipient.id}`).emit('notification', socketData); // Also send to general notifications room if urgent if (notification.priority === 'urgent') { this.io.to('notifications').emit('urgent-notification', socketData); } return { success: true, channel: 'websocket' }; } catch (error) { logger.error('[FAILED] WebSocket notification delivery failed:', error); return { success: false, channel: 'websocket', error: error.message }; } } /** * Deliver email notification */ async deliverEmailNotification(notification, recipient) { try { // This would integrate with an email service (SendGrid, AWS SES, etc.) // For now, we'll just log and queue it const emailData = { to: recipient.email, subject: notification.content.title, body: notification.content.emailBody || notification.content.message, priority: notification.priority, timestamp: notification.timestamp }; // Queue email for processing this.queues.email.push(emailData); logger.info(` Email notification queued for ${recipient.email}`); return { success: true, channel: 'email', queued: true }; } catch (error) { logger.error('[FAILED] Email notification delivery failed:', error); return { success: false, channel: 'email', error: error.message }; } } /** * Deliver SMS notification */ async deliverSMSNotification(notification, recipient) { try { // This would integrate with an SMS service (Twilio, AWS SNS, etc.) // For now, we'll just log and queue it const smsData = { to: recipient.phone, message: notification.content.smsMessage || notification.content.message, priority: notification.priority, timestamp: notification.timestamp }; // Queue SMS for processing this.queues.sms.push(smsData); logger.info(`[MOBILE] SMS notification queued for ${recipient.phone}`); return { success: true, channel: 'sms', queued: true }; } catch (error) { logger.error('[FAILED] SMS notification delivery failed:', error); return { success: false, channel: 'sms', error: error.message }; } } /** * Deliver push notification */ async deliverPushNotification(notification, recipient) { try { // This would integrate with a push notification service (Firebase, etc.) // For now, we'll just log and queue it const pushData = { userId: recipient.id, title: notification.content.title, body: notification.content.message, data: notification.data, priority: notification.priority, timestamp: notification.timestamp }; // Queue push notification for processing this.queues.push.push(pushData); logger.info(` Push notification queued for agent ${recipient.id}`); return { success: true, channel: 'push', queued: true }; } catch (error) { logger.error('[FAILED] Push notification delivery failed:', error); return { success: false, channel: 'push', error: error.message }; } } /** * Store notification for offline agent */ async storeNotificationForOfflineAgent(notification, recipient) { try { const key = `notifications:${recipient.id}`; const notificationData = { id: notification.id, type: notification.type, priority: notification.priority, timestamp: notification.timestamp, content: notification.content, data: notification.data || {} }; // Store in Redis list await this.redis.lpush(key, JSON.stringify(notificationData)); // Set expiration await this.redis.expire(key, this.config.ttl); // Keep only latest 100 notifications await this.redis.ltrim(key, 0, 99); } catch (error) { logger.error('[FAILED] Failed to store notification for offline agent:', error); } } /** * Generate notification content */ generateNotificationContent(notification) { const content = { title: '', message: '', emailBody: '', smsMessage: '' }; const customerName = notification.customer?.name || 'Client'; const platform = notification.platform || 'plateforme'; switch (notification.type) { case this.notificationTypes.NEW_MESSAGE: content.title = `Nouveau message - ${platform}`; content.message = `${customerName} a envoyé un nouveau message sur ${platform}`; break; case this.notificationTypes.NEW_CONVERSATION: content.title = `Nouvelle conversation - ${platform}`; content.message = `Nouvelle conversation avec ${customerName} sur ${platform}`; break; case this.notificationTypes.CONVERSATION_ASSIGNED: content.title = `Conversation assignée`; content.message = `Une conversation avec ${customerName} vous a été assignée`; break; case this.notificationTypes.CONVERSATION_ESCALATED: content.title = `Escalade de conversation`; content.message = `La conversation avec ${customerName} a été escaladée`; break; case this.notificationTypes.URGENT_MESSAGE: content.title = `Message urgent - ${platform}`; content.message = `${customerName} a envoyé un message urgent sur ${platform}`; break; case this.notificationTypes.CUSTOMER_SATISFACTION: content.title = `Évaluation client`; content.message = `${customerName} a évalué votre service`; break; default: content.title = `Notification - ${platform}`; content.message = `Nouvelle notification de ${customerName}`; } // Generate email body (more detailed) content.emailBody = this.generateEmailBody(notification, content); // Generate SMS message (shorter) content.smsMessage = content.message.substring(0, 160); return content; } /** * Generate email body */ generateEmailBody(notification, content) { let emailBody = `${content.message}\n\n`; if (notification.conversation) { emailBody += `Détails de la conversation:\n`; emailBody += `- Plateforme: ${notification.conversation.platform}\n`; emailBody += `- Statut: ${notification.conversation.status}\n`; emailBody += `- Urgence: ${notification.conversation.urgency}\n`; if (notification.conversation.customer) { emailBody += `- Client: ${notification.conversation.customer.name}\n`; } } emailBody += `\nHeure: ${notification.timestamp.toLocaleString('fr-FR')}\n`; emailBody += `\nConnectez-vous au dashboard pour plus de détails.`; return emailBody; } /** * Get agent notifications */ async getAgentNotifications(agentId, limit = 50) { try { const key = `notifications:${agentId}`; const notifications = await this.redis.lrange(key, 0, limit - 1); return notifications.map(notif => JSON.parse(notif)); } catch (error) { logger.error('[FAILED] Failed to get agent notifications:', error); return []; } } /** * Mark notification as read */ async markNotificationAsRead(agentId, notificationId) { try { const key = `notifications:${agentId}`; const notifications = await this.redis.lrange(key, 0, -1); const updatedNotifications = notifications.map(notifStr => { const notif = JSON.parse(notifStr); if (notif.id === notificationId) { notif.read = true; notif.readAt = new Date(); } return JSON.stringify(notif); }); // Replace the list await this.redis.del(key); if (updatedNotifications.length > 0) { await this.redis.lpush(key, ...updatedNotifications); await this.redis.expire(key, this.config.ttl); } } catch (error) { logger.error('[FAILED] Failed to mark notification as read:', error); } } /** * Start notification processors */ startNotificationProcessors() { // Process email queue setInterval(async () => { await this.processEmailQueue(); }, 30000); // Every 30 seconds // Process SMS queue setInterval(async () => { await this.processSMSQueue(); }, 30000); // Every 30 seconds // Process push notification queue setInterval(async () => { await this.processPushQueue(); }, 15000); // Every 15 seconds } /** * Process email queue */ async processEmailQueue() { try { const batch = this.queues.email.splice(0, this.config.batchSize); if (batch.length === 0) return; logger.info(` Processing ${batch.length} email notifications`); // Here you would integrate with your email service // For now, just log the emails batch.forEach(email => { logger.info(` Email to ${email.to}: ${email.subject}`); }); } catch (error) { logger.error('[FAILED] Failed to process email queue:', error); } } /** * Process SMS queue */ async processSMSQueue() { try { const batch = this.queues.sms.splice(0, this.config.batchSize); if (batch.length === 0) return; logger.info(`[MOBILE] Processing ${batch.length} SMS notifications`); // Here you would integrate with your SMS service // For now, just log the SMS messages batch.forEach(sms => { logger.info(`[MOBILE] SMS to ${sms.to}: ${sms.message}`); }); } catch (error) { logger.error('[FAILED] Failed to process SMS queue:', error); } } /** * Process push notification queue */ async processPushQueue() { try { const batch = this.queues.push.splice(0, this.config.batchSize); if (batch.length === 0) return; logger.info(` Processing ${batch.length} push notifications`); // Here you would integrate with your push notification service // For now, just log the push notifications batch.forEach(push => { logger.info(` Push to ${push.userId}: ${push.title}`); }); } catch (error) { logger.error('[FAILED] Failed to process push queue:', error); } } /** * Initialize metrics tracking */ initializeMetrics() { // Reset metrics every hour setInterval(() => { logger.info('[ANALYTICS] Notification metrics:', this.metrics); // Reset counters but keep averages this.metrics.totalNotifications = 0; this.metrics.deliveredNotifications = 0; this.metrics.failedNotifications = 0; }, 3600000); // 1 hour } /** * Update metrics */ updateMetrics(startTime, deliveryResults) { const deliveryTime = Date.now() - startTime; this.metrics.totalNotifications++; const successful = deliveryResults.filter(result => result.success).length; const failed = deliveryResults.filter(result => !result.success).length; this.metrics.deliveredNotifications += successful; this.metrics.failedNotifications += failed; // Update average delivery time this.metrics.averageDeliveryTime = (this.metrics.averageDeliveryTime * (this.metrics.totalNotifications - 1) + deliveryTime) / this.metrics.totalNotifications; } /** * Get notification metrics */ getMetrics() { return { ...this.metrics, successRate: this.metrics.totalNotifications > 0 ? (this.metrics.deliveredNotifications / this.metrics.totalNotifications) * 100 : 0, queueSizes: { email: this.queues.email.length, sms: this.queues.sms.length, push: this.queues.push.length } }; } /** * Health check */ isHealthy() { return this.isInitialized; } /** * Cleanup resources */ async cleanup() { try { this.isInitialized = false; logger.info('[COMPLETE] Notification Service cleanup completed'); } catch (error) { logger.error('[FAILED] Notification Service cleanup failed:', error); throw error; } } } module.exports = NotificationService;