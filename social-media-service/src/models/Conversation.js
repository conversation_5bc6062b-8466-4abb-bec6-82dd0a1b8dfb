/** * ============================================= * CONVERSATION MODEL * MongoDB model for social media conversations * Supports multi-platform conversation tracking * ============================================= */ const mongoose = require('mongoose'); const conversationSchema = new mongoose.Schema({ // Basic conversation info customerId: { type: mongoose.Schema.Types.ObjectId, ref: 'Customer', required: true, index: true }, platform: { type: String, required: true, enum: ['whatsapp', 'facebook', 'instagram', 'twitter', 'linkedin'], index: true }, platformConversationId: { type: String, required: false, index: true }, // Status and assignment status: { type: String, required: true, enum: ['open', 'assigned', 'pending', 'resolved', 'closed'], default: 'open', index: true }, assignedAgent: { type: mongoose.Schema.Types.ObjectId, ref: 'Agent', required: false, index: true }, assignedAt: { type: Date, required: false }, // Priority and routing urgency: { type: String, required: true, enum: ['low', 'medium', 'high', 'urgent'], default: 'medium', index: true }, priority: { type: Number, required: true, default: 1, min: 1, max: 10 }, // Content analysis intent: { type: String, required: false, enum: ['general', 'billing', 'technical', 'complaint', 'sales', 'support'], default: 'general' }, sentiment: { type: String, required: false, enum: ['very_positive', 'positive', 'neutral', 'negative', 'very_negative'], default: 'neutral' }, language: { type: String, required: true, default: 'fr' }, // AI and routing metadata requiresHuman: { type: Boolean, required: true, default: true }, suggestedSkills: [{ type: String, enum: [ 'general_support', 'billing_support', 'technical_support', 'product_support', 'complaint_handling', 'customer_retention', 'sales_support', 'account_management', 'whatsapp_support', 'facebook_support', 'instagram_support', 'twitter_support', 'linkedin_support' ] }], confidence: { type: Number, required: false, min: 0, max: 1, default: 0.5 }, // Escalation tracking escalationCount: { type: Number, required: true, default: 0 }, escalatedAt: { type: Date, required: false }, escalatedToCall: { type: Boolean, required: true, default: false }, escalationReason: { type: String, required: false }, // Message tracking messageCount: { type: Number, required: true, default: 0 }, lastMessage: { type: mongoose.Schema.Types.ObjectId, ref: 'Message', required: false }, lastActivity: { type: Date, required: true, default: Date.now, index: true }, lastAgentActivity: { type: Date, required: false }, lastCustomerActivity: { type: Date, required: false }, // Response time tracking firstResponseTime: { type: Number, // milliseconds required: false }, averageResponseTime: { type: Number, // milliseconds required: false }, // Resolution tracking resolvedAt: { type: Date, required: false }, resolutionTime: { type: Number, // milliseconds required: false }, customerSatisfaction: { rating: { type: Number, min: 1, max: 5, required: false }, feedback: { type: String, required: false }, submittedAt: { type: Date, required: false } }, // Tags and categories tags: [{ type: String, trim: true }], category: { type: String, required: false, enum: [ 'customer_service', 'technical_support', 'billing_inquiry', 'product_information', 'complaint', 'compliment', 'feature_request', 'bug_report', 'general_inquiry' ] }, // Platform-specific metadata metadata: { // WhatsApp specific whatsapp: { phoneNumber: String, businessAccountId: String, lastMessageId: String }, // Facebook specific facebook: { pageId: String, userId: String, threadId: String }, // Instagram specific instagram: { igId: String, mediaId: String, storyId: String }, // Twitter specific twitter: { userId: String, screenName: String, tweetId: String, conversationId: String }, // LinkedIn specific linkedin: { personId: String, conversationId: String, organizationId: String }, // Analysis results analysis: { multimodalResults: mongoose.Schema.Types.Mixed, mlResults: mongoose.Schema.Types.Mixed, lastAnalyzedAt: Date }, // Custom fields custom: mongoose.Schema.Types.Mixed }, // Timestamps createdAt: { type: Date, required: true, default: Date.now, index: true }, updatedAt: { type: Date, required: true, default: Date.now }, closedAt: { type: Date, required: false } }, { timestamps: true, collection: 'conversations' }); // Indexes for performance conversationSchema.index({ customerId: 1, platform: 1 }); conversationSchema.index({ assignedAgent: 1, status: 1 }); conversationSchema.index({ platform: 1, status: 1, urgency: -1 }); conversationSchema.index({ lastActivity: -1 }); conversationSchema.index({ createdAt: -1 }); conversationSchema.index({ status: 1, urgency: -1, createdAt: -1 }); // Compound indexes for common queries conversationSchema.index({ platform: 1, status: 1, assignedAgent: 1, lastActivity: -1 }); conversationSchema.index({ customerId: 1, platform: 1, status: 1, createdAt: -1 }); // Text search index conversationSchema.index({ 'metadata.analysis.intent': 'text', 'metadata.analysis.entities': 'text', tags: 'text' }); // Virtual for conversation duration conversationSchema.virtual('duration').get(function() { if (this.closedAt) { return this.closedAt - this.createdAt; } return Date.now() - this.createdAt; }); // Virtual for response time status conversationSchema.virtual('responseTimeStatus').get(function() { if (!this.firstResponseTime) return 'pending'; const slaLimits = { urgent: 30000, // 30 seconds high: 120000, // 2 minutes medium: 300000, // 5 minutes low: 900000 // 15 minutes }; const limit = slaLimits[this.urgency] || slaLimits.medium; return this.firstResponseTime <= limit ? 'within_sla' : 'exceeded_sla'; }); // Pre-save middleware conversationSchema.pre('save', function(next) { this.updatedAt = new Date(); // Update priority based on urgency and age const ageInHours = (Date.now() - this.createdAt) / (1000 * 60 * 60); const urgencyMultiplier = { urgent: 4, high: 3, medium: 2, low: 1 }; this.priority = Math.min(10, Math.max(1, (urgencyMultiplier[this.urgency] || 2) + Math.floor(ageInHours / 2) )); next(); }); // Instance methods conversationSchema.methods.updateActivity = function(isAgent = false) { this.lastActivity = new Date(); if (isAgent) { this.lastAgentActivity = new Date(); } else { this.lastCustomerActivity = new Date(); } return this.save(); }; conversationSchema.methods.escalate = function(reason) { this.escalationCount += 1; this.escalatedAt = new Date(); this.escalationReason = reason; this.urgency = this.urgency === 'urgent' ? 'urgent' : this.urgency === 'high' ? 'urgent' : 'high'; return this.save(); }; conversationSchema.methods.assignToAgent = function(agentId) { this.assignedAgent = agentId; this.assignedAt = new Date(); this.status = 'assigned'; return this.save(); }; conversationSchema.methods.resolve = function(resolutionData = {}) { this.status = 'resolved'; this.resolvedAt = new Date(); this.resolutionTime = this.resolvedAt - this.createdAt; if (resolutionData.satisfaction) { this.customerSatisfaction = resolutionData.satisfaction; } return this.save(); }; conversationSchema.methods.close = function() { this.status = 'closed'; this.closedAt = new Date(); return this.save(); }; // Static methods conversationSchema.statics.findByPlatform = function(platform, options = {}) { const query = { platform }; if (options.status) query.status = options.status; if (options.urgency) query.urgency = options.urgency; if (options.assignedAgent) query.assignedAgent = options.assignedAgent; return this.find(query) .populate('customerId', 'name email phone') .populate('assignedAgent', 'name email') .populate('lastMessage') .sort({ lastActivity: -1 }) .limit(options.limit || 50); }; conversationSchema.statics.findActiveConversations = function(agentId) { return this.find({ assignedAgent: agentId, status: { $in: ['assigned', 'pending'] } }) .populate('customerId', 'name email phone') .populate('lastMessage') .sort({ lastActivity: -1 }); }; conversationSchema.statics.getConversationStats = function(filters = {}) { const pipeline = [ { $match: filters }, { $group: { _id: null, total: { $sum: 1 }, open: { $sum: { $cond: [{ $eq: ['$status', 'open'] }, 1, 0] } }, assigned: { $sum: { $cond: [{ $eq: ['$status', 'assigned'] }, 1, 0] } }, resolved: { $sum: { $cond: [{ $eq: ['$status', 'resolved'] }, 1, 0] } }, avgResponseTime: { $avg: '$firstResponseTime' }, avgResolutionTime: { $avg: '$resolutionTime' } } } ]; return this.aggregate(pipeline); }; // Export model module.exports = mongoose.model('Conversation', conversationSchema);