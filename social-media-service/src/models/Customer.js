/** * ============================================= * [USER] CUSTOMER MODEL * MongoDB model for social media customers * Unified customer profile across all platforms * ============================================= */ const mongoose = require('mongoose'); const customerSchema = new mongoose.Schema({ // Basic customer info name: { type: String, required: false, trim: true, maxlength: 100 }, email: { type: String, required: false, trim: true, lowercase: true, match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email'] }, phone: { type: String, required: false, trim: true, index: true }, // Primary platform (where customer was first encountered) primaryPlatform: { type: String, required: true, enum: ['whatsapp', 'facebook', 'instagram', 'twitter', 'linkedin'], index: true }, // Platform-specific identifiers platformIds: { whatsapp: { phoneNumber: String, profileName: String, businessAccountId: String }, facebook: { userId: String, pageId: String, firstName: String, lastName: String, profilePic: String }, instagram: { igId: String, username: String, profilePic: String, followerCount: Number }, twitter: { userId: String, screenName: String, name: String, profileImageUrl: String, followerCount: Number, verified: Boolean }, linkedin: { personId: String, firstName: String, lastName: String, headline: String, profilePicture: String, industry: String } }, // Customer status and segmentation status: { type: String, required: true, enum: ['active', 'inactive', 'blocked', 'vip'], default: 'active', index: true }, segment: { type: String, required: false, enum: ['new', 'regular', 'premium', 'enterprise', 'at_risk', 'churned'], default: 'new' }, // Customer preferences preferences: { preferredPlatform: { type: String, enum: ['whatsapp', 'facebook', 'instagram', 'twitter', 'linkedin'] }, preferredAgent: { type: mongoose.Schema.Types.ObjectId, ref: 'Agent' }, language: { type: String, default: 'fr' }, timezone: { type: String, default: 'Europe/Paris' }, communicationStyle: { type: String, enum: ['formal', 'casual', 'technical'], default: 'casual' }, contactHours: { start: String, // HH:MM format end: String, // HH:MM format days: [String] // ['monday', 'tuesday', ...] }, notifications: { email: { type: Boolean, default: true }, sms: { type: Boolean, default: false }, push: { type: Boolean, default: true } } }, // Customer analytics analytics: { totalConversations: { type: Number, default: 0 }, totalMessages: { type: Number, default: 0 }, averageResponseTime: Number, // milliseconds lastInteractionAt: Date, firstInteractionAt: Date, // Platform-specific stats platformStats: { whatsapp: { conversations: { type: Number, default: 0 }, messages: { type: Number, default: 0 }, lastActive: Date }, facebook: { conversations: { type: Number, default: 0 }, messages: { type: Number, default: 0 }, lastActive: Date }, instagram: { conversations: { type: Number, default: 0 }, messages: { type: Number, default: 0 }, lastActive: Date }, twitter: { conversations: { type: Number, default: 0 }, messages: { type: Number, default: 0 }, lastActive: Date }, linkedin: { conversations: { type: Number, default: 0 }, messages: { type: Number, default: 0 }, lastActive: Date } }, // Behavioral patterns behavior: { averageSessionDuration: Number, preferredContactTime: String, // HH:MM format responsePattern: { type: String, enum: ['immediate', 'quick', 'delayed', 'sporadic'], default: 'quick' }, sentimentTrend: { type: String, enum: ['improving', 'stable', 'declining'], default: 'stable' }, topIntents: [String], commonIssues: [String] } }, // Customer satisfaction satisfaction: { overallRating: { type: Number, min: 1, max: 5 }, lastSurveyDate: Date, npsScore: { type: Number, min: 0, max: 10 }, feedback: [{ rating: { type: Number, min: 1, max: 5 }, comment: String, date: Date, conversationId: { type: mongoose.Schema.Types.ObjectId, ref: 'Conversation' } }] }, // Tags and categories tags: [{ type: String, trim: true }], category: { type: String, enum: [ 'individual', 'business', 'enterprise', 'government', 'non_profit' ], default: 'individual' }, // Location and demographics location: { country: String, region: String, city: String, timezone: String, coordinates: { latitude: Number, longitude: Number } }, demographics: { ageRange: { type: String, enum: ['18-24', '25-34', '35-44', '45-54', '55-64', '65+'] }, gender: { type: String, enum: ['male', 'female', 'other', 'prefer_not_to_say'] }, occupation: String, interests: [String] }, // Account information account: { freeSubscriptionId: String, planType: { type: String, enum: ['free', '2_euro', '19_99_euro', 'enterprise'] }, accountStatus: { type: String, enum: ['active', 'suspended', 'cancelled'] }, registrationDate: Date, lastBillDate: Date, nextBillDate: Date }, // Privacy and consent privacy: { dataProcessingConsent: { type: Boolean, default: false }, marketingConsent: { type: Boolean, default: false }, consentDate: Date, gdprCompliant: { type: Boolean, default: true }, dataRetentionUntil: Date }, // Notes and history notes: [{ content: String, createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'Agent' }, createdAt: { type: Date, default: Date.now }, type: { type: String, enum: ['general', 'complaint', 'compliment', 'technical', 'billing'], default: 'general' } }], // Timestamps createdAt: { type: Date, required: true, default: Date.now, index: true }, updatedAt: { type: Date, required: true, default: Date.now }, lastActiveAt: { type: Date, index: true } }, { timestamps: true, collection: 'customers' }); // Indexes for performance customerSchema.index({ email: 1 }, { sparse: true }); customerSchema.index({ phone: 1 }, { sparse: true }); customerSchema.index({ primaryPlatform: 1, status: 1 }); customerSchema.index({ 'platformIds.whatsapp.phoneNumber': 1 }, { sparse: true }); customerSchema.index({ 'platformIds.facebook.userId': 1 }, { sparse: true }); customerSchema.index({ 'platformIds.instagram.igId': 1 }, { sparse: true }); customerSchema.index({ 'platformIds.twitter.userId': 1 }, { sparse: true }); customerSchema.index({ 'platformIds.linkedin.personId': 1 }, { sparse: true }); // Compound indexes customerSchema.index({ status: 1, segment: 1, lastActiveAt: -1 }); customerSchema.index({ primaryPlatform: 1, createdAt: -1 }); // Text search index customerSchema.index({ name: 'text', email: 'text', tags: 'text', 'notes.content': 'text' }); // Virtual for full name customerSchema.virtual('fullName').get(function() { if (this.name) return this.name; // Try to construct from platform data const fb = this.platformIds.facebook; const li = this.platformIds.linkedin; if (fb && fb.firstName && fb.lastName) { return `${fb.firstName} ${fb.lastName}`; } if (li && li.firstName && li.lastName) { return `${li.firstName} ${li.lastName}`; } return 'Unknown Customer'; }); // Virtual for primary contact customerSchema.virtual('primaryContact').get(function() { if (this.email) return this.email; if (this.phone) return this.phone; // Return platform-specific identifier const platformId = this.platformIds[this.primaryPlatform]; if (platformId) { switch (this.primaryPlatform) { case 'whatsapp': return platformId.phoneNumber; case 'facebook': return platformId.userId; case 'instagram': return platformId.username; case 'twitter': return platformId.screenName; case 'linkedin': return platformId.personId; } } return 'Unknown'; }); // Pre-save middleware customerSchema.pre('save', function(next) { this.updatedAt = new Date(); // Update lastActiveAt if analytics changed if (this.isModified('analytics.lastInteractionAt')) { this.lastActiveAt = this.analytics.lastInteractionAt; } next(); }); // Instance methods customerSchema.methods.updateActivity = function(platform) { const now = new Date(); this.analytics.lastInteractionAt = now; this.lastActiveAt = now; if (platform && this.analytics.platformStats[platform]) { this.analytics.platformStats[platform].lastActive = now; } return this.save(); }; customerSchema.methods.addNote = function(content, agentId, type = 'general') { this.notes.push({ content, createdBy: agentId, type, createdAt: new Date() }); return this.save(); }; customerSchema.methods.updateSatisfaction = function(rating, comment, conversationId) { this.satisfaction.feedback.push({ rating, comment, date: new Date(), conversationId }); // Update overall rating (average of last 5 ratings) const recentFeedback = this.satisfaction.feedback.slice(-5); const avgRating = recentFeedback.reduce((sum, f) => sum + f.rating, 0) / recentFeedback.length; this.satisfaction.overallRating = Math.round(avgRating * 10) / 10; return this.save(); }; customerSchema.methods.linkPlatform = function(platform, platformData) { if (!this.platformIds[platform]) { this.platformIds[platform] = {}; } Object.assign(this.platformIds[platform], platformData); return this.save(); }; // Static methods customerSchema.statics.findByPlatformId = function(platform, platformId) { const query = {}; query[`platformIds.${platform}`] = { $exists: true }; switch (platform) { case 'whatsapp': query['platformIds.whatsapp.phoneNumber'] = platformId; break; case 'facebook': query['platformIds.facebook.userId'] = platformId; break; case 'instagram': query['platformIds.instagram.igId'] = platformId; break; case 'twitter': query['platformIds.twitter.userId'] = platformId; break; case 'linkedin': query['platformIds.linkedin.personId'] = platformId; break; } return this.findOne(query); }; customerSchema.statics.getCustomerStats = function(filters = {}) { const pipeline = [ { $match: filters }, { $group: { _id: null, total: { $sum: 1 }, active: { $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] } }, byPlatform: { $push: '$primaryPlatform' }, bySegment: { $push: '$segment' } } } ]; return this.aggregate(pipeline); }; customerSchema.statics.findActiveCustomers = function(platform, days = 30) { const cutoffDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000); const query = { status: 'active', lastActiveAt: { $gte: cutoffDate } }; if (platform) { query.primaryPlatform = platform; } return this.find(query).sort({ lastActiveAt: -1 }); }; // Export model module.exports = mongoose.model('Customer', customerSchema);