/** * ============================================= * [ADMIN] AGENT MODEL * MongoDB model for customer service agents * Handles agent skills, availability, and performance tracking * ============================================= */ const mongoose = require('mongoose'); const agentSchema = new mongoose.Schema({ // Basic agent info name: { type: String, required: true, trim: true, maxlength: 100 }, email: { type: String, required: true, unique: true, trim: true, lowercase: true, match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email'] }, employeeId: { type: String, required: true, unique: true, trim: true }, // Agent status and availability status: { type: String, required: true, enum: ['available', 'busy', 'away', 'offline'], default: 'offline', index: true }, isOnline: { type: Boolean, required: true, default: false, index: true }, lastSeen: { type: Date, index: true }, // Role and permissions role: { type: String, required: true, enum: ['agent', 'senior', 'supervisor', 'manager', 'admin'], default: 'agent', index: true }, department: { type: String, required: true, enum: [ 'customer_service', 'technical_support', 'billing', 'sales', 'retention', 'social_media' ], default: 'customer_service' }, // Skills and capabilities skills: [{ type: String, enum: [ 'general_support', 'billing_support', 'technical_support', 'product_support', 'complaint_handling', 'customer_retention', 'sales_support', 'account_management', 'whatsapp_support', 'facebook_support', 'instagram_support', 'twitter_support', 'linkedin_support', 'multilingual', 'escalation_handling', 'vip_support' ] }], languages: [{ code: { type: String, required: true }, name: { type: String, required: true }, proficiency: { type: String, enum: ['basic', 'intermediate', 'advanced', 'native'], required: true } }], // Platform expertise platformExpertise: { whatsapp: { level: { type: String, enum: ['none', 'basic', 'intermediate', 'advanced', 'expert'], default: 'none' }, certified: { type: Boolean, default: false } }, facebook: { level: { type: String, enum: ['none', 'basic', 'intermediate', 'advanced', 'expert'], default: 'none' }, certified: { type: Boolean, default: false } }, instagram: { level: { type: String, enum: ['none', 'basic', 'intermediate', 'advanced', 'expert'], default: 'none' }, certified: { type: Boolean, default: false } }, twitter: { level: { type: String, enum: ['none', 'basic', 'intermediate', 'advanced', 'expert'], default: 'none' }, certified: { type: Boolean, default: false } }, linkedin: { level: { type: String, enum: ['none', 'basic', 'intermediate', 'advanced', 'expert'], default: 'none' }, certified: { type: Boolean, default: false } } }, // Workload and capacity maxConcurrentConversations: { type: Number, required: true, default: 5, min: 1, max: 20 }, currentLoad: { type: Number, required: true, default: 0, min: 0 }, canHandleUrgent: { type: Boolean, required: true, default: true }, // Scheduling workSchedule: { timezone: { type: String, default: 'Europe/Paris' }, shifts: [{ day: { type: String, enum: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'], required: true }, startTime: { type: String, // HH:MM format required: true }, endTime: { type: String, // HH:MM format required: true }, isActive: { type: Boolean, default: true } }] }, // Performance metrics performance: { // Response times averageFirstResponseTime: Number, // milliseconds averageResponseTime: Number, // milliseconds // Resolution metrics averageResolutionTime: Number, // milliseconds resolutionRate: { type: Number, min: 0, max: 1, default: 0 }, // Customer satisfaction averageRating: { type: Number, min: 1, max: 5 }, totalRatings: { type: Number, default: 0 }, // Conversation metrics totalConversations: { type: Number, default: 0 }, totalMessages: { type: Number, default: 0 }, // Platform-specific stats platformStats: { whatsapp: { conversations: { type: Number, default: 0 }, messages: { type: Number, default: 0 }, avgResponseTime: Number, avgRating: Number }, facebook: { conversations: { type: Number, default: 0 }, messages: { type: Number, default: 0 }, avgResponseTime: Number, avgRating: Number }, instagram: { conversations: { type: Number, default: 0 }, messages: { type: Number, default: 0 }, avgResponseTime: Number, avgRating: Number }, twitter: { conversations: { type: Number, default: 0 }, messages: { type: Number, default: 0 }, avgResponseTime: Number, avgRating: Number }, linkedin: { conversations: { type: Number, default: 0 }, messages: { type: Number, default: 0 }, avgResponseTime: Number, avgRating: Number } }, // Quality metrics escalationRate: { type: Number, min: 0, max: 1, default: 0 }, // Time tracking totalWorkTime: Number, // milliseconds totalActiveTime: Number, // milliseconds lastPerformanceUpdate: Date }, // Assignment tracking lastAssigned: { type: Date, index: true }, currentConversations: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Conversation' }], // Preferences preferences: { autoAssign: { type: Boolean, default: true }, maxAutoAssign: { type: Number, default: 3 }, preferredPlatforms: [{ type: String, enum: ['whatsapp', 'facebook', 'instagram', 'twitter', 'linkedin'] }], notificationSettings: { newConversation: { type: Boolean, default: true }, urgentMessage: { type: Boolean, default: true }, escalation: { type: Boolean, default: true }, customerRating: { type: Boolean, default: true } } }, // Training and certifications training: { completedCourses: [{ name: String, completedAt: Date, certificateId: String, expiresAt: Date }], certifications: [{ name: String, issuedBy: String, issuedAt: Date, expiresAt: Date, certificateId: String }], lastTrainingDate: Date, nextTrainingDue: Date }, // Contact information contact: { phone: String, extension: String, emergencyContact: { name: String, phone: String, relationship: String } }, // Employment details employment: { hireDate: Date, contractType: { type: String, enum: ['full_time', 'part_time', 'contract', 'intern'] }, location: { type: String, enum: ['office', 'remote', 'hybrid'] }, manager: { type: mongoose.Schema.Types.ObjectId, ref: 'Agent' } }, // Timestamps createdAt: { type: Date, required: true, default: Date.now, index: true }, updatedAt: { type: Date, required: true, default: Date.now } }, { timestamps: true, collection: 'agents' }); // Indexes for performance agentSchema.index({ email: 1 }, { unique: true }); agentSchema.index({ employeeId: 1 }, { unique: true }); agentSchema.index({ status: 1, isOnline: 1 }); agentSchema.index({ role: 1, department: 1 }); agentSchema.index({ skills: 1 }); agentSchema.index({ lastAssigned: 1 }); // Compound indexes agentSchema.index({ status: 1, currentLoad: 1, maxConcurrentConversations: 1 }); agentSchema.index({ department: 1, skills: 1, status: 1 }); // Virtual for availability agentSchema.virtual('isAvailable').get(function() { return this.status === 'available' && this.isOnline && this.currentLoad < this.maxConcurrentConversations; }); // Virtual for capacity percentage agentSchema.virtual('capacityPercentage').get(function() { return Math.round((this.currentLoad / this.maxConcurrentConversations) * 100); }); // Virtual for full name with role agentSchema.virtual('displayName').get(function() { return `${this.name} (${this.role})`; }); // Pre-save middleware agentSchema.pre('save', function(next) { this.updatedAt = new Date(); // Update lastSeen when status changes to offline if (this.isModified('status') && this.status === 'offline') { this.lastSeen = new Date(); this.isOnline = false; } // Update isOnline based on status if (this.isModified('status')) { this.isOnline = ['available', 'busy', 'away'].includes(this.status); } next(); }); // Instance methods agentSchema.methods.setStatus = function(status) { this.status = status; this.isOnline = ['available', 'busy', 'away'].includes(status); if (status === 'offline') { this.lastSeen = new Date(); } return this.save(); }; agentSchema.methods.assignConversation = function(conversationId) { if (this.currentLoad >= this.maxConcurrentConversations) { throw new Error('Agent at maximum capacity'); } this.currentConversations.push(conversationId); this.currentLoad += 1; this.lastAssigned = new Date(); if (this.currentLoad >= this.maxConcurrentConversations) { this.status = 'busy'; } return this.save(); }; agentSchema.methods.unassignConversation = function(conversationId) { this.currentConversations = this.currentConversations.filter( id => !id.equals(conversationId) ); this.currentLoad = Math.max(0, this.currentLoad - 1); if (this.status === 'busy' && this.currentLoad < this.maxConcurrentConversations) { this.status = 'available'; } return this.save(); }; agentSchema.methods.updatePerformance = function(metrics) { Object.assign(this.performance, metrics); this.performance.lastPerformanceUpdate = new Date(); return this.save(); }; agentSchema.methods.addSkill = function(skill) { if (!this.skills.includes(skill)) { this.skills.push(skill); return this.save(); } return Promise.resolve(this); }; agentSchema.methods.removeSkill = function(skill) { this.skills = this.skills.filter(s => s !== skill); return this.save(); }; agentSchema.methods.isWorkingNow = function() { const now = new Date(); const dayName = now.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase(); const currentTime = now.toTimeString().slice(0, 5); // HH:MM format const todayShift = this.workSchedule.shifts.find( shift => shift.day === dayName && shift.isActive ); if (!todayShift) return false; return currentTime >= todayShift.startTime && currentTime <= todayShift.endTime; }; // Static methods agentSchema.statics.findAvailable = function(skills = [], platform = null) { const query = { status: 'available', isOnline: true, $expr: { $lt: ['$currentLoad', '$maxConcurrentConversations'] } }; if (skills.length > 0) { query.skills = { $in: skills }; } if (platform) { query[`platformExpertise.${platform}.level`] = { $ne: 'none' }; } return this.find(query).sort({ currentLoad: 1, lastAssigned: 1 }); }; agentSchema.statics.findBySkills = function(skills) { return this.find({ skills: { $in: skills }, status: { $ne: 'offline' } }).sort({ currentLoad: 1 }); }; agentSchema.statics.getAgentStats = function(filters = {}) { const pipeline = [ { $match: filters }, { $group: { _id: null, total: { $sum: 1 }, available: { $sum: { $cond: [{ $eq: ['$status', 'available'] }, 1, 0] } }, busy: { $sum: { $cond: [{ $eq: ['$status', 'busy'] }, 1, 0] } }, away: { $sum: { $cond: [{ $eq: ['$status', 'away'] }, 1, 0] } }, offline: { $sum: { $cond: [{ $eq: ['$status', 'offline'] }, 1, 0] } }, avgLoad: { $avg: '$currentLoad' }, totalLoad: { $sum: '$currentLoad' } } } ]; return this.aggregate(pipeline); }; agentSchema.statics.getPerformanceReport = function(agentId, startDate, endDate) { // This would typically involve aggregating data from conversations and messages // For now, return the agent's current performance metrics return this.findById(agentId).select('performance'); }; // Export model module.exports = mongoose.model('Agent', agentSchema);