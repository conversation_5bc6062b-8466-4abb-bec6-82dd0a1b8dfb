/** * ============================================= * MESSAGE MODEL * MongoDB model for social media messages * Supports multi-platform message storage and analysis * ============================================= */ const mongoose = require('mongoose'); const messageSchema = new mongoose.Schema({ // Basic message info platform: { type: String, required: true, enum: ['whatsapp', 'facebook', 'instagram', 'twitter', 'linkedin'], index: true }, platformMessageId: { type: String, required: true, index: true }, conversationId: { type: mongoose.Schema.Types.ObjectId, ref: 'Conversation', required: true, index: true }, customerId: { type: mongoose.Schema.Types.ObjectId, ref: 'Customer', required: true, index: true }, agentId: { type: mongoose.Schema.Types.ObjectId, ref: 'Agent', required: false, index: true }, // Message direction and type direction: { type: String, required: true, enum: ['inbound', 'outbound'], index: true }, type: { type: String, required: true, enum: [ 'text', 'image', 'audio', 'video', 'document', 'location', 'contact', 'interactive', 'template', 'system' ], index: true }, // Message content text: { type: String, required: false, maxlength: 10000 }, media: [{ type: { type: String, required: true, enum: ['image', 'audio', 'video', 'document', 'file'] }, id: String, url: String, filename: String, mimeType: String, size: Number, caption: String, thumbnail: String, duration: Number, // for audio/video dimensions: { width: Number, height: Number } }], // Message status status: { type: String, required: true, enum: ['sent', 'delivered', 'read', 'failed', 'pending'], default: 'sent', index: true }, // Timestamps timestamp: { type: Date, required: true, index: true }, deliveredAt: { type: Date, required: false }, readAt: { type: Date, required: false }, // Analysis results analysis: { // Text analysis intent: { primary: String, confidence: Number, alternatives: [{ intent: String, confidence: Number }] }, sentiment: { label: { type: String, enum: ['very_positive', 'positive', 'neutral', 'negative', 'very_negative'] }, score: Number, confidence: Number }, emotion: { dominant: String, confidence: Number, emotions: mongoose.Schema.Types.Mixed }, urgency: { level: { type: String, enum: ['low', 'medium', 'high', 'urgent'] }, score: Number, confidence: Number }, entities: mongoose.Schema.Types.Mixed, keywords: [String], language: String, // Multimodal analysis multimodal: { confidence: Number, fusionResult: mongoose.Schema.Types.Mixed, modalityContributions: mongoose.Schema.Types.Mixed }, // Analysis metadata analyzedAt: Date, analysisVersion: String, processingTime: Number }, // Interactive message data interactive: { type: { type: String, enum: ['button', 'list', 'quick_reply', 'postback'] }, payload: String, title: String, buttons: [{ id: String, title: String, payload: String }], listItems: [{ id: String, title: String, description: String, payload: String }] }, // Template message data template: { name: String, language: String, parameters: [mongoose.Schema.Types.Mixed] }, // Context and threading replyTo: { type: mongoose.Schema.Types.ObjectId, ref: 'Message', required: false }, threadId: String, // Platform-specific metadata metadata: { // WhatsApp specific whatsapp: { messageId: String, from: String, to: String, context: { messageId: String, from: String, id: String, forwarded: Boolean, frequentlyForwarded: Boolean }, profile: { name: String }, referral: { sourceUrl: String, sourceId: String, sourceType: String, headline: String, body: String, mediaType: String, imageUrl: String, videoUrl: String, thumbnailUrl: String } }, // Facebook specific facebook: { messageId: String, senderId: String, recipientId: String, pageId: String, appId: String, quickReply: { payload: String }, postback: { payload: String, title: String, referral: mongoose.Schema.Types.Mixed }, referral: mongoose.Schema.Types.Mixed, isEcho: Boolean }, // Instagram specific instagram: { messageId: String, senderId: String, recipientId: String, igId: String, storyReply: { storyId: String, mediaType: String, url: String }, isEcho: Boolean }, // Twitter specific twitter: { tweetId: String, userId: String, screenName: String, conversationId: String, inReplyToStatusId: String, inReplyToUserId: String, isRetweet: Boolean, retweetCount: Number, favoriteCount: Number, hashtags: [String], mentions: [{ screenName: String, name: String, id: String }], urls: [{ url: String, expandedUrl: String, displayUrl: String }] }, // LinkedIn specific linkedin: { messageId: String, conversationId: String, senderId: String, recipientId: String, organizationId: String, activityUrn: String }, // Processing metadata processing: { normalized: Boolean, normalizationVersion: String, originalFormat: mongoose.Schema.Types.Mixed, processingErrors: [String] }, // Custom fields custom: mongoose.Schema.Types.Mixed }, // Message flags flags: { isSpam: { type: Boolean, default: false }, isImportant: { type: Boolean, default: false }, requiresFollowUp: { type: Boolean, default: false }, isEscalated: { type: Boolean, default: false }, isAutomated: { type: Boolean, default: false } }, // Timestamps createdAt: { type: Date, required: true, default: Date.now, index: true }, updatedAt: { type: Date, required: true, default: Date.now } }, { timestamps: true, collection: 'messages' }); // Indexes for performance messageSchema.index({ conversationId: 1, timestamp: -1 }); messageSchema.index({ customerId: 1, timestamp: -1 }); messageSchema.index({ platform: 1, timestamp: -1 }); messageSchema.index({ agentId: 1, timestamp: -1 }); messageSchema.index({ platformMessageId: 1, platform: 1 }, { unique: true }); // Compound indexes for common queries messageSchema.index({ conversationId: 1, direction: 1, timestamp: -1 }); messageSchema.index({ platform: 1, status: 1, timestamp: -1 }); messageSchema.index({ customerId: 1, platform: 1, timestamp: -1 }); // Text search index messageSchema.index({ text: 'text', 'analysis.entities': 'text', 'analysis.keywords': 'text' }); // Virtual for message age messageSchema.virtual('age').get(function() { return Date.now() - this.timestamp; }); // Virtual for response time (for outbound messages) messageSchema.virtual('responseTime').get(function() { if (this.direction !== 'outbound' || !this.replyTo) return null; // This would need to be populated with the original message // return this.timestamp - this.replyTo.timestamp; return null; }); // Pre-save middleware messageSchema.pre('save', function(next) { this.updatedAt = new Date(); // Set flags based on analysis if (this.analysis) { if (this.analysis.urgency && this.analysis.urgency.level === 'urgent') { this.flags.isImportant = true; } if (this.analysis.intent && this.analysis.intent.primary === 'complaint') { this.flags.requiresFollowUp = true; } } next(); }); // Post-save middleware to update conversation messageSchema.post('save', async function(doc) { try { const Conversation = mongoose.model('Conversation'); await Conversation.findByIdAndUpdate(doc.conversationId, { lastMessage: doc._id, lastActivity: doc.timestamp, $inc: { messageCount: 1 }, ...(doc.direction === 'inbound' ? { lastCustomerActivity: doc.timestamp } : { lastAgentActivity: doc.timestamp }) }); } catch (error) { console.error('Error updating conversation after message save:', error); } }); // Instance methods messageSchema.methods.markAsRead = function() { this.status = 'read'; this.readAt = new Date(); return this.save(); }; messageSchema.methods.markAsDelivered = function() { this.status = 'delivered'; this.deliveredAt = new Date(); return this.save(); }; messageSchema.methods.addAnalysis = function(analysisData) { this.analysis = { ...this.analysis, ...analysisData, analyzedAt: new Date() }; return this.save(); }; messageSchema.methods.flag = function(flagType, value = true) { if (this.flags.hasOwnProperty(flagType)) { this.flags[flagType] = value; return this.save(); } throw new Error(`Invalid flag type: ${flagType}`); }; // Static methods messageSchema.statics.findByConversation = function(conversationId, options = {}) { const query = { conversationId }; if (options.direction) query.direction = options.direction; if (options.type) query.type = options.type; if (options.status) query.status = options.status; return this.find(query) .populate('agentId', 'name email') .sort({ timestamp: options.sortOrder || 1 }) .limit(options.limit || 100); }; messageSchema.statics.findByCustomer = function(customerId, options = {}) { const query = { customerId }; if (options.platform) query.platform = options.platform; if (options.direction) query.direction = options.direction; return this.find(query) .populate('conversationId', 'platform status') .populate('agentId', 'name email') .sort({ timestamp: -1 }) .limit(options.limit || 50); }; messageSchema.statics.getMessageStats = function(filters = {}) { const pipeline = [ { $match: filters }, { $group: { _id: null, total: { $sum: 1 }, inbound: { $sum: { $cond: [{ $eq: ['$direction', 'inbound'] }, 1, 0] } }, outbound: { $sum: { $cond: [{ $eq: ['$direction', 'outbound'] }, 1, 0] } }, byPlatform: { $push: { platform: '$platform', direction: '$direction' } } } } ]; return this.aggregate(pipeline); }; messageSchema.statics.findUnanalyzed = function(limit = 100) { return this.find({ 'analysis.analyzedAt': { $exists: false }, direction: 'inbound', type: { $in: ['text', 'image', 'audio', 'video'] } }) .sort({ timestamp: 1 }) .limit(limit); }; messageSchema.statics.searchMessages = function(searchTerm, options = {}) { const query = { $text: { $search: searchTerm } }; if (options.platform) query.platform = options.platform; if (options.conversationId) query.conversationId = options.conversationId; if (options.customerId) query.customerId = options.customerId; return this.find(query, { score: { $meta: 'textScore' } }) .sort({ score: { $meta: 'textScore' }, timestamp: -1 }) .limit(options.limit || 20); }; // Export model module.exports = mongoose.model('Message', messageSchema);