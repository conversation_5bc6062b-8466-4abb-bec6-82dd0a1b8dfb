/** * ============================================= * [MOBILE] WHATSAPP HANDLER * WhatsApp Business API integration with webhook handling * Supports message sending, media processing, and status updates * ============================================= */ const axios = require('axios'); const crypto = require('crypto'); const fs = require('fs').promises; const path = require('path'); const FormData = require('form-data'); const logger = require('../utils/logger'); const Message = require('../models/Message'); const Conversation = require('../models/Conversation'); const Customer = require('../models/Customer'); class WhatsAppHandler { constructor(messageRouterService, notificationService) { this.messageRouterService = messageRouterService; this.notificationService = notificationService; this.isInitialized = false; // WhatsApp Business API configuration this.config = { apiVersion: 'v18.0', baseUrl: 'https://graph.facebook.com', phoneNumberId: process.env.WHATSAPP_PHONE_NUMBER_ID, accessToken: process.env.WHATSAPP_ACCESS_TOKEN, webhookVerifyToken: process.env.WHATSAPP_WEBHOOK_VERIFY_TOKEN, businessAccountId: process.env.WHATSAPP_BUSINESS_ACCOUNT_ID }; // Message types and limits this.messageTypes = { text: { maxLength: 4096 }, image: { maxSize: 5 * 1024 * 1024, formats: ['jpeg', 'jpg', 'png'] }, audio: { maxSize: 16 * 1024 * 1024, formats: ['aac', 'mp4', 'mpeg', 'amr', 'ogg'] }, video: { maxSize: 16 * 1024 * 1024, formats: ['mp4', '3gpp'] }, document: { maxSize: 100 * 1024 * 1024 } }; // Rate limiting this.rateLimits = { messages: { limit: 1000, window: 60000 }, // 1000 messages per minute media: { limit: 100, window: 60000 } // 100 media uploads per minute }; this.messageQueue = []; this.rateLimitCounters = new Map(); } /** * Initialize WhatsApp Handler */ async initialize() { try { // Validate configuration this.validateConfiguration(); // Test API connection await this.testApiConnection(); // Initialize rate limiting this.initializeRateLimiting(); this.isInitialized = true; logger.info('[COMPLETE] WhatsApp Handler initialized successfully'); } catch (error) { logger.error('[FAILED] WhatsApp Handler initialization failed:', error); throw error; } } /** * Validate WhatsApp configuration */ validateConfiguration() { const requiredFields = ['phoneNumberId', 'accessToken', 'webhookVerifyToken']; const missing = requiredFields.filter(field => !this.config[field]); if (missing.length > 0) { throw new Error(`Missing WhatsApp configuration: ${missing.join(', ')}`); } } /** * Test API connection */ async testApiConnection() { try { const response = await axios.get( `${this.config.baseUrl}/${this.config.apiVersion}/${this.config.phoneNumberId}`, { headers: { 'Authorization': `Bearer ${this.config.accessToken}` } } ); if (response.status === 200) { logger.info('[COMPLETE] WhatsApp API connection successful'); } } catch (error) { logger.error('[FAILED] WhatsApp API connection failed:', error); throw new Error('WhatsApp API connection failed'); } } /** * Handle incoming webhook */ async handleWebhook(req, res) { try { // Verify webhook signature if (!this.verifyWebhookSignature(req)) { return res.status(401).json({ error: 'Invalid signature' }); } const body = req.body; // Handle webhook verification if (req.method === 'GET') { return this.handleWebhookVerification(req, res); } // Process webhook payload if (body.object === 'whatsapp_business_account') { for (const entry of body.entry || []) { await this.processWebhookEntry(entry); } } res.status(200).json({ success: true }); } catch (error) { logger.error('[FAILED] WhatsApp webhook handling failed:', error); res.status(500).json({ error: 'Webhook processing failed' }); } } /** * Handle webhook verification */ handleWebhookVerification(req, res) { const mode = req.query['hub.mode']; const token = req.query['hub.verify_token']; const challenge = req.query['hub.challenge']; if (mode === 'subscribe' && token === this.config.webhookVerifyToken) { logger.info('[COMPLETE] WhatsApp webhook verified'); return res.status(200).send(challenge); } return res.status(403).json({ error: 'Webhook verification failed' }); } /** * Verify webhook signature */ verifyWebhookSignature(req) { try { const signature = req.headers['x-hub-signature-256']; if (!signature) return false; const expectedSignature = crypto .createHmac('sha256', process.env.WHATSAPP_APP_SECRET) .update(req.body, 'utf8') .digest('hex'); return signature === `sha256=${expectedSignature}`; } catch (error) { logger.error('[FAILED] Webhook signature verification failed:', error); return false; } } /** * Process webhook entry */ async processWebhookEntry(entry) { try { for (const change of entry.changes || []) { if (change.field === 'messages') { await this.processMessageChange(change.value); } else if (change.field === 'message_template_status_update') { await this.processTemplateStatusUpdate(change.value); } } } catch (error) { logger.error('[FAILED] Failed to process webhook entry:', error); } } /** * Process message change */ async processMessageChange(value) { try { // Process incoming messages if (value.messages) { for (const message of value.messages) { await this.processIncomingMessage(message, value.metadata); } } // Process message status updates if (value.statuses) { for (const status of value.statuses) { await this.processMessageStatus(status); } } // Process contact updates if (value.contacts) { for (const contact of value.contacts) { await this.processContactUpdate(contact); } } } catch (error) { logger.error('[FAILED] Failed to process message change:', error); } } /** * Process incoming message */ async processIncomingMessage(whatsappMessage, metadata) { try { logger.info(`[MOBILE] Processing WhatsApp message: ${whatsappMessage.id}`); // Get or create customer const customer = await this.getOrCreateCustomer(whatsappMessage.from, metadata); // Normalize message format const normalizedMessage = await this.normalizeMessage(whatsappMessage, customer); // Save message to database const savedMessage = await this.saveMessage(normalizedMessage); // Route message through message router const routingResult = await this.messageRouterService.routeMessage( savedMessage, 'whatsapp', normalizedMessage.conversationId ); // Send notification await this.notificationService.sendNotification({ type: 'new_whatsapp_message', platform: 'whatsapp', messageId: savedMessage._id, customerId: customer._id, routingResult }); logger.info(`[COMPLETE] WhatsApp message processed successfully: ${whatsappMessage.id}`); } catch (error) { logger.error('[FAILED] Failed to process incoming WhatsApp message:', error); } } /** * Normalize WhatsApp message to standard format */ async normalizeMessage(whatsappMessage, customer) { try { const normalizedMessage = { id: whatsappMessage.id, platform: 'whatsapp', customerId: customer._id, customerPhone: whatsappMessage.from, direction: 'inbound', timestamp: new Date(parseInt(whatsappMessage.timestamp) * 1000), type: whatsappMessage.type, text: null, media: [], metadata: { whatsappId: whatsappMessage.id, context: whatsappMessage.context || null } }; // Process different message types switch (whatsappMessage.type) { case 'text': normalizedMessage.text = whatsappMessage.text.body; break; case 'image': normalizedMessage.media.push({ type: 'image', id: whatsappMessage.image.id, mimeType: whatsappMessage.image.mime_type, caption: whatsappMessage.image.caption || null, url: await this.downloadMedia(whatsappMessage.image.id) }); normalizedMessage.text = whatsappMessage.image.caption || null; break; case 'audio': normalizedMessage.media.push({ type: 'audio', id: whatsappMessage.audio.id, mimeType: whatsappMessage.audio.mime_type, url: await this.downloadMedia(whatsappMessage.audio.id) }); break; case 'video': normalizedMessage.media.push({ type: 'video', id: whatsappMessage.video.id, mimeType: whatsappMessage.video.mime_type, caption: whatsappMessage.video.caption || null, url: await this.downloadMedia(whatsappMessage.video.id) }); normalizedMessage.text = whatsappMessage.video.caption || null; break; case 'document': normalizedMessage.media.push({ type: 'document', id: whatsappMessage.document.id, mimeType: whatsappMessage.document.mime_type, filename: whatsappMessage.document.filename, caption: whatsappMessage.document.caption || null, url: await this.downloadMedia(whatsappMessage.document.id) }); normalizedMessage.text = whatsappMessage.document.caption || null; break; case 'location': normalizedMessage.metadata.location = { latitude: whatsappMessage.location.latitude, longitude: whatsappMessage.location.longitude, name: whatsappMessage.location.name || null, address: whatsappMessage.location.address || null }; normalizedMessage.text = `Location: ${whatsappMessage.location.name || 'Shared location'}`; break; case 'contacts': normalizedMessage.metadata.contacts = whatsappMessage.contacts; normalizedMessage.text = `Contact shared: ${whatsappMessage.contacts[0]?.name?.formatted_name || 'Unknown'}`; break; case 'interactive': if (whatsappMessage.interactive.type === 'button_reply') { normalizedMessage.text = whatsappMessage.interactive.button_reply.title; normalizedMessage.metadata.buttonId = whatsappMessage.interactive.button_reply.id; } else if (whatsappMessage.interactive.type === 'list_reply') { normalizedMessage.text = whatsappMessage.interactive.list_reply.title; normalizedMessage.metadata.listId = whatsappMessage.interactive.list_reply.id; } break; default: normalizedMessage.text = `Unsupported message type: ${whatsappMessage.type}`; break; } return normalizedMessage; } catch (error) { logger.error('[FAILED] Failed to normalize WhatsApp message:', error); throw error; } } /** * Download media from WhatsApp */ async downloadMedia(mediaId) { try { // Get media URL const mediaResponse = await axios.get( `${this.config.baseUrl}/${this.config.apiVersion}/${mediaId}`, { headers: { 'Authorization': `Bearer ${this.config.accessToken}` } } ); const mediaUrl = mediaResponse.data.url; // Download media file const fileResponse = await axios.get(mediaUrl, { headers: { 'Authorization': `Bearer ${this.config.accessToken}` }, responseType: 'stream' }); // Save to temporary storage (in production, use cloud storage) const filename = `whatsapp_media_${mediaId}_${Date.now()}`; const filepath = path.join(__dirname, '../../temp', filename); // Ensure temp directory exists await fs.mkdir(path.dirname(filepath), { recursive: true }); // Save file const writer = require('fs').createWriteStream(filepath); fileResponse.data.pipe(writer); return new Promise((resolve, reject) => { writer.on('finish', () => resolve(filepath)); writer.on('error', reject); }); } catch (error) { logger.error('[FAILED] Failed to download WhatsApp media:', error); return null; } } /** * Send message via WhatsApp */ async sendMessage(conversationId, message, agentId) { try { // Check rate limits if (!this.checkRateLimit('messages')) { throw new Error('Rate limit exceeded for messages'); } // Get conversation details const conversation = await Conversation.findById(conversationId); if (!conversation) { throw new Error('Conversation not found'); } // Get customer details const customer = await Customer.findById(conversation.customerId); if (!customer) { throw new Error('Customer not found'); } // Prepare WhatsApp message payload const payload = await this.prepareMessagePayload(customer.phone, message); // Send message via WhatsApp API const response = await axios.post( `${this.config.baseUrl}/${this.config.apiVersion}/${this.config.phoneNumberId}/messages`, payload, { headers: { 'Authorization': `Bearer ${this.config.accessToken}`, 'Content-Type': 'application/json' } } ); // Save sent message to database const savedMessage = await this.saveSentMessage( conversation, message, agentId, response.data.messages[0].id ); logger.info(`[COMPLETE] WhatsApp message sent successfully: ${response.data.messages[0].id}`); return { success: true, messageId: response.data.messages[0].id, whatsappMessageId: response.data.messages[0].id, savedMessage }; } catch (error) { logger.error('[FAILED] Failed to send WhatsApp message:', error); throw error; } } /** * Prepare message payload for WhatsApp API */ async prepareMessagePayload(recipientPhone, message) { const payload = { messaging_product: 'whatsapp', to: recipientPhone, type: 'text', text: { body: message.text } }; // Handle different message types if (message.type === 'image' && message.media) { payload.type = 'image'; payload.image = { link: message.media.url, caption: message.text || undefined }; delete payload.text; } else if (message.type === 'audio' && message.media) { payload.type = 'audio'; payload.audio = { link: message.media.url }; delete payload.text; } else if (message.type === 'video' && message.media) { payload.type = 'video'; payload.video = { link: message.media.url, caption: message.text || undefined }; delete payload.text; } else if (message.type === 'document' && message.media) { payload.type = 'document'; payload.document = { link: message.media.url, filename: message.media.filename || 'document', caption: message.text || undefined }; delete payload.text; } // Handle interactive messages (buttons, lists) if (message.interactive) { payload.type = 'interactive'; payload.interactive = message.interactive; delete payload.text; } // Handle template messages if (message.template) { payload.type = 'template'; payload.template = message.template; delete payload.text; } return payload; } /** * Save sent message to database */ async saveSentMessage(conversation, message, agentId, whatsappMessageId) { try { const savedMessage = new Message({ platform: 'whatsapp', conversationId: conversation._id, customerId: conversation.customerId, agentId, direction: 'outbound', type: message.type || 'text', text: message.text, media: message.media || [], timestamp: new Date(), status: 'sent', metadata: { whatsappMessageId, interactive: message.interactive || null, template: message.template || null } }); await savedMessage.save(); // Update conversation conversation.lastActivity = new Date(); conversation.lastMessage = savedMessage._id; await conversation.save(); return savedMessage; } catch (error) { logger.error('[FAILED] Failed to save sent WhatsApp message:', error); throw error; } } /** * Process message status updates */ async processMessageStatus(status) { try { // Update message status in database await Message.findOneAndUpdate( { 'metadata.whatsappMessageId': status.id }, { status: status.status, 'metadata.statusTimestamp': new Date(parseInt(status.timestamp) * 1000) } ); // Emit status update via WebSocket this.notificationService.emitStatusUpdate({ platform: 'whatsapp', messageId: status.id, status: status.status, timestamp: new Date(parseInt(status.timestamp) * 1000) }); } catch (error) { logger.error('[FAILED] Failed to process WhatsApp message status:', error); } } /** * Get or create customer */ async getOrCreateCustomer(phone, metadata) { try { let customer = await Customer.findOne({ phone }); if (!customer) { customer = new Customer({ phone, platform: 'whatsapp', name: metadata.display_phone_number || phone, metadata: { whatsappProfile: metadata }, createdAt: new Date() }); await customer.save(); logger.info(`[COMPLETE] Created new WhatsApp customer: ${phone}`); } return customer; } catch (error) { logger.error('[FAILED] Failed to get or create WhatsApp customer:', error); throw error; } } /** * Save message to database */ async saveMessage(normalizedMessage) { try { const message = new Message(normalizedMessage); await message.save(); return message; } catch (error) { logger.error('[FAILED] Failed to save WhatsApp message:', error); throw error; } } /** * Initialize rate limiting */ initializeRateLimiting() { setInterval(() => { this.rateLimitCounters.clear(); }, 60000); // Reset counters every minute } /** * Check rate limits */ checkRateLimit(type) { const now = Date.now(); const limit = this.rateLimits[type]; const key = `${type}_${Math.floor(now / limit.window)}`; const current = this.rateLimitCounters.get(key) || 0; if (current >= limit.limit) { return false; } this.rateLimitCounters.set(key, current + 1); return true; } /** * Health check */ isHealthy() { return this.isInitialized; } /** * Cleanup resources */ async cleanup() { try { this.isInitialized = false; logger.info('[COMPLETE] WhatsApp Handler cleanup completed'); } catch (error) { logger.error('[FAILED] WhatsApp Handler cleanup failed:', error); throw error; } } } module.exports = WhatsAppHandler;