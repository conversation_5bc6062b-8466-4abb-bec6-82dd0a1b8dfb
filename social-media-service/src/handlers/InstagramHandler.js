/** * ============================================= * INSTAGRAM HANDLER * Instagram Business API integration through Graph API * Supports DMs, story replies, comments, and media processing * ============================================= */ const axios = require('axios'); const crypto = require('crypto'); const fs = require('fs').promises; const path = require('path'); const logger = require('../utils/logger'); const Message = require('../models/Message'); const Conversation = require('../models/Conversation'); const Customer = require('../models/Customer'); class InstagramHandler { constructor(messageRouterService, notificationService) { this.messageRouterService = messageRouterService; this.notificationService = notificationService; this.isInitialized = false; // Instagram Graph API configuration this.config = { apiVersion: 'v18.0', baseUrl: 'https://graph.facebook.com', pageAccessToken: process.env.INSTAGRAM_PAGE_ACCESS_TOKEN, appSecret: process.env.INSTAGRAM_APP_SECRET, verifyToken: process.env.INSTAGRAM_VERIFY_TOKEN, instagramBusinessAccountId: process.env.INSTAGRAM_BUSINESS_ACCOUNT_ID, appId: process.env.INSTAGRAM_APP_ID }; // Message types and limits this.messageTypes = { text: { maxLength: 1000 }, image: { maxSize: 8 * 1024 * 1024, formats: ['jpg', 'jpeg', 'png'] }, video: { maxSize: 100 * 1024 * 1024, formats: ['mp4', 'mov'] }, story: { maxSize: 4 * 1024 * 1024 } }; // Rate limiting (Instagram API limits) this.rateLimits = { messages: { limit: 200, window: 60000 }, // 200 messages per minute media: { limit: 50, window: 60000 } // 50 media requests per minute }; this.rateLimitCounters = new Map(); // Instagram-specific features this.features = { directMessages: true, storyReplies: true, storyMentions: true, commentReplies: true, mediaSharing: true }; } /** * Initialize Instagram Handler */ async initialize() { try { // Validate configuration this.validateConfiguration(); // Test API connection await this.testApiConnection(); // Verify business account await this.verifyBusinessAccount(); // Initialize rate limiting this.initializeRateLimiting(); this.isInitialized = true; logger.info('[COMPLETE] Instagram Handler initialized successfully'); } catch (error) { logger.error('[FAILED] Instagram Handler initialization failed:', error); throw error; } } /** * Validate Instagram configuration */ validateConfiguration() { const requiredFields = ['pageAccessToken', 'appSecret', 'verifyToken', 'instagramBusinessAccountId']; const missing = requiredFields.filter(field => !this.config[field]); if (missing.length > 0) { throw new Error(`Missing Instagram configuration: ${missing.join(', ')}`); } } /** * Test API connection */ async testApiConnection() { try { const response = await axios.get( `${this.config.baseUrl}/${this.config.apiVersion}/${this.config.instagramBusinessAccountId}`, { params: { access_token: this.config.pageAccessToken, fields: 'name,username,profile_picture_url' } } ); if (response.status === 200 && response.data.id) { logger.info(`[COMPLETE] Instagram API connection successful - Account: @${response.data.username}`); } } catch (error) { logger.error('[FAILED] Instagram API connection failed:', error); throw new Error('Instagram API connection failed'); } } /** * Verify Instagram Business Account */ async verifyBusinessAccount() { try { const response = await axios.get( `${this.config.baseUrl}/${this.config.apiVersion}/${this.config.instagramBusinessAccountId}`, { params: { access_token: this.config.pageAccessToken, fields: 'account_type,is_verified_account' } } ); const accountData = response.data; if (accountData.account_type !== 'BUSINESS') { logger.warn(' Instagram account is not a Business account'); } logger.info(`[COMPLETE] Instagram Business Account verified - Type: ${accountData.account_type}`); } catch (error) { logger.warn(' Instagram Business Account verification failed:', error.message); } } /** * Handle incoming webhook */ async handleWebhook(req, res) { try { // Handle webhook verification if (req.method === 'GET') { return this.handleWebhookVerification(req, res); } // Verify webhook signature if (!this.verifyWebhookSignature(req)) { return res.status(401).json({ error: 'Invalid signature' }); } const body = req.body; // Process webhook payload if (body.object === 'instagram') { for (const entry of body.entry || []) { await this.processWebhookEntry(entry); } } res.status(200).json({ success: true }); } catch (error) { logger.error('[FAILED] Instagram webhook handling failed:', error); res.status(500).json({ error: 'Webhook processing failed' }); } } /** * Handle webhook verification */ handleWebhookVerification(req, res) { const mode = req.query['hub.mode']; const token = req.query['hub.verify_token']; const challenge = req.query['hub.challenge']; if (mode === 'subscribe' && token === this.config.verifyToken) { logger.info('[COMPLETE] Instagram webhook verified'); return res.status(200).send(challenge); } return res.status(403).json({ error: 'Webhook verification failed' }); } /** * Verify webhook signature */ verifyWebhookSignature(req) { try { const signature = req.headers['x-hub-signature-256']; if (!signature) return false; const expectedSignature = crypto .createHmac('sha256', this.config.appSecret) .update(req.body, 'utf8') .digest('hex'); return signature === `sha256=${expectedSignature}`; } catch (error) { logger.error('[FAILED] Instagram webhook signature verification failed:', error); return false; } } /** * Process webhook entry */ async processWebhookEntry(entry) { try { // Process messaging events if (entry.messaging) { for (const messagingEvent of entry.messaging) { await this.processMessagingEvent(messagingEvent); } } // Process changes (story mentions, comments, etc.) if (entry.changes) { for (const change of entry.changes) { await this.processInstagramChange(change); } } } catch (error) { logger.error('[FAILED] Failed to process Instagram webhook entry:', error); } } /** * Process messaging event */ async processMessagingEvent(event) { try { // Handle different event types if (event.message && !event.message.is_echo) { await this.processIncomingMessage(event); } else if (event.message && event.message.is_echo) { await this.processMessageEcho(event); } else if (event.delivery) { await this.processDeliveryConfirmation(event); } else if (event.read) { await this.processReadConfirmation(event); } } catch (error) { logger.error('[FAILED] Failed to process Instagram messaging event:', error); } } /** * Process incoming message */ async processIncomingMessage(event) { try { const message = event.message; const senderId = event.sender.id; logger.info(` Processing Instagram message: ${message.mid}`); // Get or create customer const customer = await this.getOrCreateCustomer(senderId, event); // Normalize message format const normalizedMessage = await this.normalizeMessage(message, customer, event); // Save message to database const savedMessage = await this.saveMessage(normalizedMessage); // Route message through message router const routingResult = await this.messageRouterService.routeMessage( savedMessage, 'instagram', normalizedMessage.conversationId ); // Send notification await this.notificationService.sendNotification({ type: 'new_instagram_message', platform: 'instagram', messageId: savedMessage._id, customerId: customer._id, routingResult }); logger.info(`[COMPLETE] Instagram message processed successfully: ${message.mid}`); } catch (error) { logger.error('[FAILED] Failed to process incoming Instagram message:', error); } } /** * Normalize Instagram message to standard format */ async normalizeMessage(instagramMessage, customer, event) { try { const normalizedMessage = { id: instagramMessage.mid, platform: 'instagram', customerId: customer._id, customerInstagramId: event.sender.id, direction: 'inbound', timestamp: new Date(instagramMessage.timestamp), type: 'text', text: null, media: [], metadata: { instagramMessageId: instagramMessage.mid, senderId: event.sender.id, recipientId: event.recipient.id, igId: customer.platformIds.instagram?.igId } }; // Process text message if (instagramMessage.text) { normalizedMessage.text = instagramMessage.text; normalizedMessage.type = 'text'; } // Process attachments if (instagramMessage.attachments) { for (const attachment of instagramMessage.attachments) { const mediaItem = await this.processAttachment(attachment); if (mediaItem) { normalizedMessage.media.push(mediaItem); normalizedMessage.type = mediaItem.type; } } } // Process story reply if (instagramMessage.reply_to && instagramMessage.reply_to.story) { normalizedMessage.metadata.storyReply = { storyId: instagramMessage.reply_to.story.id, mediaType: instagramMessage.reply_to.story.media?.media_type, url: instagramMessage.reply_to.story.media?.media_url }; normalizedMessage.type = 'story_reply'; } return normalizedMessage; } catch (error) { logger.error('[FAILED] Failed to normalize Instagram message:', error); throw error; } } /** * Process attachment */ async processAttachment(attachment) { try { const mediaItem = { type: attachment.type, url: attachment.payload.url, mimeType: this.getMimeType(attachment.type) }; // Handle different attachment types switch (attachment.type) { case 'image': mediaItem.mimeType = 'image/jpeg'; break; case 'video': mediaItem.mimeType = 'video/mp4'; break; case 'audio': mediaItem.mimeType = 'audio/mpeg'; break; case 'share': mediaItem.type = 'share'; mediaItem.title = attachment.title; mediaItem.description = attachment.description; break; } // Download media if it's a file if (['image', 'video', 'audio'].includes(attachment.type)) { mediaItem.localPath = await this.downloadMedia(attachment.payload.url, attachment.type); } return mediaItem; } catch (error) { logger.error('[FAILED] Failed to process Instagram attachment:', error); return null; } } /** * Process Instagram changes (story mentions, comments) */ async processInstagramChange(change) { try { logger.info(` Processing Instagram change: ${change.field}`); switch (change.field) { case 'story_insights': await this.processStoryInsights(change.value); break; case 'mentions': await this.processStoryMention(change.value); break; case 'comments': await this.processComment(change.value); break; default: logger.debug('Unhandled Instagram change:', change.field); } } catch (error) { logger.error('[FAILED] Failed to process Instagram change:', error); } } /** * Process story mention */ async processStoryMention(mentionData) { try { logger.info(' Processing Instagram story mention'); // Get mention details const mentionId = mentionData.id; const mentionDetails = await this.getStoryMentionDetails(mentionId); if (mentionDetails) { // Create customer if needed const customer = await this.getOrCreateCustomerFromMention(mentionDetails); // Create normalized message for mention const normalizedMessage = { id: `mention_${mentionId}`, platform: 'instagram', customerId: customer._id, customerInstagramId: mentionDetails.from?.id, direction: 'inbound', timestamp: new Date(), type: 'story_mention', text: `Story mention: ${mentionDetails.text || 'Media mention'}`, media: [], metadata: { mentionId: mentionId, storyId: mentionDetails.story?.id, mediaType: mentionDetails.media_type, mediaUrl: mentionDetails.media_url } }; // Save and route message const savedMessage = await this.saveMessage(normalizedMessage); await this.messageRouterService.routeMessage(savedMessage, 'instagram'); } } catch (error) { logger.error('[FAILED] Failed to process Instagram story mention:', error); } } /** * Send message via Instagram */ async sendMessage(conversationId, message, agentId) { try { // Check rate limits if (!this.checkRateLimit('messages')) { throw new Error('Rate limit exceeded for messages'); } // Get conversation details const conversation = await Conversation.findById(conversationId); if (!conversation) { throw new Error('Conversation not found'); } // Get customer details const customer = await Customer.findById(conversation.customerId); if (!customer || !customer.platformIds.instagram?.igId) { throw new Error('Customer Instagram ID not found'); } // Prepare Instagram message payload const payload = await this.prepareMessagePayload(customer.platformIds.instagram.igId, message); // Send message via Instagram Graph API const response = await axios.post( `${this.config.baseUrl}/${this.config.apiVersion}/me/messages`, payload, { params: { access_token: this.config.pageAccessToken }, headers: { 'Content-Type': 'application/json' } } ); // Save sent message to database const savedMessage = await this.saveSentMessage( conversation, message, agentId, response.data.message_id ); logger.info(`[COMPLETE] Instagram message sent successfully: ${response.data.message_id}`); return { success: true, messageId: response.data.message_id, instagramMessageId: response.data.message_id, savedMessage }; } catch (error) { logger.error('[FAILED] Failed to send Instagram message:', error); throw error; } } /** * Prepare message payload for Instagram Graph API */ async prepareMessagePayload(recipientId, message) { const payload = { recipient: { id: recipientId }, message: {} }; // Handle different message types if (message.type === 'text' || !message.type) { payload.message.text = message.text; } else if (message.type === 'image' && message.media) { payload.message.attachment = { type: 'image', payload: { url: message.media.url, is_reusable: true } }; } else if (message.type === 'video' && message.media) { payload.message.attachment = { type: 'video', payload: { url: message.media.url, is_reusable: true } }; } // Instagram doesn't support quick replies in the same way as Facebook // But we can handle them as text messages with options return payload; } /** * Get story mention details */ async getStoryMentionDetails(mentionId) { try { const response = await axios.get( `${this.config.baseUrl}/${this.config.apiVersion}/${mentionId}`, { params: { access_token: this.config.pageAccessToken, fields: 'id,media_type,media_url,timestamp,username,text' } } ); return response.data; } catch (error) { logger.error('[FAILED] Failed to get Instagram story mention details:', error); return null; } } /** * Get or create customer */ async getOrCreateCustomer(instagramUserId, event) { try { let customer = await Customer.findOne({ 'platformIds.instagram.igId': instagramUserId }); if (!customer) { // Get user profile from Instagram const profile = await this.getUserProfile(instagramUserId); customer = new Customer({ name: profile.name || profile.username || 'Instagram User', primaryPlatform: 'instagram', platformIds: { instagram: { igId: instagramUserId, username: profile.username, profilePic: profile.profile_picture_url } }, createdAt: new Date() }); await customer.save(); logger.info(`[COMPLETE] Created new Instagram customer: ${instagramUserId}`); } return customer; } catch (error) { logger.error('[FAILED] Failed to get or create Instagram customer:', error); throw error; } } /** * Get user profile from Instagram */ async getUserProfile(userId) { try { const response = await axios.get( `${this.config.baseUrl}/${this.config.apiVersion}/${userId}`, { params: { fields: 'name,username,profile_picture_url', access_token: this.config.pageAccessToken } } ); return response.data; } catch (error) { logger.warn(' Failed to get Instagram user profile:', error.message); return { name: 'Unknown User', username: 'unknown', profile_picture_url: null }; } } /** * Download media from Instagram */ async downloadMedia(mediaUrl, mediaType) { try { // Download media file const response = await axios.get(mediaUrl, { responseType: 'stream' }); // Save to temporary storage const filename = `instagram_media_${Date.now()}_${mediaType}`; const filepath = path.join(__dirname, '../../temp', filename); // Ensure temp directory exists await fs.mkdir(path.dirname(filepath), { recursive: true }); // Save file const writer = require('fs').createWriteStream(filepath); response.data.pipe(writer); return new Promise((resolve, reject) => { writer.on('finish', () => resolve(filepath)); writer.on('error', reject); }); } catch (error) { logger.error('[FAILED] Failed to download Instagram media:', error); return null; } } /** * Save message to database */ async saveMessage(normalizedMessage) { try { const message = new Message(normalizedMessage); await message.save(); return message; } catch (error) { logger.error('[FAILED] Failed to save Instagram message:', error); throw error; } } /** * Save sent message to database */ async saveSentMessage(conversation, message, agentId, instagramMessageId) { try { const savedMessage = new Message({ platform: 'instagram', conversationId: conversation._id, customerId: conversation.customerId, agentId, direction: 'outbound', type: message.type || 'text', text: message.text, media: message.media || [], timestamp: new Date(), status: 'sent', metadata: { instagramMessageId, recipientId: conversation.metadata?.instagram?.igId, businessAccountId: this.config.instagramBusinessAccountId } }); await savedMessage.save(); // Update conversation conversation.lastActivity = new Date(); conversation.lastMessage = savedMessage._id; await conversation.save(); return savedMessage; } catch (error) { logger.error('[FAILED] Failed to save sent Instagram message:', error); throw error; } } /** * Helper methods */ getMimeType(attachmentType) { const mimeTypes = { image: 'image/jpeg', video: 'video/mp4', audio: 'audio/mpeg' }; return mimeTypes[attachmentType] || 'application/octet-stream'; } initializeRateLimiting() { setInterval(() => { this.rateLimitCounters.clear(); }, 60000); // Reset counters every minute } checkRateLimit(type) { const now = Date.now(); const limit = this.rateLimits[type]; const key = `${type}_${Math.floor(now / limit.window)}`; const current = this.rateLimitCounters.get(key) || 0; if (current >= limit.limit) { return false; } this.rateLimitCounters.set(key, current + 1); return true; } /** * Process delivery confirmation */ async processDeliveryConfirmation(event) { try { const delivery = event.delivery; // Update message status in database for (const messageId of delivery.mids || []) { await Message.findOneAndUpdate( { 'metadata.instagramMessageId': messageId }, { status: 'delivered', deliveredAt: new Date(delivery.watermark) } ); } } catch (error) { logger.error('[FAILED] Failed to process Instagram delivery confirmation:', error); } } /** * Process read confirmation */ async processReadConfirmation(event) { try { const read = event.read; // Update message status in database await Message.updateMany( { 'metadata.recipientId': event.sender.id, timestamp: { $lte: new Date(read.watermark) }, status: { $ne: 'read' } }, { status: 'read', readAt: new Date(read.watermark) } ); } catch (error) { logger.error('[FAILED] Failed to process Instagram read confirmation:', error); } } /** * Process message echo */ async processMessageEcho(event) { try { logger.debug(' Instagram message echo received:', event.message.mid); } catch (error) { logger.error('[FAILED] Failed to process Instagram message echo:', error); } } /** * Health check */ isHealthy() { return this.isInitialized; } /** * Cleanup resources */ async cleanup() { try { this.isInitialized = false; logger.info('[COMPLETE] Instagram Handler cleanup completed'); } catch (error) { logger.error('[FAILED] Instagram Handler cleanup failed:', error); throw error; } } } module.exports = InstagramHandler;