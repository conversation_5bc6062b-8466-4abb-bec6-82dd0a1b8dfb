/** * ============================================= * FACEBOOK HANDLER * Facebook Messenger API integration with Graph API v18.0 * Supports messaging, postbacks, referrals, and page management * ============================================= */ const axios = require('axios'); const crypto = require('crypto'); const fs = require('fs').promises; const path = require('path'); const logger = require('../utils/logger'); const Message = require('../models/Message'); const Conversation = require('../models/Conversation'); const Customer = require('../models/Customer'); class FacebookHandler { constructor(messageRouterService, notificationService) { this.messageRouterService = messageRouterService; this.notificationService = notificationService; this.isInitialized = false; // Facebook Graph API configuration this.config = { apiVersion: 'v18.0', baseUrl: 'https://graph.facebook.com', pageAccessToken: process.env.FACEBOOK_PAGE_ACCESS_TOKEN, appSecret: process.env.FACEBOOK_APP_SECRET, verifyToken: process.env.FACEBOOK_VERIFY_TOKEN, pageId: process.env.FACEBOOK_PAGE_ID, appId: process.env.FACEBOOK_APP_ID }; // Message types and limits this.messageTypes = { text: { maxLength: 2000 }, image: { maxSize: 25 * 1024 * 1024, formats: ['jpg', 'jpeg', 'png', 'gif'] }, video: { maxSize: 25 * 1024 * 1024, formats: ['mp4', 'mov', 'avi'] }, audio: { maxSize: 25 * 1024 * 1024, formats: ['mp3', 'wav', 'aac'] }, file: { maxSize: 25 * 1024 * 1024 } }; // Rate limiting (Facebook Messenger Platform limits) this.rateLimits = { messages: { limit: 600, window: 60000 }, // 600 messages per minute broadcast: { limit: 10000, window: 3600000 } // 10k broadcast messages per hour }; this.rateLimitCounters = new Map(); // Message tags for compliance this.messageTags = { CONFIRMED_EVENT_UPDATE: 'CONFIRMED_EVENT_UPDATE', POST_PURCHASE_UPDATE: 'POST_PURCHASE_UPDATE', ACCOUNT_UPDATE: 'ACCOUNT_UPDATE', HUMAN_AGENT: 'HUMAN_AGENT' }; } /** * Initialize Facebook Handler */ async initialize() { try { // Validate configuration this.validateConfiguration(); // Test API connection await this.testApiConnection(); // Setup page configuration await this.setupPageConfiguration(); // Initialize rate limiting this.initializeRateLimiting(); this.isInitialized = true; logger.info('[COMPLETE] Facebook Handler initialized successfully'); } catch (error) { logger.error('[FAILED] Facebook Handler initialization failed:', error); throw error; } } /** * Validate Facebook configuration */ validateConfiguration() { const requiredFields = ['pageAccessToken', 'appSecret', 'verifyToken', 'pageId']; const missing = requiredFields.filter(field => !this.config[field]); if (missing.length > 0) { throw new Error(`Missing Facebook configuration: ${missing.join(', ')}`); } } /** * Test API connection */ async testApiConnection() { try { const response = await axios.get( `${this.config.baseUrl}/${this.config.apiVersion}/me`, { params: { access_token: this.config.pageAccessToken, fields: 'name,id' } } ); if (response.status === 200 && response.data.id) { logger.info(`[COMPLETE] Facebook API connection successful - Page: ${response.data.name}`); } } catch (error) { logger.error('[FAILED] Facebook API connection failed:', error); throw new Error('Facebook API connection failed'); } } /** * Setup page configuration (persistent menu, get started button, etc.) */ async setupPageConfiguration() { try { // Set up persistent menu await this.setupPersistentMenu(); // Set up get started button await this.setupGetStartedButton(); // Set up greeting text await this.setupGreetingText(); logger.info('[COMPLETE] Facebook page configuration completed'); } catch (error) { logger.warn(' Facebook page configuration failed:', error.message); // Don't throw error as this is not critical for basic functionality } } /** * Handle incoming webhook */ async handleWebhook(req, res) { try { // Handle webhook verification if (req.method === 'GET') { return this.handleWebhookVerification(req, res); } // Verify webhook signature if (!this.verifyWebhookSignature(req)) { return res.status(401).json({ error: 'Invalid signature' }); } const body = req.body; // Process webhook payload if (body.object === 'page') { for (const entry of body.entry || []) { await this.processWebhookEntry(entry); } } res.status(200).json({ success: true }); } catch (error) { logger.error('[FAILED] Facebook webhook handling failed:', error); res.status(500).json({ error: 'Webhook processing failed' }); } } /** * Handle webhook verification */ handleWebhookVerification(req, res) { const mode = req.query['hub.mode']; const token = req.query['hub.verify_token']; const challenge = req.query['hub.challenge']; if (mode === 'subscribe' && token === this.config.verifyToken) { logger.info('[COMPLETE] Facebook webhook verified'); return res.status(200).send(challenge); } return res.status(403).json({ error: 'Webhook verification failed' }); } /** * Verify webhook signature */ verifyWebhookSignature(req) { try { const signature = req.headers['x-hub-signature-256']; if (!signature) return false; const expectedSignature = crypto .createHmac('sha256', this.config.appSecret) .update(req.body, 'utf8') .digest('hex'); return signature === `sha256=${expectedSignature}`; } catch (error) { logger.error('[FAILED] Facebook webhook signature verification failed:', error); return false; } } /** * Process webhook entry */ async processWebhookEntry(entry) { try { // Process messaging events if (entry.messaging) { for (const messagingEvent of entry.messaging) { await this.processMessagingEvent(messagingEvent); } } // Process changes (page events) if (entry.changes) { for (const change of entry.changes) { await this.processPageChange(change); } } } catch (error) { logger.error('[FAILED] Failed to process Facebook webhook entry:', error); } } /** * Process messaging event */ async processMessagingEvent(event) { try { // Handle different event types if (event.message && !event.message.is_echo) { await this.processIncomingMessage(event); } else if (event.postback) { await this.processPostback(event); } else if (event.referral) { await this.processReferral(event); } else if (event.delivery) { await this.processDeliveryConfirmation(event); } else if (event.read) { await this.processReadConfirmation(event); } else if (event.message && event.message.is_echo) { await this.processMessageEcho(event); } } catch (error) { logger.error('[FAILED] Failed to process Facebook messaging event:', error); } } /** * Process incoming message */ async processIncomingMessage(event) { try { const message = event.message; const senderId = event.sender.id; logger.info(` Processing Facebook message: ${message.mid}`); // Get or create customer const customer = await this.getOrCreateCustomer(senderId, event); // Normalize message format const normalizedMessage = await this.normalizeMessage(message, customer, event); // Save message to database const savedMessage = await this.saveMessage(normalizedMessage); // Route message through message router const routingResult = await this.messageRouterService.routeMessage( savedMessage, 'facebook', normalizedMessage.conversationId ); // Send notification await this.notificationService.sendNotification({ type: 'new_facebook_message', platform: 'facebook', messageId: savedMessage._id, customerId: customer._id, routingResult }); logger.info(`[COMPLETE] Facebook message processed successfully: ${message.mid}`); } catch (error) { logger.error('[FAILED] Failed to process incoming Facebook message:', error); } } /** * Normalize Facebook message to standard format */ async normalizeMessage(facebookMessage, customer, event) { try { const normalizedMessage = { id: facebookMessage.mid, platform: 'facebook', customerId: customer._id, customerFacebookId: event.sender.id, direction: 'inbound', timestamp: new Date(facebookMessage.timestamp), type: 'text', text: null, media: [], metadata: { facebookMessageId: facebookMessage.mid, senderId: event.sender.id, recipientId: event.recipient.id, pageId: this.config.pageId } }; // Process text message if (facebookMessage.text) { normalizedMessage.text = facebookMessage.text; normalizedMessage.type = 'text'; } // Process quick reply if (facebookMessage.quick_reply) { normalizedMessage.type = 'interactive'; normalizedMessage.text = facebookMessage.quick_reply.payload; normalizedMessage.metadata.quickReplyPayload = facebookMessage.quick_reply.payload; } // Process attachments if (facebookMessage.attachments) { for (const attachment of facebookMessage.attachments) { const mediaItem = await this.processAttachment(attachment); if (mediaItem) { normalizedMessage.media.push(mediaItem); normalizedMessage.type = mediaItem.type; } } } // Process sticker if (facebookMessage.sticker_id) { normalizedMessage.type = 'sticker'; normalizedMessage.metadata.stickerId = facebookMessage.sticker_id; normalizedMessage.text = 'Sticker sent'; } return normalizedMessage; } catch (error) { logger.error('[FAILED] Failed to normalize Facebook message:', error); throw error; } } /** * Process attachment */ async processAttachment(attachment) { try { const mediaItem = { type: attachment.type, url: attachment.payload.url, mimeType: this.getMimeType(attachment.type) }; // Handle different attachment types switch (attachment.type) { case 'image': mediaItem.mimeType = 'image/jpeg'; break; case 'video': mediaItem.mimeType = 'video/mp4'; break; case 'audio': mediaItem.mimeType = 'audio/mpeg'; break; case 'file': mediaItem.mimeType = 'application/octet-stream'; mediaItem.filename = attachment.payload.title || 'file'; break; case 'location': mediaItem.type = 'location'; mediaItem.coordinates = attachment.payload.coordinates; mediaItem.title = attachment.title; break; case 'fallback': mediaItem.type = 'fallback'; mediaItem.title = attachment.title; mediaItem.url = attachment.URL; break; } // Download media if it's a file if (['image', 'video', 'audio', 'file'].includes(attachment.type)) { mediaItem.localPath = await this.downloadMedia(attachment.payload.url, attachment.type); } return mediaItem; } catch (error) { logger.error('[FAILED] Failed to process Facebook attachment:', error); return null; } } /** * Process postback event */ async processPostback(event) { try { const postback = event.postback; const senderId = event.sender.id; logger.info(` Processing Facebook postback: ${postback.payload}`); // Get or create customer const customer = await this.getOrCreateCustomer(senderId, event); // Create normalized message for postback const normalizedMessage = { id: `postback_${Date.now()}_${senderId}`, platform: 'facebook', customerId: customer._id, customerFacebookId: senderId, direction: 'inbound', timestamp: new Date(), type: 'interactive', text: postback.title || postback.payload, media: [], metadata: { postbackPayload: postback.payload, postbackTitle: postback.title, senderId: senderId, recipientId: event.recipient.id, referral: postback.referral || null } }; // Save message to database const savedMessage = await this.saveMessage(normalizedMessage); // Route message through message router const routingResult = await this.messageRouterService.routeMessage( savedMessage, 'facebook', normalizedMessage.conversationId ); logger.info(`[COMPLETE] Facebook postback processed successfully: ${postback.payload}`); } catch (error) { logger.error('[FAILED] Failed to process Facebook postback:', error); } } /** * Send message via Facebook Messenger */ async sendMessage(conversationId, message, agentId) { try { // Check rate limits if (!this.checkRateLimit('messages')) { throw new Error('Rate limit exceeded for messages'); } // Get conversation details const conversation = await Conversation.findById(conversationId); if (!conversation) { throw new Error('Conversation not found'); } // Get customer details const customer = await Customer.findById(conversation.customerId); if (!customer || !customer.platformIds.facebook?.userId) { throw new Error('Customer Facebook ID not found'); } // Prepare Facebook message payload const payload = await this.prepareMessagePayload(customer.platformIds.facebook.userId, message); // Send message via Facebook Graph API const response = await axios.post( `${this.config.baseUrl}/${this.config.apiVersion}/me/messages`, payload, { params: { access_token: this.config.pageAccessToken }, headers: { 'Content-Type': 'application/json' } } ); // Save sent message to database const savedMessage = await this.saveSentMessage( conversation, message, agentId, response.data.message_id ); logger.info(`[COMPLETE] Facebook message sent successfully: ${response.data.message_id}`); return { success: true, messageId: response.data.message_id, facebookMessageId: response.data.message_id, savedMessage }; } catch (error) { logger.error('[FAILED] Failed to send Facebook message:', error); throw error; } } /** * Prepare message payload for Facebook Graph API */ async prepareMessagePayload(recipientId, message) { const payload = { recipient: { id: recipientId }, message: {} }; // Handle different message types if (message.type === 'text' || !message.type) { payload.message.text = message.text; } else if (message.type === 'image' && message.media) { payload.message.attachment = { type: 'image', payload: { url: message.media.url, is_reusable: true } }; } else if (message.type === 'video' && message.media) { payload.message.attachment = { type: 'video', payload: { url: message.media.url, is_reusable: true } }; } else if (message.type === 'audio' && message.media) { payload.message.attachment = { type: 'audio', payload: { url: message.media.url, is_reusable: true } }; } else if (message.type === 'file' && message.media) { payload.message.attachment = { type: 'file', payload: { url: message.media.url, is_reusable: true } }; } // Handle quick replies if (message.quickReplies && message.quickReplies.length > 0) { payload.message.quick_replies = message.quickReplies.map(qr => ({ content_type: 'text', title: qr.title, payload: qr.payload })); } // Handle buttons (generic template) if (message.buttons && message.buttons.length > 0) { payload.message.attachment = { type: 'template', payload: { template_type: 'generic', elements: [{ title: message.title || message.text, subtitle: message.subtitle, buttons: message.buttons.map(btn => ({ type: btn.type || 'postback', title: btn.title, payload: btn.payload, url: btn.url })) }] } }; delete payload.message.text; } // Add message tag if specified (for compliance) if (message.tag && this.messageTags[message.tag]) { payload.tag = message.tag; } // Add messaging type payload.messaging_type = message.messagingType || 'RESPONSE'; return payload; } /** * Setup persistent menu */ async setupPersistentMenu() { const menuPayload = { persistent_menu: [{ locale: 'default', composer_input_disabled: false, call_to_actions: [ { type: 'postback', title: 'Aide & Support', payload: 'HELP_SUPPORT' }, { type: 'postback', title: 'Mon Compte', payload: 'MY_ACCOUNT' }, { type: 'web_url', title: 'Site Web Free', url: 'https://www.free.fr' } ] }] }; await axios.post( `${this.config.baseUrl}/${this.config.apiVersion}/me/messenger_profile`, menuPayload, { params: { access_token: this.config.pageAccessToken } } ); } /** * Setup get started button */ async setupGetStartedButton() { const getStartedPayload = { get_started: { payload: 'GET_STARTED' } }; await axios.post( `${this.config.baseUrl}/${this.config.apiVersion}/me/messenger_profile`, getStartedPayload, { params: { access_token: this.config.pageAccessToken } } ); } /** * Setup greeting text */ async setupGreetingText() { const greetingPayload = { greeting: [{ locale: 'default', text: 'Bonjour ! Je suis l\'assistant Free Mobile. Comment puis-je vous aider aujourd\'hui ?' }] }; await axios.post( `${this.config.baseUrl}/${this.config.apiVersion}/me/messenger_profile`, greetingPayload, { params: { access_token: this.config.pageAccessToken } } ); } /** * Download media from Facebook */ async downloadMedia(mediaUrl, mediaType) { try { // Download media file const response = await axios.get(mediaUrl, { responseType: 'stream' }); // Save to temporary storage const filename = `facebook_media_${Date.now()}_${mediaType}`; const filepath = path.join(__dirname, '../../temp', filename); // Ensure temp directory exists await fs.mkdir(path.dirname(filepath), { recursive: true }); // Save file const writer = require('fs').createWriteStream(filepath); response.data.pipe(writer); return new Promise((resolve, reject) => { writer.on('finish', () => resolve(filepath)); writer.on('error', reject); }); } catch (error) { logger.error('[FAILED] Failed to download Facebook media:', error); return null; } } /** * Get or create customer */ async getOrCreateCustomer(facebookUserId, event) { try { let customer = await Customer.findOne({ 'platformIds.facebook.userId': facebookUserId }); if (!customer) { // Get user profile from Facebook const profile = await this.getUserProfile(facebookUserId); customer = new Customer({ name: `${profile.first_name} ${profile.last_name}`, primaryPlatform: 'facebook', platformIds: { facebook: { userId: facebookUserId, pageId: event.recipient.id, firstName: profile.first_name, lastName: profile.last_name, profilePic: profile.profile_pic } }, createdAt: new Date() }); await customer.save(); logger.info(`[COMPLETE] Created new Facebook customer: ${facebookUserId}`); } return customer; } catch (error) { logger.error('[FAILED] Failed to get or create Facebook customer:', error); throw error; } } /** * Get user profile from Facebook */ async getUserProfile(userId) { try { const response = await axios.get( `${this.config.baseUrl}/${this.config.apiVersion}/${userId}`, { params: { fields: 'first_name,last_name,profile_pic', access_token: this.config.pageAccessToken } } ); return response.data; } catch (error) { logger.warn(' Failed to get Facebook user profile:', error.message); return { first_name: 'Unknown', last_name: 'User', profile_pic: null }; } } /** * Save message to database */ async saveMessage(normalizedMessage) { try { const message = new Message(normalizedMessage); await message.save(); return message; } catch (error) { logger.error('[FAILED] Failed to save Facebook message:', error); throw error; } } /** * Save sent message to database */ async saveSentMessage(conversation, message, agentId, facebookMessageId) { try { const savedMessage = new Message({ platform: 'facebook', conversationId: conversation._id, customerId: conversation.customerId, agentId, direction: 'outbound', type: message.type || 'text', text: message.text, media: message.media || [], timestamp: new Date(), status: 'sent', metadata: { facebookMessageId, recipientId: conversation.metadata?.facebook?.userId, pageId: this.config.pageId } }); await savedMessage.save(); // Update conversation conversation.lastActivity = new Date(); conversation.lastMessage = savedMessage._id; await conversation.save(); return savedMessage; } catch (error) { logger.error('[FAILED] Failed to save sent Facebook message:', error); throw error; } } /** * Helper methods */ getMimeType(attachmentType) { const mimeTypes = { image: 'image/jpeg', video: 'video/mp4', audio: 'audio/mpeg', file: 'application/octet-stream' }; return mimeTypes[attachmentType] || 'application/octet-stream'; } initializeRateLimiting() { setInterval(() => { this.rateLimitCounters.clear(); }, 60000); // Reset counters every minute } checkRateLimit(type) { const now = Date.now(); const limit = this.rateLimits[type]; const key = `${type}_${Math.floor(now / limit.window)}`; const current = this.rateLimitCounters.get(key) || 0; if (current >= limit.limit) { return false; } this.rateLimitCounters.set(key, current + 1); return true; } /** * Process delivery confirmation */ async processDeliveryConfirmation(event) { try { const delivery = event.delivery; // Update message status in database for (const messageId of delivery.mids || []) { await Message.findOneAndUpdate( { 'metadata.facebookMessageId': messageId }, { status: 'delivered', deliveredAt: new Date(delivery.watermark) } ); } } catch (error) { logger.error('[FAILED] Failed to process Facebook delivery confirmation:', error); } } /** * Process read confirmation */ async processReadConfirmation(event) { try { const read = event.read; // Update message status in database await Message.updateMany( { 'metadata.recipientId': event.sender.id, timestamp: { $lte: new Date(read.watermark) }, status: { $ne: 'read' } }, { status: 'read', readAt: new Date(read.watermark) } ); } catch (error) { logger.error('[FAILED] Failed to process Facebook read confirmation:', error); } } /** * Process message echo (sent by page) */ async processMessageEcho(event) { try { // Handle message echoes if needed logger.debug(' Facebook message echo received:', event.message.mid); } catch (error) { logger.error('[FAILED] Failed to process Facebook message echo:', error); } } /** * Process referral */ async processReferral(event) { try { const referral = event.referral; logger.info(' Facebook referral received:', referral); // Handle referral logic here // This could trigger specific welcome messages or routing } catch (error) { logger.error('[FAILED] Failed to process Facebook referral:', error); } } /** * Process page changes */ async processPageChange(change) { try { logger.info(' Facebook page change received:', change.field); // Handle different page change types switch (change.field) { case 'conversations': // Handle conversation changes break; case 'messages': // Handle message changes break; default: logger.debug('Unhandled Facebook page change:', change.field); } } catch (error) { logger.error('[FAILED] Failed to process Facebook page change:', error); } } /** * Health check */ isHealthy() { return this.isInitialized; } /** * Cleanup resources */ async cleanup() { try { this.isInitialized = false; logger.info('[COMPLETE] Facebook Handler cleanup completed'); } catch (error) { logger.error('[FAILED] Facebook Handler cleanup failed:', error); throw error; } } } module.exports = FacebookHandler;