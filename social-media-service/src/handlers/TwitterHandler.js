/** * ============================================= * TWITTER HANDLER * Twitter API v2 integration for DMs, mentions, and brand monitoring * Supports real-time webhooks and media processing * ============================================= */ const axios = require('axios'); const crypto = require('crypto'); const fs = require('fs').promises; const path = require('path'); const { TwitterApi } = require('twitter-api-v2'); const logger = require('../utils/logger'); const Message = require('../models/Message'); const Conversation = require('../models/Conversation'); const Customer = require('../models/Customer'); class TwitterHandler { constructor(messageRouterService, notificationService) { this.messageRouterService = messageRouterService; this.notificationService = notificationService; this.isInitialized = false; // Twitter API configuration this.config = { apiVersion: '2', baseUrl: 'https://api.twitter.com', bearerToken: process.env.TWITTER_BEARER_TOKEN, apiKey: process.env.TWITTER_API_KEY, apiSecret: process.env.TWITTER_API_SECRET, accessToken: process.env.TWITTER_ACCESS_TOKEN, accessTokenSecret: process.env.TWITTER_ACCESS_TOKEN_SECRET, webhookUrl: process.env.TWITTER_WEBHOOK_URL, webhookSecret: process.env.TWITTER_WEBHOOK_SECRET }; // Initialize Twitter API client this.twitterClient = null; // Message types and limits this.messageTypes = { text: { maxLength: 280 }, // DMs can be longer, but tweets are limited dm_text: { maxLength: 10000 }, // Direct messages have higher limit image: { maxSize: 5 * 1024 * 1024, formats: ['jpg', 'jpeg', 'png', 'gif', 'webp'] }, video: { maxSize: 512 * 1024 * 1024, formats: ['mp4', 'mov'] }, gif: { maxSize: 15 * 1024 * 1024 } }; // Rate limiting (Twitter API v2 limits) this.rateLimits = { tweets: { limit: 300, window: 900000 }, // 300 tweets per 15 minutes dms: { limit: 300, window: 900000 }, // 300 DMs per 15 minutes lookup: { limit: 300, window: 900000 } // 300 user lookups per 15 minutes }; this.rateLimitCounters = new Map(); // Twitter-specific features this.features = { directMessages: true, mentions: true, hashtags: true, brandMonitoring: true, mediaSharing: true, spaces: false, // Future feature communities: false // Future feature }; // Brand monitoring keywords this.monitoringKeywords = [ 'free mobile', '@freemobile', '#freemobile', 'free.fr', 'forfait free' ]; } /** * Initialize Twitter Handler */ async initialize() { try { // Validate configuration this.validateConfiguration(); // Initialize Twitter API client this.initializeTwitterClient(); // Test API connection await this.testApiConnection(); // Setup webhook if configured if (this.config.webhookUrl) { await this.setupWebhook(); } // Initialize rate limiting this.initializeRateLimiting(); this.isInitialized = true; logger.info('[COMPLETE] Twitter Handler initialized successfully'); } catch (error) { logger.error('[FAILED] Twitter Handler initialization failed:', error); throw error; } } /** * Validate Twitter configuration */ validateConfiguration() { const requiredFields = ['bearerToken', 'apiKey', 'apiSecret', 'accessToken', 'accessTokenSecret']; const missing = requiredFields.filter(field => !this.config[field]); if (missing.length > 0) { throw new Error(`Missing Twitter configuration: ${missing.join(', ')}`); } } /** * Initialize Twitter API client */ initializeTwitterClient() { this.twitterClient = new TwitterApi({ appKey: this.config.apiKey, appSecret: this.config.apiSecret, accessToken: this.config.accessToken, accessSecret: this.config.accessTokenSecret }); } /** * Test API connection */ async testApiConnection() { try { const user = await this.twitterClient.v2.me({ 'user.fields': ['username', 'name', 'public_metrics'] }); if (user.data) { logger.info(`[COMPLETE] Twitter API connection successful - Account: @${user.data.username}`); } } catch (error) { logger.error('[FAILED] Twitter API connection failed:', error); throw new Error('Twitter API connection failed'); } } /** * Setup Twitter webhook */ async setupWebhook() { try { // Note: Twitter webhook setup requires additional steps // This is a placeholder for webhook configuration logger.info(' Twitter webhook setup initiated'); // In production, you would: // 1. Register webhook URL with Twitter // 2. Handle CRC (Challenge Response Check) // 3. Subscribe to events } catch (error) { logger.warn(' Twitter webhook setup failed:', error.message); } } /** * Handle incoming webhook */ async handleWebhook(req, res) { try { // Handle CRC challenge if (req.method === 'GET' && req.query.crc_token) { return this.handleCrcChallenge(req, res); } // Verify webhook signature if (!this.verifyWebhookSignature(req)) { return res.status(401).json({ error: 'Invalid signature' }); } const body = req.body; // Process different event types if (body.direct_message_events) { await this.processDirectMessageEvents(body.direct_message_events); } if (body.tweet_create_events) { await this.processTweetEvents(body.tweet_create_events); } if (body.favorite_events) { await this.processFavoriteEvents(body.favorite_events); } if (body.follow_events) { await this.processFollowEvents(body.follow_events); } res.status(200).json({ success: true }); } catch (error) { logger.error('[FAILED] Twitter webhook handling failed:', error); res.status(500).json({ error: 'Webhook processing failed' }); } } /** * Handle CRC challenge */ handleCrcChallenge(req, res) { try { const crcToken = req.query.crc_token; const responseToken = crypto .createHmac('sha256', this.config.webhookSecret) .update(crcToken) .digest('base64'); res.status(200).json({ response_token: `sha256=${responseToken}` }); logger.info('[COMPLETE] Twitter CRC challenge handled successfully'); } catch (error) { logger.error('[FAILED] Twitter CRC challenge failed:', error); res.status(500).json({ error: 'CRC challenge failed' }); } } /** * Verify webhook signature */ verifyWebhookSignature(req) { try { const signature = req.headers['x-twitter-webhooks-signature']; if (!signature) return false; const expectedSignature = crypto .createHmac('sha256', this.config.webhookSecret) .update(req.body) .digest('base64'); return signature === `sha256=${expectedSignature}`; } catch (error) { logger.error('[FAILED] Twitter webhook signature verification failed:', error); return false; } } /** * Process direct message events */ async processDirectMessageEvents(dmEvents) { try { for (const dmEvent of dmEvents) { await this.processDirectMessage(dmEvent); } } catch (error) { logger.error('[FAILED] Failed to process Twitter DM events:', error); } } /** * Process direct message */ async processDirectMessage(dmEvent) { try { const messageData = dmEvent.message_create; const senderId = messageData.sender_id; const recipientId = messageData.target.recipient_id; // Skip messages sent by our bot const botUserId = await this.getBotUserId(); if (senderId === botUserId) { return; } logger.info(` Processing Twitter DM: ${dmEvent.id}`); // Get or create customer const customer = await this.getOrCreateCustomer(senderId); // Normalize message format const normalizedMessage = await this.normalizeDirectMessage(dmEvent, customer); // Save message to database const savedMessage = await this.saveMessage(normalizedMessage); // Route message through message router const routingResult = await this.messageRouterService.routeMessage( savedMessage, 'twitter', normalizedMessage.conversationId ); // Send notification await this.notificationService.sendNotification({ type: 'new_twitter_dm', platform: 'twitter', messageId: savedMessage._id, customerId: customer._id, routingResult }); logger.info(`[COMPLETE] Twitter DM processed successfully: ${dmEvent.id}`); } catch (error) { logger.error('[FAILED] Failed to process Twitter direct message:', error); } } /** * Process tweet events (mentions, replies) */ async processTweetEvents(tweetEvents) { try { for (const tweet of tweetEvents) { await this.processTweet(tweet); } } catch (error) { logger.error('[FAILED] Failed to process Twitter tweet events:', error); } } /** * Process tweet (mention or reply) */ async processTweet(tweet) { try { const userId = tweet.user.id_str; const tweetId = tweet.id_str; // Skip tweets from our bot const botUserId = await this.getBotUserId(); if (userId === botUserId) { return; } // Check if it's a mention or contains monitoring keywords const isMention = this.isMentionOrKeyword(tweet.text); if (!isMention) { return; } logger.info(` Processing Twitter mention/tweet: ${tweetId}`); // Get or create customer const customer = await this.getOrCreateCustomer(userId); // Normalize tweet format const normalizedMessage = await this.normalizeTweet(tweet, customer); // Save message to database const savedMessage = await this.saveMessage(normalizedMessage); // Route message through message router const routingResult = await this.messageRouterService.routeMessage( savedMessage, 'twitter', normalizedMessage.conversationId ); // Send notification await this.notificationService.sendNotification({ type: 'new_twitter_mention', platform: 'twitter', messageId: savedMessage._id, customerId: customer._id, routingResult }); logger.info(`[COMPLETE] Twitter mention processed successfully: ${tweetId}`); } catch (error) { logger.error('[FAILED] Failed to process Twitter tweet:', error); } } /** * Normalize direct message to standard format */ async normalizeDirectMessage(dmEvent, customer) { try { const messageData = dmEvent.message_create.message_data; const normalizedMessage = { id: dmEvent.id, platform: 'twitter', customerId: customer._id, customerTwitterId: dmEvent.message_create.sender_id, direction: 'inbound', timestamp: new Date(parseInt(dmEvent.created_timestamp)), type: 'text', text: messageData.text, media: [], metadata: { twitterDmId: dmEvent.id, senderId: dmEvent.message_create.sender_id, recipientId: dmEvent.message_create.target.recipient_id, conversationId: dmEvent.message_create.message_data.conversation_id } }; // Process media attachments if (messageData.attachment) { const mediaItem = await this.processTwitterMedia(messageData.attachment); if (mediaItem) { normalizedMessage.media.push(mediaItem); normalizedMessage.type = mediaItem.type; } } return normalizedMessage; } catch (error) { logger.error('[FAILED] Failed to normalize Twitter DM:', error); throw error; } } /** * Normalize tweet to standard format */ async normalizeTweet(tweet, customer) { try { const normalizedMessage = { id: tweet.id_str, platform: 'twitter', customerId: customer._id, customerTwitterId: tweet.user.id_str, direction: 'inbound', timestamp: new Date(tweet.created_at), type: 'text', text: tweet.text || tweet.full_text, media: [], metadata: { tweetId: tweet.id_str, userId: tweet.user.id_str, screenName: tweet.user.screen_name, isRetweet: !!tweet.retweeted_status, isReply: !!tweet.in_reply_to_status_id, retweetCount: tweet.retweet_count, favoriteCount: tweet.favorite_count, hashtags: tweet.entities?.hashtags || [], mentions: tweet.entities?.user_mentions || [], urls: tweet.entities?.urls || [] } }; // Process media entities if (tweet.entities?.media) { for (const media of tweet.entities.media) { const mediaItem = await this.processTwitterTweetMedia(media); if (mediaItem) { normalizedMessage.media.push(mediaItem); normalizedMessage.type = mediaItem.type; } } } return normalizedMessage; } catch (error) { logger.error('[FAILED] Failed to normalize Twitter tweet:', error); throw error; } } /** * Process Twitter media attachment */ async processTwitterMedia(attachment) { try { const media = attachment.media; const mediaItem = { type: media.type, id: media.id_str, url: media.media_url_https, mimeType: this.getMimeType(media.type) }; // Handle different media types switch (media.type) { case 'photo': mediaItem.type = 'image'; mediaItem.mimeType = 'image/jpeg'; break; case 'video': mediaItem.type = 'video'; mediaItem.mimeType = 'video/mp4'; mediaItem.duration = media.video_info?.duration_millis; break; case 'animated_gif': mediaItem.type = 'gif'; mediaItem.mimeType = 'image/gif'; break; } // Download media mediaItem.localPath = await this.downloadMedia(mediaItem.url, mediaItem.type); return mediaItem; } catch (error) { logger.error('[FAILED] Failed to process Twitter media:', error); return null; } } /** * Send direct message via Twitter */ async sendDirectMessage(conversationId, message, agentId) { try { // Check rate limits if (!this.checkRateLimit('dms')) { throw new Error('Rate limit exceeded for direct messages'); } // Get conversation details const conversation = await Conversation.findById(conversationId); if (!conversation) { throw new Error('Conversation not found'); } // Get customer details const customer = await Customer.findById(conversation.customerId); if (!customer || !customer.platformIds.twitter?.userId) { throw new Error('Customer Twitter ID not found'); } // Send DM using Twitter API v2 const dmData = { dm_conversation_id: conversation.metadata?.twitter?.conversationId, text: message.text }; // Add media if present if (message.media && message.media.url) { // Upload media first const mediaId = await this.uploadMedia(message.media.url); if (mediaId) { dmData.media_id = mediaId; } } const response = await this.twitterClient.v1.sendDm({ recipient_id: customer.platformIds.twitter.userId, text: message.text }); // Save sent message to database const savedMessage = await this.saveSentMessage( conversation, message, agentId, response.id_str ); logger.info(`[COMPLETE] Twitter DM sent successfully: ${response.id_str}`); return { success: true, messageId: response.id_str, twitterMessageId: response.id_str, savedMessage }; } catch (error) { logger.error('[FAILED] Failed to send Twitter DM:', error); throw error; } } /** * Send tweet reply */ async sendTweetReply(conversationId, message, agentId) { try { // Check rate limits if (!this.checkRateLimit('tweets')) { throw new Error('Rate limit exceeded for tweets'); } // Get conversation details const conversation = await Conversation.findById(conversationId); if (!conversation) { throw new Error('Conversation not found'); } // Get original tweet ID from conversation metadata const originalTweetId = conversation.metadata?.twitter?.tweetId; if (!originalTweetId) { throw new Error('Original tweet ID not found'); } // Send tweet reply const tweetData = { text: message.text, reply: { in_reply_to_tweet_id: originalTweetId } }; const response = await this.twitterClient.v2.tweet(tweetData); // Save sent message to database const savedMessage = await this.saveSentMessage( conversation, message, agentId, response.data.id ); logger.info(`[COMPLETE] Twitter reply sent successfully: ${response.data.id}`); return { success: true, messageId: response.data.id, twitterMessageId: response.data.id, savedMessage }; } catch (error) { logger.error('[FAILED] Failed to send Twitter reply:', error); throw error; } } /** * Get or create customer */ async getOrCreateCustomer(twitterUserId) { try { let customer = await Customer.findOne({ 'platformIds.twitter.userId': twitterUserId }); if (!customer) { // Get user profile from Twitter const profile = await this.getUserProfile(twitterUserId); customer = new Customer({ name: profile.name || profile.screen_name || 'Twitter User', primaryPlatform: 'twitter', platformIds: { twitter: { userId: twitterUserId, screenName: profile.screen_name, name: profile.name, profileImageUrl: profile.profile_image_url_https, followerCount: profile.followers_count, verified: profile.verified } }, createdAt: new Date() }); await customer.save(); logger.info(`[COMPLETE] Created new Twitter customer: ${twitterUserId}`); } return customer; } catch (error) { logger.error('[FAILED] Failed to get or create Twitter customer:', error); throw error; } } /** * Get user profile from Twitter */ async getUserProfile(userId) { try { const user = await this.twitterClient.v2.user(userId, { 'user.fields': ['name', 'username', 'profile_image_url', 'public_metrics', 'verified'] }); return { name: user.data.name, screen_name: user.data.username, profile_image_url_https: user.data.profile_image_url, followers_count: user.data.public_metrics?.followers_count || 0, verified: user.data.verified || false }; } catch (error) { logger.warn(' Failed to get Twitter user profile:', error.message); return { name: 'Unknown User', screen_name: 'unknown', profile_image_url_https: null, followers_count: 0, verified: false }; } } /** * Check if tweet is mention or contains monitoring keywords */ isMentionOrKeyword(text) { const lowerText = text.toLowerCase(); return this.monitoringKeywords.some(keyword => lowerText.includes(keyword.toLowerCase()) ); } /** * Get bot user ID */ async getBotUserId() { try { if (!this.botUserId) { const user = await this.twitterClient.v2.me(); this.botUserId = user.data.id; } return this.botUserId; } catch (error) { logger.error('[FAILED] Failed to get bot user ID:', error); return null; } } /** * Helper methods */ getMimeType(mediaType) { const mimeTypes = { photo: 'image/jpeg', video: 'video/mp4', animated_gif: 'image/gif' }; return mimeTypes[mediaType] || 'application/octet-stream'; } async downloadMedia(mediaUrl, mediaType) { try { const response = await axios.get(mediaUrl, { responseType: 'stream' }); const filename = `twitter_media_${Date.now()}_${mediaType}`; const filepath = path.join(__dirname, '../../temp', filename); await fs.mkdir(path.dirname(filepath), { recursive: true }); const writer = require('fs').createWriteStream(filepath); response.data.pipe(writer); return new Promise((resolve, reject) => { writer.on('finish', () => resolve(filepath)); writer.on('error', reject); }); } catch (error) { logger.error('[FAILED] Failed to download Twitter media:', error); return null; } } async saveMessage(normalizedMessage) { try { const message = new Message(normalizedMessage); await message.save(); return message; } catch (error) { logger.error('[FAILED] Failed to save Twitter message:', error); throw error; } } async saveSentMessage(conversation, message, agentId, twitterMessageId) { try { const savedMessage = new Message({ platform: 'twitter', conversationId: conversation._id, customerId: conversation.customerId, agentId, direction: 'outbound', type: message.type || 'text', text: message.text, media: message.media || [], timestamp: new Date(), status: 'sent', metadata: { twitterMessageId, recipientId: conversation.metadata?.twitter?.userId } }); await savedMessage.save(); conversation.lastActivity = new Date(); conversation.lastMessage = savedMessage._id; await conversation.save(); return savedMessage; } catch (error) { logger.error('[FAILED] Failed to save sent Twitter message:', error); throw error; } } initializeRateLimiting() { setInterval(() => { this.rateLimitCounters.clear(); }, 900000); // Reset counters every 15 minutes } checkRateLimit(type) { const now = Date.now(); const limit = this.rateLimits[type]; const key = `${type}_${Math.floor(now / limit.window)}`; const current = this.rateLimitCounters.get(key) || 0; if (current >= limit.limit) { return false; } this.rateLimitCounters.set(key, current + 1); return true; } /** * Health check */ isHealthy() { return this.isInitialized; } /** * Cleanup resources */ async cleanup() { try { this.isInitialized = false; logger.info('[COMPLETE] Twitter Handler cleanup completed'); } catch (error) { logger.error('[FAILED] Twitter Handler cleanup failed:', error); throw error; } } } module.exports = TwitterHandler;