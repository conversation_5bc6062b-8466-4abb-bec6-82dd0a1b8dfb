/** * ============================================= * [MOBILE] FREE MOBILE SOCIAL MEDIA SERVICE * Multi-platform social media integration service * Handles WhatsApp, Facebook, Instagram, Twitter, and LinkedIn * ============================================= */ const express = require('express'); const http = require('http'); const socketIo = require('socket.io'); const cors = require('cors'); const helmet = require('helmet'); const morgan = require('morgan'); const rateLimit = require('express-rate-limit'); const mongoose = require('mongoose'); const Redis = require('ioredis'); require('dotenv').config(); // Import routes and middleware const whatsappRoutes = require('./routes/whatsappRoutes'); const facebookRoutes = require('./routes/facebookRoutes'); const twitterRoutes = require('./routes/twitterRoutes'); const linkedinRoutes = require('./routes/linkedinRoutes'); const conversationRoutes = require('./routes/conversationRoutes'); const webhookRoutes = require('./routes/webhookRoutes'); const authMiddleware = require('./middleware/auth'); const errorHandler = require('./middleware/errorHandler'); const logger = require('./utils/logger'); // Import services const MessageRouterService = require('./services/MessageRouterService'); const PlatformNormalizerService = require('./services/PlatformNormalizerService'); const ConversationSyncService = require('./services/ConversationSyncService'); const NotificationService = require('./services/NotificationService'); // Import platform handlers const WhatsAppHandler = require('./handlers/WhatsAppHandler'); const FacebookHandler = require('./handlers/FacebookHandler'); const TwitterHandler = require('./handlers/TwitterHandler'); const LinkedInHandler = require('./handlers/LinkedInHandler'); // Initialize Express app const app = express(); const server = http.createServer(app); // Initialize Socket.IO with CORS const io = socketIo(server, { cors: { origin: [ process.env.FRONTEND_URL || "http://localhost:3001", process.env.BACKEND_URL || "http://localhost:5000" ], methods: ["GET", "POST"], credentials: true } }); // Initialize Redis for caching and session management const redis = new Redis({ host: process.env.REDIS_HOST || 'localhost', port: process.env.REDIS_PORT || 6379, password: process.env.REDIS_PASSWORD, retryDelayOnFailover: 100, maxRetriesPerRequest: 3 }); // Security middleware app.use(helmet({ contentSecurityPolicy: { directives: { defaultSrc: ["'self'"], scriptSrc: ["'self'", "'unsafe-inline'"], styleSrc: ["'self'", "'unsafe-inline'"], imgSrc: ["'self'", "data:", "blob:", "https:"], connectSrc: ["'self'", "wss:", "https:"], mediaSrc: ["'self'", "blob:", "data:"] } } })); // CORS configuration app.use(cors({ origin: [ process.env.FRONTEND_URL || 'http://localhost:3001', process.env.BACKEND_URL || 'http://localhost:5000', process.env.MULTIMODAL_SERVICE_URL || 'http://localhost:5009', process.env.ML_SERVICE_URL || 'http://localhost:5001' ], credentials: true, methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'], allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'X-Hub-Signature-256'] })); // Rate limiting with different limits for different endpoints const generalLimiter = rateLimit({ windowMs: 15 * 60 * 1000, // 15 minutes max: 1000, // Higher limit for social media service message: 'Too many requests from this IP, please try again later.', standardHeaders: true, legacyHeaders: false }); const webhookLimiter = rateLimit({ windowMs: 1 * 60 * 1000, // 1 minute max: 10000, // Very high limit for webhooks message: 'Webhook rate limit exceeded', standardHeaders: true, legacyHeaders: false, skip: (req) => { // Skip rate limiting for verified webhooks return req.headers['x-hub-signature-256'] || req.headers['x-twitter-webhooks-signature']; } }); app.use('/api/', generalLimiter); app.use('/webhooks/', webhookLimiter); // Body parsing middleware app.use(express.json({ limit: '10mb' })); app.use(express.urlencoded({ extended: true, limit: '10mb' })); // Raw body parser for webhook verification app.use('/webhooks', express.raw({ type: 'application/json' })); // Logging middleware app.use(morgan('combined', { stream: { write: message => logger.info(message.trim()) } })); // Health check endpoint app.get('/health', (req, res) => { res.json({ status: 'healthy', service: 'social-media-service', version: '1.0.0', timestamp: new Date().toISOString(), uptime: process.uptime(), memory: process.memoryUsage(), platforms: { whatsapp: whatsAppHandler.isHealthy(), facebook: facebookHandler.isHealthy(), twitter: twitterHandler.isHealthy(), linkedin: linkedInHandler.isHealthy() }, services: { messageRouter: messageRouterService.isHealthy(), platformNormalizer: platformNormalizerService.isHealthy(), conversationSync: conversationSyncService.isHealthy(), notificationService: notificationService.isHealthy() }, connections: { mongodb: mongoose.connection.readyState === 1, redis: redis.status === 'ready' } }); }); // Initialize services const messageRouterService = new MessageRouterService(io, redis); const platformNormalizerService = new PlatformNormalizerService(); const conversationSyncService = new ConversationSyncService(); const notificationService = new NotificationService(io, redis); // Initialize platform handlers const whatsAppHandler = new WhatsAppHandler(messageRouterService, notificationService); const facebookHandler = new FacebookHandler(messageRouterService, notificationService); const twitterHandler = new TwitterHandler(messageRouterService, notificationService); const linkedInHandler = new LinkedInHandler(messageRouterService, notificationService); // Make services and handlers available to routes app.locals.messageRouterService = messageRouterService; app.locals.platformNormalizerService = platformNormalizerService; app.locals.conversationSyncService = conversationSyncService; app.locals.notificationService = notificationService; app.locals.whatsAppHandler = whatsAppHandler; app.locals.facebookHandler = facebookHandler; app.locals.twitterHandler = twitterHandler; app.locals.linkedInHandler = linkedInHandler; app.locals.redis = redis; // Routes app.use('/webhooks', webhookRoutes); app.use('/api/whatsapp', authMiddleware, whatsappRoutes); app.use('/api/facebook', authMiddleware, facebookRoutes); app.use('/api/twitter', authMiddleware, twitterRoutes); app.use('/api/linkedin', authMiddleware, linkedinRoutes); app.use('/api/conversations', authMiddleware, conversationRoutes); // WebSocket connection handling io.on('connection', (socket) => { logger.info(`Client connected: ${socket.id}`); // Join agent to their room for notifications socket.on('join-agent-room', (agentId) => { socket.join(`agent-${agentId}`); logger.info(`Agent ${agentId} joined room`); }); // Handle message sending from dashboard socket.on('send-message', async (data) => { try { const { platform, conversationId, message, agentId } = data; // Route message through appropriate handler let result; switch (platform) { case 'whatsapp': result = await whatsAppHandler.sendMessage(conversationId, message, agentId); break; case 'facebook': result = await facebookHandler.sendMessage(conversationId, message, agentId); break; case 'twitter': result = await twitterHandler.sendMessage(conversationId, message, agentId); break; case 'linkedin': result = await linkedInHandler.sendMessage(conversationId, message, agentId); break; default: throw new Error(`Unsupported platform: ${platform}`); } socket.emit('message-sent', { success: true, result }); } catch (error) { socket.emit('message-error', { error: error.message }); } }); // Handle conversation status updates socket.on('update-conversation-status', async (data) => { try { const { conversationId, status, agentId } = data; await conversationSyncService.updateConversationStatus(conversationId, status, agentId); // Broadcast status update to all connected agents io.emit('conversation-status-updated', { conversationId, status, agentId }); } catch (error) { socket.emit('status-update-error', { error: error.message }); } }); // Handle typing indicators socket.on('typing-start', (data) => { const { conversationId, agentId } = data; socket.to(`conversation-${conversationId}`).emit('agent-typing', { agentId }); }); socket.on('typing-stop', (data) => { const { conversationId, agentId } = data; socket.to(`conversation-${conversationId}`).emit('agent-stopped-typing', { agentId }); }); // Handle disconnection socket.on('disconnect', () => { logger.info(`Client disconnected: ${socket.id}`); }); }); // Error handling middleware app.use(errorHandler); // 404 handler app.use('*', (req, res) => { res.status(404).json({ error: 'Endpoint not found', message: `Cannot ${req.method} ${req.originalUrl}`, service: 'social-media-service' }); }); // Start server const PORT = process.env.PORT || 5010; const HOST = process.env.HOST || '0.0.0.0'; server.listen(PORT, HOST, async () => { logger.info(`[DEPLOY] Social Media Service started on ${HOST}:${PORT}`); logger.info(`[MOBILE] Environment: ${process.env.NODE_ENV || 'development'}`); try { // Connect to MongoDB await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/freemobile-social', { useNewUrlParser: true, useUnifiedTopology: true }); logger.info('[COMPLETE] MongoDB connected successfully'); // Test Redis connection await redis.ping(); logger.info('[COMPLETE] Redis connected successfully'); // Initialize platform handlers await whatsAppHandler.initialize(); await facebookHandler.initialize(); await twitterHandler.initialize(); await linkedInHandler.initialize(); // Initialize services await messageRouterService.initialize(); await conversationSyncService.initialize(); await notificationService.initialize(); logger.info('[COMPLETE] All social media services initialized successfully'); } catch (error) { logger.error('[FAILED] Social media service initialization failed:', error); process.exit(1); } }); // Graceful shutdown process.on('SIGTERM', async () => { logger.info('SIGTERM received, shutting down gracefully'); server.close(async () => { try { await whatsAppHandler.cleanup(); await facebookHandler.cleanup(); await twitterHandler.cleanup(); await linkedInHandler.cleanup(); await messageRouterService.cleanup(); await conversationSyncService.cleanup(); await notificationService.cleanup(); await mongoose.connection.close(); await redis.quit(); logger.info('[COMPLETE] Graceful shutdown completed'); process.exit(0); } catch (error) { logger.error('[FAILED] Error during shutdown:', error); process.exit(1); } }); }); process.on('SIGINT', () => { logger.info('SIGINT received, shutting down gracefully'); process.emit('SIGTERM'); }); module.exports = { app, server, io };