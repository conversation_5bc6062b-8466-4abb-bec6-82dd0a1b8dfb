version: '3.8' services: # Base de données MongoDB mongodb: image: mongo:6.0 restart: unless-stopped environment: MONGO_INITDB_ROOT_USERNAME: ${MONGO_USERNAME:-freemobile} MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD:-freemobile2025} MONGO_INITDB_DATABASE: freemobile-chatbot volumes: - mongodb_data:/data/db - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro networks: - chatbot-network deploy: resources: limits: memory: 512M reservations: memory: 256M # Cache Redis redis: image: redis:7-alpine restart: unless-stopped command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis2025} volumes: - redis_data:/data networks: - chatbot-network deploy: resources: limits: memory: 256M reservations: memory: 128M # Serveur NLP Rasa rasa: build: context: ./rasa dockerfile: Dockerfile.prod restart: unless-stopped ports: - "5005:5005" volumes: - ./rasa:/app command: > bash -c "rasa train --domain domain.yml --data data --config config.yml --out models && rasa run --model models --enable-api --cors \"*\" --port 5005" networks: - chatbot-network depends_on: - redis deploy: resources: limits: memory: 1G reservations: memory: 512M # Backend API backend: build: context: ./backend dockerfile: Dockerfile.prod restart: unless-stopped ports: - "5000:5000" environment: NODE_ENV: production MONGODB_URI: ************************************************************************************* REDIS_URL: redis://:redis2025@redis:6379 RASA_URL: http://rasa:5005 JWT_SECRET: ${JWT_SECRET:-your-super-secure-jwt-secret-production} JWT_REFRESH_SECRET: ${JWT_REFRESH_SECRET:-your-super-secure-refresh-secret} OPENAI_API_KEY: ${OPENAI_API_KEY} FRONTEND_URL: ${FRONTEND_URL:-https://chatbot-free-mobile.vercel.app} UPLOAD_MAX_SIZE: 26214400 RATE_LIMIT_WINDOW: 900000 RATE_LIMIT_MAX: 100 LOG_LEVEL: info ANALYTICS_UPDATE_INTERVAL: 30000 WEBSOCKET_PING_TIMEOUT: 60000 WEBSOCKET_PING_INTERVAL: 25000 volumes: - ./logs:/app/logs - uploads_data:/app/uploads networks: - chatbot-network depends_on: - mongodb - redis - rasa healthcheck: test: ["CMD", "curl", "-f", "http://localhost:5000/health"] interval: 30s timeout: 10s retries: 3 start_period: 40s deploy: resources: limits: memory: 1G reservations: memory: 512M # Reverse Proxy Nginx nginx: image: nginx:alpine restart: unless-stopped ports: - "80:80" - "443:443" volumes: - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro - ./nginx/ssl:/etc/nginx/ssl:ro - ./logs/nginx:/var/log/nginx networks: - chatbot-network depends_on: - backend deploy: resources: limits: memory: 128M reservations: memory: 64M # Monitoring avec Prometheus prometheus: image: prom/prometheus:latest restart: unless-stopped ports: - "9090:9090" volumes: - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro - prometheus_data:/prometheus command: - '--config.file=/etc/prometheus/prometheus.yml' - '--storage.tsdb.path=/prometheus' - '--web.console.libraries=/etc/prometheus/console_libraries' - '--web.console.templates=/etc/prometheus/consoles' - '--storage.tsdb.retention.time=200h' - '--web.enable-lifecycle' networks: - chatbot-network # Visualisation avec Grafana grafana: image: grafana/grafana:latest restart: unless-stopped ports: - "3001:3000" environment: GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-admin2025} volumes: - grafana_data:/var/lib/grafana - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources networks: - chatbot-network depends_on: - prometheus volumes: mongodb_data: redis_data: uploads_data: prometheus_data: grafana_data: networks: chatbot-network: driver: bridge