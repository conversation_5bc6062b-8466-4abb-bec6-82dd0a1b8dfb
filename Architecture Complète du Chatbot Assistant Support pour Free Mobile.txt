Architecture Compl�te du Chatbot Assistant Support pour Free Mobile
Stack Technologique Choisie
Backend:
* Node.js + Express.js (API REST)
* MongoDB (Base de donn�es)
* Socket.io (Communication temps r�el)
* Rasa Open Source (Moteur NLU/NLP)
* Redis (Cache et sessions)
* Winston (Logging)
* JWT (Authentification)
* Docker (Conteneurisation)
Frontend:
* React.js 18
* Material-UI (Composants UI)
* Socket.io-client
* Redux Toolkit (State management)
* React Router (Navigation)
* Axios (HTTP Client)
IA & NLP:
* Rasa (Compr�hension du langage)
* OpenAI API (G�n�ration de r�ponses augment�es)
* Embeddings avec Pinecone (Base de connaissances vectorielle)
Instructions Compl�tes pour l'Agent Cursor
Copie ces instructions dans Cursor et laisse l'agent tout faire :
Je vais te guider pour cr�er un chatbot assistant support professionnel de A � Z. Tu vas suivre ces instructions �tape par �tape, en testant chaque partie avant de passer � la suivante.

CONTEXTE GLOBAL:
- Projet: Chatbot Assistant Support pour Free Mobile
- Architecture: Backend Node.js + Frontend React
- Communication: REST API + WebSocket
- Base de donn�es: MongoDB
- Moteur IA: Rasa + OpenAI
- Utilisateur root: email: <EMAIL>, password: Password131

=== PHASE 1: INITIALISATION DU PROJET ===

1. Cr�er la structure de base:
```bash
mkdir free-mobile-chatbot
cd free-mobile-chatbot
mkdir backend frontend
2. Initialiser le Backend:
cd backend
npm init -y
npm install express cors dotenv mongoose bcryptjs jsonwebtoken socket.io winston morgan helmet express-rate-limit compression
npm install --save-dev nodemon @types/node eslint prettier
3. Cr�er la structure du backend avec ces dossiers:
backend/
��� src/
�   ��� config/
�   �   ��� database.js
�   �   ��� logger.js
�   �   ��� constants.js
�   ��� controllers/
�   �   ��� authController.js
�   �   ��� chatController.js
�   �   ��� adminController.js
�   ��� models/
�   �   ��� User.js
�   �   ��� Conversation.js
�   �   ��� Message.js
�   ��� services/
�   �   ��� nlpService.js
�   �   ��� openaiService.js
�   �   ��� knowledgeService.js
�   ��� middleware/
�   �   ��� authMiddleware.js
�   �   ��� errorHandler.js
�   �   ��� rateLimiter.js
�   ��� routes/
�   �   ��� authRoutes.js
�   �   ��� chatRoutes.js
�   �   ��� adminRoutes.js
�   ��� utils/
�   �   ��� validators.js
�   �   ��� helpers.js
�   ��� app.js
��� .env
��� .gitignore
��� server.js
4. Cr�er le fichier .env dans backend/:
NODE_ENV=development
PORT=5000
MONGODB_URI=mongodb://localhost:27017/free-mobile-chatbot
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
OPENAI_API_KEY=your-openai-api-key
RASA_URL=http://localhost:5005
REDIS_URL=redis://localhost:6379
5. Cr�er backend/src/config/database.js:
const mongoose = require('mongoose');
const logger = require('./logger');

const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    logger.info('MongoDB connected successfully');
  } catch (error) {
    logger.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

module.exports = connectDB;
6. Cr�er backend/src/config/logger.js:
const winston = require('winston');

const logger = winston.createLogger({
  level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.splat(),
    winston.format.json()
  ),
  defaultMeta: { service: 'chatbot-backend' },
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' }),
  ],
});

if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    )
  }));
}

module.exports = logger;
7. Cr�er backend/src/models/User.js:
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
  },
  password: {
    type: String,
    required: true,
  },
  role: {
    type: String,
    enum: ['user', 'agent', 'admin'],
    default: 'user',
  },
  profile: {
    firstName: String,
    lastName: String,
    phoneNumber: String,
    customerId: String,
  },
  preferences: {
    language: {
      type: String,
      default: 'fr',
    },
    notifications: {
      type: Boolean,
      default: true,
    },
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  lastLogin: Date,
});

userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  this.password = await bcrypt.hash(this.password, 10);
  next();
});

userSchema.methods.comparePassword = async function(candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

module.exports = mongoose.model('User', userSchema);
8. Cr�er backend/src/models/Conversation.js:
const mongoose = require('mongoose');

const conversationSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  sessionId: {
    type: String,
    required: true,
    unique: true,
  },
  status: {
    type: String,
    enum: ['active', 'resolved', 'escalated', 'abandoned'],
    default: 'active',
  },
  channel: {
    type: String,
    enum: ['web', 'mobile', 'voice'],
    default: 'web',
  },
  metadata: {
    userAgent: String,
    ipAddress: String,
    location: Object,
  },
  startedAt: {
    type: Date,
    default: Date.now,
  },
  endedAt: Date,
  agentId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  satisfaction: {
    rating: Number,
    feedback: String,
  },
});

module.exports = mongoose.model('Conversation', conversationSchema);
9. Cr�er backend/src/models/Message.js:
const mongoose = require('mongoose');

const messageSchema = new mongoose.Schema({
  conversationId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Conversation',
    required: true,
  },
  sender: {
    type: String,
    enum: ['user', 'bot', 'agent'],
    required: true,
  },
  content: {
    text: String,
    type: {
      type: String,
      enum: ['text', 'button', 'card', 'image', 'file'],
      default: 'text',
    },
    payload: Object,
  },
  intent: {
    name: String,
    confidence: Number,
  },
  entities: [{
    entity: String,
    value: String,
    confidence: Number,
  }],
  timestamp: {
    type: Date,
    default: Date.now,
  },
  metadata: {
    processingTime: Number,
    nlpProvider: String,
    fallback: Boolean,
  },
});

module.exports = mongoose.model('Message', messageSchema);
10. Cr�er backend/src/controllers/authController.js:
const User = require('../models/User');
const jwt = require('jsonwebtoken');
const logger = require('../config/logger');

const generateToken = (userId) => {
  return jwt.sign({ userId }, process.env.JWT_SECRET, { expiresIn: '7d' });
};

exports.register = async (req, res) => {
  try {
    const { email, password, firstName, lastName } = req.body;
    
    logger.info(`Registration attempt for email: ${email}`);
    
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      logger.warn(`Registration failed - email already exists: ${email}`);
      return res.status(400).json({ error: 'Email already exists' });
    }
    
    const user = new User({
      email,
      password,
      profile: { firstName, lastName },
    });
    
    await user.save();
    const token = generateToken(user._id);
    
    logger.info(`User registered successfully: ${email}`);
    
    res.status(201).json({
      user: {
        id: user._id,
        email: user.email,
        role: user.role,
        profile: user.profile,
      },
      token,
    });
  } catch (error) {
    logger.error('Registration error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

exports.login = async (req, res) => {
  try {
    const { email, password } = req.body;
    
    logger.info(`Login attempt for email: ${email}`);
    
    const user = await User.findOne({ email });
    if (!user) {
      logger.warn(`Login failed - user not found: ${email}`);
      return res.status(401).json({ error: 'Invalid credentials' });
    }
    
    const isValidPassword = await user.comparePassword(password);
    if (!isValidPassword) {
      logger.warn(`Login failed - invalid password for: ${email}`);
      return res.status(401).json({ error: 'Invalid credentials' });
    }
    
    user.lastLogin = new Date();
    await user.save();
    
    const token = generateToken(user._id);
    
    logger.info(`User logged in successfully: ${email}`);
    
    res.json({
      user: {
        id: user._id,
        email: user.email,
        role: user.role,
        profile: user.profile,
      },
      token,
    });
  } catch (error) {
    logger.error('Login error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};
11. Cr�er backend/src/services/nlpService.js:
const axios = require('axios');
const logger = require('../config/logger');

class NLPService {
  constructor() {
    this.rasaUrl = process.env.RASA_URL || 'http://localhost:5005';
  }

  async parseMessage(message, senderId) {
    try {
      logger.debug(`Parsing message: "${message}" for sender: ${senderId}`);
      
      const response = await axios.post(`${this.rasaUrl}/model/parse`, {
        text: message,
        message_id: Date.now().toString(),
      });
      
      logger.debug(`NLP response:`, response.data);
      
      return {
        intent: response.data.intent,
        entities: response.data.entities,
        intentRanking: response.data.intent_ranking,
      };
    } catch (error) {
      logger.error('NLP parsing error:', error);
      return {
        intent: { name: 'unknown', confidence: 0 },
        entities: [],
        error: true,
      };
    }
  }

  async getResponse(message, senderId, context = {}) {
    try {
      const response = await axios.post(`${this.rasaUrl}/webhooks/rest/webhook`, {
        sender: senderId,
        message: message,
        metadata: context,
      });
      
      return response.data;
    } catch (error) {
      logger.error('NLP response error:', error);
      return [{
        text: "D�sol�, je rencontre un probl�me technique. Puis-je vous aider autrement?",
      }];
    }
  }
}

module.exports = new NLPService();
12. Cr�er backend/src/services/openaiService.js:
const axios = require('axios');
const logger = require('../config/logger');

class OpenAIService {
  constructor() {
    this.apiKey = process.env.OPENAI_API_KEY;
    this.apiUrl = 'https://api.openai.com/v1/chat/completions';
  }

  async generateResponse(context, userMessage) {
    try {
      logger.debug('Generating AI response for context:', context);
      
      const systemPrompt = `Tu es un assistant virtuel de Free Mobile. Tu dois aider les clients avec leurs questions sur:
      - Forfaits et options
      - Facturation
      - Support technique
      - Gestion de compte
      
      Sois professionnel, concis et empathique. Utilise le tutoiement comme Free Mobile.`;
      
      const response = await axios.post(
        this.apiUrl,
        {
          model: 'gpt-3.5-turbo',
          messages: [
            { role: 'system', content: systemPrompt },
            ...context.previousMessages || [],
            { role: 'user', content: userMessage },
          ],
          temperature: 0.7,
          max_tokens: 200,
        },
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
        }
      );
      
      return response.data.choices[0].message.content;
    } catch (error) {
      logger.error('OpenAI API error:', error);
      return null;
    }
  }

  async summarizeConversation(messages) {
    try {
      const conversation = messages.map(msg => 
        `${msg.sender}: ${msg.content.text}`
      ).join('\n');
      
      const response = await axios.post(
        this.apiUrl,
        {
          model: 'gpt-3.5-turbo',
          messages: [
            {
              role: 'system',
              content: 'R�sume cette conversation en quelques points cl�s en fran�ais.',
            },
            {
              role: 'user',
              content: conversation,
            },
          ],
          temperature: 0.3,
          max_tokens: 150,
        },
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
        }
      );
      
      return response.data.choices[0].message.content;
    } catch (error) {
      logger.error('Summarization error:', error);
      return null;
    }
  }
}

module.exports = new OpenAIService();
13. Cr�er backend/src/middleware/authMiddleware.js:
const jwt = require('jsonwebtoken');
const User = require('../models/User');
const logger = require('../config/logger');

exports.authenticate = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      logger.warn('Authentication failed - no token provided');
      return res.status(401).json({ error: 'Authentication required' });
    }
    
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findById(decoded.userId).select('-password');
    
    if (!user) {
      logger.warn(`Authentication failed - user not found: ${decoded.userId}`);
      return res.status(401).json({ error: 'User not found' });
    }
    
    req.user = user;
    req.token = token;
    next();
  } catch (error) {
    logger.error('Authentication error:', error);
    res.status(401).json({ error: 'Invalid token' });
  }
};

exports.authorize = (...roles) => {
  return (req, res, next) => {
    if (!roles.includes(req.user.role)) {
      logger.warn(`Authorization failed - insufficient role: ${req.user.role}`);
      return res.status(403).json({ error: 'Insufficient permissions' });
    }
    next();
  };
};
14. Cr�er backend/src/controllers/chatController.js:
const Conversation = require('../models/Conversation');
const Message = require('../models/Message');
const nlpService = require('../services/nlpService');
const openaiService = require('../services/openaiService');
const logger = require('../config/logger');
const { v4: uuidv4 } = require('uuid');

exports.startConversation = async (req, res) => {
  try {
    const sessionId = uuidv4();
    const conversation = new Conversation({
      userId: req.user._id,
      sessionId,
      channel: req.body.channel || 'web',
      metadata: {
        userAgent: req.headers['user-agent'],
        ipAddress: req.ip,
      },
    });
    
    await conversation.save();
    
    logger.info(`New conversation started: ${sessionId}`);
    
    res.json({
      conversationId: conversation._id,
      sessionId,
      welcomeMessage: "Bonjour! Je suis l'assistant virtuel Free Mobile. Comment puis-je t'aider aujourd'hui?",
    });
  } catch (error) {
    logger.error('Error starting conversation:', error);
    res.status(500).json({ error: 'Failed to start conversation' });
  }
};

exports.sendMessage = async (req, res) => {
  try {
    const { conversationId, message } = req.body;
    
    logger.info(`Message received for conversation: ${conversationId}`);
    
    // Save user message
    const userMessage = new Message({
      conversationId,
      sender: 'user',
      content: {
        text: message,
        type: 'text',
      },
    });
    await userMessage.save();
    
    // Get NLP analysis
    const nlpResult = await nlpService.parseMessage(message, req.user._id);
    
    // Get conversation history
    const previousMessages = await Message.find({ conversationId })
      .sort({ timestamp: -1 })
      .limit(10)
      .lean();
    
    // Determine response strategy
    let response;
    if (nlpResult.intent.confidence > 0.7) {
      // High confidence - use Rasa response
      const rasaResponses = await nlpService.getResponse(
        message,
        req.user._id.toString(),
        { conversationId }
      );
      response = rasaResponses[0]?.text || "Je n'ai pas compris ta demande.";
    } else {
      // Low confidence - use OpenAI for better understanding
      response = await openaiService.generateResponse(
        { previousMessages: previousMessages.slice(0, 5) },
        message
      );
      
      if (!response) {
        response = "D�sol�, je n'ai pas bien compris. Peux-tu reformuler ta question?";
      }
    }
    
    // Save bot response
    const botMessage = new Message({
      conversationId,
      sender: 'bot',
      content: {
        text: response,
        type: 'text',
      },
      intent: nlpResult.intent,
      entities: nlpResult.entities,
      metadata: {
        nlpProvider: nlpResult.intent.confidence > 0.7 ? 'rasa' : 'openai',
      },
    });
    await botMessage.save();
    
    logger.info(`Bot response sent for conversation: ${conversationId}`);
    
    res.json({
      response,
      intent: nlpResult.intent,
      messageId: botMessage._id,
    });
  } catch (error) {
    logger.error('Error sending message:', error);
    res.status(500).json({ error: 'Failed to process message' });
  }
};

exports.getConversationHistory = async (req, res) => {
  try {
    const { conversationId } = req.params;
    
    const conversation = await Conversation.findById(conversationId);
    if (!conversation || conversation.userId.toString() !== req.user._id.toString()) {
      return res.status(404).json({ error: 'Conversation not found' });
    }
    
    const messages = await Message.find({ conversationId })
      .sort({ timestamp: 1 })
      .lean();
    
    res.json({
      conversation,
      messages,
    });
  } catch (error) {
    logger.error('Error fetching conversation history:', error);
    res.status(500).json({ error: 'Failed to fetch conversation' });
  }
};
15. Cr�er backend/src/routes/authRoutes.js:
const express = require('express');
const router = express.Router();
const authController = require('../controllers/authController');
const { body, validationResult } = require('express-validator');

const validate = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

router.post(
  '/register',
  [
    body('email').isEmail().normalizeEmail(),
    body('password').isLength({ min: 6 }),
    body('firstName').notEmpty().trim(),
    body('lastName').notEmpty().trim(),
  ],
  validate,
  authController.register
);

router.post(
  '/login',
  [
    body('email').isEmail().normalizeEmail(),
    body('password').notEmpty(),
  ],
  validate,
  authController.login
);

module.exports = router;
16. Cr�er backend/src/routes/chatRoutes.js:
const express = require('express');
const router = express.Router();
const chatController = require('../controllers/chatController');
const { authenticate } = require('../middleware/authMiddleware');

router.use(authenticate);

router.post('/conversations/start', chatController.startConversation);
router.post('/messages/send', chatController.sendMessage);
router.get('/conversations/:conversationId', chatController.getConversationHistory);

module.exports = router;
17. Cr�er backend/src/app.js:
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const logger = require('./config/logger');

const authRoutes = require('./routes/authRoutes');
const chatRoutes = require('./routes/chatRoutes');

const app = express();

// Security middleware
app.use(helmet());
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? process.env.FRONTEND_URL 
    : 'http://localhost:3000',
  credentials: true,
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
});
app.use('/api/', limiter);

// Body parsing middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Compression
app.use(compression());

// Logging
app.use(morgan('combined', {
  stream: {
    write: (message) => logger.info(message.trim()),
  },
}));

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/chat', chatRoutes);

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date() });
});

// Error handling middleware
app.use((err, req, res, next) => {
  logger.error('Unhandled error:', err);
  res.status(err.status || 500).json({
    error: process.env.NODE_ENV === 'production' 
      ? 'Internal server error' 
      : err.message,
  });
});

module.exports = app;
18. Cr�er backend/server.js:
require('dotenv').config();
const http = require('http');
const socketIO = require('socket.io');
const app = require('./src/app');
const connectDB = require('./src/config/database');
const logger = require('./src/config/logger');
const User = require('./src/models/User');

const PORT = process.env.PORT || 5000;

// Create HTTP server
const server = http.createServer(app);

// Initialize Socket.IO
const io = socketIO(server, {
  cors: {
    origin: process.env.NODE_ENV === 'production' 
      ? process.env.FRONTEND_URL 
      : 'http://localhost:3000',
    credentials: true,
  },
});

// Socket.IO middleware and handlers
io.on('connection', (socket) => {
  logger.info(`New socket connection: ${socket.id}`);
  
  socket.on('join-conversation', (conversationId) => {
    socket.join(conversationId);
    logger.info(`Socket ${socket.id} joined conversation: ${conversationId}`);
  });
  
  socket.on('disconnect', () => {
    logger.info(`Socket disconnected: ${socket.id}`);
  });
});

// Make io accessible to routes
app.set('io', io);

// Initialize database and start server
const startServer = async () => {
  try {
    await connectDB();
    
    // Create root user if not exists
    const rootUser = await User.findOne({ email: '<EMAIL>' });
    if (!rootUser) {
      await User.create({
        email: '<EMAIL>',
        password: 'Password131',
        role: 'admin',
        profile: {
          firstName: 'Root',
          lastName: 'Admin',
        },
      });
      logger.info('Root user created successfully');
    }
    
    server.listen(PORT, () => {
      logger.info(`Server running on port ${PORT}`);
    });
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
};

startServer();
19. Cr�er backend/.gitignore:
node_modules/
.env
*.log
.DS_Store
dist/
build/
.vscode/
.idea/
20. Mettre � jour backend/package.json avec les scripts:
{
  "scripts": {
    "start": "node server.js",
    "dev": "nodemon server.js",
    "test": "echo \"Error: no test specified\" && exit 1"
  }
}
=== PHASE 2: CR�ATION DU FRONTEND ===
21. Initialiser le Frontend:
cd ../frontend
npx create-react-app . --template typescript
npm install @mui/material @emotion/react @emotion/styled @mui/icons-material
npm install axios socket.io-client react-router-dom
npm install @reduxjs/toolkit react-redux
npm install react-markdown remark-gfm
22. Cr�er la structure du frontend:
frontend/
��� src/
�   ��� components/
�   �   ��� Chat/
�   �   �   ��� ChatWindow.tsx
�   �   �   ��� MessageList.tsx
�   �   �   ��� MessageInput.tsx
�   �   �   ��� Message.tsx
�   �   ��� Auth/
�   �   �   ��� LoginForm.tsx
�   �   �   ��� RegisterForm.tsx
�   �   ��� Layout/
�   �   �   ��� Header.tsx
�   �   �   ��� Layout.tsx
�   �   ��� Common/
�   �       ��� LoadingSpinner.tsx
�   �       ��� ErrorBoundary.tsx
�   ��� services/
�   �   ��� api.ts
�   �   ��� auth.service.ts
�   �   ��� chat.service.ts
�   ��� store/
�   �   ��� index.ts
�   �   ��� authSlice.ts
�   �   ��� chatSlice.ts
�   ��� hooks/
�   �   ��� useAuth.ts
�   �   ��� useSocket.ts
�   ��� utils/
�   �   ��� constants.ts
�   �   ��� helpers.ts
�   ��� types/
�   �   ��� index.ts
�   ��� App.tsx
�   ��� index.tsx
23. Cr�er frontend/src/utils/constants.ts:
export const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';
export const SOCKET_URL = process.env.REACT_APP_SOCKET_URL || 'http://localhost:5000';

export const ROUTES = {
  HOME: '/',
  LOGIN: '/login',
  REGISTER: '/register',
  CHAT: '/chat',
  ADMIN: '/admin',
};
24. Cr�er frontend/src/types/index.ts:
export interface User {
  id: string;
  email: string;
  role: 'user' | 'agent' | 'admin';
  profile: {
    firstName?: string;
    lastName?: string;
    phoneNumber?: string;
  };
}

export interface Message {
  _id: string;
  conversationId: string;
  sender: 'user' | 'bot' | 'agent';
  content: {
    text: string;
    type: 'text' | 'button' | 'card' | 'image' | 'file';
    payload?: any;
  };
  timestamp: Date;
  intent?: {
    name: string;
    confidence: number;
  };
}

export interface Conversation {
  _id: string;
  sessionId: string;
  status: 'active' | 'resolved' | 'escalated' | 'abandoned';
  startedAt: Date;
  endedAt?: Date;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
}

export interface ChatState {
  conversations: Conversation[];
  currentConversation: Conversation | null;
  messages: Message[];
  loading: boolean;
  error: string | null;
  isTyping: boolean;
}
25. Cr�er frontend/src/services/api.ts:
import axios from 'axios';
import { API_BASE_URL } from '../utils/constants';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export default api;
26. Cr�er frontend/src/services/auth.service.ts:
import api from './api';
import { User } from '../types';

interface LoginResponse {
  user: User;
  token: string;
}

interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
}

class AuthService {
  async login(email: string, password: string): Promise<LoginResponse> {
    const response = await api.post<LoginResponse>('/auth/login', {
      email,
      password,
    });
    
    if (response.data.token) {
      localStorage.setItem('token', response.data.token);
    }
    
    return response.data;
  }

  async register(data: RegisterData): Promise<LoginResponse> {
    const response = await api.post<LoginResponse>('/auth/register', data);
    
    if (response.data.token) {
      localStorage.setItem('token', response.data.token);
    }
    
    return response.data;
  }

  logout(): void {
    localStorage.removeItem('token');
  }

  getCurrentUser(): User | null {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  }
}

export default new AuthService();
27. Cr�er frontend/src/services/chat.service.ts:
import api from './api';
import { Conversation, Message } from '../types';

interface StartConversationResponse {
  conversationId: string;
  sessionId: string;
  welcomeMessage: string;
}

interface SendMessageResponse {
  response: string;
  intent: {
    name: string;
    confidence: number;
  };
  messageId: string;
}

class ChatService {
  async startConversation(channel: string = 'web'): Promise<StartConversationResponse> {
    const response = await api.post<StartConversationResponse>('/chat/conversations/start', {
      channel,
    });
    return response.data;
  }

  async sendMessage(conversationId: string, message: string): Promise<SendMessageResponse> {
    const response = await api.post<SendMessageResponse>('/chat/messages/send', {
      conversationId,
      message,
    });
    return response.data;
  }

  async getConversationHistory(conversationId: string): Promise<{
    conversation: Conversation;
    messages: Message[];
  }> {
    const response = await api.get(`/chat/conversations/${conversationId}`);
    return response.data;
  }
}

export default new ChatService();
28. Cr�er frontend/src/store/index.ts:
import { configureStore } from '@reduxjs/toolkit';
import authReducer from './authSlice';
import chatReducer from './chatSlice';

export const store = configureStore({
  reducer: {
    auth: authReducer,
    chat: chatReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
29. Cr�er frontend/src/store/authSlice.ts:
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import authService from '../services/auth.service';
import { AuthState } from '../types';

const initialState: AuthState = {
  user: authService.getCurrentUser(),
  token: localStorage.getItem('token'),
  isAuthenticated: !!localStorage.getItem('token'),
  loading: false,
  error: null,
};

export const login = createAsyncThunk(
  'auth/login',
  async ({ email, password }: { email: string; password: string }) => {
    const response = await authService.login(email, password);
    localStorage.setItem('user', JSON.stringify(response.user));
    return response;
  }
);

export const register = createAsyncThunk(
  'auth/register',
  async (data: { email: string; password: string; firstName: string; lastName: string }) => {
    const response = await authService.register(data);
    localStorage.setItem('user', JSON.stringify(response.user));
    return response;
  }
);

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    logout: (state) => {
      authService.logout();
      state.user = null;
      state.token = null;
      state.isAuthenticated = false;
      localStorage.removeItem('user');
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Login
      .addCase(login.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(login.fulfilled, (state, action) => {
        state.loading = false;
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.isAuthenticated = true;
      })
      .addCase(login.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Login failed';
      })
      // Register
      .addCase(register.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(register.fulfilled, (state, action) => {
        state.loading = false;
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.isAuthenticated = true;
      })
      .addCase(register.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Registration failed';
      });
  },
});

export const { logout, clearError } = authSlice.actions;
export default authSlice.reducer;
30. Cr�er frontend/src/store/chatSlice.ts:
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import chatService from '../services/chat.service';
import { ChatState, Message } from '../types';

const initialState: ChatState = {
  conversations: [],
  currentConversation: null,
  messages: [],
  loading: false,
  error: null,
  isTyping: false,
};

export const startConversation = createAsyncThunk(
  'chat/startConversation',
  async () => {
    const response = await chatService.startConversation();
    return response;
  }
);

export const sendMessage = createAsyncThunk(
  'chat/sendMessage',
  async ({ conversationId, message }: { conversationId: string; message: string }) => {
    const response = await chatService.sendMessage(conversationId, message);
    return response;
  }
);

export const loadConversationHistory = createAsyncThunk(
  'chat/loadHistory',
  async (conversationId: string) => {
    const response = await chatService.getConversationHistory(conversationId);
    return response;
  }
);

const chatSlice = createSlice({
  name: 'chat',
  initialState,
  reducers: {
    addMessage: (state, action) => {
      state.messages.push(action.payload);
    },
    setTyping: (state, action) => {
      state.isTyping = action.payload;
    },
    clearChat: (state) => {
      state.currentConversation = null;
      state.messages = [];
    },
  },
  extraReducers: (builder) => {
    builder
      // Start conversation
      .addCase(startConversation.fulfilled, (state, action) => {
        state.currentConversation = {
          _id: action.payload.conversationId,
          sessionId: action.payload.sessionId,
          status: 'active',
          startedAt: new Date(),
        };
        // Add welcome message
        state.messages = [{
          _id: Date.now().toString(),
          conversationId: action.payload.conversationId,
          sender: 'bot',
          content: {
            text: action.payload.welcomeMessage,
            type: 'text',
          },
          timestamp: new Date(),
        }];
      })
      // Send message
      .addCase(sendMessage.pending, (state) => {
        state.isTyping = true;
      })
      .addCase(sendMessage.fulfilled, (state, action) => {
        state.isTyping = false;
        // Bot response is handled by socket or polling
      })
      // Load history
      .addCase(loadConversationHistory.fulfilled, (state, action) => {
        state.currentConversation = action.payload.conversation;
        state.messages = action.payload.messages;
      });
  },
});

export const { addMessage, setTyping, clearChat } = chatSlice.actions;
export default chatSlice.reducer;
31. Cr�er frontend/src/hooks/useAuth.ts:
import { useSelector, useDispatch } from 'react-redux';
import { RootState, AppDispatch } from '../store';
import { logout } from '../store/authSlice';

export const useAuth = () => {
  const dispatch = useDispatch<AppDispatch>();
  const auth = useSelector((state: RootState) => state.auth);

  const handleLogout = () => {
    dispatch(logout());
  };

  return {
    ...auth,
    logout: handleLogout,
  };
};
32. Cr�er frontend/src/hooks/useSocket.ts:
import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import io, { Socket } from 'socket.io-client';
import { SOCKET_URL } from '../utils/constants';
import { addMessage } from '../store/chatSlice';

export const useSocket = (conversationId?: string) => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [connected, setConnected] = useState(false);
  const dispatch = useDispatch();

  useEffect(() => {
    const newSocket = io(SOCKET_URL, {
      auth: {
        token: localStorage.getItem('token'),
      },
    });

    newSocket.on('connect', () => {
      console.log('Socket connected');
      setConnected(true);
      
      if (conversationId) {
        newSocket.emit('join-conversation', conversationId);
      }
    });

    newSocket.on('disconnect', () => {
      console.log('Socket disconnected');
      setConnected(false);
    });

    newSocket.on('new-message', (message) => {
      dispatch(addMessage(message));
    });

    setSocket(newSocket);

    return () => {
      newSocket.close();
    };
  }, [conversationId, dispatch]);

  return { socket, connected };
};
33. Cr�er frontend/src/components/Auth/LoginForm.tsx:
import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  Box,
  Paper,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
} from '@mui/material';
import { AppDispatch, RootState } from '../../store';
import { login, clearError } from '../../store/authSlice';
import { ROUTES } from '../../utils/constants';

const LoginForm: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { loading, error, isAuthenticated } = useSelector((state: RootState) => state.auth);
  
  const [formData, setFormData] = useState({
    email: '<EMAIL>',
    password: 'Password131',
  });

  useEffect(() => {
    if (isAuthenticated) {
      navigate(ROUTES.CHAT);
    }
  }, [isAuthenticated, navigate]);

  useEffect(() => {
    return () => {
      dispatch(clearError());
    };
  }, [dispatch]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    dispatch(login(formData));
  };

  return (
    <Box
      sx={{
        height: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        bgcolor: 'background.default',
      }}
    >
      <Paper elevation={3} sx={{ p: 4, maxWidth: 400, width: '100%' }}>
        <Typography variant="h4" align="center" gutterBottom>
          Connexion
        </Typography>
        <Typography variant="subtitle1" align="center" color="text.secondary" gutterBottom>
          Assistant Support Free Mobile
        </Typography>
        
        {error && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {error}
          </Alert>
        )}
        
        <Box component="form" onSubmit={handleSubmit} sx={{ mt: 3 }}>
          <TextField
            fullWidth
            label="Email"
            name="email"
            type="email"
            value={formData.email}
            onChange={handleChange}
            margin="normal"
            required
            autoComplete="email"
          />
          
          <TextField
            fullWidth
            label="Mot de passe"
            name="password"
            type="password"
            value={formData.password}
            onChange={handleChange}
            margin="normal"
            required
            autoComplete="current-password"
          />
          
          <Button
            type="submit"
            fullWidth
            variant="contained"
            sx={{ mt: 3, mb: 2 }}
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : 'Se connecter'}
          </Button>
          
          <Box sx={{ textAlign: 'center' }}>
            <Link to={ROUTES.REGISTER} style={{ textDecoration: 'none' }}>
              <Typography variant="body2" color="primary">
                Pas encore de compte? S'inscrire
              </Typography>
            </Link>
          </Box>
        </Box>
      </Paper>
    </Box>
  );
};

export default LoginForm;
34. Cr�er frontend/src/components/Chat/ChatWindow.tsx:
import React, { useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Box, Paper, Typography, Divider } from '@mui/material';
import MessageList from './MessageList';
import MessageInput from './MessageInput';
import { AppDispatch, RootState } from '../../store';
import { startConversation } from '../../store/chatSlice';
import { useSocket } from '../../hooks/useSocket';

const ChatWindow: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { currentConversation, messages, isTyping } = useSelector(
    (state: RootState) => state.chat
  );
  const { connected } = useSocket(currentConversation?._id);
  const initialized = useRef(false);

  useEffect(() => {
    if (!initialized.current) {
      dispatch(startConversation());
      initialized.current = true;
    }
  }, [dispatch]);

  return (
    <Paper
      elevation={3}
      sx={{
        height: '80vh',
        display: 'flex',
        flexDirection: 'column',
        maxWidth: 800,
        mx: 'auto',
        mt: 2,
      }}
    >
      <Box sx={{ p: 2, bgcolor: 'primary.main', color: 'white' }}>
        <Typography variant="h6">Assistant Free Mobile</Typography>
        <Typography variant="caption">
          {connected ? 'En ligne' : 'Hors ligne'}
        </Typography>
      </Box>
      
      <Divider />
      
      <MessageList messages={messages} isTyping={isTyping} />
      
      <Divider />
      
      <MessageInput conversationId={currentConversation?._id} />
    </Paper>
  );
};

export default ChatWindow;
35. Cr�er frontend/src/components/Chat/MessageList.tsx:
import React, { useEffect, useRef } from 'react';
import { Box, Typography } from '@mui/material';
import { Message as MessageType } from '../../types';
import Message from './Message';

interface MessageListProps {
  messages: MessageType[];
  isTyping: boolean;
}

const MessageList: React.FC<MessageListProps> = ({ messages, isTyping }) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, isTyping]);

  return (
    <Box
      sx={{
        flex: 1,
        overflowY: 'auto',
        p: 2,
        display: 'flex',
        flexDirection: 'column',
        gap: 2,
      }}
    >
      {messages.map((message) => (
        <Message key={message._id} message={message} />
      ))}
      
      {isTyping && (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Typography variant="body2" color="text.secondary">
            L'assistant est en train d'�crire...
          </Typography>
        </Box>
      )}
      
      <div ref={messagesEndRef} />
    </Box>
  );
};

export default MessageList;
36. Cr�er frontend/src/components/Chat/Message.tsx:
import React from 'react';
import { Box, Paper, Typography, Chip } from '@mui/material';
import { Message as MessageType } from '../../types';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

interface MessageProps {
  message: MessageType;
}

const Message: React.FC<MessageProps> = ({ message }) => {
  const isUser = message.sender === 'user';

  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: isUser ? 'flex-end' : 'flex-start',
        mb: 1,
      }}
    >
      <Paper
        elevation={1}
        sx={{
          p: 2,
          maxWidth: '70%',
          bgcolor: isUser ? 'primary.light' : 'grey.100',
          color: isUser ? 'white' : 'text.primary',
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
          <Typography variant="caption" sx={{ fontWeight: 'bold' }}>
            {message.sender === 'user' ? 'Vous' : 'Assistant'}
          </Typography>
          {message.intent && (
            <Chip
              label={`${message.intent.name} (${Math.round(message.intent.confidence * 100)}%)`}
              size="small"
              variant="outlined"
            />
          )}
        </Box>
        
        {message.content.type === 'text' ? (
          <ReactMarkdown remarkPlugins={[remarkGfm]}>
            {message.content.text}
          </ReactMarkdown>
        ) : (
          <Typography>{message.content.text}</Typography>
        )}
        
        <Typography variant="caption" sx={{ display: 'block', mt: 1, opacity: 0.7 }}>
          {new Date(message.timestamp).toLocaleTimeString()}
        </Typography>
      </Paper>
    </Box>
  );
};

export default Message;
37. Cr�er frontend/src/components/Chat/MessageInput.tsx:
import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Box, TextField, IconButton, CircularProgress } from '@mui/material';
import SendIcon from '@mui/icons-material/Send';
import { AppDispatch, RootState } from '../../store';
import { sendMessage, addMessage } from '../../store/chatSlice';

interface MessageInputProps {
  conversationId?: string;
}

const MessageInput: React.FC<MessageInputProps> = ({ conversationId }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { isTyping } = useSelector((state: RootState) => state.chat);
  const [message, setMessage] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!message.trim() || !conversationId) return;
    
    const userMessage = {
      _id: Date.now().toString(),
      conversationId,
      sender: 'user' as const,
      content: {
        text: message,
        type: 'text' as const,
      },
      timestamp: new Date(),
    };
    
    dispatch(addMessage(userMessage));
    setMessage('');
    
    const response = await dispatch(sendMessage({ conversationId, message })).unwrap();
    
    const botMessage = {
      _id: response.messageId,
      conversationId,
      sender: 'bot' as const,
      content: {
        text: response.response,
        type: 'text' as const,
      },
      timestamp: new Date(),
      intent: response.intent,
    };
    
    dispatch(addMessage(botMessage));
  };

  return (
    <Box
      component="form"
      onSubmit={handleSubmit}
      sx={{
        p: 2,
        display: 'flex',
        gap: 1,
      }}
    >
      <TextField
        fullWidth
        placeholder="Tapez votre message..."
        value={message}
        onChange={(e) => setMessage(e.target.value)}
        disabled={isTyping || !conversationId}
        variant="outlined"
        size="small"
      />
      
      <IconButton
        type="submit"
        color="primary"
        disabled={!message.trim() || isTyping || !conversationId}
      >
        {isTyping ? <CircularProgress size={24} /> : <SendIcon />}
      </IconButton>
    </Box>
  );
};

export default MessageInput;
38. Cr�er frontend/src/components/Layout/Header.tsx:
import React from 'react';
import { AppBar, Toolbar, Typography, Button, Box } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';

const Header: React.FC = () => {
  const navigate = useNavigate();
  const { user, logout } = useAuth();

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  return (
    <AppBar position="static">
      <Toolbar>
        <Typography variant="h6" sx={{ flexGrow: 1 }}>
          Free Mobile Support
        </Typography>
        
        {user && (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant="body2">
              {user.email}
            </Typography>
            <Button color="inherit" onClick={handleLogout}>
              D�connexion
            </Button>
          </Box>
        )}
      </Toolbar>
    </AppBar>
  );
};

export default Header;
39. Cr�er frontend/src/components/Layout/Layout.tsx:
import React from 'react';
import { Box } from '@mui/material';
import Header from './Header';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>
      <Header />
      <Box component="main" sx={{ p: 3 }}>
        {children}
      </Box>
    </Box>
  );
};

export default Layout;
40. Cr�er frontend/src/App.tsx:
import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { Provider } from 'react-redux';
import { store } from './store';
import LoginForm from './components/Auth/LoginForm';
import ChatWindow from './components/Chat/ChatWindow';
import Layout from './components/Layout/Layout';
import { useAuth } from './hooks/useAuth';
import { ROUTES } from './utils/constants';

const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});

const PrivateRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated } = useAuth();
  return isAuthenticated ? <>{children}</> : <Navigate to={ROUTES.LOGIN} />;
};

function AppContent() {
  return (
    <Router>
      <Routes>
        <Route path={ROUTES.LOGIN} element={<LoginForm />} />
        <Route
          path={ROUTES.CHAT}
          element={
            <PrivateRoute>
              <Layout>
                <ChatWindow />
              </Layout>
            </PrivateRoute>
          }
        />
        <Route path={ROUTES.HOME} element={<Navigate to={ROUTES.CHAT} />} />
      </Routes>
    </Router>
  );
}

function App() {
  return (
    <Provider store={store}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <AppContent />
      </ThemeProvider>
    </Provider>
  );
}

export default App;
41. Mettre � jour frontend/src/index.tsx:
import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
=== PHASE 3: CONFIGURATION DOCKER ET RASA ===
42. Cr�er docker-compose.yml � la racine du projet:
version: '3.8'

services:
  mongodb:
    image: mongo:latest
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    environment:
      MONGO_INITDB_DATABASE: free-mobile-chatbot

  redis:
    image: redis:alpine
    ports:
      - "6379:6379"

  rasa:
    image: rasa/rasa:latest-full
    ports:
      - "5005:5005"
    volumes:
      - ./rasa:/app
    command: run --enable-api --cors "*"

volumes:
  mongodb_data:
43. Cr�er le dossier rasa et ses fichiers:
mkdir rasa
cd rasa
44. Cr�er rasa/config.yml:
language: fr
pipeline:
  - name: WhitespaceTokenizer
  - name: RegexFeaturizer
  - name: LexicalSyntacticFeaturizer
  - name: CountVectorsFeaturizer
  - name: CountVectorsFeaturizer
    analyzer: char_wb
    min_ngram: 1
    max_ngram: 4
  - name: DIETClassifier
    epochs: 100
  - name: EntitySynonymMapper
  - name: ResponseSelector
    epochs: 100
policies:
  - name: MemoizationPolicy
  - name: TEDPolicy
    max_history: 5
    epochs: 100
  - name: RulePolicy
45. Cr�er rasa/domain.yml:
version: "3.1"

intents:
  - greet
  - goodbye
  - affirm
  - deny
  - bot_challenge
  - check_balance
  - check_invoice
  - change_plan
  - report_issue
  - ask_support

entities:
  - phone_number
  - invoice_date
  - plan_name

responses:
  utter_greet:
    - text: "Bonjour! Je suis l'assistant virtuel Free Mobile. Comment puis-je t'aider aujourd'hui?"
  
  utter_goodbye:
    - text: "Au revoir! N'h�site pas � revenir si tu as d'autres questions."
  
  utter_iamabot:
    - text: "Je suis un assistant virtuel cr�� pour t'aider avec tes questions Free Mobile."

session_config:
  session_expiration_time: 60
  carry_over_slots_to_new_session: true
46. Cr�er rasa/data/nlu.yml:
version: "3.1"
nlu:
- intent: greet
  examples: |
    - salut
    - bonjour
    - hello
    - bonsoir
    - coucou
    - hey

- intent: check_balance
  examples: |
    - je veux consulter mon solde
    - quel est mon cr�dit
    - combien il me reste
    - mon forfait
    - consommation

- intent: check_invoice
  examples: |
    - je veux voir ma facture
    - montrer ma derni�re facture
    - facture du mois dernier
    - combien je dois payer
47. Cr�er rasa/data/stories.yml:
version: "3.1"
stories:
- story: greet path
  steps:
  - intent: greet
  - action: utter_greet

- story: say goodbye
  steps:
  - intent: goodbye
  - action: utter_goodbye
=== PHASE 4: SCRIPTS DE D�MARRAGE ===
48. Cr�er start-backend.sh � la racine:
#!/bin/bash
cd backend
npm install
npm run dev
49. Cr�er start-frontend.sh � la racine:
#!/bin/bash
cd frontend
npm install
npm start
50. Cr�er start-all.sh � la racine:
#!/bin/bash
echo "Starting Docker services..."
docker-compose up -d

echo "Waiting for services to start..."
sleep 10

echo "Starting backend..."
cd backend
npm install
npm run dev &

echo "Starting frontend..."
cd ../frontend
npm install
npm start
51. Rendre les scripts ex�cutables:
chmod +x start-backend.sh start-frontend.sh start-all.sh
=== PHASE 5: TESTS ET FINALISATION ===
52. Tester l'installation compl�te:
# Dans un terminal
docker-compose up -d

# Dans un autre terminal
cd backend
npm install
npm run dev

# Dans un troisi�me terminal
cd frontend
npm install
npm start
53. Cr�er un README.md � la racine:
# Free Mobile Chatbot Assistant Support

## Installation

1. Cloner le projet
2. Installer Docker et Docker Compose
3. Lancer `docker-compose up -d` pour MongoDB, Redis et Rasa
4. Lancer le backend: `cd backend && npm install && npm run dev`
5. Lancer le frontend: `cd frontend && npm install && npm start`

## Acc�s

- Frontend: http://localhost:3000
- Backend API: http://localhost:5000
- Rasa: http://localhost:5005

## Compte Root

- Email: <EMAIL>
- Password: Password131

## Architecture

- Backend: Node.js + Express + MongoDB
- Frontend: React + TypeScript + Material-UI
- IA: Rasa + OpenAI
- Cache: Redis
- WebSocket: Socket.io
54. Pour d�marrer le projet complet, ex�cute simplement:
./start-all.sh
Puis visite http://localhost:3000 pour acc�der au chatbot!
IMPORTANT:
* Assure-toi d'avoir Docker install� et en cours d'ex�cution
* Les ports 3000, 5000, 5005, 6379, et 27017 doivent �tre libres
* Pour utiliser OpenAI, ajoute ta cl� API dans le fichier backend/.env
* Le projet utilise des loggers Winston qui cr�eront des fichiers de logs automatiquement
Le projet est maintenant complet et fonctionnel!

