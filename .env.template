# =============================================
# 🔐 FREE MOBILE CHATBOT RNCP - ENVIRONMENT CONFIGURATION TEMPLATE
# =============================================
# Copy this file to .env and fill in your actual values
# NEVER commit .env files with real credentials to version control
# =============================================

# =============================================
# 🌐 APPLICATION CONFIGURATION
# =============================================
NODE_ENV=development
PORT=5000
FRONTEND_URL=http://localhost:3000

# =============================================
# 🗄️ REDIS CONFIGURATION
# =============================================
# Local Redis (Development)
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password_here
REDIS_DB=0

# Production Redis (Replace with your Redis provider)
# REDIS_URL=redis://username:password@your-redis-host:6379
# REDIS_TLS_URL=rediss://username:password@your-redis-host:6380

# =============================================
# 🍃 MONGODB ATLAS CONFIGURATION
# =============================================
# MongoDB Atlas Connection String
# Replace <db_password> with your actual database password
MONGODB_URI=mongodb+srv://Anderson-Archimed01:<db_password>@chatbotrncp.za6xmim.mongodb.net/chatbotrncp?retryWrites=true&w=majority&appName=ChatbotRNCP

# Database Configuration
DB_NAME=chatbotrncp
DB_USER=Anderson-Archimed01
DB_PASSWORD=your_secure_database_password_here

# MongoDB Atlas Cluster Details
DB_CLUSTER=chatbotrncp.za6xmim.mongodb.net
DB_APP_NAME=ChatbotRNCP

# =============================================
# 🔐 JWT AUTHENTICATION
# =============================================
# Generate a strong secret key for JWT tokens
JWT_SECRET=your_super_secret_jwt_key_minimum_32_characters_long
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# =============================================
# 🔄 CI/CD CONFIGURATION
# =============================================
# CircleCI Configuration
CIRCLECI_TOKEN=your_circleci_personal_access_token_here
CIRCLECI_PROJECT_ID=your_project_id_here
CIRCLECI_VCS_TYPE=github
CIRCLECI_USERNAME=Anderson-Archimede
CIRCLECI_REPO=ChatbotRNCP

# GitHub Configuration
GITHUB_TOKEN=your_github_personal_access_token_here
GITHUB_REPO=Anderson-Archimede/ChatbotRNCP

# =============================================
# 📧 EMAIL CONFIGURATION (Optional)
# =============================================
# SMTP Configuration for notifications
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password_here
FROM_EMAIL=<EMAIL>

# =============================================
# 🔔 NOTIFICATION SERVICES (Optional)
# =============================================
# Slack Webhook for notifications
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/slack/webhook

# Discord Webhook for notifications
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/your/discord/webhook

# =============================================
# 🌐 EXTERNAL API CONFIGURATION
# =============================================
# Free Mobile API Configuration
FREE_MOBILE_API_URL=https://api.freemobile.fr
FREE_MOBILE_API_KEY=your_free_mobile_api_key_here

# AI/ML Service Configuration
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# =============================================
# 📊 MONITORING & ANALYTICS
# =============================================
# Application Monitoring
SENTRY_DSN=your_sentry_dsn_here
NEW_RELIC_LICENSE_KEY=your_new_relic_key_here

# Analytics
GOOGLE_ANALYTICS_ID=your_ga_tracking_id_here
MIXPANEL_TOKEN=your_mixpanel_token_here

# =============================================
# 🔒 SECURITY CONFIGURATION
# =============================================
# CORS Configuration
CORS_ORIGIN=http://localhost:3000,https://your-production-domain.com
CORS_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Session Configuration
SESSION_SECRET=your_session_secret_key_here
SESSION_MAX_AGE=86400000

# =============================================
# 🚀 DEPLOYMENT CONFIGURATION
# =============================================
# Vercel Configuration
VERCEL_TOKEN=your_vercel_token_here
VERCEL_PROJECT_ID=your_vercel_project_id_here
VERCEL_ORG_ID=your_vercel_org_id_here

# Docker Configuration
DOCKER_REGISTRY=your_docker_registry_here
DOCKER_IMAGE_TAG=latest

# =============================================
# 🧪 TESTING CONFIGURATION
# =============================================
# Test Database
TEST_MONGODB_URI=mongodb://localhost:27017/chatbotrncp_test
TEST_REDIS_URL=redis://localhost:6379/1

# Test API Keys (use separate test keys)
TEST_OPENAI_API_KEY=your_test_openai_key_here
TEST_FREE_MOBILE_API_KEY=your_test_api_key_here

# =============================================
# 📝 LOGGING CONFIGURATION
# =============================================
LOG_LEVEL=info
LOG_FORMAT=combined
LOG_FILE_PATH=./logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# =============================================
# 🔧 DEVELOPMENT CONFIGURATION
# =============================================
# Development Tools
DEBUG=chatbotrncp:*
WEBPACK_DEV_SERVER_PORT=3001
HOT_RELOAD=true

# API Documentation
SWAGGER_ENABLED=true
API_DOCS_PATH=/api-docs

# =============================================
# 📱 MOBILE CONFIGURATION
# =============================================
# Push Notifications
FCM_SERVER_KEY=your_fcm_server_key_here
APNS_KEY_ID=your_apns_key_id_here
APNS_TEAM_ID=your_apns_team_id_here

# =============================================
# 🌍 INTERNATIONALIZATION
# =============================================
DEFAULT_LANGUAGE=fr
SUPPORTED_LANGUAGES=fr,en,es
TRANSLATION_API_KEY=your_translation_api_key_here

# =============================================
# 💾 BACKUP CONFIGURATION
# =============================================
# Database Backup
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=./backups

# File Storage
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
AWS_S3_BUCKET=your_s3_bucket_name_here
AWS_REGION=eu-west-1

# =============================================
# 🔍 SEARCH CONFIGURATION
# =============================================
# Elasticsearch Configuration
ELASTICSEARCH_URL=http://localhost:9200
ELASTICSEARCH_INDEX=chatbotrncp
ELASTICSEARCH_USERNAME=your_elasticsearch_user_here
ELASTICSEARCH_PASSWORD=your_elasticsearch_password_here

# =============================================
# 📈 PERFORMANCE MONITORING
# =============================================
# Performance Metrics
PERFORMANCE_MONITORING=true
METRICS_COLLECTION_INTERVAL=60000
HEALTH_CHECK_INTERVAL=30000

# Cache Configuration
CACHE_TTL=3600
CACHE_MAX_SIZE=1000
CACHE_ENABLED=true

# =============================================
# 🛡️ SECURITY HEADERS
# =============================================
# Content Security Policy
CSP_ENABLED=true
CSP_REPORT_URI=/api/csp-report

# HTTPS Configuration
FORCE_HTTPS=false
HSTS_MAX_AGE=31536000
HSTS_INCLUDE_SUBDOMAINS=true

# =============================================
# 📊 BUSINESS METRICS
# =============================================
# Free Mobile Specific Configuration
FREE_MOBILE_SUBSCRIBER_COUNT=13000000
FREE_MOBILE_SUPPORT_HOURS=24/7
FREE_MOBILE_SLA_TARGET=99.97

# Performance Targets
API_RESPONSE_TIME_TARGET=2000
ML_PROCESSING_TIME_TARGET=3000
UPTIME_TARGET=99.97

# =============================================
# 🎯 FEATURE FLAGS
# =============================================
# Feature Toggles
FEATURE_AI_SUGGESTIONS=true
FEATURE_PREDICTIVE_ANALYTICS=true
FEATURE_AGENT_TRAINING=true
FEATURE_EMERGENCY_CALLS=true
FEATURE_MULTIMODAL_CHAT=true

# A/B Testing
AB_TESTING_ENABLED=true
AB_TESTING_PERCENTAGE=10

# =============================================
# 📞 TELEPHONY CONFIGURATION
# =============================================
# WebRTC Configuration
WEBRTC_STUN_SERVER=stun:stun.l.google.com:19302
WEBRTC_TURN_SERVER=turn:your-turn-server.com:3478
WEBRTC_TURN_USERNAME=your_turn_username_here
WEBRTC_TURN_PASSWORD=your_turn_password_here

# SIP Configuration
SIP_SERVER=sip.freemobile.fr
SIP_PORT=5060
SIP_USERNAME=your_sip_username_here
SIP_PASSWORD=your_sip_password_here

# =============================================
# 🔚 END OF CONFIGURATION
# =============================================
# Remember to:
# 1. Copy this file to .env
# 2. Replace all placeholder values with real credentials
# 3. Never commit .env files to version control
# 4. Use different values for development, staging, and production
# 5. Regularly rotate sensitive credentials
# =============================================
