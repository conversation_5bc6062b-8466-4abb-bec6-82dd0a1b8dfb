# Prompt de Migration AWS pour Free Mobile Chatbot SAV - Cursor/VS Code

## 🎯 Contexte du Projet

**Application**: Free Mobile Chatbot SAV (RNCP)  
**État actuel**: Development Environment Operational - Production Ready Framework  
**Version**: 2.0.0  
**Cible**: 13+ millions d'abonnés Free Mobile  
**Environnement local**: VS Code, Node.js v22.17.0, React 18.2.0, authentification JWT, mock database

**Architecture actuelle fonctionnelle**:
- ✅ Serveur d'authentification (port 5000) avec JWT et bcrypt
- ✅ Application React (port 3000) avec Material-UI et Redux
- ✅ Mock database en mémoire avec utilisateurs de test
- ✅ CORS configuré, TypeScript 96.2%, bundle optimisé (-17.9%)
- ✅ Framework AI Call Management architecturé (prêt pour intégration)

## 🚀 Objectif de Migration

Migrer l'application locale vers AWS avec une architecture scalable pour 13+ millions d'utilisateurs :
- **EC2** : Hébergement application (Load Balancer + Auto Scaling)
- **RDS** : Base de données production (PostgreSQL/MySQL)
- **ElastiCache** : Cache Redis pour sessions JWT et performance
- **S3** : Assets statiques et logs
- **CloudFront** : CDN global
- **Route 53** : DNS et domaine

## 📋 Étapes de Migration Détaillées

### 1. 🔧 **Préparation Infrastructure AWS**

Génère-moi les scripts Terraform ou AWS CLI pour :

```bash
# Configuration VPC et réseaux
# Référence: https://docs.aws.amazon.com/vpc/latest/userguide/VPC_Scenario2.html
```

**Ressources à créer** :
- VPC avec sous-réseaux publics/privés multi-AZ
- Security Groups (Port 22 SSH, 80/443 HTTPS, 3000/5000 pour dev)
- Internet Gateway et NAT Gateway
- Elastic IP pour stabilité
- Application Load Balancer avec certificat SSL

**Liens de référence Cursor** :
- https://docs.aws.amazon.com/vpc/latest/userguide/getting-started-ipv4.html
- https://docs.aws.amazon.com/elasticloadbalancing/latest/application/create-application-load-balancer.html
- https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/ec2-security-groups.html

### 2. 🖥️ **Migration Application vers EC2**

**Spécifications instance** :
- Type : t3.medium (production) / t2.micro (test)
- OS : Amazon Linux 2023 ou Ubuntu 22.04 LTS
- Auto Scaling Group (min: 2, max: 10, desired: 2)

**Script de déploiement EC2 requis** :

```bash
#!/bin/bash
# Installation Node.js v18+ et dependencies
# Configuration PM2 pour process management
# Setup nginx reverse proxy
# SSL avec Let's Encrypt ou ACM
```

**Configuration à générer** :
- User Data script pour installation automatique
- PM2 ecosystem.config.js pour Node.js processes
- Nginx configuration avec proxy vers ports 3000/5000
- Variables d'environnement avec AWS Systems Manager Parameter Store

**Liens de référence Cursor** :
- https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/user-data.html
- https://docs.aws.amazon.com/autoscaling/ec2/userguide/create-auto-scaling-group.html
- https://pm2.keymetrics.io/docs/usage/deployment/

### 3. 🗄️ **Migration Base de Données vers RDS**

**Configuration RDS** :
- Engine : PostgreSQL 15.x (recommandé pour scalabilité)
- Instance : db.t3.micro (dev) / db.r6g.large (prod)
- Multi-AZ : Oui (haute disponibilité)
- Backup : 7 jours retention
- Encryption : Activé (KMS)

**Script de migration données** :

```javascript
// Migration de mock database vers PostgreSQL
// Conversion des utilisateurs de test vers schéma RDS
// Migration des sessions et données authentification
```

**Tables à créer** :
- users (id, email, password_hash, role, created_at, updated_at)
- sessions (session_id, user_id, jwt_token, expires_at)
- conversations (conversation_id, user_id, messages, ai_analysis)
- call_logs (call_id, user_id, agent_id, duration, resolution)

**Liens de référence Cursor** :
- https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/CHAP_PostgreSQL.html
- https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/USER_CreateDBInstance.html
- https://node-postgres.com/ (pour l'intégration Node.js)

### 4. ⚡ **Migration Cache vers ElastiCache**

**Configuration ElastiCache Redis** :
- Engine : Redis 7.x
- Node type : cache.t3.micro (dev) / cache.r6g.large (prod)
- Cluster mode : Activé pour scalabilité
- Backup : Snapshots quotidiens

**Migration données Redis** :

```bash
# Sauvegarde locale (si applicable)
redis-cli BGSAVE
# Import via S3 snapshot ou migration en ligne
```

**Usage cache à implémenter** :
- Sessions JWT (TTL: 7 jours)
- Réponses AI fréquentes (TTL: 1 heure)
- Rate limiting utilisateurs
- Cache conversation context

**Liens de référence Cursor** :
- https://docs.aws.amazon.com/AmazonElastiCache/latest/red-ug/WhatIs.html
- https://docs.aws.amazon.com/AmazonElastiCache/latest/red-ug/Migration-Console.html
- https://redis.js.org/ (client Node.js)

### 5. 🤖 **Intégration AI Services AWS**

**Services à configurer** :
- Amazon Bedrock pour LLM (Claude/GPT alternative)
- Amazon Comprehend pour analyse sentiment
- Amazon Lex pour NLP conversations
- Amazon Connect pour escalade vers agents

**Code à générer** :

```javascript
// Service integration avec AWS SDK
// Remplacement mock AI par services réels
// Configuration retry et error handling
// Monitoring avec CloudWatch
```

**Liens de référence Cursor** :
- https://docs.aws.amazon.com/bedrock/latest/userguide/what-is-bedrock.html
- https://docs.aws.amazon.com/comprehend/latest/dg/what-is.html
- https://docs.aws.amazon.com/lex/latest/dg/what-is.html

### 6. 🔐 **Sécurité et Secrets Management**

**AWS Secrets Manager configuration** :
- JWT secrets rotation automatique
- Database credentials
- API keys (OpenAI, tiers)
- Encryption keys

**Code de récupération secrets** :

```javascript
// Remplacement variables .env par AWS Secrets Manager
// SDK integration avec retry logic
// Caching secrets pour performance
```

**Liens de référence Cursor** :
- https://docs.aws.amazon.com/secretsmanager/latest/userguide/intro.html
- https://docs.aws.amazon.com/sdk-for-javascript/v3/developer-guide/loading-node-credentials-json-file.html

### 7. 📊 **Monitoring et Logging**

**CloudWatch configuration** :
- Custom metrics : authentications/min, AI responses time
- Log groups : application, nginx, database
- Alarms : CPU > 80%, Memory > 85%, Error rate > 1%
- Dashboard : temps réel pour 13M utilisateurs

**X-Ray tracing** :
- Traçage des requêtes complètes
- Performance AI services
- Database query optimization

**Liens de référence Cursor** :
- https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/WhatIsCloudWatch.html
- https://docs.aws.amazon.com/xray/latest/devguide/xray-gettingstarted.html

### 8. 🚀 **CI/CD Pipeline**

**GitHub Actions + AWS CodeDeploy** :

```yaml
# .github/workflows/deploy.yml
# Build, test, deploy automatique
# Blue/Green deployment
# Rollback automatique si échec
```

**Liens de référence Cursor** :
- https://docs.aws.amazon.com/codedeploy/latest/userguide/welcome.html
- https://docs.github.com/en/actions/deployment/deploying-to-your-cloud-provider/deploying-to-amazon-elastic-container-service

## 🎯 **Livrables Attendus de Cursor**

### 1. **Scripts Infrastructure**
- Terraform/CloudFormation pour création complète AWS
- User Data scripts EC2 avec toutes installations
- Security Groups et networking configuration

### 2. **Code Application Modifié**
- Configuration production avec endpoints AWS
- Remplacement mock database par PostgreSQL
- Intégration ElastiCache pour sessions
- Secrets Manager pour credentials
- Logging CloudWatch intégré

### 3. **Scripts Migration**
- Export/Import données de développement
- Migration users de test vers RDS
- Validation data integrity

### 4. **Configuration DevOps**
- PM2 ecosystem.config.js
- Nginx configuration optimisée
- CI/CD pipeline GitHub Actions
- Docker configuration (optionnel)

### 5. **Monitoring Setup**
- CloudWatch dashboards JSON
- Alarms configuration
- X-Ray tracing setup
- Health checks endpoints

### 6. **Documentation**
- Guide déploiement step-by-step
- Troubleshooting common issues
- Scaling recommendations for 13M users
- Security best practices checklist

## 🔍 **Critères de Validation**

### Performance
- ✅ Load time < 2s (target actuel respecté)
- ✅ AI response < 1s (intégration services AWS)
- ✅ Support 13M utilisateurs concurrent
- ✅ 99.97% uptime SLA

### Sécurité
- ✅ Zero vulnerabilities (status actuel maintenu)
- ✅ Encryption at rest et in transit
- ✅ JWT secure avec rotation
- ✅ WAF et DDoS protection

### Scalabilité
- ✅ Auto Scaling fonctionnel
- ✅ Database read replicas
- ✅ Cache hit ratio > 80%
- ✅ CDN global delivery

## 🔧 **Configuration Spécifique Projet**

**Variables d'environnement à migrer** :
```env
# Actuelles en local
NODE_ENV=production
JWT_SECRET=<from-aws-secrets>
DATABASE_URL=<rds-endpoint>
REDIS_URL=<elasticache-endpoint>
OPENAI_API_KEY=<from-secrets-manager>
```

**Ports et services** :
- Frontend React : 3000
- Backend Auth : 5000
- Load Balancer : 80/443
- Database : 5432 (PostgreSQL)
- Cache : 6379 (Redis)

**Architecture actuelle à préserver** :
- Redux state management
- Material-UI components
- JWT authentication flow
- TypeScript coverage 96.2%
- Optimized bundle size

## 📚 **Ressources et Documentation**

**AWS Best Practices** :
- https://aws.amazon.com/architecture/well-architected/
- https://docs.aws.amazon.com/whitepapers/latest/aws-overview/introduction.html
- https://aws.amazon.com/getting-started/hands-on/

**Node.js sur AWS** :
- https://docs.aws.amazon.com/sdk-for-javascript/
- https://aws.amazon.com/getting-started/hands-on/deploy-nodejs-web-app/

**Production Deployment** :
- https://create-react-app.dev/docs/deployment/
- https://expressjs.com/en/advanced/best-practice-security.html

---

**🚨 Instructions pour Cursor** :
Génère un plan de migration complet avec tous les scripts, configurations et code nécessaires. Priorise la compatibilité avec l'architecture existante fonctionnelle et assure une migration sans interruption de service. Utilise les liens fournis pour vérifier les bonnes pratiques AWS actuelles.