# 🔧 Variables d'Environnement - Chatbot Free Mobile
# Copiez ce fichier vers .env et personnalisez les valeurs

# =============================================
# 🌍 ENVIRONNEMENT GÉNÉRAL
# =============================================

# Environnement de déploiement (development, staging, production)
NODE_ENV=development

# =============================================
# 🗄️ BASE DE DONNÉES MONGODB
# =============================================

# Nom d'utilisateur MongoDB
MONGO_USERNAME=freemobile

# Mot de passe MongoDB (CHANGEZ EN PRODUCTION!)
MONGO_PASSWORD=freemobile123

# Nom de la base de données
MONGO_DATABASE=freemobile_chatbot

# URI complète MongoDB (générée automatiquement si vide)
# Format: ********************************:port/database?options
MONGODB_URI=************************************************************************************

# =============================================
# 🔴 CACHE REDIS
# =============================================

# URL Redis (générée automatiquement si vide)
# Format: redis://[password@]host:port[/database]
REDIS_URL=redis://redis:6379

# Mot de passe Redis (optionnel pour développement)
# REDIS_PASSWORD=redis_secure_password

# =============================================
# 🚀 BACKEND API
# =============================================

# Port du serveur backend
PORT=5000
BACKEND_PORT=5000

# Secret JWT (GÉNÉREZ UNE CLÉ SÉCURISÉE EN PRODUCTION!)
# Utilisez: openssl rand -base64 32
JWT_SECRET=dev-secret-key-not-for-production-please-change

# Durée de validité du token JWT
JWT_EXPIRES_IN=24h

# URL du frontend (pour CORS)
FRONTEND_URL=http://localhost:3000

# =============================================
# 🤖 INTELLIGENCE ARTIFICIELLE
# =============================================

# Clé API OpenAI (REQUIS - Obtenez-la sur https://openai.com)
OPENAI_API_KEY=sk-your-openai-api-key-here

# Modèle OpenAI à utiliser
OPENAI_MODEL=gpt-3.5-turbo

# URL du serveur Rasa
RASA_URL=http://rasa:5005

# =============================================
# 🌐 FRONTEND REACT
# =============================================

# Port du serveur frontend
FRONTEND_PORT=3000

# URL de l'API backend (pour les appels depuis le frontend)
REACT_APP_API_URL=http://localhost:5000

# URL Socket.io (pour le chat temps réel)
REACT_APP_SOCKET_URL=http://localhost:5000

# Environnement React
REACT_APP_ENVIRONMENT=development

# =============================================
# 🔒 SÉCURITÉ
# =============================================

# Origines CORS autorisées (séparées par des virgules)
CORS_ORIGIN=http://localhost:3000,http://localhost:3001

# Rate limiting - Fenêtre de temps (millisecondes)
RATE_LIMIT_WINDOW_MS=900000

# Rate limiting - Nombre maximum de requêtes par fenêtre
RATE_LIMIT_MAX_REQUESTS=100

# =============================================
# 📊 LOGS ET MONITORING
# =============================================

# Niveau de log (error, warn, info, debug)
LOG_LEVEL=debug

# Fichier de log (laisser vide pour console uniquement)
LOG_FILE=./logs/app.log

# Rotation des logs (taille maximale en MB)
LOG_MAX_SIZE=20

# Nombre de fichiers de log à conserver
LOG_MAX_FILES=5

# =============================================
# 📧 EMAIL (OPTIONNEL)
# =============================================

# Configuration SMTP pour les notifications
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# Email d'expéditeur par défaut
EMAIL_FROM=<EMAIL>

# =============================================
# 🔔 NOTIFICATIONS (OPTIONNEL)
# =============================================

# URL Webhook Slack pour notifications d'erreurs
# SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# Token Discord pour notifications
# DISCORD_TOKEN=your-discord-bot-token

# =============================================
# ☁️ STOCKAGE CLOUD (OPTIONNEL)
# =============================================

# AWS S3 pour le stockage de fichiers
# AWS_ACCESS_KEY_ID=your-aws-access-key
# AWS_SECRET_ACCESS_KEY=your-aws-secret-key
# AWS_REGION=eu-west-1
# AWS_S3_BUCKET=freemobile-chatbot-uploads

# =============================================
# 📈 ANALYTICS (OPTIONNEL)
# =============================================

# Google Analytics
# GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX

# Sentry pour le monitoring d'erreurs
# SENTRY_DSN=https://<EMAIL>/project-id

# =============================================
# 🐳 DOCKER SPÉCIFIQUE
# =============================================

# Version des images Docker à utiliser
DOCKER_TAG=latest

# Registry Docker privé (optionnel)
# DOCKER_REGISTRY=your-registry.com

# =============================================
# 🌐 PRODUCTION SSL/DOMAINE
# =============================================

# Domaine principal (pour SSL Let's Encrypt)
# DOMAIN=chatbot.freemobile.fr

# Email pour Let's Encrypt
# LETSENCRYPT_EMAIL=<EMAIL>

# =============================================
# 💾 SAUVEGARDES
# =============================================

# Répertoire de sauvegarde
BACKUP_DIR=./backups

# Rétention des sauvegardes (en jours)
BACKUP_RETENTION_DAYS=7

# =============================================
# 🧪 TESTS
# =============================================

# Base de données de test
TEST_MONGODB_URI=*****************************************************************************************

# =============================================
# NOTES IMPORTANTES
# =============================================

# 🔐 SÉCURITÉ:
# - Changez TOUS les mots de passe par défaut
# - Générez un JWT_SECRET sécurisé: openssl rand -base64 32
# - Ne committez JAMAIS le fichier .env dans Git
# - Utilisez des secrets Docker en production

# 📝 CONFIGURATION:
# - Adaptez les URLs selon votre environnement
# - Vérifiez les ports disponibles
# - Testez toutes les connexions avant déploiement

# 🚀 PRODUCTION:
# - Utilisez HTTPS en production
# - Configurez un proxy reverse (Nginx)
# - Activez les sauvegardes automatiques
# - Surveillez les logs et métriques 