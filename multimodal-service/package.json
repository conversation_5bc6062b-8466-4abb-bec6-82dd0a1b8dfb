{"name": "freemobile-multimodal-service", "version": "1.0.0", "description": "Multimodal Processing Service for Free Mobile Chatbot - Text, Voice, and Image Understanding", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "build": "tsc", "build:watch": "tsc --watch"}, "keywords": ["multimodal", "nlp", "computer-vision", "speech-recognition", "ocr", "ai", "machine-learning", "free-mobile", "chatbot"], "author": "Free Mobile DevOps Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "tesseract.js": "^5.0.2", "node-speech-to-text": "^0.1.0", "gtts": "^0.2.1", "openai": "^4.20.1", "google-cloud-speech": "^6.0.0", "google-cloud-vision": "^4.0.2", "google-cloud-translate": "^8.0.2", "azure-cognitiveservices-speech-sdk": "^1.34.1", "aws-sdk": "^2.1490.0", "socket.io": "^4.7.4", "redis": "^4.6.10", "mongodb": "^6.3.0", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "winston": "^3.11.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "uuid": "^9.0.1", "moment": "^2.29.4", "jimp": "^0.22.10", "face-api.js": "^0.22.2", "compromise": "^14.10.0", "natural": "^6.5.0", "sentiment": "^5.0.2", "keyword-extractor": "^0.0.25", "language-detect": "^1.1.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.54.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "typescript": "^5.3.2", "@types/node": "^20.9.0", "@types/express": "^4.17.21", "@types/multer": "^1.4.11", "@types/jest": "^29.5.8"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/freemobile/chatbot-dashboard.git", "directory": "multimodal-service"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/server.js"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}}