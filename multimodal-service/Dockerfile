# =============================================
# 🎭 MULTIMODAL SERVICE DOCKERFILE
# Multi-stage build for AI multimodal processing
# Supports text, image, audio, and video analysis
# =============================================

# Build stage
FROM python:3.11-slim AS builder

# Set working directory
WORKDIR /app

# Install build dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    gcc \
    g++ \
    git \
    curl \
    wget \
    ffmpeg \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgl1-mesa-glx \
    libglib2.0-0 \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements
COPY requirements.txt ./

# Create virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip setuptools wheel && \
    pip install --no-cache-dir -r requirements.txt

# Install additional ML libraries
RUN pip install --no-cache-dir \
    torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu \
    transformers \
    opencv-python-headless \
    librosa \
    Pillow \
    scikit-image

# Production stage
FROM python:3.11-slim AS production

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    curl \
    ffmpeg \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN groupadd -r multimodal && useradd -r -g multimodal multimodal

# Set working directory
WORKDIR /app

# Copy virtual environment from builder
COPY --from=builder /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy application code
COPY --chown=multimodal:multimodal . .

# Create necessary directories
RUN mkdir -p /app/models /app/logs /app/temp /app/cache /app/uploads && \
    chown -R multimodal:multimodal /app

# Download pre-trained models
RUN python -c "
from transformers import pipeline
import torch

# Download sentiment analysis model
sentiment_pipeline = pipeline('sentiment-analysis', model='nlptown/bert-base-multilingual-uncased-sentiment')

# Download image classification model
image_pipeline = pipeline('image-classification', model='google/vit-base-patch16-224')

# Download audio classification model (if available)
try:
    audio_pipeline = pipeline('audio-classification', model='facebook/wav2vec2-base-960h')
except:
    print('Audio model not available, skipping...')

print('Models downloaded successfully')
" || echo "Model download failed, continuing..."

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV TRANSFORMERS_CACHE=/app/cache
ENV HF_HOME=/app/cache
ENV TORCH_HOME=/app/cache
ENV PORT=5009
ENV WORKERS=2

# Health check
HEALTHCHECK --interval=30s --timeout=20s --start-period=180s --retries=3 \
    CMD curl -f http://localhost:5009/health || exit 1

# Switch to non-root user
USER multimodal

# Expose port
EXPOSE 5009

# Start application with Gunicorn
CMD ["gunicorn", "--bind", "0.0.0.0:5009", "--workers", "2", "--worker-class", "uvicorn.workers.UvicornWorker", "--timeout", "180", "--keep-alive", "2", "--max-requests", "500", "--max-requests-jitter", "50", "app:app"]

# Labels for metadata
LABEL maintainer="Free Mobile Chatbot Team"
LABEL version="1.0.0"
LABEL description="Multimodal Service for Free Mobile Chatbot - AI Content Analysis"
LABEL org.opencontainers.image.source="https://github.com/free-mobile/chatbot"
