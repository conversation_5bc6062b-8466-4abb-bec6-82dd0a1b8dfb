/** * ============================================= * FREE MOBILE MULTIMODAL SERVICE * Advanced multimodal processing for text, voice, and image understanding * Handles OCR, speech recognition, emotion analysis, and content understanding * ============================================= */ const express = require('express'); const http = require('http'); const socketIo = require('socket.io'); const cors = require('cors'); const helmet = require('helmet'); const morgan = require('morgan'); const multer = require('multer'); const rateLimit = require('express-rate-limit'); require('dotenv').config(); // Import routes and middleware const textRoutes = require('./routes/textRoutes'); const voiceRoutes = require('./routes/voiceRoutes'); const imageRoutes = require('./routes/imageRoutes'); const multimodalRoutes = require('./routes/multimodalRoutes'); const authMiddleware = require('./middleware/auth'); const errorHandler = require('./middleware/errorHandler'); const logger = require('./utils/logger'); // Import services const TextProcessingService = require('./services/TextProcessingService'); const VoiceProcessingService = require('./services/VoiceProcessingService'); const ImageProcessingService = require('./services/ImageProcessingService'); const MultimodalFusionService = require('./services/MultimodalFusionService'); const EmotionAnalysisService = require('./services/EmotionAnalysisService'); // Initialize Express app const app = express(); const server = http.createServer(app); // Initialize Socket.IO with CORS const io = socketIo(server, { cors: { origin: process.env.FRONTEND_URL || "http://localhost:3001", methods: ["GET", "POST"], credentials: true } }); // Security middleware app.use(helmet({ contentSecurityPolicy: { directives: { defaultSrc: ["'self'"], scriptSrc: ["'self'", "'unsafe-inline'"], styleSrc: ["'self'", "'unsafe-inline'"], imgSrc: ["'self'", "data:", "blob:", "https:"], connectSrc: ["'self'", "wss:", "https:"], mediaSrc: ["'self'", "blob:", "data:"] } } })); // CORS configuration app.use(cors({ origin: [ process.env.FRONTEND_URL || 'http://localhost:3001', process.env.BACKEND_URL || 'http://localhost:5000', process.env.CALL_SERVICE_URL || 'http://localhost:5004' ], credentials: true, methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'], allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'] })); // Rate limiting const limiter = rateLimit({ windowMs: 15 * 60 * 1000, // 15 minutes max: 200, // Higher limit for multimodal processing message: 'Too many requests from this IP, please try again later.', standardHeaders: true, legacyHeaders: false }); app.use('/api/', limiter); // File upload configuration const upload = multer({ storage: multer.memoryStorage(), limits: { fileSize: 50 * 1024 * 1024, // 50MB limit for images/audio files: 5 // Maximum 5 files per request }, fileFilter: (req, file, cb) => { // Allow images, audio, and video files const allowedTypes = /jpeg|jpg|png|gif|webp|mp3|wav|ogg|mp4|webm|avi/; const extname = allowedTypes.test(file.originalname.toLowerCase()); const mimetype = allowedTypes.test(file.mimetype); if (mimetype && extname) { return cb(null, true); } else { cb(new Error('Invalid file type. Only images, audio, and video files are allowed.')); } } }); // Body parsing middleware app.use(express.json({ limit: '10mb' })); app.use(express.urlencoded({ extended: true, limit: '10mb' })); // Logging middleware app.use(morgan('combined', { stream: { write: message => logger.info(message.trim()) } })); // Health check endpoint app.get('/health', (req, res) => { res.json({ status: 'healthy', service: 'multimodal-service', version: '1.0.0', timestamp: new Date().toISOString(), uptime: process.uptime(), memory: process.memoryUsage(), services: { textProcessing: textProcessingService.isHealthy(), voiceProcessing: voiceProcessingService.isHealthy(), imageProcessing: imageProcessingService.isHealthy(), multimodalFusion: multimodalFusionService.isHealthy(), emotionAnalysis: emotionAnalysisService.isHealthy() } }); }); // Initialize services const textProcessingService = new TextProcessingService(); const voiceProcessingService = new VoiceProcessingService(); const imageProcessingService = new ImageProcessingService(); const multimodalFusionService = new MultimodalFusionService(); const emotionAnalysisService = new EmotionAnalysisService(); // Make services available to routes app.locals.textProcessingService = textProcessingService; app.locals.voiceProcessingService = voiceProcessingService; app.locals.imageProcessingService = imageProcessingService; app.locals.multimodalFusionService = multimodalFusionService; app.locals.emotionAnalysisService = emotionAnalysisService; app.locals.upload = upload; // Routes app.use('/api/text', authMiddleware, textRoutes); app.use('/api/voice', authMiddleware, voiceRoutes); app.use('/api/image', authMiddleware, imageRoutes); app.use('/api/multimodal', authMiddleware, multimodalRoutes); // WebSocket connection handling io.on('connection', (socket) => { logger.info(`Client connected: ${socket.id}`); // Handle real-time text processing socket.on('process-text-stream', async (data) => { try { const { text, options } = data; const result = await textProcessingService.processText(text, options); socket.emit('text-processed', { success: true, result }); } catch (error) { socket.emit('text-error', { error: error.message }); } }); // Handle real-time voice processing socket.on('process-voice-stream', async (data) => { try { const { audioData, options } = data; const result = await voiceProcessingService.processVoiceStream(audioData, options); socket.emit('voice-processed', { success: true, result }); } catch (error) { socket.emit('voice-error', { error: error.message }); } }); // Handle real-time emotion analysis socket.on('analyze-emotion', async (data) => { try { const { content, type } = data; // type: 'text', 'voice', 'image' const result = await emotionAnalysisService.analyzeEmotion(content, type); socket.emit('emotion-analyzed', { success: true, result }); } catch (error) { socket.emit('emotion-error', { error: error.message }); } }); // Handle multimodal fusion requests socket.on('fuse-multimodal', async (data) => { try { const { inputs, options } = data; const result = await multimodalFusionService.fuseInputs(inputs, options); socket.emit('multimodal-fused', { success: true, result }); } catch (error) { socket.emit('multimodal-error', { error: error.message }); } }); // Handle disconnection socket.on('disconnect', () => { logger.info(`Client disconnected: ${socket.id}`); }); }); // Error handling middleware app.use(errorHandler); // 404 handler app.use('*', (req, res) => { res.status(404).json({ error: 'Endpoint not found', message: `Cannot ${req.method} ${req.originalUrl}`, service: 'multimodal-service' }); }); // Start server const PORT = process.env.PORT || 5009; const HOST = process.env.HOST || '0.0.0.0'; server.listen(PORT, HOST, async () => { logger.info(`[DEPLOY] Multimodal Service started on ${HOST}:${PORT}`); logger.info(` Environment: ${process.env.NODE_ENV || 'development'}`); try { // Initialize services await textProcessingService.initialize(); await voiceProcessingService.initialize(); await imageProcessingService.initialize(); await multimodalFusionService.initialize(); await emotionAnalysisService.initialize(); logger.info('[COMPLETE] All multimodal services initialized successfully'); } catch (error) { logger.error('[FAILED] Multimodal service initialization failed:', error); process.exit(1); } }); // Graceful shutdown process.on('SIGTERM', async () => { logger.info('SIGTERM received, shutting down gracefully'); server.close(async () => { try { await textProcessingService.cleanup(); await voiceProcessingService.cleanup(); await imageProcessingService.cleanup(); await multimodalFusionService.cleanup(); await emotionAnalysisService.cleanup(); logger.info('[COMPLETE] Graceful shutdown completed'); process.exit(0); } catch (error) { logger.error('[FAILED] Error during shutdown:', error); process.exit(1); } }); }); process.on('SIGINT', () => { logger.info('SIGINT received, shutting down gracefully'); process.emit('SIGTERM'); }); module.exports = { app, server, io };