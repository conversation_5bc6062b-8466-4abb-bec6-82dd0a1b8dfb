/** * ============================================= * IMAGE PROCESSING SERVICE * Advanced image processing with OCR, object detection, and emotion analysis * Supports Google Cloud Vision API, Tesseract.js, and face-api.js * ============================================= */ const vision = require('@google-cloud/vision'); const Tesseract = require('tesseract.js'); const sharp = require('sharp'); const Jimp = require('jimp'); const faceapi = require('face-api.js'); const canvas = require('canvas'); const fs = require('fs').promises; const path = require('path'); const { v4: uuidv4 } = require('uuid'); const logger = require('../utils/logger'); // Setup face-api.js with canvas const { Canvas, Image, ImageData } = canvas; faceapi.env.monkeyPatch({ Canvas, Image, ImageData }); class ImageProcessingService { constructor() { this.isInitialized = false; this.googleVisionClient = null; this.faceApiModelsLoaded = false; // Supported image formats this.supportedFormats = ['jpeg', 'jpg', 'png', 'webp', 'gif', 'bmp', 'tiff']; // Free Mobile product categories for recognition this.productCategories = { smartphones: [ 'iphone', 'samsung', 'galaxy', 'pixel', 'huawei', 'xiaomi', 'oneplus', 'smartphone', 'téléphone', 'mobile', 'android', 'ios' ], accessories: [ 'chargeur', 'cable', 'écouteurs', 'casque', 'coque', 'protection', 'batterie', 'powerbank', 'support', 'adaptateur' ], boxes: [ 'freebox', 'box', 'modem', 'routeur', 'wifi', 'internet', 'décodeur', 'player', 'revolution', 'delta', 'pop' ], sim: [ 'carte sim', 'sim', 'puce', 'nano sim', 'micro sim', 'esim' ] }; // Emotion detection mappings this.emotionMappings = { happy: ['happy', 'joy', 'smile', 'content'], sad: ['sad', 'sorrow', 'disappointed', 'melancholy'], angry: ['angry', 'mad', 'furious', 'irritated'], surprised: ['surprised', 'shocked', 'amazed', 'astonished'], fear: ['fearful', 'scared', 'afraid', 'worried'], disgust: ['disgusted', 'repulsed', 'revolted'], neutral: ['neutral', 'calm', 'composed'] }; } /** * Initialize Image Processing Service */ async initialize() { try { // Initialize Google Cloud Vision if (process.env.GOOGLE_APPLICATION_CREDENTIALS) { this.googleVisionClient = new vision.ImageAnnotatorClient(); logger.info('[COMPLETE] Google Cloud Vision initialized'); } // Load face-api.js models await this.loadFaceApiModels(); // Create temp directory for image processing await this.ensureTempDirectory(); this.isInitialized = true; logger.info('[COMPLETE] Image Processing Service initialized successfully'); } catch (error) { logger.error('[FAILED] Image Processing Service initialization failed:', error); throw error; } } /** * Load face-api.js models for facial emotion detection */ async loadFaceApiModels() { try { const modelsPath = path.join(__dirname, '../../models/face-api'); // Create models directory if it doesn't exist await fs.mkdir(modelsPath, { recursive: true }); // Load models (in production, these would be pre-downloaded) await Promise.all([ faceapi.nets.tinyFaceDetector.loadFromDisk(modelsPath), faceapi.nets.faceLandmark68Net.loadFromDisk(modelsPath), faceapi.nets.faceRecognitionNet.loadFromDisk(modelsPath), faceapi.nets.faceExpressionNet.loadFromDisk(modelsPath) ]); this.faceApiModelsLoaded = true; logger.info('[COMPLETE] Face-api.js models loaded successfully'); } catch (error) { logger.warn(' Face-api.js models not available, facial emotion detection disabled'); this.faceApiModelsLoaded = false; } } /** * Ensure temporary directory exists for image processing */ async ensureTempDirectory() { const tempDir = path.join(__dirname, '../../temp/images'); try { await fs.mkdir(tempDir, { recursive: true }); } catch (error) { if (error.code !== 'EEXIST') { throw error; } } } /** * Process image with comprehensive analysis */ async processImage(imageBuffer, options = {}) { try { if (!imageBuffer || !Buffer.isBuffer(imageBuffer)) { throw new Error('Invalid image buffer provided'); } const processingId = uuidv4(); const startTime = Date.now(); logger.info(` Starting image processing: ${processingId}`); // Save image buffer to temporary file const tempFilePath = await this.saveImageBuffer(imageBuffer, processingId); // Get image metadata const metadata = await this.getImageMetadata(tempFilePath); // Enhance image quality if needed const enhancedImagePath = await this.enhanceImageQuality(tempFilePath, options); // Perform parallel processing const [ ocrResult, objectDetection, faceAnalysis, productRecognition, imageQuality ] = await Promise.allSettled([ this.performOCR(enhancedImagePath, options), this.detectObjects(enhancedImagePath, options), this.analyzeFaces(enhancedImagePath, options), this.recognizeProducts(enhancedImagePath, options), this.assessImageQuality(tempFilePath, metadata) ]); // Compile results const result = { processingId, processedAt: new Date().toISOString(), processingTime: Date.now() - startTime, metadata, ocr: ocrResult.status === 'fulfilled' ? ocrResult.value : null, objects: objectDetection.status === 'fulfilled' ? objectDetection.value : null, faces: faceAnalysis.status === 'fulfilled' ? faceAnalysis.value : null, products: productRecognition.status === 'fulfilled' ? productRecognition.value : null, quality: imageQuality.status === 'fulfilled' ? imageQuality.value : null, enhanced: enhancedImagePath !== tempFilePath }; // Advanced analysis if requested if (options.advancedAnalysis && this.googleVisionClient) { result.advancedAnalysis = await this.performAdvancedImageAnalysis(enhancedImagePath); } // Cleanup temporary files await this.cleanupTempFiles([tempFilePath, enhancedImagePath]); logger.info(`[COMPLETE] Image processing completed: ${processingId} (${result.processingTime}ms)`); return result; } catch (error) { logger.error('[FAILED] Image processing failed:', error); throw error; } } /** * Perform OCR (Optical Character Recognition) */ async performOCR(imagePath, options = {}) { try { const results = []; // Try Google Cloud Vision first if available if (this.googleVisionClient) { try { const googleResult = await this.performGoogleOCR(imagePath, options); results.push(googleResult); } catch (error) { logger.warn('Google OCR failed, falling back to Tesseract:', error.message); } } // Use Tesseract.js as fallback or primary const tesseractResult = await this.performTesseractOCR(imagePath, options); results.push(tesseractResult); // Combine results for better accuracy const combinedResult = this.combineOCRResults(results); return { text: combinedResult.text, confidence: combinedResult.confidence, words: combinedResult.words, lines: combinedResult.lines, paragraphs: combinedResult.paragraphs, language: options.language || 'fr', providers: results.map(r => r.provider), boundingBoxes: combinedResult.boundingBoxes }; } catch (error) { logger.error('[FAILED] OCR processing failed:', error); return { text: '', confidence: 0, error: error.message }; } } /** * Perform OCR using Google Cloud Vision */ async performGoogleOCR(imagePath, options = {}) { const imageBuffer = await fs.readFile(imagePath); const [result] = await this.googleVisionClient.textDetection({ image: { content: imageBuffer }, imageContext: { languageHints: [options.language || 'fr', 'en'] } }); const detections = result.textAnnotations; if (!detections || detections.length === 0) { return { text: '', confidence: 0, provider: 'google' }; } const fullText = detections[0].description; const words = detections.slice(1).map(detection => ({ text: detection.description, confidence: 0.9, // Google doesn't provide word-level confidence boundingBox: detection.boundingPoly })); return { text: fullText, confidence: 0.9, words, provider: 'google', boundingBoxes: detections.map(d => d.boundingPoly) }; } /** * Perform OCR using Tesseract.js */ async performTesseractOCR(imagePath, options = {}) { const { data } = await Tesseract.recognize(imagePath, options.language || 'fra+eng', { logger: m => { if (m.status === 'recognizing text') { logger.debug(`Tesseract progress: ${Math.round(m.progress * 100)}%`); } } }); return { text: data.text, confidence: data.confidence / 100, words: data.words.map(word => ({ text: word.text, confidence: word.confidence / 100, boundingBox: word.bbox })), lines: data.lines, paragraphs: data.paragraphs, provider: 'tesseract' }; } /** * Combine OCR results from multiple providers */ combineOCRResults(results) { if (results.length === 0) { return { text: '', confidence: 0, words: [], lines: [], paragraphs: [] }; } if (results.length === 1) { return results[0]; } // Use the result with highest confidence as primary const primaryResult = results.reduce((best, current) => current.confidence > best.confidence ? current : best ); // Combine words from all results for better coverage const allWords = results.flatMap(result => result.words || []); const uniqueWords = this.deduplicateWords(allWords); return { text: primaryResult.text, confidence: primaryResult.confidence, words: uniqueWords, lines: primaryResult.lines || [], paragraphs: primaryResult.paragraphs || [], boundingBoxes: primaryResult.boundingBoxes || [] }; } /** * Detect objects in image */ async detectObjects(imagePath, options = {}) { try { if (!this.googleVisionClient) { return { objects: [], confidence: 0, provider: 'none' }; } const imageBuffer = await fs.readFile(imagePath); const [result] = await this.googleVisionClient.objectLocalization({ image: { content: imageBuffer } }); const objects = result.localizedObjectAnnotations || []; return { objects: objects.map(obj => ({ name: obj.name, confidence: obj.score, boundingBox: obj.boundingPoly, category: this.categorizeObject(obj.name) })), totalObjects: objects.length, provider: 'google', categories: this.groupObjectsByCategory(objects) }; } catch (error) { logger.error('[FAILED] Object detection failed:', error); return { objects: [], confidence: 0, error: error.message }; } } /** * Analyze faces and emotions in image */ async analyzeFaces(imagePath, options = {}) { try { if (!this.faceApiModelsLoaded) { return { faces: [], emotions: [], confidence: 0, provider: 'none' }; } // Load image for face-api.js const img = await canvas.loadImage(imagePath); const canvas = faceapi.createCanvasFromMedia(img); // Detect faces with expressions const detections = await faceapi .detectAllFaces(img, new faceapi.TinyFaceDetectorOptions()) .withFaceLandmarks() .withFaceExpressions(); const faces = detections.map((detection, index) => { const expressions = detection.expressions; const dominantExpression = Object.keys(expressions).reduce((a, b) => expressions[a] > expressions[b] ? a : b ); return { id: index, boundingBox: detection.detection.box, landmarks: detection.landmarks.positions, expressions: expressions, dominantEmotion: dominantExpression, confidence: expressions[dominantExpression], age: null, // Would require additional model gender: null // Would require additional model }; }); // Aggregate emotions across all faces const aggregatedEmotions = this.aggregateFaceEmotions(faces); return { faces, totalFaces: faces.length, emotions: aggregatedEmotions, dominantEmotion: aggregatedEmotions.length > 0 ? aggregatedEmotions[0] : null, confidence: faces.length > 0 ? faces.reduce((sum, face) => sum + face.confidence, 0) / faces.length : 0, provider: 'face-api' }; } catch (error) { logger.error('[FAILED] Face analysis failed:', error); return { faces: [], emotions: [], confidence: 0, error: error.message }; } } /** * Recognize Free Mobile products in image */ async recognizeProducts(imagePath, options = {}) { try { // First, get OCR text to look for product names const ocrResult = await this.performOCR(imagePath, options); const text = ocrResult.text.toLowerCase(); // Then, get object detection results const objectResult = await this.detectObjects(imagePath, options); const objects = objectResult.objects || []; const recognizedProducts = []; // Check OCR text for product mentions Object.keys(this.productCategories).forEach(category => { this.productCategories[category].forEach(product => { if (text.includes(product.toLowerCase())) { recognizedProducts.push({ name: product, category, confidence: 0.8, source: 'ocr', context: this.extractProductContext(text, product) }); } }); }); // Check detected objects for products objects.forEach(obj => { const category = this.categorizeObject(obj.name); if (category !== 'other') { recognizedProducts.push({ name: obj.name, category, confidence: obj.confidence, source: 'object_detection', boundingBox: obj.boundingBox }); } }); // Remove duplicates and sort by confidence const uniqueProducts = this.deduplicateProducts(recognizedProducts); uniqueProducts.sort((a, b) => b.confidence - a.confidence); return { products: uniqueProducts, totalProducts: uniqueProducts.length, categories: this.groupProductsByCategory(uniqueProducts), confidence: uniqueProducts.length > 0 ? uniqueProducts.reduce((sum, p) => sum + p.confidence, 0) / uniqueProducts.length : 0, suggestions: this.generateProductSuggestions(uniqueProducts) }; } catch (error) { logger.error('[FAILED] Product recognition failed:', error); return { products: [], confidence: 0, error: error.message }; } } /** * Assess image quality */ async assessImageQuality(imagePath, metadata) { try { const image = sharp(imagePath); const stats = await image.stats(); let qualityScore = 100; const issues = []; // Check resolution if (metadata.width < 640 || metadata.height < 480) { qualityScore -= 20; issues.push('Low resolution'); } // Check if image is too dark or too bright const brightness = stats.channels.reduce((sum, channel) => sum + channel.mean, 0) / stats.channels.length; if (brightness < 50) { qualityScore -= 15; issues.push('Image too dark'); } else if (brightness > 200) { qualityScore -= 10; issues.push('Image too bright'); } // Check for blur (simplified) const sharpness = await this.calculateSharpness(imagePath); if (sharpness < 0.3) { qualityScore -= 25; issues.push('Image appears blurry'); } // Check file size vs resolution ratio const compressionRatio = metadata.size / (metadata.width * metadata.height); if (compressionRatio < 0.1) { qualityScore -= 10; issues.push('High compression artifacts'); } return { score: Math.max(0, qualityScore), rating: qualityScore >= 80 ? 'excellent' : qualityScore >= 60 ? 'good' : qualityScore >= 40 ? 'fair' : 'poor', issues, recommendations: this.generateQualityRecommendations(issues), technicalDetails: { brightness, sharpness, compressionRatio, ...metadata } }; } catch (error) { logger.error('[FAILED] Image quality assessment failed:', error); return { score: 50, rating: 'unknown', issues: ['Assessment failed'] }; } } /** * Enhance image quality */ async enhanceImageQuality(imagePath, options = {}) { try { if (!options.enhance) { return imagePath; } const enhancedPath = imagePath.replace(/(\.[^.]+)$/, '_enhanced$1'); await sharp(imagePath) .normalize() // Auto-adjust contrast .sharpen() // Enhance sharpness .gamma(1.2) // Slight gamma correction .toFile(enhancedPath); return enhancedPath; } catch (error) { logger.warn('Image enhancement failed, using original:', error.message); return imagePath; } } /** * Perform advanced image analysis using Google Cloud Vision */ async performAdvancedImageAnalysis(imagePath) { try { if (!this.googleVisionClient) { return { error: 'Google Cloud Vision not configured' }; } const imageBuffer = await fs.readFile(imagePath); const [result] = await this.googleVisionClient.annotateImage({ image: { content: imageBuffer }, features: [ { type: 'LABEL_DETECTION', maxResults: 10 }, { type: 'SAFE_SEARCH_DETECTION' }, { type: 'IMAGE_PROPERTIES' }, { type: 'WEB_DETECTION' } ] }); return { labels: result.labelAnnotations || [], safeSearch: result.safeSearchAnnotation, colors: result.imagePropertiesAnnotation?.dominantColors?.colors || [], webDetection: result.webDetection, provider: 'google_advanced' }; } catch (error) { logger.error('[FAILED] Advanced image analysis failed:', error); return { error: 'Advanced analysis unavailable' }; } } /** * Helper methods */ async saveImageBuffer(buffer, id) { const filename = `${id}.jpg`; const filepath = path.join(__dirname, '../../temp/images', filename); await fs.writeFile(filepath, buffer); return filepath; } async getImageMetadata(imagePath) { const image = sharp(imagePath); const metadata = await image.metadata(); const stats = await fs.stat(imagePath); return { width: metadata.width, height: metadata.height, format: metadata.format, size: stats.size, density: metadata.density, hasAlpha: metadata.hasAlpha, channels: metadata.channels }; } async calculateSharpness(imagePath) { // Simplified sharpness calculation using Laplacian variance try { const { data, info } = await sharp(imagePath) .greyscale() .raw() .toBuffer({ resolveWithObject: true }); // Apply Laplacian kernel (simplified) let variance = 0; const width = info.width; const height = info.height; for (let y = 1; y < height - 1; y++) { for (let x = 1; x < width - 1; x++) { const idx = y * width + x; const laplacian = -4 * data[idx] + data[idx - 1] + data[idx + 1] + data[idx - width] + data[idx + width]; variance += laplacian * laplacian; } } return variance / ((width - 2) * (height - 2) * 255 * 255); } catch (error) { return 0.5; // Default moderate sharpness } } categorizeObject(objectName) { const name = objectName.toLowerCase(); for (const [category, keywords] of Object.entries(this.productCategories)) { if (keywords.some(keyword => name.includes(keyword))) { return category; } } return 'other'; } extractProductContext(text, product) { const sentences = text.split(/[.!?]+/); const relevantSentences = sentences.filter(sentence => sentence.toLowerCase().includes(product.toLowerCase()) ); return relevantSentences.slice(0, 2).join('. '); } deduplicateWords(words) { const seen = new Set(); return words.filter(word => { const key = `${word.text}_${Math.round(word.boundingBox?.x || 0)}`; if (seen.has(key)) return false; seen.add(key); return true; }); } deduplicateProducts(products) { const seen = new Set(); return products.filter(product => { const key = `${product.name}_${product.category}`; if (seen.has(key)) return false; seen.add(key); return true; }); } groupObjectsByCategory(objects) { const categories = {}; objects.forEach(obj => { const category = this.categorizeObject(obj.name); if (!categories[category]) categories[category] = []; categories[category].push(obj); }); return categories; } groupProductsByCategory(products) { const categories = {}; products.forEach(product => { if (!categories[product.category]) categories[product.category] = []; categories[product.category].push(product); }); return categories; } aggregateFaceEmotions(faces) { if (faces.length === 0) return []; const emotionTotals = {}; faces.forEach(face => { Object.keys(face.expressions).forEach(emotion => { emotionTotals[emotion] = (emotionTotals[emotion] || 0) + face.expressions[emotion]; }); }); return Object.entries(emotionTotals) .map(([emotion, total]) => ({ emotion, confidence: total / faces.length })) .sort((a, b) => b.confidence - a.confidence); } generateProductSuggestions(products) { const suggestions = []; if (products.some(p => p.category === 'smartphones')) { suggestions.push('Consider smartphone accessories like cases or chargers'); } if (products.some(p => p.category === 'boxes')) { suggestions.push('Check for related internet service issues'); } return suggestions; } generateQualityRecommendations(issues) { const recommendations = []; if (issues.includes('Low resolution')) { recommendations.push('Use a higher resolution camera or reduce distance to subject'); } if (issues.includes('Image too dark')) { recommendations.push('Improve lighting conditions or increase exposure'); } if (issues.includes('Image appears blurry')) { recommendations.push('Hold camera steady or use image stabilization'); } if (issues.includes('High compression artifacts')) { recommendations.push('Use higher quality image settings'); } return recommendations; } async cleanupTempFiles(filePaths) { await Promise.all(filePaths.map(async (filepath) => { try { await fs.unlink(filepath); } catch (error) { logger.warn('Failed to cleanup temp file:', filepath); } })); } /** * Health check */ isHealthy() { return this.isInitialized; } /** * Cleanup resources */ async cleanup() { try { // Cleanup temporary files const tempDir = path.join(__dirname, '../../temp/images'); try { const files = await fs.readdir(tempDir); await Promise.all(files.map(file => fs.unlink(path.join(tempDir, file)).catch(() => {}) )); } catch (error) { // Directory might not exist } this.isInitialized = false; logger.info('[COMPLETE] Image Processing Service cleanup completed'); } catch (error) { logger.error('[FAILED] Image Processing Service cleanup failed:', error); throw error; } } } module.exports = ImageProcessingService;