/** * ============================================= * VOICE PROCESSING SERVICE * Advanced voice processing with speech-to-text, text-to-speech, and emotion analysis * Supports Google Cloud Speech API, Azure Cognitive Services, and real-time processing * ============================================= */ const speech = require('@google-cloud/speech'); const textToSpeech = require('@google-cloud/text-to-speech'); const sdk = require('microsoft-cognitiveservices-speech-sdk'); const fs = require('fs').promises; const path = require('path'); const { v4: uuidv4 } = require('uuid'); const logger = require('../utils/logger'); class VoiceProcessingService { constructor() { this.isInitialized = false; this.googleSpeechClient = null; this.googleTTSClient = null; this.azureSpeechConfig = null; this.azureAudioConfig = null; // Supported audio formats this.supportedFormats = ['mp3', 'wav', 'ogg', 'webm', 'm4a', 'flac']; // Voice emotion patterns (frequency and tone analysis) this.emotionPatterns = { happy: { pitch: 'high', energy: 'high', tempo: 'fast' }, sad: { pitch: 'low', energy: 'low', tempo: 'slow' }, angry: { pitch: 'high', energy: 'high', tempo: 'fast', intensity: 'high' }, calm: { pitch: 'medium', energy: 'low', tempo: 'medium' }, excited: { pitch: 'high', energy: 'high', tempo: 'fast' }, frustrated: { pitch: 'medium-high', energy: 'medium-high', tempo: 'irregular' } }; // French language voice configurations this.frenchVoices = { female: { standard: 'fr-FR-Standard-A', neural: 'fr-FR-Neural2-A', wavenet: 'fr-FR-Wavenet-A' }, male: { standard: 'fr-FR-Standard-B', neural: 'fr-FR-Neural2-B', wavenet: 'fr-FR-Wavenet-B' } }; } /** * Initialize Voice Processing Service */ async initialize() { try { // Initialize Google Cloud Speech-to-Text if (process.env.GOOGLE_APPLICATION_CREDENTIALS) { this.googleSpeechClient = new speech.SpeechClient(); this.googleTTSClient = new textToSpeech.TextToSpeechClient(); logger.info('[COMPLETE] Google Cloud Speech services initialized'); } // Initialize Azure Cognitive Services if (process.env.AZURE_SPEECH_KEY && process.env.AZURE_SPEECH_REGION) { this.azureSpeechConfig = sdk.SpeechConfig.fromSubscription( process.env.AZURE_SPEECH_KEY, process.env.AZURE_SPEECH_REGION ); this.azureSpeechConfig.speechRecognitionLanguage = 'fr-FR'; this.azureSpeechConfig.speechSynthesisLanguage = 'fr-FR'; this.azureSpeechConfig.speechSynthesisVoiceName = 'fr-FR-DeniseNeural'; logger.info('[COMPLETE] Azure Cognitive Services initialized'); } // Create temp directory for audio processing await this.ensureTempDirectory(); this.isInitialized = true; logger.info('[COMPLETE] Voice Processing Service initialized successfully'); } catch (error) { logger.error('[FAILED] Voice Processing Service initialization failed:', error); throw error; } } /** * Ensure temporary directory exists for audio processing */ async ensureTempDirectory() { const tempDir = path.join(__dirname, '../../temp/audio'); try { await fs.mkdir(tempDir, { recursive: true }); } catch (error) { if (error.code !== 'EEXIST') { throw error; } } } /** * Process voice input with comprehensive analysis */ async processVoice(audioBuffer, options = {}) { try { if (!audioBuffer || !Buffer.isBuffer(audioBuffer)) { throw new Error('Invalid audio buffer provided'); } const processingId = uuidv4(); const startTime = Date.now(); logger.info(` Starting voice processing: ${processingId}`); // Save audio buffer to temporary file const tempFilePath = await this.saveAudioBuffer(audioBuffer, processingId, options.format || 'wav'); // Perform parallel processing const [ transcriptionResult, emotionAnalysis, audioQuality, voiceActivity ] = await Promise.allSettled([ this.transcribeAudio(tempFilePath, options), this.analyzeVoiceEmotion(tempFilePath, options), this.assessAudioQuality(tempFilePath), this.detectVoiceActivity(tempFilePath) ]); // Compile results const result = { processingId, processedAt: new Date().toISOString(), processingTime: Date.now() - startTime, transcription: transcriptionResult.status === 'fulfilled' ? transcriptionResult.value : null, emotion: emotionAnalysis.status === 'fulfilled' ? emotionAnalysis.value : null, audioQuality: audioQuality.status === 'fulfilled' ? audioQuality.value : null, voiceActivity: voiceActivity.status === 'fulfilled' ? voiceActivity.value : null, metadata: { fileSize: audioBuffer.length, format: options.format || 'wav', duration: await this.getAudioDuration(tempFilePath), sampleRate: options.sampleRate || 16000 } }; // Enhanced analysis if requested if (options.enhancedAnalysis) { result.enhancedAnalysis = await this.performEnhancedVoiceAnalysis(tempFilePath, result); } // Cleanup temporary file await this.cleanupTempFile(tempFilePath); logger.info(`[COMPLETE] Voice processing completed: ${processingId} (${result.processingTime}ms)`); return result; } catch (error) { logger.error('[FAILED] Voice processing failed:', error); throw error; } } /** * Real-time voice stream processing */ async processVoiceStream(audioData, options = {}) { try { const streamId = uuidv4(); logger.info(` Processing voice stream: ${streamId}`); // Convert audio data to buffer if needed const audioBuffer = Buffer.isBuffer(audioData) ? audioData : Buffer.from(audioData); // Quick transcription for real-time feedback const quickTranscription = await this.quickTranscribe(audioBuffer, options); // Basic emotion detection const basicEmotion = await this.quickEmotionAnalysis(audioBuffer); return { streamId, timestamp: new Date().toISOString(), transcription: quickTranscription, emotion: basicEmotion, confidence: quickTranscription.confidence || 0.8, isPartial: true }; } catch (error) { logger.error('[FAILED] Voice stream processing failed:', error); throw error; } } /** * Transcribe audio using Google Cloud Speech API */ async transcribeAudio(audioFilePath, options = {}) { try { if (!this.googleSpeechClient) { return await this.transcribeWithAzure(audioFilePath, options); } const audioBytes = await fs.readFile(audioFilePath); const request = { audio: { content: audioBytes.toString('base64') }, config: { encoding: this.getGoogleAudioEncoding(options.format || 'wav'), sampleRateHertz: options.sampleRate || 16000, languageCode: options.language || 'fr-FR', alternativeLanguageCodes: ['en-US', 'es-ES'], enableAutomaticPunctuation: true, enableWordTimeOffsets: true, enableWordConfidence: true, model: 'latest_long', useEnhanced: true, profanityFilter: false, speechContexts: [{ phrases: [ 'Free Mobile', 'forfait', 'facture', 'abonnement', 'réseau', 'internet', 'mobile', 'téléphone', 'support', 'assistance' ] }] } }; const [response] = await this.googleSpeechClient.recognize(request); const transcription = response.results .map(result => result.alternatives[0]) .filter(alternative => alternative.confidence > 0.5); return { text: transcription.map(alt => alt.transcript).join(' '), confidence: transcription.length > 0 ? transcription.reduce((sum, alt) => sum + alt.confidence, 0) / transcription.length : 0, words: transcription.flatMap(alt => alt.words || []), language: options.language || 'fr-FR', provider: 'google', alternatives: response.results.map(result => result.alternatives.slice(1, 3).map(alt => ({ text: alt.transcript, confidence: alt.confidence })) ).flat() }; } catch (error) { logger.error('[FAILED] Google Speech transcription failed:', error); // Fallback to Azure if available if (this.azureSpeechConfig) { return await this.transcribeWithAzure(audioFilePath, options); } throw error; } } /** * Transcribe audio using Azure Cognitive Services */ async transcribeWithAzure(audioFilePath, options = {}) { try { if (!this.azureSpeechConfig) { throw new Error('Azure Speech services not configured'); } return new Promise((resolve, reject) => { const audioConfig = sdk.AudioConfig.fromWavFileInput(audioFilePath); const recognizer = new sdk.SpeechRecognizer(this.azureSpeechConfig, audioConfig); let fullText = ''; const words = []; recognizer.recognized = (s, e) => { if (e.result.reason === sdk.ResultReason.RecognizedSpeech) { fullText += e.result.text + ' '; // Parse word-level information if available if (e.result.json) { const parsed = JSON.parse(e.result.json); if (parsed.NBest && parsed.NBest[0] && parsed.NBest[0].Words) { words.push(...parsed.NBest[0].Words); } } } }; recognizer.canceled = (s, e) => { recognizer.close(); reject(new Error(`Azure Speech recognition canceled: ${e.errorDetails}`)); }; recognizer.sessionStopped = (s, e) => { recognizer.close(); resolve({ text: fullText.trim(), confidence: 0.85, // Azure doesn't provide detailed confidence words: words, language: options.language || 'fr-FR', provider: 'azure', alternatives: [] }); }; recognizer.startContinuousRecognitionAsync(); }); } catch (error) { logger.error('[FAILED] Azure Speech transcription failed:', error); throw error; } } /** * Quick transcription for real-time processing */ async quickTranscribe(audioBuffer, options = {}) { try { // Use a simplified transcription approach for speed const tempFile = await this.saveAudioBuffer(audioBuffer, 'quick-' + uuidv4(), 'wav'); // Use Google's streaming API if available, otherwise fallback to batch const result = await this.transcribeAudio(tempFile, { ...options, model: 'latest_short' // Optimized for short audio }); await this.cleanupTempFile(tempFile); return result; } catch (error) { logger.error('[FAILED] Quick transcription failed:', error); return { text: '', confidence: 0, provider: 'fallback' }; } } /** * Analyze voice emotion from audio patterns */ async analyzeVoiceEmotion(audioFilePath, options = {}) { try { // This is a simplified emotion analysis based on audio characteristics // In production, you would use specialized emotion recognition APIs const audioStats = await this.analyzeAudioCharacteristics(audioFilePath); const emotions = { happy: this.calculateEmotionScore(audioStats, this.emotionPatterns.happy), sad: this.calculateEmotionScore(audioStats, this.emotionPatterns.sad), angry: this.calculateEmotionScore(audioStats, this.emotionPatterns.angry), calm: this.calculateEmotionScore(audioStats, this.emotionPatterns.calm), excited: this.calculateEmotionScore(audioStats, this.emotionPatterns.excited), frustrated: this.calculateEmotionScore(audioStats, this.emotionPatterns.frustrated) }; // Find dominant emotion const dominantEmotion = Object.entries(emotions) .sort(([,a], [,b]) => b - a)[0]; return { emotions, dominant: dominantEmotion[0], confidence: dominantEmotion[1], audioCharacteristics: audioStats, analysis: { pitch: audioStats.averagePitch > 200 ? 'high' : audioStats.averagePitch < 150 ? 'low' : 'medium', energy: audioStats.energy > 0.7 ? 'high' : audioStats.energy < 0.3 ? 'low' : 'medium', tempo: audioStats.tempo > 1.2 ? 'fast' : audioStats.tempo < 0.8 ? 'slow' : 'medium' } }; } catch (error) { logger.error('[FAILED] Voice emotion analysis failed:', error); return { emotions: {}, dominant: 'neutral', confidence: 0, error: error.message }; } } /** * Quick emotion analysis for real-time processing */ async quickEmotionAnalysis(audioBuffer) { try { // Simplified emotion detection based on basic audio properties const energy = this.calculateAudioEnergy(audioBuffer); const pitch = this.estimatePitch(audioBuffer); let emotion = 'neutral'; let confidence = 0.6; if (energy > 0.8 && pitch > 200) { emotion = 'excited'; confidence = 0.7; } else if (energy < 0.3 && pitch < 150) { emotion = 'sad'; confidence = 0.65; } else if (energy > 0.7 && pitch > 180) { emotion = 'happy'; confidence = 0.68; } return { emotion, confidence, method: 'quick' }; } catch (error) { logger.error('[FAILED] Quick emotion analysis failed:', error); return { emotion: 'neutral', confidence: 0, method: 'fallback' }; } } /** * Convert text to speech with emotion-aware synthesis */ async synthesizeSpeech(text, options = {}) { try { const { language = 'fr-FR', gender = 'female', emotion = 'neutral', speed = 1.0, pitch = 0, volume = 0 } = options; if (!this.googleTTSClient) { return await this.synthesizeWithAzure(text, options); } // Select appropriate voice based on emotion and gender const voiceName = this.selectVoiceForEmotion(gender, emotion); const request = { input: { text }, voice: { languageCode: language, name: voiceName, ssmlGender: gender.toUpperCase() }, audioConfig: { audioEncoding: 'MP3', speakingRate: speed, pitch: pitch, volumeGainDb: volume, effectsProfileId: ['telephony-class-application'] } }; // Add SSML for emotion if supported if (emotion !== 'neutral') { request.input = { ssml: this.generateEmotionalSSML(text, emotion) }; } const [response] = await this.googleTTSClient.synthesizeSpeech(request); return { audioContent: response.audioContent, format: 'mp3', voice: voiceName, emotion, language, metadata: { textLength: text.length, estimatedDuration: this.estimateSpeechDuration(text, speed), synthesizedAt: new Date().toISOString() } }; } catch (error) { logger.error('[FAILED] Speech synthesis failed:', error); // Fallback to Azure if available if (this.azureSpeechConfig) { return await this.synthesizeWithAzure(text, options); } throw error; } } /** * Synthesize speech using Azure Cognitive Services */ async synthesizeWithAzure(text, options = {}) { try { if (!this.azureSpeechConfig) { throw new Error('Azure Speech services not configured'); } const synthesizer = new sdk.SpeechSynthesizer(this.azureSpeechConfig); return new Promise((resolve, reject) => { synthesizer.speakTextAsync( text, result => { if (result.reason === sdk.ResultReason.SynthesizingAudioCompleted) { resolve({ audioContent: Buffer.from(result.audioData), format: 'wav', voice: 'fr-FR-DeniseNeural', emotion: options.emotion || 'neutral', language: options.language || 'fr-FR', provider: 'azure' }); } else { reject(new Error(`Azure TTS failed: ${result.errorDetails}`)); } synthesizer.close(); }, error => { synthesizer.close(); reject(error); } ); }); } catch (error) { logger.error('[FAILED] Azure speech synthesis failed:', error); throw error; } } /** * Assess audio quality */ async assessAudioQuality(audioFilePath) { try { const stats = await this.analyzeAudioCharacteristics(audioFilePath); let qualityScore = 100; const issues = []; // Check sample rate if (stats.sampleRate < 16000) { qualityScore -= 20; issues.push('Low sample rate'); } // Check signal-to-noise ratio if (stats.snr < 10) { qualityScore -= 30; issues.push('High background noise'); } // Check clipping if (stats.clipping > 0.01) { qualityScore -= 25; issues.push('Audio clipping detected'); } // Check volume levels if (stats.averageVolume < 0.1) { qualityScore -= 15; issues.push('Low volume'); } else if (stats.averageVolume > 0.9) { qualityScore -= 10; issues.push('Volume too high'); } return { score: Math.max(0, qualityScore), rating: qualityScore >= 80 ? 'excellent' : qualityScore >= 60 ? 'good' : qualityScore >= 40 ? 'fair' : 'poor', issues, recommendations: this.generateQualityRecommendations(issues), technicalDetails: stats }; } catch (error) { logger.error('[FAILED] Audio quality assessment failed:', error); return { score: 50, rating: 'unknown', issues: ['Assessment failed'] }; } } /** * Detect voice activity in audio */ async detectVoiceActivity(audioFilePath) { try { const stats = await this.analyzeAudioCharacteristics(audioFilePath); // Simple voice activity detection based on energy levels const energyThreshold = 0.02; const voiceSegments = []; // This is a simplified implementation // In production, use specialized VAD algorithms return { hasVoice: stats.energy > energyThreshold, voicePercentage: Math.min(100, stats.energy * 100), segments: voiceSegments, totalDuration: stats.duration, voiceDuration: stats.duration * Math.min(1, stats.energy * 2), silenceDuration: stats.duration * (1 - Math.min(1, stats.energy * 2)), confidence: stats.energy > energyThreshold ? 0.8 : 0.3 }; } catch (error) { logger.error('[FAILED] Voice activity detection failed:', error); return { hasVoice: false, confidence: 0 }; } } /** * Helper methods */ async saveAudioBuffer(buffer, id, format) { const filename = `${id}.${format}`; const filepath = path.join(__dirname, '../../temp/audio', filename); await fs.writeFile(filepath, buffer); return filepath; } async cleanupTempFile(filepath) { try { await fs.unlink(filepath); } catch (error) { logger.warn('Failed to cleanup temp file:', filepath); } } getGoogleAudioEncoding(format) { const encodingMap = { 'wav': 'LINEAR16', 'mp3': 'MP3', 'ogg': 'OGG_OPUS', 'flac': 'FLAC', 'webm': 'WEBM_OPUS' }; return encodingMap[format.toLowerCase()] || 'LINEAR16'; } selectVoiceForEmotion(gender, emotion) { const voices = this.frenchVoices[gender] || this.frenchVoices.female; // Select voice based on emotion switch (emotion) { case 'happy': case 'excited': return voices.neural || voices.wavenet || voices.standard; case 'calm': case 'professional': return voices.standard; default: return voices.neural || voices.standard; } } generateEmotionalSSML(text, emotion) { const emotionMap = { happy: '<prosody rate="medium" pitch="+2st" volume="medium">', sad: '<prosody rate="slow" pitch="-2st" volume="soft">', excited: '<prosody rate="fast" pitch="+3st" volume="loud">', calm: '<prosody rate="slow" pitch="medium" volume="medium">' }; const prosodyStart = emotionMap[emotion] || '<prosody>'; return `<speak>${prosodyStart}${text}</prosody></speak>`; } estimateSpeechDuration(text, speed = 1.0) { // Rough estimation: ~150 words per minute for French const words = text.split(/\s+/).length; const baseMinutes = words / 150; return Math.round((baseMinutes * 60 * 1000) / speed); // Return in milliseconds } // Simplified audio analysis methods (would use proper audio processing libraries in production) async analyzeAudioCharacteristics(audioFilePath) { // This is a placeholder implementation // In production, use libraries like node-ffmpeg, audio-buffer-utils, etc. return { duration: 5000, // milliseconds sampleRate: 16000, averagePitch: 180, energy: 0.6, tempo: 1.0, averageVolume: 0.5, snr: 15, clipping: 0.001 }; } calculateEmotionScore(audioStats, emotionPattern) { // Simplified emotion scoring let score = 0.5; if (emotionPattern.pitch === 'high' && audioStats.averagePitch > 200) score += 0.2; if (emotionPattern.energy === 'high' && audioStats.energy > 0.7) score += 0.2; if (emotionPattern.tempo === 'fast' && audioStats.tempo > 1.2) score += 0.1; return Math.min(1.0, score); } calculateAudioEnergy(audioBuffer) { // Simplified energy calculation let sum = 0; for (let i = 0; i < audioBuffer.length; i += 2) { const sample = audioBuffer.readInt16LE(i); sum += sample * sample; } return Math.sqrt(sum / (audioBuffer.length / 2)) / 32768; } estimatePitch(audioBuffer) { // Very simplified pitch estimation return 180 + (Math.random() - 0.5) * 60; } async getAudioDuration(audioFilePath) { // Placeholder - would use proper audio analysis return 5000; // milliseconds } generateQualityRecommendations(issues) { const recommendations = []; if (issues.includes('Low sample rate')) { recommendations.push('Use at least 16kHz sample rate for better quality'); } if (issues.includes('High background noise')) { recommendations.push('Record in a quieter environment or use noise reduction'); } if (issues.includes('Audio clipping')) { recommendations.push('Reduce input volume to prevent distortion'); } if (issues.includes('Low volume')) { recommendations.push('Speak closer to the microphone or increase input gain'); } return recommendations; } async performEnhancedVoiceAnalysis(audioFilePath, basicResult) { // Enhanced analysis combining multiple results return { combinedConfidence: (basicResult.transcription?.confidence || 0) * 0.7 + (basicResult.emotion?.confidence || 0) * 0.3, qualityAdjustedResults: { transcription: basicResult.transcription, emotion: basicResult.emotion, qualityImpact: basicResult.audioQuality?.score < 60 ? 'significant' : 'minimal' }, recommendations: this.generateProcessingRecommendations(basicResult) }; } generateProcessingRecommendations(result) { const recommendations = []; if (result.transcription?.confidence < 0.7) { recommendations.push('Consider re-recording for better transcription accuracy'); } if (result.audioQuality?.score < 60) { recommendations.push('Improve audio quality for better analysis results'); } if (result.emotion?.confidence < 0.6) { recommendations.push('Emotion detection may be unreliable due to audio quality'); } return recommendations; } /** * Health check */ isHealthy() { return this.isInitialized; } /** * Cleanup resources */ async cleanup() { try { // Cleanup temporary files const tempDir = path.join(__dirname, '../../temp/audio'); try { const files = await fs.readdir(tempDir); await Promise.all(files.map(file => fs.unlink(path.join(tempDir, file)).catch(() => {}) )); } catch (error) { // Directory might not exist } this.isInitialized = false; logger.info('[COMPLETE] Voice Processing Service cleanup completed'); } catch (error) { logger.error('[FAILED] Voice Processing Service cleanup failed:', error); throw error; } } } module.exports = VoiceProcessingService;