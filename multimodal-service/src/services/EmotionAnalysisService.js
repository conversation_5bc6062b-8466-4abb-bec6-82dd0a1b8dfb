/** * ============================================= * EMOTION ANALYSIS SERVICE * Cross-modal emotion detection and analysis * Combines text sentiment, voice emotion, and facial expression analysis * ============================================= */ const logger = require('../utils/logger'); const { v4: uuidv4 } = require('uuid'); class EmotionAnalysisService { constructor() { this.isInitialized = false; // Emotion categories and mappings this.emotionCategories = { primary: ['happy', 'sad', 'angry', 'fearful', 'surprised', 'disgusted', 'neutral'], secondary: ['excited', 'frustrated', 'calm', 'anxious', 'content', 'disappointed', 'confused'], customer_service: ['satisfied', 'dissatisfied', 'impatient', 'grateful', 'concerned', 'hopeful'] }; // Emotion intensity levels this.intensityLevels = { very_low: { min: 0.0, max: 0.2 }, low: { min: 0.2, max: 0.4 }, medium: { min: 0.4, max: 0.6 }, high: { min: 0.6, max: 0.8 }, very_high: { min: 0.8, max: 1.0 } }; // Cross-modal emotion consistency rules this.consistencyRules = { text_voice: { consistent: [ { text: 'positive', voice: 'happy' }, { text: 'negative', voice: 'angry' }, { text: 'negative', voice: 'sad' }, { text: 'neutral', voice: 'calm' } ], weight: 0.4 }, text_image: { consistent: [ { text: 'positive', image: 'happy' }, { text: 'negative', image: 'angry' }, { text: 'negative', image: 'sad' } ], weight: 0.3 }, voice_image: { consistent: [ { voice: 'happy', image: 'happy' }, { voice: 'angry', image: 'angry' }, { voice: 'sad', image: 'sad' } ], weight: 0.3 } }; // Customer service emotion impact mapping this.customerServiceImpact = { positive: { emotions: ['happy', 'satisfied', 'grateful', 'content'], impact: 'positive', response_strategy: 'maintain_satisfaction', priority: 'normal' }, negative: { emotions: ['angry', 'frustrated', 'dissatisfied', 'disappointed'], impact: 'negative', response_strategy: 'de_escalation', priority: 'high' }, neutral: { emotions: ['neutral', 'calm', 'confused'], impact: 'neutral', response_strategy: 'information_provision', priority: 'normal' }, urgent: { emotions: ['fearful', 'anxious', 'impatient'], impact: 'urgent', response_strategy: 'immediate_assistance', priority: 'urgent' } }; // Temporal emotion patterns this.temporalPatterns = { escalation: ['neutral', 'concerned', 'frustrated', 'angry'], de_escalation: ['angry', 'frustrated', 'concerned', 'calm'], satisfaction_journey: ['confused', 'hopeful', 'content', 'satisfied'] }; } /** * Initialize Emotion Analysis Service */ async initialize() { try { // Load emotion models and configurations await this.loadEmotionModels(); this.isInitialized = true; logger.info('[COMPLETE] Emotion Analysis Service initialized successfully'); } catch (error) { logger.error('[FAILED] Emotion Analysis Service initialization failed:', error); throw error; } } /** * Load emotion analysis models */ async loadEmotionModels() { try { // In production, this would load pre-trained emotion models // For now, we use rule-based and statistical approaches logger.info('[COMPLETE] Emotion models loaded successfully'); } catch (error) { logger.error('[FAILED] Failed to load emotion models:', error); throw error; } } /** * Analyze emotion from multimodal content */ async analyzeEmotion(content, type, options = {}) { try { const analysisId = uuidv4(); const startTime = Date.now(); logger.info(` Starting emotion analysis: ${analysisId} (${type})`); let result; switch (type) { case 'text': result = await this.analyzeTextEmotion(content, options); break; case 'voice': result = await this.analyzeVoiceEmotion(content, options); break; case 'image': result = await this.analyzeImageEmotion(content, options); break; case 'multimodal': result = await this.analyzeMultimodalEmotion(content, options); break; default: throw new Error(`Unsupported emotion analysis type: ${type}`); } // Enhance with customer service context const enhancedResult = await this.enhanceWithCustomerServiceContext(result, options); // Add temporal analysis if history is provided if (options.emotionHistory) { enhancedResult.temporalAnalysis = this.analyzeTemporalPatterns(options.emotionHistory, result); } const finalResult = { analysisId, processedAt: new Date().toISOString(), processingTime: Date.now() - startTime, type, ...enhancedResult }; logger.info(`[COMPLETE] Emotion analysis completed: ${analysisId} (${finalResult.processingTime}ms)`); return finalResult; } catch (error) { logger.error('[FAILED] Emotion analysis failed:', error); throw error; } } /** * Analyze emotion from text content */ async analyzeTextEmotion(textContent, options = {}) { try { // Use existing text processing results if available if (textContent.sentiment && textContent.emotions) { return this.processExistingTextEmotion(textContent); } // Fallback to basic text emotion analysis return this.performBasicTextEmotionAnalysis(textContent, options); } catch (error) { logger.error('[FAILED] Text emotion analysis failed:', error); return this.getDefaultEmotionResult('text'); } } /** * Analyze emotion from voice content */ async analyzeVoiceEmotion(voiceContent, options = {}) { try { // Use existing voice processing results if available if (voiceContent.emotion) { return this.processExistingVoiceEmotion(voiceContent); } // Fallback to basic voice emotion analysis return this.performBasicVoiceEmotionAnalysis(voiceContent, options); } catch (error) { logger.error('[FAILED] Voice emotion analysis failed:', error); return this.getDefaultEmotionResult('voice'); } } /** * Analyze emotion from image content */ async analyzeImageEmotion(imageContent, options = {}) { try { // Use existing image processing results if available if (imageContent.faces && imageContent.faces.emotions) { return this.processExistingImageEmotion(imageContent); } // Fallback to basic image emotion analysis return this.performBasicImageEmotionAnalysis(imageContent, options); } catch (error) { logger.error('[FAILED] Image emotion analysis failed:', error); return this.getDefaultEmotionResult('image'); } } /** * Analyze emotion from multimodal content */ async analyzeMultimodalEmotion(multimodalContent, options = {}) { try { const emotionResults = {}; // Analyze each modality if (multimodalContent.text) { emotionResults.text = await this.analyzeTextEmotion(multimodalContent.text, options); } if (multimodalContent.voice) { emotionResults.voice = await this.analyzeVoiceEmotion(multimodalContent.voice, options); } if (multimodalContent.image) { emotionResults.image = await this.analyzeImageEmotion(multimodalContent.image, options); } // Fuse emotions across modalities const fusedEmotion = this.fuseMultimodalEmotions(emotionResults); // Analyze cross-modal consistency const consistencyAnalysis = this.analyzeCrossModalConsistency(emotionResults); return { modalityResults: emotionResults, fusedEmotion, consistencyAnalysis, confidence: this.calculateMultimodalConfidence(emotionResults, consistencyAnalysis) }; } catch (error) { logger.error('[FAILED] Multimodal emotion analysis failed:', error); return this.getDefaultEmotionResult('multimodal'); } } /** * Process existing text emotion results */ processExistingTextEmotion(textContent) { const sentiment = textContent.sentiment || {}; const emotions = textContent.emotions || {}; // Map sentiment to emotion const primaryEmotion = this.sentimentToEmotion(sentiment.label, sentiment.score); // Combine with detected emotions const combinedEmotions = { primary: emotions.dominant || primaryEmotion.emotion, confidence: Math.max(sentiment.confidence || 0, emotions.intensity || 0, primaryEmotion.confidence), intensity: this.calculateIntensity(emotions.intensity || Math.abs(sentiment.score || 0)), valence: sentiment.score || 0, arousal: this.calculateArousal(emotions.dominant || primaryEmotion.emotion) }; return { emotions: combinedEmotions, sentiment: sentiment, source: 'text_processing', rawEmotions: emotions }; } /** * Process existing voice emotion results */ processExistingVoiceEmotion(voiceContent) { const emotion = voiceContent.emotion || {}; const audioCharacteristics = emotion.audioCharacteristics || {}; return { emotions: { primary: emotion.dominant || 'neutral', confidence: emotion.confidence || 0.5, intensity: this.calculateIntensity(emotion.confidence || 0.5), valence: this.emotionToValence(emotion.dominant || 'neutral'), arousal: audioCharacteristics.energy || 0.5 }, audioFeatures: { pitch: audioCharacteristics.averagePitch, energy: audioCharacteristics.energy, tempo: audioCharacteristics.tempo }, source: 'voice_processing', rawEmotions: emotion.emotions || {} }; } /** * Process existing image emotion results */ processExistingImageEmotion(imageContent) { const faces = imageContent.faces || {}; const emotions = faces.emotions || []; if (emotions.length === 0) { return this.getDefaultEmotionResult('image'); } // Use dominant emotion from faces const dominantEmotion = emotions[0]; return { emotions: { primary: dominantEmotion.emotion || 'neutral', confidence: dominantEmotion.confidence || 0.5, intensity: this.calculateIntensity(dominantEmotion.confidence || 0.5), valence: this.emotionToValence(dominantEmotion.emotion || 'neutral'), arousal: 0.5 // Default arousal for image emotions }, faceCount: faces.totalFaces || 0, allFaceEmotions: emotions, source: 'image_processing' }; } /** * Fuse emotions from multiple modalities */ fuseMultimodalEmotions(emotionResults) { const modalities = Object.keys(emotionResults); if (modalities.length === 0) { return this.getDefaultEmotionResult('multimodal').emotions; } // Weight emotions by confidence and modality reliability const weightedEmotions = []; modalities.forEach(modality => { const result = emotionResults[modality]; if (result && result.emotions) { const weight = this.getModalityWeight(modality); weightedEmotions.push({ emotion: result.emotions.primary, confidence: result.emotions.confidence * weight, intensity: result.emotions.intensity, valence: result.emotions.valence, arousal: result.emotions.arousal, modality }); } }); // Find dominant emotion weightedEmotions.sort((a, b) => b.confidence - a.confidence); const dominantEmotion = weightedEmotions[0]; // Calculate fused metrics const avgValence = weightedEmotions.reduce((sum, e) => sum + (e.valence || 0), 0) / weightedEmotions.length; const avgArousal = weightedEmotions.reduce((sum, e) => sum + (e.arousal || 0), 0) / weightedEmotions.length; const avgIntensity = weightedEmotions.reduce((sum, e) => sum + (e.intensity || 0), 0) / weightedEmotions.length; return { primary: dominantEmotion.emotion, confidence: dominantEmotion.confidence, intensity: avgIntensity, valence: avgValence, arousal: avgArousal, contributingModalities: weightedEmotions.map(e => ({ modality: e.modality, emotion: e.emotion, confidence: e.confidence })) }; } /** * Analyze cross-modal consistency */ analyzeCrossModalConsistency(emotionResults) { const modalities = Object.keys(emotionResults); const consistencyScores = []; // Check pairwise consistency for (let i = 0; i < modalities.length; i++) { for (let j = i + 1; j < modalities.length; j++) { const mod1 = modalities[i]; const mod2 = modalities[j]; const pairKey = `${mod1}_${mod2}`; if (this.consistencyRules[pairKey]) { const score = this.calculatePairwiseConsistency( emotionResults[mod1], emotionResults[mod2], this.consistencyRules[pairKey] ); consistencyScores.push({ pair: pairKey, score, weight: this.consistencyRules[pairKey].weight }); } } } // Calculate overall consistency const overallConsistency = consistencyScores.length > 0 ? consistencyScores.reduce((sum, cs) => sum + (cs.score * cs.weight), 0) / consistencyScores.reduce((sum, cs) => sum + cs.weight, 0) : 0.5; return { overallConsistency, pairwiseScores: consistencyScores, interpretation: this.interpretConsistency(overallConsistency) }; } /** * Enhance with customer service context */ async enhanceWithCustomerServiceContext(emotionResult, options = {}) { try { const primaryEmotion = emotionResult.emotions?.primary || 'neutral'; // Find customer service impact const impact = this.findCustomerServiceImpact(primaryEmotion); // Generate response recommendations const responseRecommendations = this.generateResponseRecommendations(emotionResult, impact); // Assess escalation risk const escalationRisk = this.assessEscalationRisk(emotionResult, options); return { ...emotionResult, customerServiceContext: { impact: impact.impact, responseStrategy: impact.response_strategy, priority: impact.priority, escalationRisk, responseRecommendations } }; } catch (error) { logger.error('[FAILED] Customer service context enhancement failed:', error); return emotionResult; } } /** * Analyze temporal emotion patterns */ analyzeTemporalPatterns(emotionHistory, currentEmotion) { try { const emotions = [...emotionHistory, currentEmotion.emotions?.primary || 'neutral']; // Detect patterns const detectedPatterns = []; Object.keys(this.temporalPatterns).forEach(patternName => { const pattern = this.temporalPatterns[patternName]; if (this.matchesPattern(emotions, pattern)) { detectedPatterns.push({ pattern: patternName, confidence: this.calculatePatternConfidence(emotions, pattern), stage: this.getCurrentPatternStage(emotions, pattern) }); } }); // Calculate emotion trajectory const trajectory = this.calculateEmotionTrajectory(emotions); return { detectedPatterns, trajectory, trend: this.determineEmotionTrend(emotions), stability: this.calculateEmotionStability(emotions), predictions: this.predictNextEmotions(emotions, detectedPatterns) }; } catch (error) { logger.error('[FAILED] Temporal pattern analysis failed:', error); return { error: 'Temporal analysis unavailable' }; } } /** * Helper methods */ sentimentToEmotion(sentimentLabel, sentimentScore) { const absScore = Math.abs(sentimentScore || 0); if (sentimentLabel === 'positive') { return { emotion: 'happy', confidence: absScore }; } else if (sentimentLabel === 'negative') { return absScore > 0.5 ? { emotion: 'angry', confidence: absScore } : { emotion: 'sad', confidence: absScore }; } return { emotion: 'neutral', confidence: 0.5 }; } emotionToValence(emotion) { const valenceMap = { happy: 0.8, excited: 0.9, content: 0.6, satisfied: 0.7, grateful: 0.8, sad: -0.6, angry: -0.8, frustrated: -0.7, disappointed: -0.5, fearful: -0.4, neutral: 0, calm: 0.2, confused: -0.1 }; return valenceMap[emotion] || 0; } calculateIntensity(value) { if (value >= 0.8) return 'very_high'; if (value >= 0.6) return 'high'; if (value >= 0.4) return 'medium'; if (value >= 0.2) return 'low'; return 'very_low'; } calculateArousal(emotion) { const arousalMap = { excited: 0.9, angry: 0.8, fearful: 0.7, surprised: 0.8, happy: 0.6, frustrated: 0.7, impatient: 0.6, sad: 0.3, calm: 0.2, neutral: 0.4, content: 0.3 }; return arousalMap[emotion] || 0.5; } getModalityWeight(modality) { const weights = { text: 0.4, voice: 0.4, image: 0.2 }; return weights[modality] || 0.33; } calculatePairwiseConsistency(result1, result2, rules) { const emotion1 = result1.emotions?.primary || 'neutral'; const emotion2 = result2.emotions?.primary || 'neutral'; // Check if emotions are consistent according to rules const isConsistent = rules.consistent.some(rule => { const keys = Object.keys(rule); return keys.every(key => { if (key === Object.keys(rule)[0]) return this.emotionsMatch(rule[key], emotion1); return this.emotionsMatch(rule[key], emotion2); }); }); return isConsistent ? 1.0 : 0.0; } emotionsMatch(expected, actual) { // Simple emotion matching - could be enhanced with semantic similarity return expected === actual || this.getEmotionCategory(expected) === this.getEmotionCategory(actual); } getEmotionCategory(emotion) { const categories = { positive: ['happy', 'excited', 'content', 'satisfied', 'grateful'], negative: ['sad', 'angry', 'frustrated', 'disappointed', 'fearful'], neutral: ['neutral', 'calm', 'confused'] }; for (const [category, emotions] of Object.entries(categories)) { if (emotions.includes(emotion)) return category; } return 'neutral'; } interpretConsistency(score) { if (score >= 0.8) return 'highly_consistent'; if (score >= 0.6) return 'moderately_consistent'; if (score >= 0.4) return 'somewhat_consistent'; return 'inconsistent'; } findCustomerServiceImpact(emotion) { for (const [impact, config] of Object.entries(this.customerServiceImpact)) { if (config.emotions.includes(emotion)) { return config; } } return this.customerServiceImpact.neutral; } generateResponseRecommendations(emotionResult, impact) { const recommendations = []; switch (impact.response_strategy) { case 'de_escalation': recommendations.push('Use calm, empathetic tone'); recommendations.push('Acknowledge customer concerns'); recommendations.push('Offer immediate assistance'); break; case 'maintain_satisfaction': recommendations.push('Continue positive interaction'); recommendations.push('Ensure complete resolution'); break; case 'immediate_assistance': recommendations.push('Prioritize quick response'); recommendations.push('Provide clear next steps'); break; default: recommendations.push('Provide helpful information'); recommendations.push('Maintain professional tone'); } return recommendations; } assessEscalationRisk(emotionResult, options) { const emotion = emotionResult.emotions?.primary || 'neutral'; const confidence = emotionResult.emotions?.confidence || 0.5; const intensity = emotionResult.emotions?.intensity || 'medium'; let riskScore = 0; // High-risk emotions if (['angry', 'frustrated'].includes(emotion)) riskScore += 0.4; if (['impatient', 'disappointed'].includes(emotion)) riskScore += 0.2; // Confidence factor riskScore *= confidence; // Intensity factor const intensityMultiplier = { very_high: 1.5, high: 1.2, medium: 1.0, low: 0.8, very_low: 0.5 }; riskScore *= intensityMultiplier[intensity] || 1.0; // Historical context if (options.previousEscalations) { riskScore += 0.2; } return { score: Math.min(1.0, riskScore), level: riskScore > 0.7 ? 'high' : riskScore > 0.4 ? 'medium' : 'low', factors: this.identifyRiskFactors(emotionResult, options) }; } identifyRiskFactors(emotionResult, options) { const factors = []; const emotion = emotionResult.emotions?.primary || 'neutral'; if (['angry', 'frustrated'].includes(emotion)) { factors.push('Negative emotion detected'); } if (emotionResult.emotions?.intensity === 'very_high') { factors.push('High emotional intensity'); } if (options.previousEscalations) { factors.push('Previous escalation history'); } return factors; } matchesPattern(emotions, pattern) { if (emotions.length < pattern.length) return false; const recentEmotions = emotions.slice(-pattern.length); return pattern.every((patternEmotion, index) => this.emotionsMatch(patternEmotion, recentEmotions[index]) ); } calculatePatternConfidence(emotions, pattern) { // Simplified pattern confidence calculation return 0.8; } getCurrentPatternStage(emotions, pattern) { const recentEmotions = emotions.slice(-pattern.length); return recentEmotions.length; } calculateEmotionTrajectory(emotions) { if (emotions.length < 2) return 'stable'; const valences = emotions.map(e => this.emotionToValence(e)); const trend = valences[valences.length - 1] - valences[0]; if (trend > 0.2) return 'improving'; if (trend < -0.2) return 'declining'; return 'stable'; } determineEmotionTrend(emotions) { return this.calculateEmotionTrajectory(emotions); } calculateEmotionStability(emotions) { if (emotions.length < 3) return 'unknown'; const valences = emotions.map(e => this.emotionToValence(e)); const variance = this.calculateVariance(valences); if (variance < 0.1) return 'very_stable'; if (variance < 0.3) return 'stable'; if (variance < 0.5) return 'somewhat_unstable'; return 'unstable'; } calculateVariance(values) { const mean = values.reduce((sum, val) => sum + val, 0) / values.length; const squaredDiffs = values.map(val => Math.pow(val - mean, 2)); return squaredDiffs.reduce((sum, diff) => sum + diff, 0) / values.length; } predictNextEmotions(emotions, patterns) { const predictions = []; // Based on detected patterns patterns.forEach(pattern => { if (pattern.pattern === 'escalation') { predictions.push({ emotion: 'angry', probability: 0.7 }); } else if (pattern.pattern === 'de_escalation') { predictions.push({ emotion: 'calm', probability: 0.6 }); } }); // Default prediction based on current emotion const currentEmotion = emotions[emotions.length - 1]; predictions.push({ emotion: currentEmotion, probability: 0.5 }); return predictions.slice(0, 3); // Top 3 predictions } calculateMultimodalConfidence(emotionResults, consistencyAnalysis) { const modalityConfidences = Object.values(emotionResults) .map(result => result.emotions?.confidence || 0.5); const avgConfidence = modalityConfidences.reduce((sum, conf) => sum + conf, 0) / modalityConfidences.length; const consistencyBonus = consistencyAnalysis.overallConsistency * 0.2; return Math.min(1.0, avgConfidence + consistencyBonus); } performBasicTextEmotionAnalysis(text, options) { // Fallback basic text emotion analysis return { emotions: { primary: 'neutral', confidence: 0.5, intensity: 'medium', valence: 0, arousal: 0.5 }, source: 'basic_text_analysis' }; } performBasicVoiceEmotionAnalysis(voice, options) { // Fallback basic voice emotion analysis return { emotions: { primary: 'neutral', confidence: 0.5, intensity: 'medium', valence: 0, arousal: 0.5 }, source: 'basic_voice_analysis' }; } performBasicImageEmotionAnalysis(image, options) { // Fallback basic image emotion analysis return { emotions: { primary: 'neutral', confidence: 0.5, intensity: 'medium', valence: 0, arousal: 0.5 }, source: 'basic_image_analysis' }; } getDefaultEmotionResult(type) { return { emotions: { primary: 'neutral', confidence: 0.5, intensity: 'medium', valence: 0, arousal: 0.5 }, source: `default_${type}`, error: 'Analysis unavailable' }; } /** * Health check */ isHealthy() { return this.isInitialized; } /** * Cleanup resources */ async cleanup() { try { this.isInitialized = false; logger.info('[COMPLETE] Emotion Analysis Service cleanup completed'); } catch (error) { logger.error('[FAILED] Emotion Analysis Service cleanup failed:', error); throw error; } } } module.exports = EmotionAnalysisService;