/** * ============================================= * MULTIMODAL FUSION SERVICE * Intelligent combination of text, voice, and image analysis results * Context-aware decision making with weighted scoring algorithms * ============================================= */ const logger = require('../utils/logger'); const { v4: uuidv4 } = require('uuid'); class MultimodalFusionService { constructor() { this.isInitialized = false; // Fusion weights for different modalities this.modalityWeights = { text: { intent: 0.4, sentiment: 0.3, entities: 0.2, urgency: 0.1 }, voice: { emotion: 0.4, transcription: 0.3, quality: 0.2, activity: 0.1 }, image: { ocr: 0.3, objects: 0.25, faces: 0.25, products: 0.2 } }; // Context-aware fusion rules this.fusionRules = { customer_service: { priority: ['intent', 'urgency', 'emotion', 'sentiment'], weights: { text: 0.4, voice: 0.4, image: 0.2 } }, technical_support: { priority: ['products', 'objects', 'intent', 'ocr'], weights: { text: 0.3, voice: 0.2, image: 0.5 } }, billing_inquiry: { priority: ['intent', 'entities', 'ocr', 'sentiment'], weights: { text: 0.5, voice: 0.3, image: 0.2 } }, complaint: { priority: ['emotion', 'sentiment', 'urgency', 'intent'], weights: { text: 0.3, voice: 0.5, image: 0.2 } } }; // Confidence thresholds for different decisions this.confidenceThresholds = { high: 0.8, medium: 0.6, low: 0.4 }; // Emotion mapping between modalities this.emotionMapping = { text: { positive: ['happy', 'excited', 'satisfied'], negative: ['angry', 'frustrated', 'sad', 'disappointed'], neutral: ['calm', 'neutral'] }, voice: { positive: ['happy', 'excited', 'calm'], negative: ['angry', 'sad', 'frustrated'], neutral: ['neutral', 'calm'] }, image: { positive: ['happy', 'surprised'], negative: ['angry', 'sad', 'disgusted', 'fearful'], neutral: ['neutral'] } }; } /** * Initialize Multimodal Fusion Service */ async initialize() { try { // Load fusion models or configurations if needed await this.loadFusionModels(); this.isInitialized = true; logger.info('[COMPLETE] Multimodal Fusion Service initialized successfully'); } catch (error) { logger.error('[FAILED] Multimodal Fusion Service initialization failed:', error); throw error; } } /** * Load fusion models and configurations */ async loadFusionModels() { try { // In production, this would load pre-trained fusion models // For now, we use rule-based fusion logger.info('[COMPLETE] Fusion models loaded successfully'); } catch (error) { logger.error('[FAILED] Failed to load fusion models:', error); throw error; } } /** * Fuse multimodal inputs with intelligent combination */ async fuseInputs(inputs, options = {}) { try { const fusionId = uuidv4(); const startTime = Date.now(); logger.info(` Starting multimodal fusion: ${fusionId}`); // Validate inputs this.validateInputs(inputs); // Determine fusion context const context = await this.determineFusionContext(inputs, options); // Extract features from each modality const features = await this.extractMultimodalFeatures(inputs); // Perform weighted fusion based on context const fusedResult = await this.performWeightedFusion(features, context); // Generate confidence scores const confidenceAnalysis = this.analyzeConfidence(fusedResult, features); // Create comprehensive result const result = { fusionId, processedAt: new Date().toISOString(), processingTime: Date.now() - startTime, context: context.type, confidence: confidenceAnalysis.overall, // Primary results intent: fusedResult.intent, sentiment: fusedResult.sentiment, emotion: fusedResult.emotion, urgency: fusedResult.urgency, // Extracted information entities: fusedResult.entities, products: fusedResult.products, keyInsights: fusedResult.keyInsights, // Modality contributions modalityContributions: fusedResult.contributions, // Confidence breakdown confidenceBreakdown: confidenceAnalysis.breakdown, // Recommendations recommendations: this.generateRecommendations(fusedResult, confidenceAnalysis), // Raw features for debugging rawFeatures: options.includeRawFeatures ? features : undefined }; // Enhanced analysis if requested if (options.enhancedAnalysis) { result.enhancedAnalysis = await this.performEnhancedFusion(features, fusedResult); } logger.info(`[COMPLETE] Multimodal fusion completed: ${fusionId} (${result.processingTime}ms)`); return result; } catch (error) { logger.error('[FAILED] Multimodal fusion failed:', error); throw error; } } /** * Validate multimodal inputs */ validateInputs(inputs) { if (!inputs || typeof inputs !== 'object') { throw new Error('Invalid inputs provided'); } const validModalities = ['text', 'voice', 'image']; const providedModalities = Object.keys(inputs).filter(key => validModalities.includes(key)); if (providedModalities.length === 0) { throw new Error('At least one modality (text, voice, or image) must be provided'); } // Validate each modality providedModalities.forEach(modality => { if (!inputs[modality] || typeof inputs[modality] !== 'object') { throw new Error(`Invalid ${modality} input provided`); } }); } /** * Determine fusion context based on inputs */ async determineFusionContext(inputs, options) { try { // Start with default context let contextType = 'customer_service'; let confidence = 0.5; // Analyze text intent if available if (inputs.text && inputs.text.intent) { const intent = inputs.text.intent.primary; if (intent === 'technical') { contextType = 'technical_support'; confidence = inputs.text.intent.confidence; } else if (intent === 'billing') { contextType = 'billing_inquiry'; confidence = inputs.text.intent.confidence; } else if (intent === 'complaint') { contextType = 'complaint'; confidence = inputs.text.intent.confidence; } } // Consider voice emotion for context refinement if (inputs.voice && inputs.voice.emotion) { const voiceEmotion = inputs.voice.emotion.dominant; if (['angry', 'frustrated'].includes(voiceEmotion) && inputs.voice.emotion.confidence > 0.7) { contextType = 'complaint'; confidence = Math.max(confidence, inputs.voice.emotion.confidence); } } // Consider image products for technical context if (inputs.image && inputs.image.products && inputs.image.products.products.length > 0) { const hasProducts = inputs.image.products.products.some(p => p.confidence > 0.6); if (hasProducts) { contextType = 'technical_support'; confidence = Math.max(confidence, 0.7); } } // Override with explicit context if provided if (options.context) { contextType = options.context; confidence = 0.9; } return { type: contextType, confidence, rules: this.fusionRules[contextType] || this.fusionRules.customer_service, reasoning: this.explainContextDecision(contextType, inputs) }; } catch (error) { logger.error('[FAILED] Context determination failed:', error); return { type: 'customer_service', confidence: 0.5, rules: this.fusionRules.customer_service, reasoning: 'Default context due to analysis failure' }; } } /** * Extract features from each modality */ async extractMultimodalFeatures(inputs) { const features = { text: null, voice: null, image: null }; // Extract text features if (inputs.text) { features.text = { intent: inputs.text.intent || { primary: 'information', confidence: 0.5 }, sentiment: inputs.text.sentiment || { label: 'neutral', score: 0 }, entities: inputs.text.entities || {}, urgency: inputs.text.urgency || { level: 'medium', score: 0 }, keywords: inputs.text.keywords || { keywords: [] }, emotions: inputs.text.emotions || { dominant: 'neutral' } }; } // Extract voice features if (inputs.voice) { features.voice = { transcription: inputs.voice.transcription || { text: '', confidence: 0 }, emotion: inputs.voice.emotion || { dominant: 'neutral', confidence: 0 }, quality: inputs.voice.audioQuality || { score: 50 }, activity: inputs.voice.voiceActivity || { hasVoice: false } }; } // Extract image features if (inputs.image) { features.image = { ocr: inputs.image.ocr || { text: '', confidence: 0 }, objects: inputs.image.objects || { objects: [] }, faces: inputs.image.faces || { faces: [], emotions: [] }, products: inputs.image.products || { products: [] }, quality: inputs.image.quality || { score: 50 } }; } return features; } /** * Perform weighted fusion based on context */ async performWeightedFusion(features, context) { const weights = context.rules.weights; const priority = context.rules.priority; // Initialize fusion result const fusedResult = { intent: { primary: 'information', confidence: 0, sources: [] }, sentiment: { label: 'neutral', score: 0, confidence: 0, sources: [] }, emotion: { dominant: 'neutral', confidence: 0, sources: [] }, urgency: { level: 'medium', score: 0, confidence: 0, sources: [] }, entities: {}, products: [], keyInsights: [], contributions: {} }; // Fuse intent fusedResult.intent = this.fuseIntent(features, weights); // Fuse sentiment and emotion const emotionSentimentFusion = this.fuseEmotionSentiment(features, weights); fusedResult.sentiment = emotionSentimentFusion.sentiment; fusedResult.emotion = emotionSentimentFusion.emotion; // Fuse urgency fusedResult.urgency = this.fuseUrgency(features, weights); // Combine entities fusedResult.entities = this.combineEntities(features); // Combine products fusedResult.products = this.combineProducts(features); // Generate key insights fusedResult.keyInsights = this.generateKeyInsights(features, fusedResult); // Calculate modality contributions fusedResult.contributions = this.calculateModalityContributions(features, weights); return fusedResult; } /** * Fuse intent from multiple modalities */ fuseIntent(features, weights) { const intents = []; // Text intent (primary source) if (features.text && features.text.intent) { intents.push({ intent: features.text.intent.primary, confidence: features.text.intent.confidence * weights.text, source: 'text' }); } // Voice transcription intent (if available) if (features.voice && features.voice.transcription && features.voice.transcription.text) { // Simple intent detection from transcription const transcriptionIntent = this.detectIntentFromText(features.voice.transcription.text); intents.push({ intent: transcriptionIntent.intent, confidence: transcriptionIntent.confidence * features.voice.transcription.confidence * weights.voice, source: 'voice_transcription' }); } // Image-based intent (from products or OCR) if (features.image) { if (features.image.products.products.length > 0) { intents.push({ intent: 'technical', confidence: 0.7 * weights.image, source: 'image_products' }); } if (features.image.ocr.text) { const ocrIntent = this.detectIntentFromText(features.image.ocr.text); intents.push({ intent: ocrIntent.intent, confidence: ocrIntent.confidence * features.image.ocr.confidence * weights.image, source: 'image_ocr' }); } } // Find best intent if (intents.length === 0) { return { primary: 'information', confidence: 0.5, sources: [] }; } intents.sort((a, b) => b.confidence - a.confidence); return { primary: intents[0].intent, confidence: intents[0].confidence, sources: intents.map(i => ({ source: i.source, confidence: i.confidence })), alternatives: intents.slice(1, 3) }; } /** * Fuse emotion and sentiment */ fuseEmotionSentiment(features, weights) { const emotions = []; const sentiments = []; // Text sentiment and emotion if (features.text) { if (features.text.sentiment) { sentiments.push({ label: features.text.sentiment.label, score: features.text.sentiment.score, confidence: features.text.sentiment.confidence * weights.text, source: 'text' }); } if (features.text.emotions) { emotions.push({ emotion: features.text.emotions.dominant, confidence: features.text.emotions.intensity * weights.text, source: 'text' }); } } // Voice emotion if (features.voice && features.voice.emotion) { emotions.push({ emotion: features.voice.emotion.dominant, confidence: features.voice.emotion.confidence * weights.voice, source: 'voice' }); // Convert voice emotion to sentiment const voiceSentiment = this.emotionToSentiment(features.voice.emotion.dominant); sentiments.push({ label: voiceSentiment.label, score: voiceSentiment.score, confidence: features.voice.emotion.confidence * weights.voice, source: 'voice_emotion' }); } // Image face emotions if (features.image && features.image.faces.emotions.length > 0) { const dominantFaceEmotion = features.image.faces.emotions[0]; emotions.push({ emotion: dominantFaceEmotion.emotion, confidence: dominantFaceEmotion.confidence * weights.image, source: 'image_faces' }); // Convert face emotion to sentiment const faceSentiment = this.emotionToSentiment(dominantFaceEmotion.emotion); sentiments.push({ label: faceSentiment.label, score: faceSentiment.score, confidence: dominantFaceEmotion.confidence * weights.image, source: 'image_faces' }); } // Fuse emotions emotions.sort((a, b) => b.confidence - a.confidence); const fusedEmotion = emotions.length > 0 ? { dominant: emotions[0].emotion, confidence: emotions[0].confidence, sources: emotions.map(e => ({ source: e.source, confidence: e.confidence })) } : { dominant: 'neutral', confidence: 0.5, sources: [] }; // Fuse sentiments sentiments.sort((a, b) => b.confidence - a.confidence); const fusedSentiment = sentiments.length > 0 ? { label: sentiments[0].label, score: sentiments[0].score, confidence: sentiments[0].confidence, sources: sentiments.map(s => ({ source: s.source, confidence: s.confidence })) } : { label: 'neutral', score: 0, confidence: 0.5, sources: [] }; return { emotion: fusedEmotion, sentiment: fusedSentiment }; } /** * Fuse urgency levels */ fuseUrgency(features, weights) { const urgencyScores = []; // Text urgency if (features.text && features.text.urgency) { urgencyScores.push({ level: features.text.urgency.level, score: features.text.urgency.score * weights.text, source: 'text' }); } // Voice emotion urgency (angry/frustrated = high urgency) if (features.voice && features.voice.emotion) { const emotionUrgency = this.emotionToUrgency(features.voice.emotion.dominant); urgencyScores.push({ level: emotionUrgency.level, score: emotionUrgency.score * features.voice.emotion.confidence * weights.voice, source: 'voice_emotion' }); } // Calculate weighted average if (urgencyScores.length === 0) { return { level: 'medium', score: 0, confidence: 0.5, sources: [] }; } const totalWeight = urgencyScores.reduce((sum, u) => sum + u.score, 0); const weightedScore = urgencyScores.reduce((sum, u) => sum + (u.score * this.urgencyToScore(u.level)), 0) / totalWeight; return { level: this.scoreToUrgency(weightedScore), score: weightedScore, confidence: totalWeight / urgencyScores.length, sources: urgencyScores.map(u => ({ source: u.source, level: u.level, score: u.score })) }; } /** * Combine entities from all modalities */ combineEntities(features) { const combinedEntities = {}; // Text entities if (features.text && features.text.entities) { Object.assign(combinedEntities, features.text.entities); } // Voice transcription entities (if available) if (features.voice && features.voice.transcription && features.voice.transcription.text) { // Extract entities from transcription const transcriptionEntities = this.extractEntitiesFromText(features.voice.transcription.text); Object.keys(transcriptionEntities).forEach(key => { if (combinedEntities[key]) { combinedEntities[key] = [...new Set([...combinedEntities[key], ...transcriptionEntities[key]])]; } else { combinedEntities[key] = transcriptionEntities[key]; } }); } // Image OCR entities if (features.image && features.image.ocr && features.image.ocr.text) { const ocrEntities = this.extractEntitiesFromText(features.image.ocr.text); Object.keys(ocrEntities).forEach(key => { if (combinedEntities[key]) { combinedEntities[key] = [...new Set([...combinedEntities[key], ...ocrEntities[key]])]; } else { combinedEntities[key] = ocrEntities[key]; } }); } return combinedEntities; } /** * Combine products from all modalities */ combineProducts(features) { const products = []; // Image products (primary source) if (features.image && features.image.products && features.image.products.products) { products.push(...features.image.products.products.map(p => ({ ...p, source: 'image' }))); } // Text-mentioned products if (features.text && features.text.keywords) { const textProducts = this.extractProductsFromKeywords(features.text.keywords.keywords); products.push(...textProducts.map(p => ({ ...p, source: 'text' }))); } // Remove duplicates and sort by confidence const uniqueProducts = this.deduplicateProducts(products); return uniqueProducts.sort((a, b) => b.confidence - a.confidence); } /** * Generate key insights from fused analysis */ generateKeyInsights(features, fusedResult) { const insights = []; // Intent-based insights if (fusedResult.intent.confidence > 0.7) { insights.push({ type: 'intent', insight: `High confidence ${fusedResult.intent.primary} intent detected`, confidence: fusedResult.intent.confidence, actionable: true }); } // Emotion-sentiment mismatch if (fusedResult.emotion.confidence > 0.6 && fusedResult.sentiment.confidence > 0.6) { const emotionPolarity = this.getEmotionPolarity(fusedResult.emotion.dominant); const sentimentPolarity = fusedResult.sentiment.label; if (emotionPolarity !== sentimentPolarity) { insights.push({ type: 'emotion_sentiment_mismatch', insight: `Emotion (${fusedResult.emotion.dominant}) and sentiment (${sentimentPolarity}) don't align`, confidence: Math.min(fusedResult.emotion.confidence, fusedResult.sentiment.confidence), actionable: true }); } } // High urgency detection if (fusedResult.urgency.level === 'high' && fusedResult.urgency.confidence > 0.7) { insights.push({ type: 'high_urgency', insight: 'High urgency situation detected - prioritize response', confidence: fusedResult.urgency.confidence, actionable: true }); } // Product-related technical issue if (fusedResult.products.length > 0 && fusedResult.intent.primary === 'technical') { insights.push({ type: 'product_technical_issue', insight: `Technical issue with ${fusedResult.products[0].name} detected`, confidence: Math.min(fusedResult.intent.confidence, fusedResult.products[0].confidence), actionable: true }); } return insights; } /** * Calculate modality contributions */ calculateModalityContributions(features, weights) { const contributions = {}; Object.keys(features).forEach(modality => { if (features[modality]) { contributions[modality] = { weight: weights[modality] || 0, dataQuality: this.assessModalityQuality(features[modality], modality), keyContributions: this.identifyKeyContributions(features[modality], modality) }; } }); return contributions; } /** * Analyze confidence across all fusion results */ analyzeConfidence(fusedResult, features) { const confidenceScores = [ fusedResult.intent.confidence, fusedResult.sentiment.confidence, fusedResult.emotion.confidence, fusedResult.urgency.confidence ].filter(score => score > 0); const overall = confidenceScores.length > 0 ? confidenceScores.reduce((sum, score) => sum + score, 0) / confidenceScores.length : 0.5; return { overall, breakdown: { intent: fusedResult.intent.confidence, sentiment: fusedResult.sentiment.confidence, emotion: fusedResult.emotion.confidence, urgency: fusedResult.urgency.confidence }, reliability: overall > this.confidenceThresholds.high ? 'high' : overall > this.confidenceThresholds.medium ? 'medium' : 'low' }; } /** * Generate recommendations based on fusion results */ generateRecommendations(fusedResult, confidenceAnalysis) { const recommendations = []; // Low confidence recommendations if (confidenceAnalysis.overall < this.confidenceThresholds.medium) { recommendations.push({ type: 'data_quality', message: 'Consider collecting additional data for more reliable analysis', priority: 'medium' }); } // Intent-based recommendations if (fusedResult.intent.primary === 'complaint' && fusedResult.urgency.level === 'high') { recommendations.push({ type: 'escalation', message: 'Escalate to senior support agent immediately', priority: 'high' }); } // Emotion-based recommendations if (['angry', 'frustrated'].includes(fusedResult.emotion.dominant)) { recommendations.push({ type: 'response_tone', message: 'Use empathetic and calming response tone', priority: 'high' }); } // Product-based recommendations if (fusedResult.products.length > 0) { recommendations.push({ type: 'specialist_routing', message: `Route to ${fusedResult.products[0].category} specialist`, priority: 'medium' }); } return recommendations; } /** * Helper methods */ detectIntentFromText(text) { // Simplified intent detection const lowerText = text.toLowerCase(); if (lowerText.includes('facture') || lowerText.includes('paiement')) { return { intent: 'billing', confidence: 0.7 }; } if (lowerText.includes('problème') || lowerText.includes('panne')) { return { intent: 'technical', confidence: 0.7 }; } if (lowerText.includes('réclamation') || lowerText.includes('plainte')) { return { intent: 'complaint', confidence: 0.7 }; } return { intent: 'information', confidence: 0.5 }; } emotionToSentiment(emotion) { const positiveEmotions = ['happy', 'excited', 'calm']; const negativeEmotions = ['angry', 'sad', 'frustrated', 'fearful']; if (positiveEmotions.includes(emotion)) { return { label: 'positive', score: 1 }; } if (negativeEmotions.includes(emotion)) { return { label: 'negative', score: -1 }; } return { label: 'neutral', score: 0 }; } emotionToUrgency(emotion) { const highUrgencyEmotions = ['angry', 'frustrated', 'fearful']; const lowUrgencyEmotions = ['calm', 'happy']; if (highUrgencyEmotions.includes(emotion)) { return { level: 'high', score: 0.8 }; } if (lowUrgencyEmotions.includes(emotion)) { return { level: 'low', score: 0.2 }; } return { level: 'medium', score: 0.5 }; } urgencyToScore(level) { const scoreMap = { low: 0.2, medium: 0.5, high: 0.8, urgent: 1.0 }; return scoreMap[level] || 0.5; } scoreToUrgency(score) { if (score >= 0.8) return 'urgent'; if (score >= 0.6) return 'high'; if (score >= 0.4) return 'medium'; return 'low'; } getEmotionPolarity(emotion) { const positiveEmotions = ['happy', 'excited', 'surprised']; const negativeEmotions = ['angry', 'sad', 'frustrated', 'fearful', 'disgusted']; if (positiveEmotions.includes(emotion)) return 'positive'; if (negativeEmotions.includes(emotion)) return 'negative'; return 'neutral'; } extractEntitiesFromText(text) { // Simplified entity extraction const entities = {}; const phoneRegex = /(?:\+33|0)[1-9](?:[0-9]{8})/g; const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g; const phones = text.match(phoneRegex); const emails = text.match(emailRegex); if (phones) entities.phone = phones; if (emails) entities.email = emails; return entities; } extractProductsFromKeywords(keywords) { // Extract products mentioned in keywords const products = []; const productKeywords = ['iphone', 'samsung', 'freebox', 'chargeur']; keywords.forEach(keyword => { if (productKeywords.includes(keyword.toLowerCase())) { products.push({ name: keyword, category: 'general', confidence: 0.6 }); } }); return products; } deduplicateProducts(products) { const seen = new Set(); return products.filter(product => { const key = `${product.name}_${product.category}`; if (seen.has(key)) return false; seen.add(key); return true; }); } assessModalityQuality(modalityData, modality) { // Assess the quality of data from each modality switch (modality) { case 'text': return modalityData.intent?.confidence || 0.5; case 'voice': return modalityData.quality?.score / 100 || 0.5; case 'image': return modalityData.quality?.score / 100 || 0.5; default: return 0.5; } } identifyKeyContributions(modalityData, modality) { const contributions = []; switch (modality) { case 'text': if (modalityData.intent?.confidence > 0.7) { contributions.push(`Intent: ${modalityData.intent.primary}`); } if (modalityData.sentiment?.confidence > 0.7) { contributions.push(`Sentiment: ${modalityData.sentiment.label}`); } break; case 'voice': if (modalityData.emotion?.confidence > 0.7) { contributions.push(`Emotion: ${modalityData.emotion.dominant}`); } if (modalityData.transcription?.confidence > 0.7) { contributions.push('High-quality transcription'); } break; case 'image': if (modalityData.products?.products?.length > 0) { contributions.push(`Products: ${modalityData.products.products.length} detected`); } if (modalityData.faces?.faces?.length > 0) { contributions.push(`Faces: ${modalityData.faces.faces.length} detected`); } break; } return contributions; } explainContextDecision(contextType, inputs) { const reasons = []; if (inputs.text?.intent) { reasons.push(`Text intent: ${inputs.text.intent.primary}`); } if (inputs.voice?.emotion) { reasons.push(`Voice emotion: ${inputs.voice.emotion.dominant}`); } if (inputs.image?.products?.products?.length > 0) { reasons.push('Products detected in image'); } return reasons.join(', ') || 'Default context selection'; } async performEnhancedFusion(features, fusedResult) { // Enhanced fusion with additional analysis return { crossModalityConsistency: this.analyzeCrossModalityConsistency(features), temporalAnalysis: this.performTemporalAnalysis(features), contextualRelevance: this.assessContextualRelevance(features, fusedResult) }; } analyzeCrossModalityConsistency(features) { // Analyze consistency between different modalities const consistencyScore = 0.8; // Placeholder return { score: consistencyScore, inconsistencies: [], recommendations: [] }; } performTemporalAnalysis(features) { // Analyze temporal aspects if available return { timeRelevance: 'current', urgencyTrend: 'stable' }; } assessContextualRelevance(features, fusedResult) { // Assess how relevant the analysis is to the context return { relevanceScore: 0.85, contextAlignment: 'high' }; } /** * Health check */ isHealthy() { return this.isInitialized; } /** * Cleanup resources */ async cleanup() { try { this.isInitialized = false; logger.info('[COMPLETE] Multimodal Fusion Service cleanup completed'); } catch (error) { logger.error('[FAILED] Multimodal Fusion Service cleanup failed:', error); throw error; } } } module.exports = MultimodalFusionService;