/** * ============================================= * TEXT PROCESSING SERVICE * Advanced NLP and text understanding capabilities * Handles sentiment analysis, intent recognition, entity extraction * ============================================= */ const natural = require('natural'); const compromise = require('compromise'); const sentiment = require('sentiment'); const keywordExtractor = require('keyword-extractor'); const languageDetect = require('language-detect'); const { OpenAI } = require('openai'); const logger = require('../utils/logger'); class TextProcessingService { constructor() { this.isInitialized = false; this.openai = null; this.sentimentAnalyzer = new sentiment(); this.stemmer = natural.PorterStemmerFr; // French stemmer this.tokenizer = new natural.WordTokenizer(); this.tfidf = new natural.TfIdf(); // French language support this.frenchStopWords = [ 'le', 'de', 'et', 'à', 'un', 'il', 'être', 'et', 'en', 'avoir', 'que', 'pour', 'dans', 'ce', 'son', 'une', 'sur', 'avec', 'ne', 'se', 'pas', 'tout', 'plus', 'par', 'grand', 'en', 'être', 'et', 'en', 'avoir', 'que', 'pour' ]; // Intent patterns for Free Mobile customer service this.intentPatterns = { billing: [ /factur/i, /paiement/i, /prix/i, /coût/i, /tarif/i, /abonnement/i, /remboursement/i, /crédit/i, /débit/i, /montant/i ], technical: [ /problème/i, /panne/i, /bug/i, /erreur/i, /connexion/i, /réseau/i, /internet/i, /wifi/i, /signal/i, /débit/i, /lenteur/i ], subscription: [ /forfait/i, /offre/i, /changement/i, /modification/i, /upgrade/i, /downgrade/i, /option/i, /service/i ], complaint: [ /réclamation/i, /plainte/i, /insatisfait/i, /mécontent/i, /problème/i, /service client/i, /qualité/i ], information: [ /information/i, /renseignement/i, /question/i, /aide/i, /comment/i, /pourquoi/i, /quand/i, /où/i, /combien/i ] }; // Entity patterns this.entityPatterns = { phone: /(?:\+33|0)[1-9](?:[0-9]{8})/g, email: /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g, amount: /(\d+(?:[.,]\d{2})?)\s*(?:€|euros?|EUR)/gi, date: /\b\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4}\b/g, time: /\b\d{1,2}[h:]\d{2}\b/g }; } /** * Initialize Text Processing Service */ async initialize() { try { // Initialize OpenAI if API key is provided if (process.env.OPENAI_API_KEY) { this.openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY }); logger.info('[COMPLETE] OpenAI integration initialized'); } // Load pre-trained models or data if needed await this.loadLanguageModels(); this.isInitialized = true; logger.info('[COMPLETE] Text Processing Service initialized successfully'); } catch (error) { logger.error('[FAILED] Text Processing Service initialization failed:', error); throw error; } } /** * Load language models and training data */ async loadLanguageModels() { try { // Load training data for intent classification const trainingData = [ { text: "Ma facture est trop élevée", intent: "billing" }, { text: "Je n'arrive pas à me connecter", intent: "technical" }, { text: "Je veux changer mon forfait", intent: "subscription" }, { text: "Je ne suis pas satisfait du service", intent: "complaint" }, { text: "Comment puis-je contacter le support", intent: "information" } ]; // Train TF-IDF model trainingData.forEach(item => { this.tfidf.addDocument(item.text); }); logger.info('[COMPLETE] Language models loaded successfully'); } catch (error) { logger.error('[FAILED] Failed to load language models:', error); throw error; } } /** * Process text with comprehensive analysis */ async processText(text, options = {}) { try { if (!text || typeof text !== 'string') { throw new Error('Invalid text input'); } const result = { originalText: text, processedAt: new Date().toISOString(), language: this.detectLanguage(text), sentiment: this.analyzeSentiment(text), intent: this.classifyIntent(text), entities: this.extractEntities(text), keywords: this.extractKeywords(text), summary: await this.generateSummary(text, options), emotions: this.analyzeEmotions(text), urgency: this.assessUrgency(text), topics: this.extractTopics(text), readability: this.assessReadability(text) }; // Add advanced analysis if OpenAI is available if (this.openai && options.useAdvancedAnalysis) { result.advancedAnalysis = await this.performAdvancedAnalysis(text); } logger.info(' Text processed successfully', { textLength: text.length, language: result.language, intent: result.intent.primary, sentiment: result.sentiment.label }); return result; } catch (error) { logger.error('[FAILED] Text processing failed:', error); throw error; } } /** * Detect text language */ detectLanguage(text) { try { const detected = languageDetect.detect(text, 1); return { language: detected[0] ? detected[0][0] : 'unknown', confidence: detected[0] ? detected[0][1] : 0, supportedLanguages: ['fr', 'en', 'es', 'de', 'it'] }; } catch (error) { return { language: 'fr', confidence: 0.5, supportedLanguages: ['fr'] }; } } /** * Analyze sentiment */ analyzeSentiment(text) { try { const result = this.sentimentAnalyzer.analyze(text); let label = 'neutral'; if (result.score > 2) label = 'positive'; else if (result.score < -2) label = 'negative'; return { score: result.score, comparative: result.comparative, label, confidence: Math.abs(result.comparative), positive: result.positive, negative: result.negative, tokens: result.tokens }; } catch (error) { logger.error('[FAILED] Sentiment analysis failed:', error); return { score: 0, label: 'neutral', confidence: 0 }; } } /** * Classify intent */ classifyIntent(text) { try { const scores = {}; const lowerText = text.toLowerCase(); // Pattern-based classification Object.keys(this.intentPatterns).forEach(intent => { scores[intent] = 0; this.intentPatterns[intent].forEach(pattern => { const matches = lowerText.match(pattern); if (matches) { scores[intent] += matches.length; } }); }); // Find primary intent const sortedIntents = Object.entries(scores) .sort(([,a], [,b]) => b - a) .map(([intent, score]) => ({ intent, score })); return { primary: sortedIntents[0]?.intent || 'information', confidence: sortedIntents[0]?.score || 0, alternatives: sortedIntents.slice(1, 3), allScores: scores }; } catch (error) { logger.error('[FAILED] Intent classification failed:', error); return { primary: 'information', confidence: 0, alternatives: [] }; } } /** * Extract entities */ extractEntities(text) { try { const entities = {}; Object.keys(this.entityPatterns).forEach(entityType => { const matches = text.match(this.entityPatterns[entityType]); if (matches) { entities[entityType] = [...new Set(matches)]; // Remove duplicates } }); // Use compromise for additional entity extraction const doc = compromise(text); entities.people = doc.people().out('array'); entities.places = doc.places().out('array'); entities.organizations = doc.organizations().out('array'); entities.dates = doc.dates().out('array'); entities.money = doc.money().out('array'); return entities; } catch (error) { logger.error('[FAILED] Entity extraction failed:', error); return {}; } } /** * Extract keywords */ extractKeywords(text) { try { const keywords = keywordExtractor.extract(text, { language: 'french', remove_digits: true, return_changed_case: true, remove_duplicates: true, return_chained_words: true }); // Filter out stop words const filteredKeywords = keywords.filter(keyword => !this.frenchStopWords.includes(keyword.toLowerCase()) ); return { keywords: filteredKeywords.slice(0, 10), // Top 10 keywords count: filteredKeywords.length, density: filteredKeywords.length / this.tokenizer.tokenize(text).length }; } catch (error) { logger.error('[FAILED] Keyword extraction failed:', error); return { keywords: [], count: 0, density: 0 }; } } /** * Generate text summary */ async generateSummary(text, options = {}) { try { if (text.length < 100) { return { summary: text, method: 'original' }; } // Simple extractive summarization const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 10); if (sentences.length <= 2) { return { summary: text, method: 'original' }; } // Score sentences based on keyword frequency const keywords = this.extractKeywords(text).keywords; const sentenceScores = sentences.map(sentence => { const score = keywords.reduce((acc, keyword) => { return acc + (sentence.toLowerCase().includes(keyword.toLowerCase()) ? 1 : 0); }, 0); return { sentence: sentence.trim(), score }; }); // Select top sentences const topSentences = sentenceScores .sort((a, b) => b.score - a.score) .slice(0, Math.max(1, Math.floor(sentences.length / 3))) .map(item => item.sentence); return { summary: topSentences.join('. ') + '.', method: 'extractive', originalLength: text.length, summaryLength: topSentences.join('. ').length, compressionRatio: topSentences.join('. ').length / text.length }; } catch (error) { logger.error('[FAILED] Summary generation failed:', error); return { summary: text.substring(0, 200) + '...', method: 'truncation' }; } } /** * Analyze emotions in text */ analyzeEmotions(text) { try { const emotionKeywords = { joy: ['heureux', 'content', 'satisfait', 'ravi', 'enchanté', 'joie'], anger: ['colère', 'énervé', 'furieux', 'irrité', 'fâché', 'mécontent'], sadness: ['triste', 'déçu', 'malheureux', 'chagrin', 'peine'], fear: ['peur', 'inquiet', 'anxieux', 'stressé', 'préoccupé'], surprise: ['surpris', 'étonné', 'stupéfait', 'choqué'], disgust: ['dégoût', 'écœuré', 'répugnant', 'horrible'] }; const emotions = {}; const lowerText = text.toLowerCase(); Object.keys(emotionKeywords).forEach(emotion => { emotions[emotion] = emotionKeywords[emotion].reduce((count, keyword) => { const regex = new RegExp(keyword, 'gi'); const matches = lowerText.match(regex); return count + (matches ? matches.length : 0); }, 0); }); // Find dominant emotion const dominantEmotion = Object.entries(emotions) .sort(([,a], [,b]) => b - a)[0]; return { emotions, dominant: dominantEmotion[0], intensity: dominantEmotion[1], emotional: dominantEmotion[1] > 0 }; } catch (error) { logger.error('[FAILED] Emotion analysis failed:', error); return { emotions: {}, dominant: 'neutral', intensity: 0, emotional: false }; } } /** * Assess text urgency */ assessUrgency(text) { try { const urgencyKeywords = { high: ['urgent', 'immédiat', 'critique', 'grave', 'important', 'rapidement'], medium: ['bientôt', 'assez vite', 'dans les plus brefs délais'], low: ['quand possible', 'pas pressé', 'plus tard'] }; const lowerText = text.toLowerCase(); let urgencyScore = 0; urgencyKeywords.high.forEach(keyword => { if (lowerText.includes(keyword)) urgencyScore += 3; }); urgencyKeywords.medium.forEach(keyword => { if (lowerText.includes(keyword)) urgencyScore += 2; }); urgencyKeywords.low.forEach(keyword => { if (lowerText.includes(keyword)) urgencyScore -= 1; }); let level = 'low'; if (urgencyScore >= 3) level = 'high'; else if (urgencyScore >= 1) level = 'medium'; return { level, score: urgencyScore, indicators: this.findUrgencyIndicators(text) }; } catch (error) { logger.error('[FAILED] Urgency assessment failed:', error); return { level: 'medium', score: 0, indicators: [] }; } } /** * Find urgency indicators in text */ findUrgencyIndicators(text) { const indicators = []; const lowerText = text.toLowerCase(); if (lowerText.includes('urgent')) indicators.push('Contains "urgent"'); if (lowerText.includes('!')) indicators.push('Contains exclamation marks'); if (lowerText.includes('ASAP') || lowerText.includes('au plus vite')) indicators.push('Time pressure'); if (lowerText.includes('problème') || lowerText.includes('panne')) indicators.push('Problem reported'); return indicators; } /** * Extract topics from text */ extractTopics(text) { try { const doc = compromise(text); const topics = []; // Extract nouns as potential topics const nouns = doc.nouns().out('array'); topics.push(...nouns.slice(0, 5)); // Extract named entities as topics const people = doc.people().out('array'); const places = doc.places().out('array'); topics.push(...people, ...places); return { topics: [...new Set(topics)].slice(0, 10), categories: this.categorizeTopics(topics) }; } catch (error) { logger.error('[FAILED] Topic extraction failed:', error); return { topics: [], categories: [] }; } } /** * Categorize extracted topics */ categorizeTopics(topics) { const categories = { technical: ['internet', 'wifi', 'réseau', 'connexion', 'débit'], billing: ['facture', 'paiement', 'prix', 'coût', 'tarif'], service: ['support', 'aide', 'assistance', 'service'], product: ['forfait', 'offre', 'abonnement', 'option'] }; const result = {}; Object.keys(categories).forEach(category => { result[category] = topics.filter(topic => categories[category].some(keyword => topic.toLowerCase().includes(keyword) ) ); }); return result; } /** * Assess text readability */ assessReadability(text) { try { const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0); const words = this.tokenizer.tokenize(text); const syllables = this.countSyllables(text); const avgWordsPerSentence = words.length / sentences.length; const avgSyllablesPerWord = syllables / words.length; // Simplified readability score const readabilityScore = 206.835 - (1.015 * avgWordsPerSentence) - (84.6 * avgSyllablesPerWord); let level = 'difficult'; if (readabilityScore >= 90) level = 'very easy'; else if (readabilityScore >= 80) level = 'easy'; else if (readabilityScore >= 70) level = 'fairly easy'; else if (readabilityScore >= 60) level = 'standard'; else if (readabilityScore >= 50) level = 'fairly difficult'; return { score: Math.round(readabilityScore), level, avgWordsPerSentence: Math.round(avgWordsPerSentence), avgSyllablesPerWord: Math.round(avgSyllablesPerWord * 100) / 100, totalWords: words.length, totalSentences: sentences.length }; } catch (error) { logger.error('[FAILED] Readability assessment failed:', error); return { score: 50, level: 'standard' }; } } /** * Count syllables in text (simplified French syllable counting) */ countSyllables(text) { const words = this.tokenizer.tokenize(text.toLowerCase()); return words.reduce((total, word) => { // Simple vowel counting for French const vowels = word.match(/[aeiouyàáâäèéêëìíîïòóôöùúûü]/g); return total + (vowels ? vowels.length : 1); }, 0); } /** * Perform advanced analysis using OpenAI */ async performAdvancedAnalysis(text) { try { if (!this.openai) { return { error: 'OpenAI not configured' }; } const prompt = `Analyze the following French customer service text and provide: 1. Intent classification 2. Sentiment analysis 3. Key issues mentioned 4. Suggested response tone 5. Priority level Text: "${text}" Respond in JSON format.`; const response = await this.openai.chat.completions.create({ model: 'gpt-3.5-turbo', messages: [{ role: 'user', content: prompt }], max_tokens: 500, temperature: 0.3 }); return JSON.parse(response.choices[0].message.content); } catch (error) { logger.error('[FAILED] Advanced analysis failed:', error); return { error: 'Advanced analysis unavailable' }; } } /** * Health check */ isHealthy() { return this.isInitialized; } /** * Cleanup resources */ async cleanup() { try { this.isInitialized = false; logger.info('[COMPLETE] Text Processing Service cleanup completed'); } catch (error) { logger.error('[FAILED] Text Processing Service cleanup failed:', error); throw error; } } } module.exports = TextProcessingService;