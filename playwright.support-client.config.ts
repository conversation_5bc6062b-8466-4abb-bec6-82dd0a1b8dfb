import { defineConfig, devices } from '@playwright/test'; /** * Playwright Configuration for Support Client Page Testing * Focused on modernized features validation */ export default defineConfig({ testDir: './tests/e2e', testMatch: '**/support-client-modernized.spec.ts', /* Run tests in files in parallel */ fullyParallel: true, /* Fail the build on CI if you accidentally left test.only in the source code. */ forbidOnly: !!process.env.CI, /* Retry on CI only */ retries: process.env.CI ? 2 : 0, /* Opt out of parallel tests on CI. */ workers: process.env.CI ? 1 : undefined, /* Reporter to use. See https://playwright.dev/docs/test-reporters */ reporter: [ ['html', { outputFolder: 'test-results/support-client-report' }], ['json', { outputFile: 'test-results/support-client-results.json' }], ['junit', { outputFile: 'test-results/support-client-junit.xml' }], ['list'] ], /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */ use: { /* Base URL to use in actions like `await page.goto('/')`. */ baseURL: process.env.BASE_URL || 'http://localhost:3001', /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */ trace: 'on-first-retry', /* Take screenshot on failure */ screenshot: 'only-on-failure', /* Record video on failure */ video: 'retain-on-failure', /* Global timeout for each action */ actionTimeout: 10000, /* Global timeout for navigation */ navigationTimeout: 30000, }, /* Configure projects for major browsers */ projects: [ { name: 'chromium-desktop', use: { ...devices['Desktop Chrome'], viewport: { width: 1920, height: 1080 } }, }, { name: 'firefox-desktop', use: { ...devices['Desktop Firefox'], viewport: { width: 1920, height: 1080 } }, }, { name: 'webkit-desktop', use: { ...devices['Desktop Safari'], viewport: { width: 1920, height: 1080 } }, }, /* Test against mobile viewports. */ { name: 'mobile-chrome', use: { ...devices['Pixel 5'] }, }, { name: 'mobile-safari', use: { ...devices['iPhone 12'] }, }, /* Test against tablet viewports. */ { name: 'tablet-chrome', use: { ...devices['Desktop Chrome'], viewport: { width: 768, height: 1024 } }, }, ], /* Global setup and teardown */ globalSetup: require.resolve('./tests/global-setup.ts'), globalTeardown: require.resolve('./tests/global-teardown.ts'), /* Folder for test artifacts such as screenshots, videos, traces, etc. */ outputDir: 'test-results/support-client-artifacts', /* Configure test timeout */ timeout: 60000, expect: { timeout: 10000 }, /* Configure web server for local testing */ webServer: { command: 'npm run start:frontend', port: 3001, reuseExistingServer: !process.env.CI, timeout: 120000, }, });