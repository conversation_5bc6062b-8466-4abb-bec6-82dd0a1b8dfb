{"name": "free-mobile-chatbot", "version": "1.0.0", "description": "Chatbot Assistant Support pour Free Mobile - Architecture complète", "main": "index.js", "scripts": {"start": "concurrently \"npm run start:services\" \"npm run start:backend\" \"npm run start:frontend\"", "start:services": "docker-compose up -d", "start:backend": "cd backend && npm install && npm run dev", "start:frontend": "cd frontend && npm install && npm start", "start:backend-only": "cd backend && npm run dev", "start:frontend-only": "cd frontend && npm start", "start:ml-service": "cd ml-service && python -m uvicorn app.main:app --host 0.0.0.0 --port 5001", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install", "install:backend": "cd backend && npm install", "install:frontend": "cd frontend && npm install", "dev": "npm run start:services && sleep 10 && concurrently \"npm run start:backend-only\" \"npm run start:frontend-only\"", "build": "cd frontend && npm run build", "build:backend": "cd backend && npm install", "build:frontend": "cd frontend && npm run build", "test": "cd frontend && npm test -- --coverage --watchAll=false", "test:prod": "cd frontend && npm test -- --coverage --watchAll=false", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test -- --coverage --watchAll=false", "test:e2e": "playwright test", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:ui": "playwright test --ui", "test:smoke": "playwright test --grep @smoke", "test:regression": "playwright test --grep @regression", "test:ai-services": "playwright test tests/e2e/ai-services/", "test:infrastructure": "playwright test tests/e2e/infrastructure/", "test:analytics": "playwright test tests/e2e/analytics/", "test:security": "playwright test tests/e2e/security/", "test:performance": "playwright test tests/e2e/performance/", "test:accessibility": "playwright test tests/e2e/accessibility/", "test:cross-browser": "playwright test --project=chromium --project=firefox --project=webkit --project=edge", "setup:env": "node scripts/setup-environment.js", "setup:env:dev": "node scripts/setup-environment.js development", "setup:env:staging": "node scripts/setup-environment.js staging", "setup:env:prod": "node scripts/setup-environment.js production", "validate:env": "node scripts/setup-environment.js validate", "test:mobile": "playwright test --project=\"Mobile Chrome\" --project=\"Mobile Safari\"", "test:report": "playwright show-report", "test:install": "playwright install", "test:install-deps": "playwright install-deps", "test:codegen": "playwright codegen", "test:trace": "playwright show-trace", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:e2e:auth": "playwright test tests/e2e/auth.spec.js", "test:e2e:navigation": "playwright test tests/e2e/navigation.spec.js", "test:e2e:chat": "playwright test tests/e2e/chat.spec.js", "test:e2e:responsive": "playwright test tests/e2e/responsive.spec.js", "test:e2e:mobile": "playwright test --project=mobile-chrome --project=mobile-safari", "test:e2e:desktop": "playwright test --project=chromium-desktop --project=firefox-desktop", "test:e2e:cross-browser": "playwright test --project=chromium-desktop --project=firefox-desktop --project=webkit-desktop", "test:e2e:ci": "playwright test --reporter=github --reporter=html", "test:all": "./tests/scripts/run-tests.sh all", "test:ci": "./tests/scripts/run-tests.sh all --workers=2 --retries=3", "test:legacy:security": "npx playwright test tests/security-2fa.test.js", "test:legacy:agent": "npx playwright test tests/agent-interface.test.js", "test:legacy:intelligent": "npx playwright test tests/intelligent-modes.test.js", "test:legacy:integration": "npx playwright test tests/integration-complete.test.js", "test:legacy:regression": "npx playwright test tests/backend.test.js", "validate:production": "npm run test && echo '[COMPLETE] Système validé pour production'", "quality:check": "node scripts/code-quality-check.js", "quality:fix": "npm run quality:check && npm audit fix", "setup:prod:secure": "node scripts/secure-production-setup.js", "deploy:production:secure": "node scripts/deploy-production-secure.js", "production:start": "docker-compose -f docker-compose.prod.yml up -d", "production:stop": "docker-compose -f docker-compose.prod.yml down", "production:logs": "docker-compose -f docker-compose.prod.yml logs -f", "production:build": "docker-compose -f docker-compose.prod.yml build --no-cache", "production:restart": "npm run production:stop && npm run production:start", "production:health": "curl -f http://localhost:5000/health || exit 1", "backup:create": "mkdir -p backups/$(date +%Y%m%d_%H%M%S) && docker exec $(docker ps -qf 'name=mongodb') mongodump --out /tmp/backup", "stop:services": "docker-compose down", "logs:services": "docker-compose logs -f", "reset:db": "docker-compose down -v && docker-compose up -d mongodb redis", "train:rasa": "cd rasa && docker run --rm -v $(pwd):/app rasa/rasa:latest-full train", "shell:rasa": "cd rasa && docker run -it --rm -v $(pwd):/app rasa/rasa:latest-full shell"}, "keywords": ["chatbot", "free-mobile", "sav", "customer-support", "react", "nodejs", "mongodb", "rasa", "openai"], "author": "Free Mobile", "license": "MIT", "dependencies": {"concurrently": "^8.2.2", "mongodb": "^6.18.0", "mongoose": "^8.17.1"}, "devDependencies": {"@axe-core/playwright": "^4.8.0", "@playwright/test": "^1.54.1", "@types/node": "^20.0.0", "allure-playwright": "^2.10.0", "chai": "^5.2.1", "glob": "^11.0.3", "sinon": "^21.0.0", "supertest": "^7.1.4", "typescript": "^5.0.0"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}