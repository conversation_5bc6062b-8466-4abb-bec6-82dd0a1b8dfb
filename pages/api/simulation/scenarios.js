/** * Simulation Scenarios API Route * Handles CRUD operations for training scenarios */ import { DatabaseOperations, Collections } from '../../../lib/mongodb'; import { ObjectId } from 'mongodb'; export default async function handler(req, res) { try { switch (req.method) { case 'GET': return await getScenarios(req, res); case 'POST': return await createScenario(req, res); case 'PUT': return await updateScenario(req, res); case 'DELETE': return await deleteScenario(req, res); default: return res.status(405).json({ error: 'Method not allowed' }); } } catch (error) { console.error('[FAILED] Scenarios API error:', error); return res.status(500).json({ error: 'Internal server error', message: error.message }); } } async function getScenarios(req, res) { const { category, difficulty, tags, search, page = 1, limit = 20, sortBy = 'createdAt', sortOrder = 'desc' } = req.query; // Build query const query = { isActive: true }; if (category) query.category = category; if (difficulty) query.difficulty = difficulty; if (tags) query.tags = { $in: tags.split(',') }; if (search) { query.$or = [ { title: { $regex: search, $options: 'i' } }, { description: { $regex: search, $options: 'i' } }, { tags: { $regex: search, $options: 'i' } } ]; } // Build sort const sort = {}; sort[sortBy] = sortOrder === 'desc' ? -1 : 1; // Calculate pagination const skip = (parseInt(page) - 1) * parseInt(limit); try { const [scenarios, total] = await Promise.all([ DatabaseOperations.find( Collections.SIMULATION_SCENARIOS, query, { sort, skip, limit: parseInt(limit), projection: { title: 1, description: 1, category: 1, difficulty: 1, tags: 1, estimatedDuration: 1, learningObjectives: 1, successCriteria: 1, createdAt: 1, updatedAt: 1 } } ), DatabaseOperations.countDocuments(Collections.SIMULATION_SCENARIOS, query) ]); return res.status(200).json({ success: true, data: { scenarios, pagination: { page: parseInt(page), limit: parseInt(limit), total, pages: Math.ceil(total / parseInt(limit)) } } }); } catch (error) { throw error; } } async function createScenario(req, res) { const { title, description, category, difficulty, tags = [], estimatedDuration, learningObjectives = [], customerProfile, successCriteria, messages = [] } = req.body; // Validation if (!title || !description || !category || !difficulty) { return res.status(400).json({ error: 'Missing required fields', required: ['title', 'description', 'category', 'difficulty'] }); } const scenarioData = { title, description, category, difficulty, tags, estimatedDuration: parseInt(estimatedDuration) || 15, learningObjectives, customerProfile: customerProfile || {}, successCriteria: successCriteria || {}, messages, isActive: true, usageCount: 0, averageScore: 0, completionRate: 0 }; try { const result = await DatabaseOperations.insertOne( Collections.SIMULATION_SCENARIOS, scenarioData ); return res.status(201).json({ success: true, data: { id: result.insertedId, ...scenarioData } }); } catch (error) { throw error; } } async function updateScenario(req, res) { const { id } = req.query; const updateData = req.body; if (!id || !ObjectId.isValid(id)) { return res.status(400).json({ error: 'Invalid scenario ID' }); } // Remove fields that shouldn't be updated directly delete updateData._id; delete updateData.createdAt; delete updateData.usageCount; try { const result = await DatabaseOperations.updateOne( Collections.SIMULATION_SCENARIOS, { _id: new ObjectId(id) }, { $set: updateData } ); if (result.matchedCount === 0) { return res.status(404).json({ error: 'Scenario not found' }); } return res.status(200).json({ success: true, message: 'Scenario updated successfully' }); } catch (error) { throw error; } } async function deleteScenario(req, res) { const { id } = req.query; if (!id || !ObjectId.isValid(id)) { return res.status(400).json({ error: 'Invalid scenario ID' }); } try { // Soft delete by setting isActive to false const result = await DatabaseOperations.updateOne( Collections.SIMULATION_SCENARIOS, { _id: new ObjectId(id) }, { $set: { isActive: false } } ); if (result.matchedCount === 0) { return res.status(404).json({ error: 'Scenario not found' }); } return res.status(200).json({ success: true, message: 'Scenario deleted successfully' }); } catch (error) { throw error; } }