/** * System Health Check API Route * Validates database connectivity and system status */ import { healthCheck } from '../../../lib/mongodb'; export default async function handler(req, res) { // Only allow GET requests if (req.method !== 'GET') { return res.status(405).json({ error: 'Method not allowed', message: 'Only GET requests are allowed for health checks' }); } try { const startTime = Date.now(); // Check database connectivity const dbHealth = await healthCheck(); // Calculate response time const responseTime = Date.now() - startTime; // System information const systemInfo = { timestamp: new Date().toISOString(), environment: process.env.NODE_ENV || 'development', version: process.env.npm_package_version || '1.0.0', uptime: process.uptime(), memory: process.memoryUsage(), responseTime: `${responseTime}ms`, }; // Enhanced features status const featuresStatus = { simulationTraining: process.env.ENABLE_SIMULATION_TRAINING === 'true', predictiveAnalytics: process.env.ENABLE_PREDICTIVE_ANALYTICS === 'true', aiAssistance: process.env.ENABLE_AI_ASSISTANCE === 'true', comprehensiveAnalytics: process.env.ENABLE_COMPREHENSIVE_ANALYTICS === 'true', }; // Overall health status const isHealthy = dbHealth.status === 'healthy' && responseTime < 5000; const healthStatus = { status: isHealthy ? 'healthy' : 'unhealthy', database: dbHealth, system: systemInfo, features: featuresStatus, checks: { database: dbHealth.status === 'healthy', responseTime: responseTime < 5000, memory: systemInfo.memory.heapUsed < 500 * 1024 * 1024, // 500MB limit } }; // Set appropriate status code const statusCode = isHealthy ? 200 : 503; // Set cache headers res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate'); res.setHeader('Pragma', 'no-cache'); res.setHeader('Expires', '0'); return res.status(statusCode).json(healthStatus); } catch (error) { console.error('[FAILED] Health check failed:', error); return res.status(503).json({ status: 'unhealthy', error: error.message, timestamp: new Date().toISOString(), database: { status: 'unhealthy', error: error.message }, system: { timestamp: new Date().toISOString(), environment: process.env.NODE_ENV || 'development', uptime: process.uptime(), } }); } }