# =============================================
# 📞 CALL SYSTEM DOCKERFILE
# Multi-stage build for voice communication service
# Asterisk-based with Node.js integration
# =============================================

# Build stage
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Install build dependencies
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git \
    curl

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy source code
COPY . .

# Production stage
FROM node:18-alpine AS production

# Install Asterisk and runtime dependencies
RUN apk add --no-cache \
    asterisk \
    asterisk-sounds-en \
    asterisk-sounds-fr \
    sox \
    curl \
    dumb-init \
    tzdata \
    && rm -rf /var/cache/apk/*

# Create non-root user
RUN addgroup -g 1001 -S callsystem && \
    adduser -S calluser -u 1001 -G callsystem

# Set working directory
WORKDIR /app

# Copy built application from builder stage
COPY --from=builder --chown=calluser:callsystem /app/node_modules ./node_modules
COPY --from=builder --chown=calluser:callsystem /app/package*.json ./
COPY --chown=calluser:callsystem . .

# Create necessary directories
RUN mkdir -p /app/logs /app/recordings /app/temp /var/lib/asterisk/sounds/custom && \
    chown -R calluser:callsystem /app /var/lib/asterisk/sounds/custom

# Copy Asterisk configuration
COPY --chown=calluser:callsystem asterisk-config/ /etc/asterisk/

# Set proper permissions for Asterisk
RUN chown -R calluser:callsystem /etc/asterisk /var/lib/asterisk /var/log/asterisk /var/run/asterisk /var/spool/asterisk

# Set environment variables
ENV NODE_ENV=production
ENV PORT=5004
ENV ASTERISK_USER=calluser
ENV TZ=Europe/Paris

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:5004/health || exit 1

# Switch to non-root user
USER calluser

# Expose ports
EXPOSE 5004 5060/udp 10000-10100/udp

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start both Asterisk and Node.js application
CMD ["sh", "-c", "asterisk -f & node src/server.js"]

# Labels for metadata
LABEL maintainer="Free Mobile Chatbot Team"
LABEL version="1.0.0"
LABEL description="Call System Service for Free Mobile Chatbot - Voice Communication"
LABEL org.opencontainers.image.source="https://github.com/free-mobile/chatbot"
