# =====================================================
# ChatbotRNCP Automated Testing & Validation Script
# =====================================================
# Comprehensive testing automation for the ChatbotRNCP application
# Includes service startup, Playwright testing, and validation

param(
    [string]$TestSuite = "all",  # Options: all, unit, e2e, integration, performance
    [string]$Browser = "all",    # Options: all, chromium, firefox, webkit
    [switch]$SkipBuild,         # Skip Docker build step
    [switch]$KeepServices,      # Keep services running after tests
    [switch]$Verbose,           # Enable verbose logging
    [string]$ReportFormat = "html,json,junit"  # Report formats
)

# Configuration
$ErrorActionPreference = "Stop"
$ProgressPreference = "SilentlyContinue"

# Colors for output
$Colors = @{
    Red = "Red"
    Green = "Green"
    Yellow = "Yellow"
    Blue = "Blue"
    Cyan = "Cyan"
    Magenta = "Magenta"
}

# Paths
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir
$FrontendDir = Join-Path $ProjectRoot "frontend"
$BackendDir = Join-Path $ProjectRoot "backend"
$TestResultsDir = Join-Path $FrontendDir "test-results"
$ReportsDir = Join-Path $ProjectRoot "reports"

# Test configuration
$TestConfig = @{
    Timeout = 300  # 5 minutes
    RetryCount = 2
    HealthCheckInterval = 5
    MaxHealthCheckAttempts = 24  # 2 minutes total
}

# Functions
function Write-Log {
    param([string]$Message, [string]$Level = "INFO", [string]$Color = "White")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    Write-Host $logMessage -ForegroundColor $Color
    
    # Also log to file
    $logFile = Join-Path $ReportsDir "test-execution.log"
    Add-Content -Path $logFile -Value $logMessage
}

function Write-Success {
    param([string]$Message)
    Write-Log $Message "SUCCESS" $Colors.Green
}

function Write-Error {
    param([string]$Message)
    Write-Log $Message "ERROR" $Colors.Red
}

function Write-Warning {
    param([string]$Message)
    Write-Log $Message "WARNING" $Colors.Yellow
}

function Write-Info {
    param([string]$Message)
    Write-Log $Message "INFO" $Colors.Blue
}

function Initialize-Environment {
    Write-Info "Initializing test environment..."
    
    # Create necessary directories
    @($TestResultsDir, $ReportsDir) | ForEach-Object {
        if (-not (Test-Path $_)) {
            New-Item -ItemType Directory -Path $_ -Force | Out-Null
        }
    }
    
    # Clear previous test results
    if (Test-Path $TestResultsDir) {
        Remove-Item "$TestResultsDir\*" -Recurse -Force -ErrorAction SilentlyContinue
    }
    
    Write-Success "Environment initialized"
}

function Test-Prerequisites {
    Write-Info "Checking prerequisites..."
    
    # Check Docker
    try {
        $dockerVersion = docker --version
        Write-Success "Docker found: $dockerVersion"
    }
    catch {
        Write-Error "Docker not found. Please install Docker Desktop."
        exit 1
    }
    
    # Check Docker Compose
    try {
        $composeVersion = docker compose version
        Write-Success "Docker Compose found: $composeVersion"
    }
    catch {
        Write-Error "Docker Compose not found. Please install Docker Compose."
        exit 1
    }
    
    # Check Node.js
    try {
        $nodeVersion = node --version
        Write-Success "Node.js found: $nodeVersion"
    }
    catch {
        Write-Error "Node.js not found. Please install Node.js."
        exit 1
    }
    
    # Check if ports are available
    $requiredPorts = @(3000, 3001, 5000, 27017, 6379)
    foreach ($port in $requiredPorts) {
        $connection = Test-NetConnection -ComputerName localhost -Port $port -InformationLevel Quiet -WarningAction SilentlyContinue
        if ($connection) {
            Write-Warning "Port $port is already in use. Services may conflict."
        }
    }
    
    Write-Success "Prerequisites check completed"
}

function Start-Services {
    Write-Info "Starting ChatbotRNCP services..."
    
    Set-Location $ProjectRoot
    
    if (-not $SkipBuild) {
        Write-Info "Building Docker images..."
        try {
            docker compose -f docker-compose.prod.yml build --no-cache
            Write-Success "Docker images built successfully"
        }
        catch {
            Write-Error "Failed to build Docker images: $_"
            exit 1
        }
    }
    
    Write-Info "Starting services with Docker Compose..."
    try {
        docker compose -f docker-compose.prod.yml up -d
        Write-Success "Services started successfully"
    }
    catch {
        Write-Error "Failed to start services: $_"
        exit 1
    }
    
    # Wait for services to be healthy
    Wait-ForServices
}

function Wait-ForServices {
    Write-Info "Waiting for services to be ready..."
    
    $services = @(
        @{ Name = "Backend"; Url = "http://localhost:5000/health"; ExpectedStatus = 200 },
        @{ Name = "Frontend"; Url = "http://localhost:3001/"; ExpectedStatus = 200 },
        @{ Name = "MongoDB"; Url = "http://localhost:5000/api/health/database"; ExpectedStatus = 200 }
    )
    
    $attempt = 0
    $allHealthy = $false
    
    while (-not $allHealthy -and $attempt -lt $TestConfig.MaxHealthCheckAttempts) {
        $attempt++
        Write-Info "Health check attempt $attempt/$($TestConfig.MaxHealthCheckAttempts)..."
        
        $healthyServices = 0
        
        foreach ($service in $services) {
            try {
                $response = Invoke-WebRequest -Uri $service.Url -Method GET -TimeoutSec 10 -UseBasicParsing
                if ($response.StatusCode -eq $service.ExpectedStatus) {
                    Write-Success "✓ $($service.Name) is healthy"
                    $healthyServices++
                }
                else {
                    Write-Warning "✗ $($service.Name) returned status $($response.StatusCode)"
                }
            }
            catch {
                Write-Warning "✗ $($service.Name) is not responding"
            }
        }
        
        if ($healthyServices -eq $services.Count) {
            $allHealthy = $true
            Write-Success "All services are healthy and ready"
        }
        else {
            Start-Sleep -Seconds $TestConfig.HealthCheckInterval
        }
    }
    
    if (-not $allHealthy) {
        Write-Error "Services failed to become healthy within the timeout period"
        Show-ServiceLogs
        exit 1
    }
}

function Show-ServiceLogs {
    Write-Info "Showing service logs for debugging..."
    
    try {
        Set-Location $ProjectRoot
        docker compose -f docker-compose.prod.yml logs --tail=50
    }
    catch {
        Write-Warning "Could not retrieve service logs: $_"
    }
}

function Install-Dependencies {
    Write-Info "Installing test dependencies..."
    
    # Frontend dependencies
    Set-Location $FrontendDir
    try {
        if (-not (Test-Path "node_modules")) {
            Write-Info "Installing frontend dependencies..."
            npm install
        }
        
        # Install Playwright browsers if needed
        Write-Info "Installing Playwright browsers..."
        npx playwright install
        
        Write-Success "Dependencies installed successfully"
    }
    catch {
        Write-Error "Failed to install dependencies: $_"
        exit 1
    }
}

function Invoke-UnitTests {
    Write-Info "Running unit tests..."
    
    Set-Location $FrontendDir
    try {
        $testResult = npm test -- --coverage --watchAll=false --testResultsProcessor=jest-junit
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Unit tests passed"
            return $true
        }
        else {
            Write-Error "Unit tests failed"
            return $false
        }
    }
    catch {
        Write-Error "Failed to run unit tests: $_"
        return $false
    }
}

function Invoke-PlaywrightTests {
    param([string]$BrowserFilter = "all")

    Write-Info "Running Playwright E2E tests..."

    Set-Location $FrontendDir

    # Prepare Playwright command
    $playwrightCmd = "npx playwright test"

    # Add browser filter
    if ($BrowserFilter -ne "all") {
        $playwrightCmd += " --project=$BrowserFilter"
    }

    # Add reporter configuration
    $reporters = $ReportFormat.Split(',')
    foreach ($reporter in $reporters) {
        $playwrightCmd += " --reporter=$reporter"
    }

    # Add verbose flag if requested
    if ($Verbose) {
        $playwrightCmd += " --verbose"
    }

    try {
        Write-Info "Executing: $playwrightCmd"
        Invoke-Expression $playwrightCmd

        if ($LASTEXITCODE -eq 0) {
            Write-Success "Playwright tests passed"
            return $true
        }
        else {
            Write-Error "Playwright tests failed"
            return $false
        }
    }
    catch {
        Write-Error "Failed to run Playwright tests: $_"
        return $false
    }
}

function Invoke-PerformanceTests {
    Write-Info "Running performance tests..."

    $performanceResults = @()

    # Test API response times
    $apiEndpoints = @(
        "http://localhost:5000/health",
        "http://localhost:5000/api/auth/status",
        "http://localhost:3001/"
    )

    foreach ($endpoint in $apiEndpoints) {
        try {
            $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
            $response = Invoke-WebRequest -Uri $endpoint -Method GET -TimeoutSec 30 -UseBasicParsing
            $stopwatch.Stop()

            $result = @{
                Endpoint = $endpoint
                StatusCode = $response.StatusCode
                ResponseTime = $stopwatch.ElapsedMilliseconds
                Success = $response.StatusCode -eq 200
            }

            $performanceResults += $result

            if ($result.Success -and $result.ResponseTime -lt 2000) {
                Write-Success "✓ $endpoint - ${$result.ResponseTime}ms"
            }
            elseif ($result.Success) {
                Write-Warning "⚠ $endpoint - ${$result.ResponseTime}ms (slow)"
            }
            else {
                Write-Error "✗ $endpoint - Status: $($result.StatusCode)"
            }
        }
        catch {
            Write-Error "✗ $endpoint - Failed: $_"
            $performanceResults += @{
                Endpoint = $endpoint
                StatusCode = 0
                ResponseTime = -1
                Success = $false
                Error = $_.Exception.Message
            }
        }
    }

    # Save performance results
    $performanceReport = Join-Path $ReportsDir "performance-results.json"
    $performanceResults | ConvertTo-Json -Depth 3 | Out-File $performanceReport

    $successfulTests = ($performanceResults | Where-Object { $_.Success }).Count
    $totalTests = $performanceResults.Count

    Write-Info "Performance test results: $successfulTests/$totalTests endpoints passed"
    return $successfulTests -eq $totalTests
}

function Invoke-SecurityTests {
    Write-Info "Running security validation tests..."

    $securityResults = @()

    # Test security headers
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:3001/" -Method GET -UseBasicParsing
        $headers = $response.Headers

        $requiredHeaders = @(
            "X-Frame-Options",
            "X-Content-Type-Options",
            "X-XSS-Protection"
        )

        foreach ($header in $requiredHeaders) {
            $present = $headers.ContainsKey($header)
            $securityResults += @{
                Test = "Security Header: $header"
                Success = $present
                Details = if ($present) { $headers[$header] } else { "Missing" }
            }

            if ($present) {
                Write-Success "✓ Security header $header present"
            }
            else {
                Write-Warning "⚠ Security header $header missing"
            }
        }
    }
    catch {
        Write-Error "Failed to test security headers: $_"
    }

    # Test rate limiting (simplified)
    try {
        Write-Info "Testing rate limiting..."
        $rateLimitHit = $false

        for ($i = 1; $i -le 15; $i++) {
            try {
                $response = Invoke-WebRequest -Uri "http://localhost:5000/api/auth/status" -Method GET -UseBasicParsing -TimeoutSec 5
                if ($response.StatusCode -eq 429) {
                    $rateLimitHit = $true
                    break
                }
            }
            catch {
                if ($_.Exception.Response.StatusCode -eq 429) {
                    $rateLimitHit = $true
                    break
                }
            }
            Start-Sleep -Milliseconds 100
        }

        $securityResults += @{
            Test = "Rate Limiting"
            Success = $rateLimitHit
            Details = if ($rateLimitHit) { "Rate limit enforced" } else { "Rate limit not triggered" }
        }

        if ($rateLimitHit) {
            Write-Success "✓ Rate limiting is working"
        }
        else {
            Write-Warning "⚠ Rate limiting not triggered (may need adjustment)"
        }
    }
    catch {
        Write-Warning "Could not test rate limiting: $_"
    }

    # Save security results
    $securityReport = Join-Path $ReportsDir "security-results.json"
    $securityResults | ConvertTo-Json -Depth 3 | Out-File $securityReport

    $successfulTests = ($securityResults | Where-Object { $_.Success }).Count
    $totalTests = $securityResults.Count

    Write-Info "Security test results: $successfulTests/$totalTests tests passed"
    return $successfulTests -gt 0  # At least some security measures should be in place
}

function Generate-TestReport {
    Write-Info "Generating comprehensive test report..."

    $reportData = @{
        Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        TestSuite = $TestSuite
        Browser = $Browser
        Environment = @{
            OS = $env:OS
            PowerShellVersion = $PSVersionTable.PSVersion.ToString()
            DockerVersion = (docker --version)
        }
        Results = @{}
    }

    # Collect Playwright results if available
    $playwrightResults = Join-Path $TestResultsDir "results.json"
    if (Test-Path $playwrightResults) {
        $reportData.Results.Playwright = Get-Content $playwrightResults | ConvertFrom-Json
    }

    # Collect performance results
    $performanceResults = Join-Path $ReportsDir "performance-results.json"
    if (Test-Path $performanceResults) {
        $reportData.Results.Performance = Get-Content $performanceResults | ConvertFrom-Json
    }

    # Collect security results
    $securityResults = Join-Path $ReportsDir "security-results.json"
    if (Test-Path $securityResults) {
        $reportData.Results.Security = Get-Content $securityResults | ConvertFrom-Json
    }

    # Generate final report
    $finalReport = Join-Path $ReportsDir "comprehensive-test-report.json"
    $reportData | ConvertTo-Json -Depth 5 | Out-File $finalReport

    # Generate HTML summary
    Generate-HtmlReport $reportData

    Write-Success "Test report generated: $finalReport"
}

function Generate-HtmlReport {
    param($ReportData)

    $htmlContent = @"
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatbotRNCP Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #333; border-bottom: 2px solid #007acc; padding-bottom: 20px; margin-bottom: 30px; }
        .section { margin: 20px 0; padding: 15px; border-left: 4px solid #007acc; background-color: #f9f9f9; }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { color: #17a2b8; font-weight: bold; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #007acc; color: white; }
        .status-pass { background-color: #d4edda; color: #155724; }
        .status-fail { background-color: #f8d7da; color: #721c24; }
        .status-warn { background-color: #fff3cd; color: #856404; }
        .summary-card { display: inline-block; margin: 10px; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 8px; text-align: center; min-width: 150px; }
        .summary-number { font-size: 2em; font-weight: bold; display: block; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 ChatbotRNCP Test Report</h1>
            <p>Generated on: $($ReportData.Timestamp)</p>
            <p>Test Suite: $($ReportData.TestSuite) | Browser: $($ReportData.Browser)</p>
        </div>

        <div class="section">
            <h2>📊 Test Summary</h2>
            <div class="summary-card">
                <span class="summary-number">✓</span>
                <span>Tests Executed</span>
            </div>
            <div class="summary-card">
                <span class="summary-number">⚡</span>
                <span>Performance</span>
            </div>
            <div class="summary-card">
                <span class="summary-number">🔒</span>
                <span>Security</span>
            </div>
        </div>

        <div class="section">
            <h2>🌐 Environment Information</h2>
            <table>
                <tr><th>Property</th><th>Value</th></tr>
                <tr><td>Operating System</td><td>$($ReportData.Environment.OS)</td></tr>
                <tr><td>PowerShell Version</td><td>$($ReportData.Environment.PowerShellVersion)</td></tr>
                <tr><td>Docker Version</td><td>$($ReportData.Environment.DockerVersion)</td></tr>
            </table>
        </div>

        <div class="section">
            <h2>📝 Test Results Details</h2>
            <p>Detailed test results are available in the JSON report files.</p>
            <ul>
                <li><strong>Playwright Results:</strong> test-results/results.json</li>
                <li><strong>Performance Results:</strong> reports/performance-results.json</li>
                <li><strong>Security Results:</strong> reports/security-results.json</li>
            </ul>
        </div>

        <div class="section">
            <h2>🎯 Recommendations</h2>
            <ul>
                <li>Review any failed tests and address underlying issues</li>
                <li>Monitor performance metrics regularly</li>
                <li>Keep security measures up to date</li>
                <li>Run tests before each deployment</li>
            </ul>
        </div>
    </div>
</body>
</html>
"@

    $htmlReport = Join-Path $ReportsDir "test-report.html"
    $htmlContent | Out-File $htmlReport -Encoding UTF8
    Write-Success "HTML report generated: $htmlReport"
}

function Stop-Services {
    if (-not $KeepServices) {
        Write-Info "Stopping services..."
        try {
            Set-Location $ProjectRoot
            docker compose -f docker-compose.prod.yml down
            Write-Success "Services stopped successfully"
        }
        catch {
            Write-Warning "Failed to stop services: $_"
        }
    }
    else {
        Write-Info "Keeping services running as requested"
    }
}

function Show-TestSummary {
    param($Results)

    Write-Host "`n" -NoNewline
    Write-Host "=" * 60 -ForegroundColor $Colors.Cyan
    Write-Host "                    TEST EXECUTION SUMMARY" -ForegroundColor $Colors.Cyan
    Write-Host "=" * 60 -ForegroundColor $Colors.Cyan

    $totalTests = 0
    $passedTests = 0

    foreach ($result in $Results.GetEnumerator()) {
        $testName = $result.Key
        $testResult = $result.Value

        Write-Host "`n📋 $testName Tests:" -ForegroundColor $Colors.Blue

        if ($testResult) {
            Write-Host "   ✅ PASSED" -ForegroundColor $Colors.Green
            $passedTests++
        }
        else {
            Write-Host "   ❌ FAILED" -ForegroundColor $Colors.Red
        }
        $totalTests++
    }

    Write-Host "`n" -NoNewline
    Write-Host "🎯 OVERALL RESULT: " -NoNewline -ForegroundColor $Colors.Blue

    if ($passedTests -eq $totalTests) {
        Write-Host "ALL TESTS PASSED ✅" -ForegroundColor $Colors.Green
    }
    elseif ($passedTests -gt 0) {
        Write-Host "PARTIAL SUCCESS ⚠️ ($passedTests/$totalTests)" -ForegroundColor $Colors.Yellow
    }
    else {
        Write-Host "ALL TESTS FAILED ❌" -ForegroundColor $Colors.Red
    }

    Write-Host "`n📊 Reports generated in: $ReportsDir" -ForegroundColor $Colors.Cyan
    Write-Host "🌐 Open test-report.html in your browser for detailed results" -ForegroundColor $Colors.Cyan
    Write-Host "=" * 60 -ForegroundColor $Colors.Cyan
}

# Main execution
function Main {
    try {
        Write-Host "🚀 Starting ChatbotRNCP Automated Testing & Validation" -ForegroundColor $Colors.Cyan
        Write-Host "Test Suite: $TestSuite | Browser: $Browser" -ForegroundColor $Colors.Blue

        Initialize-Environment
        Test-Prerequisites
        Start-Services
        Install-Dependencies

        $testResults = @{}

        # Run tests based on suite selection
        switch ($TestSuite.ToLower()) {
            "all" {
                $testResults["Unit"] = Invoke-UnitTests
                $testResults["E2E"] = Invoke-PlaywrightTests -BrowserFilter $Browser
                $testResults["Performance"] = Invoke-PerformanceTests
                $testResults["Security"] = Invoke-SecurityTests
            }
            "unit" {
                $testResults["Unit"] = Invoke-UnitTests
            }
            "e2e" {
                $testResults["E2E"] = Invoke-PlaywrightTests -BrowserFilter $Browser
            }
            "integration" {
                $testResults["E2E"] = Invoke-PlaywrightTests -BrowserFilter $Browser
                $testResults["Performance"] = Invoke-PerformanceTests
            }
            "performance" {
                $testResults["Performance"] = Invoke-PerformanceTests
            }
            default {
                Write-Error "Unknown test suite: $TestSuite"
                exit 1
            }
        }

        Generate-TestReport
        Show-TestSummary $testResults

        # Determine exit code
        $failedTests = ($testResults.Values | Where-Object { -not $_ }).Count
        if ($failedTests -gt 0) {
            Write-Error "Some tests failed. Check the reports for details."
            exit 1
        }
        else {
            Write-Success "All tests passed successfully!"
            exit 0
        }
    }
    catch {
        Write-Error "Test execution failed: $_"
        exit 1
    }
    finally {
        Stop-Services
    }
}

# Script entry point
if ($MyInvocation.InvocationName -ne '.') {
    Main
}
