#!/bin/bash

# =============================================
# ✅ PRODUCTION READINESS CHECKLIST
# Free Mobile Chatbot ML Intelligence Dashboard
# Comprehensive production readiness validation
# =============================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
CHECKLIST_LOG="$PROJECT_ROOT/logs/production-readiness-$TIMESTAMP.log"

# Checklist results
declare -A checklist_results
total_checks=0
passed_checks=0
failed_checks=0
warning_checks=0

# Function to print colored output
print_header() {
    echo -e "${PURPLE}============================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}============================================${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$CHECKLIST_LOG"
}

print_success() {
    echo -e "${GREEN}[✅ PASS]${NC} $1" | tee -a "$CHECKLIST_LOG"
}

print_warning() {
    echo -e "${YELLOW}[⚠️ WARN]${NC} $1" | tee -a "$CHECKLIST_LOG"
}

print_error() {
    echo -e "${RED}[❌ FAIL]${NC} $1" | tee -a "$CHECKLIST_LOG"
}

print_step() {
    echo -e "${CYAN}[STEP]${NC} $1" | tee -a "$CHECKLIST_LOG"
}

# Function to record check result
record_check() {
    local check_name="$1"
    local status="$2"  # PASS, WARN, FAIL
    local message="$3"
    
    checklist_results["$check_name"]="$status:$message"
    total_checks=$((total_checks + 1))
    
    case "$status" in
        "PASS")
            passed_checks=$((passed_checks + 1))
            print_success "$check_name: $message"
            ;;
        "WARN")
            warning_checks=$((warning_checks + 1))
            print_warning "$check_name: $message"
            ;;
        "FAIL")
            failed_checks=$((failed_checks + 1))
            print_error "$check_name: $message"
            ;;
    esac
}

# Function to check security compliance
check_security_compliance() {
    print_step "🔒 Security Compliance Checks"
    
    # SSL/TLS Configuration
    if [[ -f "$PROJECT_ROOT/ssl/chatbot.free.fr.crt" ]] && [[ -f "$PROJECT_ROOT/ssl/chatbot.free.fr.key" ]]; then
        if openssl x509 -in "$PROJECT_ROOT/ssl/chatbot.free.fr.crt" -noout -checkend 2592000; then
            record_check "SSL Certificate" "PASS" "Valid certificate with 30+ days remaining"
        else
            record_check "SSL Certificate" "WARN" "Certificate expires within 30 days"
        fi
    else
        record_check "SSL Certificate" "FAIL" "SSL certificates not found"
    fi
    
    # Environment Variables Security
    if grep -q "CHANGEZ\|TODO\|REPLACE" "$PROJECT_ROOT/.env.production" 2>/dev/null; then
        record_check "Environment Security" "FAIL" "Default/template values found in production config"
    else
        record_check "Environment Security" "PASS" "No default values in production configuration"
    fi
    
    # JWT Secret Strength
    local jwt_secret_length=$(grep "JWT_SECRET" "$PROJECT_ROOT/.env.production" | cut -d'=' -f2 | wc -c)
    if [[ $jwt_secret_length -gt 32 ]]; then
        record_check "JWT Security" "PASS" "JWT secret is sufficiently strong"
    else
        record_check "JWT Security" "FAIL" "JWT secret is too weak (less than 32 characters)"
    fi
    
    # Rate Limiting Configuration
    if grep -q "RATE_LIMIT" "$PROJECT_ROOT/.env.production"; then
        record_check "Rate Limiting" "PASS" "Rate limiting is configured"
    else
        record_check "Rate Limiting" "WARN" "Rate limiting configuration not found"
    fi
    
    # CORS Configuration
    if grep -q "CORS_ORIGIN" "$PROJECT_ROOT/.env.production"; then
        record_check "CORS Security" "PASS" "CORS is properly configured"
    else
        record_check "CORS Security" "WARN" "CORS configuration not found"
    fi
}

# Function to check accessibility compliance
check_accessibility_compliance() {
    print_step "♿ Accessibility Compliance Checks"
    
    # Check if accessibility tests exist
    if [[ -f "$PROJECT_ROOT/tests/e2e/accessibility/wcag-compliance.spec.ts" ]]; then
        record_check "WCAG Test Suite" "PASS" "WCAG 2.1 AA test suite is available"
    else
        record_check "WCAG Test Suite" "FAIL" "WCAG accessibility tests not found"
    fi
    
    # Check for axe-core integration
    if grep -q "@axe-core/playwright" "$PROJECT_ROOT/package.json"; then
        record_check "Axe-Core Integration" "PASS" "Automated accessibility testing configured"
    else
        record_check "Axe-Core Integration" "FAIL" "Axe-core accessibility testing not configured"
    fi
    
    # Check for ARIA labels in components
    local aria_count=$(find "$PROJECT_ROOT/frontend/src" -name "*.tsx" -o -name "*.jsx" | xargs grep -l "aria-label\|role=" | wc -l)
    if [[ $aria_count -gt 10 ]]; then
        record_check "ARIA Implementation" "PASS" "ARIA labels and roles implemented in components"
    else
        record_check "ARIA Implementation" "WARN" "Limited ARIA implementation found"
    fi
    
    # Check for keyboard navigation support
    if find "$PROJECT_ROOT/frontend/src" -name "*.tsx" -o -name "*.jsx" | xargs grep -l "onKeyDown\|tabIndex" | head -1 > /dev/null; then
        record_check "Keyboard Navigation" "PASS" "Keyboard navigation support implemented"
    else
        record_check "Keyboard Navigation" "WARN" "Keyboard navigation support may be limited"
    fi
}

# Function to check Core Web Vitals
check_core_web_vitals() {
    print_step "⚡ Core Web Vitals Performance Checks"
    
    # Check if performance tests exist
    if [[ -f "$PROJECT_ROOT/tests/e2e/performance/core-web-vitals.spec.ts" ]]; then
        record_check "Performance Test Suite" "PASS" "Core Web Vitals test suite is available"
    else
        record_check "Performance Test Suite" "FAIL" "Core Web Vitals tests not found"
    fi
    
    # Check for performance monitoring configuration
    if grep -q "PERFORMANCE_MONITORING" "$PROJECT_ROOT/.env.production"; then
        record_check "Performance Monitoring" "PASS" "Performance monitoring is configured"
    else
        record_check "Performance Monitoring" "WARN" "Performance monitoring not configured"
    fi
    
    # Check for CDN configuration
    if grep -q "CDN_ENABLED=true" "$PROJECT_ROOT/.env.production"; then
        record_check "CDN Configuration" "PASS" "CDN is enabled for static assets"
    else
        record_check "CDN Configuration" "WARN" "CDN not configured for optimal performance"
    fi
    
    # Check for image optimization
    if find "$PROJECT_ROOT/frontend/public" -name "*.webp" | head -1 > /dev/null; then
        record_check "Image Optimization" "PASS" "Optimized image formats found"
    else
        record_check "Image Optimization" "WARN" "Consider using optimized image formats (WebP)"
    fi
    
    # Check for bundle optimization
    if [[ -f "$PROJECT_ROOT/frontend/webpack.config.js" ]] || [[ -f "$PROJECT_ROOT/frontend/vite.config.js" ]]; then
        record_check "Bundle Optimization" "PASS" "Build optimization configuration found"
    else
        record_check "Bundle Optimization" "WARN" "Bundle optimization configuration not found"
    fi
}

# Function to check backup and recovery procedures
check_backup_recovery() {
    print_step "💾 Backup and Recovery Procedures"
    
    # Check for backup scripts
    if [[ -f "$PROJECT_ROOT/scripts/backup-database.sh" ]]; then
        record_check "Backup Scripts" "PASS" "Database backup scripts are available"
    else
        record_check "Backup Scripts" "WARN" "Database backup scripts not found"
    fi
    
    # Check for backup directory
    if [[ -d "$PROJECT_ROOT/backups" ]]; then
        record_check "Backup Directory" "PASS" "Backup directory structure exists"
    else
        record_check "Backup Directory" "WARN" "Backup directory not found"
    fi
    
    # Check for recovery documentation
    if [[ -f "$PROJECT_ROOT/docs/disaster-recovery.md" ]]; then
        record_check "Recovery Documentation" "PASS" "Disaster recovery documentation exists"
    else
        record_check "Recovery Documentation" "WARN" "Disaster recovery documentation not found"
    fi
    
    # Check for automated backup configuration
    if grep -q "BACKUP_ENABLED=true" "$PROJECT_ROOT/.env.production"; then
        record_check "Automated Backups" "PASS" "Automated backup system is configured"
    else
        record_check "Automated Backups" "WARN" "Automated backup system not configured"
    fi
}

# Function to check monitoring dashboards
check_monitoring_dashboards() {
    print_step "📊 Monitoring Dashboard Checks"
    
    # Check for Grafana configuration
    if [[ -d "$PROJECT_ROOT/monitoring/grafana/dashboards" ]]; then
        record_check "Grafana Dashboards" "PASS" "Grafana monitoring dashboards configured"
    else
        record_check "Grafana Dashboards" "WARN" "Grafana dashboards not found"
    fi
    
    # Check for Prometheus configuration
    if [[ -f "$PROJECT_ROOT/monitoring/prometheus/prometheus.yml" ]]; then
        record_check "Prometheus Config" "PASS" "Prometheus metrics collection configured"
    else
        record_check "Prometheus Config" "WARN" "Prometheus configuration not found"
    fi
    
    # Check for alerting rules
    if [[ -f "$PROJECT_ROOT/monitoring/prometheus/alerts.yml" ]]; then
        record_check "Alerting Rules" "PASS" "Prometheus alerting rules configured"
    else
        record_check "Alerting Rules" "WARN" "Alerting rules not configured"
    fi
    
    # Check for log aggregation
    if [[ -d "$PROJECT_ROOT/monitoring/logstash" ]]; then
        record_check "Log Aggregation" "PASS" "ELK stack log aggregation configured"
    else
        record_check "Log Aggregation" "WARN" "Log aggregation not configured"
    fi
    
    # Check for health check endpoints
    if grep -q "HEALTH_CHECK_ENABLED=true" "$PROJECT_ROOT/.env.production"; then
        record_check "Health Endpoints" "PASS" "Health check endpoints are enabled"
    else
        record_check "Health Endpoints" "WARN" "Health check endpoints not configured"
    fi
}

# Function to check AI services readiness
check_ai_services_readiness() {
    print_step "🤖 AI Services Readiness"
    
    # Check for ML models
    if [[ -d "$PROJECT_ROOT/ml-models" ]]; then
        local model_count=$(find "$PROJECT_ROOT/ml-models" -name "*.pkl" -o -name "*.joblib" -o -name "*.h5" | wc -l)
        if [[ $model_count -gt 0 ]]; then
            record_check "ML Models" "PASS" "$model_count ML models found"
        else
            record_check "ML Models" "WARN" "ML model files not found"
        fi
    else
        record_check "ML Models" "WARN" "ML models directory not found"
    fi
    
    # Check for AI service configuration
    if grep -q "ML_SERVICE_ENABLED=true" "$PROJECT_ROOT/.env.production"; then
        record_check "AI Service Config" "PASS" "AI services are enabled"
    else
        record_check "AI Service Config" "WARN" "AI services configuration not found"
    fi
    
    # Check for GPU configuration
    if grep -q "ML_GPU_ENABLED=true" "$PROJECT_ROOT/.env.production"; then
        record_check "GPU Acceleration" "PASS" "GPU acceleration is enabled"
    else
        record_check "GPU Acceleration" "WARN" "GPU acceleration not enabled (CPU fallback)"
    fi
    
    # Check for model caching
    if grep -q "ML_MODEL_CACHE" "$PROJECT_ROOT/.env.production"; then
        record_check "Model Caching" "PASS" "ML model caching is configured"
    else
        record_check "Model Caching" "WARN" "ML model caching not configured"
    fi
}

# Function to check platform integrations
check_platform_integrations() {
    print_step "📱 Platform Integration Readiness"
    
    # WhatsApp Business API
    if grep -q "WHATSAPP_TOKEN" "$PROJECT_ROOT/.env.production" && ! grep -q "CHANGEZ\|TODO" "$PROJECT_ROOT/.env.production"; then
        record_check "WhatsApp Integration" "PASS" "WhatsApp Business API configured"
    else
        record_check "WhatsApp Integration" "FAIL" "WhatsApp Business API not properly configured"
    fi
    
    # Facebook Messenger
    if grep -q "FACEBOOK_PAGE_ACCESS_TOKEN" "$PROJECT_ROOT/.env.production" && ! grep -q "CHANGEZ\|TODO" "$PROJECT_ROOT/.env.production"; then
        record_check "Facebook Integration" "PASS" "Facebook Messenger API configured"
    else
        record_check "Facebook Integration" "FAIL" "Facebook Messenger API not properly configured"
    fi
    
    # Instagram Direct
    if grep -q "INSTAGRAM_ACCESS_TOKEN" "$PROJECT_ROOT/.env.production"; then
        record_check "Instagram Integration" "PASS" "Instagram Direct API configured"
    else
        record_check "Instagram Integration" "WARN" "Instagram Direct API not configured"
    fi
    
    # Twitter API
    if grep -q "TWITTER_API_KEY" "$PROJECT_ROOT/.env.production"; then
        record_check "Twitter Integration" "PASS" "Twitter API v2 configured"
    else
        record_check "Twitter Integration" "WARN" "Twitter API not configured"
    fi
    
    # Webhook endpoints
    if [[ -f "$PROJECT_ROOT/backend/src/routes/webhooks.js" ]]; then
        record_check "Webhook Endpoints" "PASS" "Platform webhook endpoints implemented"
    else
        record_check "Webhook Endpoints" "FAIL" "Webhook endpoints not found"
    fi
}

# Function to check documentation
check_documentation() {
    print_step "📚 Documentation Completeness"
    
    # API Documentation
    if [[ -f "$PROJECT_ROOT/docs/api.md" ]] || [[ -d "$PROJECT_ROOT/docs/api" ]]; then
        record_check "API Documentation" "PASS" "API documentation is available"
    else
        record_check "API Documentation" "WARN" "API documentation not found"
    fi
    
    # Deployment Documentation
    if [[ -f "$PROJECT_ROOT/docs/deployment.md" ]]; then
        record_check "Deployment Docs" "PASS" "Deployment documentation exists"
    else
        record_check "Deployment Docs" "WARN" "Deployment documentation not found"
    fi
    
    # User Manual
    if [[ -f "$PROJECT_ROOT/docs/user-manual.md" ]]; then
        record_check "User Manual" "PASS" "User manual is available"
    else
        record_check "User Manual" "WARN" "User manual not found"
    fi
    
    # README completeness
    if [[ -f "$PROJECT_ROOT/README.md" ]]; then
        local readme_size=$(wc -c < "$PROJECT_ROOT/README.md")
        if [[ $readme_size -gt 1000 ]]; then
            record_check "README Quality" "PASS" "Comprehensive README documentation"
        else
            record_check "README Quality" "WARN" "README documentation could be more detailed"
        fi
    else
        record_check "README Exists" "FAIL" "README.md not found"
    fi
}

# Function to generate checklist report
generate_checklist_report() {
    print_step "📋 Generating Production Readiness Report"
    
    local report_file="$PROJECT_ROOT/logs/production-readiness-report-$TIMESTAMP.html"
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Free Mobile Chatbot - Production Readiness Checklist</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .summary { display: flex; gap: 20px; margin: 20px 0; }
        .metric { background: #e8f4fd; padding: 15px; border-radius: 5px; flex: 1; text-align: center; }
        .pass { color: #28a745; }
        .warn { color: #ffc107; }
        .fail { color: #dc3545; }
        .section { margin: 20px 0; }
        .check-item { background: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 3px; }
        .progress-bar { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; }
        .progress-fill { height: 100%; background: linear-gradient(90deg, #28a745, #20c997); }
    </style>
</head>
<body>
    <div class="header">
        <h1>✅ Free Mobile Chatbot ML Intelligence Dashboard</h1>
        <h2>Production Readiness Checklist Report</h2>
        <p><strong>Generated:</strong> $(date)</p>
        <p><strong>Environment:</strong> Production</p>
    </div>
    
    <div class="summary">
        <div class="metric">
            <h3>Total Checks</h3>
            <p>$total_checks</p>
        </div>
        <div class="metric">
            <h3 class="pass">Passed</h3>
            <p>$passed_checks</p>
        </div>
        <div class="metric">
            <h3 class="warn">Warnings</h3>
            <p>$warning_checks</p>
        </div>
        <div class="metric">
            <h3 class="fail">Failed</h3>
            <p>$failed_checks</p>
        </div>
    </div>
    
    <div class="section">
        <h3>📊 Overall Readiness Score</h3>
        <div class="progress-bar">
            <div class="progress-fill" style="width: $(( (passed_checks * 100) / total_checks ))%"></div>
        </div>
        <p>$(( (passed_checks * 100) / total_checks ))% Ready for Production</p>
    </div>
    
    <div class="section">
        <h3>📋 Detailed Checklist Results</h3>
EOF

    # Add detailed results
    for check_name in "${!checklist_results[@]}"; do
        local result="${checklist_results[$check_name]}"
        local status="${result%%:*}"
        local message="${result#*:}"
        local icon="❓"
        local class="unknown"
        
        case "$status" in
            "PASS") icon="✅"; class="pass" ;;
            "WARN") icon="⚠️"; class="warn" ;;
            "FAIL") icon="❌"; class="fail" ;;
        esac
        
        echo "        <div class=\"check-item $class\">$icon <strong>$check_name:</strong> $message</div>" >> "$report_file"
    done

    cat >> "$report_file" << EOF
    </div>
    
    <div class="section">
        <h3>🎯 Production Go/No-Go Decision</h3>
EOF

    if [[ $failed_checks -eq 0 ]]; then
        cat >> "$report_file" << EOF
        <div class="check-item pass">
            <h4>✅ GO FOR PRODUCTION</h4>
            <p>All critical checks passed. The system is ready for production deployment.</p>
            <p><strong>Ready to serve 13+ million Free Mobile subscribers!</strong></p>
        </div>
EOF
    else
        cat >> "$report_file" << EOF
        <div class="check-item fail">
            <h4>❌ NO-GO FOR PRODUCTION</h4>
            <p>$failed_checks critical issues must be resolved before production deployment.</p>
            <p>Please address all failed checks and re-run the readiness assessment.</p>
        </div>
EOF
    fi

    cat >> "$report_file" << EOF
    </div>
    
    <div class="section">
        <h3>📋 Next Steps</h3>
        <ul>
            <li>Address any failed or warning items</li>
            <li>Re-run production readiness checklist</li>
            <li>Execute comprehensive test suite</li>
            <li>Schedule production deployment</li>
            <li>Monitor system performance post-deployment</li>
        </ul>
    </div>
</body>
</html>
EOF
    
    print_success "Production readiness report generated: $report_file"
}

# Main function
main() {
    print_header "✅ FREE MOBILE CHATBOT PRODUCTION READINESS CHECKLIST"
    
    # Create logs directory
    mkdir -p "$PROJECT_ROOT/logs"
    
    # Start checklist log
    echo "Production Readiness Checklist Started: $(date)" > "$CHECKLIST_LOG"
    
    # Execute all checks
    check_security_compliance
    check_accessibility_compliance
    check_core_web_vitals
    check_backup_recovery
    check_monitoring_dashboards
    check_ai_services_readiness
    check_platform_integrations
    check_documentation
    
    # Generate comprehensive report
    generate_checklist_report
    
    # Print summary
    print_header "📊 PRODUCTION READINESS SUMMARY"
    print_status "Total Checks: $total_checks"
    print_success "Passed: $passed_checks"
    print_warning "Warnings: $warning_checks"
    print_error "Failed: $failed_checks"
    
    local readiness_score=$(( (passed_checks * 100) / total_checks ))
    print_status "Overall Readiness Score: $readiness_score%"
    
    if [[ $failed_checks -eq 0 ]]; then
        print_header "🎉 GO FOR PRODUCTION!"
        print_success "Free Mobile Chatbot ML Intelligence Dashboard is ready for production deployment!"
        print_success "Ready to serve 13+ million Free Mobile subscribers with AI-powered customer service!"
        exit 0
    else
        print_header "⚠️ PRODUCTION READINESS ISSUES"
        print_error "$failed_checks critical issues must be resolved before production deployment."
        print_status "Please address all failed checks and re-run the readiness assessment."
        exit 1
    fi
}

# Execute main function
main "$@"
