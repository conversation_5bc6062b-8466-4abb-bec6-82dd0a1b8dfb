-- =============================================
-- 📊 TIMESCALEDB INITIALIZATION SCRIPT
-- Free Mobile Chatbot Dashboard - Phase 4 Production
-- ML Analytics and Time-Series Data Setup
-- =============================================

-- Enable TimescaleDB extension
CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;

-- Create schemas for organization
CREATE SCHEMA IF NOT EXISTS ml_analytics;
CREATE SCHEMA IF NOT EXISTS performance_metrics;
CREATE SCHEMA IF NOT EXISTS business_metrics;

-- Set search path
SET search_path TO ml_analytics, performance_metrics, business_metrics, public;

-- =============================================
-- 🤖 ML ANALYTICS TABLES
-- =============================================

-- ML Classification Metrics
CREATE TABLE ml_analytics.classification_metrics (
    time TIMESTAMPTZ NOT NULL,
    conversation_id VARCHAR(255) NOT NULL,
    category VARCHAR(100) NOT NULL,
    priority_score INTEGER NOT NULL CHECK (priority_score >= 0 AND priority_score <= 100),
    confidence DECIMAL(5,4) NOT NULL CHECK (confidence >= 0 AND confidence <= 1),
    processing_time_ms INTEGER NOT NULL,
    model_version VARCHAR(50) NOT NULL,
    human_validated BOOLEAN DEFAULT FALSE,
    validation_feedback TEXT,
    business_impact JSONB,
    sentiment_score DECIMAL(3,2),
    sentiment_trend VARCHAR(20),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create hypertable for classification metrics
SELECT create_hypertable('ml_analytics.classification_metrics', 'time', chunk_time_interval => INTERVAL '1 day');

-- ML Model Performance
CREATE TABLE ml_analytics.model_performance (
    time TIMESTAMPTZ NOT NULL,
    model_name VARCHAR(100) NOT NULL,
    model_version VARCHAR(50) NOT NULL,
    accuracy DECIMAL(5,4),
    precision_score DECIMAL(5,4),
    recall DECIMAL(5,4),
    f1_score DECIMAL(5,4),
    processing_time_avg_ms INTEGER,
    throughput_per_second DECIMAL(8,2),
    memory_usage_mb INTEGER,
    cache_hit_rate DECIMAL(5,4),
    error_rate DECIMAL(5,4),
    total_predictions INTEGER,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create hypertable for model performance
SELECT create_hypertable('ml_analytics.model_performance', 'time', chunk_time_interval => INTERVAL '1 hour');

-- ML Alert Events
CREATE TABLE ml_analytics.alert_events (
    time TIMESTAMPTZ NOT NULL,
    alert_id VARCHAR(255) NOT NULL,
    alert_type VARCHAR(100) NOT NULL,
    severity VARCHAR(20) NOT NULL,
    priority INTEGER NOT NULL,
    conversation_id VARCHAR(255),
    customer_id VARCHAR(255),
    title TEXT NOT NULL,
    description TEXT,
    context_data JSONB,
    status VARCHAR(50) DEFAULT 'active',
    assigned_to VARCHAR(255),
    escalation_level INTEGER DEFAULT 1,
    resolved_at TIMESTAMPTZ,
    resolution_time_minutes INTEGER,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create hypertable for alert events
SELECT create_hypertable('ml_analytics.alert_events', 'time', chunk_time_interval => INTERVAL '1 day');

-- =============================================
-- 📈 PERFORMANCE METRICS TABLES
-- =============================================

-- System Performance Metrics
CREATE TABLE performance_metrics.system_metrics (
    time TIMESTAMPTZ NOT NULL,
    service_name VARCHAR(100) NOT NULL,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15,6) NOT NULL,
    unit VARCHAR(20),
    tags JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create hypertable for system metrics
SELECT create_hypertable('performance_metrics.system_metrics', 'time', chunk_time_interval => INTERVAL '1 hour');

-- API Performance Metrics
CREATE TABLE performance_metrics.api_metrics (
    time TIMESTAMPTZ NOT NULL,
    endpoint VARCHAR(255) NOT NULL,
    method VARCHAR(10) NOT NULL,
    status_code INTEGER NOT NULL,
    response_time_ms INTEGER NOT NULL,
    request_size_bytes INTEGER,
    response_size_bytes INTEGER,
    user_id VARCHAR(255),
    ip_address INET,
    user_agent TEXT,
    error_message TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create hypertable for API metrics
SELECT create_hypertable('performance_metrics.api_metrics', 'time', chunk_time_interval => INTERVAL '1 hour');

-- Database Performance Metrics
CREATE TABLE performance_metrics.database_metrics (
    time TIMESTAMPTZ NOT NULL,
    database_name VARCHAR(100) NOT NULL,
    operation_type VARCHAR(50) NOT NULL,
    collection_table VARCHAR(100),
    execution_time_ms INTEGER NOT NULL,
    rows_affected INTEGER,
    query_hash VARCHAR(64),
    index_used BOOLEAN,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create hypertable for database metrics
SELECT create_hypertable('performance_metrics.database_metrics', 'time', chunk_time_interval => INTERVAL '1 hour');

-- =============================================
-- 💼 BUSINESS METRICS TABLES
-- =============================================

-- Conversation Analytics
CREATE TABLE business_metrics.conversation_analytics (
    time TIMESTAMPTZ NOT NULL,
    conversation_id VARCHAR(255) NOT NULL,
    customer_id VARCHAR(255) NOT NULL,
    agent_id VARCHAR(255),
    channel VARCHAR(50) NOT NULL,
    category VARCHAR(100),
    priority VARCHAR(20),
    status VARCHAR(50) NOT NULL,
    duration_minutes INTEGER,
    message_count INTEGER,
    resolution_time_minutes INTEGER,
    satisfaction_score INTEGER CHECK (satisfaction_score >= 1 AND satisfaction_score <= 5),
    escalated BOOLEAN DEFAULT FALSE,
    revenue_impact DECIMAL(10,2),
    cost_impact DECIMAL(10,2),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create hypertable for conversation analytics
SELECT create_hypertable('business_metrics.conversation_analytics', 'time', chunk_time_interval => INTERVAL '1 day');

-- Customer Journey Analytics
CREATE TABLE business_metrics.customer_journey (
    time TIMESTAMPTZ NOT NULL,
    customer_id VARCHAR(255) NOT NULL,
    session_id VARCHAR(255),
    event_type VARCHAR(100) NOT NULL,
    event_data JSONB,
    page_url TEXT,
    referrer TEXT,
    device_type VARCHAR(50),
    browser VARCHAR(100),
    location_country VARCHAR(2),
    location_city VARCHAR(100),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create hypertable for customer journey
SELECT create_hypertable('business_metrics.customer_journey', 'time', chunk_time_interval => INTERVAL '1 day');

-- Revenue Impact Tracking
CREATE TABLE business_metrics.revenue_impact (
    time TIMESTAMPTZ NOT NULL,
    event_type VARCHAR(100) NOT NULL,
    customer_id VARCHAR(255) NOT NULL,
    conversation_id VARCHAR(255),
    revenue_at_risk DECIMAL(10,2) DEFAULT 0,
    opportunity_value DECIMAL(10,2) DEFAULT 0,
    actual_revenue DECIMAL(10,2) DEFAULT 0,
    churn_probability DECIMAL(5,4),
    upsell_probability DECIMAL(5,4),
    category VARCHAR(100),
    ml_prediction_confidence DECIMAL(5,4),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create hypertable for revenue impact
SELECT create_hypertable('business_metrics.revenue_impact', 'time', chunk_time_interval => INTERVAL '1 day');

-- =============================================
-- 📊 INDEXES FOR PERFORMANCE
-- =============================================

-- ML Analytics Indexes
CREATE INDEX idx_classification_metrics_conversation ON ml_analytics.classification_metrics (conversation_id, time DESC);
CREATE INDEX idx_classification_metrics_category ON ml_analytics.classification_metrics (category, time DESC);
CREATE INDEX idx_classification_metrics_priority ON ml_analytics.classification_metrics (priority_score DESC, time DESC);
CREATE INDEX idx_classification_metrics_validation ON ml_analytics.classification_metrics (human_validated, time DESC);

CREATE INDEX idx_model_performance_model ON ml_analytics.model_performance (model_name, model_version, time DESC);
CREATE INDEX idx_model_performance_accuracy ON ml_analytics.model_performance (accuracy DESC, time DESC);

CREATE INDEX idx_alert_events_type ON ml_analytics.alert_events (alert_type, time DESC);
CREATE INDEX idx_alert_events_severity ON ml_analytics.alert_events (severity, status, time DESC);
CREATE INDEX idx_alert_events_customer ON ml_analytics.alert_events (customer_id, time DESC);

-- Performance Metrics Indexes
CREATE INDEX idx_system_metrics_service ON performance_metrics.system_metrics (service_name, metric_name, time DESC);
CREATE INDEX idx_api_metrics_endpoint ON performance_metrics.api_metrics (endpoint, method, time DESC);
CREATE INDEX idx_api_metrics_status ON performance_metrics.api_metrics (status_code, time DESC);
CREATE INDEX idx_database_metrics_operation ON performance_metrics.database_metrics (database_name, operation_type, time DESC);

-- Business Metrics Indexes
CREATE INDEX idx_conversation_analytics_customer ON business_metrics.conversation_analytics (customer_id, time DESC);
CREATE INDEX idx_conversation_analytics_agent ON business_metrics.conversation_analytics (agent_id, time DESC);
CREATE INDEX idx_conversation_analytics_category ON business_metrics.conversation_analytics (category, status, time DESC);

CREATE INDEX idx_customer_journey_customer ON business_metrics.customer_journey (customer_id, time DESC);
CREATE INDEX idx_customer_journey_event ON business_metrics.customer_journey (event_type, time DESC);

CREATE INDEX idx_revenue_impact_customer ON business_metrics.revenue_impact (customer_id, time DESC);
CREATE INDEX idx_revenue_impact_event ON business_metrics.revenue_impact (event_type, time DESC);

-- =============================================
-- 🔄 CONTINUOUS AGGREGATES FOR REAL-TIME ANALYTICS
-- =============================================

-- Hourly ML Classification Summary
CREATE MATERIALIZED VIEW ml_analytics.classification_summary_hourly
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 hour', time) AS bucket,
    category,
    COUNT(*) as total_classifications,
    AVG(priority_score) as avg_priority_score,
    AVG(confidence) as avg_confidence,
    AVG(processing_time_ms) as avg_processing_time,
    COUNT(*) FILTER (WHERE human_validated = true) as validated_count,
    COUNT(*) FILTER (WHERE priority_score >= 80) as high_priority_count
FROM ml_analytics.classification_metrics
GROUP BY bucket, category;

-- Daily Business Metrics Summary
CREATE MATERIALIZED VIEW business_metrics.daily_summary
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 day', time) AS bucket,
    COUNT(DISTINCT customer_id) as unique_customers,
    COUNT(*) as total_conversations,
    AVG(duration_minutes) as avg_duration,
    AVG(satisfaction_score) as avg_satisfaction,
    SUM(revenue_impact) as total_revenue_impact,
    COUNT(*) FILTER (WHERE escalated = true) as escalated_count
FROM business_metrics.conversation_analytics
GROUP BY bucket;

-- Hourly System Performance Summary
CREATE MATERIALIZED VIEW performance_metrics.system_performance_hourly
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 hour', time) AS bucket,
    service_name,
    metric_name,
    AVG(metric_value) as avg_value,
    MIN(metric_value) as min_value,
    MAX(metric_value) as max_value,
    COUNT(*) as sample_count
FROM performance_metrics.system_metrics
GROUP BY bucket, service_name, metric_name;

-- =============================================
-- 🔄 DATA RETENTION POLICIES
-- =============================================

-- Retain raw ML classification data for 90 days
SELECT add_retention_policy('ml_analytics.classification_metrics', INTERVAL '90 days');

-- Retain model performance data for 30 days
SELECT add_retention_policy('ml_analytics.model_performance', INTERVAL '30 days');

-- Retain alert events for 180 days
SELECT add_retention_policy('ml_analytics.alert_events', INTERVAL '180 days');

-- Retain system metrics for 7 days (aggregated data kept longer)
SELECT add_retention_policy('performance_metrics.system_metrics', INTERVAL '7 days');

-- Retain API metrics for 30 days
SELECT add_retention_policy('performance_metrics.api_metrics', INTERVAL '30 days');

-- Retain business metrics for 365 days
SELECT add_retention_policy('business_metrics.conversation_analytics', INTERVAL '365 days');
SELECT add_retention_policy('business_metrics.customer_journey', INTERVAL '365 days');
SELECT add_retention_policy('business_metrics.revenue_impact', INTERVAL '365 days');

-- =============================================
-- 👤 CREATE APPLICATION USER
-- =============================================

-- Create application user with appropriate permissions
CREATE USER freemobile_ml_user WITH PASSWORD 'FreeMobile_TimescaleDB_2025_SecurePassword!';

-- Grant schema permissions
GRANT USAGE ON SCHEMA ml_analytics TO freemobile_ml_user;
GRANT USAGE ON SCHEMA performance_metrics TO freemobile_ml_user;
GRANT USAGE ON SCHEMA business_metrics TO freemobile_ml_user;

-- Grant table permissions
GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA ml_analytics TO freemobile_ml_user;
GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA performance_metrics TO freemobile_ml_user;
GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA business_metrics TO freemobile_ml_user;

-- Grant permissions on future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA ml_analytics GRANT SELECT, INSERT, UPDATE ON TABLES TO freemobile_ml_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA performance_metrics GRANT SELECT, INSERT, UPDATE ON TABLES TO freemobile_ml_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA business_metrics GRANT SELECT, INSERT, UPDATE ON TABLES TO freemobile_ml_user;

-- =============================================
-- 📊 SAMPLE DATA FOR TESTING
-- =============================================

-- Insert sample ML classification data
INSERT INTO ml_analytics.classification_metrics (
    time, conversation_id, category, priority_score, confidence, 
    processing_time_ms, model_version, sentiment_score, sentiment_trend
) VALUES 
    (NOW() - INTERVAL '1 hour', 'CONV_001', 'VENTE_OPPORTUNITE', 85, 0.92, 150, 'v1.0', 0.75, 'positive'),
    (NOW() - INTERVAL '2 hours', 'CONV_002', 'RESILIATION_CRITIQUE', 95, 0.88, 200, 'v1.0', -0.65, 'negative'),
    (NOW() - INTERVAL '3 hours', 'CONV_003', 'SUPPORT_URGENT', 78, 0.85, 120, 'v1.0', -0.25, 'neutral');

-- Insert sample model performance data
INSERT INTO ml_analytics.model_performance (
    time, model_name, model_version, accuracy, precision_score, 
    recall, f1_score, processing_time_avg_ms, throughput_per_second
) VALUES 
    (NOW(), 'classification_model', 'v1.0', 0.89, 0.87, 0.91, 0.89, 145, 6.8);

-- Insert sample business metrics
INSERT INTO business_metrics.conversation_analytics (
    time, conversation_id, customer_id, channel, category, 
    status, duration_minutes, message_count, satisfaction_score
) VALUES 
    (NOW() - INTERVAL '1 hour', 'CONV_001', 'CUST_001', 'chat', 'billing', 'resolved', 15, 8, 4),
    (NOW() - INTERVAL '2 hours', 'CONV_002', 'CUST_002', 'email', 'technical', 'open', 25, 12, NULL);

-- Refresh continuous aggregates
CALL refresh_continuous_aggregate('ml_analytics.classification_summary_hourly', NULL, NULL);
CALL refresh_continuous_aggregate('business_metrics.daily_summary', NULL, NULL);
CALL refresh_continuous_aggregate('performance_metrics.system_performance_hourly', NULL, NULL);

-- =============================================
-- ✅ INITIALIZATION COMPLETE
-- =============================================

-- Log successful initialization
DO $$
BEGIN
    RAISE NOTICE '🎉 TimescaleDB initialization completed successfully!';
    RAISE NOTICE '📊 Created schemas: ml_analytics, performance_metrics, business_metrics';
    RAISE NOTICE '📈 Created hypertables with automatic partitioning';
    RAISE NOTICE '🔍 Created indexes for optimal query performance';
    RAISE NOTICE '📊 Created continuous aggregates for real-time analytics';
    RAISE NOTICE '🔄 Configured data retention policies';
    RAISE NOTICE '👤 Created application user with appropriate permissions';
    RAISE NOTICE '📝 Inserted sample data for testing';
END $$;
