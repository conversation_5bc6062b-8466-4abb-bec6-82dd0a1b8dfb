# =============================================
# 📊 POSTGRESQL/TIMESCALEDB PRODUCTION CONFIGURATION
# Free Mobile Chatbot Dashboard - Phase 4 Production
# Optimized for time-series data and analytics workloads
# =============================================

# ---------------------------------------------
# CONNECTION AND AUTHENTICATION
# ---------------------------------------------

listen_addresses = '*'
port = 5432
max_connections = 200
superuser_reserved_connections = 3

# Authentication
authentication_timeout = 1min
password_encryption = scram-sha-256

# SSL Configuration
ssl = on
ssl_cert_file = 'server.crt'
ssl_key_file = 'server.key'
ssl_ca_file = 'ca.crt'
ssl_ciphers = 'HIGH:MEDIUM:+3DES:!aNULL'
ssl_prefer_server_ciphers = on

# ---------------------------------------------
# MEMORY CONFIGURATION
# ---------------------------------------------

# Shared memory (25% of total RAM for dedicated server)
shared_buffers = 512MB

# Working memory for operations
work_mem = 16MB
maintenance_work_mem = 256MB

# Effective cache size (75% of total RAM)
effective_cache_size = 1536MB

# Random page cost (SSD optimized)
random_page_cost = 1.1
seq_page_cost = 1.0

# ---------------------------------------------
# WRITE AHEAD LOG (WAL)
# ---------------------------------------------

# WAL configuration for high write workloads
wal_level = replica
wal_buffers = 16MB
wal_writer_delay = 200ms
wal_writer_flush_after = 1MB

# Checkpoints
checkpoint_timeout = 15min
checkpoint_completion_target = 0.9
checkpoint_flush_after = 256kB
checkpoint_warning = 30s

# WAL archiving (for backup)
archive_mode = on
archive_command = 'cp %p /var/lib/postgresql/wal_archive/%f'
archive_timeout = 300s

# ---------------------------------------------
# QUERY PLANNER
# ---------------------------------------------

# Cost-based optimizer settings
cpu_tuple_cost = 0.01
cpu_index_tuple_cost = 0.005
cpu_operator_cost = 0.0025

# Join settings
enable_hashjoin = on
enable_mergejoin = on
enable_nestloop = on

# Parallel query settings
max_parallel_workers_per_gather = 4
max_parallel_workers = 8
max_parallel_maintenance_workers = 4
parallel_tuple_cost = 0.1
parallel_setup_cost = 1000.0

# ---------------------------------------------
# BACKGROUND WRITER
# ---------------------------------------------

bgwriter_delay = 200ms
bgwriter_lru_maxpages = 100
bgwriter_lru_multiplier = 2.0
bgwriter_flush_after = 512kB

# ---------------------------------------------
# VACUUM AND ANALYZE
# ---------------------------------------------

# Autovacuum settings
autovacuum = on
autovacuum_max_workers = 3
autovacuum_naptime = 1min
autovacuum_vacuum_threshold = 50
autovacuum_vacuum_scale_factor = 0.2
autovacuum_analyze_threshold = 50
autovacuum_analyze_scale_factor = 0.1
autovacuum_vacuum_cost_delay = 20ms
autovacuum_vacuum_cost_limit = 200

# Manual vacuum settings
vacuum_cost_delay = 0
vacuum_cost_page_hit = 1
vacuum_cost_page_miss = 10
vacuum_cost_page_dirty = 20
vacuum_cost_limit = 200

# ---------------------------------------------
# LOGGING
# ---------------------------------------------

# Logging configuration
logging_collector = on
log_destination = 'stderr'
log_directory = '/var/log/postgresql'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_file_mode = 0600
log_rotation_age = 1d
log_rotation_size = 100MB
log_truncate_on_rotation = on

# What to log
log_min_messages = warning
log_min_error_statement = error
log_min_duration_statement = 1000ms
log_checkpoints = on
log_connections = on
log_disconnections = on
log_lock_waits = on
log_temp_files = 10MB
log_autovacuum_min_duration = 0
log_error_verbosity = default

# Log line prefix
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '

# Statement logging
log_statement = 'ddl'
log_duration = off

# ---------------------------------------------
# STATISTICS
# ---------------------------------------------

# Statistics collection
track_activities = on
track_counts = on
track_io_timing = on
track_functions = all
stats_temp_directory = '/var/run/postgresql/stats_temp'

# Query statistics
shared_preload_libraries = 'timescaledb,pg_stat_statements'
pg_stat_statements.max = 10000
pg_stat_statements.track = all
pg_stat_statements.track_utility = on
pg_stat_statements.save = on

# ---------------------------------------------
# TIMESCALEDB SPECIFIC SETTINGS
# ---------------------------------------------

# TimescaleDB configuration
timescaledb.max_background_workers = 8
timescaledb.last_updated_threshold = '1min'
timescaledb.telemetry_level = 'basic'

# Compression settings
timescaledb.compress_segmentby_default = 'device_id'
timescaledb.compress_orderby_default = 'time DESC'

# Continuous aggregates
timescaledb.max_open_chunks_per_insert = 10
timescaledb.max_cached_chunks_per_hypertable = 10

# ---------------------------------------------
# REPLICATION (if needed)
# ---------------------------------------------

# Streaming replication
max_wal_senders = 3
wal_keep_segments = 32
hot_standby = on
hot_standby_feedback = on

# Replication timeout
wal_sender_timeout = 60s
wal_receiver_timeout = 60s

# ---------------------------------------------
# LOCK MANAGEMENT
# ---------------------------------------------

# Lock configuration
max_locks_per_transaction = 64
max_pred_locks_per_transaction = 64
deadlock_timeout = 1s

# ---------------------------------------------
# ERROR HANDLING
# ---------------------------------------------

# Error handling
exit_on_error = off
restart_after_crash = on

# Statement timeout (prevent runaway queries)
statement_timeout = 300s
lock_timeout = 30s
idle_in_transaction_session_timeout = 60s

# ---------------------------------------------
# LOCALE AND FORMATTING
# ---------------------------------------------

# Locale settings
datestyle = 'iso, mdy'
timezone = 'Europe/Paris'
lc_messages = 'en_US.UTF-8'
lc_monetary = 'fr_FR.UTF-8'
lc_numeric = 'fr_FR.UTF-8'
lc_time = 'fr_FR.UTF-8'

# Default text search configuration
default_text_search_config = 'pg_catalog.french'

# ---------------------------------------------
# CUSTOM SETTINGS FOR ML WORKLOADS
# ---------------------------------------------

# Increase limits for analytical queries
from_collapse_limit = 8
join_collapse_limit = 8
geqo_threshold = 12

# Optimize for time-series queries
enable_partitionwise_join = on
enable_partitionwise_aggregate = on

# Increase work memory for complex aggregations
max_stack_depth = 7MB

# Optimize for bulk inserts
synchronous_commit = off  # For high-throughput inserts (use with caution)
commit_delay = 100000     # Microseconds
commit_siblings = 5

# ---------------------------------------------
# MONITORING AND MAINTENANCE
# ---------------------------------------------

# Track query execution plans
auto_explain.log_min_duration = 10s
auto_explain.log_analyze = on
auto_explain.log_buffers = on
auto_explain.log_timing = on
auto_explain.log_triggers = on
auto_explain.log_verbose = on
auto_explain.log_nested_statements = on

# Log slow queries for optimization
log_slow_queries = on
slow_query_log_file = '/var/log/postgresql/slow_queries.log'
