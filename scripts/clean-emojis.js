const fs = require('fs'); const path = require('path'); // Emoji patterns to remove const emojiPatterns = [ /[\u{1F600}-\u{1F64F}]/gu, // Emoticons /[\u{1F300}-\u{1F5FF}]/gu, // Misc Symbols and Pictographs /[\u{1F680}-\u{1F6FF}]/gu, // Transport and Map /[\u{1F1E0}-\u{1F1FF}]/gu, // Regional indicator symbols /[\u{2600}-\u{26FF}]/gu, // Misc symbols /[\u{2700}-\u{27BF}]/gu, // Dingbats /[\u{1F900}-\u{1F9FF}]/gu, // Supplemental Symbols and Pictographs /[\u{1F018}-\u{1F270}]/gu, // Various symbols /[[COMPLETE][FAILED][TARGET][DEPLOY][ANALYTICS][CONFIG][PERFORMANCE][DESIGN][FEATURE][METRICS][SEARCH][AI][ADMIN][AGENT][USER][DOCS][ARCHITECTURE][SECURITY][MOBILE][ENTERPRISE]]/gu // Specific emojis found in project ]; // Emoji to text replacements const emojiReplacements = { '[COMPLETE]': '[COMPLETE]', '[FAILED]': '[FAILED]', '[TARGET]': '[TARGET]', '[DEPLOY]': '[DEPLOY]', '[ANALYTICS]': '[ANALYTICS]', '[CONFIG]': '[CONFIG]', '[PERFORMANCE]': '[PERFORMANCE]', '[DESIGN]': '[DESIGN]', '[FEATURE]': '[FEATURE]', '[METRICS]': '[METRICS]', '[SEARCH]': '[SEARCH]', '[AI]': '[AI]', '[ADMIN]': '[ADMIN]', '[AGENT]': '[AGENT]', '[USER]': '[USER]', '[DOCS]': '[DOCS]', '[ARCHITECTURE]': '[ARCHITECTURE]', '[SECURITY]': '[SECURITY]', '[MOBILE]': '[MOBILE]', '[ENTERPRISE]': '[ENTERPRISE]' }; function cleanEmojis(text) { let cleanedText = text; // Replace specific emojis with text equivalents for (const [emoji, replacement] of Object.entries(emojiReplacements)) { cleanedText = cleanedText.replace(new RegExp(emoji, 'g'), replacement); } // Remove any remaining emojis for (const pattern of emojiPatterns) { cleanedText = cleanedText.replace(pattern, ''); } // Clean up extra spaces cleanedText = cleanedText.replace(/\s+/g, ' ').trim(); return cleanedText; } function shouldProcessFile(filePath) { const ext = path.extname(filePath).toLowerCase(); const allowedExtensions = ['.md', '.txt', '.js', '.ts', '.tsx', '.jsx', '.json', '.yml', '.yaml']; // Skip node_modules and other directories if (filePath.includes('node_modules') || filePath.includes('.git') || filePath.includes('build') || filePath.includes('dist') || filePath.includes('coverage')) { return false; } return allowedExtensions.includes(ext); } function processFile(filePath) { try { const content = fs.readFileSync(filePath, 'utf8'); const cleanedContent = cleanEmojis(content); if (content !== cleanedContent) { fs.writeFileSync(filePath, cleanedContent, 'utf8'); console.log(`Cleaned: ${filePath}`); return true; } return false; } catch (error) { console.error(`Error processing ${filePath}:`, error.message); return false; } } function processDirectory(dirPath) { let filesProcessed = 0; let filesChanged = 0; function walkDir(currentPath) { const items = fs.readdirSync(currentPath); for (const item of items) { const fullPath = path.join(currentPath, item); const stat = fs.statSync(fullPath); if (stat.isDirectory()) { walkDir(fullPath); } else if (shouldProcessFile(fullPath)) { filesProcessed++; if (processFile(fullPath)) { filesChanged++; } } } } walkDir(dirPath); console.log(`\nProcessing complete:`); console.log(`Files processed: ${filesProcessed}`); console.log(`Files changed: ${filesChanged}`); } // Main execution const projectRoot = path.join(__dirname, '..'); console.log('Starting emoji cleanup...'); console.log(`Project root: ${projectRoot}`); processDirectory(projectRoot); console.log('\nEmoji cleanup completed!');