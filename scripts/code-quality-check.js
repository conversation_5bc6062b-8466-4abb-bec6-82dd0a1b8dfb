#!/usr/bin/env node /** * Code Quality Check Script * Performs various code quality checks and reports issues */ const fs = require('fs'); const path = require('path'); const { execSync } = require('child_process'); class CodeQualityChecker { constructor() { this.issues = []; this.warnings = []; this.suggestions = []; } log(message, type = 'info') { const timestamp = new Date().toISOString(); const prefix = { error: '[FAILED]', warning: '', info: 'ℹ', success: '[COMPLETE]' }[type]; console.log(`${prefix} [${timestamp}] ${message}`); } addIssue(message) { this.issues.push(message); this.log(message, 'error'); } addWarning(message) { this.warnings.push(message); this.log(message, 'warning'); } addSuggestion(message) { this.suggestions.push(message); this.log(message, 'info'); } checkFileExists(filePath, description) { if (!fs.existsSync(filePath)) { this.addIssue(`Missing ${description}: ${filePath}`); return false; } return true; } checkPackageJson() { this.log('Checking package.json files...', 'info'); const packageFiles = [ 'package.json', 'frontend/package.json', 'backend/package.json' ]; packageFiles.forEach(file => { if (this.checkFileExists(file, 'package.json')) { try { const pkg = JSON.parse(fs.readFileSync(file, 'utf8')); // Check for required scripts if (file === 'package.json') { const requiredScripts = ['test', 'build', 'start']; requiredScripts.forEach(script => { if (!pkg.scripts || !pkg.scripts[script]) { this.addWarning(`Missing script '${script}' in ${file}`); } }); } // Check for security vulnerabilities in dependencies if (pkg.dependencies) { this.addSuggestion(`Run 'npm audit' to check for vulnerabilities in ${file}`); } } catch (error) { this.addIssue(`Invalid JSON in ${file}: ${error.message}`); } } }); } checkEnvironmentFiles() { this.log('Checking environment configuration...', 'info'); const envFiles = [ 'env.template', '.env.example' ]; let hasEnvTemplate = false; envFiles.forEach(file => { if (fs.existsSync(file)) { hasEnvTemplate = true; this.log(`Found environment template: ${file}`, 'success'); } }); if (!hasEnvTemplate) { this.addWarning('No environment template file found'); } // Check if .env exists (should not be in repo) if (fs.existsSync('.env')) { this.addWarning('.env file found - ensure it\'s in .gitignore'); } } checkGitIgnore() { this.log('Checking .gitignore...', 'info'); if (this.checkFileExists('.gitignore', '.gitignore file')) { const gitignore = fs.readFileSync('.gitignore', 'utf8'); const requiredEntries = [ 'node_modules', '.env', 'dist', 'build', 'logs' ]; requiredEntries.forEach(entry => { if (!gitignore.includes(entry)) { this.addWarning(`Missing '${entry}' in .gitignore`); } }); } } checkDocumentation() { this.log('Checking documentation...', 'info'); const docFiles = [ 'README.md', 'DEPLOYMENT_GUIDE.md', 'SECURITY.md' ]; docFiles.forEach(file => { if (this.checkFileExists(file, `documentation file`)) { const content = fs.readFileSync(file, 'utf8'); if (content.length < 100) { this.addWarning(`${file} seems too short (${content.length} characters)`); } } }); } checkSecurityFiles() { this.log('Checking security configuration...', 'info'); // Check for security-related files const securityFiles = [ 'backend/src/middleware/security.js', 'backend/src/config/database.js' ]; securityFiles.forEach(file => { if (this.checkFileExists(file, 'security file')) { const content = fs.readFileSync(file, 'utf8'); // Check for common security issues if (content.includes('console.log') && content.includes('password')) { this.addIssue(`Potential password logging in ${file}`); } if (content.includes('process.env') && content.includes('console.log')) { this.addWarning(`Environment variables may be logged in ${file}`); } } }); } checkTestConfiguration() { this.log('Checking test configuration...', 'info'); const testFiles = [ 'playwright.config.js', 'frontend/jest.config.js', 'tests/basic-health-check.test.js' ]; testFiles.forEach(file => { this.checkFileExists(file, 'test configuration'); }); // Check if test directories exist const testDirs = ['tests', 'frontend/src/__tests__']; testDirs.forEach(dir => { if (!fs.existsSync(dir)) { this.addSuggestion(`Consider creating test directory: ${dir}`); } }); } checkWorkflowFiles() { this.log('Checking GitHub Actions workflows...', 'info'); const workflowDir = '.github/workflows'; if (fs.existsSync(workflowDir)) { const workflows = fs.readdirSync(workflowDir); if (workflows.length === 0) { this.addWarning('No GitHub Actions workflows found'); } else { this.log(`Found ${workflows.length} workflow(s)`, 'success'); workflows.forEach(workflow => { const content = fs.readFileSync(path.join(workflowDir, workflow), 'utf8'); // Check for common workflow issues if (!content.includes('node-version')) { this.addWarning(`${workflow}: No Node.js version specified`); } if (!content.includes('npm ci') && !content.includes('npm install')) { this.addWarning(`${workflow}: No dependency installation step`); } }); } } else { this.addWarning('No GitHub Actions workflows directory found'); } } generateReport() { this.log('\n[ANALYTICS] Code Quality Report', 'info'); this.log('='.repeat(50), 'info'); this.log(`Issues found: ${this.issues.length}`, this.issues.length > 0 ? 'error' : 'success'); this.log(`Warnings: ${this.warnings.length}`, this.warnings.length > 0 ? 'warning' : 'success'); this.log(`Suggestions: ${this.suggestions.length}`, 'info'); if (this.issues.length === 0 && this.warnings.length === 0) { this.log('\n No critical issues found!', 'success'); } else { this.log('\n Summary:', 'info'); if (this.issues.length > 0) { this.log('\nCritical Issues:', 'error'); this.issues.forEach((issue, index) => { console.log(` ${index + 1}. ${issue}`); }); } if (this.warnings.length > 0) { this.log('\nWarnings:', 'warning'); this.warnings.forEach((warning, index) => { console.log(` ${index + 1}. ${warning}`); }); } } if (this.suggestions.length > 0) { this.log('\nSuggestions for improvement:', 'info'); this.suggestions.forEach((suggestion, index) => { console.log(` ${index + 1}. ${suggestion}`); }); } return this.issues.length === 0; } run() { this.log('[SEARCH] Starting code quality check...', 'info'); this.checkPackageJson(); this.checkEnvironmentFiles(); this.checkGitIgnore(); this.checkDocumentation(); this.checkSecurityFiles(); this.checkTestConfiguration(); this.checkWorkflowFiles(); const success = this.generateReport(); this.log('\n Code quality check completed!', 'success'); process.exit(success ? 0 : 1); } } // Run the checker if (require.main === module) { const checker = new CodeQualityChecker(); checker.run(); } module.exports = CodeQualityChecker;