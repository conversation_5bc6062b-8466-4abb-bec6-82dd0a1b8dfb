#!/bin/bash

# 🚀 Free Mobile Chatbot - Production Deployment Script
# This script builds and deploys the application to production

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="free-mobile-chatbot"
DOCKER_REGISTRY="freemobile"
VERSION=${1:-"latest"}
ENVIRONMENT=${2:-"production"}

echo -e "${BLUE}🚀 Starting Free Mobile Chatbot Production Deployment${NC}"
echo -e "${BLUE}Version: ${VERSION}${NC}"
echo -e "${BLUE}Environment: ${ENVIRONMENT}${NC}"
echo "=================================================="

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check prerequisites
echo -e "${BLUE}📋 Checking prerequisites...${NC}"

if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed"
    exit 1
fi

if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed"
    exit 1
fi

print_status "Prerequisites check passed"

# Load environment variables
if [ -f ".env.production" ]; then
    export $(cat .env.production | grep -v '^#' | xargs)
    print_status "Loaded production environment variables"
else
    print_warning "No .env.production file found, using defaults"
fi

# Run tests before deployment
echo -e "${BLUE}🧪 Running tests...${NC}"

# Frontend tests
echo "Running frontend tests..."
cd frontend
npm ci --legacy-peer-deps
npm test -- --coverage --watchAll=false

if [ $? -ne 0 ]; then
    print_error "Frontend tests failed"
    exit 1
fi

print_status "Frontend tests passed"

# Backend tests
echo "Running backend tests..."
cd ../backend
npm ci

# Set test environment
export NODE_ENV=test
export MONGODB_URI="mongodb://localhost:27017/test"
export JWT_SECRET="test-secret"

npm test

if [ $? -ne 0 ]; then
    print_error "Backend tests failed"
    exit 1
fi

print_status "Backend tests passed"

cd ..

# Build production images
echo -e "${BLUE}🏗️  Building production images...${NC}"

# Build frontend
echo "Building frontend image..."
docker build -t ${DOCKER_REGISTRY}/${PROJECT_NAME}-frontend:${VERSION} \
             -t ${DOCKER_REGISTRY}/${PROJECT_NAME}-frontend:latest \
             --target production \
             ./frontend

if [ $? -ne 0 ]; then
    print_error "Frontend build failed"
    exit 1
fi

print_status "Frontend image built successfully"

# Build backend
echo "Building backend image..."
docker build -t ${DOCKER_REGISTRY}/${PROJECT_NAME}-backend:${VERSION} \
             -t ${DOCKER_REGISTRY}/${PROJECT_NAME}-backend:latest \
             --target production \
             ./backend

if [ $? -ne 0 ]; then
    print_error "Backend build failed"
    exit 1
fi

print_status "Backend image built successfully"

# Security scan
echo -e "${BLUE}🔒 Running security scans...${NC}"

# Scan frontend image
echo "Scanning frontend image for vulnerabilities..."
docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
    aquasec/trivy:latest image ${DOCKER_REGISTRY}/${PROJECT_NAME}-frontend:${VERSION}

# Scan backend image
echo "Scanning backend image for vulnerabilities..."
docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
    aquasec/trivy:latest image ${DOCKER_REGISTRY}/${PROJECT_NAME}-backend:${VERSION}

print_status "Security scans completed"

# Deploy to production
echo -e "${BLUE}🚀 Deploying to production...${NC}"

# Stop existing containers
echo "Stopping existing containers..."
docker-compose -f docker-compose.prod.yml down

# Start new deployment
echo "Starting new deployment..."
docker-compose -f docker-compose.prod.yml up -d

if [ $? -ne 0 ]; then
    print_error "Deployment failed"
    exit 1
fi

print_status "Deployment started successfully"

# Wait for services to be healthy
echo -e "${BLUE}⏳ Waiting for services to be healthy...${NC}"

# Function to check service health
check_service_health() {
    local service=$1
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if docker-compose -f docker-compose.prod.yml ps $service | grep -q "healthy"; then
            print_status "$service is healthy"
            return 0
        fi
        
        echo "Waiting for $service to be healthy (attempt $attempt/$max_attempts)..."
        sleep 10
        attempt=$((attempt + 1))
    done
    
    print_error "$service failed to become healthy"
    return 1
}

# Check each service
check_service_health "mongodb"
check_service_health "redis"
check_service_health "backend"
check_service_health "frontend"

# Run smoke tests
echo -e "${BLUE}🧪 Running smoke tests...${NC}"

# Test backend health endpoint
echo "Testing backend health..."
curl -f http://localhost:5000/health || {
    print_error "Backend health check failed"
    exit 1
}

# Test frontend
echo "Testing frontend..."
curl -f http://localhost:3001/ || {
    print_error "Frontend health check failed"
    exit 1
}

print_status "Smoke tests passed"

# Performance check
echo -e "${BLUE}📊 Running performance check...${NC}"

# Install lighthouse if not present
if ! command -v lighthouse &> /dev/null; then
    npm install -g lighthouse
fi

# Run lighthouse audit
lighthouse http://localhost:3001 --output json --output-path ./lighthouse-report.json --chrome-flags="--headless"

# Check performance score
PERFORMANCE_SCORE=$(node -e "
    const report = require('./lighthouse-report.json');
    console.log(Math.round(report.lhr.categories.performance.score * 100));
")

if [ $PERFORMANCE_SCORE -lt 80 ]; then
    print_warning "Performance score is below 80: $PERFORMANCE_SCORE"
else
    print_status "Performance score: $PERFORMANCE_SCORE"
fi

# Cleanup
echo -e "${BLUE}🧹 Cleaning up...${NC}"

# Remove old images
docker image prune -f

print_status "Cleanup completed"

# Final status
echo "=================================================="
echo -e "${GREEN}🎉 Deployment completed successfully!${NC}"
echo -e "${GREEN}Frontend: http://localhost:3001${NC}"
echo -e "${GREEN}Backend API: http://localhost:5000${NC}"
echo -e "${GREEN}Admin Panel: http://localhost:8080${NC}"
echo -e "${GREEN}Monitoring: http://localhost:9090${NC}"
echo "=================================================="

# Show running containers
echo -e "${BLUE}📋 Running containers:${NC}"
docker-compose -f docker-compose.prod.yml ps

echo -e "${GREEN}✅ Production deployment completed successfully!${NC}"
