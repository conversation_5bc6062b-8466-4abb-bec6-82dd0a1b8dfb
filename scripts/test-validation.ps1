# Simple validation script to test the automated testing system
param(
    [switch]$DryRun = $false
)

$ErrorActionPreference = "Stop"

# Colors
$Colors = @{
    Red = "Red"
    Green = "Green"
    Yellow = "Yellow"
    Blue = "Blue"
    Cyan = "Cyan"
}

function Write-TestInfo {
    param([string]$Message)
    Write-Host "[TEST] $Message" -ForegroundColor $Colors.Blue
}

function Write-TestSuccess {
    param([string]$Message)
    Write-Host "[PASS] $Message" -ForegroundColor $Colors.Green
}

function Write-TestError {
    param([string]$Message)
    Write-Host "[FAIL] $Message" -ForegroundColor $Colors.Red
}

function Test-Prerequisites {
    Write-TestInfo "Testing prerequisites..."
    
    $allGood = $true
    
    # Test Docker
    try {
        $dockerVersion = docker --version
        Write-TestSuccess "Docker available: $dockerVersion"
    }
    catch {
        Write-TestError "Docker not available"
        $allGood = $false
    }
    
    # Test Docker Compose
    try {
        $composeVersion = docker compose version
        Write-TestSuccess "Docker Compose available: $composeVersion"
    }
    catch {
        Write-TestError "Docker Compose not available"
        $allGood = $false
    }
    
    # Test Node.js
    try {
        $nodeVersion = node --version
        Write-TestSuccess "Node.js available: $nodeVersion"
    }
    catch {
        Write-TestError "Node.js not available"
        $allGood = $false
    }
    
    # Test PowerShell execution policy
    $executionPolicy = Get-ExecutionPolicy
    if ($executionPolicy -eq "Restricted") {
        Write-TestError "PowerShell execution policy is Restricted. Run: Set-ExecutionPolicy RemoteSigned"
        $allGood = $false
    }
    else {
        Write-TestSuccess "PowerShell execution policy: $executionPolicy"
    }
    
    return $allGood
}

function Test-ProjectStructure {
    Write-TestInfo "Testing project structure..."
    
    $requiredPaths = @(
        "frontend/package.json",
        "backend/package.json",
        "docker-compose.prod.yml",
        "scripts/automated-testing.ps1",
        "frontend/playwright.config.ts",
        "frontend/e2e"
    )
    
    $allGood = $true
    
    foreach ($path in $requiredPaths) {
        if (Test-Path $path) {
            Write-TestSuccess "✓ $path exists"
        }
        else {
            Write-TestError "✗ $path missing"
            $allGood = $false
        }
    }
    
    return $allGood
}

function Test-ScriptSyntax {
    Write-TestInfo "Testing script syntax..."
    
    try {
        # Test PowerShell script syntax
        $null = [System.Management.Automation.PSParser]::Tokenize((Get-Content "scripts/automated-testing.ps1" -Raw), [ref]$null)
        Write-TestSuccess "PowerShell script syntax is valid"
        
        # Test if script can be imported
        . .\scripts\automated-testing.ps1 -TestSuite "unit" -WhatIf 2>$null
        Write-TestSuccess "PowerShell script can be executed"
        
        return $true
    }
    catch {
        Write-TestError "Script syntax error: $_"
        return $false
    }
}

function Test-PortAvailability {
    Write-TestInfo "Testing port availability..."
    
    $requiredPorts = @(3000, 3001, 5000, 27017, 6379)
    $portsInUse = @()
    
    foreach ($port in $requiredPorts) {
        $connection = Test-NetConnection -ComputerName localhost -Port $port -InformationLevel Quiet -WarningAction SilentlyContinue
        if ($connection) {
            $portsInUse += $port
            Write-TestError "Port $port is in use"
        }
        else {
            Write-TestSuccess "Port $port is available"
        }
    }
    
    if ($portsInUse.Count -gt 0) {
        Write-TestError "Ports in use: $($portsInUse -join ', '). Stop services using these ports."
        return $false
    }
    
    return $true
}

function Test-Dependencies {
    Write-TestInfo "Testing dependencies..."
    
    $allGood = $true
    
    # Check if frontend dependencies are installed
    if (Test-Path "frontend/node_modules") {
        Write-TestSuccess "Frontend dependencies installed"
    }
    else {
        Write-TestError "Frontend dependencies not installed. Run: cd frontend && npm install"
        $allGood = $false
    }
    
    # Check if Playwright is installed
    if (Test-Path "frontend/node_modules/@playwright") {
        Write-TestSuccess "Playwright is installed"
    }
    else {
        Write-TestError "Playwright not installed. Run: cd frontend && npx playwright install"
        $allGood = $false
    }
    
    return $allGood
}

function Show-ValidationSummary {
    param($Results)
    
    Write-Host "`n" -NoNewline
    Write-Host "=" * 60 -ForegroundColor $Colors.Cyan
    Write-Host "                VALIDATION SUMMARY" -ForegroundColor $Colors.Cyan
    Write-Host "=" * 60 -ForegroundColor $Colors.Cyan
    
    $totalTests = $Results.Count
    $passedTests = ($Results.Values | Where-Object { $_ }).Count
    
    foreach ($result in $Results.GetEnumerator()) {
        $testName = $result.Key
        $testResult = $result.Value
        
        Write-Host "`n📋 $testName`: " -NoNewline -ForegroundColor $Colors.Blue
        
        if ($testResult) {
            Write-Host "PASSED ✅" -ForegroundColor $Colors.Green
        }
        else {
            Write-Host "FAILED ❌" -ForegroundColor $Colors.Red
        }
    }
    
    Write-Host "`n" -NoNewline
    Write-Host "🎯 OVERALL RESULT: " -NoNewline -ForegroundColor $Colors.Blue
    
    if ($passedTests -eq $totalTests) {
        Write-Host "READY FOR TESTING ✅" -ForegroundColor $Colors.Green
        Write-Host "`n🚀 You can now run the automated testing script:" -ForegroundColor $Colors.Cyan
        Write-Host "   .\scripts\automated-testing.ps1" -ForegroundColor $Colors.Yellow
    }
    elseif ($passedTests -gt 0) {
        Write-Host "PARTIAL READINESS ⚠️ ($passedTests/$totalTests)" -ForegroundColor $Colors.Yellow
        Write-Host "`n🔧 Fix the failed validations before running tests" -ForegroundColor $Colors.Yellow
    }
    else {
        Write-Host "NOT READY ❌" -ForegroundColor $Colors.Red
        Write-Host "`n🔧 Fix all validations before running tests" -ForegroundColor $Colors.Red
    }
    
    Write-Host "=" * 60 -ForegroundColor $Colors.Cyan
}

# Main execution
function Main {
    Write-Host "🔍 ChatbotRNCP Testing System Validation" -ForegroundColor $Colors.Cyan
    Write-Host "Checking if the system is ready for automated testing..." -ForegroundColor $Colors.Blue
    
    if ($DryRun) {
        Write-Host "🧪 DRY RUN MODE - No actual changes will be made" -ForegroundColor $Colors.Yellow
    }
    
    $validationResults = @{}
    
    $validationResults["Prerequisites"] = Test-Prerequisites
    $validationResults["Project Structure"] = Test-ProjectStructure
    $validationResults["Script Syntax"] = Test-ScriptSyntax
    $validationResults["Port Availability"] = Test-PortAvailability
    $validationResults["Dependencies"] = Test-Dependencies
    
    Show-ValidationSummary $validationResults
    
    # Exit with appropriate code
    $failedValidations = ($validationResults.Values | Where-Object { -not $_ }).Count
    if ($failedValidations -gt 0) {
        exit 1
    }
    else {
        exit 0
    }
}

# Run validation
Main
