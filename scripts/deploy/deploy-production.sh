#!/bin/bash
# =============================================
# 🚀 PRODUCTION DEPLOYMENT SCRIPT
# Free Mobile Chatbot Dashboard - Phase 4 Production
# Automated deployment with health checks and rollback
# =============================================

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
CONFIG_FILE="${SCRIPT_DIR}/deploy.conf"

# Default configuration
ENVIRONMENT="${ENVIRONMENT:-production}"
DOCKER_COMPOSE_FILE="${DOCKER_COMPOSE_FILE:-docker-compose.production.yml}"
ENV_FILE="${ENV_FILE:-.env.prod}"
BACKUP_DIR="${BACKUP_DIR:-/backups/deployment}"
HEALTH_CHECK_TIMEOUT="${HEALTH_CHECK_TIMEOUT:-300}"
ROLLBACK_ON_FAILURE="${ROLLBACK_ON_FAILURE:-true}"

# Service configuration
SERVICES=("mongodb-primary" "mongodb-secondary1" "mongodb-secondary2" "timescaledb" "redis-master" "ml-service" "backend" "frontend" "nginx")
CRITICAL_SERVICES=("mongodb-primary" "timescaledb" "redis-master" "ml-service" "backend" "frontend")

# Logging
LOG_FILE="${BACKUP_DIR}/logs/deployment-$(date +%Y%m%d_%H%M%S).log"
DEPLOYMENT_ID="deploy-$(date +%Y%m%d_%H%M%S)"

# Functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a "$LOG_FILE"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}" | tee -a "$LOG_FILE"
}

# Load configuration if exists
if [[ -f "$CONFIG_FILE" ]]; then
    source "$CONFIG_FILE"
    log "Configuration loaded from $CONFIG_FILE"
fi

# Create directories
create_directories() {
    log "Creating deployment directories..."
    mkdir -p "$BACKUP_DIR"/{logs,configs,data}
}

# Pre-deployment checks
pre_deployment_checks() {
    log "Running pre-deployment checks..."
    
    # Check if Docker and Docker Compose are available
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed or not in PATH"
        exit 1
    fi
    
    # Check if required files exist
    if [[ ! -f "$PROJECT_ROOT/$DOCKER_COMPOSE_FILE" ]]; then
        error "Docker Compose file not found: $DOCKER_COMPOSE_FILE"
        exit 1
    fi
    
    if [[ ! -f "$PROJECT_ROOT/$ENV_FILE" ]]; then
        error "Environment file not found: $ENV_FILE"
        exit 1
    fi
    
    # Check disk space (require at least 10GB free)
    AVAILABLE_SPACE=$(df "$PROJECT_ROOT" | awk 'NR==2 {print $4}')
    REQUIRED_SPACE=10485760  # 10GB in KB
    
    if [[ $AVAILABLE_SPACE -lt $REQUIRED_SPACE ]]; then
        error "Insufficient disk space. Available: ${AVAILABLE_SPACE}KB, Required: ${REQUIRED_SPACE}KB"
        exit 1
    fi
    
    # Check if ports are available
    check_ports
    
    log "Pre-deployment checks passed"
}

# Check if required ports are available
check_ports() {
    local ports=(80 443 5000 5001 3001 27017 5432 6379 9090 3000 9200 5601)
    
    for port in "${ports[@]}"; do
        if netstat -tuln | grep -q ":$port "; then
            warn "Port $port is already in use"
        fi
    done
}

# Backup current deployment
backup_current_deployment() {
    log "Backing up current deployment..."
    
    local backup_path="$BACKUP_DIR/configs/$DEPLOYMENT_ID"
    mkdir -p "$backup_path"
    
    # Backup Docker Compose configuration
    if docker-compose -f "$PROJECT_ROOT/$DOCKER_COMPOSE_FILE" config > "$backup_path/docker-compose-backup.yml" 2>/dev/null; then
        log "Docker Compose configuration backed up"
    else
        warn "Failed to backup Docker Compose configuration"
    fi
    
    # Backup environment file
    if [[ -f "$PROJECT_ROOT/$ENV_FILE" ]]; then
        cp "$PROJECT_ROOT/$ENV_FILE" "$backup_path/env-backup"
        log "Environment file backed up"
    fi
    
    # Backup running container information
    docker ps --format "table {{.Names}}\t{{.Image}}\t{{.Status}}" > "$backup_path/running-containers.txt" 2>/dev/null || true
    
    # Export current images
    docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.ID}}" > "$backup_path/current-images.txt" 2>/dev/null || true
    
    log "Current deployment backed up to $backup_path"
}

# Pull latest images
pull_images() {
    log "Pulling latest Docker images..."
    
    cd "$PROJECT_ROOT"
    
    if docker-compose -f "$DOCKER_COMPOSE_FILE" pull; then
        log "Docker images pulled successfully"
    else
        error "Failed to pull Docker images"
        return 1
    fi
}

# Deploy services
deploy_services() {
    log "Starting deployment of services..."
    
    cd "$PROJECT_ROOT"
    
    # Start databases first
    log "Starting database services..."
    if docker-compose -f "$DOCKER_COMPOSE_FILE" up -d mongodb-primary mongodb-secondary1 mongodb-secondary2 timescaledb redis-master; then
        log "Database services started"
    else
        error "Failed to start database services"
        return 1
    fi
    
    # Wait for databases to be ready
    wait_for_databases
    
    # Start application services
    log "Starting application services..."
    if docker-compose -f "$DOCKER_COMPOSE_FILE" up -d ml-service backend frontend; then
        log "Application services started"
    else
        error "Failed to start application services"
        return 1
    fi
    
    # Start reverse proxy
    log "Starting reverse proxy..."
    if docker-compose -f "$DOCKER_COMPOSE_FILE" up -d nginx; then
        log "Reverse proxy started"
    else
        error "Failed to start reverse proxy"
        return 1
    fi
    
    # Start monitoring services
    log "Starting monitoring services..."
    if docker-compose -f "$DOCKER_COMPOSE_FILE" up -d prometheus grafana elasticsearch logstash kibana; then
        log "Monitoring services started"
    else
        warn "Failed to start some monitoring services"
    fi
    
    log "All services deployment initiated"
}

# Wait for databases to be ready
wait_for_databases() {
    log "Waiting for databases to be ready..."
    
    # Wait for MongoDB
    local mongo_ready=false
    for i in {1..30}; do
        if docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T mongodb-primary mongosh --eval "db.adminCommand('ping')" &>/dev/null; then
            mongo_ready=true
            break
        fi
        sleep 10
    done
    
    if [[ "$mongo_ready" == "true" ]]; then
        log "MongoDB is ready"
    else
        error "MongoDB failed to start within timeout"
        return 1
    fi
    
    # Wait for TimescaleDB
    local postgres_ready=false
    for i in {1..30}; do
        if docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T timescaledb pg_isready -U timescale_admin &>/dev/null; then
            postgres_ready=true
            break
        fi
        sleep 10
    done
    
    if [[ "$postgres_ready" == "true" ]]; then
        log "TimescaleDB is ready"
    else
        error "TimescaleDB failed to start within timeout"
        return 1
    fi
    
    # Wait for Redis
    local redis_ready=false
    for i in {1..20}; do
        if docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T redis-master redis-cli ping &>/dev/null; then
            redis_ready=true
            break
        fi
        sleep 5
    done
    
    if [[ "$redis_ready" == "true" ]]; then
        log "Redis is ready"
    else
        error "Redis failed to start within timeout"
        return 1
    fi
    
    log "All databases are ready"
}

# Health checks
perform_health_checks() {
    log "Performing health checks..."
    
    local health_check_start=$(date +%s)
    local all_healthy=true
    
    # Define health check endpoints
    declare -A health_endpoints=(
        ["backend"]="http://localhost:5000/api/health"
        ["ml-service"]="http://localhost:5001/health"
        ["frontend"]="http://localhost:3001/health"
        ["nginx"]="http://localhost/health"
    )
    
    # Check each service
    for service in "${!health_endpoints[@]}"; do
        local endpoint="${health_endpoints[$service]}"
        local service_healthy=false
        
        log "Checking health of $service..."
        
        for i in {1..30}; do
            if curl -f -s "$endpoint" &>/dev/null; then
                service_healthy=true
                log "$service is healthy"
                break
            fi
            sleep 10
        done
        
        if [[ "$service_healthy" == "false" ]]; then
            error "$service failed health check"
            all_healthy=false
        fi
    done
    
    # Check Docker container health
    for service in "${CRITICAL_SERVICES[@]}"; do
        local container_health=$(docker-compose -f "$DOCKER_COMPOSE_FILE" ps -q "$service" | xargs docker inspect --format='{{.State.Health.Status}}' 2>/dev/null || echo "unknown")
        
        if [[ "$container_health" == "healthy" || "$container_health" == "unknown" ]]; then
            log "$service container is healthy"
        else
            error "$service container is unhealthy: $container_health"
            all_healthy=false
        fi
    done
    
    local health_check_end=$(date +%s)
    local health_check_duration=$((health_check_end - health_check_start))
    
    if [[ "$all_healthy" == "true" ]]; then
        log "All health checks passed (Duration: ${health_check_duration}s)"
        return 0
    else
        error "Some health checks failed (Duration: ${health_check_duration}s)"
        return 1
    fi
}

# Run database migrations
run_migrations() {
    log "Running database migrations..."
    
    # Run MongoDB migrations
    if docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T backend npm run migrate; then
        log "MongoDB migrations completed"
    else
        warn "MongoDB migrations failed or not available"
    fi
    
    # Run TimescaleDB migrations
    if docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T ml-service python -m alembic upgrade head; then
        log "TimescaleDB migrations completed"
    else
        warn "TimescaleDB migrations failed or not available"
    fi
}

# Rollback deployment
rollback_deployment() {
    error "Rolling back deployment..."
    
    cd "$PROJECT_ROOT"
    
    # Stop current services
    docker-compose -f "$DOCKER_COMPOSE_FILE" down
    
    # Restore from backup if available
    local latest_backup=$(find "$BACKUP_DIR/configs" -name "deploy-*" -type d | sort -r | head -1)
    
    if [[ -n "$latest_backup" && -f "$latest_backup/docker-compose-backup.yml" ]]; then
        log "Restoring from backup: $latest_backup"
        
        # Restore configuration
        cp "$latest_backup/docker-compose-backup.yml" "$PROJECT_ROOT/$DOCKER_COMPOSE_FILE"
        
        if [[ -f "$latest_backup/env-backup" ]]; then
            cp "$latest_backup/env-backup" "$PROJECT_ROOT/$ENV_FILE"
        fi
        
        # Start services with backup configuration
        docker-compose -f "$DOCKER_COMPOSE_FILE" up -d
        
        log "Rollback completed"
    else
        error "No backup found for rollback"
    fi
}

# Send notifications
send_notification() {
    local status="$1"
    local message="$2"
    
    # Send email notification if configured
    if [[ -n "${NOTIFICATION_EMAIL:-}" ]]; then
        local subject="Deployment $status - Free Mobile Chatbot"
        echo "$message" | mail -s "$subject" "$NOTIFICATION_EMAIL" 2>/dev/null || true
    fi
    
    # Send Slack notification if configured
    if [[ -n "${SLACK_WEBHOOK_URL:-}" ]]; then
        local payload="{\"text\":\"🚀 Deployment $status: $message\"}"
        curl -X POST -H 'Content-type: application/json' --data "$payload" "$SLACK_WEBHOOK_URL" 2>/dev/null || true
    fi
    
    # Log to syslog
    logger -t "freemobile-deployment" "$status: $message"
}

# Generate deployment report
generate_deployment_report() {
    local status="$1"
    local start_time="$2"
    local end_time="$3"
    
    local duration=$((end_time - start_time))
    
    cat << EOF
Free Mobile Chatbot Deployment Report
=====================================
Deployment ID: $DEPLOYMENT_ID
Environment: $ENVIRONMENT
Status: $status
Start Time: $(date -d "@$start_time" '+%Y-%m-%d %H:%M:%S')
End Time: $(date -d "@$end_time" '+%Y-%m-%d %H:%M:%S')
Duration: ${duration}s

Services Deployed:
$(printf '%s\n' "${SERVICES[@]}")

Critical Services:
$(printf '%s\n' "${CRITICAL_SERVICES[@]}")

Configuration:
- Docker Compose File: $DOCKER_COMPOSE_FILE
- Environment File: $ENV_FILE
- Backup Directory: $BACKUP_DIR

Logs: $LOG_FILE
EOF
}

# Main deployment function
main() {
    local start_time=$(date +%s)
    
    log "🚀 Starting Free Mobile Chatbot production deployment..."
    log "Deployment ID: $DEPLOYMENT_ID"
    log "Environment: $ENVIRONMENT"
    
    # Create directories
    create_directories
    
    # Pre-deployment checks
    if ! pre_deployment_checks; then
        error "Pre-deployment checks failed"
        exit 1
    fi
    
    # Backup current deployment
    backup_current_deployment
    
    # Pull latest images
    if ! pull_images; then
        error "Failed to pull images"
        exit 1
    fi
    
    # Deploy services
    if ! deploy_services; then
        error "Service deployment failed"
        if [[ "$ROLLBACK_ON_FAILURE" == "true" ]]; then
            rollback_deployment
        fi
        exit 1
    fi
    
    # Run database migrations
    run_migrations
    
    # Perform health checks
    if ! perform_health_checks; then
        error "Health checks failed"
        if [[ "$ROLLBACK_ON_FAILURE" == "true" ]]; then
            rollback_deployment
            send_notification "FAILED" "Deployment failed health checks and was rolled back"
            exit 1
        fi
    fi
    
    local end_time=$(date +%s)
    
    # Generate deployment report
    local report=$(generate_deployment_report "SUCCESS" "$start_time" "$end_time")
    log "$report"
    
    # Send success notification
    send_notification "SUCCESS" "Deployment completed successfully in $((end_time - start_time))s"
    
    log "🎉 Deployment completed successfully!"
    log "📊 Dashboard available at: http://localhost"
    log "📈 Monitoring available at: http://localhost:3000 (Grafana)"
    log "📋 Logs available at: http://localhost:5601 (Kibana)"
    
    exit 0
}

# Handle script arguments
case "${1:-deploy}" in
    deploy)
        main
        ;;
    rollback)
        rollback_deployment
        ;;
    health-check)
        perform_health_checks
        ;;
    --help|-h)
        echo "Usage: $0 [deploy|rollback|health-check]"
        echo "Deploy Free Mobile Chatbot to production environment"
        exit 0
        ;;
    *)
        error "Invalid command: $1"
        echo "Usage: $0 [deploy|rollback|health-check]"
        exit 1
        ;;
esac
