#!/bin/bash
# =============================================
# ✅ PRODUCTION ENVIRONMENT VALIDATION
# Free Mobile Chatbot Dashboard - Phase 4 Production
# Comprehensive validation of production deployment
# =============================================

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Validation results
VALIDATION_RESULTS=()
CRITICAL_FAILURES=0
WARNINGS=0
PASSED_CHECKS=0

# Functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
    WARNINGS=$((WARNINGS + 1))
    VALIDATION_RESULTS+=("⚠️  WARNING: $1")
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    CRITICAL_FAILURES=$((CRITICAL_FAILURES + 1))
    VALIDATION_RESULTS+=("❌ ERROR: $1")
}

success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] ✅ $1${NC}"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
    VALIDATION_RESULTS+=("✅ PASSED: $1")
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Validate system requirements
validate_system_requirements() {
    log "🔧 Validating system requirements..."
    
    # Check OS
    if [[ -f /etc/os-release ]]; then
        source /etc/os-release
        if [[ "$ID" =~ ^(ubuntu|debian|centos|rhel)$ ]]; then
            success "Operating system supported: $PRETTY_NAME"
        else
            warn "Operating system may not be fully supported: $PRETTY_NAME"
        fi
    else
        warn "Cannot determine operating system"
    fi
    
    # Check CPU cores
    CPU_CORES=$(nproc)
    if [[ $CPU_CORES -ge 8 ]]; then
        success "CPU cores sufficient: $CPU_CORES cores"
    elif [[ $CPU_CORES -ge 4 ]]; then
        warn "CPU cores below recommended (8+): $CPU_CORES cores"
    else
        error "CPU cores insufficient: $CPU_CORES cores (minimum 4 required)"
    fi
    
    # Check RAM
    TOTAL_RAM=$(free -g | awk '/^Mem:/{print $2}')
    if [[ $TOTAL_RAM -ge 16 ]]; then
        success "RAM sufficient: ${TOTAL_RAM}GB"
    elif [[ $TOTAL_RAM -ge 8 ]]; then
        warn "RAM below recommended (16GB+): ${TOTAL_RAM}GB"
    else
        error "RAM insufficient: ${TOTAL_RAM}GB (minimum 8GB required)"
    fi
    
    # Check disk space
    AVAILABLE_SPACE=$(df / | awk 'NR==2 {print int($4/1024/1024)}')
    if [[ $AVAILABLE_SPACE -ge 100 ]]; then
        success "Disk space sufficient: ${AVAILABLE_SPACE}GB available"
    elif [[ $AVAILABLE_SPACE -ge 50 ]]; then
        warn "Disk space below recommended (100GB+): ${AVAILABLE_SPACE}GB available"
    else
        error "Disk space insufficient: ${AVAILABLE_SPACE}GB available (minimum 50GB required)"
    fi
}

# Validate Docker installation
validate_docker() {
    log "🐳 Validating Docker installation..."
    
    # Check Docker
    if command -v docker &> /dev/null; then
        DOCKER_VERSION=$(docker --version | cut -d' ' -f3 | cut -d',' -f1)
        success "Docker installed: version $DOCKER_VERSION"
        
        # Check Docker daemon
        if docker info &> /dev/null; then
            success "Docker daemon running"
        else
            error "Docker daemon not running"
        fi
    else
        error "Docker not installed"
    fi
    
    # Check Docker Compose
    if command -v docker-compose &> /dev/null; then
        COMPOSE_VERSION=$(docker-compose --version | cut -d' ' -f3 | cut -d',' -f1)
        success "Docker Compose installed: version $COMPOSE_VERSION"
    else
        error "Docker Compose not installed"
    fi
}

# Validate network configuration
validate_network() {
    log "🌐 Validating network configuration..."
    
    # Check required ports
    local ports=(80 443 5000 5001 3001 27017 5432 6379 9090 3000 9200 5601)
    local ports_in_use=()
    
    for port in "${ports[@]}"; do
        if netstat -tuln 2>/dev/null | grep -q ":$port "; then
            ports_in_use+=($port)
        fi
    done
    
    if [[ ${#ports_in_use[@]} -eq 0 ]]; then
        success "All required ports available"
    else
        warn "Some ports already in use: ${ports_in_use[*]}"
    fi
    
    # Check internet connectivity
    if curl -s --connect-timeout 5 https://google.com &> /dev/null; then
        success "Internet connectivity available"
    else
        error "No internet connectivity"
    fi
}

# Validate configuration files
validate_configuration() {
    log "⚙️ Validating configuration files..."
    
    # Check Docker Compose file
    if [[ -f "$PROJECT_ROOT/docker-compose.production.yml" ]]; then
        success "Docker Compose production file exists"
        
        # Validate Docker Compose syntax
        if docker-compose -f "$PROJECT_ROOT/docker-compose.production.yml" config &> /dev/null; then
            success "Docker Compose configuration valid"
        else
            error "Docker Compose configuration invalid"
        fi
    else
        error "Docker Compose production file missing"
    fi
    
    # Check environment file
    if [[ -f "$PROJECT_ROOT/.env.prod" ]]; then
        success "Production environment file exists"
        
        # Check critical environment variables
        source "$PROJECT_ROOT/.env.prod"
        
        local required_vars=(
            "JWT_SECRET"
            "MONGO_ROOT_PASSWORD"
            "TIMESCALE_PASSWORD"
            "REDIS_PASSWORD"
            "ML_API_KEY"
        )
        
        for var in "${required_vars[@]}"; do
            if [[ -n "${!var:-}" ]]; then
                success "Environment variable $var configured"
            else
                error "Environment variable $var not configured"
            fi
        done
    else
        error "Production environment file missing"
    fi
    
    # Check SSL certificates
    if [[ -f "$PROJECT_ROOT/ssl/your-domain.crt" && -f "$PROJECT_ROOT/ssl/your-domain.key" ]]; then
        success "SSL certificates found"
    else
        warn "SSL certificates not found (HTTPS will not work)"
    fi
}

# Validate services health
validate_services() {
    log "🔍 Validating services health..."
    
    # Check if services are running
    cd "$PROJECT_ROOT"
    
    local services=(
        "mongodb-primary"
        "timescaledb"
        "redis-master"
        "ml-service"
        "backend"
        "frontend"
        "nginx"
    )
    
    for service in "${services[@]}"; do
        if docker-compose -f docker-compose.production.yml ps -q "$service" &> /dev/null; then
            local status=$(docker-compose -f docker-compose.production.yml ps "$service" | tail -n +3 | awk '{print $4}')
            if [[ "$status" == "Up" ]]; then
                success "Service $service is running"
            else
                error "Service $service is not running: $status"
            fi
        else
            warn "Service $service not found (may not be deployed yet)"
        fi
    done
}

# Validate endpoints
validate_endpoints() {
    log "🌐 Validating service endpoints..."
    
    # Define endpoints to check
    declare -A endpoints=(
        ["Frontend"]="http://localhost:3001/health"
        ["Backend API"]="http://localhost:5000/api/health"
        ["ML Service"]="http://localhost:5001/health"
        ["Nginx Proxy"]="http://localhost/health"
        ["Prometheus"]="http://localhost:9090/-/healthy"
        ["Grafana"]="http://localhost:3000/api/health"
    )
    
    for service in "${!endpoints[@]}"; do
        local endpoint="${endpoints[$service]}"
        
        if curl -f -s --connect-timeout 10 "$endpoint" &> /dev/null; then
            success "$service endpoint responding: $endpoint"
        else
            warn "$service endpoint not responding: $endpoint"
        fi
    done
}

# Validate database connections
validate_databases() {
    log "🗄️ Validating database connections..."
    
    cd "$PROJECT_ROOT"
    
    # MongoDB
    if docker-compose -f docker-compose.production.yml exec -T mongodb-primary mongosh --eval "db.adminCommand('ping')" &> /dev/null; then
        success "MongoDB connection successful"
    else
        error "MongoDB connection failed"
    fi
    
    # TimescaleDB
    if docker-compose -f docker-compose.production.yml exec -T timescaledb pg_isready -U timescale_admin &> /dev/null; then
        success "TimescaleDB connection successful"
    else
        error "TimescaleDB connection failed"
    fi
    
    # Redis
    if docker-compose -f docker-compose.production.yml exec -T redis-master redis-cli ping &> /dev/null; then
        success "Redis connection successful"
    else
        error "Redis connection failed"
    fi
}

# Validate security configuration
validate_security() {
    log "🔒 Validating security configuration..."
    
    # Check firewall status
    if command -v ufw &> /dev/null; then
        if ufw status | grep -q "Status: active"; then
            success "UFW firewall is active"
        else
            warn "UFW firewall is not active"
        fi
    else
        warn "UFW firewall not installed"
    fi
    
    # Check fail2ban
    if command -v fail2ban-client &> /dev/null; then
        if systemctl is-active --quiet fail2ban; then
            success "Fail2ban is active"
        else
            warn "Fail2ban is not active"
        fi
    else
        warn "Fail2ban not installed"
    fi
    
    # Check for default passwords
    if [[ -f "$PROJECT_ROOT/.env.prod" ]]; then
        source "$PROJECT_ROOT/.env.prod"
        
        if [[ "$JWT_SECRET" == *"change-this"* ]]; then
            error "JWT_SECRET contains default value - change immediately"
        fi
        
        if [[ "$MONGO_ROOT_PASSWORD" == *"SecurePassword"* ]]; then
            warn "MongoDB password appears to be default - consider changing"
        fi
    fi
}

# Generate validation report
generate_report() {
    log "📊 Generating validation report..."
    
    echo ""
    echo "========================================"
    echo "🚀 PRODUCTION VALIDATION REPORT"
    echo "========================================"
    echo "Date: $(date)"
    echo "Environment: Production"
    echo ""
    echo "📈 SUMMARY:"
    echo "  ✅ Passed: $PASSED_CHECKS"
    echo "  ⚠️  Warnings: $WARNINGS"
    echo "  ❌ Critical Failures: $CRITICAL_FAILURES"
    echo ""
    echo "📋 DETAILED RESULTS:"
    
    for result in "${VALIDATION_RESULTS[@]}"; do
        echo "  $result"
    done
    
    echo ""
    echo "========================================"
    
    if [[ $CRITICAL_FAILURES -eq 0 ]]; then
        if [[ $WARNINGS -eq 0 ]]; then
            echo "🎉 ALL VALIDATIONS PASSED!"
            echo "✅ Production environment is ready for deployment"
        else
            echo "⚠️  VALIDATION COMPLETED WITH WARNINGS"
            echo "🔧 Please review warnings before proceeding"
        fi
        echo "========================================"
        return 0
    else
        echo "❌ VALIDATION FAILED"
        echo "🚨 Critical issues must be resolved before deployment"
        echo "========================================"
        return 1
    fi
}

# Main validation function
main() {
    log "🚀 Starting Free Mobile Chatbot production validation..."
    
    validate_system_requirements
    validate_docker
    validate_network
    validate_configuration
    validate_services
    validate_endpoints
    validate_databases
    validate_security
    
    if generate_report; then
        exit 0
    else
        exit 1
    fi
}

# Handle script arguments
case "${1:-validate}" in
    validate)
        main
        ;;
    system)
        validate_system_requirements
        ;;
    docker)
        validate_docker
        ;;
    network)
        validate_network
        ;;
    config)
        validate_configuration
        ;;
    services)
        validate_services
        ;;
    endpoints)
        validate_endpoints
        ;;
    databases)
        validate_databases
        ;;
    security)
        validate_security
        ;;
    --help|-h)
        echo "Usage: $0 [validate|system|docker|network|config|services|endpoints|databases|security]"
        echo "Validate Free Mobile Chatbot production environment"
        exit 0
        ;;
    *)
        error "Invalid command: $1"
        echo "Usage: $0 [validate|system|docker|network|config|services|endpoints|databases|security]"
        exit 1
        ;;
esac
