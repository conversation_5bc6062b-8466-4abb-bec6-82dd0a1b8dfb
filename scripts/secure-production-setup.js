#!/usr/bin/env node /** * [SECURITY] SECURE PRODUCTION SETUP SCRIPT * Automated configuration for ChatbotRNCP production deployment * * This script: * 1. Generates secure credentials * 2. Rotates compromised API keys * 3. Configures production environment * 4. Validates all connections * 5. Prepares for Vercel deployment */ const crypto = require('crypto'); const fs = require('fs'); const path = require('path'); const readline = require('readline'); // Colors for console output const colors = { red: '\x1b[31m', green: '\x1b[32m', yellow: '\x1b[33m', blue: '\x1b[34m', magenta: '\x1b[35m', cyan: '\x1b[36m', white: '\x1b[37m', reset: '\x1b[0m', bright: '\x1b[1m' }; const log = { info: (msg) => console.log(`${colors.blue}ℹ ${msg}${colors.reset}`), success: (msg) => console.log(`${colors.green}[COMPLETE] ${msg}${colors.reset}`), warning: (msg) => console.log(`${colors.yellow} ${msg}${colors.reset}`), error: (msg) => console.log(`${colors.red}[FAILED] ${msg}${colors.reset}`), critical: (msg) => console.log(`${colors.red}${colors.bright} CRITICAL: ${msg}${colors.reset}`), header: (msg) => console.log(`${colors.cyan}${colors.bright}\n[SECURITY] ${msg}${colors.reset}\n`) }; // Create readline interface for user input const rl = readline.createInterface({ input: process.stdin, output: process.stdout }); // Utility functions const generateSecureSecret = (length = 64) => { return crypto.randomBytes(length).toString('base64'); }; const generateHexSecret = (length = 32) => { return crypto.randomBytes(length).toString('hex'); }; const askQuestion = (question) => { return new Promise((resolve) => { rl.question(`${colors.cyan} ${question}${colors.reset} `, resolve); }); }; const confirmAction = async (message) => { const answer = await askQuestion(`${message} (y/N): `); return answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes'; }; // Main setup function async function setupProductionEnvironment() { log.header('CHATBOT RNCP - SECURE PRODUCTION SETUP'); log.critical('SECURITY ALERT: Compromised credentials detected!'); log.warning('The following credentials were exposed and must be rotated:'); console.log(' • OpenAI API Key'); console.log(' • Redis/Upstash credentials'); console.log(' • All production secrets'); const proceed = await confirmAction('\nDo you want to proceed with secure credential generation?'); if (!proceed) { log.info('Setup cancelled by user.'); process.exit(0); } // Step 1: Generate new secure credentials log.header('STEP 1: GENERATING SECURE CREDENTIALS'); const credentials = { JWT_SECRET: generateSecureSecret(64), JWT_REFRESH_SECRET: generateSecureSecret(64), SESSION_SECRET: generateSecureSecret(64), API_KEY_ADMIN: generateHexSecret(32), API_KEY_AGENT: generateHexSecret(32), API_KEY_PUBLIC: generateHexSecret(32), BACKUP_ENCRYPTION_KEY: generateSecureSecret(32), ML_API_KEY: generateHexSecret(32), ANALYTICS_API_KEY: generateHexSecret(32), RASA_TOKEN: generateHexSecret(16) }; log.success('Generated secure credentials:'); Object.keys(credentials).forEach(key => { const maskedValue = credentials[key].substring(0, 8) + '...'; console.log(` • ${key}: ${maskedValue}`); }); // Step 2: Get user inputs for external services log.header('STEP 2: EXTERNAL SERVICE CONFIGURATION'); log.warning('You need to manually rotate these compromised credentials:'); console.log(' 1. OpenAI API Key: https://platform.openai.com/api-keys'); console.log(' 2. Redis/Upstash: https://console.upstash.com/'); console.log(' 3. MongoDB Atlas password: https://cloud.mongodb.com/'); const externalCredentials = {}; // OpenAI API Key const rotateOpenAI = await confirmAction('\nHave you generated a new OpenAI API key?'); if (rotateOpenAI) { externalCredentials.OPENAI_API_KEY = await askQuestion('Enter new OpenAI API key: '); } else { log.error('OpenAI API key rotation is REQUIRED for security!'); externalCredentials.OPENAI_API_KEY = 'REPLACE_WITH_NEW_OPENAI_KEY'; } // MongoDB Atlas const rotateMongoPassword = await confirmAction('Have you updated MongoDB Atlas password?'); if (rotateMongoPassword) { externalCredentials.MONGO_PASSWORD = await askQuestion('Enter new MongoDB password: '); } else { log.error('MongoDB password rotation is REQUIRED for security!'); externalCredentials.MONGO_PASSWORD = 'REPLACE_WITH_NEW_MONGO_PASSWORD'; } // Redis/Upstash const rotateRedis = await confirmAction('Have you created a new Redis/Upstash instance?'); if (rotateRedis) { externalCredentials.REDIS_URL = await askQuestion('Enter new Redis URL: '); externalCredentials.UPSTASH_REDIS_REST_URL = await askQuestion('Enter new Upstash REST URL: '); externalCredentials.UPSTASH_REDIS_REST_TOKEN = await askQuestion('Enter new Upstash REST token: '); } else { log.error('Redis credentials rotation is REQUIRED for security!'); externalCredentials.REDIS_URL = 'REPLACE_WITH_NEW_REDIS_URL'; externalCredentials.UPSTASH_REDIS_REST_URL = 'REPLACE_WITH_NEW_UPSTASH_URL'; externalCredentials.UPSTASH_REDIS_REST_TOKEN = 'REPLACE_WITH_NEW_UPSTASH_TOKEN'; } // Step 3: Create production environment file log.header('STEP 3: CREATING PRODUCTION ENVIRONMENT FILE'); const envContent = `# [SECURITY] CHATBOT RNCP - PRODUCTION ENVIRONMENT # Generated on: ${new Date().toISOString()} # NEVER COMMIT THIS FILE TO VERSION CONTROL # Environment NODE_ENV=production ENVIRONMENT=production # Application URLs FRONTEND_URL=https://chatbotrncp.vercel.app BACKEND_URL=https://chatbotrncp.vercel.app/api DOMAIN=chatbotrncp.vercel.app # [SECURITY] Security Credentials (AUTO-GENERATED) JWT_SECRET=${credentials.JWT_SECRET} JWT_EXPIRES_IN=24h JWT_REFRESH_SECRET=${credentials.JWT_REFRESH_SECRET} JWT_REFRESH_EXPIRES_IN=7d SESSION_SECRET=${credentials.SESSION_SECRET} # Database Configuration MONGODB_URI=mongodb+srv://Anderson-Archimed01:${externalCredentials.MONGO_PASSWORD}@chatbotrncp.za6xmim.mongodb.net/freemobile-chatbot?retryWrites=true&w=majority&appName=ChatbotRNCP DATABASE_NAME=freemobile-chatbot # Redis Configuration (ROTATED) REDIS_URL=${externalCredentials.REDIS_URL} UPSTASH_REDIS_REST_URL=${externalCredentials.UPSTASH_REDIS_REST_URL} UPSTASH_REDIS_REST_TOKEN=${externalCredentials.UPSTASH_REDIS_REST_TOKEN} # [AI] AI Services (ROTATED) OPENAI_API_KEY=${externalCredentials.OPENAI_API_KEY} OPENAI_MODEL=gpt-4 OPENAI_MAX_TOKENS=150 OPENAI_TEMPERATURE=0.7 # API Security (AUTO-GENERATED) API_KEY_ADMIN=${credentials.API_KEY_ADMIN} API_KEY_AGENT=${credentials.API_KEY_AGENT} API_KEY_PUBLIC=${credentials.API_KEY_PUBLIC} # [DEPLOY] Vercel Configuration VERCEL_TOKEN=\${VERCEL_TOKEN} VERCEL_TEAM=anderson-archimedes-projects VERCEL_PROJECT=prj_4ckip9ZMjJEAdyfPftgYO355Z5I7 # Security CORS_ORIGIN=https://chatbotrncp.vercel.app RATE_LIMIT_WINDOW_MS=900000 RATE_LIMIT_MAX_REQUESTS=100 # [ANALYTICS] Monitoring SENTRY_DSN=https://<EMAIL>/PROJECT_ID LOG_LEVEL=info # [TARGET] Features FEATURE_CHAT_HISTORY=true FEATURE_FILE_UPLOAD=true FEATURE_VOICE_MESSAGES=false FEATURE_VIDEO_CALLS=false FEATURE_MULTI_LANGUAGE=true `; const envPath = path.join(__dirname, '..', '.env.production'); fs.writeFileSync(envPath, envContent); // Set secure file permissions (Unix-like systems) try { fs.chmodSync(envPath, 0o600); log.success('Set secure file permissions (600) on .env.production'); } catch (error) { log.warning('Could not set file permissions (Windows system)'); } log.success(`Production environment file created: ${envPath}`); // Step 4: Create GitHub Secrets configuration log.header('STEP 4: GITHUB SECRETS CONFIGURATION'); const githubSecrets = { MONGODB_URI: `mongodb+srv://Anderson-Archimed01:${externalCredentials.MONGO_PASSWORD}@chatbotrncp.za6xmim.mongodb.net/freemobile-chatbot?retryWrites=true&w=majority&appName=ChatbotRNCP`, JWT_SECRET: credentials.JWT_SECRET, OPENAI_API_KEY: externalCredentials.OPENAI_API_KEY, REDIS_URL: externalCredentials.REDIS_URL, UPSTASH_REDIS_REST_URL: externalCredentials.UPSTASH_REDIS_REST_URL, UPSTASH_REDIS_REST_TOKEN: externalCredentials.UPSTASH_REDIS_REST_TOKEN, VERCEL_TOKEN: '${VERCEL_TOKEN}', VERCEL_TEAM: 'anderson-archimedes-projects', VERCEL_PROJECT: 'prj_4ckip9ZMjJEAdyfPftgYO355Z5I7' }; const secretsPath = path.join(__dirname, '..', 'github-secrets.json'); fs.writeFileSync(secretsPath, JSON.stringify(githubSecrets, null, 2)); log.success(`GitHub secrets configuration saved: ${secretsPath}`); log.warning('Add these secrets to your GitHub repository settings!'); // Step 5: Security validation log.header('STEP 5: SECURITY VALIDATION'); const securityChecks = [ { name: 'JWT Secret length', check: credentials.JWT_SECRET.length >= 64, required: true }, { name: 'Session Secret length', check: credentials.SESSION_SECRET.length >= 64, required: true }, { name: 'OpenAI API Key format', check: externalCredentials.OPENAI_API_KEY.startsWith('sk-'), required: true }, { name: 'MongoDB URI format', check: externalCredentials.MONGO_PASSWORD !== 'REPLACE_WITH_NEW_MONGO_PASSWORD', required: true }, { name: 'Redis URL format', check: externalCredentials.REDIS_URL !== 'REPLACE_WITH_NEW_REDIS_URL', required: true } ]; let allChecksPassed = true; securityChecks.forEach(check => { if (check.check) { log.success(`${check.name}: PASSED`); } else { if (check.required) { log.error(`${check.name}: FAILED (REQUIRED)`); allChecksPassed = false; } else { log.warning(`${check.name}: FAILED (OPTIONAL)`); } } }); // Final summary log.header('SETUP COMPLETE'); if (allChecksPassed) { log.success('All security checks passed!'); log.info('Next steps:'); console.log(' 1. Add GitHub secrets from github-secrets.json'); console.log(' 2. Update .gitignore to exclude .env.production'); console.log(' 3. Test connections with npm run validate:env'); console.log(' 4. Deploy to Vercel with npm run deploy:production'); } else { log.error('Some security checks failed. Please fix before deployment!'); process.exit(1); } rl.close(); } // Error handling process.on('uncaughtException', (error) => { log.error(`Uncaught Exception: ${error.message}`); process.exit(1); }); process.on('unhandledRejection', (reason, promise) => { log.error(`Unhandled Rejection at: ${promise}, reason: ${reason}`); process.exit(1); }); // Run the setup if (require.main === module) { setupProductionEnvironment().catch(error => { log.error(`Setup failed: ${error.message}`); process.exit(1); }); } module.exports = { setupProductionEnvironment };