#!/bin/bash

# =============================================
# 🏥 HEALTH MONITORING SCRIPT
# Comprehensive health checks for all services
# Real-time monitoring with alerting
# =============================================

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
COMPOSE_FILE="docker-compose.production.yml"
LOG_FILE="./logs/health-monitor.log"
ALERT_WEBHOOK_URL="${ALERT_WEBHOOK_URL:-}"
CHECK_INTERVAL=30
MAX_RETRIES=3

# Service configurations
declare -A SERVICES=(
    ["mongodb"]="27017"
    ["redis"]="6379"
    ["timescaledb"]="5432"
    ["backend"]="5000"
    ["ml-service"]="5001"
    ["multimodal-service"]="5009"
    ["call-system"]="5004"
    ["social-media-service"]="5010"
    ["frontend"]="3001"
    ["nginx"]="80"
)

declare -A SERVICE_HEALTH_ENDPOINTS=(
    ["backend"]="http://localhost:5000/health"
    ["ml-service"]="http://localhost:5001/health"
    ["multimodal-service"]="http://localhost:5009/health"
    ["call-system"]="http://localhost:5004/health"
    ["social-media-service"]="http://localhost:5010/health"
    ["frontend"]="http://localhost:3001/health"
    ["nginx"]="http://localhost/health"
)

declare -A SERVICE_STATUS
declare -A SERVICE_LAST_CHECK
declare -A SERVICE_FAILURE_COUNT

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

# Initialize monitoring
initialize_monitoring() {
    log "🏥 Initializing health monitoring..."
    
    # Create log directory
    mkdir -p "$(dirname "$LOG_FILE")"
    touch "$LOG_FILE"
    
    # Initialize service status
    for service in "${!SERVICES[@]}"; do
        SERVICE_STATUS[$service]="unknown"
        SERVICE_LAST_CHECK[$service]=0
        SERVICE_FAILURE_COUNT[$service]=0
    done
    
    success "Health monitoring initialized"
}

# Check service health
check_service_health() {
    local service=$1
    local port=${SERVICES[$service]}
    local endpoint=${SERVICE_HEALTH_ENDPOINTS[$service]:-}
    local status="healthy"
    local details=""
    
    # Check if container is running
    if ! docker-compose -f "$COMPOSE_FILE" ps | grep -q "$service.*Up"; then
        status="down"
        details="Container not running"
    else
        # Check port connectivity
        if ! nc -z localhost "$port" 2>/dev/null; then
            status="unhealthy"
            details="Port $port not accessible"
        elif [[ -n "$endpoint" ]]; then
            # Check health endpoint
            local http_code
            http_code=$(curl -s -o /dev/null -w "%{http_code}" "$endpoint" --max-time 10 || echo "000")
            
            if [[ "$http_code" != "200" ]]; then
                status="unhealthy"
                details="Health endpoint returned $http_code"
            fi
        fi
    fi
    
    # Update service status
    local previous_status=${SERVICE_STATUS[$service]}
    SERVICE_STATUS[$service]=$status
    SERVICE_LAST_CHECK[$service]=$(date +%s)
    
    # Handle status changes
    if [[ "$status" != "healthy" ]]; then
        SERVICE_FAILURE_COUNT[$service]=$((SERVICE_FAILURE_COUNT[$service] + 1))
        
        if [[ "$previous_status" == "healthy" ]]; then
            warning "Service $service became $status: $details"
            send_alert "service_unhealthy" "$service" "$status" "$details"
        fi
    else
        if [[ "$previous_status" != "healthy" && "$previous_status" != "unknown" ]]; then
            success "Service $service recovered"
            send_alert "service_recovered" "$service" "$status" "Service is now healthy"
        fi
        SERVICE_FAILURE_COUNT[$service]=0
    fi
    
    echo "$status"
}

# Check system resources
check_system_resources() {
    local cpu_usage
    local memory_usage
    local disk_usage
    
    # CPU usage
    cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')
    
    # Memory usage
    memory_usage=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
    
    # Disk usage
    disk_usage=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
    
    # Check thresholds
    if (( $(echo "$cpu_usage > 80" | bc -l) )); then
        warning "High CPU usage: ${cpu_usage}%"
        send_alert "high_cpu" "system" "warning" "CPU usage: ${cpu_usage}%"
    fi
    
    if (( $(echo "$memory_usage > 85" | bc -l) )); then
        warning "High memory usage: ${memory_usage}%"
        send_alert "high_memory" "system" "warning" "Memory usage: ${memory_usage}%"
    fi
    
    if (( disk_usage > 90 )); then
        warning "High disk usage: ${disk_usage}%"
        send_alert "high_disk" "system" "warning" "Disk usage: ${disk_usage}%"
    fi
    
    echo "CPU: ${cpu_usage}%, Memory: ${memory_usage}%, Disk: ${disk_usage}%"
}

# Check Docker health
check_docker_health() {
    if ! docker info >/dev/null 2>&1; then
        error "Docker daemon is not running"
        send_alert "docker_down" "docker" "critical" "Docker daemon is not responding"
        return 1
    fi
    
    # Check Docker Compose services
    local unhealthy_services=()
    
    while IFS= read -r line; do
        if [[ "$line" =~ unhealthy ]]; then
            local service_name=$(echo "$line" | awk '{print $1}')
            unhealthy_services+=("$service_name")
        fi
    done < <(docker-compose -f "$COMPOSE_FILE" ps --format "table {{.Name}}\t{{.Status}}" | tail -n +2)
    
    if [[ ${#unhealthy_services[@]} -gt 0 ]]; then
        warning "Unhealthy Docker services: ${unhealthy_services[*]}"
        for service in "${unhealthy_services[@]}"; do
            send_alert "docker_unhealthy" "$service" "warning" "Docker health check failed"
        done
    fi
    
    return 0
}

# Check database connections
check_database_connections() {
    local mongodb_status="unknown"
    local redis_status="unknown"
    local timescale_status="unknown"
    
    # MongoDB
    if docker-compose -f "$COMPOSE_FILE" exec -T mongodb-primary mongosh --eval "db.adminCommand('ping')" >/dev/null 2>&1; then
        mongodb_status="healthy"
    else
        mongodb_status="unhealthy"
        warning "MongoDB connection failed"
    fi
    
    # Redis
    if docker-compose -f "$COMPOSE_FILE" exec -T redis redis-cli ping >/dev/null 2>&1; then
        redis_status="healthy"
    else
        redis_status="unhealthy"
        warning "Redis connection failed"
    fi
    
    # TimescaleDB
    if docker-compose -f "$COMPOSE_FILE" exec -T timescaledb pg_isready >/dev/null 2>&1; then
        timescale_status="healthy"
    else
        timescale_status="unhealthy"
        warning "TimescaleDB connection failed"
    fi
    
    echo "MongoDB: $mongodb_status, Redis: $redis_status, TimescaleDB: $timescale_status"
}

# Check SSL certificates
check_ssl_certificates() {
    local cert_file="nginx/certs/fullchain.pem"
    
    if [[ -f "$cert_file" ]]; then
        local expiry_date
        expiry_date=$(openssl x509 -enddate -noout -in "$cert_file" | cut -d= -f2)
        local expiry_timestamp
        expiry_timestamp=$(date -d "$expiry_date" +%s)
        local current_timestamp
        current_timestamp=$(date +%s)
        local days_until_expiry
        days_until_expiry=$(( (expiry_timestamp - current_timestamp) / 86400 ))
        
        if (( days_until_expiry < 30 )); then
            warning "SSL certificate expires in $days_until_expiry days"
            send_alert "ssl_expiring" "ssl" "warning" "Certificate expires in $days_until_expiry days"
        elif (( days_until_expiry < 0 )); then
            error "SSL certificate has expired"
            send_alert "ssl_expired" "ssl" "critical" "SSL certificate has expired"
        fi
        
        echo "SSL certificate valid for $days_until_expiry days"
    else
        warning "SSL certificate file not found"
        echo "SSL certificate not found"
    fi
}

# Send alert
send_alert() {
    local alert_type=$1
    local service=$2
    local severity=$3
    local message=$4
    
    if [[ -z "$ALERT_WEBHOOK_URL" ]]; then
        return 0
    fi
    
    local payload
    payload=$(cat <<EOF
{
    "alert_type": "$alert_type",
    "service": "$service",
    "severity": "$severity",
    "message": "$message",
    "timestamp": "$(date -Iseconds)",
    "hostname": "$(hostname)",
    "environment": "production"
}
EOF
)
    
    curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$payload" \
        "$ALERT_WEBHOOK_URL" >/dev/null 2>&1 || true
}

# Generate health report
generate_health_report() {
    local report_file="./logs/health-report-$(date +%Y%m%d-%H%M%S).json"
    local overall_status="healthy"
    local unhealthy_services=()
    
    # Check overall status
    for service in "${!SERVICE_STATUS[@]}"; do
        if [[ "${SERVICE_STATUS[$service]}" != "healthy" ]]; then
            overall_status="unhealthy"
            unhealthy_services+=("$service")
        fi
    done
    
    # System resources
    local system_resources
    system_resources=$(check_system_resources)
    
    # Database status
    local database_status
    database_status=$(check_database_connections)
    
    # SSL status
    local ssl_status
    ssl_status=$(check_ssl_certificates)
    
    # Generate JSON report
    cat > "$report_file" <<EOF
{
    "timestamp": "$(date -Iseconds)",
    "overall_status": "$overall_status",
    "services": {
$(for service in "${!SERVICE_STATUS[@]}"; do
    echo "        \"$service\": {"
    echo "            \"status\": \"${SERVICE_STATUS[$service]}\","
    echo "            \"last_check\": ${SERVICE_LAST_CHECK[$service]},"
    echo "            \"failure_count\": ${SERVICE_FAILURE_COUNT[$service]}"
    echo "        },"
done | sed '$ s/,$//')
    },
    "system": {
        "resources": "$system_resources",
        "databases": "$database_status",
        "ssl": "$ssl_status"
    },
    "unhealthy_services": [$(printf '"%s",' "${unhealthy_services[@]}" | sed 's/,$//')]
}
EOF
    
    echo "$report_file"
}

# Display status dashboard
display_status() {
    clear
    echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║                    🏥 HEALTH MONITOR                         ║${NC}"
    echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
    
    echo -e "${BLUE}📊 Service Status:${NC}"
    echo "┌─────────────────────────┬──────────────┬─────────────────┐"
    echo "│ Service                 │ Status       │ Last Check      │"
    echo "├─────────────────────────┼──────────────┼─────────────────┤"
    
    for service in "${!SERVICES[@]}"; do
        local status=${SERVICE_STATUS[$service]}
        local last_check=${SERVICE_LAST_CHECK[$service]}
        local status_color
        
        case $status in
            "healthy") status_color="${GREEN}" ;;
            "unhealthy") status_color="${YELLOW}" ;;
            "down") status_color="${RED}" ;;
            *) status_color="${NC}" ;;
        esac
        
        local last_check_str="Never"
        if [[ $last_check -gt 0 ]]; then
            last_check_str=$(date -d "@$last_check" "+%H:%M:%S")
        fi
        
        printf "│ %-23s │ ${status_color}%-12s${NC} │ %-15s │\n" "$service" "$status" "$last_check_str"
    done
    
    echo "└─────────────────────────┴──────────────┴─────────────────┘"
    echo ""
    
    # System resources
    echo -e "${BLUE}💻 System Resources:${NC}"
    check_system_resources
    echo ""
    
    # Database status
    echo -e "${BLUE}🗄️ Database Status:${NC}"
    check_database_connections
    echo ""
    
    # SSL status
    echo -e "${BLUE}🔒 SSL Status:${NC}"
    check_ssl_certificates
    echo ""
    
    echo -e "${BLUE}Last update: $(date)${NC}"
    echo "Press Ctrl+C to stop monitoring"
}

# Main monitoring loop
start_monitoring() {
    log "🚀 Starting continuous health monitoring..."
    
    while true; do
        # Check all services
        for service in "${!SERVICES[@]}"; do
            check_service_health "$service" >/dev/null
        done
        
        # Check Docker health
        check_docker_health >/dev/null
        
        # Display status
        display_status
        
        # Generate periodic reports
        local current_minute
        current_minute=$(date +%M)
        if [[ $((current_minute % 15)) -eq 0 ]]; then
            local report_file
            report_file=$(generate_health_report)
            log "📊 Health report generated: $report_file"
        fi
        
        # Wait for next check
        sleep $CHECK_INTERVAL
    done
}

# One-time health check
run_health_check() {
    log "🔍 Running one-time health check..."
    
    local overall_healthy=true
    
    echo -e "\n${BLUE}Service Health Check Results:${NC}"
    echo "================================"
    
    for service in "${!SERVICES[@]}"; do
        local status
        status=$(check_service_health "$service")
        
        local status_icon
        case $status in
            "healthy") status_icon="${GREEN}✅${NC}" ;;
            "unhealthy") status_icon="${YELLOW}⚠️${NC}" ;;
            "down") status_icon="${RED}❌${NC}"; overall_healthy=false ;;
            *) status_icon="${NC}❓${NC}" ;;
        esac
        
        echo -e "$status_icon $service: $status"
    done
    
    echo ""
    echo -e "${BLUE}System Resources:${NC}"
    check_system_resources
    
    echo ""
    echo -e "${BLUE}Database Connections:${NC}"
    check_database_connections
    
    echo ""
    echo -e "${BLUE}SSL Certificate:${NC}"
    check_ssl_certificates
    
    echo ""
    if $overall_healthy; then
        success "✅ Overall system health: HEALTHY"
        return 0
    else
        error "❌ Overall system health: UNHEALTHY"
        return 1
    fi
}

# Restart unhealthy services
restart_unhealthy_services() {
    log "🔄 Checking for unhealthy services to restart..."
    
    local services_to_restart=()
    
    for service in "${!SERVICES[@]}"; do
        if [[ "${SERVICE_STATUS[$service]}" != "healthy" && "${SERVICE_FAILURE_COUNT[$service]}" -ge $MAX_RETRIES ]]; then
            services_to_restart+=("$service")
        fi
    done
    
    if [[ ${#services_to_restart[@]} -gt 0 ]]; then
        warning "Restarting unhealthy services: ${services_to_restart[*]}"
        
        for service in "${services_to_restart[@]}"; do
            log "Restarting $service..."
            docker-compose -f "$COMPOSE_FILE" restart "$service"
            SERVICE_FAILURE_COUNT[$service]=0
            send_alert "service_restarted" "$service" "info" "Service automatically restarted"
        done
    else
        log "No services need restarting"
    fi
}

# Script options
case "${1:-monitor}" in
    "monitor")
        initialize_monitoring
        start_monitoring
        ;;
    "check")
        initialize_monitoring
        run_health_check
        ;;
    "restart")
        initialize_monitoring
        for service in "${!SERVICES[@]}"; do
            check_service_health "$service" >/dev/null
        done
        restart_unhealthy_services
        ;;
    "report")
        initialize_monitoring
        for service in "${!SERVICES[@]}"; do
            check_service_health "$service" >/dev/null
        done
        report_file=$(generate_health_report)
        success "Health report generated: $report_file"
        cat "$report_file"
        ;;
    "status")
        initialize_monitoring
        for service in "${!SERVICES[@]}"; do
            check_service_health "$service" >/dev/null
        done
        display_status
        ;;
    *)
        echo "Usage: $0 {monitor|check|restart|report|status}"
        echo ""
        echo "Commands:"
        echo "  monitor  - Start continuous monitoring (default)"
        echo "  check    - Run one-time health check"
        echo "  restart  - Restart unhealthy services"
        echo "  report   - Generate health report"
        echo "  status   - Show current status"
        exit 1
        ;;
esac
