#!/usr/bin/env node /** * ============================================= * [SECURITY] SECURE DEVELOPMENT SETUP SCRIPT * Free Mobile Chatbot RNCP - Safe Development Environment * ============================================= */ const fs = require('fs'); const path = require('path'); const { execSync } = require('child_process'); const crypto = require('crypto'); console.log('[SECURITY] Free Mobile Chatbot RNCP - Secure Development Setup'); console.log('===================================================='); console.log(''); // Colors for console output const colors = { reset: '\x1b[0m', red: '\x1b[31m', green: '\x1b[32m', yellow: '\x1b[33m', blue: '\x1b[34m', magenta: '\x1b[35m', cyan: '\x1b[36m' }; function log(message, color = 'reset') { console.log(`${colors[color]}${message}${colors.reset}`); } function generateSecureKey(length = 32) { return crypto.randomBytes(length).toString('hex'); } function generateJWTSecret() { return crypto.randomBytes(64).toString('base64'); } async function checkPrerequisites() { log('[SEARCH] Checking Prerequisites...', 'blue'); const prerequisites = [ { name: 'Node.js', command: 'node --version' }, { name: 'npm', command: 'npm --version' }, { name: 'Git', command: 'git --version' } ]; for (const prereq of prerequisites) { try { const version = execSync(prereq.command, { encoding: 'utf8' }).trim(); log(` [COMPLETE] ${prereq.name}: ${version}`, 'green'); } catch (error) { log(` [FAILED] ${prereq.name}: Not installed`, 'red'); throw new Error(`${prereq.name} is required but not installed`); } } log('', 'reset'); } async function setupEnvironment() { log(' Setting Up Development Environment...', 'blue'); // Create .env symlink to development environment const envPath = path.join(process.cwd(), '.env'); const devEnvPath = path.join(process.cwd(), '.env.development'); if (fs.existsSync(envPath)) { fs.unlinkSync(envPath); } // Copy development environment file fs.copyFileSync(devEnvPath, envPath); // Generate secure keys for development const jwtSecret = generateJWTSecret(); const sessionSecret = generateSecureKey(32); // Update .env with generated secrets let envContent = fs.readFileSync(envPath, 'utf8'); envContent = envContent.replace( 'JWT_SECRET=dev_jwt_secret_key_for_development_only_32_chars_minimum_length_required', `JWT_SECRET=${jwtSecret}` ); envContent = envContent.replace( 'SESSION_SECRET=dev_session_secret_key_for_development_32_chars_min', `SESSION_SECRET=${sessionSecret}` ); fs.writeFileSync(envPath, envContent); // Set secure permissions try { fs.chmodSync(envPath, 0o600); log(' [COMPLETE] Environment file created with secure permissions', 'green'); } catch (error) { log(' Could not set file permissions (Windows limitation)', 'yellow'); } log('', 'reset'); } async function installDependencies() { log(' Installing Dependencies...', 'blue'); try { // Install root dependencies log(' Installing root dependencies...', 'cyan'); execSync('npm install', { stdio: 'inherit' }); // Install backend dependencies log(' Installing backend dependencies...', 'cyan'); execSync('cd backend && npm install', { stdio: 'inherit' }); // Install frontend dependencies log(' Installing frontend dependencies...', 'cyan'); execSync('cd frontend && npm install', { stdio: 'inherit' }); log(' [COMPLETE] All dependencies installed successfully', 'green'); } catch (error) { log(' [FAILED] Failed to install dependencies', 'red'); throw error; } log('', 'reset'); } async function setupLocalServices() { log(' Setting Up Local Services...', 'blue'); // Check if MongoDB is available locally try { execSync('mongod --version', { stdio: 'pipe' }); log(' [COMPLETE] MongoDB is available locally', 'green'); } catch (error) { log(' MongoDB not found locally - using authentication server with mock data', 'yellow'); } // Check if Redis is available locally try { execSync('redis-server --version', { stdio: 'pipe' }); log(' [COMPLETE] Redis is available locally', 'green'); } catch (error) { log(' Redis not found locally - using in-memory storage', 'yellow'); } log('', 'reset'); } async function startServices() { log('[DEPLOY] Starting Development Services...', 'blue'); try { // Start authentication server (with mock data) log(' [SECURITY] Starting authentication server...', 'cyan'); const { spawn } = require('child_process'); const authServer = spawn('node', ['auth-server.js'], { cwd: path.join(process.cwd(), 'backend'), stdio: 'pipe' }); // Wait for server to start await new Promise((resolve) => { authServer.stdout.on('data', (data) => { if (data.toString().includes('Authentication server running')) { log(' [COMPLETE] Authentication server started on port 5000', 'green'); resolve(); } }); }); // Build and start frontend log(' [DESIGN] Building frontend application...', 'cyan'); execSync('cd frontend && npm run build', { stdio: 'pipe' }); log(' Starting frontend server...', 'cyan'); const frontendServer = spawn('npx', ['serve', '-s', 'build', '-p', '3000'], { cwd: path.join(process.cwd(), 'frontend'), stdio: 'pipe' }); // Wait for frontend to start await new Promise((resolve) => { setTimeout(() => { log(' [COMPLETE] Frontend server started on port 3000', 'green'); resolve(); }, 3000); }); } catch (error) { log(' [FAILED] Failed to start services', 'red'); throw error; } log('', 'reset'); } async function validateSetup() { log('[COMPLETE] Validating Setup...', 'blue'); const validations = [ { name: 'Environment file', check: () => fs.existsSync('.env'), message: 'Environment configuration loaded' }, { name: 'Backend dependencies', check: () => fs.existsSync('backend/node_modules'), message: 'Backend dependencies installed' }, { name: 'Frontend dependencies', check: () => fs.existsSync('frontend/node_modules'), message: 'Frontend dependencies installed' }, { name: 'Frontend build', check: () => fs.existsSync('frontend/build'), message: 'Frontend application built' } ]; for (const validation of validations) { if (validation.check()) { log(` [COMPLETE] ${validation.message}`, 'green'); } else { log(` [FAILED] ${validation.name} validation failed`, 'red'); } } log('', 'reset'); } async function displayResults() { log(' Setup Complete!', 'green'); log('================', 'green'); log(''); log(' Application URLs:', 'blue'); log(' Frontend: http://localhost:3000', 'cyan'); log(' Backend API: http://localhost:5000', 'cyan'); log(' API Health: http://localhost:5000/health', 'cyan'); log(''); log('[SECURITY] Test Credentials:', 'blue'); log(' Email: <EMAIL>', 'cyan'); log(' Password: AdminPassword123!', 'cyan'); log(''); log(' Available Commands:', 'blue'); log(' npm run validate:env - Validate configuration', 'cyan'); log(' npm run test:e2e - Run E2E tests', 'cyan'); log(' npm run start:backend - Start backend only', 'cyan'); log(' npm run start:frontend - Start frontend only', 'cyan'); log(''); log(' Security Notes:', 'yellow'); log(' - This is a DEVELOPMENT environment only', 'yellow'); log(' - Uses mock data and local services', 'yellow'); log(' - NOT suitable for production use', 'yellow'); log(' - Rotate credentials before production deployment', 'yellow'); log(''); log(' Important Security Reminder:', 'red'); log(' The credentials you provided earlier are now COMPROMISED', 'red'); log(' Please rotate them immediately:', 'red'); log(' 1. CircleCI: Generate new personal access token', 'red'); log(' 2. MongoDB: Change password in Atlas dashboard', 'red'); log(''); } // Main execution async function main() { try { await checkPrerequisites(); await setupEnvironment(); await installDependencies(); await setupLocalServices(); await startServices(); await validateSetup(); await displayResults(); log('[COMPLETE] Free Mobile Chatbot RNCP development environment is ready!', 'green'); } catch (error) { log('[FAILED] Setup failed:', 'red'); log(error.message, 'red'); process.exit(1); } } if (require.main === module) { main(); } module.exports = { main, checkPrerequisites, setupEnvironment, installDependencies, startServices, validateSetup };