const fs = require('fs'); const path = require('path'); // List of files with unused imports to fix const filesToFix = [ 'frontend/src/components/Dashboard/ClientProfilePanel.tsx', 'frontend/src/components/Dashboard/ClientRequestsTable.tsx', 'frontend/src/components/Dashboard/LiveChatWidget.tsx', 'frontend/src/components/Dashboard/ML/AlertsPanel.tsx', 'frontend/src/components/Dashboard/ML/ClassificationHistory.tsx', 'frontend/src/components/Dashboard/ML/MLDashboard.tsx', 'frontend/src/components/Dashboard/ML/MLOverview.tsx', 'frontend/src/components/Dashboard/ML/MLSettings.tsx', 'frontend/src/components/Dashboard/ML/PerformanceMetrics.tsx', 'frontend/src/components/Dashboard/ML/PriorityQueuePanel.tsx', 'frontend/src/components/Multimodal/ImageUploader.tsx', 'frontend/src/components/Multimodal/ProcessingDisplay.tsx', 'frontend/src/components/Multimodal/VoiceRecorder.tsx', 'frontend/src/components/Predictive/AnomalyDetectionPanel.tsx', 'frontend/src/components/Predictive/ChurnPredictionPanel.tsx', 'frontend/src/components/Predictive/DemandForecastPanel.tsx', 'frontend/src/components/Simulation/AIAssistantPanel.tsx', 'frontend/src/components/Simulation/PerformancePanel.tsx', 'frontend/src/components/Simulation/ScenarioSelector.tsx', 'frontend/src/components/Simulation/SimulationInterface.tsx' ]; // Common unused imports to remove const unusedImports = [ 'CalendarIcon', 'FilterIcon', 'NotificationsActive', 'MuiBox', 'Refresh', 'Grid', 'TrendingDown', 'CheckCircle', 'Paper', 'Divider', 'List', 'ListItem', 'ListItemText', 'ListItemSecondaryAction', 'IconButton', 'Edit', 'Storage', 'Security', 'InfoIcon', 'useState', 'Tooltip', 'TrendingUpIcon', 'TrendingDownIcon', 'TrendingFlatIcon', 'ConfidenceIcon', 'MicOffIcon', 'WaveformIcon', 'Button', 'CustomerIcon', 'ForecastIcon', 'PersonIcon', 'CategoryIcon', 'PauseIcon', 'MetricsIcon' ]; function fixUnusedImports(filePath) { try { const fullPath = path.join(__dirname, '..', filePath); if (!fs.existsSync(fullPath)) { console.log(`File not found: ${filePath}`); return; } let content = fs.readFileSync(fullPath, 'utf8'); let modified = false; // Remove unused imports from import statements unusedImports.forEach(importName => { // Remove from destructured imports const regex1 = new RegExp(`\\s*,?\\s*${importName}\\s*,?`, 'g'); const regex2 = new RegExp(`${importName}\\s*as\\s*\\w+\\s*,?`, 'g'); if (content.includes(importName) && !content.includes(`${importName}(`)) { content = content.replace(regex1, ''); content = content.replace(regex2, ''); modified = true; } }); // Clean up empty import lines content = content.replace(/import\s*{\s*}\s*from\s*['"][^'"]*['"];?\s*\n/g, ''); content = content.replace(/,\s*}\s*from/g, ' }'); content = content.replace(/{\s*,/g, '{'); if (modified) { fs.writeFileSync(fullPath, content); console.log(`[COMPLETE] Fixed unused imports in: ${filePath}`); } } catch (error) { console.error(`[FAILED] Error fixing ${filePath}:`, error.message); } } console.log('[CONFIG] Fixing ESLint warnings - unused imports...'); filesToFix.forEach(fixUnusedImports); console.log('[COMPLETE] ESLint warning fixes completed!');