#!/usr/bin/env node /** * Support Form Page Test Runner * Comprehensive testing script for modernized Support Form features */ const { execSync, spawn } = require('child_process'); const fs = require('fs'); const path = require('path'); class SupportFormTestRunner { constructor() { this.testResults = { startTime: new Date(), endTime: null, totalTests: 0, passedTests: 0, failedTests: 0, skippedTests: 0, testSuites: [], errors: [], coverage: null }; this.config = { baseUrl: process.env.BASE_URL || 'http://localhost:3001', timeout: 60000, retries: 2, browsers: ['chromium', 'firefox', 'webkit'], devices: ['desktop', 'mobile', 'tablet'] }; } async runTests() { console.log('[DEPLOY] Starting Support Form Page Test Suite'); console.log('=' .repeat(60)); try { // Step 1: Verify environment await this.verifyEnvironment(); // Step 2: Start services if needed await this.ensureServicesRunning(); // Step 3: Run Playwright tests await this.runPlaywrightTests(); // Step 4: Generate test report await this.generateTestReport(); // Step 5: Validate results this.validateResults(); } catch (error) { console.error('[FAILED] Test execution failed:', error.message); this.testResults.errors.push(error.message); process.exit(1); } } async verifyEnvironment() { console.log('[SEARCH] Verifying test environment...'); // Check if Playwright is installed try { execSync('npx playwright --version', { stdio: 'pipe' }); console.log('[COMPLETE] Playwright is installed'); } catch (error) { throw new Error('Playwright is not installed. Run: npm install @playwright/test'); } // Verify test files exist const testFile = path.join(__dirname, '../tests/e2e/support-form-modernized.spec.ts'); if (!fs.existsSync(testFile)) { throw new Error('Support Form test file not found'); } console.log('[COMPLETE] Test files verified'); } async ensureServicesRunning() { console.log('[CONFIG] Checking required services...'); try { // Check if frontend is running const response = await fetch(this.config.baseUrl); if (response.ok) { console.log('[COMPLETE] Frontend service is running'); } else { throw new Error('Frontend service not responding'); } } catch (error) { console.log(' Frontend service not detected. Please ensure it\'s running on port 3001'); throw new Error('Frontend service required for testing'); } } async runPlaywrightTests() { console.log(' Running Playwright tests for Support Form page...'); return new Promise((resolve, reject) => { const configPath = path.join(__dirname, '../playwright.support-form.config.ts'); const command = 'npx'; const args = [ 'playwright', 'test', '--config', configPath, '--reporter=json' ]; console.log(`Executing: ${command} ${args.join(' ')}`); const testProcess = spawn(command, args, { stdio: ['inherit', 'pipe', 'pipe'], cwd: path.join(__dirname, '..') }); let stdout = ''; let stderr = ''; testProcess.stdout.on('data', (data) => { stdout += data.toString(); process.stdout.write(data); }); testProcess.stderr.on('data', (data) => { stderr += data.toString(); process.stderr.write(data); }); testProcess.on('close', (code) => { this.testResults.endTime = new Date(); // Parse test results try { this.parseTestResults(stdout, stderr); } catch (parseError) { console.log(' Could not parse test results, but tests completed'); } if (code === 0) { console.log('[COMPLETE] All tests completed successfully'); resolve(); } else { console.log(` Tests completed with exit code: ${code}`); resolve(); // Don't reject, let validation handle the results } }); testProcess.on('error', (error) => { console.error('[FAILED] Failed to start test process:', error); reject(error); }); }); } parseTestResults(stdout, stderr) { // Try to find JSON results const jsonMatch = stdout.match(/\{[\s\S]*"stats"[\s\S]*\}/); if (jsonMatch) { try { const results = JSON.parse(jsonMatch[0]); this.testResults.totalTests = results.stats?.total || 0; this.testResults.passedTests = results.stats?.passed || 0; this.testResults.failedTests = results.stats?.failed || 0; this.testResults.skippedTests = results.stats?.skipped || 0; } catch (error) { console.log('Could not parse JSON results'); } } // Parse text output for basic stats const passedMatch = stdout.match(/(\d+) passed/); const failedMatch = stdout.match(/(\d+) failed/); const skippedMatch = stdout.match(/(\d+) skipped/); if (passedMatch) this.testResults.passedTests = parseInt(passedMatch[1]); if (failedMatch) this.testResults.failedTests = parseInt(failedMatch[1]); if (skippedMatch) this.testResults.skippedTests = parseInt(skippedMatch[1]); this.testResults.totalTests = this.testResults.passedTests + this.testResults.failedTests + this.testResults.skippedTests; } async generateTestReport() { console.log('[ANALYTICS] Generating test report...'); const duration = this.testResults.endTime - this.testResults.startTime; const successRate = this.testResults.totalTests > 0 ? (this.testResults.passedTests / this.testResults.totalTests * 100).toFixed(2) : 0; const report = { title: 'Support Form Page - Modernization Test Report', timestamp: new Date().toISOString(), duration: `${Math.round(duration / 1000)}s`, summary: { totalTests: this.testResults.totalTests, passed: this.testResults.passedTests, failed: this.testResults.failedTests, skipped: this.testResults.skippedTests, successRate: `${successRate}%` }, testSuites: [ 'Step-Based Form Navigation', 'Step 1: Personal Information', 'Step 2: Problem Details', 'Step 3: File Attachments', 'Step 4: Confirmation', 'Responsive Design', 'Interactive Elements', 'Professional UI/UX' ], features: [ 'Multi-step form with stepper navigation', 'Real-time form validation with error feedback', 'Enhanced file upload with progress tracking', 'Professional Material-UI design', 'Responsive design across all devices', 'Dynamic priority and category selection', 'Form confirmation and summary display', 'Snackbar notifications for user feedback', 'Free Mobile branding and color scheme' ], environment: { baseUrl: this.config.baseUrl, browsers: this.config.browsers, devices: this.config.devices } }; // Save report to file const reportPath = path.join(__dirname, '../test-results/support-form-test-report.json'); fs.mkdirSync(path.dirname(reportPath), { recursive: true }); fs.writeFileSync(reportPath, JSON.stringify(report, null, 2)); console.log('[COMPLETE] Test report generated:', reportPath); // Display summary console.log('\n Test Summary:'); console.log('=' .repeat(40)); console.log(`Total Tests: ${report.summary.totalTests}`); console.log(`Passed: ${report.summary.passed}`); console.log(`Failed: ${report.summary.failed}`); console.log(`Skipped: ${report.summary.skipped}`); console.log(`Success Rate: ${report.summary.successRate}`); console.log(`Duration: ${report.duration}`); } validateResults() { console.log('\n[SEARCH] Validating test results...'); if (this.testResults.totalTests === 0) { throw new Error('No tests were executed'); } if (this.testResults.failedTests > 0) { console.log(` ${this.testResults.failedTests} tests failed`); console.log('Please review the test results and fix any issues before proceeding'); return false; } if (this.testResults.passedTests === 0) { throw new Error('No tests passed - this indicates a serious issue'); } const successRate = (this.testResults.passedTests / this.testResults.totalTests) * 100; if (successRate < 80) { console.log(` Success rate (${successRate.toFixed(2)}%) is below 80%`); return false; } console.log('[COMPLETE] All validation checks passed!'); console.log(' Support Form page modernization is ready for deployment'); return true; } } // Run tests if called directly if (require.main === module) { const runner = new SupportFormTestRunner(); runner.runTests().catch(error => { console.error('Test execution failed:', error); process.exit(1); }); } module.exports = SupportFormTestRunner;