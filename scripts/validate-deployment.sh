#!/bin/bash

# =============================================
# ✅ DEPLOYMENT VALIDATION SCRIPT
# Comprehensive validation for Free Mobile Chatbot
# ML Intelligence Dashboard Enhanced Features
# =============================================

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENVIRONMENT="${1:-production}"
VALIDATION_TIMEOUT="${VALIDATION_TIMEOUT:-300}"

# Logging
LOG_FILE="$PROJECT_ROOT/logs/validation-$(date +%Y%m%d-%H%M%S).log"
mkdir -p "$(dirname "$LOG_FILE")"

log() {
    echo -e "${2:-$NC}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a "$LOG_FILE"
}

log_info() {
    log "$1" "$BLUE"
}

log_success() {
    log "$1" "$GREEN"
}

log_warning() {
    log "$1" "$YELLOW"
}

log_error() {
    log "$1" "$RED"
}

# Validation counters
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0
WARNING_CHECKS=0

# Test result tracking
increment_total() {
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
}

increment_passed() {
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
    increment_total
}

increment_failed() {
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
    increment_total
}

increment_warning() {
    WARNING_CHECKS=$((WARNING_CHECKS + 1))
    increment_total
}

# Service availability checks
check_service_health() {
    local service_name="$1"
    local port="$2"
    local endpoint="${3:-/health}"
    local expected_status="${4:-200}"
    
    log_info "🏥 Checking $service_name health..."
    
    local url="http://localhost:$port$endpoint"
    local response_code
    
    if response_code=$(curl -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null); then
        if [ "$response_code" = "$expected_status" ]; then
            log_success "✅ $service_name is healthy (HTTP $response_code)"
            increment_passed
        else
            log_error "❌ $service_name returned HTTP $response_code (expected $expected_status)"
            increment_failed
        fi
    else
        log_error "❌ $service_name is not responding"
        increment_failed
    fi
}

# Database connectivity checks
check_database_connectivity() {
    log_info "🗄️ Checking database connectivity..."
    
    # MongoDB
    if docker-compose -f docker-compose.enhanced.yml exec -T mongodb mongosh --eval "db.adminCommand('ping')" >/dev/null 2>&1; then
        log_success "✅ MongoDB is accessible"
        increment_passed
    else
        log_error "❌ MongoDB is not accessible"
        increment_failed
    fi
    
    # Redis
    if docker-compose -f docker-compose.enhanced.yml exec -T redis redis-cli ping >/dev/null 2>&1; then
        log_success "✅ Redis is accessible"
        increment_passed
    else
        log_error "❌ Redis is not accessible"
        increment_failed
    fi
    
    # TimescaleDB
    if docker-compose -f docker-compose.enhanced.yml exec -T timescaledb pg_isready -U postgres >/dev/null 2>&1; then
        log_success "✅ TimescaleDB is accessible"
        increment_passed
    else
        log_error "❌ TimescaleDB is not accessible"
        increment_failed
    fi
}

# API endpoint validation
validate_api_endpoints() {
    log_info "🔌 Validating API endpoints..."
    
    local endpoints=(
        "GET:5000:/health:200"
        "GET:5000:/api/auth/status:200"
        "GET:5001:/health:200"
        "GET:5001:/api/models/status:200"
        "POST:5000:/api/simulation/scenarios:401"  # Should require auth
        "POST:5000:/api/predictive/churn:401"      # Should require auth
        "POST:5000:/api/enhanced-ai/suggestions/contextual:401"  # Should require auth
        "GET:5000:/api/analytics/dashboard:401"   # Should require auth
    )
    
    for endpoint_spec in "${endpoints[@]}"; do
        IFS=':' read -r method port path expected_status <<< "$endpoint_spec"
        
        local url="http://localhost:$port$path"
        local response_code
        
        if [ "$method" = "GET" ]; then
            response_code=$(curl -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null || echo "000")
        else
            response_code=$(curl -s -o /dev/null -w "%{http_code}" -X "$method" -H "Content-Type: application/json" "$url" 2>/dev/null || echo "000")
        fi
        
        if [ "$response_code" = "$expected_status" ]; then
            log_success "✅ $method $path returned HTTP $response_code"
            increment_passed
        else
            log_error "❌ $method $path returned HTTP $response_code (expected $expected_status)"
            increment_failed
        fi
    done
}

# WebSocket connectivity test
test_websocket_connections() {
    log_info "🔌 Testing WebSocket connections..."
    
    local websocket_test_script="$PROJECT_ROOT/scripts/test-websocket.js"
    
    if [ -f "$websocket_test_script" ]; then
        if node "$websocket_test_script" >/dev/null 2>&1; then
            log_success "✅ WebSocket connections are working"
            increment_passed
        else
            log_error "❌ WebSocket connection test failed"
            increment_failed
        fi
    else
        log_warning "⚠️ WebSocket test script not found, skipping"
        increment_warning
    fi
}

# ML model validation
validate_ml_models() {
    log_info "🧠 Validating ML models..."
    
    # Test churn prediction model
    local churn_test_payload='{"customer_data": {"tier": "gold", "usage_pattern": "high", "support_contacts": 2}}'
    local churn_response
    
    if churn_response=$(curl -s -X POST "http://localhost:5001/api/predict/churn" \
        -H "Content-Type: application/json" \
        -d "$churn_test_payload" 2>/dev/null); then
        
        if echo "$churn_response" | grep -q "churn_probability"; then
            log_success "✅ Churn prediction model is working"
            increment_passed
        else
            log_error "❌ Churn prediction model returned invalid response"
            increment_failed
        fi
    else
        log_error "❌ Churn prediction model is not responding"
        increment_failed
    fi
    
    # Test sentiment analysis model
    local sentiment_test_payload='{"text": "I am very satisfied with the service"}'
    local sentiment_response
    
    if sentiment_response=$(curl -s -X POST "http://localhost:5001/api/analyze/sentiment" \
        -H "Content-Type: application/json" \
        -d "$sentiment_test_payload" 2>/dev/null); then
        
        if echo "$sentiment_response" | grep -q "sentiment_score"; then
            log_success "✅ Sentiment analysis model is working"
            increment_passed
        else
            log_error "❌ Sentiment analysis model returned invalid response"
            increment_failed
        fi
    else
        log_error "❌ Sentiment analysis model is not responding"
        increment_failed
    fi
}

# Performance validation
validate_performance() {
    log_info "⚡ Validating performance metrics..."
    
    # API response time test
    local api_response_time
    api_response_time=$(curl -o /dev/null -s -w '%{time_total}' "http://localhost:5000/health" 2>/dev/null || echo "999")
    
    if (( $(echo "$api_response_time < 2.0" | bc -l 2>/dev/null || echo 0) )); then
        log_success "✅ API response time: ${api_response_time}s (target: <2s)"
        increment_passed
    else
        log_warning "⚠️ API response time is high: ${api_response_time}s (target: <2s)"
        increment_warning
    fi
    
    # ML service response time test
    local ml_response_time
    ml_response_time=$(curl -o /dev/null -s -w '%{time_total}' "http://localhost:5001/health" 2>/dev/null || echo "999")
    
    if (( $(echo "$ml_response_time < 3.0" | bc -l 2>/dev/null || echo 0) )); then
        log_success "✅ ML service response time: ${ml_response_time}s (target: <3s)"
        increment_passed
    else
        log_warning "⚠️ ML service response time is high: ${ml_response_time}s (target: <3s)"
        increment_warning
    fi
    
    # Memory usage check
    local memory_usage
    memory_usage=$(docker stats --no-stream --format "table {{.Container}}\t{{.MemUsage}}" | grep -E "(backend|ml-service|frontend)" | awk '{print $2}' | head -1 | cut -d'/' -f1 | sed 's/MiB//')
    
    if [ -n "$memory_usage" ] && [ "$memory_usage" -lt 1000 ]; then
        log_success "✅ Memory usage is within acceptable limits"
        increment_passed
    else
        log_warning "⚠️ High memory usage detected"
        increment_warning
    fi
}

# Security validation
validate_security() {
    log_info "🔒 Validating security measures..."
    
    # Check for exposed sensitive endpoints without authentication
    local sensitive_endpoints=(
        "/api/admin"
        "/api/users"
        "/api/config"
        "/metrics"
    )
    
    for endpoint in "${sensitive_endpoints[@]}"; do
        local response_code
        response_code=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:5000$endpoint" 2>/dev/null || echo "000")
        
        if [ "$response_code" = "401" ] || [ "$response_code" = "403" ] || [ "$response_code" = "404" ]; then
            log_success "✅ $endpoint is properly protected (HTTP $response_code)"
            increment_passed
        else
            log_error "❌ $endpoint may be exposed (HTTP $response_code)"
            increment_failed
        fi
    done
    
    # Check HTTPS redirect (if applicable)
    if [ "$ENVIRONMENT" = "production" ]; then
        local https_response
        https_response=$(curl -s -o /dev/null -w "%{http_code}" "https://localhost" 2>/dev/null || echo "000")
        
        if [ "$https_response" = "200" ] || [ "$https_response" = "301" ] || [ "$https_response" = "302" ]; then
            log_success "✅ HTTPS is configured"
            increment_passed
        else
            log_warning "⚠️ HTTPS configuration may need attention"
            increment_warning
        fi
    fi
}

# Feature-specific validation
validate_enhanced_features() {
    log_info "🎮 Validating enhanced features..."
    
    # Simulation feature validation
    log_info "Testing simulation scenarios endpoint..."
    local scenarios_response
    scenarios_response=$(curl -s "http://localhost:5000/api/simulation/scenarios" 2>/dev/null || echo "")
    
    if echo "$scenarios_response" | grep -q "Unauthorized\|Authentication"; then
        log_success "✅ Simulation scenarios endpoint requires authentication"
        increment_passed
    else
        log_warning "⚠️ Simulation scenarios endpoint authentication needs verification"
        increment_warning
    fi
    
    # Predictive analytics validation
    log_info "Testing predictive analytics endpoint..."
    local predictive_response
    predictive_response=$(curl -s "http://localhost:5000/api/predictive/churn" 2>/dev/null || echo "")
    
    if echo "$predictive_response" | grep -q "Unauthorized\|Authentication"; then
        log_success "✅ Predictive analytics endpoint requires authentication"
        increment_passed
    else
        log_warning "⚠️ Predictive analytics endpoint authentication needs verification"
        increment_warning
    fi
    
    # Enhanced AI validation
    log_info "Testing enhanced AI endpoint..."
    local ai_response
    ai_response=$(curl -s -X POST "http://localhost:5000/api/enhanced-ai/suggestions/contextual" \
        -H "Content-Type: application/json" \
        -d '{"test": true}' 2>/dev/null || echo "")
    
    if echo "$ai_response" | grep -q "Unauthorized\|Authentication"; then
        log_success "✅ Enhanced AI endpoint requires authentication"
        increment_passed
    else
        log_warning "⚠️ Enhanced AI endpoint authentication needs verification"
        increment_warning
    fi
}

# Monitoring validation
validate_monitoring() {
    log_info "📊 Validating monitoring setup..."
    
    # Check Prometheus metrics
    if curl -s "http://localhost:9090/metrics" >/dev/null 2>&1; then
        log_success "✅ Prometheus is collecting metrics"
        increment_passed
    else
        log_warning "⚠️ Prometheus metrics endpoint not accessible"
        increment_warning
    fi
    
    # Check Grafana dashboard
    if curl -s "http://localhost:3000" >/dev/null 2>&1; then
        log_success "✅ Grafana dashboard is accessible"
        increment_passed
    else
        log_warning "⚠️ Grafana dashboard not accessible"
        increment_warning
    fi
}

# Generate validation report
generate_validation_report() {
    log_info "📋 Generating validation report..."
    
    local report_file="$PROJECT_ROOT/logs/validation-report-$(date +%Y%m%d-%H%M%S).json"
    local success_rate
    
    if [ $TOTAL_CHECKS -gt 0 ]; then
        success_rate=$(echo "scale=2; $PASSED_CHECKS * 100 / $TOTAL_CHECKS" | bc -l 2>/dev/null || echo "0")
    else
        success_rate="0"
    fi
    
    cat > "$report_file" << EOF
{
  "validation": {
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "environment": "$ENVIRONMENT",
    "version": "$(git rev-parse HEAD 2>/dev/null || echo 'unknown')",
    "summary": {
      "total_checks": $TOTAL_CHECKS,
      "passed_checks": $PASSED_CHECKS,
      "failed_checks": $FAILED_CHECKS,
      "warning_checks": $WARNING_CHECKS,
      "success_rate": "$success_rate%"
    },
    "status": "$([ $FAILED_CHECKS -eq 0 ] && echo 'PASSED' || echo 'FAILED')",
    "recommendations": [
      $([ $WARNING_CHECKS -gt 0 ] && echo '"Review warning items for optimization opportunities",' || echo '')
      $([ $FAILED_CHECKS -gt 0 ] && echo '"Address failed checks before production deployment",' || echo '')
      "Monitor system performance continuously",
      "Review security configurations regularly"
    ]
  }
}
EOF
    
    log_success "✅ Validation report generated: $report_file"
}

# Main validation function
main() {
    log_info "🚀 Starting Free Mobile Chatbot ML Intelligence Dashboard validation"
    log_info "Environment: $ENVIRONMENT"
    log_info "Timestamp: $(date)"
    
    # Core service health checks
    check_service_health "Backend API" "5000"
    check_service_health "ML Service" "5001"
    check_service_health "Frontend" "3001" "/" "200"
    
    # Database connectivity
    check_database_connectivity
    
    # API endpoint validation
    validate_api_endpoints
    
    # WebSocket connectivity
    test_websocket_connections
    
    # ML model validation
    validate_ml_models
    
    # Performance validation
    validate_performance
    
    # Security validation
    validate_security
    
    # Enhanced features validation
    validate_enhanced_features
    
    # Monitoring validation
    validate_monitoring
    
    # Generate report
    generate_validation_report
    
    # Final summary
    log_info "📊 VALIDATION SUMMARY"
    log_info "Total Checks: $TOTAL_CHECKS"
    log_success "Passed: $PASSED_CHECKS"
    log_warning "Warnings: $WARNING_CHECKS"
    log_error "Failed: $FAILED_CHECKS"
    
    local success_rate
    if [ $TOTAL_CHECKS -gt 0 ]; then
        success_rate=$(echo "scale=1; $PASSED_CHECKS * 100 / $TOTAL_CHECKS" | bc -l 2>/dev/null || echo "0")
    else
        success_rate="0"
    fi
    
    log_info "Success Rate: ${success_rate}%"
    
    if [ $FAILED_CHECKS -eq 0 ]; then
        log_success "🎉 All critical validations passed! System is ready for $ENVIRONMENT."
        exit 0
    else
        log_error "❌ $FAILED_CHECKS critical validations failed. Please address issues before proceeding."
        exit 1
    fi
}

# Script usage
usage() {
    echo "Usage: $0 [environment]"
    echo "  environment: production, staging, development (default: production)"
    echo ""
    echo "Environment variables:"
    echo "  VALIDATION_TIMEOUT: Validation timeout in seconds (default: 300)"
    echo ""
    echo "Example:"
    echo "  $0 production"
    echo "  VALIDATION_TIMEOUT=600 $0 staging"
}

# Handle script arguments
case "${1:-}" in
    -h|--help)
        usage
        exit 0
        ;;
    production|staging|development)
        main
        ;;
    "")
        main
        ;;
    *)
        echo "Invalid environment: $1"
        usage
        exit 1
        ;;
esac
