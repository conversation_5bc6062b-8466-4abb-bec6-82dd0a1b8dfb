#!/usr/bin/env node /** * Free Mobile Chatbot - Health Check Script * Comprehensive health monitoring for production deployment */ const http = require('http'); const https = require('https'); const { exec } = require('child_process'); const fs = require('fs'); const path = require('path'); // Configuration const config = { services: { frontend: { name: 'Frontend', url: process.env.FRONTEND_URL || 'http://localhost:3001', healthPath: '/health', timeout: 5000, critical: true }, backend: { name: 'Backend API', url: process.env.BACKEND_URL || 'http://localhost:5000', healthPath: '/health', timeout: 5000, critical: true }, mongodb: { name: 'MongoDB', container: 'freemobile-mongodb-prod', critical: true }, redis: { name: 'Redis', container: 'freemobile-redis-prod', critical: true }, rasa: { name: '<PERSON>sa NLU', url: process.env.RASA_URL || 'http://localhost:5005', healthPath: '/status', timeout: 10000, critical: false } }, thresholds: { responseTime: 2000, // ms memoryUsage: 80, // percentage diskUsage: 85, // percentage cpuUsage: 80 // percentage } }; // Colors for console output const colors = { reset: '\x1b[0m', red: '\x1b[31m', green: '\x1b[32m', yellow: '\x1b[33m', blue: '\x1b[34m', magenta: '\x1b[35m', cyan: '\x1b[36m' }; // Utility functions const log = { info: (msg) => console.log(`${colors.blue}ℹ ${msg}${colors.reset}`), success: (msg) => console.log(`${colors.green}[COMPLETE] ${msg}${colors.reset}`), warning: (msg) => console.log(`${colors.yellow} ${msg}${colors.reset}`), error: (msg) => console.log(`${colors.red}[FAILED] ${msg}${colors.reset}`), header: (msg) => console.log(`${colors.cyan} ${msg}${colors.reset}`) }; // HTTP health check function checkHttpHealth(service) { return new Promise((resolve) => { const url = new URL(service.url + (service.healthPath || '/')); const client = url.protocol === 'https:' ? https : http; const startTime = Date.now(); const req = client.get(url, { timeout: service.timeout }, (res) => { const responseTime = Date.now() - startTime; const isHealthy = res.statusCode >= 200 && res.statusCode < 300; resolve({ name: service.name, healthy: isHealthy, responseTime, statusCode: res.statusCode, critical: service.critical }); }); req.on('error', (error) => { resolve({ name: service.name, healthy: false, error: error.message, critical: service.critical }); }); req.on('timeout', () => { req.destroy(); resolve({ name: service.name, healthy: false, error: 'Timeout', critical: service.critical }); }); }); } // Docker container health check function checkContainerHealth(service) { return new Promise((resolve) => { exec(`docker inspect --format='{{.State.Health.Status}}' ${service.container}`, (error, stdout) => { if (error) { resolve({ name: service.name, healthy: false, error: 'Container not found or not running', critical: service.critical }); return; } const status = stdout.trim(); const isHealthy = status === 'healthy'; resolve({ name: service.name, healthy: isHealthy, status, critical: service.critical }); }); }); } // System resource check function checkSystemResources() { return new Promise((resolve) => { // Check memory usage exec('free -m', (error, stdout) => { if (error) { resolve({ error: 'Unable to check system resources' }); return; } const lines = stdout.split('\n'); const memLine = lines[1].split(/\s+/); const total = parseInt(memLine[1]); const used = parseInt(memLine[2]); const memoryUsage = Math.round((used / total) * 100); // Check disk usage exec('df -h /', (error, stdout) => { let diskUsage = 0; if (!error) { const diskLine = stdout.split('\n')[1].split(/\s+/); diskUsage = parseInt(diskLine[4].replace('%', '')); } // Check CPU usage exec('top -bn1 | grep "Cpu(s)"', (error, stdout) => { let cpuUsage = 0; if (!error) { const match = stdout.match(/(\d+\.\d+)%us/); if (match) { cpuUsage = parseFloat(match[1]); } } resolve({ memory: { usage: memoryUsage, total }, disk: { usage: diskUsage }, cpu: { usage: cpuUsage } }); }); }); }); }); } // Database connectivity check function checkDatabaseConnectivity() { return new Promise((resolve) => { // Check MongoDB exec('docker exec freemobile-mongodb-prod mongosh --eval "db.adminCommand(\'ping\')"', (error, stdout) => { const mongoHealthy = !error && stdout.includes('ok'); // Check Redis exec('docker exec freemobile-redis-prod redis-cli ping', (error, stdout) => { const redisHealthy = !error && stdout.includes('PONG'); resolve({ mongodb: mongoHealthy, redis: redisHealthy }); }); }); }); } // Application-specific checks function checkApplicationMetrics() { return new Promise((resolve) => { // Check if all required environment variables are set const requiredEnvVars = [ 'MONGODB_URI', 'REDIS_URL', 'JWT_SECRET' ]; const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]); // Check log files for errors const logPath = path.join(__dirname, '../logs'); let recentErrors = 0; if (fs.existsSync(logPath)) { try { const errorLogPath = path.join(logPath, 'error.log'); if (fs.existsSync(errorLogPath)) { const stats = fs.statSync(errorLogPath); const now = new Date(); const fileAge = (now - stats.mtime) / (1000 * 60); // minutes if (fileAge < 60) { // Check for errors in last hour const content = fs.readFileSync(errorLogPath, 'utf8'); recentErrors = (content.match(/ERROR/g) || []).length; } } } catch (error) { // Ignore log file errors } } resolve({ missingEnvVars, recentErrors }); }); } // Main health check function async function runHealthCheck() { log.header('Free Mobile Chatbot - Health Check Report'); console.log('='.repeat(60)); const results = { timestamp: new Date().toISOString(), overall: { healthy: true, critical_failures: 0 }, services: [], system: {}, database: {}, application: {} }; // Check HTTP services log.info('Checking HTTP services...'); for (const [key, service] of Object.entries(config.services)) { if (service.url) { const result = await checkHttpHealth(service); results.services.push(result); if (!result.healthy) { if (result.critical) { results.overall.critical_failures++; log.error(`${result.name}: ${result.error || 'Unhealthy'}`); } else { log.warning(`${result.name}: ${result.error || 'Unhealthy'}`); } } else { const responseMsg = result.responseTime ? ` (${result.responseTime}ms)` : ''; log.success(`${result.name}: Healthy${responseMsg}`); } } } // Check Docker containers log.info('Checking Docker containers...'); for (const [key, service] of Object.entries(config.services)) { if (service.container) { const result = await checkContainerHealth(service); results.services.push(result); if (!result.healthy) { if (result.critical) { results.overall.critical_failures++; log.error(`${result.name}: ${result.error || result.status}`); } else { log.warning(`${result.name}: ${result.error || result.status}`); } } else { log.success(`${result.name}: ${result.status || 'Healthy'}`); } } } // Check system resources log.info('Checking system resources...'); results.system = await checkSystemResources(); if (results.system.error) { log.warning(`System resources: ${results.system.error}`); } else { const { memory, disk, cpu } = results.system; // Memory check if (memory.usage > config.thresholds.memoryUsage) { log.warning(`Memory usage: ${memory.usage}% (threshold: ${config.thresholds.memoryUsage}%)`); } else { log.success(`Memory usage: ${memory.usage}%`); } // Disk check if (disk.usage > config.thresholds.diskUsage) { log.warning(`Disk usage: ${disk.usage}% (threshold: ${config.thresholds.diskUsage}%)`); } else { log.success(`Disk usage: ${disk.usage}%`); } // CPU check if (cpu.usage > config.thresholds.cpuUsage) { log.warning(`CPU usage: ${cpu.usage}% (threshold: ${config.thresholds.cpuUsage}%)`); } else { log.success(`CPU usage: ${cpu.usage}%`); } } // Check database connectivity log.info('Checking database connectivity...'); results.database = await checkDatabaseConnectivity(); if (results.database.mongodb) { log.success('MongoDB: Connected'); } else { log.error('MongoDB: Connection failed'); results.overall.critical_failures++; } if (results.database.redis) { log.success('Redis: Connected'); } else { log.error('Redis: Connection failed'); results.overall.critical_failures++; } // Check application metrics log.info('Checking application configuration...'); results.application = await checkApplicationMetrics(); if (results.application.missingEnvVars.length > 0) { log.warning(`Missing environment variables: ${results.application.missingEnvVars.join(', ')}`); } else { log.success('All required environment variables are set'); } if (results.application.recentErrors > 10) { log.warning(`Recent errors in logs: ${results.application.recentErrors}`); } else { log.success(`Recent errors: ${results.application.recentErrors}`); } // Overall status results.overall.healthy = results.overall.critical_failures === 0; console.log('='.repeat(60)); if (results.overall.healthy) { log.success('Overall Status: HEALTHY'); } else { log.error(`Overall Status: UNHEALTHY (${results.overall.critical_failures} critical failures)`); } // Save results to file const reportPath = path.join(__dirname, '../health-report.json'); fs.writeFileSync(reportPath, JSON.stringify(results, null, 2)); log.info(`Health report saved to: ${reportPath}`); // Exit with appropriate code process.exit(results.overall.healthy ? 0 : 1); } // Run health check if (require.main === module) { runHealthCheck().catch(error => { log.error(`Health check failed: ${error.message}`); process.exit(1); }); } module.exports = { runHealthCheck, checkHttpHealth, checkContainerHealth };