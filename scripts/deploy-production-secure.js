#!/usr/bin/env node /** * [DEPLOY] SECURE PRODUCTION DEPLOYMENT SCRIPT * Automated deployment to Vercel with security validation * * This script: * 1. Validates environment configuration * 2. Runs security checks * 3. Builds the application * 4. Deploys to Vercel * 5. Validates deployment * 6. Runs post-deployment tests */ const { execSync, spawn } = require('child_process'); const fs = require('fs'); const path = require('path'); const https = require('https'); // Colors for console output const colors = { red: '\x1b[31m', green: '\x1b[32m', yellow: '\x1b[33m', blue: '\x1b[34m', magenta: '\x1b[35m', cyan: '\x1b[36m', reset: '\x1b[0m', bright: '\x1b[1m' }; const log = { info: (msg) => console.log(`${colors.blue}ℹ ${msg}${colors.reset}`), success: (msg) => console.log(`${colors.green}[COMPLETE] ${msg}${colors.reset}`), warning: (msg) => console.log(`${colors.yellow} ${msg}${colors.reset}`), error: (msg) => console.log(`${colors.red}[FAILED] ${msg}${colors.reset}`), critical: (msg) => console.log(`${colors.red}${colors.bright} CRITICAL: ${msg}${colors.reset}`), header: (msg) => console.log(`${colors.cyan}${colors.bright}\n[DEPLOY] ${msg}${colors.reset}\n`) }; // Configuration const config = { projectRoot: path.join(__dirname, '..'), frontendDir: path.join(__dirname, '..', 'frontend'), backendDir: path.join(__dirname, '..', 'backend'), envFile: path.join(__dirname, '..', '.env.production'), vercelConfig: path.join(__dirname, '..', 'vercel.json'), deploymentUrl: 'https://chatbotrncp.vercel.app', healthCheckEndpoint: '/api/health', maxRetries: 3, retryDelay: 5000 }; // Utility functions const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms)); const execCommand = (command, options = {}) => { try { const result = execSync(command, { encoding: 'utf8', stdio: 'pipe', cwd: options.cwd || config.projectRoot, ...options }); return { success: true, output: result.trim() }; } catch (error) { return { success: false, error: error.message, output: error.stdout ? error.stdout.toString() : '', stderr: error.stderr ? error.stderr.toString() : '' }; } }; const makeHttpRequest = (url, options = {}) => { return new Promise((resolve, reject) => { const request = https.get(url, options, (response) => { let data = ''; response.on('data', chunk => data += chunk); response.on('end', () => { resolve({ statusCode: response.statusCode, headers: response.headers, body: data }); }); }); request.on('error', reject); request.setTimeout(10000, () => { request.destroy(); reject(new Error('Request timeout')); }); }); }; // Validation functions async function validateEnvironment() { log.header('STEP 1: ENVIRONMENT VALIDATION'); // Check if production env file exists if (!fs.existsSync(config.envFile)) { log.error('Production environment file not found!'); log.info('Run: node scripts/secure-production-setup.js'); return false; } log.success('Production environment file found'); // Load and validate environment variables const envContent = fs.readFileSync(config.envFile, 'utf8'); const requiredVars = [ 'NODE_ENV', 'MONGODB_URI', 'JWT_SECRET', 'OPENAI_API_KEY', 'REDIS_URL', 'FRONTEND_URL', 'VERCEL_PROJECT' ]; const missingVars = []; const insecureVars = []; requiredVars.forEach(varName => { const regex = new RegExp(`^${varName}=(.+)$`, 'm'); const match = envContent.match(regex); if (!match) { missingVars.push(varName); } else { const value = match[1].trim(); // Check for placeholder values if (value.includes('REPLACE_WITH') || value.includes('YOUR_') || value.length < 10) { insecureVars.push(varName); } } }); if (missingVars.length > 0) { log.error(`Missing required environment variables: ${missingVars.join(', ')}`); return false; } if (insecureVars.length > 0) { log.error(`Insecure/placeholder values detected: ${insecureVars.join(', ')}`); log.info('Run: node scripts/secure-production-setup.js'); return false; } log.success('All required environment variables are configured'); return true; } async function runSecurityChecks() { log.header('STEP 2: SECURITY CHECKS'); const checks = [ { name: 'Check for exposed secrets in code', command: 'grep -r "sk-" frontend/src/ || true', shouldBeEmpty: true }, { name: 'Verify .env files are in .gitignore', command: 'grep -q ".env" .gitignore', shouldSucceed: true }, { name: 'Check for TODO/FIXME in production code', command: 'grep -r "TODO\\|FIXME" frontend/src/ || true', shouldBeEmpty: false, warning: true } ]; let allChecksPassed = true; for (const check of checks) { const result = execCommand(check.command); if (check.shouldBeEmpty) { if (result.output.trim() === '') { log.success(`${check.name}: PASSED`); } else { log.error(`${check.name}: FAILED - Found: ${result.output.trim()}`); if (!check.warning) allChecksPassed = false; } } else if (check.shouldSucceed) { if (result.success) { log.success(`${check.name}: PASSED`); } else { log.error(`${check.name}: FAILED`); if (!check.warning) allChecksPassed = false; } } } return allChecksPassed; } async function buildApplication() { log.header('STEP 3: APPLICATION BUILD'); // Install dependencies log.info('Installing frontend dependencies...'); const installResult = execCommand('npm ci', { cwd: config.frontendDir }); if (!installResult.success) { log.error(`Dependency installation failed: ${installResult.error}`); return false; } log.success('Dependencies installed'); // Build frontend log.info('Building frontend application...'); const buildResult = execCommand('npm run build', { cwd: config.frontendDir }); if (!buildResult.success) { log.error(`Frontend build failed: ${buildResult.error}`); log.error(`Build output: ${buildResult.stderr}`); return false; } log.success('Frontend build completed'); // Verify build output const buildDir = path.join(config.frontendDir, 'build'); if (!fs.existsSync(buildDir)) { log.error('Build directory not found'); return false; } const buildFiles = fs.readdirSync(buildDir); if (!buildFiles.includes('index.html')) { log.error('Build output missing index.html'); return false; } log.success('Build output validated'); return true; } async function deployToVercel() { log.header('STEP 4: VERCEL DEPLOYMENT'); // Check if Vercel CLI is installed const vercelCheck = execCommand('vercel --version'); if (!vercelCheck.success) { log.error('Vercel CLI not found. Install with: npm install -g vercel'); return false; } log.success(`Vercel CLI found: ${vercelCheck.output}`); // Deploy to production log.info('Deploying to Vercel production...'); const deployResult = execCommand('vercel --prod --yes', { env: { ...process.env, VERCEL_TOKEN: process.env.VERCEL_TOKEN } }); if (!deployResult.success) { log.error(`Deployment failed: ${deployResult.error}`); log.error(`Deploy output: ${deployResult.stderr}`); return false; } log.success('Deployment completed'); log.info(`Deployment URL: ${config.deploymentUrl}`); return true; } async function validateDeployment() { log.header('STEP 5: DEPLOYMENT VALIDATION'); // Wait for deployment to be ready log.info('Waiting for deployment to be ready...'); await sleep(10000); // Health check try { const healthResponse = await makeHttpRequest(`${config.deploymentUrl}${config.healthCheckEndpoint}`); if (healthResponse.statusCode === 200) { log.success('Health check passed'); } else { log.warning(`Health check returned status: ${healthResponse.statusCode}`); } } catch (error) { log.warning(`Health check failed: ${error.message}`); } // Test main page try { const mainPageResponse = await makeHttpRequest(config.deploymentUrl); if (mainPageResponse.statusCode === 200) { log.success('Main page accessible'); } else { log.error(`Main page returned status: ${mainPageResponse.statusCode}`); return false; } } catch (error) { log.error(`Main page test failed: ${error.message}`); return false; } // Test API endpoints const apiEndpoints = ['/api/auth/health', '/api/dashboard/health']; for (const endpoint of apiEndpoints) { try { const response = await makeHttpRequest(`${config.deploymentUrl}${endpoint}`); if (response.statusCode === 200 || response.statusCode === 404) { log.success(`API endpoint ${endpoint}: accessible`); } else { log.warning(`API endpoint ${endpoint}: status ${response.statusCode}`); } } catch (error) { log.warning(`API endpoint ${endpoint}: ${error.message}`); } } return true; } async function runPostDeploymentTests() { log.header('STEP 6: POST-DEPLOYMENT TESTS'); // Run smoke tests log.info('Running smoke tests...'); const smokeTestResult = execCommand('npm run test:smoke', { cwd: config.frontendDir, env: { ...process.env, BASE_URL: config.deploymentUrl } }); if (smokeTestResult.success) { log.success('Smoke tests passed'); } else { log.warning('Smoke tests failed - this may be expected for initial deployment'); } return true; } // Main deployment function async function deployProduction() { log.header('CHATBOT RNCP - SECURE PRODUCTION DEPLOYMENT'); const startTime = Date.now(); try { // Step 1: Validate environment if (!(await validateEnvironment())) { throw new Error('Environment validation failed'); } // Step 2: Run security checks if (!(await runSecurityChecks())) { throw new Error('Security checks failed'); } // Step 3: Build application if (!(await buildApplication())) { throw new Error('Application build failed'); } // Step 4: Deploy to Vercel if (!(await deployToVercel())) { throw new Error('Vercel deployment failed'); } // Step 5: Validate deployment if (!(await validateDeployment())) { throw new Error('Deployment validation failed'); } // Step 6: Run post-deployment tests await runPostDeploymentTests(); // Success summary const duration = Math.round((Date.now() - startTime) / 1000); log.header('DEPLOYMENT SUCCESSFUL! '); log.success(`Deployment completed in ${duration} seconds`); log.success(`Application URL: ${config.deploymentUrl}`); log.info('Next steps:'); console.log(' 1. Monitor application logs'); console.log(' 2. Run full test suite'); console.log(' 3. Update DNS if using custom domain'); console.log(' 4. Configure monitoring alerts'); } catch (error) { log.error(`Deployment failed: ${error.message}`); log.info('Troubleshooting steps:'); console.log(' 1. Check environment configuration'); console.log(' 2. Verify Vercel token is set'); console.log(' 3. Review build logs'); console.log(' 4. Check network connectivity'); process.exit(1); } } // Error handling process.on('uncaughtException', (error) => { log.error(`Uncaught Exception: ${error.message}`); process.exit(1); }); process.on('unhandledRejection', (reason, promise) => { log.error(`Unhandled Rejection: ${reason}`); process.exit(1); }); // Run deployment if called directly if (require.main === module) { deployProduction().catch(error => { log.error(`Deployment script failed: ${error.message}`); process.exit(1); }); } module.exports = { deployProduction };