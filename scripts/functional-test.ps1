#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Comprehensive Functional Testing Script for ChatbotRNCP
.DESCRIPTION
    Tests core functionality including authentication, navigation, chat interface, 
    support ticket system, and dashboard data display
.EXAMPLE
    .\functional-test.ps1
#>

param(
    [string]$FrontendUrl = "http://localhost:51692",
    [string]$BackendUrl = "http://localhost:5000",
    [switch]$Verbose = $false
)

# Colors for output
$Green = "`e[32m"
$Red = "`e[31m"
$Yellow = "`e[33m"
$Blue = "`e[34m"
$Reset = "`e[0m"

function Write-TestResult {
    param(
        [string]$TestName,
        [bool]$Success,
        [string]$Details = ""
    )
    
    $status = if ($Success) { "${Green}✅ PASS${Reset}" } else { "${Red}❌ FAIL${Reset}" }
    $message = "[$status] $TestName"
    if ($Details) {
        $message += " - $Details"
    }
    Write-Host $message
}

function Test-HttpEndpoint {
    param(
        [string]$Url,
        [string]$ExpectedContent = "",
        [int]$ExpectedStatusCode = 200
    )
    
    try {
        $response = Invoke-WebRequest -Uri $Url -UseBasicParsing -TimeoutSec 10
        $success = $response.StatusCode -eq $ExpectedStatusCode
        
        if ($ExpectedContent -and $success) {
            $success = $response.Content -like "*$ExpectedContent*"
        }
        
        return @{
            Success = $success
            StatusCode = $response.StatusCode
            Content = $response.Content
            Error = $null
        }
    }
    catch {
        return @{
            Success = $false
            StatusCode = 0
            Content = ""
            Error = $_.Exception.Message
        }
    }
}

function Test-JsonEndpoint {
    param(
        [string]$Url,
        [hashtable]$ExpectedFields = @{}
    )
    
    try {
        $response = Invoke-RestMethod -Uri $Url -Method Get -TimeoutSec 10
        $success = $true
        $details = @()
        
        foreach ($field in $ExpectedFields.Keys) {
            if ($response.PSObject.Properties.Name -contains $field) {
                $expectedValue = $ExpectedFields[$field]
                if ($expectedValue -and $response.$field -ne $expectedValue) {
                    $success = $false
                    $details += "Field '$field' expected '$expectedValue', got '$($response.$field)'"
                }
            } else {
                $success = $false
                $details += "Missing field '$field'"
            }
        }
        
        return @{
            Success = $success
            Data = $response
            Details = $details -join "; "
            Error = $null
        }
    }
    catch {
        return @{
            Success = $false
            Data = $null
            Details = ""
            Error = $_.Exception.Message
        }
    }
}

Write-Host "${Blue}🧪 ChatbotRNCP Functional Testing Suite${Reset}"
Write-Host "${Blue}======================================${Reset}"
Write-Host ""

$testResults = @()

# Test 1: Frontend Accessibility
Write-Host "${Yellow}📱 Testing Frontend Accessibility...${Reset}"
$frontendTest = Test-HttpEndpoint -Url $FrontendUrl
Write-TestResult -TestName "Frontend Server Accessibility" -Success $frontendTest.Success -Details "Status: $($frontendTest.StatusCode)"
$testResults += @{ Name = "Frontend Accessibility"; Success = $frontendTest.Success }

# Test 2: Backend Health Check
Write-Host "${Yellow}🔧 Testing Backend Health...${Reset}"
$healthTest = Test-JsonEndpoint -Url "$BackendUrl/health" -ExpectedFields @{ status = "ok" }
Write-TestResult -TestName "Backend Health Check" -Success $healthTest.Success -Details $healthTest.Details
$testResults += @{ Name = "Backend Health"; Success = $healthTest.Success }

# Test 3: API Status
Write-Host "${Yellow}🌐 Testing API Status...${Reset}"
$apiTest = Test-JsonEndpoint -Url "$BackendUrl/api/status"
Write-TestResult -TestName "API Status Endpoint" -Success $apiTest.Success -Details $apiTest.Details
$testResults += @{ Name = "API Status"; Success = $apiTest.Success }

# Test 4: Authentication Endpoints
Write-Host "${Yellow}🔐 Testing Authentication Endpoints...${Reset}"

# Test auth status endpoint (should return 401 for unauthenticated)
try {
    $authResponse = Invoke-WebRequest -Uri "$BackendUrl/api/auth/status" -UseBasicParsing -TimeoutSec 10
    $authSuccess = $authResponse.StatusCode -eq 401 -or $authResponse.StatusCode -eq 200
    $authDetails = "Status: $($authResponse.StatusCode)"
}
catch {
    $authSuccess = $_.Exception.Response.StatusCode -eq 401
    $authDetails = "Expected 401 Unauthorized: $($_.Exception.Response.StatusCode)"
}

Write-TestResult -TestName "Auth Status Endpoint" -Success $authSuccess -Details $authDetails
$testResults += @{ Name = "Auth Endpoints"; Success = $authSuccess }

# Test 5: Frontend Content Validation
Write-Host "${Yellow}📄 Testing Frontend Content...${Reset}"
$contentSuccess = $frontendTest.Success -and $frontendTest.Content -like "*React App*"
Write-TestResult -TestName "Frontend Content Validation" -Success $contentSuccess -Details "React App title found"
$testResults += @{ Name = "Frontend Content"; Success = $contentSuccess }

# Test 6: Database Connectivity (via backend)
Write-Host "${Yellow}🗄️ Testing Database Connectivity...${Reset}"
$dbTest = Test-HttpEndpoint -Url "$BackendUrl/api/status"
$dbSuccess = $dbTest.Success
Write-TestResult -TestName "Database Connectivity" -Success $dbSuccess -Details "Via API status check"
$testResults += @{ Name = "Database Connectivity"; Success = $dbSuccess }

# Test 7: CORS Configuration
Write-Host "${Yellow}🌐 Testing CORS Configuration...${Reset}"
try {
    $corsHeaders = @{
        'Origin' = $FrontendUrl
        'Access-Control-Request-Method' = 'GET'
    }
    $corsResponse = Invoke-WebRequest -Uri "$BackendUrl/api/status" -Headers $corsHeaders -UseBasicParsing -TimeoutSec 10
    $corsSuccess = $corsResponse.StatusCode -eq 200
    $corsDetails = "CORS headers accepted"
}
catch {
    $corsSuccess = $false
    $corsDetails = "CORS test failed: $($_.Exception.Message)"
}

Write-TestResult -TestName "CORS Configuration" -Success $corsSuccess -Details $corsDetails
$testResults += @{ Name = "CORS Configuration"; Success = $corsSuccess }

# Test 8: Service Dependencies
Write-Host "${Yellow}🐳 Testing Service Dependencies...${Reset}"

# Test MongoDB
try {
    $mongoTest = Test-NetConnection -ComputerName "localhost" -Port 27017 -WarningAction SilentlyContinue
    $mongoSuccess = $mongoTest.TcpTestSucceeded
}
catch {
    $mongoSuccess = $false
}

Write-TestResult -TestName "MongoDB Connection" -Success $mongoSuccess -Details "Port 27017"

# Test Redis
try {
    $redisTest = Test-NetConnection -ComputerName "localhost" -Port 6379 -WarningAction SilentlyContinue
    $redisSuccess = $redisTest.TcpTestSucceeded
}
catch {
    $redisSuccess = $false
}

Write-TestResult -TestName "Redis Connection" -Success $redisSuccess -Details "Port 6379"

$servicesSuccess = $mongoSuccess -and $redisSuccess
$testResults += @{ Name = "Service Dependencies"; Success = $servicesSuccess }

# Summary
Write-Host ""
Write-Host "${Blue}📊 Test Summary${Reset}"
Write-Host "${Blue}===============${Reset}"

$totalTests = $testResults.Count
$passedTests = ($testResults | Where-Object { $_.Success }).Count
$failedTests = $totalTests - $passedTests

Write-Host "Total Tests: $totalTests"
Write-Host "${Green}Passed: $passedTests${Reset}"
if ($failedTests -gt 0) {
    Write-Host "${Red}Failed: $failedTests${Reset}"
}

$successRate = [math]::Round(($passedTests / $totalTests) * 100, 1)
Write-Host "Success Rate: $successRate%"

Write-Host ""
if ($failedTests -eq 0) {
    Write-Host "${Green}🎉 All tests passed! The application is ready for use.${Reset}"
} else {
    Write-Host "${Yellow}⚠️ Some tests failed. Please review the results above.${Reset}"
}

Write-Host ""
Write-Host "${Blue}🌐 Application URLs:${Reset}"
Write-Host "Frontend: $FrontendUrl"
Write-Host "Backend:  $BackendUrl"
Write-Host "Health:   $($BackendUrl)/health"

return $failedTests -eq 0
