#!/bin/bash

# =============================================
# 🚀 FREE MOBILE CHATBOT ENHANCED DEPLOYMENT
# ML Intelligence Dashboard Deployment Script
# Production-ready with comprehensive health checks
# =============================================

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENVIRONMENT="${1:-production}"
BACKUP_BEFORE_DEPLOY="${BACKUP_BEFORE_DEPLOY:-true}"
HEALTH_CHECK_TIMEOUT="${HEALTH_CHECK_TIMEOUT:-300}"
ROLLBACK_ON_FAILURE="${ROLLBACK_ON_FAILURE:-true}"

# Logging
LOG_FILE="$PROJECT_ROOT/logs/deployment-$(date +%Y%m%d-%H%M%S).log"
mkdir -p "$(dirname "$LOG_FILE")"

log() {
    echo -e "${2:-$NC}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a "$LOG_FILE"
}

log_info() {
    log "$1" "$BLUE"
}

log_success() {
    log "$1" "$GREEN"
}

log_warning() {
    log "$1" "$YELLOW"
}

log_error() {
    log "$1" "$RED"
}

# Error handling
cleanup() {
    local exit_code=$?
    if [ $exit_code -ne 0 ]; then
        log_error "Deployment failed with exit code $exit_code"
        if [ "$ROLLBACK_ON_FAILURE" = "true" ]; then
            log_warning "Initiating rollback..."
            rollback_deployment
        fi
    fi
    exit $exit_code
}

trap cleanup EXIT

# Pre-deployment checks
check_prerequisites() {
    log_info "🔍 Checking prerequisites..."
    
    # Check if Docker is running
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker is not running or not accessible"
        exit 1
    fi
    
    # Check if Docker Compose is available
    if ! command -v docker-compose >/dev/null 2>&1; then
        log_error "Docker Compose is not installed"
        exit 1
    fi
    
    # Check if required environment files exist
    if [ ! -f "$PROJECT_ROOT/.env.$ENVIRONMENT" ]; then
        log_error "Environment file .env.$ENVIRONMENT not found"
        exit 1
    fi
    
    # Check disk space (minimum 10GB)
    available_space=$(df "$PROJECT_ROOT" | awk 'NR==2 {print $4}')
    if [ "$available_space" -lt 10485760 ]; then # 10GB in KB
        log_error "Insufficient disk space. At least 10GB required."
        exit 1
    fi
    
    # Check if ports are available
    local ports=(5000 5001 3001 27017 6379 5432)
    for port in "${ports[@]}"; do
        if netstat -tuln | grep -q ":$port "; then
            log_warning "Port $port is already in use"
        fi
    done
    
    log_success "✅ Prerequisites check passed"
}

# Backup current deployment
backup_deployment() {
    if [ "$BACKUP_BEFORE_DEPLOY" != "true" ]; then
        log_info "⏭️ Skipping backup (BACKUP_BEFORE_DEPLOY=false)"
        return
    fi
    
    log_info "💾 Creating backup before deployment..."
    
    local backup_dir="$PROJECT_ROOT/backups/pre-deployment-$(date +%Y%m%d-%H%M%S)"
    mkdir -p "$backup_dir"
    
    # Check if services are running
    if docker-compose -f docker-compose.enhanced.yml ps | grep -q "Up"; then
        # Backup databases
        log_info "Backing up MongoDB..."
        docker-compose -f docker-compose.enhanced.yml exec -T mongodb mongodump --out /tmp/backup 2>/dev/null || true
        docker cp "$(docker-compose -f docker-compose.enhanced.yml ps -q mongodb)":/tmp/backup "$backup_dir/mongodb" 2>/dev/null || true
        
        log_info "Backing up TimescaleDB..."
        docker-compose -f docker-compose.enhanced.yml exec -T timescaledb pg_dump -U postgres freemobile_timeseries > "$backup_dir/timescaledb.sql" 2>/dev/null || true
    fi
    
    # Backup application data
    log_info "Backing up application data..."
    cp -r "$PROJECT_ROOT/uploads" "$backup_dir/" 2>/dev/null || true
    cp -r "$PROJECT_ROOT/ml-models" "$backup_dir/" 2>/dev/null || true
    
    # Store backup location for potential rollback
    echo "$backup_dir" > "$PROJECT_ROOT/.last_backup"
    
    log_success "✅ Backup completed: $backup_dir"
}

# Build and deploy services
deploy_services() {
    log_info "🚀 Starting deployment for environment: $ENVIRONMENT"
    
    cd "$PROJECT_ROOT"
    
    # Load environment variables
    if [ -f ".env.$ENVIRONMENT" ]; then
        export $(cat ".env.$ENVIRONMENT" | grep -v '^#' | xargs)
    fi
    
    # Pull latest images
    log_info "📥 Pulling latest base images..."
    docker-compose -f docker-compose.enhanced.yml pull --ignore-pull-failures
    
    # Build services
    log_info "🔨 Building services..."
    docker-compose -f docker-compose.enhanced.yml build --no-cache --parallel
    
    # Stop existing services gracefully
    log_info "⏹️ Stopping existing services..."
    docker-compose -f docker-compose.enhanced.yml down --timeout 30
    
    # Start database services first
    log_info "🗄️ Starting database services..."
    docker-compose -f docker-compose.enhanced.yml up -d mongodb redis timescaledb
    
    # Wait for databases to be ready
    wait_for_service "mongodb" "27017" "MongoDB"
    wait_for_service "redis" "6379" "Redis"
    wait_for_service "timescaledb" "5432" "TimescaleDB"
    
    # Run database migrations
    run_migrations
    
    # Start ML service
    log_info "🧠 Starting ML service..."
    docker-compose -f docker-compose.enhanced.yml up -d ml-service
    wait_for_service "ml-service" "5001" "ML Service"
    
    # Start backend service
    log_info "⚙️ Starting backend service..."
    docker-compose -f docker-compose.enhanced.yml up -d backend
    wait_for_service "backend" "5000" "Backend API"
    
    # Start frontend and other services
    log_info "🌐 Starting remaining services..."
    docker-compose -f docker-compose.enhanced.yml up -d
    
    log_success "✅ All services started successfully"
}

# Wait for service to be ready
wait_for_service() {
    local service_name="$1"
    local port="$2"
    local display_name="$3"
    local timeout=60
    local count=0
    
    log_info "⏳ Waiting for $display_name to be ready..."
    
    while [ $count -lt $timeout ]; do
        if docker-compose -f docker-compose.enhanced.yml exec -T "$service_name" nc -z localhost "$port" 2>/dev/null; then
            log_success "✅ $display_name is ready"
            return 0
        fi
        sleep 2
        count=$((count + 2))
        printf "."
    done
    
    log_error "❌ $display_name failed to start within $timeout seconds"
    return 1
}

# Run database migrations
run_migrations() {
    log_info "🔄 Running database migrations..."
    
    # MongoDB migrations
    if [ -d "$PROJECT_ROOT/backend/migrations/mongodb" ]; then
        log_info "Running MongoDB migrations..."
        docker-compose -f docker-compose.enhanced.yml exec -T backend npm run migrate:mongodb || true
    fi
    
    # TimescaleDB migrations
    if [ -d "$PROJECT_ROOT/backend/migrations/timescaledb" ]; then
        log_info "Running TimescaleDB migrations..."
        docker-compose -f docker-compose.enhanced.yml exec -T backend npm run migrate:timescaledb || true
    fi
    
    log_success "✅ Database migrations completed"
}

# Health checks
perform_health_checks() {
    log_info "🏥 Performing comprehensive health checks..."
    
    local services=("backend:5000/health" "ml-service:5001/health" "frontend:80")
    local failed_checks=0
    
    for service_endpoint in "${services[@]}"; do
        local service=$(echo "$service_endpoint" | cut -d: -f1)
        local endpoint=$(echo "$service_endpoint" | cut -d: -f2-)
        
        log_info "Checking health of $service..."
        
        local count=0
        local max_attempts=30
        
        while [ $count -lt $max_attempts ]; do
            if curl -f -s "http://localhost:$endpoint" >/dev/null 2>&1; then
                log_success "✅ $service health check passed"
                break
            fi
            
            if [ $count -eq $((max_attempts - 1)) ]; then
                log_error "❌ $service health check failed"
                failed_checks=$((failed_checks + 1))
                break
            fi
            
            sleep 10
            count=$((count + 1))
        done
    done
    
    # Test ML model endpoints
    log_info "🧠 Testing ML model endpoints..."
    if curl -f -s -X POST "http://localhost:5001/health" >/dev/null 2>&1; then
        log_success "✅ ML model endpoints working"
    else
        log_error "❌ ML model endpoint test failed"
        failed_checks=$((failed_checks + 1))
    fi
    
    if [ $failed_checks -gt 0 ]; then
        log_error "❌ $failed_checks health checks failed"
        return 1
    fi
    
    log_success "✅ All health checks passed"
    return 0
}

# Performance tests
run_performance_tests() {
    log_info "⚡ Running performance tests..."
    
    # Test API response times
    local api_response_time=$(curl -o /dev/null -s -w '%{time_total}' "http://localhost:5000/health" 2>/dev/null || echo "failed")
    if [ "$api_response_time" != "failed" ] && (( $(echo "$api_response_time > 2.0" | bc -l 2>/dev/null || echo 0) )); then
        log_warning "⚠️ API response time is high: ${api_response_time}s (target: <2s)"
    else
        log_success "✅ API response time: ${api_response_time}s"
    fi
    
    # Test ML service response times
    local ml_response_time=$(curl -o /dev/null -s -w '%{time_total}' "http://localhost:5001/health" 2>/dev/null || echo "failed")
    if [ "$ml_response_time" != "failed" ] && (( $(echo "$ml_response_time > 3.0" | bc -l 2>/dev/null || echo 0) )); then
        log_warning "⚠️ ML service response time is high: ${ml_response_time}s (target: <3s)"
    else
        log_success "✅ ML service response time: ${ml_response_time}s"
    fi
    
    log_success "✅ Performance tests completed"
}

# Post-deployment tasks
post_deployment_tasks() {
    log_info "🔧 Running post-deployment tasks..."
    
    # Clear caches
    log_info "Clearing application caches..."
    docker-compose -f docker-compose.enhanced.yml exec -T redis redis-cli FLUSHDB 2>/dev/null || true
    
    # Send deployment notification
    if [ -n "${SLACK_WEBHOOK_URL:-}" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"✅ Free Mobile Chatbot ML Intelligence Dashboard deployed successfully to $ENVIRONMENT\"}" \
            "$SLACK_WEBHOOK_URL" 2>/dev/null || true
    fi
    
    log_success "✅ Post-deployment tasks completed"
}

# Main deployment function
main() {
    log_info "🚀 Starting Free Mobile Chatbot ML Intelligence Dashboard deployment"
    log_info "Environment: $ENVIRONMENT"
    log_info "Timestamp: $(date)"
    
    check_prerequisites
    backup_deployment
    deploy_services
    
    if perform_health_checks; then
        run_performance_tests
        post_deployment_tasks
        log_success "🎉 Deployment completed successfully!"
        log_info "📊 Dashboard available at: http://localhost:3001"
        log_info "🔧 Backend API available at: http://localhost:5000"
        log_info "🧠 ML Service available at: http://localhost:5001"
    else
        log_error "❌ Deployment failed health checks"
        exit 1
    fi
}

# Script usage
usage() {
    echo "Usage: $0 [environment]"
    echo "  environment: production, staging, development (default: production)"
    echo ""
    echo "Environment variables:"
    echo "  BACKUP_BEFORE_DEPLOY: Create backup before deployment (default: true)"
    echo "  HEALTH_CHECK_TIMEOUT: Health check timeout in seconds (default: 300)"
    echo "  ROLLBACK_ON_FAILURE: Rollback on deployment failure (default: true)"
    echo ""
    echo "Example:"
    echo "  $0 production"
    echo "  BACKUP_BEFORE_DEPLOY=false $0 staging"
}

# Handle script arguments
case "${1:-}" in
    -h|--help)
        usage
        exit 0
        ;;
    production|staging|development)
        main
        ;;
    "")
        main
        ;;
    *)
        echo "Invalid environment: $1"
        usage
        exit 1
        ;;
esac
