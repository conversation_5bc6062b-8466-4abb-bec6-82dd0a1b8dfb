#!/bin/bash

# =====================================================
# ChatbotRNCP Automated Testing & Validation Script
# =====================================================
# Comprehensive testing automation for the ChatbotRNCP application
# Linux/macOS version

set -e  # Exit on any error

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
FRONTEND_DIR="$PROJECT_ROOT/frontend"
BACKEND_DIR="$PROJECT_ROOT/backend"
TEST_RESULTS_DIR="$FRONTEND_DIR/test-results"
REPORTS_DIR="$PROJECT_ROOT/reports"

# Default parameters
TEST_SUITE="all"
BROWSER="all"
SKIP_BUILD=false
KEEP_SERVICES=false
VERBOSE=false
REPORT_FORMAT="html,json,junit"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --test-suite SUITE    Test suite to run (all, unit, e2e, integration, performance)"
    echo "  --browser BROWSER     Browser to test (all, chromium, firefox, webkit)"
    echo "  --skip-build          Skip Docker build step"
    echo "  --keep-services       Keep services running after tests"
    echo "  --verbose             Enable verbose logging"
    echo "  --report-format FMT   Report formats (html,json,junit)"
    echo "  --help                Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Run all tests"
    echo "  $0 --test-suite e2e --browser chrome # Run E2E tests in Chrome only"
    echo "  $0 --test-suite performance          # Run performance tests only"
}

parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --test-suite)
                TEST_SUITE="$2"
                shift 2
                ;;
            --browser)
                BROWSER="$2"
                shift 2
                ;;
            --skip-build)
                SKIP_BUILD=true
                shift
                ;;
            --keep-services)
                KEEP_SERVICES=true
                shift
                ;;
            --verbose)
                VERBOSE=true
                shift
                ;;
            --report-format)
                REPORT_FORMAT="$2"
                shift 2
                ;;
            --help)
                show_usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
}

initialize_environment() {
    log_info "Initializing test environment..."
    
    # Create necessary directories
    mkdir -p "$TEST_RESULTS_DIR" "$REPORTS_DIR"
    
    # Clear previous test results
    rm -rf "$TEST_RESULTS_DIR"/*
    
    # Create log file
    touch "$REPORTS_DIR/test-execution.log"
    
    log_success "Environment initialized"
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker not found. Please install Docker."
        exit 1
    fi
    log_success "Docker found: $(docker --version)"
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose not found. Please install Docker Compose."
        exit 1
    fi
    log_success "Docker Compose found"
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js not found. Please install Node.js."
        exit 1
    fi
    log_success "Node.js found: $(node --version)"
    
    # Check if ports are available
    local ports=(3000 3001 5000 27017 6379)
    for port in "${ports[@]}"; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            log_warning "Port $port is already in use. Services may conflict."
        fi
    done
    
    log_success "Prerequisites check completed"
}

start_services() {
    log_info "Starting ChatbotRNCP services..."
    
    cd "$PROJECT_ROOT"
    
    if [ "$SKIP_BUILD" = false ]; then
        log_info "Building Docker images..."
        if ! docker compose -f docker-compose.prod.yml build --no-cache; then
            log_error "Failed to build Docker images"
            exit 1
        fi
        log_success "Docker images built successfully"
    fi
    
    log_info "Starting services with Docker Compose..."
    if ! docker compose -f docker-compose.prod.yml up -d; then
        log_error "Failed to start services"
        exit 1
    fi
    log_success "Services started successfully"
    
    wait_for_services
}

wait_for_services() {
    log_info "Waiting for services to be ready..."
    
    local max_attempts=24
    local attempt=0
    local all_healthy=false
    
    while [ $attempt -lt $max_attempts ] && [ "$all_healthy" = false ]; do
        attempt=$((attempt + 1))
        log_info "Health check attempt $attempt/$max_attempts..."
        
        local healthy_services=0
        local total_services=3
        
        # Check backend
        if curl -f http://localhost:5000/health &> /dev/null; then
            log_success "✓ Backend is healthy"
            healthy_services=$((healthy_services + 1))
        else
            log_warning "✗ Backend is not responding"
        fi
        
        # Check frontend
        if curl -f http://localhost:3001/ &> /dev/null; then
            log_success "✓ Frontend is healthy"
            healthy_services=$((healthy_services + 1))
        else
            log_warning "✗ Frontend is not responding"
        fi
        
        # Check database via backend
        if curl -f http://localhost:5000/api/health/database &> /dev/null; then
            log_success "✓ Database is healthy"
            healthy_services=$((healthy_services + 1))
        else
            log_warning "✗ Database is not responding"
        fi
        
        if [ $healthy_services -eq $total_services ]; then
            all_healthy=true
            log_success "All services are healthy and ready"
        else
            sleep 5
        fi
    done
    
    if [ "$all_healthy" = false ]; then
        log_error "Services failed to become healthy within the timeout period"
        show_service_logs
        exit 1
    fi
}

show_service_logs() {
    log_info "Showing service logs for debugging..."
    cd "$PROJECT_ROOT"
    docker compose -f docker-compose.prod.yml logs --tail=50
}

install_dependencies() {
    log_info "Installing test dependencies..."
    
    cd "$FRONTEND_DIR"
    
    if [ ! -d "node_modules" ]; then
        log_info "Installing frontend dependencies..."
        npm install
    fi
    
    log_info "Installing Playwright browsers..."
    npx playwright install
    
    log_success "Dependencies installed successfully"
}

run_unit_tests() {
    log_info "Running unit tests..."
    
    cd "$FRONTEND_DIR"
    if npm test -- --coverage --watchAll=false --testResultsProcessor=jest-junit; then
        log_success "Unit tests passed"
        return 0
    else
        log_error "Unit tests failed"
        return 1
    fi
}

run_playwright_tests() {
    local browser_filter="$1"
    
    log_info "Running Playwright E2E tests..."
    
    cd "$FRONTEND_DIR"
    
    local playwright_cmd="npx playwright test"
    
    if [ "$browser_filter" != "all" ]; then
        playwright_cmd="$playwright_cmd --project=$browser_filter"
    fi
    
    IFS=',' read -ra reporters <<< "$REPORT_FORMAT"
    for reporter in "${reporters[@]}"; do
        playwright_cmd="$playwright_cmd --reporter=$reporter"
    done
    
    if [ "$VERBOSE" = true ]; then
        playwright_cmd="$playwright_cmd --verbose"
    fi
    
    log_info "Executing: $playwright_cmd"
    if eval "$playwright_cmd"; then
        log_success "Playwright tests passed"
        return 0
    else
        log_error "Playwright tests failed"
        return 1
    fi
}

run_performance_tests() {
    log_info "Running performance tests..."
    
    local endpoints=(
        "http://localhost:5000/health"
        "http://localhost:5000/api/auth/status"
        "http://localhost:3001/"
    )
    
    local results_file="$REPORTS_DIR/performance-results.json"
    echo '[]' > "$results_file"
    
    local all_passed=true
    
    for endpoint in "${endpoints[@]}"; do
        local start_time=$(date +%s%3N)
        
        if curl -f "$endpoint" -o /dev/null -s -w "%{http_code}" > /tmp/status_code 2>/dev/null; then
            local end_time=$(date +%s%3N)
            local response_time=$((end_time - start_time))
            local status_code=$(cat /tmp/status_code)
            
            if [ "$status_code" = "200" ] && [ $response_time -lt 2000 ]; then
                log_success "✓ $endpoint - ${response_time}ms"
            elif [ "$status_code" = "200" ]; then
                log_warning "⚠ $endpoint - ${response_time}ms (slow)"
            else
                log_error "✗ $endpoint - Status: $status_code"
                all_passed=false
            fi
        else
            log_error "✗ $endpoint - Failed to connect"
            all_passed=false
        fi
    done
    
    if [ "$all_passed" = true ]; then
        log_success "Performance tests passed"
        return 0
    else
        log_error "Some performance tests failed"
        return 1
    fi
}

run_security_tests() {
    log_info "Running security validation tests..."
    
    local all_passed=true
    
    # Test security headers
    log_info "Testing security headers..."
    local headers_response=$(curl -I http://localhost:3001/ 2>/dev/null)
    
    local required_headers=("X-Frame-Options" "X-Content-Type-Options" "X-XSS-Protection")
    
    for header in "${required_headers[@]}"; do
        if echo "$headers_response" | grep -i "$header" > /dev/null; then
            log_success "✓ Security header $header present"
        else
            log_warning "⚠ Security header $header missing"
        fi
    done
    
    # Test rate limiting (simplified)
    log_info "Testing rate limiting..."
    local rate_limit_hit=false
    
    for i in {1..15}; do
        local status_code=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:5000/api/auth/status)
        if [ "$status_code" = "429" ]; then
            rate_limit_hit=true
            break
        fi
        sleep 0.1
    done
    
    if [ "$rate_limit_hit" = true ]; then
        log_success "✓ Rate limiting is working"
    else
        log_warning "⚠ Rate limiting not triggered (may need adjustment)"
    fi
    
    log_success "Security tests completed"
    return 0
}

generate_test_report() {
    log_info "Generating comprehensive test report..."
    
    local report_file="$REPORTS_DIR/comprehensive-test-report.json"
    local html_report="$REPORTS_DIR/test-report.html"
    
    # Generate basic JSON report
    cat > "$report_file" << EOF
{
    "timestamp": "$(date -Iseconds)",
    "testSuite": "$TEST_SUITE",
    "browser": "$BROWSER",
    "environment": {
        "os": "$(uname -s)",
        "shell": "$SHELL",
        "dockerVersion": "$(docker --version)"
    }
}
EOF
    
    # Generate HTML report
    cat > "$html_report" << 'EOF'
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatbotRNCP Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #333; border-bottom: 2px solid #007acc; padding-bottom: 20px; margin-bottom: 30px; }
        .section { margin: 20px 0; padding: 15px; border-left: 4px solid #007acc; background-color: #f9f9f9; }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 ChatbotRNCP Test Report</h1>
            <p>Generated on: $(date)</p>
        </div>
        <div class="section">
            <h2>📊 Test Summary</h2>
            <p>Test execution completed. Check individual test files for detailed results.</p>
        </div>
    </div>
</body>
</html>
EOF
    
    log_success "Test report generated: $report_file"
    log_success "HTML report generated: $html_report"
}

stop_services() {
    if [ "$KEEP_SERVICES" = false ]; then
        log_info "Stopping services..."
        cd "$PROJECT_ROOT"
        if docker compose -f docker-compose.prod.yml down; then
            log_success "Services stopped successfully"
        else
            log_warning "Failed to stop services"
        fi
    else
        log_info "Keeping services running as requested"
    fi
}

show_test_summary() {
    local unit_result=$1
    local e2e_result=$2
    local performance_result=$3
    local security_result=$4
    
    echo ""
    echo "============================================================"
    echo "                    TEST EXECUTION SUMMARY"
    echo "============================================================"
    
    local total_tests=0
    local passed_tests=0
    
    if [ -n "$unit_result" ]; then
        echo ""
        echo "📋 Unit Tests:"
        if [ $unit_result -eq 0 ]; then
            echo "   ✅ PASSED"
            passed_tests=$((passed_tests + 1))
        else
            echo "   ❌ FAILED"
        fi
        total_tests=$((total_tests + 1))
    fi
    
    if [ -n "$e2e_result" ]; then
        echo ""
        echo "📋 E2E Tests:"
        if [ $e2e_result -eq 0 ]; then
            echo "   ✅ PASSED"
            passed_tests=$((passed_tests + 1))
        else
            echo "   ❌ FAILED"
        fi
        total_tests=$((total_tests + 1))
    fi
    
    if [ -n "$performance_result" ]; then
        echo ""
        echo "📋 Performance Tests:"
        if [ $performance_result -eq 0 ]; then
            echo "   ✅ PASSED"
            passed_tests=$((passed_tests + 1))
        else
            echo "   ❌ FAILED"
        fi
        total_tests=$((total_tests + 1))
    fi
    
    if [ -n "$security_result" ]; then
        echo ""
        echo "📋 Security Tests:"
        if [ $security_result -eq 0 ]; then
            echo "   ✅ PASSED"
            passed_tests=$((passed_tests + 1))
        else
            echo "   ❌ FAILED"
        fi
        total_tests=$((total_tests + 1))
    fi
    
    echo ""
    echo -n "🎯 OVERALL RESULT: "
    
    if [ $passed_tests -eq $total_tests ]; then
        echo -e "${GREEN}ALL TESTS PASSED ✅${NC}"
    elif [ $passed_tests -gt 0 ]; then
        echo -e "${YELLOW}PARTIAL SUCCESS ⚠️ ($passed_tests/$total_tests)${NC}"
    else
        echo -e "${RED}ALL TESTS FAILED ❌${NC}"
    fi
    
    echo ""
    echo "📊 Reports generated in: $REPORTS_DIR"
    echo "🌐 Open test-report.html in your browser for detailed results"
    echo "============================================================"
}

main() {
    echo -e "${CYAN}🚀 Starting ChatbotRNCP Automated Testing & Validation${NC}"
    echo -e "${BLUE}Test Suite: $TEST_SUITE | Browser: $BROWSER${NC}"
    
    initialize_environment
    check_prerequisites
    start_services
    install_dependencies
    
    local unit_result=""
    local e2e_result=""
    local performance_result=""
    local security_result=""
    local overall_result=0
    
    case "$TEST_SUITE" in
        "all")
            run_unit_tests; unit_result=$?
            run_playwright_tests "$BROWSER"; e2e_result=$?
            run_performance_tests; performance_result=$?
            run_security_tests; security_result=$?
            ;;
        "unit")
            run_unit_tests; unit_result=$?
            ;;
        "e2e")
            run_playwright_tests "$BROWSER"; e2e_result=$?
            ;;
        "integration")
            run_playwright_tests "$BROWSER"; e2e_result=$?
            run_performance_tests; performance_result=$?
            ;;
        "performance")
            run_performance_tests; performance_result=$?
            ;;
        *)
            log_error "Unknown test suite: $TEST_SUITE"
            exit 1
            ;;
    esac
    
    generate_test_report
    show_test_summary "$unit_result" "$e2e_result" "$performance_result" "$security_result"
    
    # Determine exit code
    for result in "$unit_result" "$e2e_result" "$performance_result" "$security_result"; do
        if [ -n "$result" ] && [ $result -ne 0 ]; then
            overall_result=1
        fi
    done
    
    if [ $overall_result -eq 0 ]; then
        log_success "All tests passed successfully!"
    else
        log_error "Some tests failed. Check the reports for details."
    fi
    
    stop_services
    exit $overall_result
}

# Parse command line arguments
parse_arguments "$@"

# Run main function
main
