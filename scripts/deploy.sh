#!/bin/bash

# Script de déploiement automatisé - Chatbot Free Mobile
# Usage: ./scripts/deploy.sh [production|staging]

set -e

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT=${1:-production}
PROJECT_NAME="free-mobile-chatbot"
DOCKER_REGISTRY="registry.digitalocean.com/freemobile"
BACKUP_DIR="./backups/$(date +%Y%m%d_%H%M%S)"

# Fonctions utilitaires
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Vérification des prérequis
check_prerequisites() {
    log_info "Vérification des prérequis..."
    
    # Vérifier Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker n'est pas installé"
        exit 1
    fi
    
    # Vérifier Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose n'est pas installé"
        exit 1
    fi
    
    # Vérifier les variables d'environnement
    if [[ ! -f ".env.${ENVIRONMENT}" ]]; then
        log_error "Fichier .env.${ENVIRONMENT} manquant"
        exit 1
    fi
    
    log_success "Prérequis validés"
}

# Sauvegarde des données
backup_data() {
    log_info "Sauvegarde des données..."
    
    mkdir -p "$BACKUP_DIR"
    
    # Sauvegarde MongoDB
    if docker-compose ps mongodb &> /dev/null; then
        log_info "Sauvegarde de MongoDB..."
        docker-compose exec -T mongodb mongodump --out /tmp/backup
        docker cp $(docker-compose ps -q mongodb):/tmp/backup "$BACKUP_DIR/mongodb"
    fi
    
    # Sauvegarde des logs
    if [[ -d "./logs" ]]; then
        log_info "Sauvegarde des logs..."
        cp -r ./logs "$BACKUP_DIR/"
    fi
    
    log_success "Sauvegarde terminée: $BACKUP_DIR"
}

# Construction des images Docker
build_images() {
    log_info "Construction des images Docker..."
    
    # Backend
    log_info "Construction de l'image backend..."
    docker build -f backend/Dockerfile.prod -t ${DOCKER_REGISTRY}/${PROJECT_NAME}-backend:latest ./backend
    
    # Rasa
    log_info "Construction de l'image Rasa..."
    docker build -f rasa/Dockerfile.prod -t ${DOCKER_REGISTRY}/${PROJECT_NAME}-rasa:latest ./rasa
    
    log_success "Images construites avec succès"
}

# Push vers le registry
push_images() {
    log_info "Push des images vers le registry..."
    
    docker push ${DOCKER_REGISTRY}/${PROJECT_NAME}-backend:latest
    docker push ${DOCKER_REGISTRY}/${PROJECT_NAME}-rasa:latest
    
    log_success "Images poussées vers le registry"
}

# Tests de santé
health_check() {
    log_info "Vérification de la santé des services..."
    
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f http://localhost:5000/health &> /dev/null; then
            log_success "Backend en ligne"
            break
        fi
        
        log_info "Tentative $attempt/$max_attempts - En attente du backend..."
        sleep 10
        ((attempt++))
    done
    
    if [[ $attempt -gt $max_attempts ]]; then
        log_error "Le backend n'a pas démarré dans les temps"
        return 1
    fi
    
    # Test Rasa
    if curl -f http://localhost:5005/status &> /dev/null; then
        log_success "Rasa en ligne"
    else
        log_warning "Rasa ne répond pas"
    fi
}

# Déploiement principal
deploy() {
    log_info "🚀 Démarrage du déploiement en environnement: $ENVIRONMENT"
    
    # Chargement des variables d'environnement
    export $(cat .env.${ENVIRONMENT} | grep -v '^#' | xargs)
    
    # Arrêt des services existants
    log_info "Arrêt des services existants..."
    docker-compose -f docker-compose.prod.yml down --remove-orphans
    
    # Démarrage des nouveaux services
    log_info "Démarrage des nouveaux services..."
    docker-compose -f docker-compose.prod.yml up -d --build
    
    # Attendre que les services soient prêts
    log_info "Attente du démarrage des services..."
    sleep 30
    
    # Tests de santé
    health_check
    
    log_success "✅ Déploiement terminé avec succès!"
}

# Rollback en cas d'échec
rollback() {
    log_warning "🔄 Rollback en cours..."
    
    if [[ -n "$BACKUP_DIR" && -d "$BACKUP_DIR" ]]; then
        # Restaurer MongoDB
        if [[ -d "$BACKUP_DIR/mongodb" ]]; then
            log_info "Restauration de MongoDB..."
            docker-compose exec -T mongodb mongorestore --drop /tmp/backup
        fi
        
        log_success "Rollback terminé"
    else
        log_error "Aucune sauvegarde trouvée pour le rollback"
    fi
}

# Nettoyage des anciennes images
cleanup() {
    log_info "Nettoyage des anciennes images..."
    
    docker image prune -f
    docker system prune -f --volumes
    
    log_success "Nettoyage terminé"
}

# Gestion des erreurs
trap 'log_error "Erreur détectée, rollback..."; rollback; exit 1' ERR

# Menu principal
case "${1:-}" in
    "production"|"staging")
        check_prerequisites
        backup_data
        build_images
        deploy
        cleanup
        ;;
    "rollback")
        rollback
        ;;
    "health")
        health_check
        ;;
    "cleanup")
        cleanup
        ;;
    *)
        echo "Usage: $0 {production|staging|rollback|health|cleanup}"
        echo ""
        echo "  production  - Déploie en production"
        echo "  staging     - Déploie en staging"
        echo "  rollback    - Restaure la dernière sauvegarde"
        echo "  health      - Vérifie la santé des services"
        echo "  cleanup     - Nettoie les images inutilisées"
        exit 1
        ;;
esac

log_success "🎉 Script terminé avec succès!" 