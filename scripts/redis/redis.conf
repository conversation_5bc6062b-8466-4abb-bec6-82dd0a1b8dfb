# =============================================
# 🔴 REDIS PRODUCTION CONFIGURATION
# Free Mobile Chatbot Dashboard - Phase 4 Production
# Optimized for caching and session management
# =============================================

# ---------------------------------------------
# NETWORK CONFIGURATION
# ---------------------------------------------

# Bind to all interfaces
bind 0.0.0.0

# Port configuration
port 6379

# TCP listen backlog
tcp-backlog 511

# TCP keepalive
tcp-keepalive 300

# Timeout for idle clients (0 = disabled)
timeout 0

# ---------------------------------------------
# GENERAL CONFIGURATION
# ---------------------------------------------

# Run as daemon
daemonize no

# Process ID file
pidfile /var/run/redis/redis-server.pid

# Log level (debug, verbose, notice, warning)
loglevel notice

# Log file
logfile /var/log/redis/redis-server.log

# Number of databases
databases 16

# ---------------------------------------------
# SECURITY
# ---------------------------------------------

# Require password authentication
requirepass FreeMobile_Redis_2025_SecurePassword!

# Disable dangerous commands
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command KEYS ""
rename-command CONFIG "CONFIG_b835c3f8a5d9e7f2"
rename-command SHUTDOWN "SHUTDOWN_a7b9c2d4e6f8"
rename-command DEBUG ""
rename-command EVAL ""

# Protected mode
protected-mode yes

# ---------------------------------------------
# MEMORY MANAGEMENT
# ---------------------------------------------

# Maximum memory usage
maxmemory 512mb

# Memory eviction policy
maxmemory-policy allkeys-lru

# Memory sampling for LRU
maxmemory-samples 5

# ---------------------------------------------
# PERSISTENCE
# ---------------------------------------------

# RDB Snapshots
save 900 1      # Save if at least 1 key changed in 900 seconds
save 300 10     # Save if at least 10 keys changed in 300 seconds
save 60 10000   # Save if at least 10000 keys changed in 60 seconds

# Stop writes on RDB errors
stop-writes-on-bgsave-error yes

# Compress RDB files
rdbcompression yes

# Checksum RDB files
rdbchecksum yes

# RDB filename
dbfilename dump.rdb

# Working directory
dir /data

# AOF (Append Only File) persistence
appendonly yes
appendfilename "appendonly.aof"

# AOF fsync policy
appendfsync everysec

# Rewrite AOF when it grows by this percentage
auto-aof-rewrite-percentage 100

# Minimum size for AOF rewrite
auto-aof-rewrite-min-size 64mb

# Load truncated AOF on startup
aof-load-truncated yes

# Use RDB-AOF hybrid persistence
aof-use-rdb-preamble yes

# ---------------------------------------------
# REPLICATION
# ---------------------------------------------

# Replica serve stale data when link is down
replica-serve-stale-data yes

# Replica read-only mode
replica-read-only yes

# Replication diskless sync
repl-diskless-sync no

# Replication diskless sync delay
repl-diskless-sync-delay 5

# Replica priority for sentinel
replica-priority 100

# ---------------------------------------------
# CLIENT MANAGEMENT
# ---------------------------------------------

# Maximum number of clients
maxclients 10000

# Client output buffer limits
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# Client query buffer limit
client-query-buffer-limit 1gb

# Protocol buffer limit
proto-max-bulk-len 512mb

# ---------------------------------------------
# SLOW LOG
# ---------------------------------------------

# Slow log configuration
slowlog-log-slower-than 10000
slowlog-max-len 128

# ---------------------------------------------
# LATENCY MONITORING
# ---------------------------------------------

# Latency monitoring threshold
latency-monitor-threshold 100

# ---------------------------------------------
# EVENT NOTIFICATION
# ---------------------------------------------

# Keyspace notifications
notify-keyspace-events "Ex"

# ---------------------------------------------
# ADVANCED CONFIGURATION
# ---------------------------------------------

# Hash table configuration
hash-max-ziplist-entries 512
hash-max-ziplist-value 64

# List configuration
list-max-ziplist-size -2
list-compress-depth 0

# Set configuration
set-max-intset-entries 512

# Sorted set configuration
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# HyperLogLog configuration
hll-sparse-max-bytes 3000

# Streams configuration
stream-node-max-bytes 4096
stream-node-max-entries 100

# Active rehashing
activerehashing yes

# Client output buffer limits for different client types
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# ---------------------------------------------
# THREADING
# ---------------------------------------------

# I/O threads (Redis 6.0+)
io-threads 4
io-threads-do-reads yes

# ---------------------------------------------
# MODULES
# ---------------------------------------------

# Load modules (if needed)
# loadmodule /path/to/module.so

# ---------------------------------------------
# CLUSTER CONFIGURATION (if using cluster)
# ---------------------------------------------

# Enable cluster mode
# cluster-enabled yes

# Cluster configuration file
# cluster-config-file nodes-6379.conf

# Cluster node timeout
# cluster-node-timeout 15000

# Cluster require full coverage
# cluster-require-full-coverage yes

# ---------------------------------------------
# SENTINEL CONFIGURATION (if using sentinel)
# ---------------------------------------------

# Sentinel configuration would go here if using Redis Sentinel
# for high availability

# ---------------------------------------------
# PERFORMANCE TUNING
# ---------------------------------------------

# TCP no delay
tcp-nodelay yes

# SO_KEEPALIVE configuration
tcp-keepalive 300

# Disable transparent huge pages
disable-thp yes

# Jemalloc background thread
jemalloc-bg-thread yes

# Active defragmentation
activedefrag yes
active-defrag-ignore-bytes 100mb
active-defrag-threshold-lower 10
active-defrag-threshold-upper 100
active-defrag-cycle-min 1
active-defrag-cycle-max 25

# ---------------------------------------------
# MONITORING
# ---------------------------------------------

# Enable latency monitoring
latency-monitor-threshold 100

# Track client info
tracking-table-max-keys 1000000

# ---------------------------------------------
# LOGGING ENHANCEMENTS
# ---------------------------------------------

# Syslog configuration
# syslog-enabled yes
# syslog-ident redis
# syslog-facility local0

# ---------------------------------------------
# CUSTOM CONFIGURATION FOR ML WORKLOADS
# ---------------------------------------------

# Optimize for ML caching patterns
maxmemory-policy allkeys-lru
maxmemory-samples 10

# Increase hash table size for better performance
hash-max-ziplist-entries 1024
hash-max-ziplist-value 128

# Optimize for session storage
timeout 1800  # 30 minutes session timeout

# Enable keyspace notifications for cache invalidation
notify-keyspace-events "KEA"

# Optimize for high-frequency reads
replica-lazy-flush yes
lazyfree-lazy-eviction yes
lazyfree-lazy-expire yes
lazyfree-lazy-server-del yes

# ---------------------------------------------
# BACKUP AND RECOVERY
# ---------------------------------------------

# RDB save points optimized for production
save 3600 1     # Save every hour if at least 1 change
save 300 100    # Save every 5 minutes if at least 100 changes
save 60 10000   # Save every minute if at least 10000 changes

# Background save nice priority
rdb-save-incremental-fsync yes

# AOF rewrite incremental fsync
aof-rewrite-incremental-fsync yes
