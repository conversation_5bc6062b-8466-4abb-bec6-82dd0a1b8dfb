#!/usr/bin/env bash
set -euo pipefail

# Helper functions for interacting with Vercel CLI
# Required envs: VERCEL_TOKEN, VERCEL_TEAM (optional), VERCEL_PROJECT (optional)

vc() {
  local base=(vercel --token "$VERCEL_TOKEN")
  if [ -n "${VERCEL_TEAM:-}" ]; then base+=(--scope "$VERCEL_TEAM"); fi
  if [ -n "${VERCEL_PROJECT:-}" ]; then base+=(--project "$VERCEL_PROJECT"); fi
  "${base[@]}" "$@"
}

# Return the deployment URL currently serving the given alias
get_current_alias_target() {
  local alias="$1"
  # List aliases and select matching one
  vc alias ls --json | jq -r ".[] | select(.alias == \"${alias}\") | .deployment.url" | head -n1
}

# Deploy frontend (or given path) and emit deployment URL (preview)
deploy_preview() {
  local path="${1:-frontend}"
  (cd "$path" && vc deploy --yes --cwd "$path" --prebuilt --json || vc deploy --yes --cwd "$path" --json) | jq -r '.url'
}

# Set traffic split for alias between stable and canary URLs
set_alias_traffic() {
  local alias="$1" stable="$2" canary="$3" canaryPct="$4"
  local stablePct=$((100 - canaryPct))
  echo "Setting traffic: ${alias} -> ${stable}=${stablePct}% ${canary}=${canaryPct}%"
  vc traffic "$alias" "${stable}=${stablePct}" "${canary}=${canaryPct}"
}

