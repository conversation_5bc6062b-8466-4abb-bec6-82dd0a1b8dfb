#!/usr/bin/env bash
set -euo pipefail

# Sync essential production env variables into the Vercel project (non-interactive)
# Requires: VERCEL_TOKEN, (optional) VERCEL_TEAM, VERCEL_PROJECT
# Inputs via env: NODE_ENV, MONGODB_URI, REDIS_URL, JWT_SECRET, CORS_ORIGIN, OPENAI_API_KEY

source "$(dirname "$0")/vercel-utils.sh"

add_env() {
  local name="$1" value="$2" target="production"
  if [ -z "${value}" ]; then
    echo "[skip] $name is empty; skipping"
    return 0
  fi
  # Remove existing silently if exists (ignore errors)
  vc env rm "$name" "$target" -y >/dev/null 2>&1 || true
  # Add value non-interactively; do not echo value to logs
  printf '%s' "$value" | vc env add "$name" "$target" -y >/dev/null
  echo "[ok] set $name for $target"
}

main() {
  add_env NODE_ENV "${NODE_ENV:-production}"
  add_env MONGODB_URI "${MONGODB_URI:-}"
  add_env REDIS_URL "${REDIS_URL:-}"
  add_env JWT_SECRET "${JWT_SECRET:-}"
  add_env CORS_ORIGIN "${CORS_ORIGIN:-}"
  add_env OPENAI_API_KEY "${OPENAI_API_KEY:-}"
}

main

