import http from 'k6/http'; import { check, sleep } from 'k6'; export const options = { scenarios: { smoke: { executor: 'ramping-vus', startVUs: 1, stages: [ { duration: '30s', target: __ENV.MAX_VUS ? Number(__ENV.MAX_VUS) : 10 }, { duration: '1m', target: __ENV.MAX_VUS ? Number(__ENV.MAX_VUS) : 10 }, { duration: '30s', target: 0 }, ], gracefulStop: '30s', exec: 'main', }, }, thresholds: { http_req_duration: ['p(95)<2000'], }, }; const API = __ENV.API_URL; const BASE = __ENV.BASE_URL; export function main() { // Health const h = http.get(`${API}/health`); check(h, { 'health 200': (r) => r.status === 200 }); // Sample: Dashboard metrics const d = http.get(`${API}/api/dashboard/metrics`); check(d, { 'dashboard ok': (r) => r.status === 200 }); // Sleep small to avoid hammering sleep(1); }