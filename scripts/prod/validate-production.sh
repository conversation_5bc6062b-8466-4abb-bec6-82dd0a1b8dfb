#!/usr/bin/env bash
set -euo pipefail

# Production validation orchestrator (APIs, WebSockets, DBs, cache, a11y/perf hooks)
# This script orchestrates existing test runners where possible to avoid duplication.

RED='\033[0;31m'; GREEN='\033[0;32m'; YELLOW='\033[1;33m'; BLUE='\033[0;34m'; NC='\033[0m'
log() { echo -e "${BLUE}[INFO]${NC} $*"; }
ok()  { echo -e "${GREEN}[OK]${NC}   $*"; }
warn(){ echo -e "${YELLOW}[WARN]${NC} $*"; }
err() { echo -e "${RED}[ERR]${NC}  $*"; }

: "${BASE_URL:?BASE_URL required (frontend)}" || true
: "${API_URL:?API_URL required}" || true
: "${ML_URL:=}"
: "${REPORT_DIR:=reports/prod-validation}"
mkdir -p "$REPORT_DIR"

check_endpoint() {
  local url="$1" name="$2" max_ms="${3:-2000}"
  local start end ms code
  start=$(date +%s%3N)
  code=$(curl -s -o /dev/null -w "%{http_code}" "$url" || true)
  end=$(date +%s%3N); ms=$((end-start))
  if [ "$code" = "200" ] && [ $ms -lt $max_ms ]; then ok "${name}: 200 in ${ms}ms"; return 0
  elif [ "$code" = "200" ]; then warn "${name}: 200 in ${ms}ms (slow)"; return 0
  else err "${name}: HTTP ${code}"; return 1; fi
}

api_health_suite() {
  log "API health checks"
  local failures=0
  # Core
  check_endpoint "$API_URL/health" "Health" 2000 || failures=$((failures+1))
  check_endpoint "$API_URL/api/auth/status" "Auth status" 2000 || failures=$((failures+1))
  # Auth
  check_endpoint "$API_URL/api/auth/login" "Auth login (GET/405 expected)" 3000 || true
  # Features
  check_endpoint "$API_URL/api/v2/ml/health" "ML proxy health" 3000 || true
  check_endpoint "$API_URL/api/analytics/overview" "Analytics overview" 3000 || failures=$((failures+1))
  check_endpoint "$API_URL/api/support/tickets" "Support tickets" 3000 || failures=$((failures+1))
  check_endpoint "$API_URL/api/dashboard/metrics" "Dashboard metrics" 3000 || failures=$((failures+1))
  check_endpoint "$API_URL/api/notifications" "Notifications" 3000 || true
  check_endpoint "$API_URL/api/customer/profile" "Customer profile" 3000 || true
  check_endpoint "$API_URL/api/agent/sessions" "Agent sessions" 3000 || true
  check_endpoint "$API_URL/api/emergency-calls/health" "Emergency calls health" 3000 || true
  check_endpoint "$API_URL/api/ai-calls/health" "AI calls health" 3000 || true
  # Security endpoints
  check_endpoint "$API_URL/api/security/status" "Security status" 3000 || true
  return $failures
}

redis_check() {
  if [ -n "${REDIS_URL:-}" ]; then
    log "Redis check at $REDIS_URL (logical)"
    # Only checks via API if endpoint exists
    curl -fsS "$API_URL/api/health/cache" >/dev/null && ok "Redis via API healthy" || warn "No cache health endpoint or failing"
  else
    warn "REDIS_URL not provided; skipping"
  fi
}

mongo_check() {
  curl -fsS "$API_URL/api/health/database" >/dev/null && ok "MongoDB via API healthy" || warn "DB health endpoint failing"
}

websocket_check() {
  node - <<'NODE'
  const io = require('socket.io-client');
  const url = process.env.WS_URL || (process.env.BASE_URL || '').replace('http', 'ws');
  const socket = io(url, { path: '/socket.io-legacy', timeout: 5000, transports: ['websocket'] });
  const start = Date.now();
  let done = false;
  function exit(code,msg){ if(!done){ done=true; console.log(msg||''); process.exit(code);} }
  socket.on('connect', ()=>{
    const rtt = Date.now()-start; console.log(`WS connect OK in ${rtt}ms`); exit(0);
  });
  socket.on('connect_error', (e)=>{ console.error('WS connect error:', e.message); exit(1); });
  setTimeout(()=>exit(1,'WS timeout'), 10000);
NODE
}

playwright_e2e() {
  # Reuse frontend Playwright config to hit BASE_URL
  if command -v npx >/dev/null 2>&1; then
    (cd frontend && BASE_URL="$BASE_URL" npx playwright test || true)
  fi
}

security_scan() {
  if command -v trivy >/dev/null 2>&1; then
    trivy fs . --severity HIGH,CRITICAL --exit-code 0 | tee "$REPORT_DIR/trivy.txt"
  fi
  if command -v npm >/dev/null 2>&1; then
    (cd backend && npm audit --production || true) | tee "$REPORT_DIR/npm-audit-backend.txt"
    (cd frontend && npm audit --production || true) | tee "$REPORT_DIR/npm-audit-frontend.txt"
  fi
}

main() {
  log "Production validation starting for $BASE_URL"
  api_health_suite || true
  redis_check || true
  mongo_check || true
  websocket_check || true
  playwright_e2e || true
  security_scan || true
  ok "Validation completed (see $REPORT_DIR)"
}

main

