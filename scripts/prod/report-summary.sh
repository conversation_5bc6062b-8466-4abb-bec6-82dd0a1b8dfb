#!/usr/bin/env bash
set -euo pipefail

# Summarize deployment and validation results into a single JSON for artifacts
: "${REPORT_DIR:=reports/prod-validation}"
mkdir -p "$REPORT_DIR"

TIMESTAMP=$(date -Iseconds)
cat > "$REPORT_DIR/summary.json" <<EOF
{
  "timestamp": "$TIMESTAMP",
  "releaseId": "${RELEASE_ID:-unknown}",
  "environment": "${ENVIRONMENT:-production}",
  "frontend": "${FRONTEND_URL:-}",
  "api": "${API_URL:-}",
  "ml": "${ML_URL:-}",
  "notes": "See individual logs and test reports in this directory"
}
EOF

echo "Summary written to $REPORT_DIR/summary.json"

