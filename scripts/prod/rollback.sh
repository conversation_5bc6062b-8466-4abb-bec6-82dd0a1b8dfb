#!/usr/bin/env bash
set -euo pipefail

# Rollback script driven by health check failures
# Integrate with deployment provider to switch traffic back to previous stable release

RED='\033[0;31m'; GREEN='\033[0;32m'; YELLOW='\033[1;33m'; BLUE='\033[0;34m'; NC='\033[0m'
log() { echo -e "${BLUE}[INFO]${NC} $*"; }
ok()  { echo -e "${GREEN}[OK]${NC}   $*"; }
warn(){ echo -e "${YELLOW}[WARN]${NC} $*"; }
err() { echo -e "${RED}[ERR]${NC}  $*"; }

: "${PRIMARY_SERVICE:=frontend}"
: "${STABLE_RELEASE_ID:?STABLE_RELEASE_ID is required}" || true
: "${ALIAS_DOMAIN:=chatbotrncp.vercel.app}"
: "${VERCEL_PROJECT:=********************************}"

provider_switch_to_stable() {
  local svc="$1" relUrl="$2"
  source "$(dirname "$0")/vercel-utils.sh"
  : "${ALIAS_DOMAIN:=chatbotrncp.vercel.app}"
  # Route 100% traffic to stable URL
  # We need the current canary URL to compose a split cmd; if unknown, deploy-utils will accept any placeholder as second entry
  local currentStable=$(get_current_alias_target "$ALIAS_DOMAIN")
  if [ -z "$currentStable" ]; then log "No current stable found; proceeding"; fi
  set_alias_traffic "$ALIAS_DOMAIN" "${relUrl}" "${currentStable:-$relUrl}" 0
}

main() {
  log "Initiating rollback to $STABLE_RELEASE_ID for $PRIMARY_SERVICE"
  provider_switch_to_stable "$PRIMARY_SERVICE" "$STABLE_RELEASE_ID"
  ok "Rollback command issued"
}

main

