#!/usr/bin/env bash
set -euo pipefail

# Canary deployment orchestrator (traffic shifting 5% -> 25% -> 50% -> 100%)
# Assumes an ingress/controller or Vercel projects where traffic can be split via alias or header routing.
# Implement provider-specific steps via functions below.

RED='\033[0;31m'; GREEN='\033[0;32m'; YELLOW='\033[1;33m'; BLUE='\033[0;34m'; NC='\033[0m'
log() { echo -e "${BLUE}[INFO]${NC} $*"; }
ok()  { echo -e "${GREEN}[OK]${NC}   $*"; }
warn(){ echo -e "${YELLOW}[WARN]${NC} $*"; }
err() { echo -e "${RED}[ERR]${NC}  $*"; }

: "${RELEASE_ID:?RELEASE_ID is required}" || true
: "${PRIMARY_SERVICE:=frontend}"    # service name
: "${ENVIRONMENT:=production}"
: "${TRAFFIC_STEPS:=5,25,50,100}"
: "${HEALTH_URL:?HEALTH_URL is required}" || true
: "${TIMEOUT_SECS:=300}"
: "${STEP_HOLD_SECS:=120}"

# Provider hooks — Vercel alias-based traffic splitting
# Required envs: VERCEL_TOKEN, VERCEL_TEAM (optional), VERCEL_PROJECT (optional)
# Also requires jq installed
source "$(dirname "$0")/vercel-utils.sh"

: "${ALIAS_DOMAIN:=chatbotrncp.vercel.app}"
: "${VERCEL_PROJECT:=********************************}"
STABLE_URL=""
CANARY_URL=""

init_canary() {
  if ! command -v jq >/dev/null 2>&1; then err "jq is required"; exit 1; fi
  log "Initializing canary against alias ${ALIAS_DOMAIN}"
  STABLE_URL=$(get_current_alias_target "$ALIAS_DOMAIN")
  if [ -z "$STABLE_URL" ]; then err "Could not determine current stable deployment for alias ${ALIAS_DOMAIN}"; exit 1; fi
  ok "Stable deployment: ${STABLE_URL}"
  if [ -n "${PREDEPLOYED_CANARY_URL:-}" ]; then
    CANARY_URL="$PREDEPLOYED_CANARY_URL"
    ok "Using predeployed canary: ${CANARY_URL}"
  else
    CANARY_URL=$(deploy_preview frontend)
    ok "New canary deployment: ${CANARY_URL}"
  fi
}

set_traffic_percentage() {
  local svc="$1" pct="$2"
  # svc currently unused; we manage alias level
  if [ -z "$STABLE_URL" ] || [ -z "$CANARY_URL" ]; then init_canary; fi
  if [ "$pct" -eq 0 ]; then
    # route 100% to stable
    set_alias_traffic "$ALIAS_DOMAIN" "$STABLE_URL" "$CANARY_URL" 0
  else
    set_alias_traffic "$ALIAS_DOMAIN" "$STABLE_URL" "$CANARY_URL" "$pct"
  fi
}

current_release_log() {
  log "Release ${RELEASE_ID} | env=${ENVIRONMENT}"
}

wait_health() {
  local url="$1" timeout="$2" start=$(date +%s)
  log "Waiting for healthy: $url (timeout=${timeout}s)"
  until curl -fsS "$url" >/dev/null; do
    if [ $(( $(date +%s) - start )) -ge "$timeout" ]; then
      err "Health check timeout for $url"; return 1
    fi
    sleep 5
  done
  ok "Health OK: $url"
}

smoke_validate() {
  # Extend with API checks, WebSocket pings, etc.
  curl -fsS "$HEALTH_URL" >/dev/null && ok "Smoke OK" || { err "Smoke failed"; return 1; }
}

rollback() {
  warn "Rolling back ${PRIMARY_SERVICE} release ${RELEASE_ID}"
  set_traffic_percentage "$PRIMARY_SERVICE" 0 || true
}

main() {
  current_release_log
  IFS=',' read -ra STEPS <<< "$TRAFFIC_STEPS"
  for step in "${STEPS[@]}"; do
    set_traffic_percentage "$PRIMARY_SERVICE" "$step"
    wait_health "$HEALTH_URL" "$TIMEOUT_SECS"
    if smoke_validate; then
      ok "Step ${step}% validated; holding ${STEP_HOLD_SECS}s"
      sleep "$STEP_HOLD_SECS"
    else
      err "Validation failed at ${step}%"; rollback; exit 1
    fi
  done
  ok "Canary complete for ${PRIMARY_SERVICE}"
}

trap 'err "Unexpected error"; rollback; exit 1' ERR
main

