#!/usr/bin/env bash
set -euo pipefail

# Preflight validation for production deploy
# Usage: PREVIEW=0 DOMAIN=chatbot.example.com BACKEND_URL=https://api.example.com ML_URL=https://ml.example.com ./scripts/prod/preflight.sh

RED='\033[0;31m'; GREEN='\033[0;32m'; YELLOW='\033[1;33m'; BLUE='\033[0;34m'; NC='\033[0m'
log() { echo -e "${BLUE}[INFO]${NC} $*"; }
ok()  { echo -e "${GREEN}[OK]${NC}   $*"; }
warn(){ echo -e "${YELLOW}[WARN]${NC} $*"; }
err() { echo -e "${RED}[ERR]${NC}  $*"; }

: "${DOMAIN:?DOMAIN is required}" || true
: "${BACKEND_URL:?BACKEND_URL is required}" || true
: "${FRONTEND_URL:=${DOMAIN}}"
: "${ML_URL:=}"
: "${REQUIRED_VARS:=JWT_SECRET,OPENAI_API_KEY,REDIS_URL,MONGODB_URI}"

log "Preflight: verifying configuration files"
if [ -f vercel.json ]; then ok "Found vercel.json"; else warn "vercel.json not found (ok if deploying via K8s)"; fi
if [ -f .env.production ] || [ -f .env.prod ] || [ -f .env ]; then ok "Found env file"; else warn ".env* not found in repo (expect envs from CI secrets)"; fi

log "Preflight: verifying required environment variables are set in CI context"
IFS=',' read -ra VLIST <<< "$REQUIRED_VARS"
MISSING=()
for v in "${VLIST[@]}"; do
  if [ -z "${!v:-}" ]; then MISSING+=("$v"); fi
done
if [ ${#MISSING[@]} -gt 0 ]; then err "Missing required env vars: ${MISSING[*]}"; exit 2; else ok "All required envs present"; fi

log "Preflight: SSL/TLS check for ${DOMAIN}"
if command -v openssl >/dev/null 2>&1; then
  EXP_DATE=$(echo | openssl s_client -servername "$DOMAIN" -connect "$DOMAIN:443" 2>/dev/null | openssl x509 -noout -dates | sed -n 's/.*notAfter=//p') || true
  if [ -n "$EXP_DATE" ]; then ok "Certificate notAfter: $EXP_DATE"; else warn "Could not retrieve certificate info"; fi
else
  warn "openssl not available; skipping TLS check"
fi

log "Preflight: backend health ${BACKEND_URL}/health"
if curl -fsS "$BACKEND_URL/health" -m 10 >/dev/null; then ok "Backend health OK"; else err "Backend health check failed"; exit 3; fi

if [ -n "$ML_URL" ]; then
  log "Preflight: ML health ${ML_URL}/health"
  if curl -fsS "$ML_URL/health" -m 10 >/dev/null; then ok "ML health OK"; else warn "ML health check failed"; fi
fi

log "Preflight complete"
