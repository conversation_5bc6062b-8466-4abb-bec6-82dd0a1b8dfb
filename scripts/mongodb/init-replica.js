// ============================================= // MONGODB REPLICA SET INITIALIZATION // Free Mobile Chatbot Dashboard - Phase 4 Production // Initialize MongoDB replica set for high availability // ============================================= print('[DEPLOY] Starting MongoDB Replica Set Initialization...'); // Wait for MongoDB to be ready sleep(5000); try { // Initialize replica set print(' Initializing replica set...'); var config = { "_id": "rs0", "version": 1, "members": [ { "_id": 0, "host": "mongodb-primary:27017", "priority": 2, "tags": { "role": "primary" } }, { "_id": 1, "host": "mongodb-secondary1:27017", "priority": 1, "tags": { "role": "secondary" } }, { "_id": 2, "host": "mongodb-secondary2:27017", "priority": 1, "tags": { "role": "secondary" } } ], "settings": { "chainingAllowed": true, "heartbeatIntervalMillis": 2000, "heartbeatTimeoutSecs": 10, "electionTimeoutMillis": 10000, "catchUpTimeoutMillis": 60000, "getLastErrorModes": { "majority": { "role": 2 } }, "getLastErrorDefaults": { "w": "majority", "wtimeout": 5000 } } }; var result = rs.initiate(config); print('[COMPLETE] Replica set initialization result:', JSON.stringify(result)); // Wait for replica set to be ready print('⏳ Waiting for replica set to be ready...'); sleep(10000); // Check replica set status var status = rs.status(); print('[ANALYTICS] Replica set status:', JSON.stringify(status, null, 2)); // Create application database and user print('[USER] Creating application database and user...'); // Switch to admin database db = db.getSiblingDB('admin'); // Create application user db.createUser({ user: process.env.MONGO_ROOT_USERNAME || 'freemobile_admin', pwd: process.env.MONGO_ROOT_PASSWORD || 'FreeMobile_MongoDB_2025_SecurePassword!', roles: [ { role: 'root', db: 'admin' } ] }); // Switch to application database db = db.getSiblingDB(process.env.MONGO_DATABASE || 'freemobile_chatbot_prod'); // Create application collections with proper indexes print(' Creating application collections...'); // Users collection db.createCollection('users'); db.users.createIndex({ email: 1 }, { unique: true }); db.users.createIndex({ role: 1 }); db.users.createIndex({ createdAt: 1 }); db.users.createIndex({ lastLoginAt: 1 }); // Conversations collection db.createCollection('conversations'); db.conversations.createIndex({ customerId: 1 }); db.conversations.createIndex({ agentId: 1 }); db.conversations.createIndex({ status: 1 }); db.conversations.createIndex({ createdAt: 1 }); db.conversations.createIndex({ updatedAt: 1 }); db.conversations.createIndex({ priority: 1 }); db.conversations.createIndex({ category: 1 }); // Messages collection db.createCollection('messages'); db.messages.createIndex({ conversationId: 1 }); db.messages.createIndex({ senderId: 1 }); db.messages.createIndex({ timestamp: 1 }); db.messages.createIndex({ type: 1 }); // Customers collection db.createCollection('customers'); db.customers.createIndex({ email: 1 }, { unique: true }); db.customers.createIndex({ phoneNumber: 1 }); db.customers.createIndex({ customerId: 1 }, { unique: true }); db.customers.createIndex({ createdAt: 1 }); db.customers.createIndex({ lastContactAt: 1 }); // ML Classifications collection db.createCollection('ml_classifications'); db.ml_classifications.createIndex({ conversationId: 1 }); db.ml_classifications.createIndex({ category: 1 }); db.ml_classifications.createIndex({ priorityScore: 1 }); db.ml_classifications.createIndex({ confidence: 1 }); db.ml_classifications.createIndex({ processedAt: 1 }); db.ml_classifications.createIndex({ humanValidated: 1 }); // ML Alerts collection db.createCollection('ml_alerts'); db.ml_alerts.createIndex({ type: 1 }); db.ml_alerts.createIndex({ severity: 1 }); db.ml_alerts.createIndex({ status: 1 }); db.ml_alerts.createIndex({ priority: 1 }); db.ml_alerts.createIndex({ createdAt: 1 }); db.ml_alerts.createIndex({ resolvedAt: 1 }); // Analytics collection db.createCollection('analytics'); db.analytics.createIndex({ date: 1 }); db.analytics.createIndex({ type: 1 }); db.analytics.createIndex({ category: 1 }); // Sessions collection db.createCollection('sessions'); db.sessions.createIndex({ sessionId: 1 }, { unique: true }); db.sessions.createIndex({ userId: 1 }); db.sessions.createIndex({ expiresAt: 1 }, { expireAfterSeconds: 0 }); // Audit logs collection db.createCollection('audit_logs'); db.audit_logs.createIndex({ userId: 1 }); db.audit_logs.createIndex({ action: 1 }); db.audit_logs.createIndex({ timestamp: 1 }); db.audit_logs.createIndex({ resource: 1 }); // Create compound indexes for better query performance db.conversations.createIndex({ customerId: 1, status: 1 }); db.conversations.createIndex({ agentId: 1, status: 1 }); db.conversations.createIndex({ createdAt: 1, priority: 1 }); db.ml_classifications.createIndex({ category: 1, priorityScore: 1 }); db.ml_classifications.createIndex({ processedAt: 1, humanValidated: 1 }); print('[COMPLETE] Collections and indexes created successfully'); // Insert initial admin user print('[ADMIN] Creating initial admin user...'); var adminUser = { email: '<EMAIL>', password: '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uO.G', // hashed 'admin123' firstName: 'Admin', lastName: 'Free Mobile', role: 'admin', isActive: true, permissions: [ 'dashboard.view', 'conversations.manage', 'users.manage', 'analytics.view', 'ml.manage', 'settings.manage' ], createdAt: new Date(), updatedAt: new Date() }; db.users.insertOne(adminUser); print('[COMPLETE] Initial admin user created'); // Insert sample data for testing print('[ANALYTICS] Inserting sample data...'); // Sample customer var sampleCustomer = { customerId: 'CUST_001', email: '<EMAIL>', firstName: 'Jean', lastName: 'Dupont', phoneNumber: '+33123456789', subscriptionType: 'mobile', status: 'active', createdAt: new Date(), lastContactAt: new Date() }; db.customers.insertOne(sampleCustomer); // Sample conversation var sampleConversation = { customerId: sampleCustomer._id, subject: 'Problème de facturation', status: 'open', priority: 'high', category: 'billing', channel: 'chat', createdAt: new Date(), updatedAt: new Date(), metadata: { source: 'website', userAgent: 'Mozilla/5.0...' } }; db.conversations.insertOne(sampleConversation); print('[COMPLETE] Sample data inserted successfully'); // Set read preference for replica set print(' Configuring read preferences...'); db.runCommand({ setDefaultRWConcern: 1, defaultReadConcern: { level: "majority" }, defaultWriteConcern: { w: "majority", wtimeout: 5000 } }); print(' MongoDB Replica Set initialization completed successfully!'); print(' Summary:'); print(' - Replica set "rs0" initialized with 3 members'); print(' - Application database and collections created'); print(' - Indexes created for optimal performance'); print(' - Initial admin user created'); print(' - Sample data inserted for testing'); print(' - Read/Write concerns configured'); } catch (error) { print('[FAILED] Error during MongoDB initialization:', error); throw error; }