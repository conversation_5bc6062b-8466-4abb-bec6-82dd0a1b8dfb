# =============================================
# 🗄️ MONGODB PRODUCTION CONFIGURATION
# Free Mobile Chatbot Dashboard - Phase 4 Production
# Optimized for performance, security, and reliability
# =============================================

# Network interfaces
net:
  port: 27017
  bindIp: 0.0.0.0
  maxIncomingConnections: 1000
  wireObjectCheck: true
  ipv6: false

# Storage configuration
storage:
  dbPath: /data/db
  journal:
    enabled: true
    commitIntervalMs: 100
  directoryPerDB: true
  syncPeriodSecs: 60
  engine: wiredTiger
  wiredTiger:
    engineConfig:
      cacheSizeGB: 1
      journalCompressor: snappy
      directoryForIndexes: true
    collectionConfig:
      blockCompressor: snappy
    indexConfig:
      prefixCompression: true

# System log configuration
systemLog:
  destination: file
  logAppend: true
  path: /var/log/mongodb/mongod.log
  logRotate: reopen
  verbosity: 1
  component:
    accessControl:
      verbosity: 1
    command:
      verbosity: 1
    control:
      verbosity: 1
    geo:
      verbosity: 1
    index:
      verbosity: 1
    network:
      verbosity: 1
    query:
      verbosity: 1
    replication:
      verbosity: 1
    sharding:
      verbosity: 1
    storage:
      verbosity: 1
    write:
      verbosity: 1

# Process management
processManagement:
  fork: false
  pidFilePath: /var/run/mongodb/mongod.pid
  timeZoneInfo: /usr/share/zoneinfo

# Security configuration
security:
  authorization: enabled
  keyFile: /etc/mongodb/keyfile
  clusterAuthMode: keyFile

# Replication configuration
replication:
  replSetName: rs0
  enableMajorityReadConcern: true

# Operation profiling
operationProfiling:
  mode: slowOp
  slowOpThresholdMs: 100
  slowOpSampleRate: 1.0

# Set parameters for optimization
setParameter:
  # Connection management
  connPoolMaxShardedConnsPerHost: 200
  connPoolMaxConnsPerHost: 200
  
  # Query optimization
  internalQueryPlanEvaluationMaxResults: 101
  internalQueryPlanEvaluationCollFraction: 0.3
  
  # Write concern
  journalCommitInterval: 100
  
  # Replication
  replWriterThreadCount: 16
  replBatchLimitOperations: 5000
  
  # Indexing
  indexBuildRetry: true
  
  # Logging
  logLevel: 1
  
  # Performance
  wiredTigerConcurrentReadTransactions: 128
  wiredTigerConcurrentWriteTransactions: 128
  
  # Security
  authenticationMechanisms: SCRAM-SHA-1,SCRAM-SHA-256
  
  # Diagnostics
  diagnosticDataCollectionEnabled: true
  
  # Failover
  electionTimeoutMillis: 10000
  
  # Chunk migration
  migrateCloneInsertionBatchSize: 50
  migrateCloneInsertionBatchDelayMS: 0
