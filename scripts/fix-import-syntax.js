const fs = require('fs'); const path = require('path'); const glob = require('glob'); function fixImportSyntax(filePath) { try { let content = fs.readFileSync(filePath, 'utf8'); let modified = false; // Fix broken import statements // Pattern: } 'module'; -> } from 'module'; content = content.replace(/}\s*['"]([^'"]+)['"];/g, "} from '$1';"); // Fix missing commas in imports content = content.replace(/(\w+)\s+(\w+\s+as\s+\w+)/g, '$1, $2'); // Fix missing 'from' keyword content = content.replace(/import\s*{([^}]+)}\s*['"]([^'"]+)['"];/g, "import {$1} from '$2';"); if (content !== fs.readFileSync(filePath, 'utf8')) { fs.writeFileSync(filePath, content); console.log(`[COMPLETE] Fixed import syntax in: ${filePath}`); modified = true; } return modified; } catch (error) { console.error(`[FAILED] Error fixing ${filePath}:`, error.message); return false; } } console.log('[CONFIG] Fixing import syntax errors...'); // Find all TypeScript/JavaScript files in frontend/src const files = [ 'frontend/src/**/*.tsx', 'frontend/src/**/*.ts', 'frontend/src/**/*.jsx', 'frontend/src/**/*.js' ]; let totalFixed = 0; files.forEach(pattern => { const matches = glob.sync(pattern, { cwd: path.join(__dirname, '..') }); matches.forEach(file => { const fullPath = path.join(__dirname, '..', file); if (fixImportSyntax(fullPath)) { totalFixed++; } }); }); console.log(`[COMPLETE] Fixed import syntax in ${totalFixed} files!`);