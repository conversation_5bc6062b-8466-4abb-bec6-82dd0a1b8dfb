#!/bin/bash
# =============================================
# 🗄️ MONGODB BACKUP SCRIPT
# Free Mobile Chatbot Dashboard - Phase 4 Production
# Automated backup with compression and retention
# =============================================

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="${SCRIPT_DIR}/backup.conf"

# Default configuration
MONGO_HOST="${MONGO_HOST:-mongodb-primary}"
MONGO_PORT="${MONGO_PORT:-27017}"
MONGO_USERNAME="${MONGO_USERNAME:-freemobile_admin}"
MONGO_PASSWORD="${MONGO_PASSWORD:-FreeMobile_MongoDB_2025_SecurePassword!}"
MONGO_DATABASE="${MONGO_DATABASE:-freemobile_chatbot_prod}"
MONGO_AUTH_DB="${MONGO_AUTH_DB:-admin}"

BACKUP_DIR="${BACKUP_DIR:-/backups/mongodb}"
RETENTION_DAYS="${RETENTION_DAYS:-30}"
COMPRESSION="${COMPRESSION:-gzip}"
NOTIFICATION_EMAIL="${NOTIFICATION_EMAIL:-<EMAIL>}"

# AWS S3 Configuration (optional)
AWS_S3_BUCKET="${AWS_S3_BUCKET:-}"
AWS_REGION="${AWS_REGION:-eu-west-1}"

# Logging
LOG_FILE="${BACKUP_DIR}/logs/mongodb-backup.log"
TIMESTAMP=$(date '+%Y%m%d_%H%M%S')
BACKUP_NAME="mongodb_backup_${TIMESTAMP}"

# Functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a "$LOG_FILE"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}" | tee -a "$LOG_FILE"
}

# Load configuration if exists
if [[ -f "$CONFIG_FILE" ]]; then
    source "$CONFIG_FILE"
    log "Configuration loaded from $CONFIG_FILE"
fi

# Create directories
create_directories() {
    log "Creating backup directories..."
    mkdir -p "$BACKUP_DIR"/{daily,weekly,monthly,logs}
    mkdir -p "$BACKUP_DIR/temp"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if mongodump is available
    if ! command -v mongodump &> /dev/null; then
        error "mongodump is not installed or not in PATH"
        exit 1
    fi
    
    # Check MongoDB connection
    if ! mongosh --host "$MONGO_HOST:$MONGO_PORT" \
                 --username "$MONGO_USERNAME" \
                 --password "$MONGO_PASSWORD" \
                 --authenticationDatabase "$MONGO_AUTH_DB" \
                 --eval "db.adminCommand('ping')" &> /dev/null; then
        error "Cannot connect to MongoDB"
        exit 1
    fi
    
    # Check disk space (require at least 5GB free)
    AVAILABLE_SPACE=$(df "$BACKUP_DIR" | awk 'NR==2 {print $4}')
    REQUIRED_SPACE=5242880  # 5GB in KB
    
    if [[ $AVAILABLE_SPACE -lt $REQUIRED_SPACE ]]; then
        error "Insufficient disk space. Available: ${AVAILABLE_SPACE}KB, Required: ${REQUIRED_SPACE}KB"
        exit 1
    fi
    
    log "Prerequisites check passed"
}

# Perform MongoDB backup
perform_backup() {
    local backup_type="$1"
    local backup_path="$BACKUP_DIR/$backup_type/$BACKUP_NAME"
    
    log "Starting MongoDB backup ($backup_type)..."
    
    # Create backup directory
    mkdir -p "$backup_path"
    
    # Perform mongodump
    if mongodump \
        --host "$MONGO_HOST:$MONGO_PORT" \
        --username "$MONGO_USERNAME" \
        --password "$MONGO_PASSWORD" \
        --authenticationDatabase "$MONGO_AUTH_DB" \
        --db "$MONGO_DATABASE" \
        --out "$backup_path" \
        --gzip \
        --oplog; then
        
        log "MongoDB dump completed successfully"
    else
        error "MongoDB dump failed"
        return 1
    fi
    
    # Create metadata file
    cat > "$backup_path/backup_metadata.json" << EOF
{
    "backup_name": "$BACKUP_NAME",
    "backup_type": "$backup_type",
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "database": "$MONGO_DATABASE",
    "host": "$MONGO_HOST:$MONGO_PORT",
    "compression": "$COMPRESSION",
    "size_bytes": $(du -sb "$backup_path" | cut -f1),
    "files_count": $(find "$backup_path" -type f | wc -l)
}
EOF
    
    # Compress backup if requested
    if [[ "$COMPRESSION" == "gzip" ]]; then
        log "Compressing backup..."
        tar -czf "${backup_path}.tar.gz" -C "$BACKUP_DIR/$backup_type" "$BACKUP_NAME"
        rm -rf "$backup_path"
        backup_path="${backup_path}.tar.gz"
        log "Backup compressed: $backup_path"
    fi
    
    # Calculate and log backup size
    BACKUP_SIZE=$(du -sh "$backup_path" | cut -f1)
    log "Backup completed: $backup_path (Size: $BACKUP_SIZE)"
    
    # Upload to S3 if configured
    if [[ -n "$AWS_S3_BUCKET" ]]; then
        upload_to_s3 "$backup_path" "$backup_type"
    fi
    
    return 0
}

# Upload backup to AWS S3
upload_to_s3() {
    local backup_path="$1"
    local backup_type="$2"
    local s3_key="mongodb/$backup_type/$(basename "$backup_path")"
    
    log "Uploading backup to S3: s3://$AWS_S3_BUCKET/$s3_key"
    
    if aws s3 cp "$backup_path" "s3://$AWS_S3_BUCKET/$s3_key" \
        --region "$AWS_REGION" \
        --storage-class STANDARD_IA; then
        log "Backup uploaded to S3 successfully"
    else
        warn "Failed to upload backup to S3"
    fi
}

# Clean old backups
cleanup_old_backups() {
    log "Cleaning up old backups..."
    
    # Clean local backups
    for backup_type in daily weekly monthly; do
        local retention_days="$RETENTION_DAYS"
        
        # Different retention for different backup types
        case "$backup_type" in
            daily) retention_days=7 ;;
            weekly) retention_days=30 ;;
            monthly) retention_days=365 ;;
        esac
        
        log "Cleaning $backup_type backups older than $retention_days days"
        find "$BACKUP_DIR/$backup_type" -name "mongodb_backup_*" -mtime +$retention_days -delete
    done
    
    # Clean S3 backups if configured
    if [[ -n "$AWS_S3_BUCKET" ]]; then
        log "Cleaning old S3 backups..."
        # This would require AWS CLI and proper lifecycle policies
        # For now, we rely on S3 lifecycle policies configured separately
    fi
    
    log "Cleanup completed"
}

# Verify backup integrity
verify_backup() {
    local backup_path="$1"
    
    log "Verifying backup integrity..."
    
    if [[ "$backup_path" == *.tar.gz ]]; then
        # Verify compressed backup
        if tar -tzf "$backup_path" > /dev/null 2>&1; then
            log "Backup archive integrity verified"
            return 0
        else
            error "Backup archive is corrupted"
            return 1
        fi
    else
        # Verify uncompressed backup
        if [[ -d "$backup_path" ]] && [[ -f "$backup_path/backup_metadata.json" ]]; then
            log "Backup directory structure verified"
            return 0
        else
            error "Backup directory structure is invalid"
            return 1
        fi
    fi
}

# Send notification
send_notification() {
    local status="$1"
    local message="$2"
    
    if [[ -n "$NOTIFICATION_EMAIL" ]]; then
        local subject="MongoDB Backup $status - $(date)"
        echo "$message" | mail -s "$subject" "$NOTIFICATION_EMAIL" 2>/dev/null || true
    fi
    
    # Log to syslog
    logger -t "mongodb-backup" "$status: $message"
}

# Generate backup report
generate_report() {
    local backup_path="$1"
    local backup_type="$2"
    local start_time="$3"
    local end_time="$4"
    
    local duration=$((end_time - start_time))
    local backup_size=$(du -sh "$backup_path" 2>/dev/null | cut -f1 || echo "Unknown")
    
    cat << EOF
MongoDB Backup Report
=====================
Backup Name: $BACKUP_NAME
Backup Type: $backup_type
Database: $MONGO_DATABASE
Start Time: $(date -d "@$start_time" '+%Y-%m-%d %H:%M:%S')
End Time: $(date -d "@$end_time" '+%Y-%m-%d %H:%M:%S')
Duration: ${duration}s
Backup Size: $backup_size
Backup Path: $backup_path
Status: SUCCESS
EOF
}

# Main execution
main() {
    local backup_type="${1:-daily}"
    local start_time=$(date +%s)
    
    log "Starting MongoDB backup process..."
    log "Backup type: $backup_type"
    
    # Create directories
    create_directories
    
    # Check prerequisites
    check_prerequisites
    
    # Perform backup
    if perform_backup "$backup_type"; then
        local backup_path="$BACKUP_DIR/$backup_type/$BACKUP_NAME"
        [[ "$COMPRESSION" == "gzip" ]] && backup_path="${backup_path}.tar.gz"
        
        # Verify backup
        if verify_backup "$backup_path"; then
            local end_time=$(date +%s)
            
            # Generate report
            local report=$(generate_report "$backup_path" "$backup_type" "$start_time" "$end_time")
            log "$report"
            
            # Clean old backups
            cleanup_old_backups
            
            # Send success notification
            send_notification "SUCCESS" "$report"
            
            log "MongoDB backup completed successfully"
            exit 0
        else
            error "Backup verification failed"
            send_notification "FAILED" "Backup verification failed for $BACKUP_NAME"
            exit 1
        fi
    else
        error "Backup process failed"
        send_notification "FAILED" "Backup process failed for $BACKUP_NAME"
        exit 1
    fi
}

# Handle script arguments
case "${1:-daily}" in
    daily|weekly|monthly)
        main "$1"
        ;;
    --help|-h)
        echo "Usage: $0 [daily|weekly|monthly]"
        echo "Performs MongoDB backup with specified retention policy"
        exit 0
        ;;
    *)
        error "Invalid backup type: $1"
        echo "Usage: $0 [daily|weekly|monthly]"
        exit 1
        ;;
esac
