#!/bin/bash
# =============================================
# 📊 TIMESCALEDB BACKUP SCRIPT
# Free Mobile Chatbot Dashboard - Phase 4 Production
# Automated backup with compression and retention
# =============================================

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="${SCRIPT_DIR}/backup.conf"

# Default configuration
POSTGRES_HOST="${POSTGRES_HOST:-timescaledb}"
POSTGRES_PORT="${POSTGRES_PORT:-5432}"
POSTGRES_USER="${POSTGRES_USER:-timescale_admin}"
POSTGRES_PASSWORD="${POSTGRES_PASSWORD:-FreeMobile_TimescaleDB_2025_SecurePassword!}"
POSTGRES_DB="${POSTGRES_DB:-freemobile_ml_analytics}"

BACKUP_DIR="${BACKUP_DIR:-/backups/timescaledb}"
RETENTION_DAYS="${RETENTION_DAYS:-30}"
COMPRESSION="${COMPRESSION:-gzip}"
NOTIFICATION_EMAIL="${NOTIFICATION_EMAIL:-<EMAIL>}"

# AWS S3 Configuration (optional)
AWS_S3_BUCKET="${AWS_S3_BUCKET:-}"
AWS_REGION="${AWS_REGION:-eu-west-1}"

# Logging
LOG_FILE="${BACKUP_DIR}/logs/timescaledb-backup.log"
TIMESTAMP=$(date '+%Y%m%d_%H%M%S')
BACKUP_NAME="timescaledb_backup_${TIMESTAMP}"

# Export password for pg_dump
export PGPASSWORD="$POSTGRES_PASSWORD"

# Functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a "$LOG_FILE"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}" | tee -a "$LOG_FILE"
}

# Load configuration if exists
if [[ -f "$CONFIG_FILE" ]]; then
    source "$CONFIG_FILE"
    log "Configuration loaded from $CONFIG_FILE"
fi

# Create directories
create_directories() {
    log "Creating backup directories..."
    mkdir -p "$BACKUP_DIR"/{daily,weekly,monthly,logs}
    mkdir -p "$BACKUP_DIR/temp"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if pg_dump is available
    if ! command -v pg_dump &> /dev/null; then
        error "pg_dump is not installed or not in PATH"
        exit 1
    fi
    
    # Check PostgreSQL connection
    if ! psql -h "$POSTGRES_HOST" -p "$POSTGRES_PORT" -U "$POSTGRES_USER" -d "$POSTGRES_DB" \
             -c "SELECT 1;" &> /dev/null; then
        error "Cannot connect to PostgreSQL/TimescaleDB"
        exit 1
    fi
    
    # Check if TimescaleDB extension is available
    if ! psql -h "$POSTGRES_HOST" -p "$POSTGRES_PORT" -U "$POSTGRES_USER" -d "$POSTGRES_DB" \
             -c "SELECT extname FROM pg_extension WHERE extname = 'timescaledb';" | grep -q timescaledb; then
        warn "TimescaleDB extension not found in database"
    fi
    
    # Check disk space (require at least 10GB free for analytics data)
    AVAILABLE_SPACE=$(df "$BACKUP_DIR" | awk 'NR==2 {print $4}')
    REQUIRED_SPACE=10485760  # 10GB in KB
    
    if [[ $AVAILABLE_SPACE -lt $REQUIRED_SPACE ]]; then
        error "Insufficient disk space. Available: ${AVAILABLE_SPACE}KB, Required: ${REQUIRED_SPACE}KB"
        exit 1
    fi
    
    log "Prerequisites check passed"
}

# Get database statistics
get_db_stats() {
    log "Gathering database statistics..."
    
    # Get database size
    DB_SIZE=$(psql -h "$POSTGRES_HOST" -p "$POSTGRES_PORT" -U "$POSTGRES_USER" -d "$POSTGRES_DB" \
              -t -c "SELECT pg_size_pretty(pg_database_size('$POSTGRES_DB'));" | xargs)
    
    # Get table count
    TABLE_COUNT=$(psql -h "$POSTGRES_HOST" -p "$POSTGRES_PORT" -U "$POSTGRES_USER" -d "$POSTGRES_DB" \
                  -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema NOT IN ('information_schema', 'pg_catalog');" | xargs)
    
    # Get hypertable count
    HYPERTABLE_COUNT=$(psql -h "$POSTGRES_HOST" -p "$POSTGRES_PORT" -U "$POSTGRES_USER" -d "$POSTGRES_DB" \
                       -t -c "SELECT COUNT(*) FROM timescaledb_information.hypertables;" 2>/dev/null | xargs || echo "0")
    
    log "Database size: $DB_SIZE"
    log "Table count: $TABLE_COUNT"
    log "Hypertable count: $HYPERTABLE_COUNT"
}

# Perform TimescaleDB backup
perform_backup() {
    local backup_type="$1"
    local backup_path="$BACKUP_DIR/$backup_type"
    local backup_file="$backup_path/$BACKUP_NAME.sql"
    
    log "Starting TimescaleDB backup ($backup_type)..."
    
    # Create backup directory
    mkdir -p "$backup_path"
    
    # Perform pg_dump with TimescaleDB-specific options
    if pg_dump \
        -h "$POSTGRES_HOST" \
        -p "$POSTGRES_PORT" \
        -U "$POSTGRES_USER" \
        -d "$POSTGRES_DB" \
        --verbose \
        --no-password \
        --format=custom \
        --compress=9 \
        --no-privileges \
        --no-owner \
        --file="$backup_file"; then
        
        log "PostgreSQL dump completed successfully"
    else
        error "PostgreSQL dump failed"
        return 1
    fi
    
    # Create schema-only backup for faster restores
    local schema_file="$backup_path/${BACKUP_NAME}_schema.sql"
    if pg_dump \
        -h "$POSTGRES_HOST" \
        -p "$POSTGRES_PORT" \
        -U "$POSTGRES_USER" \
        -d "$POSTGRES_DB" \
        --schema-only \
        --verbose \
        --no-password \
        --file="$schema_file"; then
        
        log "Schema-only backup completed"
    else
        warn "Schema-only backup failed"
    fi
    
    # Create TimescaleDB-specific metadata backup
    create_timescaledb_metadata "$backup_path"
    
    # Create metadata file
    cat > "$backup_path/backup_metadata.json" << EOF
{
    "backup_name": "$BACKUP_NAME",
    "backup_type": "$backup_type",
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "database": "$POSTGRES_DB",
    "host": "$POSTGRES_HOST:$POSTGRES_PORT",
    "database_size": "$DB_SIZE",
    "table_count": $TABLE_COUNT,
    "hypertable_count": $HYPERTABLE_COUNT,
    "compression": "$COMPRESSION",
    "backup_size_bytes": $(stat -f%z "$backup_file" 2>/dev/null || stat -c%s "$backup_file" 2>/dev/null || echo "0"),
    "schema_backup": "$(basename "$schema_file")",
    "pg_version": "$(psql -h "$POSTGRES_HOST" -p "$POSTGRES_PORT" -U "$POSTGRES_USER" -d "$POSTGRES_DB" -t -c "SELECT version();" | head -1 | xargs)"
}
EOF
    
    # Compress backup if requested
    if [[ "$COMPRESSION" == "gzip" ]]; then
        log "Compressing backup..."
        tar -czf "${backup_path}/${BACKUP_NAME}.tar.gz" -C "$backup_path" \
            "$(basename "$backup_file")" \
            "$(basename "$schema_file")" \
            "backup_metadata.json" \
            "timescaledb_metadata.sql" 2>/dev/null || true
        
        # Remove uncompressed files
        rm -f "$backup_file" "$schema_file" "$backup_path/backup_metadata.json" "$backup_path/timescaledb_metadata.sql"
        backup_file="${backup_path}/${BACKUP_NAME}.tar.gz"
        log "Backup compressed: $backup_file"
    fi
    
    # Calculate and log backup size
    BACKUP_SIZE=$(du -sh "$backup_file" | cut -f1)
    log "Backup completed: $backup_file (Size: $BACKUP_SIZE)"
    
    # Upload to S3 if configured
    if [[ -n "$AWS_S3_BUCKET" ]]; then
        upload_to_s3 "$backup_file" "$backup_type"
    fi
    
    return 0
}

# Create TimescaleDB-specific metadata backup
create_timescaledb_metadata() {
    local backup_path="$1"
    local metadata_file="$backup_path/timescaledb_metadata.sql"
    
    log "Creating TimescaleDB metadata backup..."
    
    cat > "$metadata_file" << 'EOF'
-- TimescaleDB Metadata Backup
-- This file contains TimescaleDB-specific metadata for restore

-- Hypertables information
\echo 'Hypertables:'
SELECT schemaname, tablename, num_dimensions 
FROM timescaledb_information.hypertables;

-- Continuous aggregates information
\echo 'Continuous Aggregates:'
SELECT view_schema, view_name, materialized_only 
FROM timescaledb_information.continuous_aggregates;

-- Compression policies
\echo 'Compression Policies:'
SELECT hypertable_schema, hypertable_name, compress_after 
FROM timescaledb_information.compression_policies;

-- Retention policies
\echo 'Retention Policies:'
SELECT hypertable_schema, hypertable_name, drop_after 
FROM timescaledb_information.retention_policies;

-- Jobs information
\echo 'Background Jobs:'
SELECT job_id, application_name, schedule_interval, max_runtime, max_retries, retry_period 
FROM timescaledb_information.jobs;
EOF
    
    # Execute the metadata queries and append results
    psql -h "$POSTGRES_HOST" -p "$POSTGRES_PORT" -U "$POSTGRES_USER" -d "$POSTGRES_DB" \
         -f "$metadata_file" >> "$metadata_file.output" 2>/dev/null || true
}

# Upload backup to AWS S3
upload_to_s3() {
    local backup_file="$1"
    local backup_type="$2"
    local s3_key="timescaledb/$backup_type/$(basename "$backup_file")"
    
    log "Uploading backup to S3: s3://$AWS_S3_BUCKET/$s3_key"
    
    if aws s3 cp "$backup_file" "s3://$AWS_S3_BUCKET/$s3_key" \
        --region "$AWS_REGION" \
        --storage-class STANDARD_IA; then
        log "Backup uploaded to S3 successfully"
    else
        warn "Failed to upload backup to S3"
    fi
}

# Clean old backups
cleanup_old_backups() {
    log "Cleaning up old backups..."
    
    # Clean local backups
    for backup_type in daily weekly monthly; do
        local retention_days="$RETENTION_DAYS"
        
        # Different retention for different backup types
        case "$backup_type" in
            daily) retention_days=7 ;;
            weekly) retention_days=30 ;;
            monthly) retention_days=365 ;;
        esac
        
        log "Cleaning $backup_type backups older than $retention_days days"
        find "$BACKUP_DIR/$backup_type" -name "timescaledb_backup_*" -mtime +$retention_days -delete
    done
    
    log "Cleanup completed"
}

# Verify backup integrity
verify_backup() {
    local backup_file="$1"
    
    log "Verifying backup integrity..."
    
    if [[ "$backup_file" == *.tar.gz ]]; then
        # Verify compressed backup
        if tar -tzf "$backup_file" > /dev/null 2>&1; then
            log "Backup archive integrity verified"
            return 0
        else
            error "Backup archive is corrupted"
            return 1
        fi
    else
        # Verify PostgreSQL custom format backup
        if pg_restore --list "$backup_file" > /dev/null 2>&1; then
            log "PostgreSQL backup integrity verified"
            return 0
        else
            error "PostgreSQL backup is corrupted"
            return 1
        fi
    fi
}

# Send notification
send_notification() {
    local status="$1"
    local message="$2"
    
    if [[ -n "$NOTIFICATION_EMAIL" ]]; then
        local subject="TimescaleDB Backup $status - $(date)"
        echo "$message" | mail -s "$subject" "$NOTIFICATION_EMAIL" 2>/dev/null || true
    fi
    
    # Log to syslog
    logger -t "timescaledb-backup" "$status: $message"
}

# Generate backup report
generate_report() {
    local backup_file="$1"
    local backup_type="$2"
    local start_time="$3"
    local end_time="$4"
    
    local duration=$((end_time - start_time))
    local backup_size=$(du -sh "$backup_file" 2>/dev/null | cut -f1 || echo "Unknown")
    
    cat << EOF
TimescaleDB Backup Report
=========================
Backup Name: $BACKUP_NAME
Backup Type: $backup_type
Database: $POSTGRES_DB
Database Size: $DB_SIZE
Table Count: $TABLE_COUNT
Hypertable Count: $HYPERTABLE_COUNT
Start Time: $(date -d "@$start_time" '+%Y-%m-%d %H:%M:%S')
End Time: $(date -d "@$end_time" '+%Y-%m-%d %H:%M:%S')
Duration: ${duration}s
Backup Size: $backup_size
Backup File: $backup_file
Status: SUCCESS
EOF
}

# Main execution
main() {
    local backup_type="${1:-daily}"
    local start_time=$(date +%s)
    
    log "Starting TimescaleDB backup process..."
    log "Backup type: $backup_type"
    
    # Create directories
    create_directories
    
    # Check prerequisites
    check_prerequisites
    
    # Get database statistics
    get_db_stats
    
    # Perform backup
    if perform_backup "$backup_type"; then
        local backup_file="$BACKUP_DIR/$backup_type/$BACKUP_NAME"
        [[ "$COMPRESSION" == "gzip" ]] && backup_file="${backup_file}.tar.gz" || backup_file="${backup_file}.sql"
        
        # Verify backup
        if verify_backup "$backup_file"; then
            local end_time=$(date +%s)
            
            # Generate report
            local report=$(generate_report "$backup_file" "$backup_type" "$start_time" "$end_time")
            log "$report"
            
            # Clean old backups
            cleanup_old_backups
            
            # Send success notification
            send_notification "SUCCESS" "$report"
            
            log "TimescaleDB backup completed successfully"
            exit 0
        else
            error "Backup verification failed"
            send_notification "FAILED" "Backup verification failed for $BACKUP_NAME"
            exit 1
        fi
    else
        error "Backup process failed"
        send_notification "FAILED" "Backup process failed for $BACKUP_NAME"
        exit 1
    fi
}

# Handle script arguments
case "${1:-daily}" in
    daily|weekly|monthly)
        main "$1"
        ;;
    --help|-h)
        echo "Usage: $0 [daily|weekly|monthly]"
        echo "Performs TimescaleDB backup with specified retention policy"
        exit 0
        ;;
    *)
        error "Invalid backup type: $1"
        echo "Usage: $0 [daily|weekly|monthly]"
        exit 1
        ;;
esac
