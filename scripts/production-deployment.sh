#!/bin/bash

# Free Mobile Chatbot ML Intelligence Dashboard - Production Deployment Script
# Configures MongoDB and deploys to Vercel with all enhanced features

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="freemobile-chatbot-ml-dashboard"
VERCEL_PROJECT_URL="https://vercel.com/anderson-archimedes-projects/chatbotrncp"
MONGODB_URI="mongodb+srv://Anderson-Archimed01:<EMAIL>/?retryWrites=true&w=majority&appName=ChatbotRNCP"
DATABASE_NAME="freemobile_chatbot_prod"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_banner() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║         Free Mobile Chatbot ML Dashboard Deployment         ║"
    echo "║                    Production Setup                         ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

check_dependencies() {
    log_info "Checking dependencies..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed"
        exit 1
    fi
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        log_error "npm is not installed"
        exit 1
    fi
    
    # Check Vercel CLI
    if ! command -v vercel &> /dev/null; then
        log_warning "Vercel CLI not found, installing..."
        npm install -g vercel
    fi
    
    log_success "All dependencies are available"
}

setup_mongodb() {
    log_info "Setting up MongoDB database..."
    
    # Install MongoDB dependencies
    npm install mongodb
    
    # Run database setup script
    log_info "Initializing database schema..."
    node config/mongodb-setup.js
    
    if [ $? -eq 0 ]; then
        log_success "MongoDB database setup completed"
    else
        log_error "MongoDB setup failed"
        exit 1
    fi
}

configure_environment() {
    log_info "Configuring environment variables..."
    
    # Create .env.local for local testing
    if [ ! -f ".env.local" ]; then
        cp .env.example .env.local
        log_info "Created .env.local from template"
        log_warning "Please update .env.local with your actual values"
    fi
    
    # Set Vercel environment variables
    log_info "Setting Vercel environment variables..."
    
    # Production environment variables
    vercel env add MONGODB_URI production <<< "$MONGODB_URI"
    vercel env add DATABASE_NAME production <<< "$DATABASE_NAME"
    vercel env add NODE_ENV production <<< "production"
    vercel env add NEXT_PUBLIC_APP_ENV production <<< "production"
    
    # Generate secure secrets
    JWT_SECRET=$(openssl rand -base64 32)
    SESSION_SECRET=$(openssl rand -base64 32)
    REFRESH_TOKEN_SECRET=$(openssl rand -base64 32)
    
    vercel env add JWT_SECRET production <<< "$JWT_SECRET"
    vercel env add SESSION_SECRET production <<< "$SESSION_SECRET"
    vercel env add REFRESH_TOKEN_SECRET production <<< "$REFRESH_TOKEN_SECRET"
    
    # Feature flags
    vercel env add ENABLE_SIMULATION_TRAINING production <<< "true"
    vercel env add ENABLE_PREDICTIVE_ANALYTICS production <<< "true"
    vercel env add ENABLE_AI_ASSISTANCE production <<< "true"
    vercel env add ENABLE_COMPREHENSIVE_ANALYTICS production <<< "true"
    
    # Application URLs
    vercel env add NEXT_PUBLIC_APP_URL production <<< "https://chatbotrncp.vercel.app"
    vercel env add NEXT_PUBLIC_API_URL production <<< "https://chatbotrncp.vercel.app/api"
    
    # Security headers
    vercel env add ALLOWED_ORIGINS production <<< "https://chatbotrncp.vercel.app,https://freemobile.fr"
    
    # Rate limiting
    vercel env add RATE_LIMIT_WINDOW_MS production <<< "900000"
    vercel env add RATE_LIMIT_MAX_REQUESTS production <<< "100"
    
    # Business configuration
    vercel env add COMPANY_NAME production <<< "Free Mobile"
    vercel env add COMPANY_DOMAIN production <<< "freemobile.fr"
    vercel env add DEFAULT_LANGUAGE production <<< "fr"
    vercel env add TIMEZONE production <<< "Europe/Paris"
    
    # Compliance
    vercel env add GDPR_COMPLIANCE production <<< "true"
    vercel env add DATA_RETENTION_DAYS production <<< "365"
    
    log_success "Environment variables configured"
}

build_and_test() {
    log_info "Building and testing application..."
    
    # Install dependencies
    npm ci
    
    # Run type checking
    if [ -f "tsconfig.json" ]; then
        npx tsc --noEmit
    fi
    
    # Run linting
    if [ -f ".eslintrc.js" ] || [ -f ".eslintrc.json" ]; then
        npm run lint
    fi
    
    # Build application
    npm run build
    
    if [ $? -eq 0 ]; then
        log_success "Build completed successfully"
    else
        log_error "Build failed"
        exit 1
    fi
}

deploy_to_vercel() {
    log_info "Deploying to Vercel..."
    
    # Login to Vercel (if not already logged in)
    vercel whoami || vercel login
    
    # Link to existing project
    vercel link --yes
    
    # Deploy to production
    vercel --prod
    
    if [ $? -eq 0 ]; then
        log_success "Deployment completed successfully"
        log_info "Application URL: https://chatbotrncp.vercel.app"
    else
        log_error "Deployment failed"
        exit 1
    fi
}

validate_deployment() {
    log_info "Validating deployment..."
    
    # Wait for deployment to be ready
    sleep 30
    
    # Test health endpoint
    HEALTH_URL="https://chatbotrncp.vercel.app/api/system/health"
    
    log_info "Testing health endpoint: $HEALTH_URL"
    
    HEALTH_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "$HEALTH_URL")
    
    if [ "$HEALTH_RESPONSE" = "200" ]; then
        log_success "Health check passed"
    else
        log_error "Health check failed (HTTP $HEALTH_RESPONSE)"
        exit 1
    fi
    
    # Test database connectivity
    log_info "Testing database connectivity..."
    
    HEALTH_DATA=$(curl -s "$HEALTH_URL")
    DB_STATUS=$(echo "$HEALTH_DATA" | grep -o '"status":"[^"]*"' | head -1 | cut -d'"' -f4)
    
    if [ "$DB_STATUS" = "healthy" ]; then
        log_success "Database connectivity verified"
    else
        log_error "Database connectivity failed"
        exit 1
    fi
    
    # Test enhanced features
    log_info "Testing enhanced features..."
    
    FEATURES_TO_TEST=(
        "/api/simulation/scenarios"
        "/api/predictive/churn"
        "/api/enhanced-ai/suggestions"
        "/api/analytics/dashboard"
    )
    
    for endpoint in "${FEATURES_TO_TEST[@]}"; do
        FEATURE_URL="https://chatbotrncp.vercel.app$endpoint"
        FEATURE_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "$FEATURE_URL")
        
        if [ "$FEATURE_RESPONSE" = "200" ] || [ "$FEATURE_RESPONSE" = "401" ]; then
            log_success "Feature endpoint $endpoint is accessible"
        else
            log_warning "Feature endpoint $endpoint returned HTTP $FEATURE_RESPONSE"
        fi
    done
}

print_deployment_summary() {
    echo -e "${GREEN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    DEPLOYMENT SUCCESSFUL                    ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    
    echo -e "${BLUE}🚀 Application URL:${NC} https://chatbotrncp.vercel.app"
    echo -e "${BLUE}📊 Health Check:${NC} https://chatbotrncp.vercel.app/api/system/health"
    echo -e "${BLUE}🎮 Simulation Training:${NC} ✅ Enabled"
    echo -e "${BLUE}🧠 Predictive Analytics:${NC} ✅ Enabled"
    echo -e "${BLUE}💡 AI Assistance:${NC} ✅ Enabled"
    echo -e "${BLUE}📈 Comprehensive Analytics:${NC} ✅ Enabled"
    echo ""
    echo -e "${YELLOW}📋 Next Steps:${NC}"
    echo "1. Configure monitoring alerts"
    echo "2. Set up regular database backups"
    echo "3. Configure SSL certificates (if needed)"
    echo "4. Set up user authentication"
    echo "5. Import initial data and scenarios"
    echo ""
    echo -e "${GREEN}✅ Free Mobile Chatbot ML Intelligence Dashboard is ready for production!${NC}"
}

# Main execution
main() {
    print_banner
    
    log_info "Starting production deployment..."
    
    check_dependencies
    setup_mongodb
    configure_environment
    build_and_test
    deploy_to_vercel
    validate_deployment
    
    print_deployment_summary
    
    log_success "Production deployment completed successfully!"
}

# Run main function
main "$@"
