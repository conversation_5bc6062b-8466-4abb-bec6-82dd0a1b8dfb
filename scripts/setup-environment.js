#!/usr/bin/env node /** * ============================================= * [SECURITY] SECURE ENVIRONMENT SETUP SCRIPT * Free Mobile Chatbot RNCP - Environment Configuration * ============================================= */ const fs = require('fs'); const path = require('path'); const crypto = require('crypto'); const readline = require('readline'); const rl = readline.createInterface({ input: process.stdin, output: process.stdout }); console.log('[SECURITY] Free Mobile Chatbot RNCP - Secure Environment Setup'); console.log('====================================================='); console.log(''); // Generate secure random strings function generateSecureKey(length = 32) { return crypto.randomBytes(length).toString('hex'); } function generateJWTSecret() { return crypto.randomBytes(64).toString('base64'); } // Prompt user for input function askQuestion(question) { return new Promise((resolve) => { rl.question(question, (answer) => { resolve(answer); }); }); } // Mask sensitive input function askSecretQuestion(question) { return new Promise((resolve) => { process.stdout.write(question); process.stdin.setRawMode(true); process.stdin.resume(); process.stdin.setEncoding('utf8'); let input = ''; process.stdin.on('data', (char) => { char = char.toString(); if (char === '\n' || char === '\r' || char === '\u0004') { process.stdin.setRawMode(false); process.stdin.pause(); process.stdout.write('\n'); resolve(input); } else if (char === '\u0003') { process.exit(); } else if (char === '\u007f') { // Backspace if (input.length > 0) { input = input.slice(0, -1); process.stdout.write('\b \b'); } } else { input += char; process.stdout.write('*'); } }); }); } // Validate MongoDB URI function validateMongoURI(uri) { const mongoPattern = /^mongodb(\+srv)?:\/\/.+/; return mongoPattern.test(uri); } // Validate Redis URL function validateRedisURL(url) { const redisPattern = /^redis(s)?:\/\/.*/; return redisPattern.test(url) || url === 'redis://localhost:6379'; } // Main setup function async function setupEnvironment() { try { console.log(' This script will help you configure your environment variables securely.'); console.log(' Sensitive information will be masked during input.'); console.log(''); // Environment selection const environment = await askQuestion(' Select environment (development/staging/production): '); console.log(''); // Database configuration console.log(' MongoDB Configuration:'); console.log(' Default cluster: chatbotrncp.za6xmim.mongodb.net'); console.log(' Default user: Anderson-Archimed01'); const useDefaultMongo = await askQuestion(' Use default MongoDB cluster? (y/n): '); let mongoURI; if (useDefaultMongo.toLowerCase() === 'y') { const dbPassword = await askSecretQuestion(' Enter MongoDB password: '); const dbName = environment === 'production' ? 'chatbotrncp' : `chatbotrncp_${environment}`; mongoURI = `mongodb+srv://Anderson-Archimed01:${dbPassword}@chatbotrncp.za6xmim.mongodb.net/${dbName}?retryWrites=true&w=majority&appName=ChatbotRNCP`; } else { mongoURI = await askQuestion(' Enter complete MongoDB URI: '); } if (!validateMongoURI(mongoURI)) { console.log('[FAILED] Invalid MongoDB URI format'); process.exit(1); } console.log(' [COMPLETE] MongoDB URI configured'); console.log(''); // Redis configuration console.log(' Redis Configuration:'); const useLocalRedis = await askQuestion(' Use local Redis (redis://localhost:6379)? (y/n): '); let redisURL; if (useLocalRedis.toLowerCase() === 'y') { redisURL = 'redis://localhost:6379'; } else { redisURL = await askQuestion(' Enter Redis URL: '); } if (!validateRedisURL(redisURL)) { console.log('[FAILED] Invalid Redis URL format'); process.exit(1); } console.log(' [COMPLETE] Redis URL configured'); console.log(''); // JWT Configuration console.log('[SECURITY] JWT Configuration:'); const generateJWT = await askQuestion(' Generate secure JWT secret automatically? (y/n): '); let jwtSecret; if (generateJWT.toLowerCase() === 'y') { jwtSecret = generateJWTSecret(); console.log(' [COMPLETE] Secure JWT secret generated'); } else { jwtSecret = await askSecretQuestion(' Enter JWT secret (minimum 32 characters): '); if (jwtSecret.length < 32) { console.log('[FAILED] JWT secret must be at least 32 characters'); process.exit(1); } } console.log(''); // CI/CD Configuration console.log(' CI/CD Configuration:'); const setupCICD = await askQuestion(' Configure CI/CD tokens? (y/n): '); let circleciToken = ''; let githubToken = ''; if (setupCICD.toLowerCase() === 'y') { circleciToken = await askSecretQuestion(' Enter CircleCI token: '); githubToken = await askSecretQuestion(' Enter GitHub token: '); console.log(' [COMPLETE] CI/CD tokens configured'); } console.log(''); // Additional configuration console.log(' Additional Configuration:'); const port = await askQuestion(' API port (default: 5000): ') || '5000'; const frontendURL = await askQuestion(' Frontend URL (default: http://localhost:3000): ') || 'http://localhost:3000'; console.log(''); // Generate environment file content const envContent = `# ============================================= # [SECURITY] FREE MOBILE CHATBOT RNCP - ENVIRONMENT CONFIGURATION # Generated on: ${new Date().toISOString()} # Environment: ${environment.toUpperCase()} # ============================================= # Application Configuration NODE_ENV=${environment} PORT=${port} FRONTEND_URL=${frontendURL} # Database Configuration MONGODB_URI=${mongoURI} DB_NAME=${environment === 'production' ? 'chatbotrncp' : `chatbotrncp_${environment}`} # Redis Configuration REDIS_URL=${redisURL} # JWT Configuration JWT_SECRET=${jwtSecret} JWT_EXPIRES_IN=7d JWT_REFRESH_EXPIRES_IN=30d # CI/CD Configuration ${circleciToken ? `CIRCLECI_TOKEN=${circleciToken}` : '# CIRCLECI_TOKEN=your_circleci_token_here'} ${githubToken ? `GITHUB_TOKEN=${githubToken}` : '# GITHUB_TOKEN=your_github_token_here'} # Security Configuration CORS_ORIGIN=${frontendURL} CORS_CREDENTIALS=true SESSION_SECRET=${generateSecureKey(32)} # Rate Limiting RATE_LIMIT_WINDOW_MS=900000 RATE_LIMIT_MAX_REQUESTS=100 # Logging Configuration LOG_LEVEL=${environment === 'production' ? 'warn' : 'info'} LOG_FORMAT=combined # Feature Flags FEATURE_AI_SUGGESTIONS=true FEATURE_PREDICTIVE_ANALYTICS=true FEATURE_AGENT_TRAINING=true FEATURE_EMERGENCY_CALLS=true # Performance Configuration API_RESPONSE_TIME_TARGET=2000 ML_PROCESSING_TIME_TARGET=3000 UPTIME_TARGET=99.97 # Free Mobile Configuration FREE_MOBILE_SUBSCRIBER_COUNT=13000000 FREE_MOBILE_SUPPORT_HOURS=24/7 FREE_MOBILE_SLA_TARGET=99.97 # ============================================= # SECURITY NOTICE # This file contains sensitive information. # Never commit this file to version control. # Ensure file permissions are set to 600. # ============================================= `; // Write environment file const envFilePath = path.join(process.cwd(), `.env.${environment}`); fs.writeFileSync(envFilePath, envContent, { mode: 0o600 }); // Create symlink for .env if it's development if (environment === 'development') { const mainEnvPath = path.join(process.cwd(), '.env'); if (fs.existsSync(mainEnvPath)) { fs.unlinkSync(mainEnvPath); } fs.symlinkSync(`.env.${environment}`, '.env'); } console.log('[COMPLETE] Environment Configuration Complete!'); console.log('====================================='); console.log(` Environment file created: .env.${environment}`); console.log(` File permissions set to 600 (owner read/write only)`); if (environment === 'development') { console.log(' Symlink created: .env -> .env.development'); } console.log(''); console.log('[SEARCH] Next Steps:'); console.log('1. Verify all configuration values are correct'); console.log('2. Test database and Redis connections'); console.log('3. Start your application'); console.log('4. Monitor logs for any configuration issues'); console.log(''); console.log(' Security Reminders:'); console.log('- Never commit .env files to version control'); console.log('- Regularly rotate sensitive credentials'); console.log('- Use different credentials for each environment'); console.log('- Monitor access to sensitive configuration'); console.log(''); } catch (error) { console.error('[FAILED] Setup failed:', error.message); process.exit(1); } finally { rl.close(); } } // Validation function async function validateConfiguration() { console.log('[SEARCH] Validating Configuration...'); try { // Test MongoDB connection const { MongoClient } = require('mongodb'); const mongoURI = process.env.MONGODB_URI; if (mongoURI) { const client = new MongoClient(mongoURI); await client.connect(); await client.close(); console.log('[COMPLETE] MongoDB connection successful'); } // Test Redis connection const redis = require('redis'); const redisURL = process.env.REDIS_URL; if (redisURL) { const client = redis.createClient({ url: redisURL }); await client.connect(); await client.ping(); await client.quit(); console.log('[COMPLETE] Redis connection successful'); } console.log('[COMPLETE] All configurations validated successfully'); } catch (error) { console.error('[FAILED] Configuration validation failed:', error.message); console.log(''); console.log('[CONFIG] Troubleshooting:'); console.log('1. Verify your credentials are correct'); console.log('2. Check network connectivity'); console.log('3. Ensure services are running'); console.log('4. Review the CREDENTIAL_MANAGEMENT_GUIDE.md'); } } // Main execution if (require.main === module) { const command = process.argv[2]; if (command === 'validate') { require('dotenv').config(); validateConfiguration(); } else { setupEnvironment(); } } module.exports = { setupEnvironment, validateConfiguration, generateSecureKey, generateJWTSecret };