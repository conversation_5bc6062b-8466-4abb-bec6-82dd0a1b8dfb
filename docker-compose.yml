# Docker Compose - Environnement de Développement # Configuration optimisée pour le développement avec hot reload services: # Base de données MongoDB mongodb: image: mongo:6.0 container_name: freemobile-mongodb-dev restart: unless-stopped environment: MONGO_INITDB_ROOT_USERNAME: ${MONGO_USERNAME:-freemobile} MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD:-freemobile123} MONGO_INITDB_DATABASE: ${MONGO_DATABASE:-freemobile_chatbot} ports: - "27017:27017" volumes: - mongodb_data:/data/db - ./mongo-init-dev.js:/docker-entrypoint-initdb.d/mongo-init.js:ro networks: - chatbot-network healthcheck: test: ["CMD-SHELL", "mongosh --quiet -u $MONGO_INITDB_ROOT_USERNAME -p $MONGO_INITDB_ROOT_PASSWORD --authenticationDatabase admin --eval 'db.adminCommand({ ping: 1 })' || mongo --quiet -u $MONGO_INITDB_ROOT_USERNAME -p $MONGO_INITDB_ROOT_PASSWORD --authenticationDatabase admin --eval 'db.adminCommand({ ping: 1 })'"] interval: 30s timeout: 10s retries: 5 start_period: 60s # Cache Redis redis: image: redis:7-alpine container_name: freemobile-redis-dev restart: unless-stopped ports: - "6379:6379" volumes: - redis_data:/data networks: - chatbot-network healthcheck: test: ["CMD", "redis-cli", "ping"] interval: 30s timeout: 10s retries: 3 command: redis-server --appendonly yes # [AI] Service ML de classification intelligente ml-service: build: context: ./ml-service dockerfile: Dockerfile target: development container_name: freemobile-ml-service-dev restart: unless-stopped ports: - "5001:5001" # API ML - "8000:8000" # Métriques Prometheus environment: - DEBUG=true - ML_HOST=0.0.0.0 - ML_PORT=5001 - ML_WORKERS=4 - REDIS_URL=redis://redis:6379 - MONGODB_URL=mongodb://mongodb:27017/free-mobile-chatbot - DATABASE_URL=***********************************************/analytics - MODEL_PATH=/models - CACHE_TTL_SECONDS=3600 - MAX_CONCURRENT_REQUESTS=100 - ENABLE_CACHING=true - ENABLE_METRICS=true - LOG_LEVEL=INFO - JWT_SECRET=your-super-secure-jwt-secret-key-for-ml-service volumes: - ./ml-service:/app - ml_models:/models - ml_logs:/logs networks: - chatbot-network depends_on: - redis - mongodb - timescaledb healthcheck: test: ["CMD", "curl", "-f", "http://localhost:5001/health"] interval: 30s timeout: 10s retries: 3 start_period: 60s # [ANALYTICS] TimescaleDB pour analytics et métriques ML timescaledb: image: timescale/timescaledb:latest-pg14 container_name: freemobile-timescaledb-dev restart: unless-stopped ports: - "5432:5432" environment: - POSTGRES_DB=analytics - POSTGRES_USER=postgres - POSTGRES_PASSWORD=password - TIMESCALEDB_TELEMETRY=off volumes: - timescale_data:/var/lib/postgresql/data networks: - chatbot-network healthcheck: test: ["CMD-SHELL", "pg_isready -U postgres -d analytics"] interval: 30s timeout: 10s retries: 3 start_period: 40s # Call Service - Bidirectional Call System with Twilio & WebRTC call-service: build: context: ./call-service dockerfile: Dockerfile container_name: freemobile-call-service-dev restart: unless-stopped ports: - "5004:5004" environment: - NODE_ENV=development - PORT=5004 - HOST=0.0.0.0 - MONGODB_URI=mongodb://mongodb:27017/free-mobile-chatbot - REDIS_URL=redis://redis:6379 - TWILIO_ACCOUNT_SID=${TWILIO_ACCOUNT_SID:-your_twilio_account_sid} - TWILIO_AUTH_TOKEN=${TWILIO_AUTH_TOKEN:-your_twilio_auth_token} - TWILIO_PHONE_NUMBER=${TWILIO_PHONE_NUMBER:-+***********} - TWILIO_WEBHOOK_URL=${TWILIO_WEBHOOK_URL:-http://localhost:5004/api/webhooks} - FRONTEND_URL=http://localhost:3001 - BACKEND_URL=http://localhost:5000 - JWT_SECRET=${JWT_SECRET:-your-super-secure-jwt-secret-key-for-call-service} - TURN_SERVER_URL=${TURN_SERVER_URL:-} - TURN_USERNAME=${TURN_USERNAME:-} - TURN_PASSWORD=${TURN_PASSWORD:-} - LOG_LEVEL=INFO - MAX_CONCURRENT_CALLS=100 - QUEUE_PROCESSING_INTERVAL=5000 volumes: - ./call-service:/app - call_logs:/logs networks: - chatbot-network depends_on: - redis - mongodb healthcheck: test: ["CMD", "curl", "-f", "http://localhost:5004/health"] interval: 30s timeout: 10s retries: 3 start_period: 60s # [DEPLOY] Backend API Node.js backend: build: context: ./backend dockerfile: Dockerfile target: dev-dependencies container_name: freemobile-backend-dev restart: unless-stopped environment: NODE_ENV: development PORT: 5000 MONGODB_URI: mongodb://${MONGO_USERNAME:-freemobile}:${MONGO_PASSWORD:-freemobile123}@mongodb:27017/${MONGO_DATABASE:-freemobile_chatbot}?authSource=admin REDIS_URL: redis://redis:6379 REDIS_HOST: redis REDIS_PORT: 6379 JWT_SECRET: ${JWT_SECRET:-dev-secret-key-not-for-production} OPENAI_API_KEY: ${OPENAI_API_KEY} RASA_URL: http://rasa:5005 FRONTEND_URL: http://localhost:3000 LOG_LEVEL: debug ports: - "5000:5000" volumes: - ./backend:/app - /app/node_modules - ./logs:/app/logs networks: - chatbot-network depends_on: mongodb: condition: service_healthy redis: condition: service_healthy healthcheck: test: ["CMD", "curl", "-f", "http://localhost:5000/health"] interval: 30s timeout: 10s retries: 3 start_period: 30s command: npm run dev # [AI] Rasa NLP Engine rasa: build: context: ./rasa dockerfile: Dockerfile target: trainer container_name: freemobile-rasa-dev restart: unless-stopped environment: RASA_MODEL_PATH: /app/models RASA_CORE_DEBUG_FILE: /app/logs/rasa_core.log ports: - "5005:5005" volumes: - ./rasa:/app - ./logs:/app/logs networks: - chatbot-network depends_on: - redis healthcheck: test: ["CMD", "curl", "-f", "http://localhost:5005/status"] interval: 60s timeout: 15s retries: 3 start_period: 120s command: > bash -c " echo '[AI] Entraînement du modèle Rasa...' && rasa train --domain domain.yml --data data --config config.yml --out models --fixed-model-name chatbot-model && echo '[DEPLOY] Démarrage du serveur Rasa...' && rasa run --model models/chatbot-model.tar.gz --enable-api --cors '*' --port 5005 --log-file logs/rasa.log --debug " # Frontend React frontend: build: context: ./frontend dockerfile: Dockerfile target: dependencies container_name: freemobile-frontend-dev restart: unless-stopped environment: REACT_APP_API_URL: http://localhost:5000/api REACT_APP_SOCKET_URL: http://localhost:5000 REACT_APP_ENVIRONMENT: development CHOKIDAR_USEPOLLING: "true" WATCHPACK_POLLING: "true" ports: - "3000:3000" volumes: - ./frontend:/app - /app/node_modules networks: - chatbot-network depends_on: - backend stdin_open: true tty: true command: npm start # [ANALYTICS] Adminer - Interface de gestion MongoDB (développement) adminer: image: adminer:4.8.1 container_name: freemobile-adminer-dev restart: unless-stopped ports: - "8080:8080" networks: - chatbot-network depends_on: - mongodb # [METRICS] Redis Commander - Interface Redis (développement) redis-commander: image: rediscommander/redis-commander:latest container_name: freemobile-redis-commander-dev restart: unless-stopped environment: REDIS_HOSTS: local:redis:6379 ports: - "8081:8081" networks: - chatbot-network depends_on: - redis # Volumes persistants volumes: mongodb_data: name: freemobile_mongodb_dev_data redis_data: name: freemobile_redis_dev_data ml_models: name: freemobile_ml_models_dev_data ml_logs: name: freemobile_ml_logs_dev_data timescale_data: name: freemobile_timescale_dev_data call_logs: name: freemobile_call_logs_dev_data # Réseau networks: chatbot-network: name: freemobile_chatbot_dev_network driver: bridge