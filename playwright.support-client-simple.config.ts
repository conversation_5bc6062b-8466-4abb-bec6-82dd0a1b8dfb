import { defineConfig, devices } from '@playwright/test'; /** * Simplified Playwright Configuration for Support Client Page Testing * Frontend-focused testing without backend dependencies */ export default defineConfig({ testDir: './tests/e2e', testMatch: '**/support-client-modernized.spec.ts', /* Run tests in files in parallel */ fullyParallel: false, /* Fail the build on CI if you accidentally left test.only in the source code. */ forbidOnly: !!process.env.CI, /* Retry on CI only */ retries: process.env.CI ? 2 : 1, /* Opt out of parallel tests on CI. */ workers: 1, /* Reporter to use. See https://playwright.dev/docs/test-reporters */ reporter: [ ['html', { outputFolder: 'test-results/support-client-report' }], ['list'], ['json', { outputFile: 'test-results/support-client-results.json' }] ], /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */ use: { /* Base URL to use in actions like `await page.goto('/')`. */ baseURL: 'http://localhost:3001', /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */ trace: 'on-first-retry', /* Take screenshot on failure */ screenshot: 'only-on-failure', /* Record video on failure */ video: 'retain-on-failure', /* Global timeout for each action */ actionTimeout: 15000, /* Global timeout for navigation */ navigationTimeout: 30000, /* Ignore HTTPS errors */ ignoreHTTPSErrors: true, }, /* Configure projects for major browsers */ projects: [ { name: 'chromium-desktop', use: { ...devices['Desktop Chrome'], viewport: { width: 1920, height: 1080 } }, }, { name: 'mobile-chrome', use: { ...devices['Pixel 5'] }, }, ], /* NO global setup - test frontend only */ // globalSetup: undefined, // globalTeardown: undefined, /* Folder for test artifacts such as screenshots, videos, traces, etc. */ outputDir: 'test-results/support-client-artifacts', /* Configure test timeout */ timeout: 60000, expect: { timeout: 10000 }, /* Don't start web server - assume it's already running */ // webServer: undefined, });