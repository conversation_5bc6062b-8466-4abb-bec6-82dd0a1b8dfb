{"users": {"admin": {"email": "<EMAIL>", "password": "AdminTest123!", "role": "admin", "firstName": "Test", "lastName": "Admin", "permissions": ["user_management", "system_settings", "analytics_view", "admin_panel"]}, "agent": {"email": "<EMAIL>", "password": "AgentTest123!", "role": "agent", "firstName": "Test", "lastName": "Agent", "permissions": ["ticket_management", "chat_support", "customer_view"]}, "user": {"email": "<EMAIL>", "password": "UserTest123!", "role": "user", "firstName": "Test", "lastName": "User", "permissions": ["chat_access", "profile_edit"]}}, "testMessages": ["Hello, I need help with my account", "Can you help me with billing?", "I want to change my plan", "Technical support needed", "How do I reset my password?", "What are your business hours?", "I'm having trouble with my service", "Can you explain the pricing?", "I need to update my information", "How do I cancel my subscription?"], "testScenarios": {"login": {"validCredentials": {"email": "<EMAIL>", "password": "UserTest123!"}, "invalidEmail": {"email": "<EMAIL>", "password": "password"}, "invalidPassword": {"email": "<EMAIL>", "password": "wrongpassword"}, "emptyFields": {"email": "", "password": ""}, "invalidEmailFormat": {"email": "invalid-email", "password": "password123"}}, "registration": {"validData": {"firstName": "New", "lastName": "User", "email": "<EMAIL>", "password": "NewUser123!", "confirmPassword": "NewUser123!"}, "invalidData": {"firstName": "", "lastName": "", "email": "invalid-email", "password": "weak", "confirmPassword": "different"}}, "chat": {"commonQueries": ["Hello", "Help me", "What can you do?", "I have a problem", "Thank you"], "longMessage": "This is a very long message that tests how the chat interface handles lengthy user inputs. It should be properly displayed and not break the layout or functionality of the chat system. The message continues to test various aspects of the chat interface including text wrapping, scrolling, and overall user experience.", "specialCharacters": "Test message with special characters: !@#$%^&*()_+-=[]{}|;':\",./<>?", "emojis": "Test message with emojis: [DEPLOY] ⭐", "multiline": "This is line 1\nThis is line 2\nThis is line 3"}}, "testPages": {"public": ["/login", "/register", "/forgot-password"], "protected": ["/dashboard", "/chat", "/profile", "/settings"], "admin": ["/admin", "/admin/users", "/admin/settings", "/admin/analytics"], "agent": ["/agent", "/tickets", "/agent/dashboard"]}, "apiEndpoints": {"auth": {"login": "/api/auth/login", "logout": "/api/auth/logout", "register": "/api/auth/register", "refresh": "/api/auth/refresh"}, "chat": {"send": "/api/chat/send", "history": "/api/chat/history", "status": "/api/chat/status"}, "user": {"profile": "/api/user/profile", "update": "/api/user/update", "preferences": "/api/user/preferences"}, "admin": {"users": "/api/admin/users", "settings": "/api/admin/settings", "analytics": "/api/admin/analytics"}}, "errorMessages": {"auth": {"invalidCredentials": "Invalid email or password", "emailRequired": "Email is required", "passwordRequired": "Password is required", "invalidEmailFormat": "Please enter a valid email address"}, "chat": {"messageEmpty": "Message cannot be empty", "messageTooLong": "Message is too long", "connectionError": "Connection error. Please try again.", "sendFailed": "Failed to send message"}, "general": {"networkError": "Network error. Please check your connection.", "serverError": "Server error. Please try again later.", "accessDenied": "Access denied. You don't have permission to view this page.", "pageNotFound": "Page not found"}}, "testEnvironment": {"baseUrl": "http://localhost:3001", "apiUrl": "http://localhost:5000", "testDatabase": "mongodb://localhost:27017/chatbot-test", "timeouts": {"pageLoad": 10000, "apiResponse": 5000, "elementVisible": 5000, "userInteraction": 3000}}, "accessibility": {"requiredAttributes": ["alt", "aria-label", "aria-<PERSON><PERSON>", "role"], "colorContrast": {"minimumRatio": 4.5, "largeTextRatio": 3.0}, "keyboardNavigation": ["Tab", "Shift+Tab", "Enter", "Space", "Escape"]}, "performance": {"thresholds": {"pageLoadTime": 5000, "apiResponseTime": 2000, "interactionResponseTime": 1000, "mobileLoadTime": 8000}, "metrics": ["First Contentful Paint", "Largest Contentful Paint", "Cumulative Layout Shift", "First Input Delay"]}, "responsive": {"breakpoints": {"mobile": 768, "tablet": 1024, "desktop": 1200}, "viewports": [{"width": 320, "height": 568, "name": "iPhone SE"}, {"width": 375, "height": 667, "name": "iPhone 8"}, {"width": 414, "height": 896, "name": "iPhone 11"}, {"width": 768, "height": 1024, "name": "iPad"}, {"width": 1024, "height": 768, "name": "iPad Landscape"}, {"width": 1366, "height": 768, "name": "Desktop"}, {"width": 1920, "height": 1080, "name": "Desktop Large"}]}}