/** * ============================================= * TEST FIXTURES * Reusable test fixtures for authentication and API contexts * ============================================= */ import { test as base, expect, Page, APIRequestContext } from '@playwright/test'; import { testConfig } from '../playwright.config'; // Define fixture types export interface TestFixtures { authenticatedPage: Page; adminPage: Page; agentPage: Page; customerPage: Page; apiContext: APIRequestContext; mlContext: APIRequestContext; socialMediaContext: APIRequestContext; testData: TestDataFixture; } export interface TestDataFixture { conversations: any[]; agents: any[]; customers: any[]; messages: any[]; } // Extend base test with custom fixtures export const test = base.extend<TestFixtures>({ // Authenticated page fixture authenticatedPage: async ({ browser }, use) => { const context = await browser.newContext(); const page = await context.newPage(); // Login as default agent await loginUser(page, testConfig.users.agent); await use(page); await context.close(); }, // Admin page fixture adminPage: async ({ browser }, use) => { const context = await browser.newContext(); const page = await context.newPage(); await loginUser(page, testConfig.users.admin); await use(page); await context.close(); }, // Agent page fixture agentPage: async ({ browser }, use) => { const context = await browser.newContext(); const page = await context.newPage(); await loginUser(page, testConfig.users.agent); await use(page); await context.close(); }, // Customer page fixture customerPage: async ({ browser }, use) => { const context = await browser.newContext(); const page = await context.newPage(); await loginUser(page, testConfig.users.customer); await use(page); await context.close(); }, // API context fixture apiContext: async ({ playwright }, use) => { const context = await playwright.request.newContext({ baseURL: testConfig.api.baseURL, extraHTTPHeaders: { 'Content-Type': 'application/json', 'Accept': 'application/json' } }); // Authenticate API context const loginResponse = await context.post('/api/auth/login', { data: { email: testConfig.users.agent.email, password: testConfig.users.agent.password } }); const loginData = await loginResponse.json(); const token = loginData.token; // Add authorization header await context.dispose(); const authenticatedContext = await playwright.request.newContext({ baseURL: testConfig.api.baseURL, extraHTTPHeaders: { 'Content-Type': 'application/json', 'Accept': 'application/json', 'Authorization': `Bearer ${token}` } }); await use(authenticatedContext); await authenticatedContext.dispose(); }, // ML service context fixture mlContext: async ({ playwright }, use) => { const context = await playwright.request.newContext({ baseURL: testConfig.ml.baseURL, extraHTTPHeaders: { 'Content-Type': 'application/json', 'Accept': 'application/json' }, timeout: testConfig.ml.timeout }); await use(context); await context.dispose(); }, // Social Media service context fixture socialMediaContext: async ({ playwright }, use) => { const context = await playwright.request.newContext({ baseURL: 'http://localhost:5010', extraHTTPHeaders: { 'Content-Type': 'application/json', 'Accept': 'application/json' } }); await use(context); await context.dispose(); }, // Test data fixture testData: async ({}, use) => { const testData: TestDataFixture = { conversations: [ { id: 'test-conv-1', customerId: 'test-customer-1', platform: 'whatsapp', status: 'open', urgency: 'medium', sentiment: 'neutral' }, { id: 'test-conv-2', customerId: 'test-customer-2', platform: 'facebook', status: 'assigned', urgency: 'high', sentiment: 'negative', assignedAgent: 'test-agent-1' } ], agents: [ { id: 'test-agent-1', name: 'Agent Test 1', email: '<EMAIL>', skills: ['technical_support', 'billing_support'], status: 'available' }, { id: 'test-agent-2', name: 'Agent Test 2', email: '<EMAIL>', skills: ['sales_support', 'customer_service'], status: 'busy' } ], customers: [ { id: 'test-customer-1', name: 'Customer Test 1', email: '<EMAIL>', segment: 'standard' }, { id: 'test-customer-2', name: 'Customer VIP Test', email: '<EMAIL>', segment: 'vip' } ], messages: [ { conversationId: 'test-conv-1', text: 'Bonjour, j\'ai un problème avec mon forfait', direction: 'inbound', platform: 'whatsapp' }, { conversationId: 'test-conv-2', text: 'Je suis très mécontent du service', direction: 'inbound', platform: 'facebook', analysis: { sentiment: { label: 'negative', score: 0.2, confidence: 0.9 }, intent: { primary: 'complaint', confidence: 0.8 } } } ] }; await use(testData); } }); /** * Login user helper function */ async function loginUser(page: Page, user: any) { await page.goto('/login'); // Fill login form await page.fill('[data-testid="email-input"]', user.email); await page.fill('[data-testid="password-input"]', user.password); // Submit form await page.click('[data-testid="login-button"]'); // Wait for successful login await page.waitForURL('/dashboard'); await expect(page.locator('[data-testid="user-menu"]')).toBeVisible(); } /** * Test utilities */ export class TestUtils { static async waitForStableNetwork(page: Page) { await page.waitForLoadState('networkidle'); await page.waitForTimeout(1000); } static async takeScreenshot(page: Page, name: string) { await page.screenshot({ path: `test-results/screenshots/${name}-${Date.now()}.png`, fullPage: true }); } static generateTestId(): string { return `test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`; } static async waitForElement(page: Page, selector: string, timeout = 10000) { await page.waitForSelector(selector, { timeout }); } static async waitForText(page: Page, text: string, timeout = 10000) { await page.waitForFunction( (searchText) => document.body.innerText.includes(searchText), text, { timeout } ); } static async scrollToElement(page: Page, selector: string) { await page.locator(selector).scrollIntoViewIfNeeded(); } static async clearAndFill(page: Page, selector: string, text: string) { await page.fill(selector, ''); await page.fill(selector, text); } static async selectOption(page: Page, selector: string, value: string) { await page.selectOption(selector, value); } static async uploadFile(page: Page, selector: string, filePath: string) { await page.setInputFiles(selector, filePath); } static async waitForAPIResponse(page: Page, urlPattern: string | RegExp) { return page.waitForResponse(urlPattern); } static async interceptAPICall(page: Page, urlPattern: string | RegExp, mockResponse: any) { await page.route(urlPattern, route => { route.fulfill({ status: 200, contentType: 'application/json', body: JSON.stringify(mockResponse) }); }); } static async getElementText(page: Page, selector: string): Promise<string> { return await page.locator(selector).textContent() || ''; } static async getElementAttribute(page: Page, selector: string, attribute: string): Promise<string> { return await page.locator(selector).getAttribute(attribute) || ''; } static async isElementVisible(page: Page, selector: string): Promise<boolean> { return await page.locator(selector).isVisible(); } static async isElementEnabled(page: Page, selector: string): Promise<boolean> { return await page.locator(selector).isEnabled(); } static async getElementCount(page: Page, selector: string): Promise<number> { return await page.locator(selector).count(); } static async clickAndWait(page: Page, selector: string, waitFor?: string) { await page.click(selector); if (waitFor) { await page.waitForSelector(waitFor); } } static async hoverAndClick(page: Page, hoverSelector: string, clickSelector: string) { await page.hover(hoverSelector); await page.click(clickSelector); } static async dragAndDrop(page: Page, sourceSelector: string, targetSelector: string) { await page.dragAndDrop(sourceSelector, targetSelector); } static async pressKey(page: Page, key: string) { await page.keyboard.press(key); } static async typeText(page: Page, text: string, delay = 100) { await page.keyboard.type(text, { delay }); } static async getPageTitle(page: Page): Promise<string> { return await page.title(); } static async getCurrentURL(page: Page): Promise<string> { return page.url(); } static async goBack(page: Page) { await page.goBack(); } static async goForward(page: Page) { await page.goForward(); } static async reload(page: Page) { await page.reload(); } static async setViewportSize(page: Page, width: number, height: number) { await page.setViewportSize({ width, height }); } static async emulateDevice(page: Page, device: any) { await page.emulate(device); } static async setCookie(page: Page, name: string, value: string) { await page.context().addCookies([{ name, value, domain: new URL(page.url()).hostname, path: '/' }]); } static async getCookie(page: Page, name: string) { const cookies = await page.context().cookies(); return cookies.find(cookie => cookie.name === name); } static async clearCookies(page: Page) { await page.context().clearCookies(); } static async setLocalStorage(page: Page, key: string, value: string) { await page.evaluate(({ key, value }) => { localStorage.setItem(key, value); }, { key, value }); } static async getLocalStorage(page: Page, key: string): Promise<string | null> { return await page.evaluate((key) => { return localStorage.getItem(key); }, key); } static async clearLocalStorage(page: Page) { await page.evaluate(() => { localStorage.clear(); }); } } export { expect };