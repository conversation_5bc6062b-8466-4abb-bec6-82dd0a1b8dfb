/** * ============================================= * EMERGENCY CALL SYSTEM TESTS * Comprehensive testing for emergency call functionality * Tests API endpoints, WebRTC integration, and compliance * ============================================= */ const request = require('supertest'); const { expect } = require('chai'); const sinon = require('sinon'); const app = require('../backend/src/app'); const EmergencyCall = require('../backend/src/models/EmergencyCall'); const User = require('../backend/src/models/User'); const emergencyCallService = require('../backend/src/services/emergencyCallService'); describe(' Emergency Call System', () => { let authToken; let testUser; let testAgent; let emergencyCallId; before(async () => { // Create test user testUser = await User.create({ email: '<EMAIL>', password: 'TestPassword123!', profile: { firstName: 'Test', lastName: 'Customer', phone: '+33123456789' }, role: 'customer', status: 'active' }); // Create test agent testAgent = await User.create({ email: '<EMAIL>', password: 'AgentPassword123!', profile: { firstName: 'Test', lastName: 'Agent', phone: '+33987654321', skills: ['emergency_support', 'priority_support'], status: 'available' }, role: 'agent', status: 'active' }); // Get auth token const loginResponse = await request(app) .post('/api/auth/login') .send({ email: testUser.email, password: 'TestPassword123!' }); authToken = loginResponse.body.token; }); after(async () => { // Cleanup test data await EmergencyCall.deleteMany({ userId: testUser._id }); await User.deleteMany({ _id: { $in: [testUser._id, testAgent._id] } }); }); describe('Emergency Call Initiation', () => { it('should successfully initiate an emergency call', async () => { const response = await request(app) .post('/api/emergency-calls/initiate') .set('Authorization', `Bearer ${authToken}`) .send({ userId: testUser._id.toString(), urgencyLevel: 'high', description: 'Mon téléphone ne fonctionne plus depuis ce matin, je ne peux plus recevoir d\'appels', conversationHistory: [ { content: 'Bonjour, j\'ai un problème avec mon téléphone', sender: 'user', timestamp: new Date() }, { content: 'Je peux vous aider avec votre problème', sender: 'bot', timestamp: new Date() } ] }); expect(response.status).to.equal(201); expect(response.body.success).to.be.true; expect(response.body.emergencyCallId).to.exist; expect(response.body.status).to.equal('initiated'); expect(response.body.routing).to.exist; emergencyCallId = response.body.emergencyCallId; }); it('should reject emergency call with invalid urgency level', async () => { const response = await request(app) .post('/api/emergency-calls/initiate') .set('Authorization', `Bearer ${authToken}`) .send({ userId: testUser._id.toString(), urgencyLevel: 'invalid_level', description: 'Test emergency call' }); expect(response.status).to.equal(400); expect(response.body.success).to.be.false; expect(response.body.error).to.include('Validation failed'); }); it('should enforce rate limiting for emergency calls', async () => { // Make multiple rapid emergency call attempts const promises = []; for (let i = 0; i < 5; i++) { promises.push( request(app) .post('/api/emergency-calls/initiate') .set('Authorization', `Bearer ${authToken}`) .send({ userId: testUser._id.toString(), urgencyLevel: 'medium', description: `Rate limit test call ${i}` }) ); } const responses = await Promise.all(promises); // At least one should be rate limited const rateLimitedResponses = responses.filter(r => r.status === 429); expect(rateLimitedResponses.length).to.be.greaterThan(0); }); it('should require authentication for emergency calls', async () => { const response = await request(app) .post('/api/emergency-calls/initiate') .send({ userId: testUser._id.toString(), urgencyLevel: 'high', description: 'Unauthorized emergency call test' }); expect(response.status).to.equal(401); expect(response.body.success).to.be.false; }); }); describe('Emergency Call Escalation', () => { it('should successfully escalate emergency call to human agent', async () => { const response = await request(app) .post(`/api/emergency-calls/${emergencyCallId}/escalate`) .set('Authorization', `Bearer ${authToken}`) .send({ reason: 'Le chatbot ne peut pas résoudre mon problème technique' }); expect(response.status).to.equal(200); expect(response.body.success).to.be.true; expect(response.body.status).to.equal('escalated'); expect(response.body.queuePosition).to.exist; expect(response.body.estimatedWaitTime).to.exist; }); it('should reject escalation for non-existent emergency call', async () => { const fakeCallId = '12345678-1234-1234-1234-123456789012'; const response = await request(app) .post(`/api/emergency-calls/${fakeCallId}/escalate`) .set('Authorization', `Bearer ${authToken}`) .send({ reason: 'Test escalation' }); expect(response.status).to.equal(404); expect(response.body.success).to.be.false; }); }); describe('Emergency Call Status', () => { it('should retrieve emergency call status', async () => { const response = await request(app) .get(`/api/emergency-calls/${emergencyCallId}/status`) .set('Authorization', `Bearer ${authToken}`); expect(response.status).to.equal(200); expect(response.body.success).to.be.true; expect(response.body.emergencyCall).to.exist; expect(response.body.emergencyCall.emergencyCallId).to.equal(emergencyCallId); expect(response.body.emergencyCall.status).to.exist; }); }); describe('Agent Connection', () => { let agentAuthToken; before(async () => { // Get agent auth token const agentLoginResponse = await request(app) .post('/api/auth/login') .send({ email: testAgent.email, password: 'AgentPassword123!' }); agentAuthToken = agentLoginResponse.body.token; }); it('should allow agent to connect to emergency call', async () => { const response = await request(app) .post(`/api/emergency-calls/${emergencyCallId}/connect-agent`) .set('Authorization', `Bearer ${agentAuthToken}`) .send({ agentId: testAgent._id.toString() }); expect(response.status).to.equal(200); expect(response.body.success).to.be.true; expect(response.body.status).to.equal('connected_to_agent'); expect(response.body.agentInfo).to.exist; expect(response.body.webrtcSessionId).to.exist; }); it('should reject non-agent users from connecting to emergency calls', async () => { const response = await request(app) .post(`/api/emergency-calls/${emergencyCallId}/connect-agent`) .set('Authorization', `Bearer ${authToken}`) // Customer token .send({ agentId: testAgent._id.toString() }); expect(response.status).to.equal(403); expect(response.body.success).to.be.false; }); }); describe('Queue Statistics', () => { it('should provide queue statistics for agents', async () => { const agentLoginResponse = await request(app) .post('/api/auth/login') .send({ email: testAgent.email, password: 'AgentPassword123!' }); const response = await request(app) .get('/api/emergency-calls/queue/stats') .set('Authorization', `Bearer ${agentLoginResponse.body.token}`); expect(response.status).to.equal(200); expect(response.body.success).to.be.true; expect(response.body.stats).to.exist; expect(response.body.stats.activeCalls).to.be.a('number'); }); it('should reject queue statistics request from customers', async () => { const response = await request(app) .get('/api/emergency-calls/queue/stats') .set('Authorization', `Bearer ${authToken}`); expect(response.status).to.equal(403); expect(response.body.success).to.be.false; }); }); describe('Service Health Check', () => { it('should return emergency call service health status', async () => { const response = await request(app) .get('/api/emergency-calls/health'); expect(response.status).to.equal(200); expect(response.body.success).to.be.true; expect(response.body.service).to.equal('Emergency Call Service'); expect(response.body.status).to.equal('operational'); expect(response.body.endpoints.emergencyHotline).to.equal('9198'); expect(response.body.endpoints.humanSupportLine).to.equal('**********'); expect(response.body.compliance.frenchTelecomRegulations).to.equal('compliant'); }); }); describe('AI Urgency Assessment', () => { it('should correctly assess high urgency scenarios', async () => { const highUrgencyData = { urgencyLevel: 'high', description: 'Panne totale de réseau depuis 3 heures, impossible de recevoir des appels urgents', conversationHistory: [ { content: 'Mon téléphone ne fonctionne plus du tout', sender: 'user' }, { content: 'Ça ne marche toujours pas', sender: 'user' }, { content: 'Je veux parler à quelqu\'un', sender: 'user' } ] }; const assessment = await emergencyCallService.performAIUrgencyAssessment(highUrgencyData); expect(assessment.urgencyScore).to.be.greaterThan(7); expect(assessment.confidence).to.be.greaterThan(0.6); expect(assessment.recommendations).to.include('priority_queue'); }); it('should correctly assess critical urgency scenarios', async () => { const criticalUrgencyData = { urgencyLevel: 'critical', description: 'Urgence absolue - compte piraté, factures frauduleuses en cours', conversationHistory: [] }; const assessment = await emergencyCallService.performAIUrgencyAssessment(criticalUrgencyData); expect(assessment.urgencyScore).to.be.greaterThan(9); expect(assessment.recommendations).to.include('immediate_human_transfer'); }); }); describe('Compliance and Security', () => { it('should include GDPR compliance data in emergency calls', async () => { const emergencyCall = await EmergencyCall.findOne({ emergencyCallId }); expect(emergencyCall.compliance.gdprCompliant).to.be.true; expect(emergencyCall.compliance.recordingConsent).to.be.true; expect(emergencyCall.compliance.dataRetentionNotified).to.be.true; }); it('should maintain audit trail for emergency calls', async () => { const emergencyCall = await EmergencyCall.findOne({ emergencyCallId }); expect(emergencyCall.compliance.auditTrail).to.be.an('array'); expect(emergencyCall.compliance.auditTrail.length).to.be.greaterThan(0); const auditEntry = emergencyCall.compliance.auditTrail[0]; expect(auditEntry.action).to.exist; expect(auditEntry.timestamp).to.exist; }); it('should sanitize input data', async () => { const response = await request(app) .post('/api/emergency-calls/initiate') .set('Authorization', `Bearer ${authToken}`) .send({ userId: testUser._id.toString(), urgencyLevel: 'medium', description: '<script>alert("xss")</script>Problème avec mon téléphone<script>', reason: '<img src=x onerror=alert(1)>Test reason' }); // Should succeed but with sanitized data expect(response.status).to.equal(201); const savedCall = await EmergencyCall.findOne({ emergencyCallId: response.body.emergencyCallId }); expect(savedCall.description).to.not.include('<script>'); expect(savedCall.description).to.not.include('<img'); }); }); describe('Performance and Reliability', () => { it('should handle concurrent emergency call requests', async () => { const concurrentRequests = 10; const promises = []; for (let i = 0; i < concurrentRequests; i++) { promises.push( request(app) .post('/api/emergency-calls/initiate') .set('Authorization', `Bearer ${authToken}`) .send({ userId: testUser._id.toString(), urgencyLevel: 'medium', description: `Concurrent test call ${i}` }) ); } const responses = await Promise.all(promises); const successfulResponses = responses.filter(r => r.status === 201); // Should handle at least some concurrent requests successfully expect(successfulResponses.length).to.be.greaterThan(0); }); it('should respond within acceptable time limits', async () => { const startTime = Date.now(); const response = await request(app) .get('/api/emergency-calls/health'); const responseTime = Date.now() - startTime; expect(response.status).to.equal(200); expect(responseTime).to.be.lessThan(2000); // Less than 2 seconds }); }); });