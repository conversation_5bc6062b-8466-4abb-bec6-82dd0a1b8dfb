const { test, expect } = require('@playwright/test'); // Configuration pour les tests frontend const FRONTEND_URL = 'http://localhost:3000'; const SUPPORT_PAGE_URL = `${FRONTEND_URL}/support`; /** * Tests pour la nouvelle interface Support adaptée selon la maquette * Page SupportHomePage.tsx avec 3 onglets : * 1. Formulaire Support * 2. Panel Admin * 3. Analytics */ test.describe('[TARGET] Tests Interface Support - Adaptation Maquette', () => { test.beforeEach(async ({ page }) => { // Naviguer vers la page support await page.goto(SUPPORT_PAGE_URL); // Attendre que la page soit chargée await page.waitForLoadState('networkidle'); // Vérifier que nous sommes sur la bonne page await expect(page).toHaveURL(SUPPORT_PAGE_URL); }); test(' Doit afficher le header Free Mobile correct', async ({ page }) => { // Vérifier le logo Free const logo = page.locator('text=Free').first(); await expect(logo).toBeVisible(); // Vérifier le titre "Support Client" await expect(page.locator('text=Support Client')).toBeVisible(); // Vérifier les onglets de navigation await expect(page.locator('text=Formulaire Support')).toBeVisible(); await expect(page.locator('text=Panel Admin')).toBeVisible(); await expect(page.locator('text=Analytics')).toBeVisible(); }); test(' Onglet 1 - Formulaire Support complet', async ({ page }) => { // Nous sommes par défaut sur l'onglet Formulaire Support // Vérifier le titre du formulaire await expect(page.locator('text=Contacter le support Free')).toBeVisible(); // Vérifier tous les champs du formulaire await expect(page.locator('input[label="Nom complet"]')).toBeVisible(); await expect(page.locator('input[label="ID Client Free"]')).toBeVisible(); await expect(page.locator('input[label="Adresse email"]')).toBeVisible(); // Vérifier le dropdown catégorie await expect(page.locator('text=Catégorie du problème')).toBeVisible(); // Vérifier la zone de description await expect(page.locator('textarea[label="Description du problème"]')).toBeVisible(); // Vérifier la zone d'upload await expect(page.locator('text=Glissez votre fichier ici')).toBeVisible(); // Vérifier le bouton d'envoi await expect(page.locator('button:has-text("Envoyer ma demande")')).toBeVisible(); }); test(' Onglet 1 - Chat en direct', async ({ page }) => { // Vérifier la section chat à droite await expect(page.locator('text=Chat en direct')).toBeVisible(); // Vérifier l'indicateur "En ligne" await expect(page.locator('text=En ligne')).toBeVisible(); // Vérifier le point vert d'indicateur de statut const statusIndicator = page.locator('[style*="background-color: #4caf50"]'); await expect(statusIndicator).toBeVisible(); // Vérifier la zone de saisie du chat await expect(page.locator('input[placeholder="Tapez votre message..."]')).toBeVisible(); // Vérifier le bouton d'envoi du chat await expect(page.locator('button:has([data-testid="SendIcon"])')).toBeVisible(); }); test(' Onglet 1 - Questions fréquentes', async ({ page }) => { // Vérifier la section FAQ await expect(page.locator('text=Questions fréquentes')).toBeVisible(); // Vérifier les accordéons FAQ await expect(page.locator('text=Comment résoudre les problèmes de connexion')).toBeVisible(); await expect(page.locator('text=Problèmes de facturation')).toBeVisible(); // Tester l'ouverture d'un accordéon await page.click('text=Comment résoudre les problèmes de connexion'); // Vérifier que le contenu s'affiche await expect(page.locator('text=Vérifiez d\'abord que tous les câbles')).toBeVisible(); }); test(' Navigation entre onglets', async ({ page }) => { // Cliquer sur l'onglet Panel Admin await page.click('text=Panel Admin'); // Vérifier que nous sommes sur l'onglet Panel Admin await expect(page.locator('text=Panel Admin').first()).toBeVisible(); // Cliquer sur l'onglet Analytics await page.click('text=Analytics'); // Vérifier que nous sommes sur l'onglet Analytics await expect(page.locator('text=Analytics Dashboard')).toBeVisible(); // Revenir à l'onglet Formulaire Support await page.click('text=Formulaire Support'); // Vérifier que nous sommes de retour sur le formulaire await expect(page.locator('text=Contacter le support Free')).toBeVisible(); }); test(' Onglet 2 - Panel Admin - Menu latéral', async ({ page }) => { // Aller sur l'onglet Panel Admin await page.click('text=Panel Admin'); // Vérifier le titre du panel await expect(page.locator('text=Panel Admin').first()).toBeVisible(); // Vérifier tous les éléments du menu latéral await expect(page.locator('text=Dashboard')).toBeVisible(); await expect(page.locator('text=Conversations')).toBeVisible(); await expect(page.locator('text=Réclamations')).toBeVisible(); await expect(page.locator('text=Suggestions')).toBeVisible(); await expect(page.locator('text=Résiliations')).toBeVisible(); }); test('[ANALYTICS] Onglet 2 - Panel Admin - Tableau des demandes', async ({ page }) => { // Aller sur l'onglet Panel Admin await page.click('text=Panel Admin'); // Vérifier le titre du tableau await expect(page.locator('text=Demandes clients')).toBeVisible(); // Vérifier les filtres await expect(page.locator('text=Tous les statuts')).toBeVisible(); await expect(page.locator('text=Toutes catégories')).toBeVisible(); // Vérifier les en-têtes du tableau await expect(page.locator('text=Client')).toBeVisible(); await expect(page.locator('text=Catégorie')).toBeVisible(); await expect(page.locator('text=Statut')).toBeVisible(); await expect(page.locator('text=Date')).toBeVisible(); await expect(page.locator('text=Tags')).toBeVisible(); await expect(page.locator('text=Actions')).toBeVisible(); // Vérifier les données de démonstration await expect(page.locator('text=Jean Dupont')).toBeVisible(); await expect(page.locator('text=Marie Martin')).toBeVisible(); // Vérifier les chips de statut await expect(page.locator('text=En attente')).toBeVisible(); await expect(page.locator('text=En cours')).toBeVisible(); }); test('[USER] Onglet 2 - Panel Admin - Profil Client', async ({ page }) => { // Aller sur l'onglet Panel Admin await page.click('text=Panel Admin'); // Vérifier le profil client await expect(page.locator('text=Jean Dupont').first()).toBeVisible(); await expect(page.locator('text=ID: FR12345678')).toBeVisible(); // Vérifier l'historique await expect(page.locator('text=Historique récent')).toBeVisible(); await expect(page.locator('text=25/06 Problème résolu')).toBeVisible(); await expect(page.locator('text=20/06 Connexion facturation')).toBeVisible(); }); test('[METRICS] Onglet 3 - Analytics - Métriques principales', async ({ page }) => { // Aller sur l'onglet Analytics await page.click('text=Analytics'); // Vérifier le titre await expect(page.locator('text=Analytics Dashboard')).toBeVisible(); // Vérifier les 4 métriques principales await expect(page.locator('text=1,247')).toBeVisible(); // Total Tickets await expect(page.locator('text=4.2/5')).toBeVisible(); // Score CSAT await expect(page.locator('text=2.4h')).toBeVisible(); // Temps moyen await expect(page.locator('text=3.2%')).toBeVisible(); // Taux résiliation // Vérifier les labels des métriques await expect(page.locator('text=Total Tickets')).toBeVisible(); await expect(page.locator('text=Score CSAT')).toBeVisible(); await expect(page.locator('text=Temps moyen')).toBeVisible(); await expect(page.locator('text=Taux résiliation')).toBeVisible(); }); test('[ANALYTICS] Onglet 3 - Analytics - Graphiques', async ({ page }) => { // Aller sur l'onglet Analytics await page.click('text=Analytics'); // Vérifier les titres des graphiques await expect(page.locator('text=Volume des tickets (30 jours)')).toBeVisible(); await expect(page.locator('text=Répartition par catégorie')).toBeVisible(); // Vérifier les placeholders des graphiques await expect(page.locator('text=Graphique en courbe')).toBeVisible(); await expect(page.locator('text=Graphique en secteurs')).toBeVisible(); }); test('[TARGET] Onglet 3 - Analytics - Analyses avancées', async ({ page }) => { // Aller sur l'onglet Analytics await page.click('text=Analytics'); // Vérifier le top 5 des raisons de résiliation await expect(page.locator('text=Top 5 raisons de résiliation')).toBeVisible(); await expect(page.locator('text=Tarifs trop élevés')).toBeVisible(); await expect(page.locator('text=Problème de réseau')).toBeVisible(); await expect(page.locator('text=Facturation incorrecte')).toBeVisible(); await expect(page.locator('text=Service client')).toBeVisible(); // Vérifier les pourcentages await expect(page.locator('text=32%')).toBeVisible(); await expect(page.locator('text=28%')).toBeVisible(); await expect(page.locator('text=22%')).toBeVisible(); await expect(page.locator('text=18%')).toBeVisible(); // Vérifier les suggestions d'amélioration IA await expect(page.locator('text=Suggestions d\'amélioration IA')).toBeVisible(); await expect(page.locator('text=Améliorer la qualité réseau')).toBeVisible(); await expect(page.locator('text=Optimiser les tarifs')).toBeVisible(); await expect(page.locator('text=Formation équipe support')).toBeVisible(); }); test(' Test Responsive - Mobile', async ({ page }) => { // Simuler un viewport mobile await page.setViewportSize({ width: 375, height: 667 }); // Recharger la page await page.reload(); // Vérifier que les éléments sont toujours visibles await expect(page.locator('text=Free')).toBeVisible(); await expect(page.locator('text=Support Client')).toBeVisible(); await expect(page.locator('text=Formulaire Support')).toBeVisible(); // Vérifier que le formulaire est responsive await expect(page.locator('text=Contacter le support Free')).toBeVisible(); }); test(' Test Responsive - Desktop', async ({ page }) => { // Simuler un viewport desktop await page.setViewportSize({ width: 1920, height: 1080 }); // Recharger la page await page.reload(); // Vérifier que l'interface est optimisée pour desktop await expect(page.locator('text=Free')).toBeVisible(); await expect(page.locator('text=Support Client')).toBeVisible(); // Vérifier que le chat est visible à droite await expect(page.locator('text=Chat en direct')).toBeVisible(); }); test(' Test Fonctionnel - Remplissage du formulaire', async ({ page }) => { // Remplir le formulaire de support await page.fill('input[label="Nom complet"]', 'Jean Dupont Test'); await page.fill('input[label="ID Client Free"]', 'FR12345678'); await page.fill('input[label="Adresse email"]', '<EMAIL>'); // Sélectionner une catégorie await page.click('text=Catégorie du problème'); await page.click('text=Réseau'); // Remplir la description await page.fill('textarea[label="Description du problème"]', 'Problème de connexion internet intermittent'); // Vérifier que les champs sont bien remplis await expect(page.locator('input[value="Jean Dupont Test"]')).toBeVisible(); await expect(page.locator('input[value="FR12345678"]')).toBeVisible(); await expect(page.locator('input[value="<EMAIL>"]')).toBeVisible(); await expect(page.locator('textarea:has-text("Problème de connexion")')).toBeVisible(); }); test(' Test Fonctionnel - Chat en direct', async ({ page }) => { // Taper un message dans le chat await page.fill('input[placeholder="Tapez votre message..."]', 'Bonjour, j\'ai besoin d\'aide'); // Cliquer sur le bouton d'envoi await page.click('button:has([data-testid="SendIcon"])'); // Vérifier que le message a été envoyé (le champ doit être vide) await expect(page.locator('input[placeholder="Tapez votre message..."]')).toHaveValue(''); }); test('[DESIGN] Test Couleurs Free Mobile', async ({ page }) => { // Vérifier que les couleurs Free Mobile sont appliquées const freeButton = page.locator('text=Free').first(); await expect(freeButton).toHaveCSS('background-color', 'rgb(255, 255, 255)'); // blanc // Vérifier le bouton principal const submitButton = page.locator('button:has-text("Envoyer ma demande")'); await expect(submitButton).toBeVisible(); }); test('[SEARCH] Test Accessibilité - Navigation clavier', async ({ page }) => { // Tester la navigation au clavier await page.keyboard.press('Tab'); await page.keyboard.press('Tab'); await page.keyboard.press('Tab'); // Vérifier que les éléments sont focusables const focused = page.locator(':focus'); await expect(focused).toBeVisible(); }); test('[PERFORMANCE] Test Performance - Chargement rapide', async ({ page }) => { const startTime = Date.now(); // Aller sur la page await page.goto(SUPPORT_PAGE_URL); await page.waitForLoadState('networkidle'); const loadTime = Date.now() - startTime; // Vérifier que le chargement prend moins de 5 secondes expect(loadTime).toBeLessThan(5000); // Vérifier que tous les éléments critiques sont chargés await expect(page.locator('text=Free')).toBeVisible(); await expect(page.locator('text=Support Client')).toBeVisible(); await expect(page.locator('text=Contacter le support Free')).toBeVisible(); }); }); /** * Tests spécifiques aux interactions utilisateur */ test.describe(' Tests Interactions Utilisateur', () => { test.beforeEach(async ({ page }) => { await page.goto(SUPPORT_PAGE_URL); await page.waitForLoadState('networkidle'); }); test('[TARGET] Test Workflow complet utilisateur', async ({ page }) => { // 1. Remplir le formulaire await page.fill('input[label="Nom complet"]', 'Marie Martin'); await page.fill('input[label="ID Client Free"]', '**********'); await page.fill('input[label="Adresse email"]', '<EMAIL>'); // 2. Sélectionner une catégorie await page.click('text=Catégorie du problème'); await page.click('text=Facturation'); // 3. Remplir la description await page.fill('textarea[label="Description du problème"]', 'Erreur sur ma facture du mois dernier'); // 4. Envoyer un message dans le chat await page.fill('input[placeholder="Tapez votre message..."]', 'J\'ai aussi une question urgente'); await page.click('button:has([data-testid="SendIcon"])'); // 5. Aller sur le panel admin await page.click('text=Panel Admin'); await expect(page.locator('text=Demandes clients')).toBeVisible(); // 6. Aller sur les analytics await page.click('text=Analytics'); await expect(page.locator('text=Analytics Dashboard')).toBeVisible(); // 7. Revenir au formulaire await page.click('text=Formulaire Support'); await expect(page.locator('text=Contacter le support Free')).toBeVisible(); }); test(' Test Accordéons FAQ', async ({ page }) => { // Tester l'ouverture/fermeture des accordéons const faqItem = page.locator('text=Comment résoudre les problèmes de connexion'); // Ouvrir l'accordéon await faqItem.click(); await expect(page.locator('text=Vérifiez d\'abord que tous les câbles')).toBeVisible(); // Fermer l'accordéon await faqItem.click(); await expect(page.locator('text=Vérifiez d\'abord que tous les câbles')).not.toBeVisible(); }); test('[DESIGN] Test Chips et Badges', async ({ page }) => { // Aller sur le panel admin await page.click('text=Panel Admin'); // Vérifier les chips de catégorie await expect(page.locator('text=Réseau')).toBeVisible(); await expect(page.locator('text=Facturation')).toBeVisible(); // Vérifier les chips de statut await expect(page.locator('text=En attente')).toBeVisible(); await expect(page.locator('text=En cours')).toBeVisible(); // Vérifier les chips de priorité await expect(page.locator('text=urgent')).toBeVisible(); await expect(page.locator('text=standard')).toBeVisible(); }); }); /** * Tests de régression pour s'assurer que les fonctionnalités existantes marchent encore */ test.describe(' Tests de Régression', () => { test(' Navigation vers la page support depuis l\'accueil', async ({ page }) => { // Aller sur l'accueil await page.goto(FRONTEND_URL); // Vérifier la redirection vers /support await expect(page).toHaveURL(SUPPORT_PAGE_URL); }); test('[SECURITY] Vérification que l\'authentification fonctionne', async ({ page }) => { // Aller sur la page de login await page.goto(`${FRONTEND_URL}/login`); // Vérifier que nous sommes sur la page de login await expect(page).toHaveURL(`${FRONTEND_URL}/login`); // Vérifier que le formulaire de login est présent await expect(page.locator('input[type="email"]')).toBeVisible(); await expect(page.locator('input[type="password"]')).toBeVisible(); }); test('[MOBILE] Test Menu Navigation', async ({ page }) => { // Naviguer vers la page support await page.goto(SUPPORT_PAGE_URL); // Vérifier que le menu "Support Free" est présent await expect(page.locator('text=Support Free')).toBeVisible(); // Vérifier les autres éléments du menu await expect(page.locator('text=Tableau de bord')).toBeVisible(); await expect(page.locator('text=Chat Assistant')).toBeVisible(); await expect(page.locator('text=Mon Profil')).toBeVisible(); }); });