/** * ============================================= * EMERGENCY CALL TEST HELPERS * Utility functions for emergency call E2E testing * Includes mocks, assertions, and common test patterns * ============================================= */ import { Page, expect } from '@playwright/test'; // Test data constants export const EMERGENCY_TEST_DATA = { emergencyHotline: '9198', humanSupportLine: '**********', urgencyLevels: ['medium', 'high', 'urgent', 'critical'], testDescriptions: { short: 'Test', valid: 'Mon téléphone ne fonctionne plus depuis ce matin', long: 'A'.repeat(1001), xss: '<script>alert("xss")</script>Problème avec mon téléphone', critical: 'URGENCE ABSOLUE - Mon compte a é<PERSON> piraté, des prélèvements frauduleux sont en cours' } }; // Mock API responses export const MOCK_RESPONSES = { emergencyCallInitiated: { success: true, emergencyCallId: 'test-emergency-call-123', status: 'initiated', routing: { route: 'priority_queue', priority: 'high', estimatedWaitTime: 30000 } }, emergencyCallEscalated: { success: true, status: 'escalated', queuePosition: 1, estimatedWaitTime: 45000, priority: 'high' }, agentConnected: { success: true, status: 'connected_to_agent', agentInfo: { agentId: 'test-agent-456', agentName: 'Test Agent', connectedAt: new Date() }, webrtcSessionId: 'webrtc-session-789' }, rateLimitExceeded: { success: false, error: 'Emergency call rate limit exceeded', message: 'Trop d\'appels d\'urgence récents. Veuillez patienter.', retryAfter: 300 }, serviceHealth: { success: true, service: 'Emergency Call Service', status: 'operational', endpoints: { emergencyHotline: '9198', humanSupportLine: '**********' }, compliance: { frenchTelecomRegulations: 'compliant', gdpr: 'compliant', dataRetention: '90 days' } } }; /** * Login helper for different user types */ export async function loginUser(page: Page, userType: 'customer' | 'agent' | 'admin') { const users = { customer: { email: '<EMAIL>', password: 'TestPassword123!' }, agent: { email: '<EMAIL>', password: 'AgentPassword123!' }, admin: { email: '<EMAIL>', password: 'AdminPassword123!' } }; const user = users[userType]; await page.goto('/login'); await page.fill('[data-testid="email-input"]', user.email); await page.fill('[data-testid="password-input"]', user.password); await page.click('[data-testid="login-button"]'); // Wait for successful login await page.waitForURL('**/dashboard', { timeout: 30000 }); await expect(page.locator('[data-testid="user-menu"]')).toBeVisible(); } /** * Navigate to chat interface */ export async function navigateToChat(page: Page) { await page.click('[data-testid="chat-nav-link"]'); await page.waitForSelector('[data-testid="chat-window"]', { timeout: 30000 }); } /** * Wait for emergency call button to be visible */ export async function waitForEmergencyCallButton(page: Page) { await page.waitForSelector('[data-testid="emergency-call-button"]', { timeout: 30000 }); } /** * Open emergency call dialog */ export async function openEmergencyCallDialog(page: Page) { await waitForEmergencyCallButton(page); await page.click('[data-testid="emergency-call-button"]'); await page.waitForSelector('[data-testid="emergency-call-dialog"]', { timeout: 10000 }); } /** * Fill emergency call form */ export async function fillEmergencyCallForm( page: Page, urgencyLevel: string, description: string ) { await page.selectOption('[data-testid="urgency-level-select"]', urgencyLevel); await page.fill('[data-testid="problem-description"]', description); } /** * Setup API mocks for emergency call testing */ export async function setupEmergencyCallMocks(page: Page) { // Mock emergency call initiation await page.route('**/api/emergency-calls/initiate', async route => { await route.fulfill({ status: 201, contentType: 'application/json', body: JSON.stringify(MOCK_RESPONSES.emergencyCallInitiated) }); }); // Mock escalation await page.route('**/api/emergency-calls/*/escalate', async route => { await route.fulfill({ status: 200, contentType: 'application/json', body: JSON.stringify(MOCK_RESPONSES.emergencyCallEscalated) }); }); // Mock agent connection await page.route('**/api/emergency-calls/*/connect-agent', async route => { await route.fulfill({ status: 200, contentType: 'application/json', body: JSON.stringify(MOCK_RESPONSES.agentConnected) }); }); // Mock service health await page.route('**/api/emergency-calls/health', async route => { await route.fulfill({ status: 200, contentType: 'application/json', body: JSON.stringify(MOCK_RESPONSES.serviceHealth) }); }); } /** * Setup rate limiting mock */ export async function setupRateLimitingMock(page: Page, maxCalls: number = 3) { let callCount = 0; await page.route('**/api/emergency-calls/initiate', async route => { callCount++; if (callCount > maxCalls) { await route.fulfill({ status: 429, contentType: 'application/json', body: JSON.stringify(MOCK_RESPONSES.rateLimitExceeded) }); } else { await route.fulfill({ status: 201, contentType: 'application/json', body: JSON.stringify({ ...MOCK_RESPONSES.emergencyCallInitiated, emergencyCallId: `test-call-${callCount}` }) }); } }); } /** * Setup WebSocket mocks for real-time updates */ export async function setupWebSocketMocks(page: Page) { await page.addInitScript(() => { // Mock Socket.IO for testing (window as any).mockSocketEvents = []; const originalIo = (window as any).io; (window as any).io = () => ({ on: (event: string, callback: Function) => { (window as any).mockSocketEvents.push({ event, callback }); }, emit: () => {}, connected: true, disconnect: () => {} }); }); } /** * Trigger WebSocket event simulation */ export async function triggerWebSocketEvent(page: Page, eventName: string, data: any) { await page.evaluate(({ eventName, data }) => { const events = (window as any).mockSocketEvents || []; const event = events.find((e: any) => e.event === eventName); if (event && event.callback) { event.callback(data); } }, { eventName, data }); } /** * Assert emergency call button properties */ export async function assertEmergencyCallButton(page: Page) { const button = page.locator('[data-testid="emergency-call-button"]'); // Visibility and content await expect(button).toBeVisible(); await expect(button).toContainText('Appel d\'Urgence'); // Accessibility await expect(button).toHaveAttribute('aria-label'); await expect(button).toHaveAttribute('role', 'button'); // Styling await expect(button).toHaveCSS('background-color', 'rgb(211, 47, 47)'); // Touch target size (WCAG compliance) const buttonBox = await button.boundingBox(); expect(buttonBox?.height).toBeGreaterThanOrEqual(48); expect(buttonBox?.width).toBeGreaterThanOrEqual(48); } /** * Assert emergency call dialog properties */ export async function assertEmergencyCallDialog(page: Page) { const dialog = page.locator('[data-testid="emergency-call-dialog"]'); // Visibility and accessibility await expect(dialog).toBeVisible(); await expect(dialog).toHaveAttribute('aria-labelledby'); await expect(dialog).toHaveAttribute('aria-describedby'); // French content await expect(page.locator('text=Appel d\'Urgence Free Mobile')).toBeVisible(); await expect(page.locator('text=Niveau d\'urgence')).toBeVisible(); await expect(page.locator('text=Description du problème')).toBeVisible(); // Emergency contacts await expect(page.locator(`text=${EMERGENCY_TEST_DATA.emergencyHotline}`)).toBeVisible(); await expect(page.locator(`text=${EMERGENCY_TEST_DATA.humanSupportLine}`)).toBeVisible(); } /** * Assert form validation */ export async function assertFormValidation(page: Page) { const descriptionInput = page.locator('[data-testid="problem-description"]'); const submitButton = page.locator('[data-testid="initiate-call-button"]'); // Test minimum length await descriptionInput.fill(EMERGENCY_TEST_DATA.testDescriptions.short); await expect(submitButton).toBeDisabled(); // Test valid input await descriptionInput.fill(EMERGENCY_TEST_DATA.testDescriptions.valid); await expect(submitButton).toBeEnabled(); // Test character counter const helperText = page.locator('[data-testid="description-helper-text"]'); await expect(helperText).toContainText('/1000'); } /** * Measure API response time */ export async function measureApiResponseTime(page: Page, apiEndpoint: string): Promise<number> { let responseTime = 0; await page.route(apiEndpoint, async route => { const startTime = Date.now(); await route.continue(); responseTime = Date.now() - startTime; }); return responseTime; } /** * Test keyboard navigation */ export async function testKeyboardNavigation(page: Page) { // Navigate to emergency button with Tab await page.keyboard.press('Tab'); await page.keyboard.press('Tab'); await page.keyboard.press('Tab'); // Verify focus on emergency button const focusedElement = await page.locator(':focus'); await expect(focusedElement).toHaveAttribute('data-testid', 'emergency-call-button'); // Open dialog with Enter await page.keyboard.press('Enter'); await expect(page.locator('[data-testid="emergency-call-dialog"]')).toBeVisible(); // Navigate through dialog await page.keyboard.press('Tab'); // Urgency select await page.keyboard.press('Tab'); // Description input await page.keyboard.press('Tab'); // Submit button const submitButton = page.locator('[data-testid="initiate-call-button"]'); await expect(submitButton).toBeFocused(); } /** * Test mobile responsiveness */ export async function testMobileResponsiveness(page: Page) { // Set mobile viewport await page.setViewportSize({ width: 375, height: 667 }); // Verify button is properly sized const button = page.locator('[data-testid="emergency-call-button"]'); const buttonBox = await button.boundingBox(); expect(buttonBox?.height).toBeGreaterThanOrEqual(48); expect(buttonBox?.width).toBeGreaterThanOrEqual(48); // Test dialog responsiveness await button.click(); const dialog = page.locator('[data-testid="emergency-call-dialog"]'); const dialogBox = await dialog.boundingBox(); expect(dialogBox?.width).toBeLessThanOrEqual(375); }