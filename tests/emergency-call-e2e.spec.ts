/** * ============================================= * EMERGENCY CALL E2E TESTS * Comprehensive Playwright tests for emergency call functionality * Tests UI, API integration, security, and compliance * ============================================= */ import { test, expect, Page, BrowserContext } from '@playwright/test'; import { chromium, firefox, webkit } from '@playwright/test'; // Test configuration const TEST_CONFIG = { baseURL: process.env.PLAYWRIGHT_BASE_URL || 'http://localhost:3001', apiURL: process.env.PLAYWRIGHT_API_URL || 'http://localhost:5000', timeout: 30000, emergencyHotline: '9198', humanSupportLine: '0745303145' }; // Test user credentials const TEST_USERS = { customer: { email: '<EMAIL>', password: 'TestPassword123!', role: 'customer' }, agent: { email: '<EMAIL>', password: 'AgentPassword123!', role: 'agent' } }; // Helper functions async function loginUser(page: Page, userType: 'customer' | 'agent') { const user = TEST_USERS[userType]; await page.goto(`${TEST_CONFIG.baseURL}/login`); await page.fill('[data-testid="email-input"]', user.email); await page.fill('[data-testid="password-input"]', user.password); await page.click('[data-testid="login-button"]'); // Wait for successful login await page.waitForURL('**/dashboard', { timeout: TEST_CONFIG.timeout }); await expect(page.locator('[data-testid="user-menu"]')).toBeVisible(); } async function navigateToChat(page: Page) { await page.click('[data-testid="chat-nav-link"]'); await page.waitForSelector('[data-testid="chat-window"]', { timeout: TEST_CONFIG.timeout }); } async function waitForEmergencyCallButton(page: Page) { await page.waitForSelector('[data-testid="emergency-call-button"]', { timeout: TEST_CONFIG.timeout }); } // Test suite test.describe(' Emergency Call Feature E2E Tests', () => { test.describe('1. Emergency Call Button Functionality', () => { test('should display emergency call button in chat interface', async ({ page }) => { await loginUser(page, 'customer'); await navigateToChat(page); // Check emergency call button visibility const emergencyButton = page.locator('[data-testid="emergency-call-button"]'); await expect(emergencyButton).toBeVisible(); await expect(emergencyButton).toContainText('Appel d\'Urgence'); // Verify button styling and accessibility await expect(emergencyButton).toHaveCSS('background-color', 'rgb(211, 47, 47)'); await expect(emergencyButton).toHaveAttribute('aria-label'); await expect(emergencyButton).toHaveAttribute('role', 'button'); }); test('should open emergency call dialog with urgency selection', async ({ page }) => { await loginUser(page, 'customer'); await navigateToChat(page); await waitForEmergencyCallButton(page); // Click emergency call button await page.click('[data-testid="emergency-call-button"]'); // Verify dialog opens const dialog = page.locator('[data-testid="emergency-call-dialog"]'); await expect(dialog).toBeVisible(); // Check urgency level options const urgencySelect = page.locator('[data-testid="urgency-level-select"]'); await expect(urgencySelect).toBeVisible(); await urgencySelect.click(); const urgencyOptions = ['medium', 'high', 'urgent', 'critical']; for (const option of urgencyOptions) { const optionElement = page.locator(`[data-value="${option}"]`); await expect(optionElement).toBeVisible(); } }); test('should validate problem description input limits', async ({ page }) => { await loginUser(page, 'customer'); await navigateToChat(page); await waitForEmergencyCallButton(page); await page.click('[data-testid="emergency-call-button"]'); const descriptionInput = page.locator('[data-testid="problem-description"]'); await expect(descriptionInput).toBeVisible(); // Test minimum length validation (10 characters) await descriptionInput.fill('Short'); const submitButton = page.locator('[data-testid="initiate-call-button"]'); await expect(submitButton).toBeDisabled(); // Test valid description await descriptionInput.fill('Mon téléphone ne fonctionne plus depuis ce matin'); await expect(submitButton).toBeEnabled(); // Test maximum length (1000 characters) const longText = 'A'.repeat(1001); await descriptionInput.fill(longText); const helperText = page.locator('[data-testid="description-helper-text"]'); await expect(helperText).toContainText('1000/1000'); }); test('should display French language content correctly', async ({ page }) => { await loginUser(page, 'customer'); await navigateToChat(page); await waitForEmergencyCallButton(page); await page.click('[data-testid="emergency-call-button"]'); // Verify French text content await expect(page.locator('text=Appel d\'Urgence Free Mobile')).toBeVisible(); await expect(page.locator('text=Niveau d\'urgence')).toBeVisible(); await expect(page.locator('text=Description du problème')).toBeVisible(); await expect(page.locator('text=Contacts d\'urgence')).toBeVisible(); await expect(page.locator(`text=${TEST_CONFIG.emergencyHotline}`)).toBeVisible(); await expect(page.locator(`text=${TEST_CONFIG.humanSupportLine}`)).toBeVisible(); }); }); test.describe('2. Emergency Call Flow Testing', () => { test('should initiate emergency call with valid authentication', async ({ page }) => { await loginUser(page, 'customer'); await navigateToChat(page); await waitForEmergencyCallButton(page); // Initiate emergency call await page.click('[data-testid="emergency-call-button"]'); await page.selectOption('[data-testid="urgency-level-select"]', 'high'); await page.fill('[data-testid="problem-description"]', 'Mon réseau mobile ne fonctionne plus depuis 2 heures, impossible de recevoir des appels'); // Mock API response for emergency call initiation await page.route('**/api/emergency-calls/initiate', async route => { await route.fulfill({ status: 201, contentType: 'application/json', body: JSON.stringify({ success: true, emergencyCallId: 'test-emergency-call-123', status: 'initiated', routing: { route: 'priority_queue', priority: 'high', estimatedWaitTime: 30000 } }) }); }); await page.click('[data-testid="initiate-call-button"]'); // Verify call initiation await expect(page.locator('[data-testid="emergency-call-status"]')).toBeVisible(); await expect(page.locator('text=Appel d\'urgence initié')).toBeVisible(); }); test('should display real-time status updates', async ({ page }) => { await loginUser(page, 'customer'); await navigateToChat(page); // Mock WebSocket connection for real-time updates await page.addInitScript(() => { // Mock Socket.IO for testing (window as any).mockSocketEvents = []; const originalIo = (window as any).io; (window as any).io = () => ({ on: (event: string, callback: Function) => { (window as any).mockSocketEvents.push({ event, callback }); }, emit: () => {}, connected: true }); }); await waitForEmergencyCallButton(page); // Simulate emergency call status updates await page.evaluate(() => { const statusEvent = (window as any).mockSocketEvents.find((e: any) => e.event === 'emergency-call-initiated' ); if (statusEvent) { statusEvent.callback({ emergencyCallId: 'test-call-123', status: 'in_queue', queuePosition: 2, estimatedWaitTime: 90000 }); } }); // Verify status display updates await expect(page.locator('[data-testid="queue-position"]')).toContainText('2'); await expect(page.locator('[data-testid="estimated-wait-time"]')).toContainText('1 minute'); }); test('should handle AI urgency assessment routing', async ({ page }) => { await loginUser(page, 'customer'); await navigateToChat(page); await waitForEmergencyCallButton(page); // Test critical urgency routing await page.click('[data-testid="emergency-call-button"]'); await page.selectOption('[data-testid="urgency-level-select"]', 'critical'); await page.fill('[data-testid="problem-description"]', 'URGENCE ABSOLUE - Mon compte a été piraté, des prélèvements frauduleux sont en cours'); await page.route('**/api/emergency-calls/initiate', async route => { await route.fulfill({ status: 201, contentType: 'application/json', body: JSON.stringify({ success: true, emergencyCallId: 'critical-call-456', status: 'initiated', routing: { route: 'immediate_human_transfer', priority: 'critical', estimatedWaitTime: 5000, requiresHumanAgent: true } }) }); }); await page.click('[data-testid="initiate-call-button"]'); // Verify immediate human transfer routing await expect(page.locator('[data-testid="routing-status"]')).toContainText('Transfert immédiat'); await expect(page.locator('[data-testid="priority-indicator"]')).toContainText('CRITIQUE'); }); }); test.describe('3. Security and Rate Limiting', () => { test('should enforce rate limiting (3 calls per 5 minutes)', async ({ page }) => { await loginUser(page, 'customer'); await navigateToChat(page); await waitForEmergencyCallButton(page); // Mock rate limiting response let callCount = 0; await page.route('**/api/emergency-calls/initiate', async route => { callCount++; if (callCount > 3) { await route.fulfill({ status: 429, contentType: 'application/json', body: JSON.stringify({ success: false, error: 'Emergency call rate limit exceeded', message: 'Trop d\'appels d\'urgence récents. Veuillez patienter.', retryAfter: 300 }) }); } else { await route.fulfill({ status: 201, contentType: 'application/json', body: JSON.stringify({ success: true, emergencyCallId: `test-call-${callCount}`, status: 'initiated' }) }); } }); // Make multiple emergency call attempts for (let i = 1; i <= 4; i++) { await page.click('[data-testid="emergency-call-button"]'); await page.selectOption('[data-testid="urgency-level-select"]', 'medium'); await page.fill('[data-testid="problem-description"]', `Test call ${i} for rate limiting`); await page.click('[data-testid="initiate-call-button"]'); if (i <= 3) { await expect(page.locator('[data-testid="success-message"]')).toBeVisible(); } else { await expect(page.locator('[data-testid="rate-limit-error"]')).toBeVisible(); await expect(page.locator('text=Trop d\'appels d\'urgence récents')).toBeVisible(); } // Close dialog for next iteration if (i < 4) { await page.click('[data-testid="close-dialog"]'); } } }); test('should sanitize input and prevent XSS attacks', async ({ page }) => { await loginUser(page, 'customer'); await navigateToChat(page); await waitForEmergencyCallButton(page); await page.click('[data-testid="emergency-call-button"]'); // Test XSS prevention const maliciousInput = '<script>alert("XSS")</script>Mon téléphone ne marche pas<img src=x onerror=alert(1)>'; await page.fill('[data-testid="problem-description"]', maliciousInput); // Verify input is sanitized const inputValue = await page.inputValue('[data-testid="problem-description"]'); expect(inputValue).not.toContain('<script>'); expect(inputValue).not.toContain('<img'); expect(inputValue).toContain('Mon téléphone ne marche pas'); }); test('should require authentication for emergency calls', async ({ page }) => { // Test without authentication await page.goto(`${TEST_CONFIG.baseURL}/chat`); // Should redirect to login or show auth required message await expect(page).toHaveURL(/.*login.*/); // Verify emergency call button is not accessible const emergencyButton = page.locator('[data-testid="emergency-call-button"]'); await expect(emergencyButton).not.toBeVisible(); }); }); test.describe('4. Agent Integration Testing', () => { test('should allow agent to connect to emergency call', async ({ page }) => { await loginUser(page, 'agent'); await page.goto(`${TEST_CONFIG.baseURL}/agent-dashboard`); // Mock emergency call in queue await page.route('**/api/emergency-calls/queue/stats', async route => { await route.fulfill({ status: 200, contentType: 'application/json', body: JSON.stringify({ success: true, stats: { activeCalls: 1, queueBreakdown: [ { urgencyLevel: 'high', count: 1, averageWaitTime: 45000 } ] } }) }); }); // Verify agent can see emergency calls await expect(page.locator('[data-testid="emergency-queue"]')).toBeVisible(); await expect(page.locator('[data-testid="emergency-call-item"]')).toBeVisible(); // Test agent connection await page.route('**/api/emergency-calls/*/connect-agent', async route => { await route.fulfill({ status: 200, contentType: 'application/json', body: JSON.stringify({ success: true, status: 'connected_to_agent', agentInfo: { agentId: 'agent-123', agentName: 'Test Agent', connectedAt: new Date() }, webrtcSessionId: 'webrtc-session-456' }) }); }); await page.click('[data-testid="connect-to-call-button"]'); // Verify connection success await expect(page.locator('[data-testid="call-connected-status"]')).toBeVisible(); await expect(page.locator('text=Connecté à l\'appel d\'urgence')).toBeVisible(); }); test('should handle supervisor escalation', async ({ page }) => { await loginUser(page, 'agent'); await page.goto(`${TEST_CONFIG.baseURL}/agent-dashboard`); // Mock active emergency call await page.evaluate(() => { (window as any).activeEmergencyCall = { emergencyCallId: 'escalation-test-789', status: 'connected_to_agent', urgencyLevel: 'critical' }; }); // Test escalation to supervisor await page.route('**/api/emergency-calls/*/escalate', async route => { await route.fulfill({ status: 200, contentType: 'application/json', body: JSON.stringify({ success: true, escalation: { supervisorId: 'supervisor-123', escalatedAt: new Date(), reason: 'Complex technical issue requiring supervisor expertise' } }) }); }); await page.click('[data-testid="escalate-to-supervisor"]'); await page.fill('[data-testid="escalation-reason"]', 'Complex technical issue requiring supervisor expertise'); await page.click('[data-testid="confirm-escalation"]'); // Verify escalation success await expect(page.locator('[data-testid="escalation-success"]')).toBeVisible(); await expect(page.locator('text=Escaladé vers superviseur')).toBeVisible(); }); }); test.describe('5. Cross-browser and Accessibility Testing', () => { test('should work correctly in Chrome, Firefox, and Safari', async ({ browserName }) => { // This test will run across different browsers automatically test.skip(browserName === 'webkit' && process.platform === 'win32', 'Safari not available on Windows'); const { page } = await test.step('Setup browser', async () => { const context = await test.info().project.use.browserName === 'chromium' ? chromium.launch() : test.info().project.use.browserName === 'firefox' ? firefox.launch() : webkit.launch(); const page = await (await context.newContext()).newPage(); return { page }; }); await loginUser(page, 'customer'); await navigateToChat(page); await waitForEmergencyCallButton(page); // Test emergency call button functionality across browsers await page.click('[data-testid="emergency-call-button"]'); await expect(page.locator('[data-testid="emergency-call-dialog"]')).toBeVisible(); // Verify browser-specific rendering const button = page.locator('[data-testid="emergency-call-button"]'); const buttonBox = await button.boundingBox(); expect(buttonBox?.height).toBeGreaterThanOrEqual(48); // WCAG touch target size await page.close(); }); test('should be keyboard navigable', async ({ page }) => { await loginUser(page, 'customer'); await navigateToChat(page); await waitForEmergencyCallButton(page); // Test keyboard navigation await page.keyboard.press('Tab'); await page.keyboard.press('Tab'); await page.keyboard.press('Tab'); // Navigate to emergency button // Verify focus on emergency button const focusedElement = await page.locator(':focus'); await expect(focusedElement).toHaveAttribute('data-testid', 'emergency-call-button'); // Open dialog with Enter key await page.keyboard.press('Enter'); await expect(page.locator('[data-testid="emergency-call-dialog"]')).toBeVisible(); // Navigate through dialog with Tab await page.keyboard.press('Tab'); // Urgency select await page.keyboard.press('Tab'); // Description input await page.keyboard.press('Tab'); // Submit button const submitButton = page.locator('[data-testid="initiate-call-button"]'); await expect(submitButton).toBeFocused(); }); test('should meet WCAG 2.1 AA accessibility standards', async ({ page }) => { await loginUser(page, 'customer'); await navigateToChat(page); await waitForEmergencyCallButton(page); // Test color contrast const emergencyButton = page.locator('[data-testid="emergency-call-button"]'); const buttonStyles = await emergencyButton.evaluate(el => { const styles = window.getComputedStyle(el); return { backgroundColor: styles.backgroundColor, color: styles.color, fontSize: styles.fontSize }; }); // Verify minimum font size (WCAG requirement) const fontSize = parseInt(buttonStyles.fontSize); expect(fontSize).toBeGreaterThanOrEqual(14); // Test ARIA attributes await expect(emergencyButton).toHaveAttribute('aria-label'); await expect(emergencyButton).toHaveAttribute('role', 'button'); // Open dialog and test accessibility await page.click('[data-testid="emergency-call-button"]'); const dialog = page.locator('[data-testid="emergency-call-dialog"]'); await expect(dialog).toHaveAttribute('aria-labelledby'); await expect(dialog).toHaveAttribute('aria-describedby'); // Test form labels const urgencySelect = page.locator('[data-testid="urgency-level-select"]'); await expect(urgencySelect).toHaveAttribute('aria-label'); const descriptionInput = page.locator('[data-testid="problem-description"]'); await expect(descriptionInput).toHaveAttribute('aria-describedby'); }); test('should be mobile responsive', async ({ page }) => { // Set mobile viewport await page.setViewportSize({ width: 375, height: 667 }); await loginUser(page, 'customer'); await navigateToChat(page); await waitForEmergencyCallButton(page); // Verify button is visible and properly sized on mobile const emergencyButton = page.locator('[data-testid="emergency-call-button"]'); await expect(emergencyButton).toBeVisible(); const buttonBox = await emergencyButton.boundingBox(); expect(buttonBox?.height).toBeGreaterThanOrEqual(48); // Touch target size expect(buttonBox?.width).toBeGreaterThanOrEqual(48); // Test dialog responsiveness await page.click('[data-testid="emergency-call-button"]'); const dialog = page.locator('[data-testid="emergency-call-dialog"]'); await expect(dialog).toBeVisible(); const dialogBox = await dialog.boundingBox(); expect(dialogBox?.width).toBeLessThanOrEqual(375); // Fits in mobile viewport }); }); test.describe('6. Performance and Compliance Testing', () => { test('should meet API response time targets (<2s)', async ({ page }) => { await loginUser(page, 'customer'); await navigateToChat(page); await waitForEmergencyCallButton(page); // Measure API response time let responseTime = 0; await page.route('**/api/emergency-calls/initiate', async route => { const startTime = Date.now(); await route.fulfill({ status: 201, contentType: 'application/json', body: JSON.stringify({ success: true, emergencyCallId: 'perf-test-123', status: 'initiated' }) }); responseTime = Date.now() - startTime; }); await page.click('[data-testid="emergency-call-button"]'); await page.selectOption('[data-testid="urgency-level-select"]', 'high'); await page.fill('[data-testid="problem-description"]', 'Performance test emergency call'); const startTime = Date.now(); await page.click('[data-testid="initiate-call-button"]'); await page.waitForSelector('[data-testid="emergency-call-status"]'); const totalTime = Date.now() - startTime; // Verify response time is under 2 seconds expect(totalTime).toBeLessThan(2000); }); test('should handle concurrent emergency calls', async ({ browser }) => { const contexts = await Promise.all([ browser.newContext(), browser.newContext(), browser.newContext() ]); const pages = await Promise.all(contexts.map(context => context.newPage())); // Login all users concurrently await Promise.all(pages.map(page => loginUser(page, 'customer'))); await Promise.all(pages.map(page => navigateToChat(page))); await Promise.all(pages.map(page => waitForEmergencyCallButton(page))); // Mock concurrent API responses let callCount = 0; await Promise.all(pages.map(page => page.route('**/api/emergency-calls/initiate', async route => { callCount++; await route.fulfill({ status: 201, contentType: 'application/json', body: JSON.stringify({ success: true, emergencyCallId: `concurrent-call-${callCount}`, status: 'initiated' }) }); }) )); // Initiate concurrent emergency calls await Promise.all(pages.map(async (page, index) => { await page.click('[data-testid="emergency-call-button"]'); await page.selectOption('[data-testid="urgency-level-select"]', 'medium'); await page.fill('[data-testid="problem-description"]', `Concurrent test call ${index + 1}`); await page.click('[data-testid="initiate-call-button"]'); })); // Verify all calls were successful await Promise.all(pages.map(page => expect(page.locator('[data-testid="emergency-call-status"]')).toBeVisible() )); // Cleanup await Promise.all(contexts.map(context => context.close())); }); test('should validate GDPR compliance data handling', async ({ page }) => { await loginUser(page, 'customer'); await navigateToChat(page); await waitForEmergencyCallButton(page); // Mock API to verify GDPR compliance data await page.route('**/api/emergency-calls/initiate', async route => { const requestBody = await route.request().postDataJSON(); // Verify GDPR compliance fields are included expect(requestBody.gdprConsent).toBeDefined(); expect(requestBody.gdprConsent.dataProcessingConsent).toBe(true); expect(requestBody.gdprConsent.recordingConsent).toBe(true); expect(requestBody.gdprConsent.dataRetentionNotified).toBe(true); await route.fulfill({ status: 201, contentType: 'application/json', body: JSON.stringify({ success: true, emergencyCallId: 'gdpr-test-456', status: 'initiated', compliance: { gdprCompliant: true, dataRetentionPeriod: '90 days', regulationVersion: 'ARCEP-2024' } }) }); }); await page.click('[data-testid="emergency-call-button"]'); await page.selectOption('[data-testid="urgency-level-select"]', 'high'); await page.fill('[data-testid="problem-description"]', 'GDPR compliance test call'); await page.click('[data-testid="initiate-call-button"]'); // Verify compliance indicators await expect(page.locator('[data-testid="gdpr-compliant-indicator"]')).toBeVisible(); await expect(page.locator('text=Conforme RGPD')).toBeVisible(); }); }); test.describe('7. Existing Feature Regression Testing', () => { test('should not break existing chat functionality', async ({ page }) => { await loginUser(page, 'customer'); await navigateToChat(page); // Test normal chat still works const messageInput = page.locator('[data-testid="message-input"]'); await expect(messageInput).toBeVisible(); await messageInput.fill('Bonjour, j\'ai une question sur mon forfait'); await page.click('[data-testid="send-message-button"]'); // Verify message appears in chat await expect(page.locator('[data-testid="user-message"]').last()).toContainText('question sur mon forfait'); // Verify emergency button is still visible await expect(page.locator('[data-testid="emergency-call-button"]')).toBeVisible(); }); test('should maintain dashboard functionality', async ({ page }) => { await loginUser(page, 'customer'); // Test dashboard navigation await page.click('[data-testid="dashboard-nav-link"]'); await expect(page.locator('[data-testid="dashboard-content"]')).toBeVisible(); // Test analytics widgets await expect(page.locator('[data-testid="analytics-widget"]')).toBeVisible(); // Verify emergency call integration doesn't break dashboard await expect(page.locator('[data-testid="user-menu"]')).toBeVisible(); await expect(page.locator('[data-testid="notification-bell"]')).toBeVisible(); }); test('should preserve user authentication flow', async ({ page }) => { // Test login flow await page.goto(`${TEST_CONFIG.baseURL}/login`); await page.fill('[data-testid="email-input"]', TEST_USERS.customer.email); await page.fill('[data-testid="password-input"]', TEST_USERS.customer.password); await page.click('[data-testid="login-button"]'); // Verify successful login await page.waitForURL('**/dashboard'); await expect(page.locator('[data-testid="user-menu"]')).toBeVisible(); // Test logout await page.click('[data-testid="user-menu"]'); await page.click('[data-testid="logout-button"]'); await page.waitForURL('**/login'); // Verify logout successful await expect(page.locator('[data-testid="login-form"]')).toBeVisible(); }); }); });