import { test, expect, Page } from '@playwright/test'; // Test configuration const FRONTEND_URL = 'http://localhost:51692'; const BACKEND_URL = 'http://localhost:5000'; // Test credentials const TEST_CREDENTIALS = { admin: { email: '<EMAIL>', password: 'AdminPassword123!' }, user: { email: '<EMAIL>', password: 'UserPassword123!' }, agent: { email: '<EMAIL>', password: 'AgentPassword123!' }, invalid: { email: '<EMAIL>', password: 'wrongpassword' } }; test.describe('ChatbotRNCP Authentication Debug Tests', () => { let page: Page; test.beforeEach(async ({ browser }) => { page = await browser.newPage(); // Enable console logging page.on('console', msg => { console.log(` BROWSER CONSOLE [${msg.type()}]:`, msg.text()); }); // Monitor network requests page.on('request', request => { if (request.url().includes('/api/')) { console.log(` REQUEST: ${request.method()} ${request.url()}`); if (request.method() === 'POST') { console.log(` POST DATA:`, request.postData()); } } }); // Monitor network responses page.on('response', response => { if (response.url().includes('/api/')) { console.log(` RESPONSE: ${response.status()} ${response.url()}`); } }); // Monitor network failures page.on('requestfailed', request => { console.log(`[FAILED] REQUEST FAILED: ${request.url()} - ${request.failure()?.errorText}`); }); }); test('1. Verify Frontend and Backend Services are Running', async () => { console.log('\n[SEARCH] Testing service availability...'); // Test frontend accessibility console.log('Testing frontend at:', FRONTEND_URL); const frontendResponse = await page.goto(FRONTEND_URL); expect(frontendResponse?.status()).toBe(200); console.log('[COMPLETE] Frontend is accessible'); // Test backend health endpoint console.log('Testing backend health at:', `${BACKEND_URL}/health`); try { const healthResponse = await page.request.get(`${BACKEND_URL}/health`); expect(healthResponse.status()).toBe(200); const healthData = await healthResponse.json(); console.log('[COMPLETE] Backend health:', healthData); } catch (error) { console.log('[FAILED] Backend health check failed:', error); throw error; } }); test('2. Analyze Login Page Structure and Elements', async () => { console.log('\n[SEARCH] Analyzing login page structure...'); await page.goto(`${FRONTEND_URL}/login`); await page.waitForLoadState('networkidle'); // Check if login form exists const loginForm = page.locator('form'); await expect(loginForm).toBeVisible(); console.log('[COMPLETE] Login form is visible'); // Check email field const emailField = page.locator('input[type="email"], input[name="email"]'); await expect(emailField).toBeVisible(); console.log('[COMPLETE] Email field is visible'); // Check password field const passwordField = page.locator('input[type="password"], input[name="password"]'); await expect(passwordField).toBeVisible(); console.log('[COMPLETE] Password field is visible'); // Check submit button const submitButton = page.locator('button[type="submit"], button:has-text("SE CONNECTER")'); await expect(submitButton).toBeVisible(); console.log('[COMPLETE] Submit button is visible'); // Check for any existing error messages const errorMessage = page.locator('text=Erreur de connexion'); if (await errorMessage.isVisible()) { console.log(' Error message already visible on page load'); } // Take screenshot of login page await page.screenshot({ path: 'tests/screenshots/login-page-structure.png', fullPage: true }); console.log(' Screenshot saved: login-page-structure.png'); }); test('3. Test Admin Login Flow with Network Monitoring', async () => { console.log('\n[SEARCH] Testing admin login flow...'); await page.goto(`${FRONTEND_URL}/login`); await page.waitForLoadState('networkidle'); // Fill in admin credentials await page.fill('input[type="email"], input[name="email"]', TEST_CREDENTIALS.admin.email); await page.fill('input[type="password"], input[name="password"]', TEST_CREDENTIALS.admin.password); console.log(` Filled credentials: ${TEST_CREDENTIALS.admin.email}`); // Monitor the login request const loginRequestPromise = page.waitForRequest(request => request.url().includes('/api/auth/login') && request.method() === 'POST' ); const loginResponsePromise = page.waitForResponse(response => response.url().includes('/api/auth/login') ); // Take screenshot before submission await page.screenshot({ path: 'tests/screenshots/before-login-submit.png', fullPage: true }); // Submit the form await page.click('button[type="submit"], button:has-text("SE CONNECTER")'); console.log(' Login form submitted'); try { // Wait for network activity const loginRequest = await loginRequestPromise; const loginResponse = await loginResponsePromise; console.log(' Login request captured:', { url: loginRequest.url(), method: loginRequest.method(), headers: loginRequest.headers(), postData: loginRequest.postData() }); console.log(' Login response captured:', { status: loginResponse.status(), statusText: loginResponse.statusText(), headers: loginResponse.headers() }); const responseBody = await loginResponse.text(); console.log(' Response body:', responseBody); // Check if login was successful if (loginResponse.status() === 200) { console.log('[COMPLETE] Login request successful'); // Wait for potential redirect await page.waitForTimeout(2000); // Check current URL const currentUrl = page.url(); console.log(' Current URL after login:', currentUrl); // Check if redirected to dashboard or home if (currentUrl.includes('/dashboard') || currentUrl.includes('/home') || !currentUrl.includes('/login')) { console.log('[COMPLETE] Successfully redirected after login'); } else { console.log(' Still on login page after successful API call'); } } else { console.log('[FAILED] Login request failed with status:', loginResponse.status()); } } catch (error) { console.log('[FAILED] Login request/response monitoring failed:', error); // Check for error messages on the page const errorElements = await page.locator('[class*="error"], [class*="Error"], text=Erreur').all(); for (const element of errorElements) { const errorText = await element.textContent(); console.log(' Error message found:', errorText); } } // Take screenshot after submission await page.screenshot({ path: 'tests/screenshots/after-login-submit.png', fullPage: true }); console.log(' Screenshot saved: after-login-submit.png'); }); test('4. Test All User Roles Authentication', async () => { console.log('\n[SEARCH] Testing all user roles...'); for (const [role, credentials] of Object.entries(TEST_CREDENTIALS)) { if (role === 'invalid') continue; console.log(`\n[USER] Testing ${role} login...`); await page.goto(`${FRONTEND_URL}/login`); await page.waitForLoadState('networkidle'); await page.fill('input[type="email"], input[name="email"]', credentials.email); await page.fill('input[type="password"], input[name="password"]', credentials.password); const responsePromise = page.waitForResponse(response => response.url().includes('/api/auth/login'), { timeout: 10000 } ); await page.click('button[type="submit"], button:has-text("SE CONNECTER")'); try { const response = await responsePromise; console.log(` ${role} login response:`, response.status()); if (response.status() === 200) { const responseData = await response.json(); console.log(`[COMPLETE] ${role} login successful:`, { userId: responseData.user?.id, email: responseData.user?.email, role: responseData.user?.role, tokenReceived: !!responseData.token }); } else { const errorData = await response.text(); console.log(`[FAILED] ${role} login failed:`, errorData); } } catch (error) { console.log(`[FAILED] ${role} login timeout or error:`, error); } await page.waitForTimeout(1000); } }); test('5. Test Invalid Credentials Error Handling', async () => { console.log('\n[SEARCH] Testing invalid credentials...'); await page.goto(`${FRONTEND_URL}/login`); await page.waitForLoadState('networkidle'); await page.fill('input[type="email"], input[name="email"]', TEST_CREDENTIALS.invalid.email); await page.fill('input[type="password"], input[name="password"]', TEST_CREDENTIALS.invalid.password); const responsePromise = page.waitForResponse(response => response.url().includes('/api/auth/login') ); await page.click('button[type="submit"], button:has-text("SE CONNECTER")'); try { const response = await responsePromise; console.log(' Invalid login response status:', response.status()); const responseData = await response.text(); console.log(' Invalid login response:', responseData); // Check if error is properly displayed on frontend await page.waitForTimeout(1000); const errorMessage = page.locator('text=Erreur, text=Identifiants incorrects, [class*="error"]'); if (await errorMessage.isVisible()) { const errorText = await errorMessage.textContent(); console.log('[COMPLETE] Error message displayed:', errorText); } else { console.log(' No error message displayed for invalid credentials'); } } catch (error) { console.log('[FAILED] Invalid credentials test failed:', error); } await page.screenshot({ path: 'tests/screenshots/invalid-credentials.png', fullPage: true }); }); test('6. Debug CORS and Network Issues', async () => { console.log('\n[SEARCH] Testing CORS and network configuration...'); await page.goto(`${FRONTEND_URL}/login`); // Test direct API call from browser context try { const directApiCall = await page.evaluate(async () => { try { const response = await fetch('http://localhost:5000/api/auth/login', { method: 'POST', headers: { 'Content-Type': 'application/json', }, body: JSON.stringify({ email: '<EMAIL>', password: 'AdminPassword123!' }) }); return { status: response.status, statusText: response.statusText, headers: Object.fromEntries(response.headers.entries()), body: await response.text() }; } catch (error) { return { error: error.message, name: error.name }; } }); console.log(' Direct API call result:', directApiCall); if (directApiCall.error) { console.log('[FAILED] CORS or network issue detected:', directApiCall.error); } else { console.log('[COMPLETE] Direct API call successful'); } } catch (error) { console.log('[FAILED] Direct API test failed:', error); } }); test.afterEach(async () => { await page.close(); }); });