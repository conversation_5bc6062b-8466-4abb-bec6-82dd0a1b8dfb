const { test, expect } = require('@playwright/test'); /** * TESTS D'INTÉGRATION COMPLÈTE * Validation de tous les systèmes ensemble */ let userToken; let agentToken; let adminToken; let userId; let agentId; let conversationId; test.describe(' Tests d\'Intégration Complète', () => { test.beforeAll(async ({ request }) => { console.log('[DEPLOY] Initialisation des comptes de test...'); // Créer les différents types d'utilisateurs const accounts = [ { email: '<EMAIL>', role: 'user', firstName: 'Test', lastName: 'User' }, { email: '<EMAIL>', role: 'agent', firstName: 'Test', lastName: 'Agent' }, { email: '<EMAIL>', role: 'admin', firstName: 'Test', lastName: 'Admin' } ]; const tokens = {}; const userIds = {}; for (const account of accounts) { const response = await request.post('/api/auth/register', { data: { email: account.email, password: 'IntegrationTest123!', firstName: account.firstName, lastName: account.lastName } }); expect(response.ok()).toBeTruthy(); const data = await response.json(); tokens[account.role] = data.token; userIds[account.role] = data.user.id; } userToken = tokens.user; agentToken = tokens.agent; adminToken = tokens.admin; userId = userIds.user; agentId = userIds.agent; console.log('[COMPLETE] Comptes de test créés avec succès'); }); test.describe(' Workflow Complet Utilisateur → Bot → Agent', () => { test('Scenario 1: Support Client Complet', async ({ request }) => { console.log('[MOBILE] Test du workflow complet de support client...'); // ÉTAPE 1: Utilisateur démarre une conversation console.log(' [USER] Utilisateur démarre une conversation...'); const startResponse = await request.post('/api/chat/conversations/start', { headers: { 'Authorization': `Bearer ${userToken}` }, data: { channel: 'web', location: { latitude: 48.8566, longitude: 2.3522, city: 'Paris', country: 'France' } } }); expect(startResponse.ok()).toBeTruthy(); const startData = await startResponse.json(); conversationId = startData.conversationId; expect(startData.success).toBe(true); expect(startData.welcomeMessage).toContain('Free Mobile'); expect(startData.channel).toBe('web'); // ÉTAPE 2: Utilisateur envoie un message de problème console.log(' Utilisateur signale un problème...'); const problemMessage = "Bonjour, j'ai un gros problème avec ma facture. Elle est beaucoup trop élevée ce mois-ci et je ne comprends pas pourquoi!"; const messageResponse = await request.post('/api/chat/messages/send', { headers: { 'Authorization': `Bearer ${userToken}` }, data: { conversationId: conversationId, message: problemMessage } }); expect(messageResponse.ok()).toBeTruthy(); const messageData = await messageResponse.json(); expect(messageData.success).toBe(true); expect(messageData.response.text).toBeDefined(); expect(messageData.response.text.length).toBeGreaterThan(0); // Vérifier que les modes intelligents fonctionnent if (messageData.metadata?.intelligentFeaturesUsed) { expect(messageData.response.mode).toBeDefined(); expect(['conversational', 'guided', 'hybrid', 'proactive']).toContain(messageData.response.mode); // Si sentiment détecté, devrait être négatif (problème) if (messageData.response.sentiment) { expect(['frustration', 'urgence']).toContain(messageData.response.sentiment.emotion); } } // ÉTAPE 3: Bot fournit une réponse avec options console.log(' [AI] Bot analyse et répond...'); expect(messageData.response.text.toLowerCase()).toMatch(/(facture|problème|aide|comprend)/); // ÉTAPE 4: Escalade vers un agent (simulation) console.log(' Escalade vers un agent...'); const escalateResponse = await request.post(`/api/agent/conversations/${conversationId}/take-control`, { headers: { 'Authorization': `Bearer ${agentToken}` } }); // Peut échouer si les permissions ne sont pas correctement définies if (escalateResponse.ok()) { const escalateData = await escalateResponse.json(); expect(escalateData.success).toBe(true); // ÉTAPE 5: Agent envoie une réponse personnalisée console.log(' [ADMIN] Agent répond au client...'); const agentResponse = await request.post(`/api/agent/conversations/${conversationId}/messages`, { headers: { 'Authorization': `Bearer ${agentToken}` }, data: { message: "Bonjour ! Je comprends votre frustration concernant votre facture. Je vais examiner votre compte en détail et vous expliquer les charges.", messageType: 'text' } }); if (agentResponse.ok()) { const agentData = await agentResponse.json(); expect(agentData.success).toBe(true); } // ÉTAPE 6: Résolution et fermeture console.log(' [COMPLETE] Agent résout et ferme la conversation...'); const closeResponse = await request.post(`/api/agent/conversations/${conversationId}/close`, { headers: { 'Authorization': `Bearer ${agentToken}` }, data: { resolutionNote: 'Problème de facturation expliqué et résolu. Client satisfait.', satisfactionRequested: true } }); if (closeResponse.ok()) { const closeData = await closeResponse.json(); expect(closeData.success).toBe(true); } } else { console.log(' Escalade échouée - permissions insuffisantes'); } // ÉTAPE 7: Vérification de l'historique console.log(' Vérification de l\'historique...'); const historyResponse = await request.get(`/api/chat/conversations/${conversationId}`, { headers: { 'Authorization': `Bearer ${userToken}` } }); expect(historyResponse.ok()).toBeTruthy(); const historyData = await historyResponse.json(); expect(historyData.conversation).toBeDefined(); expect(historyData.messages).toBeDefined(); expect(historyData.messages.length).toBeGreaterThan(0); console.log('[COMPLETE] Workflow complet testé avec succès'); }); }); test.describe('[SECURITY] Intégration Sécurité Complète', () => { test('Scenario 2: Authentification Sécurisée avec 2FA', async ({ request }) => { console.log('[SECURITY] Test du workflow de sécurité complet...'); // ÉTAPE 1: Vérifier le statut de sécurité initial console.log(' [ANALYTICS] Vérification du statut de sécurité...'); const statusResponse = await request.get('/api/security/status', { headers: { 'Authorization': `Bearer ${userToken}` } }); expect(statusResponse.ok()).toBeTruthy(); const statusData = await statusResponse.json(); expect(statusData.success).toBe(true); expect(statusData.securityStatus.twoFactorEnabled).toBe(false); expect(statusData.securityStatus.securityRecommendations.length).toBeGreaterThan(0); // ÉTAPE 2: Générer un secret 2FA console.log(' Génération du secret 2FA...'); const generateResponse = await request.get('/api/security/2fa/generate', { headers: { 'Authorization': `Bearer ${userToken}` } }); expect(generateResponse.ok()).toBeTruthy(); const generateData = await generateResponse.json(); expect(generateData.success).toBe(true); expect(generateData.data.qrCode).toBeDefined(); expect(generateData.data.backupCodes).toHaveLength(10); // ÉTAPE 3: Analyser une tentative de connexion console.log(' [SEARCH] Analyse de sécurité de connexion...'); const analyzeResponse = await request.post('/api/security/analyze-login', { headers: { 'Authorization': `Bearer ${userToken}` }, data: { userId: userId, location: { latitude: 48.8566, longitude: 2.3522, city: 'Paris', country: 'France' } } }); expect(analyzeResponse.ok()).toBeTruthy(); const analyzeData = await analyzeResponse.json(); expect(analyzeData.success).toBe(true); expect(analyzeData.analysis.riskScore).toBeDefined(); expect(analyzeData.analysis.riskLevel).toMatch(/^(low|medium|high)$/); // ÉTAPE 4: Vérifier les logs de sécurité console.log(' Consultation des logs de sécurité...'); const logsResponse = await request.get('/api/security/logs', { headers: { 'Authorization': `Bearer ${userToken}` } }); expect(logsResponse.ok()).toBeTruthy(); const logsData = await logsResponse.json(); expect(logsData.success).toBe(true); expect(Array.isArray(logsData.logs)).toBe(true); console.log('[COMPLETE] Workflow de sécurité testé avec succès'); }); }); test.describe('[TARGET] Performance et Charge', () => { test('Scenario 3: Test de Charge Simultanée', async ({ request }) => { console.log('[PERFORMANCE] Test de performance sous charge...'); const concurrentRequests = 10; const requests = []; // Créer des requêtes simultanées for (let i = 0; i < concurrentRequests; i++) { requests.push( request.post('/api/chat/conversations/start', { headers: { 'Authorization': `Bearer ${userToken}` }, data: { channel: 'web' } }) ); } const startTime = Date.now(); const responses = await Promise.all(requests); const endTime = Date.now(); const totalTime = endTime - startTime; const avgTimePerRequest = totalTime / concurrentRequests; console.log(` [ANALYTICS] ${concurrentRequests} requêtes en ${totalTime}ms (${avgTimePerRequest.toFixed(2)}ms/req)`); // Vérifier que toutes les requêtes ont réussi responses.forEach(response => { expect(response.ok()).toBeTruthy(); }); // Performance acceptable expect(avgTimePerRequest).toBeLessThan(2000); // Moins de 2s par requête expect(totalTime).toBeLessThan(10000); // Moins de 10s au total console.log('[COMPLETE] Test de charge réussi'); }); test('Scenario 4: Test de Débit Messages', async ({ request }) => { console.log(' Test de débit de messages...'); // Créer une conversation const convResponse = await request.post('/api/chat/conversations/start', { headers: { 'Authorization': `Bearer ${userToken}` }, data: { channel: 'web' } }); expect(convResponse.ok()).toBeTruthy(); const convData = await convResponse.json(); const testConversationId = convData.conversationId; // Envoyer plusieurs messages rapidement const messageCount = 5; const messageRequests = []; for (let i = 0; i < messageCount; i++) { messageRequests.push( request.post('/api/chat/messages/send', { headers: { 'Authorization': `Bearer ${userToken}` }, data: { conversationId: testConversationId, message: `Message de test ${i + 1}` } }) ); } const startTime = Date.now(); const messageResponses = await Promise.all(messageRequests); const endTime = Date.now(); const totalTime = endTime - startTime; const msgPerSecond = (messageCount / totalTime) * 1000; console.log(` [ANALYTICS] ${messageCount} messages en ${totalTime}ms (${msgPerSecond.toFixed(2)} msg/s)`); // Vérifier que tous les messages ont été traités messageResponses.forEach(response => { expect(response.ok()).toBeTruthy(); }); console.log('[COMPLETE] Test de débit réussi'); }); }); test.describe(' Tests de Sécurité Défensive', () => { test('Scenario 5: Tentatives d\'Attaque', async ({ request }) => { console.log(' Test de résistance aux attaques...'); // Test d'injection SQL console.log(' Test injection SQL...'); const sqlInjectionAttempts = [ "'; DROP TABLE users; --", "1' OR '1'='1", "admin'; DELETE FROM messages; --" ]; for (const maliciousInput of sqlInjectionAttempts) { const response = await request.post('/api/chat/messages/send', { headers: { 'Authorization': `Bearer ${userToken}` }, data: { conversationId: conversationId || '507f1f77bcf86cd799439011', message: maliciousInput } }); // Devrait soit rejeter soit nettoyer l'entrée if (response.ok()) { const data = await response.json(); expect(data.response.text).not.toContain('DROP TABLE'); expect(data.response.text).not.toContain('DELETE FROM'); } else { expect(response.status()).toBe(400); } } // Test XSS console.log(' Test XSS...'); const xssAttempts = [ '<script>alert("xss")</script>', '<img src=x onerror=alert("xss")>', 'javascript:alert("xss")' ]; for (const xssInput of xssAttempts) { const response = await request.post('/api/chat/messages/send', { headers: { 'Authorization': `Bearer ${userToken}` }, data: { conversationId: conversationId || '507f1f77bcf86cd799439011', message: xssInput } }); if (response.ok()) { const data = await response.json(); expect(data.response.text).not.toContain('<script>'); expect(data.response.text).not.toContain('javascript:'); } else { expect(response.status()).toBe(400); } } // Test de rate limiting console.log(' Test rate limiting...'); const rateLimitRequests = []; for (let i = 0; i < 15; i++) { rateLimitRequests.push( request.post('/api/auth/login', { data: { email: '<EMAIL>', password: 'wrongpassword' } }) ); } const rateLimitResponses = await Promise.all(rateLimitRequests); const rateLimitedCount = rateLimitResponses.filter(r => r.status() === 429).length; expect(rateLimitedCount).toBeGreaterThan(0); console.log(` [ANALYTICS] ${rateLimitedCount} requêtes bloquées par rate limiting`); console.log('[COMPLETE] Tests de sécurité défensive réussis'); }); }); test.describe(' Compatibilité et Régression', () => { test('Scenario 6: Compatibilité API', async ({ request }) => { console.log(' Test de compatibilité API...'); // Vérifier que les endpoints de base fonctionnent toujours const endpoints = [ { method: 'GET', path: '/health', needsAuth: false }, { method: 'GET', path: '/api/security/status', needsAuth: true }, { method: 'POST', path: '/api/chat/conversations/start', needsAuth: true, data: { channel: 'web' } } ]; for (const endpoint of endpoints) { const options = { data: endpoint.data }; if (endpoint.needsAuth) { options.headers = { 'Authorization': `Bearer ${userToken}` }; } const response = await request[endpoint.method.toLowerCase()](endpoint.path, options); expect([200, 201]).toContain(response.status()); const data = await response.json(); expect(data).toBeDefined(); console.log(` [COMPLETE] ${endpoint.method} ${endpoint.path} - OK`); } console.log('[COMPLETE] Compatibilité API vérifiée'); }); }); test.describe('[ANALYTICS] Métriques et Analytics', () => { test('Scenario 7: Collecte de Métriques', async ({ request }) => { console.log('[ANALYTICS] Test de collecte de métriques...'); // Créer une conversation avec messages const convResponse = await request.post('/api/chat/conversations/start', { headers: { 'Authorization': `Bearer ${userToken}` }, data: { channel: 'web' } }); expect(convResponse.ok()).toBeTruthy(); const convData = await convResponse.json(); const metricsConversationId = convData.conversationId; // Envoyer des messages avec différents types const testMessages = [ "Bonjour", "J'ai un problème avec ma facture", "Merci pour votre aide" ]; const messageMetrics = []; for (const message of testMessages) { const startTime = Date.now(); const response = await request.post('/api/chat/messages/send', { headers: { 'Authorization': `Bearer ${userToken}` }, data: { conversationId: metricsConversationId, message: message } }); const endTime = Date.now(); expect(response.ok()).toBeTruthy(); const data = await response.json(); messageMetrics.push({ message, responseTime: endTime - startTime, mode: data.response?.mode, processingTime: data.metadata?.processingTime, intelligentFeaturesUsed: data.metadata?.intelligentFeaturesUsed }); } // Analyser les métriques const avgResponseTime = messageMetrics.reduce((sum, m) => sum + m.responseTime, 0) / messageMetrics.length; const intelligentModesUsed = messageMetrics.filter(m => m.intelligentFeaturesUsed).length; console.log(` [ANALYTICS] Temps de réponse moyen: ${avgResponseTime.toFixed(2)}ms`); console.log(` [ANALYTICS] Modes intelligents utilisés: ${intelligentModesUsed}/${messageMetrics.length}`); // Vérifications de performance expect(avgResponseTime).toBeLessThan(3000); // Moins de 3s en moyenne console.log('[COMPLETE] Métriques collectées avec succès'); }); }); }); test.describe('[TARGET] Tests de Validation Finale', () => { test('[COMPLETE] Validation Complète du Système', async ({ request }) => { console.log('[TARGET] VALIDATION FINALE DU SYSTÈME COMPLET'); const validationResults = { authentication: false, chatFunctionality: false, intelligentModes: false, security: false, agentInterface: false, performance: false }; try { // Test 1: Authentification console.log(' [SECURITY] Test authentification...'); const authResponse = await request.post('/api/auth/login', { data: { email: '<EMAIL>', password: 'IntegrationTest123!' } }); validationResults.authentication = authResponse.ok(); // Test 2: Fonctionnalité de chat console.log(' Test chat...'); const convResponse = await request.post('/api/chat/conversations/start', { headers: { 'Authorization': `Bearer ${userToken}` }, data: { channel: 'web' } }); if (convResponse.ok()) { const convData = await convResponse.json(); const msgResponse = await request.post('/api/chat/messages/send', { headers: { 'Authorization': `Bearer ${userToken}` }, data: { conversationId: convData.conversationId, message: 'Test de validation finale' } }); validationResults.chatFunctionality = msgResponse.ok(); // Test 3: Modes intelligents if (msgResponse.ok()) { const msgData = await msgResponse.json(); validationResults.intelligentModes = msgData.metadata?.intelligentFeaturesUsed || false; } } // Test 4: Sécurité console.log(' Test sécurité...'); const securityResponse = await request.get('/api/security/status', { headers: { 'Authorization': `Bearer ${userToken}` } }); validationResults.security = securityResponse.ok(); // Test 5: Interface agent console.log(' Test interface agent...'); const agentResponse = await request.get('/api/agent/dashboard', { headers: { 'Authorization': `Bearer ${agentToken}` } }); validationResults.agentInterface = [200, 403].includes(agentResponse.status()); // Test 6: Performance console.log(' [PERFORMANCE] Test performance...'); const startTime = Date.now(); const perfResponse = await request.get('/health'); const endTime = Date.now(); validationResults.performance = perfResponse.ok() && (endTime - startTime) < 1000; } catch (error) { console.error('Erreur lors de la validation:', error); } // Affichage des résultats console.log('\n RÉSULTATS DE VALIDATION:'); Object.entries(validationResults).forEach(([test, result]) => { const status = result ? '[COMPLETE] PASS' : '[FAILED] FAIL'; console.log(` ${test}: ${status}`); expect(result).toBe(true); }); const successRate = Object.values(validationResults).filter(r => r).length / Object.keys(validationResults).length; console.log(`\n[TARGET] TAUX DE RÉUSSITE: ${(successRate * 100).toFixed(1)}%`); expect(successRate).toBeGreaterThan(0.8); // Au moins 80% de réussite console.log('\n[DEPLOY] SYSTÈME VALIDÉ POUR PRODUCTION!'); }); });