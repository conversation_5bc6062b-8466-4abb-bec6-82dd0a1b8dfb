/** * ============================================= * PLAYWRIGHT CONFIGURATION * Comprehensive E2E Testing for AI Services & Production Infrastructure * Cross-browser, Accessibility, Performance Testing * ============================================= */ import { defineConfig, devices } from '@playwright/test'; /** * See https://playwright.dev/docs/test-configuration. */ export default defineConfig({ testDir: './e2e', /* Run tests in files in parallel */ fullyParallel: true, /* Fail the build on CI if you accidentally left test.only in the source code. */ forbidOnly: !!process.env.CI, /* Retry on CI only */ retries: process.env.CI ? 2 : 0, /* Opt out of parallel tests on CI. */ workers: process.env.CI ? 1 : undefined, /* Reporter to use. See https://playwright.dev/docs/test-reporters */ reporter: [ ['html', { outputFolder: 'test-results/html-report' }], ['json', { outputFile: 'test-results/results.json' }], ['junit', { outputFile: 'test-results/junit.xml' }], ['allure-playwright', { outputFolder: 'test-results/allure-results' }] ], /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */ use: { /* Base URL to use in actions like `await page.goto('/')`. */ baseURL: process.env.BASE_URL || 'http://localhost:51692', /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */ trace: 'on-first-retry', /* Take screenshot on failure */ screenshot: 'only-on-failure', /* Record video on failure */ video: 'retain-on-failure', /* Global timeout for each action */ actionTimeout: 30000, /* Global timeout for navigation */ navigationTimeout: 30000, /* Ignore HTTPS errors */ ignoreHTTPSErrors: true, /* Extra HTTP headers */ extraHTTPHeaders: { 'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8' } }, /* Configure projects for major browsers */ projects: [ // Desktop Browsers { name: 'chromium', use: { ...devices['Desktop Chrome'], viewport: { width: 1920, height: 1080 } }, }, { name: 'firefox', use: { ...devices['Desktop Firefox'], viewport: { width: 1920, height: 1080 } }, }, { name: 'webkit', use: { ...devices['Desktop Safari'], viewport: { width: 1920, height: 1080 } }, }, { name: 'edge', use: { ...devices['Desktop Edge'], viewport: { width: 1920, height: 1080 } }, }, // Mobile Devices { name: 'Mobile Chrome', use: { ...devices['Pixel 5'] }, }, { name: 'Mobile Safari', use: { ...devices['iPhone 12'] }, }, // Tablet Devices { name: 'Tablet Chrome', use: { ...devices['iPad Pro'] }, }, // Accessibility Testing { name: 'accessibility', use: { ...devices['Desktop Chrome'], viewport: { width: 1920, height: 1080 } }, testMatch: '**/accessibility/**/*.spec.ts' }, // Performance Testing { name: 'performance', use: { ...devices['Desktop Chrome'], viewport: { width: 1920, height: 1080 } }, testMatch: '**/performance/**/*.spec.ts' }, // API Testing { name: 'api', use: { baseURL: process.env.API_BASE_URL || 'http://localhost:5000' }, testMatch: '**/api/**/*.spec.ts' } ], /* Global setup and teardown */ globalSetup: require.resolve('./global-setup.ts'), globalTeardown: require.resolve('./global-teardown.ts'), /* Test timeout */ timeout: 60000, /* Expect timeout */ expect: { timeout: 10000, toHaveScreenshot: { mode: 'strict', threshold: 0.2 } }, /* Run your local dev server before starting the tests */ webServer: [ { command: 'npm run start:backend', port: 5000, reuseExistingServer: !process.env.CI, timeout: 120000 }, { command: 'npm run start:ml-service', port: 5001, reuseExistingServer: !process.env.CI, timeout: 120000 }, { command: 'npm run start:frontend', port: 3001, reuseExistingServer: !process.env.CI, timeout: 120000 } ], /* Test directories */ testIgnore: [ '**/node_modules/**', '**/dist/**', '**/build/**' ], /* Output directory */ outputDir: 'test-results/artifacts', /* Metadata */ metadata: { 'test-suite': 'Free Mobile Chatbot AI Services E2E Tests', 'version': '1.0.0', 'environment': process.env.NODE_ENV || 'test', 'timestamp': new Date().toISOString() } }); /* Environment-specific configurations */ if (process.env.NODE_ENV === 'production') { // Production-specific settings module.exports.use.baseURL = 'https://chatbot.free.fr'; module.exports.use.ignoreHTTPSErrors = false; module.exports.retries = 3; module.exports.workers = 2; } if (process.env.NODE_ENV === 'staging') { // Staging-specific settings module.exports.use.baseURL = 'https://staging-chatbot.free.fr'; module.exports.retries = 2; } /* Test tags configuration */ export const testTags = { smoke: '@smoke', regression: '@regression', ai: '@ai', infrastructure: '@infrastructure', performance: '@performance', accessibility: '@accessibility', security: '@security', mobile: '@mobile', api: '@api' }; /* Test data configuration */ export const testConfig = { users: { admin: { email: '<EMAIL>', password: 'TestAdmin123!', role: 'admin' }, agent: { email: '<EMAIL>', password: 'TestAgent123!', role: 'agent' }, customer: { email: '<EMAIL>', password: 'TestCustomer123!', role: 'customer' } }, api: { timeout: 30000, retries: 3, baseURL: process.env.API_BASE_URL || 'http://localhost:5000' }, ml: { timeout: 60000, confidenceThreshold: 0.8, baseURL: process.env.ML_BASE_URL || 'http://localhost:5001' }, performance: { lcp: 2500, // Largest Contentful Paint (ms) fid: 100, // First Input Delay (ms) cls: 0.1, // Cumulative Layout Shift tti: 3800 // Time to Interactive (ms) }, accessibility: { standard: 'WCAG21AA', tags: ['wcag2a', 'wcag2aa', 'wcag21aa'] } }; /* Custom test fixtures */ export interface TestFixtures { authenticatedPage: any; adminPage: any; agentPage: any; customerPage: any; apiContext: any; mlContext: any; } /* Test utilities */ export const testUtils = { generateTestId: () => `test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`, waitForStableNetwork: async (page: any) => { await page.waitForLoadState('networkidle'); await page.waitForTimeout(1000); }, takeScreenshot: async (page: any, name: string) => { await page.screenshot({ path: `test-results/screenshots/${name}-${Date.now()}.png`, fullPage: true }); } };