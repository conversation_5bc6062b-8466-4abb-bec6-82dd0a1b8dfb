#!/bin/bash

# =============================================
# 🧪 COMPREHENSIVE TEST RUNNER SCRIPT
# Execute all test suites with proper reporting
# =============================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
TEST_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
PROJECT_ROOT="$(cd "$TEST_DIR/.." && pwd)"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
RESULTS_DIR="$TEST_DIR/test-results"
REPORTS_DIR="$RESULTS_DIR/reports"

# Default values
BROWSER="chromium"
HEADED=false
WORKERS=1
TIMEOUT=60000
RETRIES=2
TAGS=""
ENVIRONMENT="test"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS] [TEST_SUITE]

Test Suites:
  all                 Run all test suites (default)
  ai-services         Run AI services tests only
  infrastructure      Run infrastructure tests only
  analytics          Run analytics dashboard tests only
  security           Run security tests only
  performance        Run performance tests only
  accessibility      Run accessibility tests only
  smoke              Run smoke tests only
  regression         Run regression tests only

Options:
  -b, --browser BROWSER    Browser to use (chromium, firefox, webkit, edge)
  -h, --headed            Run tests in headed mode
  -w, --workers NUM       Number of parallel workers (default: 1)
  -t, --timeout MS        Test timeout in milliseconds (default: 60000)
  -r, --retries NUM       Number of retries on failure (default: 2)
  -e, --environment ENV   Environment (test, staging, production)
  --tags TAGS            Run tests with specific tags (@smoke, @regression, etc.)
  --help                 Show this help message

Examples:
  $0                                    # Run all tests
  $0 smoke                             # Run smoke tests only
  $0 ai-services --browser firefox     # Run AI tests in Firefox
  $0 --headed --workers 2              # Run all tests headed with 2 workers
  $0 --tags "@smoke,@ai"               # Run tests with smoke and ai tags

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -b|--browser)
            BROWSER="$2"
            shift 2
            ;;
        -h|--headed)
            HEADED=true
            shift
            ;;
        -w|--workers)
            WORKERS="$2"
            shift 2
            ;;
        -t|--timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        -r|--retries)
            RETRIES="$2"
            shift 2
            ;;
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --tags)
            TAGS="$2"
            shift 2
            ;;
        --help)
            show_usage
            exit 0
            ;;
        -*)
            print_error "Unknown option $1"
            show_usage
            exit 1
            ;;
        *)
            TEST_SUITE="$1"
            shift
            ;;
    esac
done

# Set default test suite
TEST_SUITE=${TEST_SUITE:-"all"}

# Validate browser
case $BROWSER in
    chromium|firefox|webkit|edge)
        ;;
    *)
        print_error "Invalid browser: $BROWSER"
        print_error "Valid browsers: chromium, firefox, webkit, edge"
        exit 1
        ;;
esac

# Create results directories
mkdir -p "$RESULTS_DIR"
mkdir -p "$REPORTS_DIR"
mkdir -p "$RESULTS_DIR/screenshots"
mkdir -p "$RESULTS_DIR/videos"
mkdir -p "$RESULTS_DIR/traces"

print_status "Starting Free Mobile Chatbot AI Services Test Suite"
print_status "=============================================="
print_status "Test Suite: $TEST_SUITE"
print_status "Browser: $BROWSER"
print_status "Environment: $ENVIRONMENT"
print_status "Workers: $WORKERS"
print_status "Headed: $HEADED"
print_status "Timeout: ${TIMEOUT}ms"
print_status "Retries: $RETRIES"
print_status "Results Directory: $RESULTS_DIR"
print_status "=============================================="

# Change to project root
cd "$PROJECT_ROOT"

# Set environment variables
export NODE_ENV="$ENVIRONMENT"
export PWTEST_BROWSER="$BROWSER"
export PWTEST_HEADED="$HEADED"
export PWTEST_WORKERS="$WORKERS"
export PWTEST_TIMEOUT="$TIMEOUT"
export PWTEST_RETRIES="$RETRIES"

# Build Playwright command
PLAYWRIGHT_CMD="npx playwright test"

# Add browser selection
PLAYWRIGHT_CMD="$PLAYWRIGHT_CMD --project=$BROWSER"

# Add headed mode
if [ "$HEADED" = true ]; then
    PLAYWRIGHT_CMD="$PLAYWRIGHT_CMD --headed"
fi

# Add workers
PLAYWRIGHT_CMD="$PLAYWRIGHT_CMD --workers=$WORKERS"

# Add timeout
PLAYWRIGHT_CMD="$PLAYWRIGHT_CMD --timeout=$TIMEOUT"

# Add retries
PLAYWRIGHT_CMD="$PLAYWRIGHT_CMD --retries=$RETRIES"

# Add test pattern based on suite
case $TEST_SUITE in
    "ai-services")
        PLAYWRIGHT_CMD="$PLAYWRIGHT_CMD tests/e2e/ai-services/"
        ;;
    "infrastructure")
        PLAYWRIGHT_CMD="$PLAYWRIGHT_CMD tests/e2e/infrastructure/"
        ;;
    "analytics")
        PLAYWRIGHT_CMD="$PLAYWRIGHT_CMD tests/e2e/analytics/"
        ;;
    "security")
        PLAYWRIGHT_CMD="$PLAYWRIGHT_CMD tests/e2e/security/"
        ;;
    "performance")
        PLAYWRIGHT_CMD="$PLAYWRIGHT_CMD tests/e2e/performance/"
        ;;
    "accessibility")
        PLAYWRIGHT_CMD="$PLAYWRIGHT_CMD tests/e2e/accessibility/"
        ;;
    "smoke")
        PLAYWRIGHT_CMD="$PLAYWRIGHT_CMD --grep @smoke"
        ;;
    "regression")
        PLAYWRIGHT_CMD="$PLAYWRIGHT_CMD --grep @regression"
        ;;
    "all")
        PLAYWRIGHT_CMD="$PLAYWRIGHT_CMD tests/e2e/"
        ;;
    *)
        print_error "Unknown test suite: $TEST_SUITE"
        show_usage
        exit 1
        ;;
esac

# Add tags filter if specified
if [ -n "$TAGS" ]; then
    PLAYWRIGHT_CMD="$PLAYWRIGHT_CMD --grep \"$TAGS\""
fi

# Function to check if services are running
check_services() {
    print_status "Checking if required services are running..."
    
    local services=(
        "http://localhost:5000/health:Backend API"
        "http://localhost:5001/health:ML Service"
        "http://localhost:3001:Frontend"
    )
    
    for service in "${services[@]}"; do
        local url="${service%:*}"
        local name="${service#*:}"
        
        if curl -f -s "$url" > /dev/null 2>&1; then
            print_success "$name is running"
        else
            print_warning "$name is not responding at $url"
        fi
    done
}

# Function to start services if needed
start_services() {
    print_status "Starting required services..."
    
    # Check if Docker Compose is available
    if command -v docker-compose &> /dev/null; then
        print_status "Starting services with Docker Compose..."
        docker-compose -f docker-compose.test.yml up -d
        
        # Wait for services to be ready
        print_status "Waiting for services to be ready..."
        sleep 30
        
        check_services
    else
        print_warning "Docker Compose not found. Please start services manually."
        print_warning "Required services:"
        print_warning "  - Backend API (port 5000)"
        print_warning "  - ML Service (port 5001)"
        print_warning "  - Frontend (port 3001)"
    fi
}

# Function to generate test report
generate_report() {
    print_status "Generating comprehensive test report..."
    
    local report_file="$REPORTS_DIR/test-report-$TIMESTAMP.html"
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Free Mobile Chatbot AI Services - Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .summary { display: flex; gap: 20px; margin: 20px 0; }
        .metric { background: #e8f4fd; padding: 15px; border-radius: 5px; flex: 1; text-align: center; }
        .passed { color: #28a745; }
        .failed { color: #dc3545; }
        .skipped { color: #ffc107; }
        .section { margin: 20px 0; }
        .test-suite { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Free Mobile Chatbot AI Services - Test Report</h1>
        <p><strong>Generated:</strong> $(date)</p>
        <p><strong>Test Suite:</strong> $TEST_SUITE</p>
        <p><strong>Browser:</strong> $BROWSER</p>
        <p><strong>Environment:</strong> $ENVIRONMENT</p>
    </div>
    
    <div class="section">
        <h2>Test Execution Summary</h2>
        <div class="summary">
            <div class="metric">
                <h3>Total Tests</h3>
                <p id="total-tests">-</p>
            </div>
            <div class="metric">
                <h3 class="passed">Passed</h3>
                <p id="passed-tests">-</p>
            </div>
            <div class="metric">
                <h3 class="failed">Failed</h3>
                <p id="failed-tests">-</p>
            </div>
            <div class="metric">
                <h3 class="skipped">Skipped</h3>
                <p id="skipped-tests">-</p>
            </div>
        </div>
    </div>
    
    <div class="section">
        <h2>Test Suites</h2>
        <div class="test-suite">
            <h3>🤖 AI Services Tests</h3>
            <p>Message Suggestions, Auto-Response, Intelligent Routing, Sentiment Escalation</p>
        </div>
        <div class="test-suite">
            <h3>🏗️ Infrastructure Tests</h3>
            <p>Health Monitoring, Multi-Platform Integration</p>
        </div>
        <div class="test-suite">
            <h3>📊 Analytics Tests</h3>
            <p>Predictive Dashboard, Real-time Updates</p>
        </div>
        <div class="test-suite">
            <h3>🔒 Security Tests</h3>
            <p>Authentication, Authorization, Data Protection</p>
        </div>
        <div class="test-suite">
            <h3>⚡ Performance Tests</h3>
            <p>Core Web Vitals, Load Times, Resource Usage</p>
        </div>
        <div class="test-suite">
            <h3>♿ Accessibility Tests</h3>
            <p>WCAG 2.1 AA Compliance, Keyboard Navigation, Screen Reader Support</p>
        </div>
    </div>
    
    <div class="section">
        <h2>Detailed Results</h2>
        <p>For detailed test results, please check:</p>
        <ul>
            <li><a href="html-report/index.html">HTML Report</a></li>
            <li><a href="results.json">JSON Results</a></li>
            <li><a href="junit.xml">JUnit XML</a></li>
        </ul>
    </div>
</body>
</html>
EOF
    
    print_success "Test report generated: $report_file"
}

# Function to cleanup
cleanup() {
    print_status "Cleaning up..."
    
    # Stop services if we started them
    if [ "$STARTED_SERVICES" = true ]; then
        print_status "Stopping test services..."
        docker-compose -f docker-compose.test.yml down
    fi
}

# Set trap for cleanup
trap cleanup EXIT

# Main execution
main() {
    # Check if services are running
    check_services
    
    # Start services if needed (optional)
    # start_services
    # STARTED_SERVICES=true
    
    print_status "Executing test command: $PLAYWRIGHT_CMD"
    
    # Run the tests
    if eval "$PLAYWRIGHT_CMD"; then
        print_success "All tests completed successfully!"
        TEST_EXIT_CODE=0
    else
        print_error "Some tests failed!"
        TEST_EXIT_CODE=1
    fi
    
    # Generate report
    generate_report
    
    # Show results summary
    print_status "=============================================="
    print_status "Test Execution Complete"
    print_status "=============================================="
    print_status "Results available in: $RESULTS_DIR"
    print_status "HTML Report: $RESULTS_DIR/html-report/index.html"
    print_status "Screenshots: $RESULTS_DIR/screenshots/"
    print_status "Videos: $RESULTS_DIR/videos/"
    print_status "=============================================="
    
    return $TEST_EXIT_CODE
}

# Execute main function
main "$@"
