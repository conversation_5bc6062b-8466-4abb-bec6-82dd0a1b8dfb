const { test, expect } = require('@playwright/test'); /** * TESTS DE SÉCURITÉ ET AUTHENTIFICATION 2FA * Validation complète des nouvelles fonctionnalités de sécurité */ let authToken; let userId; let userEmail = '<EMAIL>'; let userPassword = 'SecurePassword123!'; test.describe(' Tests de Sécurité et 2FA', () => { test.beforeAll(async ({ request }) => { // Créer un utilisateur de test const registerResponse = await request.post('/api/auth/register', { data: { email: userEmail, password: userPassword, firstName: 'Test', lastName: 'Security' } }); expect(registerResponse.ok()).toBeTruthy(); const registerData = await registerResponse.json(); authToken = registerData.token; userId = registerData.user.id; }); test.describe('[ANALYTICS] Statut de Sécurité', () => { test('Devrait obtenir le statut de sécurité initial', async ({ request }) => { const response = await request.get('/api/security/status', { headers: { 'Authorization': `Bearer ${authToken}` } }); expect(response.ok()).toBeTruthy(); const data = await response.json(); expect(data.success).toBe(true); expect(data.securityStatus).toBeDefined(); expect(data.securityStatus.twoFactorEnabled).toBe(false); expect(data.securityStatus.securityRecommendations).toContainEqual( expect.objectContaining({ type: 'enable_2fa', priority: 'high' }) ); }); }); test.describe('[SECURITY] Authentification 2FA', () => { let qrCode; let backupCodes; let twoFactorSecret; test('Devrait générer un secret 2FA', async ({ request }) => { const response = await request.get('/api/security/2fa/generate', { headers: { 'Authorization': `Bearer ${authToken}` } }); expect(response.ok()).toBeTruthy(); const data = await response.json(); expect(data.success).toBe(true); expect(data.data.qrCode).toBeDefined(); expect(data.data.backupCodes).toHaveLength(10); expect(data.data.setupInstructions).toHaveLength(3); qrCode = data.data.qrCode; backupCodes = data.data.backupCodes; // Vérifier le format du QR code expect(qrCode).toMatch(/^data:image\/png;base64,/); // Vérifier le format des codes de backup backupCodes.forEach(code => { expect(code).toMatch(/^[A-F0-9]{8}$/); }); }); test('Ne devrait pas permettre de générer un nouveau secret si 2FA déjà activée', async ({ request }) => { // D'abord activer la 2FA avec un code valide simulé // Note: En test, on utilise un code fixe car on ne peut pas générer un vrai code TOTP const enableResponse = await request.post('/api/security/2fa/enable', { headers: { 'Authorization': `Bearer ${authToken}` }, data: { verificationCode: '123456' } // Code de test }); // Si l'activation échoue (normal car code invalide), on skip le test suivant if (!enableResponse.ok()) { test.skip(); return; } // Essayer de générer un nouveau secret const response = await request.get('/api/security/2fa/generate', { headers: { 'Authorization': `Bearer ${authToken}` } }); expect(response.status()).toBe(400); const data = await response.json(); expect(data.error).toContain('déjà activée'); }); test('Devrait rejeter un code 2FA invalide', async ({ request }) => { const response = await request.post('/api/security/2fa/enable', { headers: { 'Authorization': `Bearer ${authToken}` }, data: { verificationCode: '000000' } }); expect(response.status()).toBe(400); const data = await response.json(); expect(data.error).toContain('invalide'); }); test('Devrait valider le format du code 2FA', async ({ request }) => { const invalidCodes = ['123', '12345678', 'abcdef', '']; for (const code of invalidCodes) { const response = await request.post('/api/security/2fa/enable', { headers: { 'Authorization': `Bearer ${authToken}` }, data: { verificationCode: code } }); expect(response.status()).toBe(400); } }); }); test.describe('[SEARCH] Analyse de Sécurité', () => { test('Devrait analyser une tentative de connexion', async ({ request }) => { const response = await request.post('/api/security/analyze-login', { headers: { 'Authorization': `Bearer ${authToken}` }, data: { userId: userId, location: { latitude: 48.8566, longitude: 2.3522, city: 'Paris', country: 'France' } } }); expect(response.ok()).toBeTruthy(); const data = await response.json(); expect(data.success).toBe(true); expect(data.analysis).toBeDefined(); expect(data.analysis.riskScore).toBeGreaterThanOrEqual(0); expect(data.analysis.riskScore).toBeLessThanOrEqual(1); expect(data.analysis.riskLevel).toMatch(/^(low|medium|high)$/); expect(data.clientInfo).toBeDefined(); }); test('Devrait détecter une IP suspecte', async ({ request }) => { // Simuler une connexion depuis une IP suspecte const response = await request.post('/api/security/analyze-login', { headers: { 'Authorization': `Bearer ${authToken}`, 'X-Forwarded-For': '********' // IP privée suspecte en production }, data: { userId: userId } }); expect(response.ok()).toBeTruthy(); const data = await response.json(); // Le score de risque devrait être plus élevé expect(data.analysis.riskScore).toBeGreaterThan(0); }); test('Devrait valider les paramètres d\'analyse', async ({ request }) => { // Test sans userId const response1 = await request.post('/api/security/analyze-login', { headers: { 'Authorization': `Bearer ${authToken}` }, data: {} }); expect(response1.status()).toBe(400); // Test avec userId invalide const response2 = await request.post('/api/security/analyze-login', { headers: { 'Authorization': `Bearer ${authToken}` }, data: { userId: 'invalid-id' } }); expect(response2.status()).toBe(400); }); }); test.describe(' Logs de Sécurité', () => { test('Devrait récupérer les logs de sécurité', async ({ request }) => { const response = await request.get('/api/security/logs', { headers: { 'Authorization': `Bearer ${authToken}` } }); expect(response.ok()).toBeTruthy(); const data = await response.json(); expect(data.success).toBe(true); expect(data.logs).toBeDefined(); expect(Array.isArray(data.logs)).toBe(true); expect(data.summary).toBeDefined(); expect(data.summary.totalEvents).toBeGreaterThanOrEqual(0); }); test('Devrait respecter la limite de logs', async ({ request }) => { const response = await request.get('/api/security/logs?limit=5', { headers: { 'Authorization': `Bearer ${authToken}` } }); expect(response.ok()).toBeTruthy(); const data = await response.json(); expect(data.logs.length).toBeLessThanOrEqual(5); }); }); test.describe(' Validation des Données', () => { test('Devrait valider les IDs MongoDB', async ({ request }) => { const invalidIds = ['invalid', '123', '', 'not-an-object-id']; for (const id of invalidIds) { const response = await request.post('/api/security/analyze-login', { headers: { 'Authorization': `Bearer ${authToken}` }, data: { userId: id } }); expect(response.status()).toBe(400); const data = await response.json(); expect(data.error).toContain('invalide'); } }); test('Devrait détecter le contenu suspect', async ({ request }) => { const suspiciousInputs = [ '<script>alert("xss")</script>', 'javascript:alert(1)', 'SELECT * FROM users', '<iframe src="malicious.com"></iframe>' ]; // Tester avec les contrôleurs de chat qui utilisent la validation for (const input of suspiciousInputs) { const response = await request.post('/api/chat/messages/send', { headers: { 'Authorization': `Bearer ${authToken}` }, data: { conversationId: '507f1f77bcf86cd799439011', // ID valide pour le test message: input } }); // Devrait soit rejeter soit nettoyer l'entrée if (response.ok()) { const data = await response.json(); // Vérifier que le contenu a été nettoyé expect(data.response.text).not.toContain('<script>'); expect(data.response.text).not.toContain('javascript:'); } else { expect(response.status()).toBe(400); } } }); test('Devrait limiter la longueur des messages', async ({ request }) => { const longMessage = 'a'.repeat(2001); // Dépasse la limite de 2000 caractères const response = await request.post('/api/chat/messages/send', { headers: { 'Authorization': `Bearer ${authToken}` }, data: { conversationId: '507f1f77bcf86cd799439011', message: longMessage } }); expect(response.status()).toBe(400); const data = await response.json(); expect(data.error).toContain('invalide'); }); }); test.describe(' Protection contre les Attaques', () => { test('Devrait appliquer le rate limiting', async ({ request }) => { const requests = []; // Faire de nombreuses requêtes rapidement for (let i = 0; i < 10; i++) { requests.push( request.post('/api/auth/login', { data: { email: '<EMAIL>', password: 'wrongpassword' } }) ); } const responses = await Promise.all(requests); // Au moins une requête devrait être rate-limitée const rateLimitedResponses = responses.filter(r => r.status() === 429); expect(rateLimitedResponses.length).toBeGreaterThan(0); }); test('Devrait rejeter les tokens JWT invalides', async ({ request }) => { const invalidTokens = [ 'invalid.token.here', 'Bearer invalid', '', 'malformed-token' ]; for (const token of invalidTokens) { const response = await request.get('/api/security/status', { headers: { 'Authorization': token } }); expect(response.status()).toBe(401); } }); test('Devrait valider les headers requis', async ({ request }) => { // Test sans header Authorization const response = await request.get('/api/security/status'); expect(response.status()).toBe(401); // Test avec header malformé const response2 = await request.get('/api/security/status', { headers: { 'Authorization': 'InvalidFormat' } }); expect(response2.status()).toBe(401); }); }); test.describe('[SECURITY] Authentification Avancée', () => { test('Devrait améliorer la validation des emails', async ({ request }) => { const invalidEmails = [ 'notanemail', 'test@', '@example.com', '<EMAIL>', 'test@.com' ]; for (const email of invalidEmails) { const response = await request.post('/api/auth/register', { data: { email: email, password: 'ValidPassword123!', firstName: 'Test', lastName: 'User' } }); expect(response.status()).toBe(400); const data = await response.json(); expect(data.error).toContain('invalide'); } }); test('Devrait valider la force des mots de passe', async ({ request }) => { const weakPasswords = [ '123456', 'password', 'qwerty', 'abc123', 'Password', // Pas de chiffre ni symbole '12345678' // Pas de lettres ]; for (const password of weakPasswords) { const response = await request.post('/api/auth/register', { data: { email: `test${Date.now()}@example.com`, password: password, firstName: 'Test', lastName: 'User' } }); expect(response.status()).toBe(400); const data = await response.json(); expect(data.details.some(detail => detail.includes('mot de passe') || detail.includes('faible') )).toBe(true); } }); test('Devrait normaliser les emails', async ({ request }) => { const testEmail = '<EMAIL>'; const normalizedEmail = '<EMAIL>'; const response = await request.post('/api/auth/register', { data: { email: testEmail, password: 'ValidPassword123!', firstName: 'Test', lastName: 'User' } }); if (response.ok()) { const data = await response.json(); expect(data.user.email).toBe(normalizedEmail); } }); }); test.describe('[PERFORMANCE] Performance de Sécurité', () => { test('Devrait répondre rapidement aux requêtes de sécurité', async ({ request }) => { const startTime = Date.now(); const response = await request.get('/api/security/status', { headers: { 'Authorization': `Bearer ${authToken}` } }); const endTime = Date.now(); const responseTime = endTime - startTime; expect(response.ok()).toBeTruthy(); expect(responseTime).toBeLessThan(1000); // Moins de 1 seconde }); test('Devrait gérer les requêtes concurrentes', async ({ request }) => { const concurrentRequests = Array(5).fill().map(() => request.get('/api/security/status', { headers: { 'Authorization': `Bearer ${authToken}` } }) ); const responses = await Promise.all(concurrentRequests); // Toutes les requêtes devraient réussir responses.forEach(response => { expect(response.ok()).toBeTruthy(); }); }); }); }); test.describe(' Tests de Régression', () => { test('Devrait maintenir la compatibilité avec l\'API existante', async ({ request }) => { // Test que les endpoints existants fonctionnent toujours const healthResponse = await request.get('/health'); expect(healthResponse.ok()).toBeTruthy(); const healthData = await healthResponse.json(); expect(healthData.status).toBe('OK'); }); test('Devrait préserver les fonctionnalités de base', async ({ request }) => { // Test d'authentification de base const loginResponse = await request.post('/api/auth/login', { data: { email: userEmail, password: userPassword } }); expect(loginResponse.ok()).toBeTruthy(); const loginData = await loginResponse.json(); expect(loginData.success).toBe(true); expect(loginData.token).toBeDefined(); }); });