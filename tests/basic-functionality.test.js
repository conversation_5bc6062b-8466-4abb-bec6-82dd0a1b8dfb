/** * Basic Functionality Test * Tests core application functionality and navigation */ const { test, expect } = require('@playwright/test'); test.describe('ChatbotRNCP Basic Functionality', () => { test.beforeEach(async ({ page }) => { // Navigate to the application await page.goto('/'); }); test('should load the application homepage', async ({ page }) => { // Wait for the page to load await page.waitForLoadState('networkidle'); // Check that the page title is set await expect(page).toHaveTitle(/React App/); // Check that the root element exists const rootElement = page.locator('#root'); await expect(rootElement).toBeVisible(); console.log('[COMPLETE] Homepage loaded successfully'); }); test('should display navigation elements', async ({ page }) => { // Wait for the page to load await page.waitForLoadState('networkidle'); // Look for common navigation elements const possibleNavElements = [ 'nav', '[role="navigation"]', '.navbar', '.navigation', '.header', 'header' ]; let navFound = false; for (const selector of possibleNavElements) { const element = page.locator(selector); if (await element.count() > 0) { await expect(element.first()).toBeVisible(); navFound = true; console.log(`[COMPLETE] Navigation found: ${selector}`); break; } } if (!navFound) { console.log(' No navigation elements found - this might be expected for a single-page app'); } }); test('should handle login page navigation', async ({ page }) => { // Wait for the page to load await page.waitForLoadState('networkidle'); // Look for login-related elements const loginSelectors = [ 'a[href*="login"]', 'button:has-text("Login")', 'button:has-text("Se connecter")', '.login-button', '#login-button' ]; let loginElementFound = false; for (const selector of loginSelectors) { const element = page.locator(selector); if (await element.count() > 0) { await expect(element.first()).toBeVisible(); loginElementFound = true; console.log(`[COMPLETE] Login element found: ${selector}`); // Try to click the login element try { await element.first().click(); await page.waitForLoadState('networkidle'); console.log('[COMPLETE] Login navigation successful'); } catch (error) { console.log(` Could not click login element: ${error.message}`); } break; } } if (!loginElementFound) { console.log(' No login elements found - checking if already on login page'); // Check if we're already on a login page const loginPageElements = [ 'input[type="email"]', 'input[type="password"]', 'form[class*="login"]', '.login-form' ]; for (const selector of loginPageElements) { const element = page.locator(selector); if (await element.count() > 0) { console.log(`[COMPLETE] Already on login page - found: ${selector}`); loginElementFound = true; break; } } } expect(loginElementFound).toBeTruthy(); }); test('should display main content area', async ({ page }) => { // Wait for the page to load await page.waitForLoadState('networkidle'); // Look for main content areas const contentSelectors = [ 'main', '[role="main"]', '.main-content', '.content', '.app-content', '.container' ]; let contentFound = false; for (const selector of contentSelectors) { const element = page.locator(selector); if (await element.count() > 0) { await expect(element.first()).toBeVisible(); contentFound = true; console.log(`[COMPLETE] Main content found: ${selector}`); break; } } if (!contentFound) { // Fallback: check if there's any visible content in the root const rootContent = page.locator('#root'); await expect(rootContent).toBeVisible(); console.log('[COMPLETE] Root content is visible'); } }); test('should be responsive', async ({ page }) => { // Test desktop view await page.setViewportSize({ width: 1200, height: 800 }); await page.waitForLoadState('networkidle'); const rootElement = page.locator('#root'); await expect(rootElement).toBeVisible(); console.log('[COMPLETE] Desktop view works'); // Test tablet view await page.setViewportSize({ width: 768, height: 1024 }); await page.waitForLoadState('networkidle'); await expect(rootElement).toBeVisible(); console.log('[COMPLETE] Tablet view works'); // Test mobile view await page.setViewportSize({ width: 375, height: 667 }); await page.waitForLoadState('networkidle'); await expect(rootElement).toBeVisible(); console.log('[COMPLETE] Mobile view works'); }); test('should handle page refresh', async ({ page }) => { // Load the page await page.waitForLoadState('networkidle'); // Refresh the page await page.reload(); await page.waitForLoadState('networkidle'); // Check that the page still works const rootElement = page.locator('#root'); await expect(rootElement).toBeVisible(); console.log('[COMPLETE] Page refresh works'); }); });