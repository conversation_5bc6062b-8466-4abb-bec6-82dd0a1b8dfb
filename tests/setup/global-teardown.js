const mongoose = require('mongoose'); const fs = require('fs'); const path = require('path'); /** * Global teardown for Playwright tests * - Cleans up test database * - Removes temporary test files * - Generates test summary */ async function globalTeardown(config) { console.log(' Starting global test teardown...'); try { // Cleanup test database await cleanupTestDatabase(); // Remove temporary test files await cleanupTestFiles(); // Generate test summary await generateTestSummary(); console.log('[COMPLETE] Global teardown completed successfully'); } catch (error) { console.error('[FAILED] Global teardown failed:', error); // Don't throw error to avoid masking test failures } } /** * Cleanup test database */ async function cleanupTestDatabase() { console.log(' Cleaning up test database...'); const testDbUri = process.env.TEST_MONGODB_URI || 'mongodb://localhost:27017/chatbot-test'; try { // Connect to test database await mongoose.connect(testDbUri, { useNewUrlParser: true, useUnifiedTopology: true, }); // Drop test database await mongoose.connection.db.dropDatabase(); console.log('[COMPLETE] Test database cleaned'); // Close connection await mongoose.disconnect(); } catch (error) { console.warn(' Database cleanup failed:', error.message); } } /** * Remove temporary test files */ async function cleanupTestFiles() { console.log(' Cleaning up temporary test files...'); const tempFiles = [ './tests/fixtures/temp-*.json', './tests/temp-*', './test-results/temp-*' ]; for (const pattern of tempFiles) { try { const glob = require('glob'); const files = glob.sync(pattern); for (const file of files) { if (fs.existsSync(file)) { fs.unlinkSync(file); console.log(` Removed ${file}`); } } } catch (error) { console.warn(` Failed to cleanup ${pattern}:`, error.message); } } } /** * Generate test summary report */ async function generateTestSummary() { console.log('[ANALYTICS] Generating test summary...'); try { const resultsPath = './test-results/results.json'; if (fs.existsSync(resultsPath)) { const results = JSON.parse(fs.readFileSync(resultsPath, 'utf8')); const summary = { timestamp: new Date().toISOString(), total: results.stats?.total || 0, passed: results.stats?.passed || 0, failed: results.stats?.failed || 0, skipped: results.stats?.skipped || 0, duration: results.stats?.duration || 0, projects: results.suites?.map(suite => ({ name: suite.title, tests: suite.specs?.length || 0, passed: suite.specs?.filter(spec => spec.ok).length || 0, failed: suite.specs?.filter(spec => !spec.ok).length || 0 })) || [] }; // Write summary const summaryPath = './test-results/summary.json'; fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2)); // Console summary console.log('[METRICS] Test Summary:'); console.log(` Total: ${summary.total}`); console.log(` Passed: ${summary.passed}`); console.log(` Failed: ${summary.failed}`); console.log(` Skipped: ${summary.skipped}`); console.log(` Duration: ${Math.round(summary.duration / 1000)}s`); } else { console.log(' No test results found for summary'); } } catch (error) { console.warn(' Failed to generate test summary:', error.message); } } module.exports = globalTeardown;