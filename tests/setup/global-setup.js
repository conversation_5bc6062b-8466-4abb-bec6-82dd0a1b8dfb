const { chromium } = require('@playwright/test'); const mongoose = require('mongoose'); const { exec } = require('child_process'); const { promisify } = require('util'); const execAsync = promisify(exec); /** * Global setup for Playwright tests * - Starts services if not running * - Sets up test database * - Creates test fixtures */ async function globalSetup(config) { console.log('[DEPLOY] Starting global test setup...'); try { // Setup test database await setupTestDatabase(); // Create test fixtures await createTestFixtures(); // Verify services are running await verifyServices(); console.log('[COMPLETE] Global setup completed successfully'); } catch (error) { console.error('[FAILED] Global setup failed:', error); throw error; } } /** * Setup test database with clean state */ async function setupTestDatabase() { console.log(' Setting up test database...'); const testDbUri = process.env.TEST_MONGODB_URI || 'mongodb://localhost:27017/chatbot-test'; try { // Connect to test database await mongoose.connect(testDbUri, { useNewUrlParser: true, useUnifiedTopology: true, }); // Drop existing test database for clean state await mongoose.connection.db.dropDatabase(); console.log(' Cleaned test database'); // Close connection await mongoose.disconnect(); } catch (error) { console.error('[FAILED] Database setup failed:', error); throw error; } } /** * Create test fixtures and seed data */ async function createTestFixtures() { console.log(' Creating test fixtures...'); // Test user credentials for different roles const testUsers = { admin: { email: '<EMAIL>', password: 'AdminTest123!', role: 'admin', firstName: 'Test', lastName: 'Admin' }, agent: { email: '<EMAIL>', password: 'AgentTest123!', role: 'agent', firstName: 'Test', lastName: 'Agent' }, user: { email: '<EMAIL>', password: 'UserTest123!', role: 'user', firstName: 'Test', lastName: 'User' } }; // Store test credentials in global config global.testUsers = testUsers; // Create test data file const testDataPath = './tests/fixtures/test-data.json'; const fs = require('fs'); const path = require('path'); // Ensure fixtures directory exists const fixturesDir = path.dirname(testDataPath); if (!fs.existsSync(fixturesDir)) { fs.mkdirSync(fixturesDir, { recursive: true }); } // Write test data fs.writeFileSync(testDataPath, JSON.stringify({ users: testUsers, testMessages: [ 'Hello, I need help with my account', 'Can you help me with billing?', 'I want to change my plan', 'Technical support needed' ], testScenarios: { login: { validCredentials: testUsers.user, invalidEmail: { email: '<EMAIL>', password: 'password' }, invalidPassword: { email: testUsers.user.email, password: 'wrongpassword' }, emptyFields: { email: '', password: '' } } } }, null, 2)); console.log('[COMPLETE] Test fixtures created'); } /** * Verify that required services are running */ async function verifyServices() { console.log('[SEARCH] Verifying services...'); const services = [ { name: 'Backend API', url: 'http://localhost:5000/health' }, { name: 'Frontend App', url: 'http://localhost:3001' } ]; for (const service of services) { try { const response = await fetch(service.url); if (response.ok) { console.log(`[COMPLETE] ${service.name} is running`); } else { throw new Error(`Service returned status ${response.status}`); } } catch (error) { console.warn(` ${service.name} not accessible: ${error.message}`); // Don't fail setup if services aren't running - they might be started by webServer config } } } /** * Wait for a service to be ready */ async function waitForService(url, timeout = 30000) { const startTime = Date.now(); while (Date.now() - startTime < timeout) { try { const response = await fetch(url); if (response.ok) { return true; } } catch (error) { // Service not ready yet } await new Promise(resolve => setTimeout(resolve, 1000)); } throw new Error(`Service at ${url} did not become ready within ${timeout}ms`); } module.exports = globalSetup;