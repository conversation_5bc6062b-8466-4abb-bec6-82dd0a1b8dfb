const { test, expect } = require('@playwright/test'); // Configuration pour les tests frontend avec authentification const FRONTEND_URL = 'http://localhost:3000'; const SUPPORT_PAGE_URL = `${FRONTEND_URL}/support`; const LOGIN_PAGE_URL = `${FRONTEND_URL}/login`; /** * Tests pour la nouvelle interface Support avec authentification */ test.describe('[TARGET] Tests Interface Support - Avec Authentification', () => { test.beforeEach(async ({ page }) => { // Aller sur la page de login await page.goto(LOGIN_PAGE_URL); await page.waitForLoadState('networkidle'); // Simuler une authentification (peut être adapté selon votre implémentation) // Pour les tests, on peut utiliser un token mock ou des identifiants de test await page.evaluate(() => { // Simuler un token d'authentification en localStorage localStorage.setItem('authToken', 'test-token-123'); localStorage.setItem('user', JSON.stringify({ id: '1', email: '<EMAIL>', name: 'Test User' })); }); // Naviguer vers la page support await page.goto(SUPPORT_PAGE_URL); await page.waitForLoadState('networkidle'); // Vérifier que nous sommes sur la bonne page await expect(page).toHaveURL(SUPPORT_PAGE_URL); }); test(' Doit afficher le header Free Mobile correct', async ({ page }) => { // Vérifier le logo Free await expect(page.locator('text=Free').first()).toBeVisible(); // Vérifier le titre "Support Client" await expect(page.locator('text=Support Client')).toBeVisible(); // Vérifier les onglets de navigation await expect(page.locator('text=Formulaire Support')).toBeVisible(); await expect(page.locator('text=Panel Admin')).toBeVisible(); await expect(page.locator('text=Analytics')).toBeVisible(); }); test(' Onglet 1 - Formulaire Support complet', async ({ page }) => { // Vérifier le titre du formulaire await expect(page.locator('text=Contacter le support Free')).toBeVisible(); // Vérifier les champs du formulaire avec des sélecteurs plus flexibles await expect(page.locator('input[name="fullName"], input[placeholder*="Nom"], input[label*="Nom"]')).toBeVisible(); await expect(page.locator('input[name="clientId"], input[placeholder*="ID Client"], input[label*="ID Client"]')).toBeVisible(); await expect(page.locator('input[name="email"], input[type="email"], input[placeholder*="email"]')).toBeVisible(); // Vérifier la zone de description await expect(page.locator('textarea, input[multiline]')).toBeVisible(); // Vérifier la zone d'upload await expect(page.locator('text=Glissez votre fichier')).toBeVisible(); // Vérifier le bouton d'envoi await expect(page.locator('button:has-text("Envoyer")')).toBeVisible(); }); test(' Chat en direct', async ({ page }) => { // Vérifier la section chat await expect(page.locator('text=Chat en direct')).toBeVisible(); // Vérifier l'indicateur "En ligne" await expect(page.locator('text=En ligne')).toBeVisible(); // Vérifier la zone de saisie du chat await expect(page.locator('input[placeholder*="message"], input[placeholder*="Tapez"]')).toBeVisible(); // Vérifier le bouton d'envoi du chat await expect(page.locator('button:has([aria-label="send"], [data-testid="SendIcon"])')).toBeVisible(); }); test(' Navigation entre onglets', async ({ page }) => { // Cliquer sur l'onglet Panel Admin await page.click('text=Panel Admin'); await page.waitForTimeout(1000); // Vérifier que nous sommes sur l'onglet Panel Admin await expect(page.locator('text=Panel Admin').first()).toBeVisible(); // Cliquer sur l'onglet Analytics await page.click('text=Analytics'); await page.waitForTimeout(1000); // Vérifier que nous sommes sur l'onglet Analytics await expect(page.locator('text=Analytics Dashboard')).toBeVisible(); // Revenir à l'onglet Formulaire Support await page.click('text=Formulaire Support'); await page.waitForTimeout(1000); // Vérifier que nous sommes de retour sur le formulaire await expect(page.locator('text=Contacter le support Free')).toBeVisible(); }); test(' Panel Admin - Menu latéral', async ({ page }) => { // Aller sur l'onglet Panel Admin await page.click('text=Panel Admin'); await page.waitForTimeout(1000); // Vérifier les éléments du menu latéral await expect(page.locator('text=Dashboard')).toBeVisible(); await expect(page.locator('text=Conversations')).toBeVisible(); await expect(page.locator('text=Réclamations')).toBeVisible(); await expect(page.locator('text=Suggestions')).toBeVisible(); await expect(page.locator('text=Résiliations')).toBeVisible(); }); test('[ANALYTICS] Panel Admin - Tableau des demandes', async ({ page }) => { // Aller sur l'onglet Panel Admin await page.click('text=Panel Admin'); await page.waitForTimeout(1000); // Vérifier le titre du tableau await expect(page.locator('text=Demandes clients')).toBeVisible(); // Vérifier les filtres await expect(page.locator('text=Tous les statuts')).toBeVisible(); await expect(page.locator('text=Toutes catégories')).toBeVisible(); // Vérifier les en-têtes du tableau await expect(page.locator('text=Client')).toBeVisible(); await expect(page.locator('text=Catégorie')).toBeVisible(); await expect(page.locator('text=Statut')).toBeVisible(); await expect(page.locator('text=Date')).toBeVisible(); // Vérifier les données de démonstration await expect(page.locator('text=Jean Dupont')).toBeVisible(); await expect(page.locator('text=Marie Martin')).toBeVisible(); }); test('[METRICS] Analytics - Métriques principales', async ({ page }) => { // Aller sur l'onglet Analytics await page.click('text=Analytics'); await page.waitForTimeout(1000); // Vérifier le titre await expect(page.locator('text=Analytics Dashboard')).toBeVisible(); // Vérifier les métriques principales await expect(page.locator('text=1,247')).toBeVisible(); // Total Tickets await expect(page.locator('text=4.2')).toBeVisible(); // Score CSAT await expect(page.locator('text=2.4h')).toBeVisible(); // Temps moyen await expect(page.locator('text=3.2%')).toBeVisible(); // Taux résiliation // Vérifier les labels des métriques await expect(page.locator('text=Total Tickets')).toBeVisible(); await expect(page.locator('text=Score CSAT')).toBeVisible(); await expect(page.locator('text=Temps moyen')).toBeVisible(); await expect(page.locator('text=Taux résiliation')).toBeVisible(); }); test('[ANALYTICS] Analytics - Graphiques et analyses', async ({ page }) => { // Aller sur l'onglet Analytics await page.click('text=Analytics'); await page.waitForTimeout(1000); // Vérifier les titres des graphiques await expect(page.locator('text=Volume des tickets')).toBeVisible(); await expect(page.locator('text=Répartition par catégorie')).toBeVisible(); // Vérifier le top 5 des raisons de résiliation await expect(page.locator('text=Top 5 raisons de résiliation')).toBeVisible(); await expect(page.locator('text=Tarifs trop élevés')).toBeVisible(); await expect(page.locator('text=Problème de réseau')).toBeVisible(); // Vérifier les suggestions d'amélioration IA await expect(page.locator('text=Suggestions d\'amélioration IA')).toBeVisible(); await expect(page.locator('text=Améliorer la qualité réseau')).toBeVisible(); await expect(page.locator('text=Optimiser les tarifs')).toBeVisible(); }); test(' Test Responsive - Mobile', async ({ page }) => { // Simuler un viewport mobile await page.setViewportSize({ width: 375, height: 667 }); // Recharger la page await page.reload(); await page.waitForLoadState('networkidle'); // Vérifier que les éléments sont toujours visibles await expect(page.locator('text=Free')).toBeVisible(); await expect(page.locator('text=Support Client')).toBeVisible(); await expect(page.locator('text=Formulaire Support')).toBeVisible(); }); test(' Test Responsive - Desktop', async ({ page }) => { // Simuler un viewport desktop await page.setViewportSize({ width: 1920, height: 1080 }); // Recharger la page await page.reload(); await page.waitForLoadState('networkidle'); // Vérifier que l'interface est optimisée pour desktop await expect(page.locator('text=Free')).toBeVisible(); await expect(page.locator('text=Support Client')).toBeVisible(); await expect(page.locator('text=Chat en direct')).toBeVisible(); }); test(' Test Fonctionnel - Remplissage du formulaire', async ({ page }) => { // Utiliser des sélecteurs plus robustes const nameInput = page.locator('input').first(); const clientIdInput = page.locator('input').nth(1); const emailInput = page.locator('input[type="email"]'); // Remplir le formulaire await nameInput.fill('Jean Dupont Test'); await clientIdInput.fill('**********'); await emailInput.fill('<EMAIL>'); // Vérifier que les champs sont bien remplis await expect(nameInput).toHaveValue('Jean Dupont Test'); await expect(clientIdInput).toHaveValue('**********'); await expect(emailInput).toHaveValue('<EMAIL>'); }); test(' Test Fonctionnel - Chat en direct', async ({ page }) => { // Trouver le champ de saisie du chat const chatInput = page.locator('input[placeholder*="message"], input[placeholder*="Tapez"]'); // Taper un message dans le chat await chatInput.fill('Bonjour, j\'ai besoin d\'aide'); // Cliquer sur le bouton d'envoi await page.click('button:has([aria-label="send"], [data-testid="SendIcon"])'); // Vérifier que le message a été envoyé (le champ doit être vide) await expect(chatInput).toHaveValue(''); }); test('[PERFORMANCE] Test Performance - Chargement rapide', async ({ page }) => { const startTime = Date.now(); // Aller sur la page (déjà fait dans beforeEach, mais on test une nouvelle navigation) await page.goto(SUPPORT_PAGE_URL); await page.waitForLoadState('networkidle'); const loadTime = Date.now() - startTime; // Vérifier que le chargement prend moins de 5 secondes expect(loadTime).toBeLessThan(5000); // Vérifier que tous les éléments critiques sont chargés await expect(page.locator('text=Free')).toBeVisible(); await expect(page.locator('text=Support Client')).toBeVisible(); await expect(page.locator('text=Contacter le support Free')).toBeVisible(); }); }); /** * Tests spécifiques aux interactions utilisateur */ test.describe(' Tests Interactions Utilisateur - Avec Auth', () => { test.beforeEach(async ({ page }) => { // Authentification similaire await page.goto(LOGIN_PAGE_URL); await page.waitForLoadState('networkidle'); await page.evaluate(() => { localStorage.setItem('authToken', 'test-token-123'); localStorage.setItem('user', JSON.stringify({ id: '1', email: '<EMAIL>', name: 'Test User' })); }); await page.goto(SUPPORT_PAGE_URL); await page.waitForLoadState('networkidle'); }); test('[TARGET] Test Workflow complet utilisateur', async ({ page }) => { // 1. Remplir le formulaire const inputs = page.locator('input'); await inputs.nth(0).fill('Marie Martin'); await inputs.nth(1).fill('**********'); await inputs.nth(2).fill('<EMAIL>'); // 2. Envoyer un message dans le chat const chatInput = page.locator('input[placeholder*="message"], input[placeholder*="Tapez"]'); await chatInput.fill('J\'ai aussi une question urgente'); await page.click('button:has([aria-label="send"], [data-testid="SendIcon"])'); // 3. Aller sur le panel admin await page.click('text=Panel Admin'); await page.waitForTimeout(1000); await expect(page.locator('text=Demandes clients')).toBeVisible(); // 4. Aller sur les analytics await page.click('text=Analytics'); await page.waitForTimeout(1000); await expect(page.locator('text=Analytics Dashboard')).toBeVisible(); // 5. Revenir au formulaire await page.click('text=Formulaire Support'); await page.waitForTimeout(1000); await expect(page.locator('text=Contacter le support Free')).toBeVisible(); }); test('[DESIGN] Test Chips et Badges', async ({ page }) => { // Aller sur le panel admin await page.click('text=Panel Admin'); await page.waitForTimeout(1000); // Vérifier les chips de catégorie await expect(page.locator('text=Réseau')).toBeVisible(); await expect(page.locator('text=Facturation')).toBeVisible(); // Vérifier les chips de statut await expect(page.locator('text=En attente')).toBeVisible(); await expect(page.locator('text=En cours')).toBeVisible(); // Vérifier les chips de priorité await expect(page.locator('text=urgent')).toBeVisible(); await expect(page.locator('text=standard')).toBeVisible(); }); }); /** * Tests de validation des couleurs et design */ test.describe('[DESIGN] Tests Design Free Mobile', () => { test.beforeEach(async ({ page }) => { await page.goto(LOGIN_PAGE_URL); await page.waitForLoadState('networkidle'); await page.evaluate(() => { localStorage.setItem('authToken', 'test-token-123'); localStorage.setItem('user', JSON.stringify({ id: '1', email: '<EMAIL>', name: 'Test User' })); }); await page.goto(SUPPORT_PAGE_URL); await page.waitForLoadState('networkidle'); }); test('[DESIGN] Test Couleurs Free Mobile', async ({ page }) => { // Vérifier que les couleurs Free Mobile sont appliquées const freeButton = page.locator('text=Free').first(); await expect(freeButton).toBeVisible(); // Vérifier le bouton principal const submitButton = page.locator('button:has-text("Envoyer")'); await expect(submitButton).toBeVisible(); // Vérifier la couleur de fond du header const header = page.locator('header, [role="banner"]').first(); await expect(header).toBeVisible(); }); test('[SEARCH] Test Accessibilité - Navigation clavier', async ({ page }) => { // Tester la navigation au clavier await page.keyboard.press('Tab'); await page.keyboard.press('Tab'); await page.keyboard.press('Tab'); // Vérifier que les éléments sont focusables const focused = page.locator(':focus'); await expect(focused).toBeVisible(); }); });