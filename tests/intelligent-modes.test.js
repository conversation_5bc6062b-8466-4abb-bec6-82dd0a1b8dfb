const { test, expect } = require('@playwright/test'); /** * TESTS MODES DE FONCTIONNEMENT INTELLIGENTS * Validation des 4 modes : Conversationnel, Guidé, Hybride, Proactif */ let userToken; let userId; let conversationId; test.describe(' Tests Modes de Fonctionnement Intelligents', () => { test.beforeAll(async ({ request }) => { // Créer un utilisateur de test const userResponse = await request.post('/api/auth/register', { data: { email: '<EMAIL>', password: 'IntelligentPassword123!', firstName: 'Test', lastName: 'Intelligent' } }); expect(userResponse.ok()).toBeTruthy(); const userData = await userResponse.json(); userToken = userData.token; userId = userData.user.id; }); test.beforeEach(async ({ request }) => { // Créer une nouvelle conversation pour chaque test const convResponse = await request.post('/api/chat/conversations/start', { headers: { 'Authorization': `<PERSON><PERSON> ${userToken}` }, data: { channel: 'web' } }); expect(convResponse.ok()).toBeTruthy(); const convData = await convResponse.json(); conversationId = convData.conversationId; }); test.describe(' Mode Conversationnel Libre', () => { test('Devrait détecter et adapter le ton selon l\'émotion', async ({ request }) => { const emotionalMessages = [ { message: "Je suis vraiment frustré par ce problème!", expectedSentiment: "frustration", expectedTone: "empathetic" }, { message: "Merci beaucoup, c'est parfait!", expectedSentiment: "satisfaction", expectedTone: "positive" }, { message: "C'est urgent, j'ai besoin d'aide rapidement!", expectedSentiment: "urgence", expectedTone: "efficient" } ]; for (const testCase of emotionalMessages) { const response = await request.post('/api/chat/messages/send', { headers: { 'Authorization': `Bearer ${userToken}` }, data: { conversationId: conversationId, message: testCase.message } }); expect(response.ok()).toBeTruthy(); const data = await response.json(); expect(data.success).toBe(true); expect(data.response).toBeDefined(); // Vérifier si les modes intelligents sont utilisés if (data.metadata?.intelligentFeaturesUsed) { expect(data.response.mode).toBeDefined(); // Si sentiment détecté, vérifier qu'il correspond if (data.response.sentiment) { expect(data.response.tone).toBeDefined(); } } } }); test('Devrait générer des réponses naturelles et décontractées', async ({ request }) => { const casualMessages = [ "Salut, ça va?", "J'ai un petit souci avec mon téléphone", "Comment ça marche cette histoire de forfait?" ]; for (const message of casualMessages) { const response = await request.post('/api/chat/messages/send', { headers: { 'Authorization': `Bearer ${userToken}` }, data: { conversationId: conversationId, message: message } }); expect(response.ok()).toBeTruthy(); const data = await response.json(); expect(data.response.text).toBeDefined(); expect(data.response.text.length).toBeGreaterThan(0); // Vérifier que la réponse semble naturelle (contient des mots-clés conversationnels) const conversationalKeywords = ['salut', 'bonjour', 'hey', 'ok', 'ça', 'tu', 'te', 'toi']; const responseText = data.response.text.toLowerCase(); const hasConversationalTone = conversationalKeywords.some(keyword => responseText.includes(keyword) ); if (data.response.mode === 'conversational') { expect(hasConversationalTone).toBe(true); } } }); }); test.describe('[TARGET] Mode Guidé par Menus', () => { test('Devrait fournir des boutons et options structurées', async ({ request }) => { const guidedTriggers = [ "Menu principal", "Que puis-je faire?", "Aidez-moi", "Options disponibles" ]; for (const message of guidedTriggers) { const response = await request.post('/api/chat/messages/send', { headers: { 'Authorization': `Bearer ${userToken}` }, data: { conversationId: conversationId, message: message } }); expect(response.ok()).toBeTruthy(); const data = await response.json(); expect(data.response.text).toBeDefined(); // Si mode guidé activé, vérifier la présence de boutons ou quick replies if (data.response.mode === 'guided') { const hasInteractiveElements = (data.response.buttons && data.response.buttons.length > 0) || (data.response.quickReplies && data.response.quickReplies.length > 0); expect(hasInteractiveElements).toBe(true); } } }); test('Devrait adapter les menus selon le contexte', async ({ request }) => { const contextualMessages = [ { message: "Problème avec ma facture", expectedContext: "billing" }, { message: "Mon réseau ne marche pas", expectedContext: "technical" }, { message: "Je veux changer de forfait", expectedContext: "subscription" } ]; for (const testCase of contextualMessages) { const response = await request.post('/api/chat/messages/send', { headers: { 'Authorization': `Bearer ${userToken}` }, data: { conversationId: conversationId, message: testCase.message } }); expect(response.ok()).toBeTruthy(); const data = await response.json(); // Vérifier que la réponse contient des éléments contextuels const responseText = data.response.text.toLowerCase(); const buttons = data.response.buttons || []; const quickReplies = data.response.quickReplies || []; const allText = [responseText, ...buttons.map(b => b.text?.toLowerCase() || ''), ...quickReplies.map(q => q.toLowerCase())].join(' '); switch (testCase.expectedContext) { case "billing": expect(allText).toMatch(/(facture|paiement|montant|euros?)/); break; case "technical": expect(allText).toMatch(/(réseau|connexion|technique|problème)/); break; case "subscription": expect(allText).toMatch(/(forfait|offre|changement|plan)/); break; } } }); }); test.describe(' Mode Hybride Intelligent', () => { test('Devrait combiner réponse directe et options selon la confiance', async ({ request }) => { const hybridMessages = [ { message: "Quel est le prix du forfait 100Go?", // Question précise expectDirectAnswer: true }, { message: "J'ai des trucs qui marchent pas bien", // Question vague expectClarification: true } ]; for (const testCase of hybridMessages) { const response = await request.post('/api/chat/messages/send', { headers: { 'Authorization': `Bearer ${userToken}` }, data: { conversationId: conversationId, message: testCase.message } }); expect(response.ok()).toBeTruthy(); const data = await response.json(); if (data.response.mode === 'hybrid') { if (testCase.expectDirectAnswer) { // Devrait contenir une réponse directe + options expect(data.response.text).toBeDefined(); expect(data.response.buttons || data.response.quickReplies).toBeDefined(); } if (testCase.expectClarification) { // Devrait demander des clarifications expect(data.response.text.toLowerCase()).toMatch(/(précis|détail|clarifi|expliqu)/); } } } }); test('Devrait s\'adapter au profil utilisateur', async ({ request }) => { // Messages identiques mais contexte différent const message = "Comment configurer mon APN?"; const response = await request.post('/api/chat/messages/send', { headers: { 'Authorization': `Bearer ${userToken}` }, data: { conversationId: conversationId, message: message } }); expect(response.ok()).toBeTruthy(); const data = await response.json(); // Vérifier que la réponse est adaptée expect(data.response.text).toBeDefined(); expect(data.response.text.length).toBeGreaterThan(10); // Le mode hybride devrait fournir une réponse complète if (data.response.mode === 'hybrid') { expect(data.response.text.toLowerCase()).toMatch(/(apn|configur|paramètr)/); } }); }); test.describe('[DEPLOY] Mode Proactif', () => { test('Devrait détecter les opportunités proactives', async ({ request }) => { const proactiveScenarios = [ { message: "Ma data va bientôt être épuisée", expectedInsight: "data_limit" }, { message: "J'utilise jamais tout mon forfait", expectedInsight: "plan_optimization" } ]; for (const scenario of proactiveScenarios) { const response = await request.post('/api/chat/messages/send', { headers: { 'Authorization': `Bearer ${userToken}` }, data: { conversationId: conversationId, message: scenario.message } }); expect(response.ok()).toBeTruthy(); const data = await response.json(); // Vérifier la réponse principale expect(data.response.text).toBeDefined(); // Si mode proactif, vérifier les insights if (data.response.mode === 'proactive' && data.response.proactiveInsights) { expect(Array.isArray(data.response.proactiveInsights)).toBe(true); if (data.response.proactiveInsights.length > 0) { const insights = data.response.proactiveInsights; expect(insights[0].type).toBeDefined(); expect(insights[0].title).toBeDefined(); expect(insights[0].message).toBeDefined(); } } } }); test('Devrait fournir des recommandations personnalisées', async ({ request }) => { const message = "Comment économiser sur ma facture?"; const response = await request.post('/api/chat/messages/send', { headers: { 'Authorization': `Bearer ${userToken}` }, data: { conversationId: conversationId, message: message } }); expect(response.ok()).toBeTruthy(); const data = await response.json(); expect(data.response.text).toBeDefined(); // Vérifier si des suggestions personnalisées sont fournies if (data.response.suggestions && data.response.suggestions.length > 0) { const suggestions = data.response.suggestions; suggestions.forEach(suggestion => { expect(typeof suggestion).toBe('string'); expect(suggestion.length).toBeGreaterThan(0); }); } }); }); test.describe(' Sélection Automatique de Mode', () => { test('Devrait choisir le mode approprié selon le contexte', async ({ request }) => { const modeTests = [ { message: "Bonjour, je suis perdu, aidez-moi", expectedModes: ['guided', 'hybrid'] // Devrait privilégier guidé ou hybride }, { message: "Salut! Ça va? J'ai juste une petite question", expectedModes: ['conversational', 'hybrid'] // Devrait privilégier conversationnel }, { message: "Forfait 20Go prix", expectedModes: ['guided', 'hybrid'] // Question directe } ]; for (const test of modeTests) { const response = await request.post('/api/chat/messages/send', { headers: { 'Authorization': `Bearer ${userToken}` }, data: { conversationId: conversationId, message: test.message } }); expect(response.ok()).toBeTruthy(); const data = await response.json(); expect(data.response.text).toBeDefined(); // Si un mode est détecté, vérifier qu'il est approprié if (data.response.mode && data.response.mode !== 'fallback') { expect(test.expectedModes).toContain(data.response.mode); } } }); test('Devrait maintenir la cohérence dans une conversation', async ({ request }) => { const messages = [ "Bonjour, j'ai un problème", "C'est avec ma facture", "Elle est trop élevée" ]; const modes = []; for (const message of messages) { const response = await request.post('/api/chat/messages/send', { headers: { 'Authorization': `Bearer ${userToken}` }, data: { conversationId: conversationId, message: message } }); expect(response.ok()).toBeTruthy(); const data = await response.json(); if (data.response.mode) { modes.push(data.response.mode); } } // La conversation devrait maintenir une certaine cohérence if (modes.length > 1) { // Ne devrait pas changer de mode radicalement à chaque message const uniqueModes = [...new Set(modes)]; expect(uniqueModes.length).toBeLessThanOrEqual(2); } }); }); test.describe('[ANALYTICS] Métadonnées et Analytics', () => { test('Devrait fournir des métadonnées détaillées', async ({ request }) => { const response = await request.post('/api/chat/messages/send', { headers: { 'Authorization': `Bearer ${userToken}` }, data: { conversationId: conversationId, message: "Test des métadonnées intelligentes" } }); expect(response.ok()).toBeTruthy(); const data = await response.json(); expect(data.metadata).toBeDefined(); expect(data.metadata.processingTime).toBeGreaterThan(0); if (data.metadata.intelligentFeaturesUsed) { expect(data.metadata.mode).toBeDefined(); expect(data.response.mode).toBeDefined(); } }); test('Devrait mesurer les temps de traitement', async ({ request }) => { const startTime = Date.now(); const response = await request.post('/api/chat/messages/send', { headers: { 'Authorization': `Bearer ${userToken}` }, data: { conversationId: conversationId, message: "Test de performance des modes intelligents" } }); const endTime = Date.now(); const totalTime = endTime - startTime; expect(response.ok()).toBeTruthy(); const data = await response.json(); // Le traitement intelligent ne devrait pas être trop lent expect(totalTime).toBeLessThan(5000); // 5 secondes max if (data.metadata?.processingTime) { expect(data.metadata.processingTime).toBeLessThan(totalTime); } }); }); test.describe('[CONFIG] Gestion d\'Erreurs Intelligentes', () => { test('Devrait gérer les erreurs gracieusement', async ({ request }) => { // Test avec un message très long const longMessage = 'a'.repeat(3000); const response = await request.post('/api/chat/messages/send', { headers: { 'Authorization': `Bearer ${userToken}` }, data: { conversationId: conversationId, message: longMessage } }); expect(response.status()).toBe(400); const data = await response.json(); expect(data.error).toBeDefined(); }); test('Devrait fallback vers mode simple en cas d\'erreur', async ({ request }) => { // Message normal qui devrait fonctionner même si les modes intelligents échouent const response = await request.post('/api/chat/messages/send', { headers: { 'Authorization': `Bearer ${userToken}` }, data: { conversationId: conversationId, message: "Message de test simple" } }); expect(response.ok()).toBeTruthy(); const data = await response.json(); expect(data.response.text).toBeDefined(); expect(data.response.text.length).toBeGreaterThan(0); // Même en cas de fallback, une réponse devrait être fournie if (data.metadata?.fallbackUsed) { expect(data.response.mode).toBe('fallback'); } }); }); }); test.describe(' Tests d\'Intégration Modes Intelligents', () => { test('Devrait intégrer avec les autres systèmes', async ({ request }) => { // Test d'intégration avec le système d'auth const response = await request.post('/api/chat/messages/send', { headers: { 'Authorization': `Bearer ${userToken}` }, data: { conversationId: conversationId, message: "Test d'intégration complète" } }); expect(response.ok()).toBeTruthy(); const data = await response.json(); expect(data.success).toBe(true); expect(data.response).toBeDefined(); expect(data.intent).toBeDefined(); // Devrait fournir l'intention détectée expect(data.timestamp).toBeDefined(); }); test('Devrait maintenir la compatibilité avec l\'API existante', async ({ request }) => { // Vérifier que la nouvelle API enrichie reste compatible const response = await request.post('/api/chat/messages/send', { headers: { 'Authorization': `Bearer ${userToken}` }, data: { conversationId: conversationId, message: "Test de compatibilité API" } }); expect(response.ok()).toBeTruthy(); const data = await response.json(); // Champs obligatoires de l'ancienne API expect(data.messageId).toBeDefined(); expect(data.timestamp).toBeDefined(); // Nouveaux champs enrichis expect(data.response).toBeDefined(); expect(data.metadata).toBeDefined(); }); });