const { test, expect } = require('@playwright/test'); test.describe('Basic Health Checks', () => { test('Frontend should load successfully', async ({ page }) => { // Navigate to the frontend await page.goto('/'); // Wait for the page to load await page.waitForLoadState('networkidle'); // Check if the page title contains expected text await expect(page).toHaveTitle(/Free Mobile|Chatbot|Dashboard/); // Check if the page doesn't have any critical errors const errorMessages = page.locator('[data-testid="error"], .error, .alert-error'); await expect(errorMessages).toHaveCount(0); }); test('Backend health endpoint should respond', async ({ request }) => { // Test backend health endpoint const response = await request.get('/health'); expect(response.status()).toBe(200); const data = await response.json(); expect(data).toHaveProperty('status'); expect(data.status).toBe('ok'); }); test('API status endpoint should respond', async ({ request }) => { // Test API status endpoint const response = await request.get('/api/status'); expect(response.status()).toBe(200); const data = await response.json(); expect(data).toHaveProperty('message'); }); test('Frontend should handle navigation', async ({ page }) => { await page.goto('/'); // Wait for the page to load await page.waitForLoadState('networkidle'); // Check if we can navigate (this might redirect to login or dashboard) const currentUrl = page.url(); expect(currentUrl).toMatch(/localhost|127\.0\.0\.1/); // Check if the page has basic structure const body = page.locator('body'); await expect(body).toBeVisible(); }); });