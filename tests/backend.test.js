const { test, expect } = require('@playwright/test'); const { MongoClient } = require('mongodb'); // Configuration const API_BASE_URL = 'http://localhost:5000/api'; const HEALTH_URL = 'http://localhost:5000/health'; // Variables globales pour les tests let authToken = ''; let conversationId = ''; let testUser = { email: `test${Date.now()}@free.fr`, // Email unique basé sur timestamp password: 'Test123!', firstName: 'Jean', lastName: 'Test' }; // Helper function pour s'assurer d'avoir un token valide async function ensureAuthToken(request) { if (!authToken) { console.log(' Creating auth token...'); const response = await request.post(`${API_BASE_URL}/auth/register`, { data: { email: `test_${Date.now()}@free.fr`, password: 'Test123!', firstName: 'Test', lastName: 'User' } }); if (response.ok()) { const data = await response.json(); authToken = data.token; console.log('[COMPLETE] Auth token created successfully'); } else { // Si l'email existe déjà, essayer de se connecter const loginResponse = await request.post(`${API_BASE_URL}/auth/login`, { data: { email: testUser.email, password: testUser.password } }); if (loginResponse.ok()) { const loginData = await loginResponse.json(); authToken = loginData.token; console.log('[COMPLETE] Logged in successfully'); } else { throw new Error('Failed to get auth token'); } } } return authToken; } test.describe(' Tests Backend - Chatbot Free Mobile', () => { test.beforeAll(async ({ request }) => { // Attendre que le backend soit prêt let attempts = 0; const maxAttempts = 30; while (attempts < maxAttempts) { try { const response = await request.get(HEALTH_URL); if (response.ok()) { console.log('[COMPLETE] Backend is ready'); break; } } catch (error) { console.log(`⏳ Waiting for backend... (${attempts + 1}/${maxAttempts})`); await new Promise(resolve => setTimeout(resolve, 2000)); attempts++; } } if (attempts >= maxAttempts) { throw new Error('[FAILED] Backend failed to start within timeout'); } // Créer un token d'authentification pour tous les tests await ensureAuthToken(request); }); test.describe('[SEARCH] Health Check', () => { test('should return health status', async ({ request }) => { const response = await request.get(HEALTH_URL); expect(response.ok()).toBeTruthy(); const data = await response.json(); expect(data.status).toBe('OK'); expect(data.version).toBe('1.0.0'); expect(data.services).toBeDefined(); }); }); test.describe('[SECURITY] Authentication', () => { test('should register a new user', async ({ request }) => { const response = await request.post(`${API_BASE_URL}/auth/register`, { data: testUser }); expect(response.ok()).toBeTruthy(); const data = await response.json(); expect(data.user.email).toBe(testUser.email); expect(data.user.profile.firstName).toBe(testUser.firstName); expect(data.token).toBeDefined(); // Sauvegarder le token authToken = data.token; }); test('should login with valid credentials', async ({ request }) => { const response = await request.post(`${API_BASE_URL}/auth/login`, { data: { email: testUser.email, password: testUser.password } }); expect(response.ok()).toBeTruthy(); const data = await response.json(); expect(data.user.email).toBe(testUser.email); expect(data.token).toBeDefined(); // Mettre à jour le token authToken = data.token; }); test('should fail login with invalid credentials', async ({ request }) => { const response = await request.post(`${API_BASE_URL}/auth/login`, { data: { email: testUser.email, password: 'wrongpassword' } }); expect(response.status()).toBe(401); const data = await response.json(); expect(data.error).toBe('Invalid credentials'); }); }); test.describe(' Chat System', () => { test('should start a new conversation', async ({ request }) => { const response = await request.post(`${API_BASE_URL}/chat/conversations/start`, { headers: { 'Authorization': `Bearer ${authToken}` }, data: { channel: 'web' } }); expect(response.ok()).toBeTruthy(); const data = await response.json(); expect(data.conversationId).toBeDefined(); expect(data.sessionId).toBeDefined(); expect(data.welcomeMessage).toBeDefined(); // Sauvegarder l'ID de conversation conversationId = data.conversationId; }); test('should send and receive messages', async ({ request }) => { const testMessage = "Bonjour, je veux voir ma consommation"; const response = await request.post(`${API_BASE_URL}/chat/messages/send`, { headers: { 'Authorization': `Bearer ${authToken}` }, data: { conversationId: conversationId, message: testMessage } }); expect(response.ok()).toBeTruthy(); const data = await response.json(); expect(data.response).toBeDefined(); expect(data.messageId).toBeDefined(); expect(data.sentiment).toBeDefined(); // Vérifier que le sentiment est analysé expect(['neutral', 'happy', 'sad', 'angry', 'frustrated', 'satisfied']).toContain(data.sentiment); }); test('should detect negative sentiment and escalation', async ({ request }) => { const negativeMessage = "C'est inadmissible, ça marche pas du tout, je vais résilier !"; const response = await request.post(`${API_BASE_URL}/chat/messages/send`, { headers: { 'Authorization': `Bearer ${authToken}` }, data: { conversationId: conversationId, message: negativeMessage } }); expect(response.ok()).toBeTruthy(); const data = await response.json(); expect(data.response).toBeDefined(); expect(data.sentiment).toBeDefined(); // Vérifier la détection de sentiment négatif expect(['angry', 'frustrated', 'sad']).toContain(data.sentiment); }); test('should retrieve conversation history', async ({ request }) => { const response = await request.get(`${API_BASE_URL}/chat/conversations/${conversationId}`, { headers: { 'Authorization': `Bearer ${authToken}` } }); expect(response.ok()).toBeTruthy(); const data = await response.json(); expect(data.conversation).toBeDefined(); expect(data.messages).toBeDefined(); expect(Array.isArray(data.messages)).toBeTruthy(); expect(data.messages.length).toBeGreaterThan(0); }); }); test.describe('[MOBILE] Subscription Management', () => { test('should get available plans', async ({ request }) => { const response = await request.get(`${API_BASE_URL}/subscription/plans/available`, { headers: { 'Authorization': `Bearer ${authToken}` } }); expect(response.ok()).toBeTruthy(); const data = await response.json(); expect(Array.isArray(data)).toBeTruthy(); expect(data.length).toBeGreaterThan(0); expect(data[0]).toHaveProperty('name'); expect(data[0]).toHaveProperty('price'); }); test('should get current plan', async ({ request }) => { // Note: Peut retourner une erreur si l'utilisateur n'a pas de profil client const response = await request.get(`${API_BASE_URL}/subscription/plans/current`, { headers: { 'Authorization': `Bearer ${authToken}` } }); // Accepter soit un succès avec des données, soit une erreur de profil manquant const data = await response.json(); if (response.ok()) { expect(data.subscription).toBeDefined(); } else { expect(data.error).toContain('Profil client non trouvé'); } }); test('should simulate plan change', async ({ request }) => { const response = await request.post(`${API_BASE_URL}/subscription/plans/simulate`, { headers: { 'Authorization': `Bearer ${authToken}`, 'Content-Type': 'application/json' }, data: { newPlanId: 'free_5g_200' } }); const data = await response.json(); if (response.ok()) { expect(data.current).toBeDefined(); expect(data.new).toBeDefined(); } else { expect(data.error).toContain('Profil client non trouvé'); } }); test('should get available options', async ({ request }) => { const response = await request.get(`${API_BASE_URL}/subscription/options/available`, { headers: { 'Authorization': `Bearer ${authToken}` } }); expect(response.ok()).toBeTruthy(); const data = await response.json(); expect(Array.isArray(data)).toBeTruthy(); }); }); test.describe(' Notifications', () => { test('should get user notifications', async ({ request }) => { const response = await request.get(`${API_BASE_URL}/notifications`, { headers: { 'Authorization': `Bearer ${authToken}` } }); expect(response.ok()).toBeTruthy(); const data = await response.json(); expect(Array.isArray(data)).toBeTruthy(); // Peut être vide pour un nouvel utilisateur }); test('should trigger proactive notifications', async ({ request }) => { const response = await request.post(`${API_BASE_URL}/notifications/trigger-proactive`, { headers: { 'Authorization': `Bearer ${authToken}` } }); expect(response.ok()).toBeTruthy(); const data = await response.json(); expect(data.success).toBeTruthy(); expect(data.count).toBeDefined(); expect(Array.isArray(data.notifications)).toBeTruthy(); }); test('should get notification stats', async ({ request }) => { const response = await request.get(`${API_BASE_URL}/notifications/stats`, { headers: { 'Authorization': `Bearer ${authToken}` } }); expect(response.ok()).toBeTruthy(); const data = await response.json(); expect(data.total).toBeDefined(); expect(data.unread).toBeDefined(); expect(data.byType).toBeDefined(); expect(data.byStatus).toBeDefined(); }); }); test.describe(' Security & Authorization', () => { test('should reject requests without authentication', async ({ request }) => { const response = await request.get(`${API_BASE_URL}/chat/conversations/start`); expect(response.status()).toBe(401); }); test('should reject requests with invalid token', async ({ request }) => { const response = await request.get(`${API_BASE_URL}/chat/conversations/start`, { headers: { 'Authorization': 'Bearer invalid-token' } }); expect(response.status()).toBe(401); }); }); test.describe('[PERFORMANCE] Performance & Reliability', () => { test('should handle multiple concurrent requests', async ({ request }) => { const requests = Array(5).fill().map(() => request.get(`${API_BASE_URL}/subscription/plans/available`, { headers: { 'Authorization': `Bearer ${authToken}` } }) ); const responses = await Promise.all(requests); responses.forEach(response => { expect(response.ok()).toBeTruthy(); }); }); test('should respond within acceptable time limits', async ({ request }) => { const start = Date.now(); const response = await request.get(`${API_BASE_URL}/subscription/plans/available`, { headers: { 'Authorization': `Bearer ${authToken}` } }); const duration = Date.now() - start; expect(response.ok()).toBeTruthy(); expect(duration).toBeLessThan(5000); // 5 secondes max }); }); test.describe('[FEATURE] Business Logic', () => { test('should handle consumption check request', async ({ request }) => { const response = await request.post(`${API_BASE_URL}/chat/messages/send`, { headers: { 'Authorization': `Bearer ${authToken}` }, data: { conversationId: conversationId, message: "combien il me reste de data?" } }); expect(response.ok()).toBeTruthy(); const data = await response.json(); expect(data.response).toBeDefined(); expect(data.intent).toBeDefined(); // Vérifier que l'intention est correctement détectée if (data.intent.confidence > 0.7) { expect(data.intent.name).toBe('check_consumption'); } }); test('should handle plan change request', async ({ request }) => { const response = await request.post(`${API_BASE_URL}/chat/messages/send`, { headers: { 'Authorization': `Bearer ${authToken}` }, data: { conversationId: conversationId, message: "je veux passer au forfait 5G" } }); expect(response.ok()).toBeTruthy(); const data = await response.json(); expect(data.response).toBeDefined(); expect(data.intent).toBeDefined(); // Vérifier que l'intention est correctement détectée if (data.intent.confidence > 0.7) { expect(data.intent.name).toBe('change_plan'); } }); test('should provide Rich Message responses', async ({ request }) => { const response = await request.post(`${API_BASE_URL}/chat/messages/send`, { headers: { 'Authorization': `Bearer ${authToken}` }, data: { conversationId: conversationId, message: "voir les forfaits disponibles" } }); expect(response.ok()).toBeTruthy(); const data = await response.json(); expect(data.response).toBeDefined(); // Vérifier si des éléments Rich Message sont présents if (data.type !== 'text') { expect(data.type).toBeDefined(); expect(data.payload).toBeDefined(); } }); }); test.describe(' Integration Tests', () => { test('should complete a full user journey', async ({ request }) => { // 1. Démarrer une nouvelle conversation const conversationResponse = await request.post(`${API_BASE_URL}/chat/conversations/start`, { headers: { 'Authorization': `Bearer ${authToken}` }, data: { channel: 'web' } }); expect(conversationResponse.ok()).toBeTruthy(); const { conversationId: newConversationId } = await conversationResponse.json(); // 2. Envoyer un message de salutation const greetResponse = await request.post(`${API_BASE_URL}/chat/messages/send`, { headers: { 'Authorization': `Bearer ${authToken}` }, data: { conversationId: newConversationId, message: "Bonjour" } }); expect(greetResponse.ok()).toBeTruthy(); // 3. Demander la consommation const consumptionResponse = await request.post(`${API_BASE_URL}/chat/messages/send`, { headers: { 'Authorization': `Bearer ${authToken}` }, data: { conversationId: newConversationId, message: "Ma consommation s'il vous plait" } }); expect(consumptionResponse.ok()).toBeTruthy(); // 4. Vérifier l'historique const historyResponse = await request.get(`${API_BASE_URL}/chat/conversations/${newConversationId}`, { headers: { 'Authorization': `Bearer ${authToken}` } }); expect(historyResponse.ok()).toBeTruthy(); const historyData = await historyResponse.json(); expect(historyData.messages.length).toBeGreaterThanOrEqual(4); // Au moins 4 messages }); }); }); // Helper functions for extended testing test.describe(' AI & NLP Features', () => { test('should handle various greeting formats', async ({ request }) => { const greetings = ['salut', 'bonjour', 'hey', 'coucou', 'bonsoir']; for (const greeting of greetings) { const response = await request.post(`${API_BASE_URL}/chat/messages/send`, { headers: { 'Authorization': `Bearer ${authToken}` }, data: { conversationId: conversationId, message: greeting } }); expect(response.ok()).toBeTruthy(); const data = await response.json(); expect(data.response).toBeDefined(); expect(data.sentiment).toBeDefined(); } }); test('should maintain conversation context', async ({ request }) => { // Premier message const firstResponse = await request.post(`${API_BASE_URL}/chat/messages/send`, { headers: { 'Authorization': `Bearer ${authToken}` }, data: { conversationId: conversationId, message: "Je veux changer de forfait" } }); expect(firstResponse.ok()).toBeTruthy(); // Message de suivi qui fait référence au premier const followUpResponse = await request.post(`${API_BASE_URL}/chat/messages/send`, { headers: { 'Authorization': `Bearer ${authToken}` }, data: { conversationId: conversationId, message: "Celui à 29,99 euros" } }); expect(followUpResponse.ok()).toBeTruthy(); const data = await followUpResponse.json(); expect(data.response).toBeDefined(); // Le contexte devrait être maintenu }); }); console.log(` [TARGET] Tests Backend - Résumé des Fonctionnalités Testées : [COMPLETE] Health Check [COMPLETE] Authentication (Register/Login) [COMPLETE] Chat System (Start/Send/History) [COMPLETE] Sentiment Analysis [COMPLETE] Subscription Management [COMPLETE] Notifications [COMPLETE] Security & Authorization [COMPLETE] Performance & Reliability [COMPLETE] Business Logic [COMPLETE] AI & NLP Features [COMPLETE] Integration Tests [DEPLOY] Prêt pour la validation complète ! `);