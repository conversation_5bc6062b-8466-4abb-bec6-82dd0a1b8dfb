/** * ============================================= * [DEPLOY] GLOBAL TEST SETUP * Initialize test environment and dependencies * ============================================= */ import { chromium, FullConfig } from '@playwright/test'; import { testConfig } from './playwright.config'; async function globalSetup(config: FullConfig) { console.log('[DEPLOY] Starting global test setup...'); // Create browser instance for setup const browser = await chromium.launch(); const context = await browser.newContext(); const page = await context.newPage(); try { // 1. Wait for services to be ready await waitForServices(); // 2. Setup test database await setupTestDatabase(); // 3. Create test users await createTestUsers(page); // 4. Initialize ML models await initializeMLModels(); // 5. Setup emergency call test environment await setupEmergencyCallEnvironment(); // 6. Setup test data await setupTestData(); console.log('[COMPLETE] Global test setup completed successfully'); } catch (error) { console.error('[FAILED] Global test setup failed:', error); throw error; } finally { await context.close(); await browser.close(); } } /** * Wait for all services to be ready */ async function waitForServices() { console.log('⏳ Waiting for services to be ready...'); const services = [ { name: 'Backend API', url: 'http://localhost:5000/health' }, { name: 'ML Service', url: 'http://localhost:5001/health' }, { name: 'Frontend', url: 'http://localhost:3001' }, { name: 'MongoDB', url: 'http://localhost:5000/api/health/database' }, { name: 'Redis', url: 'http://localhost:5000/api/health/cache' } ]; for (const service of services) { await waitForService(service.name, service.url); } console.log('[COMPLETE] All services are ready'); } /** * Wait for a specific service to be ready */ async function waitForService(name: string, url: string, maxRetries = 30) { console.log(`⏳ Waiting for ${name}...`); for (let i = 0; i < maxRetries; i++) { try { const response = await fetch(url); if (response.ok) { console.log(`[COMPLETE] ${name} is ready`); return; } } catch (error) { // Service not ready yet } await new Promise(resolve => setTimeout(resolve, 2000)); } throw new Error(`[FAILED] ${name} failed to start after ${maxRetries} retries`); } /** * Setup test database with clean state */ async function setupTestDatabase() { console.log(' Setting up test database...'); try { // Clear test collections const response = await fetch('http://localhost:5000/api/test/database/reset', { method: 'POST', headers: { 'Content-Type': 'application/json' } }); if (!response.ok) { throw new Error(`Database reset failed: ${response.statusText}`); } console.log('[COMPLETE] Test database setup completed'); } catch (error) { console.error('[FAILED] Database setup failed:', error); throw error; } } /** * Create test users for different roles */ async function createTestUsers(page: any) { console.log(' Creating test users...'); const users = [ testConfig.users.admin, testConfig.users.agent, testConfig.users.customer ]; for (const user of users) { try { const response = await fetch('http://localhost:5000/api/auth/register', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ email: user.email, password: user.password, name: `Test ${user.role}`, role: user.role }) }); if (response.ok) { console.log(`[COMPLETE] Created test user: ${user.email}`); } else if (response.status === 409) { console.log(`ℹ Test user already exists: ${user.email}`); } else { throw new Error(`Failed to create user ${user.email}: ${response.statusText}`); } } catch (error) { console.error(`[FAILED] Failed to create user ${user.email}:`, error); throw error; } } console.log('[COMPLETE] Test users setup completed'); } /** * Initialize ML models and services */ async function initializeMLModels() { console.log('[AI] Initializing ML models...'); try { // Initialize message suggestion models await fetch('http://localhost:5001/api/ml/suggestions/initialize', { method: 'POST' }); // Initialize auto-response models await fetch('http://localhost:5001/api/ml/auto-response/initialize', { method: 'POST' }); // Initialize routing models await fetch('http://localhost:5001/api/ml/routing/initialize', { method: 'POST' }); // Initialize sentiment models await fetch('http://localhost:5001/api/ml/sentiment/initialize', { method: 'POST' }); // Wait for models to load await new Promise(resolve => setTimeout(resolve, 10000)); console.log('[COMPLETE] ML models initialized'); } catch (error) { console.error('[FAILED] ML models initialization failed:', error); throw error; } } /** * Setup test data for comprehensive testing */ async function setupTestData() { console.log('[ANALYTICS] Setting up test data...'); try { // Create test conversations await createTestConversations(); // Create test agents await createTestAgents(); // Create test customers await createTestCustomers(); // Create test messages await createTestMessages(); console.log('[COMPLETE] Test data setup completed'); } catch (error) { console.error('[FAILED] Test data setup failed:', error); throw error; } } /** * Create test conversations */ async function createTestConversations() { const conversations = [ { id: 'test-conv-1', customerId: 'test-customer-1', platform: 'whatsapp', status: 'open', urgency: 'medium', sentiment: 'neutral' }, { id: 'test-conv-2', customerId: 'test-customer-2', platform: 'facebook', status: 'assigned', urgency: 'high', sentiment: 'negative', assignedAgent: 'test-agent-1' }, { id: 'test-conv-3', customerId: 'test-customer-3', platform: 'instagram', status: 'resolved', urgency: 'low', sentiment: 'positive' } ]; for (const conv of conversations) { await fetch('http://localhost:5000/api/test/conversations', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(conv) }); } } /** * Create test agents */ async function createTestAgents() { const agents = [ { id: 'test-agent-1', name: 'Agent Test 1', email: '<EMAIL>', skills: ['technical_support', 'billing_support'], status: 'available', platformSupport: { whatsapp: true, facebook: true, instagram: true, twitter: true } }, { id: 'test-agent-2', name: 'Agent Test 2', email: '<EMAIL>', skills: ['sales_support', 'customer_service'], status: 'busy', platformSupport: { whatsapp: true, facebook: true, instagram: false, twitter: true } } ]; for (const agent of agents) { await fetch('http://localhost:5000/api/test/agents', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(agent) }); } } /** * Create test customers */ async function createTestCustomers() { const customers = [ { id: 'test-customer-1', name: 'Customer Test 1', email: '<EMAIL>', segment: 'standard', phoneNumber: '+33123456789' }, { id: 'test-customer-2', name: 'Customer VIP Test', email: '<EMAIL>', segment: 'vip', phoneNumber: '+33987654321' }, { id: 'test-customer-3', name: 'Customer Test 3', email: '<EMAIL>', segment: 'standard', phoneNumber: '+33555666777' } ]; for (const customer of customers) { await fetch('http://localhost:5000/api/test/customers', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(customer) }); } } /** * Create test messages */ async function createTestMessages() { const messages = [ { conversationId: 'test-conv-1', text: 'Bonjour, j\'ai un problème avec mon forfait', direction: 'inbound', platform: 'whatsapp', timestamp: new Date() }, { conversationId: 'test-conv-2', text: 'Je suis très mécontent du service', direction: 'inbound', platform: 'facebook', timestamp: new Date(), analysis: { sentiment: { label: 'negative', score: 0.2, confidence: 0.9 }, intent: { primary: 'complaint', confidence: 0.8 } } } ]; for (const message of messages) { await fetch('http://localhost:5000/api/test/messages', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(message) }); } } /** * Setup emergency call test environment */ async function setupEmergencyCallEnvironment() { console.log(' Setting up emergency call test environment...'); try { // Verify emergency call service health const healthResponse = await fetch('http://localhost:5000/api/emergency-calls/health'); if (!healthResponse.ok) { throw new Error('Emergency call service not available'); } const healthData = await healthResponse.json(); console.log('[COMPLETE] Emergency call service health check passed:', healthData); // Create emergency call test users with specific permissions const emergencyTestUsers = [ { email: '<EMAIL>', password: 'EmergencyTest123!', role: 'customer', profile: { firstName: 'Emergency', lastName: 'Customer', phone: '+33123456789' }, emergencyCallPermissions: { canInitiate: true, canEscalate: true, maxCallsPerHour: 5 } }, { email: '<EMAIL>', password: 'EmergencyAgent123!', role: 'agent', profile: { firstName: 'Emergency', lastName: 'Agent', phone: '+33987654321', skills: ['emergency_support', 'priority_support'], status: 'available' }, emergencyCallPermissions: { canConnect: true, canEscalate: true, canViewQueue: true } } ]; // Create emergency test users for (const user of emergencyTestUsers) { try { await fetch('http://localhost:5000/api/test/users', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(user) }); console.log(`[COMPLETE] Created emergency test user: ${user.email}`); } catch (error) { console.log(`ℹ Emergency test user already exists: ${user.email}`); } } // Setup sample emergency call data const sampleEmergencyCall = { emergencyCallId: 'test-emergency-call-e2e', userId: 'emergency-customer-id', urgencyLevel: 'high', description: 'Sample emergency call for E2E testing', status: 'in_queue', queueInfo: { position: 1, estimatedWaitTime: 45000, priority: 'high' }, compliance: { gdprCompliant: true, recordingConsent: true, dataRetentionNotified: true } }; await fetch('http://localhost:5000/api/test/emergency-calls', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(sampleEmergencyCall) }); console.log('[COMPLETE] Emergency call test environment setup complete'); } catch (error) { console.error('[FAILED] Emergency call environment setup failed:', error); throw error; } } export default globalSetup;