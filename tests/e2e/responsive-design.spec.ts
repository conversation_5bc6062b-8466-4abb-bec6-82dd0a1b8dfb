import { test, expect, Page } from '@playwright/test'; test.describe('Responsive Design - Cross-Device Testing', () => { let page: Page; test.beforeEach(async ({ browser }) => { page = await browser.newPage(); // Authenticate if needed await page.goto('/'); await page.waitForLoadState('networkidle'); const loginButton = page.locator('button:has-text("Se connecter")'); if (await loginButton.isVisible()) { await page.fill('input[name="email"], input[id="email"]', '<EMAIL>'); await page.fill('input[name="password"], input[id="password"]', 'password'); await loginButton.click(); await page.waitForLoadState('networkidle'); } }); test.afterEach(async () => { await page.close(); }); test.describe('Desktop Viewports', () => { test('should work on large desktop (1920x1080)', async () => { await page.setViewportSize({ width: 1920, height: 1080 }); await page.goto('/dashboard/administrator'); await page.waitForLoadState('networkidle'); // Verify all elements are visible and properly laid out await expect(page.locator('h4:has-text("Panneau Administrateur")')).toBeVisible(); // Check that all three tabs are visible horizontally await expect(page.locator('[role="tab"]:has-text("Notifications")')).toBeVisible(); await expect(page.locator('[role="tab"]:has-text("Suggestions IA")')).toBeVisible(); await expect(page.locator('[role="tab"]:has-text("ML Intelligence")')).toBeVisible(); // Verify statistics cards are in grid layout await page.click('[role="tab"]:has-text("Notifications")'); await page.waitForTimeout(500); const statsCards = page.locator('[role="tabpanel"] .MuiGrid-item'); const cardCount = await statsCards.count(); expect(cardCount).toBeGreaterThanOrEqual(4); // Should have 4 statistics cards // Verify cards are properly spaced const firstCard = statsCards.first(); const cardBox = await firstCard.boundingBox(); expect(cardBox?.width).toBeGreaterThan(200); // Cards should have adequate width }); test('should work on standard desktop (1366x768)', async () => { await page.setViewportSize({ width: 1366, height: 768 }); await page.goto('/dashboard/administrator'); await page.waitForLoadState('networkidle'); // Verify layout adapts to smaller desktop await expect(page.locator('h4:has-text("Panneau Administrateur")')).toBeVisible(); await expect(page.locator('[role="tab"]:has-text("Notifications")')).toBeVisible(); // Check that content is still accessible await page.click('[role="tab"]:has-text("Suggestions IA")'); await page.waitForTimeout(500); await expect(page.locator('text=Total suggestions')).toBeVisible(); }); }); test.describe('Tablet Viewports', () => { test('should work on iPad (768x1024)', async () => { await page.setViewportSize({ width: 768, height: 1024 }); await page.goto('/dashboard/administrator'); await page.waitForLoadState('networkidle'); // Verify tablet layout await expect(page.locator('h4:has-text("Panneau Administrateur")')).toBeVisible(); // Tabs should still be horizontal but may be smaller await expect(page.locator('[role="tab"]:has-text("Notifications")')).toBeVisible(); await expect(page.locator('[role="tab"]:has-text("Suggestions IA")')).toBeVisible(); // Test tab switching on tablet await page.click('[role="tab"]:has-text("ML Intelligence")'); await page.waitForTimeout(500); await expect(page.locator('text=Total modèles')).toBeVisible(); // Verify statistics cards adapt to tablet width const statsCards = page.locator('[role="tabpanel"] .MuiGrid-item'); const cardCount = await statsCards.count(); expect(cardCount).toBeGreaterThanOrEqual(4); }); test('should work on iPad Mini (768x1024) landscape', async () => { await page.setViewportSize({ width: 1024, height: 768 }); await page.goto('/dashboard/administrator'); await page.waitForLoadState('networkidle'); // Verify landscape tablet layout await expect(page.locator('h4:has-text("Panneau Administrateur")')).toBeVisible(); // All tabs should be visible in landscape await expect(page.locator('[role="tab"]:has-text("Notifications")')).toBeVisible(); await expect(page.locator('[role="tab"]:has-text("Suggestions IA")')).toBeVisible(); await expect(page.locator('[role="tab"]:has-text("ML Intelligence")')).toBeVisible(); }); }); test.describe('Mobile Viewports', () => { test('should work on iPhone (375x667)', async () => { await page.setViewportSize({ width: 375, height: 667 }); await page.goto('/dashboard/administrator'); await page.waitForLoadState('networkidle'); // Verify mobile layout await expect(page.locator('h4:has-text("Panneau Administrateur")')).toBeVisible(); // Tabs should be visible but may be stacked or scrollable await expect(page.locator('[role="tab"]:has-text("Notifications")')).toBeVisible(); // Test tab functionality on mobile await page.click('[role="tab"]:has-text("Notifications")'); await page.waitForTimeout(500); await expect(page.locator('text=Total notifications')).toBeVisible(); // Verify statistics cards stack vertically on mobile const statsCards = page.locator('[role="tabpanel"] .MuiGrid-item'); const firstCard = statsCards.first(); const secondCard = statsCards.nth(1); if (await firstCard.isVisible() && await secondCard.isVisible()) { const firstCardBox = await firstCard.boundingBox(); const secondCardBox = await secondCard.boundingBox(); // On mobile, cards should stack vertically (second card below first) expect(secondCardBox?.y).toBeGreaterThan(firstCardBox?.y || 0); } }); test('should work on Android (360x640)', async () => { await page.setViewportSize({ width: 360, height: 640 }); await page.goto('/dashboard/administrator'); await page.waitForLoadState('networkidle'); // Verify Android mobile layout await expect(page.locator('h4:has-text("Panneau Administrateur")')).toBeVisible(); // Test all tabs work on small Android screen await page.click('[role="tab"]:has-text("Suggestions IA")'); await page.waitForTimeout(500); await expect(page.locator('text=Total suggestions')).toBeVisible(); await page.click('[role="tab"]:has-text("ML Intelligence")'); await page.waitForTimeout(500); await expect(page.locator('text=Total modèles')).toBeVisible(); }); test('should work on large mobile (414x896)', async () => { await page.setViewportSize({ width: 414, height: 896 }); await page.goto('/dashboard/administrator'); await page.waitForLoadState('networkidle'); // Verify large mobile layout await expect(page.locator('h4:has-text("Panneau Administrateur")')).toBeVisible(); // Should have better layout on larger mobile screens await expect(page.locator('[role="tab"]:has-text("Notifications")')).toBeVisible(); await expect(page.locator('[role="tab"]:has-text("Suggestions IA")')).toBeVisible(); await expect(page.locator('[role="tab"]:has-text("ML Intelligence")')).toBeVisible(); }); }); test.describe('Touch Interactions', () => { test('should handle touch interactions on tablet', async () => { await page.setViewportSize({ width: 768, height: 1024 }); await page.goto('/dashboard/administrator'); await page.waitForLoadState('networkidle'); // Simulate touch interactions await page.tap('[role="tab"]:has-text("Suggestions IA")'); await page.waitForTimeout(500); await expect(page.locator('text=Total suggestions')).toBeVisible(); await page.tap('[role="tab"]:has-text("ML Intelligence")'); await page.waitForTimeout(500); await expect(page.locator('text=Total modèles')).toBeVisible(); }); test('should handle touch interactions on mobile', async () => { await page.setViewportSize({ width: 375, height: 667 }); await page.goto('/dashboard/administrator'); await page.waitForLoadState('networkidle'); // Test touch interactions on mobile await page.tap('[role="tab"]:has-text("Notifications")'); await page.waitForTimeout(500); await expect(page.locator('text=Total notifications')).toBeVisible(); }); }); test.describe('Content Overflow and Scrolling', () => { test('should handle content overflow on small screens', async () => { await page.setViewportSize({ width: 320, height: 568 }); // Very small screen await page.goto('/dashboard/administrator'); await page.waitForLoadState('networkidle'); // Content should be accessible even on very small screens await expect(page.locator('h4:has-text("Panneau Administrateur")')).toBeVisible(); // Should be able to scroll if needed await page.click('[role="tab"]:has-text("Notifications")'); await page.waitForTimeout(500); // Check if content is scrollable const tabPanel = page.locator('[role="tabpanel"]'); await expect(tabPanel).toBeVisible(); }); test('should maintain usability with horizontal scrolling if needed', async () => { await page.setViewportSize({ width: 320, height: 568 }); await page.goto('/dashboard/administrator'); await page.waitForLoadState('networkidle'); // Tabs might require horizontal scrolling on very small screens const tabsContainer = page.locator('[role="tablist"]'); await expect(tabsContainer).toBeVisible(); // All tabs should still be accessible await page.click('[role="tab"]:has-text("Notifications")'); await page.waitForTimeout(500); await expect(page.locator('text=Total notifications')).toBeVisible(); }); }); test.describe('Orientation Changes', () => { test('should handle portrait to landscape on mobile', async () => { // Start in portrait await page.setViewportSize({ width: 375, height: 667 }); await page.goto('/dashboard/administrator'); await page.waitForLoadState('networkidle'); await expect(page.locator('h4:has-text("Panneau Administrateur")')).toBeVisible(); // Switch to landscape await page.setViewportSize({ width: 667, height: 375 }); await page.waitForTimeout(1000); // Should still work in landscape await expect(page.locator('h4:has-text("Panneau Administrateur")')).toBeVisible(); await expect(page.locator('[role="tab"]:has-text("Notifications")')).toBeVisible(); }); test('should handle landscape to portrait on tablet', async () => { // Start in landscape await page.setViewportSize({ width: 1024, height: 768 }); await page.goto('/dashboard/administrator'); await page.waitForLoadState('networkidle'); await expect(page.locator('h4:has-text("Panneau Administrateur")')).toBeVisible(); // Switch to portrait await page.setViewportSize({ width: 768, height: 1024 }); await page.waitForTimeout(1000); // Should adapt to portrait orientation await expect(page.locator('h4:has-text("Panneau Administrateur")')).toBeVisible(); await expect(page.locator('[role="tab"]:has-text("Notifications")')).toBeVisible(); }); }); test.describe('Performance on Different Devices', () => { test('should load quickly on mobile devices', async () => { await page.setViewportSize({ width: 375, height: 667 }); const startTime = Date.now(); await page.goto('/dashboard/administrator'); await page.waitForLoadState('networkidle'); const loadTime = Date.now() - startTime; // Should load within reasonable time on mobile expect(loadTime).toBeLessThan(15000); // 15 seconds for mobile await expect(page.locator('h4:has-text("Panneau Administrateur")')).toBeVisible(); }); test('should be responsive during tab switching on mobile', async () => { await page.setViewportSize({ width: 375, height: 667 }); await page.goto('/dashboard/administrator'); await page.waitForLoadState('networkidle'); const startTime = Date.now(); // Quick tab switching await page.click('[role="tab"]:has-text("Suggestions IA")'); await page.waitForTimeout(100); await page.click('[role="tab"]:has-text("ML Intelligence")'); await page.waitForTimeout(100); await page.click('[role="tab"]:has-text("Notifications")'); const switchTime = Date.now() - startTime; expect(switchTime).toBeLessThan(3000); // Should be responsive on mobile }); }); });