import { test, expect, Page } from '@playwright/test'; test.describe('Administrator Panel - Comprehensive E2E Testing', () => { let page: Page; test.beforeEach(async ({ browser }) => { page = await browser.newPage(); // Navigate to the application await page.goto('/'); // Wait for the application to load await page.waitForLoadState('networkidle'); // Check if we need to authenticate (mock authentication) const loginButton = page.locator('button:has-text("Se connecter")'); if (await loginButton.isVisible()) { // Fill in mock credentials await page.fill('input[name="email"]', '<EMAIL>'); await page.fill('input[name="password"]', 'password'); await loginButton.click(); await page.waitForLoadState('networkidle'); } }); test.afterEach(async () => { await page.close(); }); test.describe('Navigation and Access', () => { test('should navigate to Administrator Panel', async () => { // Navigate to Administrator Panel await page.goto('/dashboard/administrator'); await page.waitForLoadState('networkidle'); // Verify we're on the Administrator Panel await expect(page.locator('h4:has-text("Panneau Administrateur")')).toBeVisible(); // Verify Free Mobile branding await expect(page.locator('text=Free Mobile')).toBeVisible(); // Verify all three tabs are present await expect(page.locator('text=Notifications')).toBeVisible(); await expect(page.locator('text=Suggestions IA Avancées')).toBeVisible(); await expect(page.locator('text=Tableau de Bord ML Intelligence')).toBeVisible(); }); test('should display badge notifications on tabs', async () => { await page.goto('/dashboard/administrator'); await page.waitForLoadState('networkidle'); // Check for badge notifications (numbers on tabs) const notificationsBadge = page.locator('[role="tab"]:has-text("Notifications") .MuiBadge-badge'); const aiSuggestionsBadge = page.locator('[role="tab"]:has-text("Suggestions IA") .MuiBadge-badge'); const mlDashboardBadge = page.locator('[role="tab"]:has-text("ML Intelligence") .MuiBadge-badge'); // Badges should be visible and contain numbers if (await notificationsBadge.isVisible()) { const badgeText = await notificationsBadge.textContent(); expect(parseInt(badgeText || '0')).toBeGreaterThanOrEqual(0); } }); }); test.describe('Notifications Tab', () => { test.beforeEach(async () => { await page.goto('/dashboard/administrator'); await page.waitForLoadState('networkidle'); // Click on Notifications tab await page.click('[role="tab"]:has-text("Notifications")'); await page.waitForTimeout(1000); }); test('should display notifications statistics', async () => { // Verify statistics cards are present await expect(page.locator('text=Total notifications')).toBeVisible(); await expect(page.locator('text=Non lues')).toBeVisible(); await expect(page.locator('text=Action requise')).toBeVisible(); await expect(page.locator('text=Succès')).toBeVisible(); // Verify statistics have numbers const totalNotifications = page.locator('h4').first(); await expect(totalNotifications).toBeVisible(); }); test('should display notification system info', async () => { // Verify the info alert is present await expect(page.locator('text=Système de notifications complet')).toBeVisible(); await expect(page.locator('text=Toutes les fonctionnalités de notification sont entièrement opérationnelles')).toBeVisible(); }); test('should have Free Mobile branding colors', async () => { // Check for Free Mobile primary color (#ed1c24) in statistics cards const primaryColorCard = page.locator('[style*="#ed1c24"]').first(); await expect(primaryColorCard).toBeVisible(); }); }); test.describe('Advanced AI Suggestions Tab', () => { test.beforeEach(async () => { await page.goto('/dashboard/administrator'); await page.waitForLoadState('networkidle'); // Click on AI Suggestions tab await page.click('[role="tab"]:has-text("Suggestions IA")'); await page.waitForTimeout(1000); }); test('should display AI suggestions statistics', async () => { // Verify statistics cards are present await expect(page.locator('text=Total suggestions')).toBeVisible(); await expect(page.locator('text=En attente')).toBeVisible(); await expect(page.locator('text=Implémentées')).toBeVisible(); await expect(page.locator('text=Confiance moyenne')).toBeVisible(); // Verify confidence percentage is displayed const confidencePercentage = page.locator('text=%').first(); await expect(confidencePercentage).toBeVisible(); }); test('should display AI suggestions info', async () => { // Verify the info alert is present await expect(page.locator('text=Interface avancée de gestion des suggestions IA')).toBeVisible(); await expect(page.locator('text=Toutes les fonctionnalités sont entièrement opérationnelles')).toBeVisible(); }); test('should have proper color coding for different metrics', async () => { // Check for different colored statistics cards const orangeCard = page.locator('[style*="#ff9800"]').first(); const greenCard = page.locator('[style*="#4caf50"]').first(); const blueCard = page.locator('[style*="#2196f3"]').first(); await expect(orangeCard).toBeVisible(); await expect(greenCard).toBeVisible(); await expect(blueCard).toBeVisible(); }); }); test.describe('ML Intelligence Dashboard Tab', () => { test.beforeEach(async () => { await page.goto('/dashboard/administrator'); await page.waitForLoadState('networkidle'); // Click on ML Intelligence tab await page.click('[role="tab"]:has-text("ML Intelligence")'); await page.waitForTimeout(1000); }); test('should display ML dashboard statistics', async () => { // Verify statistics cards are present await expect(page.locator('text=Total modèles')).toBeVisible(); await expect(page.locator('text=Modèles actifs')).toBeVisible(); await expect(page.locator('text=Alertes ML')).toBeVisible(); await expect(page.locator('text=Précision moyenne')).toBeVisible(); }); test('should display ML dashboard info', async () => { // Verify the info alert is present await expect(page.locator('text=Tableau de bord ML Intelligence complet')).toBeVisible(); await expect(page.locator('text=Toutes les fonctionnalités ML sont entièrement opérationnelles')).toBeVisible(); }); test('should have tabbed interface', async () => { // Check for ML dashboard tabs await expect(page.locator('text=Modèles')).toBeVisible(); await expect(page.locator('text=Alertes')).toBeVisible(); await expect(page.locator('text=Performance')).toBeVisible(); }); }); test.describe('Tab Switching and Navigation', () => { test('should switch between tabs smoothly', async () => { await page.goto('/dashboard/administrator'); await page.waitForLoadState('networkidle'); // Test switching between all tabs await page.click('[role="tab"]:has-text("Notifications")'); await page.waitForTimeout(500); await expect(page.locator('text=Total notifications')).toBeVisible(); await page.click('[role="tab"]:has-text("Suggestions IA")'); await page.waitForTimeout(500); await expect(page.locator('text=Total suggestions')).toBeVisible(); await page.click('[role="tab"]:has-text("ML Intelligence")'); await page.waitForTimeout(500); await expect(page.locator('text=Total modèles')).toBeVisible(); // Switch back to first tab await page.click('[role="tab"]:has-text("Notifications")'); await page.waitForTimeout(500); await expect(page.locator('text=Total notifications')).toBeVisible(); }); test('should maintain tab state during navigation', async () => { await page.goto('/dashboard/administrator'); await page.waitForLoadState('networkidle'); // Click on AI Suggestions tab await page.click('[role="tab"]:has-text("Suggestions IA")'); await page.waitForTimeout(500); // Verify the tab is active const activeTab = page.locator('[role="tab"][aria-selected="true"]'); await expect(activeTab).toContainText('Suggestions IA'); }); }); test.describe('Responsive Design', () => { test('should work on desktop viewport', async () => { await page.setViewportSize({ width: 1920, height: 1080 }); await page.goto('/dashboard/administrator'); await page.waitForLoadState('networkidle'); // Verify all elements are visible on desktop await expect(page.locator('h4:has-text("Panneau Administrateur")')).toBeVisible(); await expect(page.locator('[role="tab"]:has-text("Notifications")')).toBeVisible(); await expect(page.locator('[role="tab"]:has-text("Suggestions IA")')).toBeVisible(); await expect(page.locator('[role="tab"]:has-text("ML Intelligence")')).toBeVisible(); }); test('should work on tablet viewport', async () => { await page.setViewportSize({ width: 768, height: 1024 }); await page.goto('/dashboard/administrator'); await page.waitForLoadState('networkidle'); // Verify responsive layout await expect(page.locator('h4:has-text("Panneau Administrateur")')).toBeVisible(); await expect(page.locator('[role="tab"]:has-text("Notifications")')).toBeVisible(); }); test('should work on mobile viewport', async () => { await page.setViewportSize({ width: 375, height: 667 }); await page.goto('/dashboard/administrator'); await page.waitForLoadState('networkidle'); // Verify mobile layout await expect(page.locator('h4:has-text("Panneau Administrateur")')).toBeVisible(); await expect(page.locator('[role="tab"]:has-text("Notifications")')).toBeVisible(); }); }); test.describe('Performance and Loading', () => { test('should load within acceptable time', async () => { const startTime = Date.now(); await page.goto('/dashboard/administrator'); await page.waitForLoadState('networkidle'); const loadTime = Date.now() - startTime; expect(loadTime).toBeLessThan(10000); // Should load within 10 seconds }); test('should handle tab switching without delays', async () => { await page.goto('/dashboard/administrator'); await page.waitForLoadState('networkidle'); const startTime = Date.now(); // Switch between tabs quickly await page.click('[role="tab"]:has-text("Suggestions IA")'); await page.waitForTimeout(100); await page.click('[role="tab"]:has-text("ML Intelligence")'); await page.waitForTimeout(100); await page.click('[role="tab"]:has-text("Notifications")'); const switchTime = Date.now() - startTime; expect(switchTime).toBeLessThan(2000); // Should switch within 2 seconds }); }); test.describe('Error Handling', () => { test('should handle navigation errors gracefully', async () => { // Try to navigate to a non-existent admin route await page.goto('/dashboard/administrator/nonexistent'); await page.waitForLoadState('networkidle'); // Should redirect to main administrator panel or show error const currentUrl = page.url(); expect(currentUrl).toContain('/dashboard'); }); }); });