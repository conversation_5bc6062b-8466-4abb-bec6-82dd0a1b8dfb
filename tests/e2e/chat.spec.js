const { test, expect } = require('@playwright/test'); const TestHelpers = require('../utils/test-helpers'); test.describe('Chat Interface Tests', () => { let helpers; let testUsers; test.beforeEach(async ({ page }) => { helpers = new TestHelpers(page); // Load test users const fs = require('fs'); const testData = JSON.parse(fs.readFileSync('./tests/fixtures/test-data.json', 'utf8')); testUsers = testData.users; // Login and navigate to chat await helpers.login(testUsers.user); await page.goto('/chat'); }); test.describe('Chat Interface Layout', () => { test('should display chat interface correctly', async ({ page }) => { // Verify main chat components await expect(page.locator('[data-testid="chat-interface"]')).toBeVisible(); await expect(page.locator('[data-testid="chat-history"]')).toBeVisible(); await expect(page.locator('[data-testid="message-input"]')).toBeVisible(); await expect(page.locator('[data-testid="send-button"]')).toBeVisible(); }); test('should show chat header with user info', async ({ page }) => { const hasChatHeader = await helpers.elementExists('[data-testid="chat-header"]'); const hasUserInfo = await helpers.elementExists('[data-testid="chat-user-info"]'); expect(hasChatHeader || hasUserInfo).toBeTruthy(); }); test('should display typing indicator area', async ({ page }) => { const hasTypingIndicator = await helpers.elementExists('[data-testid="typing-indicator"]'); const hasStatusArea = await helpers.elementExists('[data-testid="chat-status"]'); // Either typing indicator or status area should exist expect(hasTypingIndicator || hasStatusArea).toBeTruthy(); }); }); test.describe('Message Sending', () => { test('should send a text message successfully', async ({ page }) => { const testMessage = 'Hello, I need help with my account'; // Type message await page.fill('[data-testid="message-input"]', testMessage); // Send message await page.click('[data-testid="send-button"]'); // Verify message appears in chat history await expect(page.locator('[data-testid="chat-history"]')).toContainText(testMessage); // Verify input is cleared await expect(page.locator('[data-testid="message-input"]')).toHaveValue(''); }); test('should send message using Enter key', async ({ page }) => { const testMessage = 'Testing Enter key functionality'; // Type message and press Enter await page.fill('[data-testid="message-input"]', testMessage); await page.press('[data-testid="message-input"]', 'Enter'); // Verify message appears await expect(page.locator('[data-testid="chat-history"]')).toContainText(testMessage); }); test('should not send empty messages', async ({ page }) => { // Try to send empty message await page.click('[data-testid="send-button"]'); // Verify no empty message is sent const messages = await page.locator('[data-testid="message"]').count(); const emptyMessages = await page.locator('[data-testid="message"]:has-text("")').count(); expect(emptyMessages).toBe(0); }); test('should handle long messages correctly', async ({ page }) => { const longMessage = 'A'.repeat(1000); // 1000 character message await page.fill('[data-testid="message-input"]', longMessage); await page.click('[data-testid="send-button"]'); // Verify long message is handled (either sent or truncated with warning) const messageExists = await helpers.elementExists(`[data-testid="message"]:has-text("${longMessage.substring(0, 50)}")`); const hasWarning = await helpers.elementExists('[data-testid="message-length-warning"]'); expect(messageExists || hasWarning).toBeTruthy(); }); }); test.describe('Message Display', () => { test('should display user messages with correct styling', async ({ page }) => { const testMessage = 'This is a user message'; await page.fill('[data-testid="message-input"]', testMessage); await page.click('[data-testid="send-button"]'); // Wait for message to appear await page.waitForSelector('[data-testid="user-message"]', { timeout: 5000 }); // Verify user message styling const userMessage = page.locator('[data-testid="user-message"]').last(); await expect(userMessage).toBeVisible(); await expect(userMessage).toContainText(testMessage); }); test('should display bot responses', async ({ page }) => { const testMessage = 'Hello'; await page.fill('[data-testid="message-input"]', testMessage); await page.click('[data-testid="send-button"]'); // Wait for bot response (with timeout) try { await page.waitForSelector('[data-testid="bot-message"]', { timeout: 10000 }); // Verify bot message exists const botMessage = page.locator('[data-testid="bot-message"]').last(); await expect(botMessage).toBeVisible(); } catch (error) { // Bot might not be configured - check for any response or error message const hasResponse = await helpers.elementExists('[data-testid="bot-message"]'); const hasError = await helpers.elementExists('[data-testid="chat-error"]'); expect(hasResponse || hasError).toBeTruthy(); } }); test('should show message timestamps', async ({ page }) => { const testMessage = 'Message with timestamp'; await page.fill('[data-testid="message-input"]', testMessage); await page.click('[data-testid="send-button"]'); // Check for timestamp const hasTimestamp = await helpers.elementExists('[data-testid="message-timestamp"]'); const hasTimeInfo = await helpers.elementExists('[data-testid="message-time"]'); expect(hasTimestamp || hasTimeInfo).toBeTruthy(); }); }); test.describe('Chat History', () => { test('should maintain chat history during session', async ({ page }) => { const messages = ['First message', 'Second message', 'Third message']; // Send multiple messages for (const message of messages) { await page.fill('[data-testid="message-input"]', message); await page.click('[data-testid="send-button"]'); await page.waitForTimeout(500); // Small delay between messages } // Verify all messages are in history for (const message of messages) { await expect(page.locator('[data-testid="chat-history"]')).toContainText(message); } }); test('should scroll to bottom when new message is sent', async ({ page }) => { // Send multiple messages to create scrollable content for (let i = 0; i < 10; i++) { await page.fill('[data-testid="message-input"]', `Message ${i + 1}`); await page.click('[data-testid="send-button"]'); await page.waitForTimeout(200); } // Check if chat is scrolled to bottom const chatHistory = page.locator('[data-testid="chat-history"]'); const isScrolledToBottom = await chatHistory.evaluate(element => { return element.scrollTop + element.clientHeight >= element.scrollHeight - 10; }); expect(isScrolledToBottom).toBeTruthy(); }); test('should load previous chat history on page load', async ({ page }) => { // Send a message const testMessage = 'Message to be remembered'; await page.fill('[data-testid="message-input"]', testMessage); await page.click('[data-testid="send-button"]'); // Reload page await page.reload(); await page.waitForLoadState('networkidle'); // Check if message history is restored const hasHistory = await helpers.elementExists('[data-testid="chat-history"]'); if (hasHistory) { // If chat history exists, it might contain previous messages const historyContent = await page.locator('[data-testid="chat-history"]').textContent(); // Just verify that history area exists and is functional expect(historyContent).toBeDefined(); } }); }); test.describe('Chat Features', () => { test('should show typing indicator when bot is responding', async ({ page }) => { const testMessage = 'Hello bot'; await page.fill('[data-testid="message-input"]', testMessage); await page.click('[data-testid="send-button"]'); // Look for typing indicator (might appear briefly) try { await page.waitForSelector('[data-testid="typing-indicator"]', { timeout: 3000 }); await expect(page.locator('[data-testid="typing-indicator"]')).toBeVisible(); } catch (error) { // Typing indicator might be too fast to catch or not implemented console.log('Typing indicator not found or too fast'); } }); test('should handle emoji input', async ({ page }) => { const emojiMessage = 'Hello! '; await page.fill('[data-testid="message-input"]', emojiMessage); await page.click('[data-testid="send-button"]'); // Verify emoji message is displayed correctly await expect(page.locator('[data-testid="chat-history"]')).toContainText(emojiMessage); }); test('should support message actions (if available)', async ({ page }) => { const testMessage = 'Message with actions'; await page.fill('[data-testid="message-input"]', testMessage); await page.click('[data-testid="send-button"]'); // Check for message actions (copy, delete, etc.) const hasMessageActions = await helpers.elementExists('[data-testid="message-actions"]'); const hasMessageMenu = await helpers.elementExists('[data-testid="message-menu"]'); // Message actions are optional features if (hasMessageActions || hasMessageMenu) { console.log('Message actions available'); } }); }); test.describe('Error Handling', () => { test('should handle network errors gracefully', async ({ page }) => { // Simulate network failure await page.route('**/api/chat/**', route => { route.abort('failed'); }); const testMessage = 'Message during network failure'; await page.fill('[data-testid="message-input"]', testMessage); await page.click('[data-testid="send-button"]'); // Check for error handling const hasErrorMessage = await helpers.elementExists('[data-testid="chat-error"]'); const hasRetryButton = await helpers.elementExists('[data-testid="retry-button"]'); const hasOfflineIndicator = await helpers.elementExists('[data-testid="offline-indicator"]'); expect(hasErrorMessage || hasRetryButton || hasOfflineIndicator).toBeTruthy(); }); test('should show connection status', async ({ page }) => { // Check for connection status indicator const hasConnectionStatus = await helpers.elementExists('[data-testid="connection-status"]'); const hasOnlineIndicator = await helpers.elementExists('[data-testid="online-indicator"]'); // Connection status is optional but good to have if (hasConnectionStatus || hasOnlineIndicator) { console.log('Connection status indicator available'); } }); }); test.describe('Mobile Chat Interface', () => { test('should work correctly on mobile devices', async ({ page }) => { // Set mobile viewport await page.setViewportSize({ width: 375, height: 667 }); // Verify mobile layout await expect(page.locator('[data-testid="chat-interface"]')).toBeVisible(); await expect(page.locator('[data-testid="message-input"]')).toBeVisible(); // Test sending message on mobile const mobileMessage = 'Mobile test message'; await page.fill('[data-testid="message-input"]', mobileMessage); await page.click('[data-testid="send-button"]'); await expect(page.locator('[data-testid="chat-history"]')).toContainText(mobileMessage); }); test('should handle virtual keyboard on mobile', async ({ page }) => { await page.setViewportSize({ width: 375, height: 667 }); // Focus on input (simulates virtual keyboard) await page.focus('[data-testid="message-input"]'); // Verify interface still usable await expect(page.locator('[data-testid="message-input"]')).toBeFocused(); await expect(page.locator('[data-testid="send-button"]')).toBeVisible(); }); }); });