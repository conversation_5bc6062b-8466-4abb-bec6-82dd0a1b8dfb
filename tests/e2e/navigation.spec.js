const { test, expect } = require('@playwright/test'); const TestHelpers = require('../utils/test-helpers'); test.describe('Navigation and Page Loading Tests', () => { let helpers; let testUsers; test.beforeEach(async ({ page }) => { helpers = new TestHelpers(page); // Load test users const fs = require('fs'); const testData = JSON.parse(fs.readFileSync('./tests/fixtures/test-data.json', 'utf8')); testUsers = testData.users; }); test.describe('Public Pages', () => { test('should load login page correctly', async ({ page }) => { await page.goto('/login'); // Verify page loads without errors await expect(page.locator('[data-testid="login-form"]')).toBeVisible(); await expect(page.locator('[data-testid="email-input"]')).toBeVisible(); await expect(page.locator('[data-testid="password-input"]')).toBeVisible(); await expect(page.locator('[data-testid="login-button"]')).toBeVisible(); // Verify page title await expect(page).toHaveTitle(/login/i); }); test('should load registration page correctly', async ({ page }) => { await page.goto('/register'); // Check if registration page exists or redirects appropriately const hasRegisterForm = await helpers.elementExists('[data-testid="register-form"]'); const isRedirectedToLogin = page.url().includes('/login'); // Either should have registration form or redirect to login expect(hasRegisterForm || isRedirectedToLogin).toBeTruthy(); }); test('should handle 404 pages gracefully', async ({ page }) => { await page.goto('/non-existent-page'); // Should show 404 page or redirect const has404 = await helpers.elementExists('[data-testid="not-found"]'); const isRedirected = !page.url().includes('/non-existent-page'); expect(has404 || isRedirected).toBeTruthy(); }); }); test.describe('Protected Pages - User Role', () => { test.beforeEach(async ({ page }) => { await helpers.login(testUsers.user); }); test('should load dashboard correctly', async ({ page }) => { await page.goto('/dashboard'); // Verify dashboard elements await expect(page.locator('[data-testid="dashboard"]')).toBeVisible(); await expect(page.locator('[data-testid="user-menu"]')).toBeVisible(); // Check for main dashboard components const hasWelcomeMessage = await helpers.elementExists('[data-testid="welcome-message"]'); const hasStatsCards = await helpers.elementExists('[data-testid="stats-cards"]'); const hasRecentActivity = await helpers.elementExists('[data-testid="recent-activity"]'); expect(hasWelcomeMessage || hasStatsCards || hasRecentActivity).toBeTruthy(); }); test('should load chat interface correctly', async ({ page }) => { await page.goto('/chat'); // Verify chat interface elements await expect(page.locator('[data-testid="chat-interface"]')).toBeVisible(); // Check for chat components const hasChatHistory = await helpers.elementExists('[data-testid="chat-history"]'); const hasMessageInput = await helpers.elementExists('[data-testid="message-input"]'); const hasSendButton = await helpers.elementExists('[data-testid="send-button"]'); expect(hasChatHistory && hasMessageInput && hasSendButton).toBeTruthy(); }); test('should load profile/settings page correctly', async ({ page }) => { await page.goto('/profile'); // Verify profile page elements const hasProfileForm = await helpers.elementExists('[data-testid="profile-form"]'); const hasSettingsPanel = await helpers.elementExists('[data-testid="settings-panel"]'); expect(hasProfileForm || hasSettingsPanel).toBeTruthy(); }); }); test.describe('Protected Pages - Agent Role', () => { test.beforeEach(async ({ page }) => { await helpers.login(testUsers.agent); }); test('should load agent dashboard correctly', async ({ page }) => { await page.goto('/agent'); // Verify agent dashboard elements const hasAgentDashboard = await helpers.elementExists('[data-testid="agent-dashboard"]'); const hasTicketQueue = await helpers.elementExists('[data-testid="ticket-queue"]'); const hasAgentStats = await helpers.elementExists('[data-testid="agent-stats"]'); expect(hasAgentDashboard || hasTicketQueue || hasAgentStats).toBeTruthy(); }); test('should access ticket management', async ({ page }) => { await page.goto('/tickets'); // Verify ticket management interface const hasTicketList = await helpers.elementExists('[data-testid="ticket-list"]'); const hasTicketFilters = await helpers.elementExists('[data-testid="ticket-filters"]'); expect(hasTicketList || hasTicketFilters).toBeTruthy(); }); }); test.describe('Protected Pages - Admin Role', () => { test.beforeEach(async ({ page }) => { await helpers.login(testUsers.admin); }); test('should load admin panel correctly', async ({ page }) => { await page.goto('/admin'); // Verify admin panel elements await expect(page.locator('[data-testid="admin-panel"]')).toBeVisible(); // Check for admin components const hasUserManagement = await helpers.elementExists('[data-testid="user-management"]'); const hasSystemSettings = await helpers.elementExists('[data-testid="system-settings"]'); const hasAnalytics = await helpers.elementExists('[data-testid="analytics-panel"]'); expect(hasUserManagement || hasSystemSettings || hasAnalytics).toBeTruthy(); }); test('should access user management', async ({ page }) => { await page.goto('/admin/users'); // Verify user management interface const hasUserList = await helpers.elementExists('[data-testid="user-list"]'); const hasUserActions = await helpers.elementExists('[data-testid="user-actions"]'); expect(hasUserList || hasUserActions).toBeTruthy(); }); test('should access system analytics', async ({ page }) => { await page.goto('/admin/analytics'); // Verify analytics interface const hasAnalyticsCharts = await helpers.elementExists('[data-testid="analytics-charts"]'); const hasMetricsDashboard = await helpers.elementExists('[data-testid="metrics-dashboard"]'); expect(hasAnalyticsCharts || hasMetricsDashboard).toBeTruthy(); }); }); test.describe('Navigation Menu', () => { test.beforeEach(async ({ page }) => { await helpers.login(testUsers.user); }); test('should navigate between pages using menu', async ({ page }) => { await page.goto('/dashboard'); // Test navigation to different pages const navigationTests = [ { selector: '[data-testid="nav-chat"]', expectedUrl: '/chat' }, { selector: '[data-testid="nav-profile"]', expectedUrl: '/profile' }, { selector: '[data-testid="nav-dashboard"]', expectedUrl: '/dashboard' } ]; for (const navTest of navigationTests) { const navElement = await helpers.elementExists(navTest.selector); if (navElement) { await page.click(navTest.selector); await page.waitForURL(`**${navTest.expectedUrl}`, { timeout: 5000 }); expect(page.url()).toContain(navTest.expectedUrl); } } }); test('should highlight active navigation item', async ({ page }) => { await page.goto('/dashboard'); // Check if active nav item is highlighted const activeNavItem = page.locator('[data-testid="nav-dashboard"].active, [data-testid="nav-dashboard"][aria-current="page"]'); const hasActiveClass = await activeNavItem.count() > 0; if (hasActiveClass) { await expect(activeNavItem).toBeVisible(); } }); }); test.describe('Responsive Navigation', () => { test('should show mobile menu on small screens', async ({ page }) => { // Set mobile viewport await page.setViewportSize({ width: 375, height: 667 }); await helpers.login(testUsers.user); await page.goto('/dashboard'); // Check for mobile menu toggle const hasMobileMenuToggle = await helpers.elementExists('[data-testid="mobile-menu-toggle"]'); const hasMobileMenu = await helpers.elementExists('[data-testid="mobile-menu"]'); expect(hasMobileMenuToggle || hasMobileMenu).toBeTruthy(); }); test('should toggle mobile menu correctly', async ({ page }) => { await page.setViewportSize({ width: 375, height: 667 }); await helpers.login(testUsers.user); await page.goto('/dashboard'); const mobileMenuToggle = '[data-testid="mobile-menu-toggle"]'; const mobileMenu = '[data-testid="mobile-menu"]'; if (await helpers.elementExists(mobileMenuToggle)) { // Open mobile menu await page.click(mobileMenuToggle); // Verify menu is visible await expect(page.locator(mobileMenu)).toBeVisible(); // Close mobile menu await page.click(mobileMenuToggle); // Verify menu is hidden await expect(page.locator(mobileMenu)).toBeHidden(); } }); }); test.describe('Page Performance', () => { test('should load pages within acceptable time', async ({ page }) => { await helpers.login(testUsers.user); const pages = ['/dashboard', '/chat', '/profile']; for (const pagePath of pages) { const startTime = Date.now(); await page.goto(pagePath); await page.waitForLoadState('networkidle'); const loadTime = Date.now() - startTime; // Page should load within 5 seconds expect(loadTime).toBeLessThan(5000); } }); test('should not have console errors on page load', async ({ page }) => { const consoleErrors = []; page.on('console', msg => { if (msg.type() === 'error') { consoleErrors.push(msg.text()); } }); await helpers.login(testUsers.user); await page.goto('/dashboard'); await page.waitForLoadState('networkidle'); // Filter out known acceptable errors (if any) const criticalErrors = consoleErrors.filter(error => !error.includes('favicon') && !error.includes('analytics') && !error.includes('third-party') ); expect(criticalErrors).toHaveLength(0); }); }); });