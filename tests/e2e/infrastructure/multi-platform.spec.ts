/** * ============================================= * [MOBILE] MULTI-PLATFORM INTEGRATION TESTS * Comprehensive testing of WhatsApp, Facebook, Instagram, Twitter * ============================================= */ import { test, expect, TestUtils } from '../../fixtures/test-fixtures'; import { testConfig } from '../../playwright.config'; test.describe('Multi-Platform Integration', () => { test.beforeEach(async ({ adminPage }) => { // Navigate to platform management dashboard await adminPage.goto('/dashboard/admin/platforms'); await TestUtils.waitForStableNetwork(adminPage); }); test.describe('@infrastructure @smoke Platform Status and Configuration', () => { test('should display status for all supported platforms', async ({ adminPage, socialMediaContext }) => { // Verify platform dashboard is loaded await TestUtils.waitForElement(adminPage, '[data-testid="platforms-dashboard"]'); const platformsDashboard = adminPage.locator('[data-testid="platforms-dashboard"]'); await expect(platformsDashboard).toBeVisible(); // Check that all platforms are displayed const supportedPlatforms = ['whatsapp', 'facebook', 'instagram', 'twitter']; for (const platform of supportedPlatforms) { const platformCard = adminPage.locator(`[data-testid="platform-${platform}"]`); await expect(platformCard).toBeVisible(); // Verify platform status indicator const statusIndicator = platformCard.locator('[data-testid="connection-status"]'); await expect(statusIndicator).toBeVisible(); // Verify configuration status const configStatus = platformCard.locator('[data-testid="config-status"]'); await expect(configStatus).toBeVisible(); // Verify message count const messageCount = platformCard.locator('[data-testid="message-count"]'); await expect(messageCount).toBeVisible(); } }); test('should validate platform API connections', async ({ socialMediaContext }) => { const platforms = ['whatsapp', 'facebook', 'instagram', 'twitter']; for (const platform of platforms) { const response = await socialMediaContext.get(`/api/platforms/${platform}/status`); expect(response.ok()).toBeTruthy(); const status = await response.json(); expect(status).toHaveProperty('platform', platform); expect(status).toHaveProperty('connected'); expect(status).toHaveProperty('last_activity'); expect(status).toHaveProperty('webhook_status'); if (status.connected) { expect(status.webhook_status).toBe('active'); } } }); test('should show platform-specific metrics', async ({ adminPage }) => { // Click on WhatsApp platform for detailed view await adminPage.click('[data-testid="platform-whatsapp"]'); await TestUtils.waitForElement(adminPage, '[data-testid="platform-details-modal"]'); const detailsModal = adminPage.locator('[data-testid="platform-details-modal"]'); await expect(detailsModal).toBeVisible(); // Verify WhatsApp-specific metrics await expect(detailsModal.locator('[data-testid="messages-sent"]')).toBeVisible(); await expect(detailsModal.locator('[data-testid="messages-received"]')).toBeVisible(); await expect(detailsModal.locator('[data-testid="delivery-rate"]')).toBeVisible(); await expect(detailsModal.locator('[data-testid="response-time"]')).toBeVisible(); await expect(detailsModal.locator('[data-testid="active-conversations"]')).toBeVisible(); // WhatsApp specific metrics await expect(detailsModal.locator('[data-testid="template-usage"]')).toBeVisible(); await expect(detailsModal.locator('[data-testid="media-messages"]')).toBeVisible(); }); }); test.describe('@infrastructure @whatsapp WhatsApp Integration', () => { test('should handle WhatsApp webhook messages', async ({ socialMediaContext }) => { // Simulate WhatsApp webhook message const webhookPayload = { object: 'whatsapp_business_account', entry: [{ id: 'test-entry-id', changes: [{ value: { messaging_product: 'whatsapp', metadata: { display_phone_number: '+***********', phone_number_id: 'test-phone-id' }, messages: [{ from: '+***********', id: 'test-message-id', timestamp: Math.floor(Date.now() / 1000).toString(), text: { body: 'Bonjour, j\'ai besoin d\'aide' }, type: 'text' }] }, field: 'messages' }] }] }; const response = await socialMediaContext.post('/api/webhooks/whatsapp', { data: webhookPayload }); expect(response.ok()).toBeTruthy(); // Verify message was processed const result = await response.json(); expect(result).toHaveProperty('status', 'processed'); expect(result).toHaveProperty('conversation_id'); expect(result).toHaveProperty('message_id'); }); test('should send WhatsApp messages', async ({ socialMediaContext }) => { const messagePayload = { to: '+***********', type: 'text', text: { body: 'Bonjour ! Comment puis-je vous aider aujourd\'hui ?' } }; const response = await socialMediaContext.post('/api/platforms/whatsapp/send', { data: messagePayload }); expect(response.ok()).toBeTruthy(); const result = await response.json(); expect(result).toHaveProperty('message_id'); expect(result).toHaveProperty('status', 'sent'); }); test('should handle WhatsApp media messages', async ({ socialMediaContext }) => { // Test image message const imagePayload = { to: '+***********', type: 'image', image: { link: 'https://example.com/test-image.jpg', caption: 'Voici votre facture' } }; const imageResponse = await socialMediaContext.post('/api/platforms/whatsapp/send', { data: imagePayload }); expect(imageResponse.ok()).toBeTruthy(); // Test document message const documentPayload = { to: '+***********', type: 'document', document: { link: 'https://example.com/test-document.pdf', filename: 'facture.pdf', caption: 'Votre facture du mois' } }; const documentResponse = await socialMediaContext.post('/api/platforms/whatsapp/send', { data: documentPayload }); expect(documentResponse.ok()).toBeTruthy(); }); test('should handle WhatsApp template messages', async ({ socialMediaContext }) => { const templatePayload = { to: '+***********', type: 'template', template: { name: 'welcome_message', language: { code: 'fr' }, components: [{ type: 'body', parameters: [{ type: 'text', text: 'Jean Dupont' }] }] } }; const response = await socialMediaContext.post('/api/platforms/whatsapp/send', { data: templatePayload }); expect(response.ok()).toBeTruthy(); const result = await response.json(); expect(result).toHaveProperty('message_id'); expect(result).toHaveProperty('status', 'sent'); }); }); test.describe('@infrastructure @facebook Facebook Integration', () => { test('should handle Facebook Messenger webhooks', async ({ socialMediaContext }) => { const webhookPayload = { object: 'page', entry: [{ id: 'test-page-id', time: Date.now(), messaging: [{ sender: { id: 'test-user-id' }, recipient: { id: 'test-page-id' }, timestamp: Date.now(), message: { mid: 'test-message-id', text: 'Bonjour, j\'ai une question' } }] }] }; const response = await socialMediaContext.post('/api/webhooks/facebook', { data: webhookPayload }); expect(response.ok()).toBeTruthy(); const result = await response.json(); expect(result).toHaveProperty('status', 'processed'); }); test('should send Facebook Messenger messages', async ({ socialMediaContext }) => { const messagePayload = { recipient: { id: 'test-user-id' }, message: { text: 'Bonjour ! Comment puis-je vous aider ?' } }; const response = await socialMediaContext.post('/api/platforms/facebook/send', { data: messagePayload }); expect(response.ok()).toBeTruthy(); const result = await response.json(); expect(result).toHaveProperty('message_id'); }); test('should handle Facebook quick replies', async ({ socialMediaContext }) => { const quickReplyPayload = { recipient: { id: 'test-user-id' }, message: { text: 'Comment puis-je vous aider ?', quick_replies: [ { content_type: 'text', title: 'Problème technique', payload: 'TECHNICAL_ISSUE' }, { content_type: 'text', title: 'Question facturation', payload: 'BILLING_QUESTION' }, { content_type: 'text', title: 'Autre', payload: 'OTHER' } ] } }; const response = await socialMediaContext.post('/api/platforms/facebook/send', { data: quickReplyPayload }); expect(response.ok()).toBeTruthy(); }); test('should handle Facebook postback events', async ({ socialMediaContext }) => { const postbackPayload = { object: 'page', entry: [{ id: 'test-page-id', time: Date.now(), messaging: [{ sender: { id: 'test-user-id' }, recipient: { id: 'test-page-id' }, timestamp: Date.now(), postback: { payload: 'TECHNICAL_ISSUE', title: 'Problème technique' } }] }] }; const response = await socialMediaContext.post('/api/webhooks/facebook', { data: postbackPayload }); expect(response.ok()).toBeTruthy(); const result = await response.json(); expect(result).toHaveProperty('status', 'processed'); expect(result).toHaveProperty('intent', 'technical_issue'); }); }); test.describe('@infrastructure @instagram Instagram Integration', () => { test('should handle Instagram Direct Message webhooks', async ({ socialMediaContext }) => { const webhookPayload = { object: 'instagram', entry: [{ id: 'test-instagram-id', time: Date.now(), messaging: [{ sender: { id: 'test-user-id' }, recipient: { id: 'test-business-id' }, timestamp: Date.now(), message: { mid: 'test-message-id', text: 'Salut ! Question sur vos forfaits' } }] }] }; const response = await socialMediaContext.post('/api/webhooks/instagram', { data: webhookPayload }); expect(response.ok()).toBeTruthy(); const result = await response.json(); expect(result).toHaveProperty('status', 'processed'); }); test('should send Instagram Direct Messages', async ({ socialMediaContext }) => { const messagePayload = { recipient: { id: 'test-user-id' }, message: { text: 'Salut ! Comment puis-je t\'aider ? ' } }; const response = await socialMediaContext.post('/api/platforms/instagram/send', { data: messagePayload }); expect(response.ok()).toBeTruthy(); const result = await response.json(); expect(result).toHaveProperty('message_id'); }); test('should handle Instagram story replies', async ({ socialMediaContext }) => { const storyReplyPayload = { object: 'instagram', entry: [{ id: 'test-instagram-id', time: Date.now(), messaging: [{ sender: { id: 'test-user-id' }, recipient: { id: 'test-business-id' }, timestamp: Date.now(), message: { mid: 'test-message-id', text: 'Réponse à votre story', reply_to: { story: { url: 'https://instagram.com/story/123', id: 'story-123' } } } }] }] }; const response = await socialMediaContext.post('/api/webhooks/instagram', { data: storyReplyPayload }); expect(response.ok()).toBeTruthy(); const result = await response.json(); expect(result).toHaveProperty('status', 'processed'); expect(result).toHaveProperty('story_reply', true); }); }); test.describe('@infrastructure @twitter Twitter Integration', () => { test('should handle Twitter mention webhooks', async ({ socialMediaContext }) => { const webhookPayload = { tweet_create_events: [{ id_str: 'test-tweet-id', text: '@FreeMobile_Fr Bonjour, j\'ai un problème avec mon forfait', user: { id_str: 'test-user-id', screen_name: 'test_user', name: 'Test User' }, created_at: new Date().toISOString(), in_reply_to_status_id_str: null, entities: { user_mentions: [{ screen_name: 'FreeMobile_Fr', id_str: 'freemobile-id' }] } }] }; const response = await socialMediaContext.post('/api/webhooks/twitter', { data: webhookPayload }); expect(response.ok()).toBeTruthy(); const result = await response.json(); expect(result).toHaveProperty('status', 'processed'); }); test('should send Twitter replies', async ({ socialMediaContext }) => { const replyPayload = { status: '@test_user Bonjour ! Nous allons vous aider avec votre forfait. Pouvez-vous nous envoyer un DM ?', in_reply_to_status_id: 'test-tweet-id', auto_populate_reply_metadata: true }; const response = await socialMediaContext.post('/api/platforms/twitter/reply', { data: replyPayload }); expect(response.ok()).toBeTruthy(); const result = await response.json(); expect(result).toHaveProperty('tweet_id'); }); test('should handle Twitter Direct Messages', async ({ socialMediaContext }) => { // Receive DM webhook const dmWebhookPayload = { direct_message_events: [{ id: 'test-dm-id', created_timestamp: Date.now().toString(), message_create: { target: { recipient_id: 'freemobile-id' }, sender_id: 'test-user-id', message_data: { text: 'Bonjour, j\'ai besoin d\'aide en privé' } } }], users: { 'test-user-id': { id: 'test-user-id', name: 'Test User', screen_name: 'test_user' } } }; const webhookResponse = await socialMediaContext.post('/api/webhooks/twitter', { data: dmWebhookPayload }); expect(webhookResponse.ok()).toBeTruthy(); // Send DM reply const dmReplyPayload = { event: { type: 'message_create', message_create: { target: { recipient_id: 'test-user-id' }, message_data: { text: 'Bonjour ! Comment puis-je vous aider ?' } } } }; const replyResponse = await socialMediaContext.post('/api/platforms/twitter/dm', { data: dmReplyPayload }); expect(replyResponse.ok()).toBeTruthy(); }); test('should monitor hashtag mentions', async ({ socialMediaContext }) => { const hashtagResponse = await socialMediaContext.get('/api/platforms/twitter/hashtag-mentions?hashtag=FreeMobile&count=10'); expect(hashtagResponse.ok()).toBeTruthy(); const mentions = await hashtagResponse.json(); expect(mentions).toHaveProperty('tweets'); expect(mentions.tweets).toBeInstanceOf(Array); for (const tweet of mentions.tweets) { expect(tweet).toHaveProperty('id'); expect(tweet).toHaveProperty('text'); expect(tweet).toHaveProperty('user'); expect(tweet).toHaveProperty('created_at'); expect(tweet.text.toLowerCase()).toContain('#freemobile'); } }); }); test.describe('@infrastructure @cross-platform Cross-Platform Features', () => { test('should maintain conversation context across platforms', async ({ socialMediaContext }) => { const customerId = 'test-customer-cross-platform'; // Start conversation on WhatsApp await socialMediaContext.post('/api/webhooks/whatsapp', { data: { object: 'whatsapp_business_account', entry: [{ changes: [{ value: { messages: [{ from: '+***********', id: 'whatsapp-msg-1', text: { body: 'Bonjour, j\'ai un problème' }, type: 'text' }] } }] }] } }); // Continue conversation on Facebook (same customer) await socialMediaContext.post('/api/webhooks/facebook', { data: { object: 'page', entry: [{ messaging: [{ sender: { id: customerId }, message: { text: 'Je continue notre conversation ici' } }] }] } }); // Verify conversation context is maintained const contextResponse = await socialMediaContext.get(`/api/conversations/customer/${customerId}/context`); expect(contextResponse.ok()).toBeTruthy(); const context = await contextResponse.json(); expect(context).toHaveProperty('platforms'); expect(context.platforms).toContain('whatsapp'); expect(context.platforms).toContain('facebook'); expect(context).toHaveProperty('unified_conversation_id'); }); test('should route messages to appropriate agents based on platform expertise', async ({ socialMediaContext, mlContext }) => { const platforms = ['whatsapp', 'facebook', 'instagram', 'twitter']; for (const platform of platforms) { const routingResponse = await mlContext.post('/api/ml/routing/route-conversation', { data: { conversation_id: `test-conv-${platform}`, customer_message: 'J\'ai besoin d\'aide', platform: platform, customer_id: 'test-customer-1', urgency: 'medium' } }); expect(routingResponse.ok()).toBeTruthy(); const routing = await routingResponse.json(); // Verify agent has platform expertise const agent = routing.recommended_agent; expect(agent.metadata.platformSupport[platform]).toBeTruthy(); expect(agent.platform_expertise).toBeGreaterThan(0.5); } }); test('should adapt AI responses for each platform', async ({ mlContext }) => { const message = 'Bonjour, comment allez-vous ?'; const platforms = ['whatsapp', 'facebook', 'instagram', 'twitter']; for (const platform of platforms) { const suggestionResponse = await mlContext.post('/api/ml/suggestions/generate', { data: { conversation_id: 'test-conv-adaptation', customer_message: message, platform: platform, agent_id: 'test-agent-1' } }); expect(suggestionResponse.ok()).toBeTruthy(); const suggestions = await suggestionResponse.json(); expect(suggestions.suggestions.length).toBeGreaterThan(0); for (const suggestion of suggestions.suggestions) { expect(suggestion.platform).toBe(platform); expect(suggestion.platform_specific).toBeTruthy(); // Platform-specific validations if (platform === 'twitter') { expect(suggestion.text.length).toBeLessThanOrEqual(280); } else if (platform === 'instagram') { expect(suggestion.text).toMatch(/[[AI]]/); // Should include emojis } } } }); }); test.describe('@infrastructure @performance Platform Performance', () => { test('should handle high message volume across platforms', async ({ socialMediaContext }) => { const platforms = ['whatsapp', 'facebook', 'instagram', 'twitter']; const messagesPerPlatform = 10; const promises = []; for (const platform of platforms) { for (let i = 0; i < messagesPerPlatform; i++) { const promise = socialMediaContext.post(`/api/webhooks/${platform}`, { data: generateMockWebhookPayload(platform, i) }); promises.push(promise); } } const responses = await Promise.all(promises); // All messages should be processed successfully for (const response of responses) { expect(response.ok()).toBeTruthy(); } }); test('should maintain response times under load', async ({ socialMediaContext }) => { const startTime = Date.now(); const response = await socialMediaContext.post('/api/webhooks/whatsapp', { data: generateMockWebhookPayload('whatsapp', 1) }); const endTime = Date.now(); const responseTime = endTime - startTime; expect(response.ok()).toBeTruthy(); expect(responseTime).toBeLessThan(2000); // Should process within 2 seconds }); test('should track platform-specific metrics', async ({ socialMediaContext }) => { const metricsResponse = await socialMediaContext.get('/api/platforms/metrics'); expect(metricsResponse.ok()).toBeTruthy(); const metrics = await metricsResponse.json(); expect(metrics).toHaveProperty('platforms'); const platforms = ['whatsapp', 'facebook', 'instagram', 'twitter']; for (const platform of platforms) { expect(metrics.platforms).toHaveProperty(platform); const platformMetrics = metrics.platforms[platform]; expect(platformMetrics).toHaveProperty('messages_received'); expect(platformMetrics).toHaveProperty('messages_sent'); expect(platformMetrics).toHaveProperty('response_time'); expect(platformMetrics).toHaveProperty('error_rate'); expect(platformMetrics).toHaveProperty('active_conversations'); } }); }); }); // Helper function to generate mock webhook payloads function generateMockWebhookPayload(platform: string, index: number): any { const baseTimestamp = Date.now(); switch (platform) { case 'whatsapp': return { object: 'whatsapp_business_account', entry: [{ changes: [{ value: { messages: [{ from: `+**********${index}`, id: `whatsapp-msg-${index}`, text: { body: `Test message ${index}` }, type: 'text' }] } }] }] }; case 'facebook': return { object: 'page', entry: [{ messaging: [{ sender: { id: `facebook-user-${index}` }, message: { mid: `facebook-msg-${index}`, text: `Test message ${index}` }, timestamp: baseTimestamp }] }] }; case 'instagram': return { object: 'instagram', entry: [{ messaging: [{ sender: { id: `instagram-user-${index}` }, message: { mid: `instagram-msg-${index}`, text: `Test message ${index}` }, timestamp: baseTimestamp }] }] }; case 'twitter': return { tweet_create_events: [{ id_str: `twitter-tweet-${index}`, text: `@FreeMobile_Fr Test message ${index}`, user: { id_str: `twitter-user-${index}`, screen_name: `test_user_${index}` }, created_at: new Date(baseTimestamp).toISOString() }] }; default: return {}; } }