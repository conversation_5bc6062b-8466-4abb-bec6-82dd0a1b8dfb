/** * ============================================= * HEALTH MONITORING SYSTEM TESTS * Comprehensive testing of production infrastructure monitoring * ============================================= */ import { test, expect, TestUtils } from '../../fixtures/test-fixtures'; import { testConfig } from '../../playwright.config'; test.describe('Health Monitoring System', () => { test.beforeEach(async ({ adminPage }) => { // Navigate to system health dashboard await adminPage.goto('/dashboard/admin/system-health'); await TestUtils.waitForStableNetwork(adminPage); }); test.describe('@infrastructure @smoke Service Health Checks', () => { test('should display health status for all core services', async ({ adminPage, apiContext }) => { // Verify health dashboard is loaded await TestUtils.waitForElement(adminPage, '[data-testid="health-dashboard"]'); const healthDashboard = adminPage.locator('[data-testid="health-dashboard"]'); await expect(healthDashboard).toBeVisible(); // Check that all core services are displayed const coreServices = [ 'mongodb', 'redis', 'timescaledb', 'backend', 'ml-service', 'multimodal-service', 'call-system', 'social-media-service', 'frontend', 'nginx' ]; for (const service of coreServices) { const serviceCard = adminPage.locator(`[data-testid="service-${service}"]`); await expect(serviceCard).toBeVisible(); // Verify service status indicator const statusIndicator = serviceCard.locator('[data-testid="status-indicator"]'); await expect(statusIndicator).toBeVisible(); // Verify last check timestamp const lastCheck = serviceCard.locator('[data-testid="last-check"]'); await expect(lastCheck).toBeVisible(); } }); test('should show real-time service status updates', async ({ adminPage }) => { // Wait for initial load await TestUtils.waitForElement(adminPage, '[data-testid="health-dashboard"]'); // Get initial status const backendService = adminPage.locator('[data-testid="service-backend"]'); const initialStatus = await backendService.locator('[data-testid="status-text"]').textContent(); // Wait for auto-refresh (should happen every 30 seconds) await adminPage.waitForTimeout(35000); // Verify status was updated (timestamp should change) const lastCheckElement = backendService.locator('[data-testid="last-check"]'); const lastCheckTime = await lastCheckElement.textContent(); // Should show recent timestamp expect(lastCheckTime).toMatch(/\d{2}:\d{2}:\d{2}/); }); test('should provide detailed health information for each service', async ({ adminPage }) => { // Click on a service for detailed view await adminPage.click('[data-testid="service-backend"]'); await TestUtils.waitForElement(adminPage, '[data-testid="service-details-modal"]'); const detailsModal = adminPage.locator('[data-testid="service-details-modal"]'); await expect(detailsModal).toBeVisible(); // Verify detailed information is displayed await expect(detailsModal.locator('[data-testid="service-name"]')).toBeVisible(); await expect(detailsModal.locator('[data-testid="service-status"]')).toBeVisible(); await expect(detailsModal.locator('[data-testid="service-uptime"]')).toBeVisible(); await expect(detailsModal.locator('[data-testid="service-response-time"]')).toBeVisible(); await expect(detailsModal.locator('[data-testid="service-error-rate"]')).toBeVisible(); await expect(detailsModal.locator('[data-testid="service-last-restart"]')).toBeVisible(); // Should have action buttons await expect(detailsModal.locator('[data-testid="restart-service"]')).toBeVisible(); await expect(detailsModal.locator('[data-testid="view-logs"]')).toBeVisible(); }); test('should validate health check endpoints', async ({ apiContext }) => { const healthEndpoints = [ '/health', '/api/health/database', '/api/health/cache', '/api/health/ml-service', '/api/health/multimodal-service', '/api/health/social-media-service' ]; for (const endpoint of healthEndpoints) { const response = await apiContext.get(endpoint); expect(response.ok()).toBeTruthy(); const healthData = await response.json(); expect(healthData).toHaveProperty('status'); expect(healthData).toHaveProperty('timestamp'); expect(['healthy', 'degraded', 'unhealthy']).toContain(healthData.status); } }); }); test.describe('@infrastructure @regression System Resource Monitoring', () => { test('should display system resource metrics', async ({ adminPage }) => { // Navigate to system resources section await adminPage.click('[data-testid="system-resources-tab"]'); await TestUtils.waitForElement(adminPage, '[data-testid="resource-metrics"]'); const resourceMetrics = adminPage.locator('[data-testid="resource-metrics"]'); await expect(resourceMetrics).toBeVisible(); // Verify CPU metrics const cpuMetric = resourceMetrics.locator('[data-testid="cpu-usage"]'); await expect(cpuMetric).toBeVisible(); const cpuValue = await cpuMetric.locator('[data-testid="metric-value"]').textContent(); expect(cpuValue).toMatch(/\d+(\.\d+)?%/); // Verify Memory metrics const memoryMetric = resourceMetrics.locator('[data-testid="memory-usage"]'); await expect(memoryMetric).toBeVisible(); const memoryValue = await memoryMetric.locator('[data-testid="metric-value"]').textContent(); expect(memoryValue).toMatch(/\d+(\.\d+)?%/); // Verify Disk metrics const diskMetric = resourceMetrics.locator('[data-testid="disk-usage"]'); await expect(diskMetric).toBeVisible(); const diskValue = await diskMetric.locator('[data-testid="metric-value"]').textContent(); expect(diskValue).toMatch(/\d+(\.\d+)?%/); }); test('should show resource usage trends', async ({ adminPage }) => { await adminPage.click('[data-testid="system-resources-tab"]'); await TestUtils.waitForElement(adminPage, '[data-testid="resource-charts"]'); // Verify charts are displayed const cpuChart = adminPage.locator('[data-testid="cpu-chart"]'); await expect(cpuChart).toBeVisible(); const memoryChart = adminPage.locator('[data-testid="memory-chart"]'); await expect(memoryChart).toBeVisible(); const diskChart = adminPage.locator('[data-testid="disk-chart"]'); await expect(diskChart).toBeVisible(); // Verify chart data is loaded await expect(cpuChart.locator('canvas')).toBeVisible(); await expect(memoryChart.locator('canvas')).toBeVisible(); await expect(diskChart.locator('canvas')).toBeVisible(); }); test('should alert on high resource usage', async ({ adminPage, apiContext }) => { // Simulate high CPU usage alert const alertResponse = await apiContext.post('/api/test/simulate-alert', { data: { type: 'high_cpu', value: 85, threshold: 80 } }); expect(alertResponse.ok()).toBeTruthy(); // Check that alert appears in dashboard await adminPage.reload(); await TestUtils.waitForElement(adminPage, '[data-testid="system-alerts"]'); const alertsPanel = adminPage.locator('[data-testid="system-alerts"]'); await expect(alertsPanel).toBeVisible(); const highCpuAlert = alertsPanel.locator('[data-testid="alert-high_cpu"]'); await expect(highCpuAlert).toBeVisible(); await expect(highCpuAlert.locator('[data-testid="alert-severity"]')).toContainText('warning'); }); }); test.describe('@infrastructure @database Database Health Monitoring', () => { test('should monitor database connections and performance', async ({ adminPage, apiContext }) => { await adminPage.click('[data-testid="database-health-tab"]'); await TestUtils.waitForElement(adminPage, '[data-testid="database-metrics"]'); const databaseMetrics = adminPage.locator('[data-testid="database-metrics"]'); await expect(databaseMetrics).toBeVisible(); // MongoDB metrics const mongoSection = databaseMetrics.locator('[data-testid="mongodb-metrics"]'); await expect(mongoSection).toBeVisible(); await expect(mongoSection.locator('[data-testid="connection-status"]')).toBeVisible(); await expect(mongoSection.locator('[data-testid="active-connections"]')).toBeVisible(); await expect(mongoSection.locator('[data-testid="query-performance"]')).toBeVisible(); // Redis metrics const redisSection = databaseMetrics.locator('[data-testid="redis-metrics"]'); await expect(redisSection).toBeVisible(); await expect(redisSection.locator('[data-testid="memory-usage"]')).toBeVisible(); await expect(redisSection.locator('[data-testid="hit-rate"]')).toBeVisible(); // TimescaleDB metrics const timescaleSection = databaseMetrics.locator('[data-testid="timescaledb-metrics"]'); await expect(timescaleSection).toBeVisible(); await expect(timescaleSection.locator('[data-testid="connection-status"]')).toBeVisible(); await expect(timescaleSection.locator('[data-testid="query-performance"]')).toBeVisible(); }); test('should test database connectivity', async ({ apiContext }) => { // Test MongoDB connection const mongoResponse = await apiContext.get('/api/health/database/mongodb'); expect(mongoResponse.ok()).toBeTruthy(); const mongoHealth = await mongoResponse.json(); expect(mongoHealth.status).toBe('healthy'); expect(mongoHealth).toHaveProperty('response_time'); expect(mongoHealth).toHaveProperty('active_connections'); // Test Redis connection const redisResponse = await apiContext.get('/api/health/database/redis'); expect(redisResponse.ok()).toBeTruthy(); const redisHealth = await redisResponse.json(); expect(redisHealth.status).toBe('healthy'); expect(redisHealth).toHaveProperty('memory_usage'); expect(redisHealth).toHaveProperty('hit_rate'); // Test TimescaleDB connection const timescaleResponse = await apiContext.get('/api/health/database/timescaledb'); expect(timescaleResponse.ok()).toBeTruthy(); const timescaleHealth = await timescaleResponse.json(); expect(timescaleHealth.status).toBe('healthy'); expect(timescaleHealth).toHaveProperty('response_time'); }); }); test.describe('@infrastructure @ssl SSL Certificate Monitoring', () => { test('should monitor SSL certificate status', async ({ adminPage }) => { await adminPage.click('[data-testid="ssl-monitoring-tab"]'); await TestUtils.waitForElement(adminPage, '[data-testid="ssl-certificates"]'); const sslSection = adminPage.locator('[data-testid="ssl-certificates"]'); await expect(sslSection).toBeVisible(); // Main certificate const mainCert = sslSection.locator('[data-testid="main-certificate"]'); await expect(mainCert).toBeVisible(); await expect(mainCert.locator('[data-testid="cert-status"]')).toBeVisible(); await expect(mainCert.locator('[data-testid="cert-expiry"]')).toBeVisible(); await expect(mainCert.locator('[data-testid="cert-issuer"]')).toBeVisible(); // Days until expiry should be displayed const expiryText = await mainCert.locator('[data-testid="cert-expiry"]').textContent(); expect(expiryText).toMatch(/\d+ days?/); }); test('should alert on certificate expiration', async ({ adminPage, apiContext }) => { // Check SSL certificate status via API const sslResponse = await apiContext.get('/api/health/ssl'); expect(sslResponse.ok()).toBeTruthy(); const sslHealth = await sslResponse.json(); expect(sslHealth).toHaveProperty('certificates'); expect(sslHealth.certificates).toBeInstanceOf(Array); for (const cert of sslHealth.certificates) { expect(cert).toHaveProperty('domain'); expect(cert).toHaveProperty('expires_in_days'); expect(cert).toHaveProperty('status'); // Should alert if expiring soon if (cert.expires_in_days < 30) { expect(cert.status).toMatch(/warning|critical/); } } }); }); test.describe('@infrastructure @alerts Alert System', () => { test('should display active alerts', async ({ adminPage }) => { await TestUtils.waitForElement(adminPage, '[data-testid="active-alerts"]'); const alertsPanel = adminPage.locator('[data-testid="active-alerts"]'); await expect(alertsPanel).toBeVisible(); // Should show alert count const alertCount = alertsPanel.locator('[data-testid="alert-count"]'); await expect(alertCount).toBeVisible(); // Should have alert list const alertList = alertsPanel.locator('[data-testid="alert-list"]'); await expect(alertList).toBeVisible(); }); test('should allow alert acknowledgment', async ({ adminPage }) => { // Find an active alert const firstAlert = adminPage.locator('[data-testid="alert-item"]').first(); if (await firstAlert.isVisible()) { // Acknowledge the alert await firstAlert.locator('[data-testid="acknowledge-alert"]').click(); await TestUtils.waitForElement(adminPage, '[data-testid="acknowledge-modal"]'); const ackModal = adminPage.locator('[data-testid="acknowledge-modal"]'); await expect(ackModal).toBeVisible(); await adminPage.fill('[data-testid="ack-comment"]', 'Alert acknowledged - investigating'); await adminPage.click('[data-testid="confirm-acknowledge"]'); // Verify alert is acknowledged await TestUtils.waitForElement(adminPage, '[data-testid="ack-success"]'); await expect(firstAlert.locator('[data-testid="alert-status"]')).toContainText('acknowledged'); } }); test('should send webhook notifications for critical alerts', async ({ apiContext }) => { // Simulate critical alert const alertResponse = await apiContext.post('/api/test/simulate-alert', { data: { type: 'service_down', service: 'ml-service', severity: 'critical' } }); expect(alertResponse.ok()).toBeTruthy(); // Verify webhook was called (check logs or mock webhook) const webhookResponse = await apiContext.get('/api/test/webhook-calls'); expect(webhookResponse.ok()).toBeTruthy(); const webhookCalls = await webhookResponse.json(); const criticalAlert = webhookCalls.find((call: any) => call.data.severity === 'critical' && call.data.service === 'ml-service' ); expect(criticalAlert).toBeDefined(); }); }); test.describe('@infrastructure @recovery Automated Recovery', () => { test('should automatically restart failed services', async ({ adminPage, apiContext }) => { // Simulate service failure const failureResponse = await apiContext.post('/api/test/simulate-failure', { data: { service: 'test-service', failure_type: 'crash' } }); expect(failureResponse.ok()).toBeTruthy(); // Wait for automated recovery await adminPage.waitForTimeout(10000); // Check recovery status const recoveryResponse = await apiContext.get('/api/health/recovery-status'); expect(recoveryResponse.ok()).toBeTruthy(); const recoveryStatus = await recoveryResponse.json(); expect(recoveryStatus).toHaveProperty('recent_recoveries'); // Should have attempted recovery const testServiceRecovery = recoveryStatus.recent_recoveries.find( (recovery: any) => recovery.service === 'test-service' ); expect(testServiceRecovery).toBeDefined(); expect(testServiceRecovery.action).toBe('restart'); }); test('should escalate after multiple failed recovery attempts', async ({ apiContext }) => { // Simulate multiple failures for (let i = 0; i < 3; i++) { await apiContext.post('/api/test/simulate-failure', { data: { service: 'persistent-failure-service', failure_type: 'crash' } }); await new Promise(resolve => setTimeout(resolve, 2000)); } // Check escalation status const escalationResponse = await apiContext.get('/api/health/escalation-status'); expect(escalationResponse.ok()).toBeTruthy(); const escalationStatus = await escalationResponse.json(); expect(escalationStatus).toHaveProperty('escalated_services'); const escalatedService = escalationStatus.escalated_services.find( (service: any) => service.name === 'persistent-failure-service' ); expect(escalatedService).toBeDefined(); expect(escalatedService.escalation_reason).toContain('multiple_failures'); }); }); test.describe('@infrastructure @performance Performance Monitoring', () => { test('should track response times and throughput', async ({ adminPage }) => { await adminPage.click('[data-testid="performance-tab"]'); await TestUtils.waitForElement(adminPage, '[data-testid="performance-metrics"]'); const performanceSection = adminPage.locator('[data-testid="performance-metrics"]'); await expect(performanceSection).toBeVisible(); // API response times const apiMetrics = performanceSection.locator('[data-testid="api-metrics"]'); await expect(apiMetrics).toBeVisible(); await expect(apiMetrics.locator('[data-testid="avg-response-time"]')).toBeVisible(); await expect(apiMetrics.locator('[data-testid="p95-response-time"]')).toBeVisible(); await expect(apiMetrics.locator('[data-testid="throughput"]')).toBeVisible(); // ML service metrics const mlMetrics = performanceSection.locator('[data-testid="ml-metrics"]'); await expect(mlMetrics).toBeVisible(); await expect(mlMetrics.locator('[data-testid="prediction-time"]')).toBeVisible(); await expect(mlMetrics.locator('[data-testid="model-accuracy"]')).toBeVisible(); }); test('should monitor Core Web Vitals', async ({ adminPage }) => { await adminPage.click('[data-testid="web-vitals-tab"]'); await TestUtils.waitForElement(adminPage, '[data-testid="web-vitals-metrics"]'); const webVitalsSection = adminPage.locator('[data-testid="web-vitals-metrics"]'); await expect(webVitalsSection).toBeVisible(); // Core Web Vitals await expect(webVitalsSection.locator('[data-testid="lcp-metric"]')).toBeVisible(); // Largest Contentful Paint await expect(webVitalsSection.locator('[data-testid="fid-metric"]')).toBeVisible(); // First Input Delay await expect(webVitalsSection.locator('[data-testid="cls-metric"]')).toBeVisible(); // Cumulative Layout Shift // Verify metrics are within acceptable ranges const lcpValue = await webVitalsSection.locator('[data-testid="lcp-value"]').textContent(); const lcpMs = parseFloat(lcpValue?.replace('ms', '') || '0'); expect(lcpMs).toBeLessThan(2500); // Good LCP threshold const clsValue = await webVitalsSection.locator('[data-testid="cls-value"]').textContent(); const cls = parseFloat(clsValue || '0'); expect(cls).toBeLessThan(0.1); // Good CLS threshold }); }); test.describe('@infrastructure @logs Log Management', () => { test('should provide access to service logs', async ({ adminPage }) => { // Click on a service to view logs await adminPage.click('[data-testid="service-backend"]'); await TestUtils.waitForElement(adminPage, '[data-testid="service-details-modal"]'); await adminPage.click('[data-testid="view-logs"]'); await TestUtils.waitForElement(adminPage, '[data-testid="logs-viewer"]'); const logsViewer = adminPage.locator('[data-testid="logs-viewer"]'); await expect(logsViewer).toBeVisible(); // Should have log entries const logEntries = logsViewer.locator('[data-testid="log-entry"]'); expect(await logEntries.count()).toBeGreaterThan(0); // Should have log filtering options await expect(logsViewer.locator('[data-testid="log-level-filter"]')).toBeVisible(); await expect(logsViewer.locator('[data-testid="log-search"]')).toBeVisible(); await expect(logsViewer.locator('[data-testid="log-time-range"]')).toBeVisible(); }); test('should allow log filtering and searching', async ({ adminPage }) => { await adminPage.click('[data-testid="service-backend"]'); await TestUtils.waitForElement(adminPage, '[data-testid="service-details-modal"]'); await adminPage.click('[data-testid="view-logs"]'); await TestUtils.waitForElement(adminPage, '[data-testid="logs-viewer"]'); // Filter by error level await adminPage.selectOption('[data-testid="log-level-filter"]', 'error'); await TestUtils.waitForStableNetwork(adminPage); // Verify only error logs are shown const logEntries = adminPage.locator('[data-testid="log-entry"]'); const entryCount = await logEntries.count(); if (entryCount > 0) { const firstEntry = logEntries.first(); const logLevel = await firstEntry.locator('[data-testid="log-level"]').textContent(); expect(logLevel?.toLowerCase()).toBe('error'); } // Search for specific term await adminPage.fill('[data-testid="log-search"]', 'authentication'); await adminPage.keyboard.press('Enter'); await TestUtils.waitForStableNetwork(adminPage); // Verify search results const searchResults = adminPage.locator('[data-testid="log-entry"]'); const searchCount = await searchResults.count(); if (searchCount > 0) { const firstResult = searchResults.first(); const logText = await firstResult.locator('[data-testid="log-message"]').textContent(); expect(logText?.toLowerCase()).toContain('authentication'); } }); }); });