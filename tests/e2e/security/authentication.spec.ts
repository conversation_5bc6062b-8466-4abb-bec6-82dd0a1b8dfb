/** * ============================================= * SECURITY AND AUTHENTICATION TESTS * Comprehensive testing of security measures and access controls * ============================================= */ import { test, expect, TestUtils } from '../../fixtures/test-fixtures'; import { testConfig } from '../../playwright.config'; test.describe('Security and Authentication', () => { test.describe('@security @smoke Authentication Flow', () => { test('should require authentication for protected routes', async ({ page }) => { const protectedRoutes = [ '/dashboard', '/dashboard/conversations', '/dashboard/analytics', '/dashboard/admin', '/dashboard/admin/users', '/dashboard/admin/system-health' ]; for (const route of protectedRoutes) { await page.goto(route); // Should redirect to login page await page.waitForURL('**/login**'); expect(page.url()).toContain('/login'); // Verify login form is displayed await expect(page.locator('[data-testid="login-form"]')).toBeVisible(); } }); test('should authenticate users with valid credentials', async ({ page }) => { await page.goto('/login'); // Fill login form await page.fill('[data-testid="email-input"]', testConfig.users.agent.email); await page.fill('[data-testid="password-input"]', testConfig.users.agent.password); // Submit form await page.click('[data-testid="login-button"]'); // Should redirect to dashboard await page.waitForURL('**/dashboard**'); expect(page.url()).toContain('/dashboard'); // Verify user is authenticated await expect(page.locator('[data-testid="user-menu"]')).toBeVisible(); await expect(page.locator('[data-testid="logout-button"]')).toBeVisible(); }); test('should reject invalid credentials', async ({ page }) => { await page.goto('/login'); const invalidCredentials = [ { email: '<EMAIL>', password: 'wrongpassword' }, { email: testConfig.users.agent.email, password: 'wrongpassword' }, { email: '<EMAIL>', password: testConfig.users.agent.password }, { email: '', password: testConfig.users.agent.password }, { email: testConfig.users.agent.email, password: '' } ]; for (const credentials of invalidCredentials) { await page.fill('[data-testid="email-input"]', credentials.email); await page.fill('[data-testid="password-input"]', credentials.password); await page.click('[data-testid="login-button"]'); // Should show error message await expect(page.locator('[data-testid="error-message"]')).toBeVisible(); // Should remain on login page expect(page.url()).toContain('/login'); // Clear form for next iteration await page.fill('[data-testid="email-input"]', ''); await page.fill('[data-testid="password-input"]', ''); } }); test('should implement rate limiting for login attempts', async ({ page }) => { await page.goto('/login'); // Attempt multiple failed logins for (let i = 0; i < 6; i++) { await page.fill('[data-testid="email-input"]', '<EMAIL>'); await page.fill('[data-testid="password-input"]', 'wrongpassword'); await page.click('[data-testid="login-button"]'); await page.waitForTimeout(500); } // Should show rate limiting message const rateLimitMessage = page.locator('[data-testid="rate-limit-message"]'); if (await rateLimitMessage.isVisible()) { await expect(rateLimitMessage).toContainText('trop de tentatives'); } }); test('should handle session expiration', async ({ authenticatedPage }) => { // Simulate session expiration by clearing tokens await authenticatedPage.evaluate(() => { localStorage.removeItem('authToken'); sessionStorage.removeItem('authToken'); }); // Try to access protected resource await authenticatedPage.goto('/dashboard/admin'); // Should redirect to login await authenticatedPage.waitForURL('**/login**'); expect(authenticatedPage.url()).toContain('/login'); }); test('should logout users properly', async ({ authenticatedPage }) => { // Verify user is logged in await expect(authenticatedPage.locator('[data-testid="user-menu"]')).toBeVisible(); // Logout await authenticatedPage.click('[data-testid="user-menu"]'); await authenticatedPage.click('[data-testid="logout-button"]'); // Should redirect to login await authenticatedPage.waitForURL('**/login**'); expect(authenticatedPage.url()).toContain('/login'); // Verify tokens are cleared const authToken = await authenticatedPage.evaluate(() => { return localStorage.getItem('authToken') || sessionStorage.getItem('authToken'); }); expect(authToken).toBeNull(); }); }); test.describe('@security @regression Role-Based Access Control', () => { test('should enforce admin-only access to admin routes', async ({ agentPage, adminPage }) => { const adminOnlyRoutes = [ '/dashboard/admin/users', '/dashboard/admin/system-health', '/dashboard/admin/platforms', '/dashboard/admin/routing', '/dashboard/admin/escalations' ]; // Test with agent role (should be denied) for (const route of adminOnlyRoutes) { await agentPage.goto(route); // Should show access denied or redirect const accessDenied = agentPage.locator('[data-testid="access-denied"]'); const currentUrl = agentPage.url(); if (await accessDenied.isVisible()) { await expect(accessDenied).toBeVisible(); } else { // Should redirect away from admin route expect(currentUrl).not.toContain(route); } } // Test with admin role (should be allowed) for (const route of adminOnlyRoutes) { await adminPage.goto(route); await TestUtils.waitForStableNetwork(adminPage); // Should successfully load the page expect(adminPage.url()).toContain(route); await expect(adminPage.locator('[data-testid="page-content"]')).toBeVisible(); } }); test('should restrict API access based on user roles', async ({ apiContext }) => { // Test admin-only API endpoints const adminEndpoints = [ '/api/admin/users', '/api/admin/system/health', '/api/admin/platforms/config', '/api/admin/analytics/full' ]; for (const endpoint of adminEndpoints) { const response = await apiContext.get(endpoint); // Should either succeed (if admin) or return 403 (if not admin) expect([200, 403]).toContain(response.status()); if (response.status() === 403) { const errorData = await response.json(); expect(errorData).toHaveProperty('error'); expect(errorData.error.toLowerCase()).toContain('access denied'); } } }); test('should validate user permissions for specific actions', async ({ agentPage, adminPage }) => { // Test agent permissions await agentPage.goto('/dashboard/conversations'); await TestUtils.waitForStableNetwork(agentPage); // Agents should see conversation management await expect(agentPage.locator('[data-testid="conversations-list"]')).toBeVisible(); // But not admin functions const adminButtons = agentPage.locator('[data-testid="admin-actions"]'); if (await adminButtons.isVisible()) { expect(await adminButtons.count()).toBe(0); } // Test admin permissions await adminPage.goto('/dashboard/admin/users'); await TestUtils.waitForStableNetwork(adminPage); // Admins should see user management await expect(adminPage.locator('[data-testid="user-management"]')).toBeVisible(); await expect(adminPage.locator('[data-testid="create-user-button"]')).toBeVisible(); await expect(adminPage.locator('[data-testid="delete-user-button"]')).toBeVisible(); }); }); test.describe('@security @api API Security', () => { test('should require valid JWT tokens for API access', async ({ page }) => { // Test API call without token const response = await page.request.get('/api/conversations'); expect(response.status()).toBe(401); const errorData = await response.json(); expect(errorData).toHaveProperty('error'); expect(errorData.error.toLowerCase()).toContain('unauthorized'); }); test('should validate JWT token integrity', async ({ page }) => { // Test with invalid token const invalidTokens = [ 'invalid.token.here', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid.signature', '', 'Bearer invalid-token' ]; for (const token of invalidTokens) { const response = await page.request.get('/api/conversations', { headers: { 'Authorization': `Bearer ${token}` } }); expect(response.status()).toBe(401); } }); test('should implement proper CORS headers', async ({ page }) => { const response = await page.request.options('/api/health'); // Check CORS headers const corsHeaders = response.headers(); expect(corsHeaders).toHaveProperty('access-control-allow-origin'); expect(corsHeaders).toHaveProperty('access-control-allow-methods'); expect(corsHeaders).toHaveProperty('access-control-allow-headers'); }); test('should sanitize input data', async ({ apiContext }) => { // Test XSS prevention const xssPayloads = [ '<script>alert("xss")</script>', 'javascript:alert("xss")', '<img src="x" onerror="alert(1)">', '"><script>alert("xss")</script>' ]; for (const payload of xssPayloads) { const response = await apiContext.post('/api/conversations', { data: { message: payload, customerId: 'test-customer' } }); if (response.ok()) { const data = await response.json(); // Message should be sanitized expect(data.message).not.toContain('<script>'); expect(data.message).not.toContain('javascript:'); } } }); test('should prevent SQL injection attempts', async ({ apiContext }) => { const sqlInjectionPayloads = [ "'; DROP TABLE users; --", "' OR '1'='1", "1' UNION SELECT * FROM users --", "'; DELETE FROM conversations; --" ]; for (const payload of sqlInjectionPayloads) { const response = await apiContext.get(`/api/conversations?search=${encodeURIComponent(payload)}`); // Should not return 500 error (which might indicate SQL error) expect(response.status()).not.toBe(500); if (response.ok()) { const data = await response.json(); // Should return normal search results, not expose database structure expect(data).toHaveProperty('conversations'); } } }); }); test.describe('@security @headers Security Headers', () => { test('should implement security headers', async ({ page }) => { const response = await page.goto('/'); const headers = response?.headers() || {}; // Check for security headers expect(headers).toHaveProperty('x-frame-options'); expect(headers['x-frame-options']).toBe('DENY'); expect(headers).toHaveProperty('x-content-type-options'); expect(headers['x-content-type-options']).toBe('nosniff'); expect(headers).toHaveProperty('x-xss-protection'); expect(headers['x-xss-protection']).toBe('1; mode=block'); expect(headers).toHaveProperty('strict-transport-security'); expect(headers['strict-transport-security']).toContain('max-age='); expect(headers).toHaveProperty('content-security-policy'); expect(headers['content-security-policy']).toContain('default-src'); }); test('should implement Content Security Policy', async ({ page }) => { const response = await page.goto('/'); const headers = response?.headers() || {}; const csp = headers['content-security-policy']; expect(csp).toBeDefined(); // Check CSP directives expect(csp).toContain('default-src'); expect(csp).toContain('script-src'); expect(csp).toContain('style-src'); expect(csp).toContain('img-src'); expect(csp).toContain('connect-src'); // Should not allow unsafe-eval or unsafe-inline without nonce expect(csp).not.toContain('unsafe-eval'); if (csp.includes('unsafe-inline')) { expect(csp).toContain('nonce-'); } }); test('should prevent clickjacking attacks', async ({ page }) => { const response = await page.goto('/'); const headers = response?.headers() || {}; // X-Frame-Options should prevent framing expect(headers['x-frame-options']).toMatch(/DENY|SAMEORIGIN/); // CSP frame-ancestors should also be set const csp = headers['content-security-policy']; if (csp) { expect(csp).toContain('frame-ancestors'); } }); }); test.describe('@security @data Data Protection', () => { test('should encrypt sensitive data in transit', async ({ page }) => { // Verify HTTPS is enforced await page.goto('/login'); expect(page.url()).toMatch(/^https:/); // Check for secure cookies const cookies = await page.context().cookies(); for (const cookie of cookies) { if (cookie.name.includes('session') || cookie.name.includes('auth')) { expect(cookie.secure).toBeTruthy(); expect(cookie.httpOnly).toBeTruthy(); } } }); test('should mask sensitive information in logs', async ({ apiContext }) => { // Make API call with sensitive data const response = await apiContext.post('/api/auth/login', { data: { email: '<EMAIL>', password: 'sensitive-password-123' } }); // Check that password is not exposed in response const responseText = await response.text(); expect(responseText).not.toContain('sensitive-password-123'); if (response.ok()) { const data = await response.json(); expect(data).not.toHaveProperty('password'); } }); test('should implement proper session management', async ({ authenticatedPage }) => { // Check session cookie properties const cookies = await authenticatedPage.context().cookies(); const sessionCookie = cookies.find(c => c.name.includes('session') || c.name.includes('auth')); if (sessionCookie) { expect(sessionCookie.secure).toBeTruthy(); expect(sessionCookie.httpOnly).toBeTruthy(); expect(sessionCookie.sameSite).toMatch(/Strict|Lax/); } // Verify session timeout const sessionData = await authenticatedPage.evaluate(() => { return { localStorage: Object.keys(localStorage), sessionStorage: Object.keys(sessionStorage) }; }); // Should have auth-related storage const hasAuthStorage = sessionData.localStorage.some(key => key.includes('auth')) || sessionData.sessionStorage.some(key => key.includes('auth')); expect(hasAuthStorage).toBeTruthy(); }); }); test.describe('@security @vulnerability Vulnerability Testing', () => { test('should prevent directory traversal attacks', async ({ page }) => { const traversalPayloads = [ '../../../etc/passwd', '..\\..\\..\\windows\\system32\\config\\sam', '%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd', '....//....//....//etc/passwd' ]; for (const payload of traversalPayloads) { const response = await page.request.get(`/api/files/${encodeURIComponent(payload)}`); // Should not return file contents or 200 status expect(response.status()).not.toBe(200); if (response.status() !== 404) { const responseText = await response.text(); expect(responseText).not.toContain('root:'); expect(responseText).not.toContain('Administrator'); } } }); test('should handle malformed requests gracefully', async ({ page }) => { const malformedRequests = [ { url: '/api/conversations', method: 'POST', data: 'invalid-json' }, { url: '/api/conversations', method: 'POST', data: { malformed: undefined } }, { url: '/api/conversations', method: 'POST', data: null } ]; for (const request of malformedRequests) { const response = await page.request.post(request.url, { data: request.data, headers: { 'Content-Type': 'application/json' } }); // Should return 400 Bad Request, not 500 Internal Server Error expect(response.status()).toBe(400); const errorData = await response.json(); expect(errorData).toHaveProperty('error'); } }); test('should implement rate limiting', async ({ page }) => { const requests = []; // Make multiple rapid requests for (let i = 0; i < 20; i++) { requests.push(page.request.get('/api/health')); } const responses = await Promise.all(requests); // Some requests should be rate limited const rateLimitedResponses = responses.filter(r => r.status() === 429); if (rateLimitedResponses.length > 0) { // Verify rate limit headers const rateLimitResponse = rateLimitedResponses[0]; const headers = rateLimitResponse.headers(); expect(headers).toHaveProperty('x-ratelimit-limit'); expect(headers).toHaveProperty('x-ratelimit-remaining'); expect(headers).toHaveProperty('x-ratelimit-reset'); } }); }); });