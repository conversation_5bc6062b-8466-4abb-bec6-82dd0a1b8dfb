import { test, expect, Page } from '@playwright/test'; /** * Comprehensive Test Suite for Modernized Support Form Page * Tests all enhanced features implemented in Phase 3 */ test.describe('Support Form Page - Modernized Features', () => { let page: Page; const BASE_URL = process.env.BASE_URL || 'http://localhost:3001'; test.beforeEach(async ({ browser }) => { page = await browser.newPage(); await page.goto(BASE_URL); await page.waitForLoadState('networkidle'); // Navigate to Support Form page // Try different navigation methods const supportFormLink = page.locator('text=Support').or(page.locator('text=Formulaire')).or(page.locator('[href*="support"]')); if (await supportFormLink.isVisible()) { await supportFormLink.click(); await page.waitForLoadState('networkidle'); } }); test.afterEach(async () => { await page.close(); }); test.describe('Step-Based Form Navigation', () => { test('should display stepper with all 4 steps', async () => { // Check for stepper component const stepper = page.locator('.MuiStepper-root').or(page.locator('[data-testid="form-stepper"]')); await expect(stepper).toBeVisible(); // Check for all step labels await expect(page.locator('text=Informations personnelles')).toBeVisible(); await expect(page.locator('text=Détails du problème')).toBeVisible(); await expect(page.locator('text=Pièces jointes')).toBeVisible(); await expect(page.locator('text=Confirmation')).toBeVisible(); }); test('should start on step 1 (Personal Information)', async () => { // Check that we're on the first step const step1Content = page.locator('text=Informations personnelles').or(page.locator('text=Nom complet')); await expect(step1Content).toBeVisible(); // Check that personal information fields are visible const nameField = page.locator('input[label*="Nom"]').or(page.locator('input[placeholder*="nom"]')); const emailField = page.locator('input[type="email"]').or(page.locator('input[label*="email"]')); await expect(nameField).toBeVisible(); await expect(emailField).toBeVisible(); }); test('should navigate between steps correctly', async () => { // Fill step 1 with valid data const nameField = page.locator('input').first(); const emailField = page.locator('input[type="email"]').first(); await nameField.fill('Jean Dupont'); await emailField.fill('<EMAIL>'); // Click next button const nextButton = page.locator('button:has-text("Suivant")').or(page.locator('button:has-text("Next")')); await nextButton.click(); await page.waitForTimeout(500); // Should be on step 2 const step2Content = page.locator('text=Détails du problème').or(page.locator('text=Catégorie')); await expect(step2Content).toBeVisible(); // Test back button const backButton = page.locator('button:has-text("Précédent")').or(page.locator('button:has-text("Back")')); await backButton.click(); await page.waitForTimeout(500); // Should be back on step 1 await expect(nameField).toBeVisible(); }); }); test.describe('Step 1: Personal Information', () => { test('should validate required fields', async () => { // Try to proceed without filling required fields const nextButton = page.locator('button:has-text("Suivant")').or(page.locator('button:has-text("Next")')); await nextButton.click(); // Should show validation errors or stay on same step const errorMessage = page.locator('text=requis').or(page.locator('text=required')).or(page.locator('.MuiFormHelperText-root')); await expect(errorMessage.first()).toBeVisible(); }); test('should validate email format', async () => { const emailField = page.locator('input[type="email"]').first(); // Enter invalid email await emailField.fill('invalid-email'); await emailField.blur(); await page.waitForTimeout(500); // Should show email validation error const emailError = page.locator('text=invalide').or(page.locator('text=invalid')); await expect(emailError).toBeVisible(); }); test('should validate phone number format', async () => { const phoneField = page.locator('input[label*="téléphone"]').or(page.locator('input[placeholder*="phone"]')); if (await phoneField.isVisible()) { // Enter invalid phone number await phoneField.fill('123'); await phoneField.blur(); await page.waitForTimeout(500); // Should show phone validation error const phoneError = page.locator('text=invalide').or(page.locator('text=invalid')); await expect(phoneError).toBeVisible(); } }); test('should accept valid personal information', async () => { // Fill all fields with valid data const nameField = page.locator('input').first(); const emailField = page.locator('input[type="email"]').first(); await nameField.fill('Jean Dupont'); await emailField.fill('<EMAIL>'); // Optional phone field const phoneField = page.locator('input[label*="téléphone"]').or(page.locator('input[placeholder*="phone"]')); if (await phoneField.isVisible()) { await phoneField.fill('01 23 45 67 89'); } // Should be able to proceed to next step const nextButton = page.locator('button:has-text("Suivant")').or(page.locator('button:has-text("Next")')); await nextButton.click(); await page.waitForTimeout(500); // Should be on step 2 const step2Content = page.locator('text=Détails du problème').or(page.locator('text=Catégorie')); await expect(step2Content).toBeVisible(); }); }); test.describe('Step 2: Problem Details', () => { test.beforeEach(async () => { // Fill step 1 to get to step 2 const nameField = page.locator('input').first(); const emailField = page.locator('input[type="email"]').first(); await nameField.fill('Jean Dupont'); await emailField.fill('<EMAIL>'); const nextButton = page.locator('button:has-text("Suivant")').or(page.locator('button:has-text("Next")')); await nextButton.click(); await page.waitForTimeout(500); }); test('should display category selection', async () => { const categorySelect = page.locator('[role="combobox"]').or(page.locator('select')).first(); await expect(categorySelect).toBeVisible(); }); test('should display priority selection', async () => { const prioritySelect = page.locator('text=Priorité').or(page.locator('text=Priority')); await expect(prioritySelect).toBeVisible(); }); test('should validate required fields', async () => { // Try to proceed without filling required fields const nextButton = page.locator('button:has-text("Suivant")').or(page.locator('button:has-text("Next")')); await nextButton.click(); // Should show validation errors const errorMessage = page.locator('text=requis').or(page.locator('text=required')); await expect(errorMessage.first()).toBeVisible(); }); test('should validate description length', async () => { const descriptionField = page.locator('textarea').or(page.locator('input[multiline]')); // Enter short description await descriptionField.fill('Test'); await descriptionField.blur(); await page.waitForTimeout(500); // Should show length validation error const lengthError = page.locator('text=caractères').or(page.locator('text=characters')); await expect(lengthError).toBeVisible(); }); test('should accept valid problem details', async () => { // Select category const categorySelect = page.locator('[role="combobox"]').first(); await categorySelect.click(); const categoryOption = page.locator('[role="option"]').first(); await categoryOption.click(); // Fill description const descriptionField = page.locator('textarea').or(page.locator('input[multiline]')); await descriptionField.fill('Je rencontre un problème avec ma connexion internet. La vitesse est très lente depuis hier matin.'); // Should be able to proceed to next step const nextButton = page.locator('button:has-text("Suivant")').or(page.locator('button:has-text("Next")')); await nextButton.click(); await page.waitForTimeout(500); // Should be on step 3 const step3Content = page.locator('text=Pièces jointes').or(page.locator('text=Attachments')); await expect(step3Content).toBeVisible(); }); }); test.describe('Step 3: File Attachments', () => { test.beforeEach(async () => { // Fill steps 1 and 2 to get to step 3 const nameField = page.locator('input').first(); const emailField = page.locator('input[type="email"]').first(); await nameField.fill('Jean Dupont'); await emailField.fill('<EMAIL>'); let nextButton = page.locator('button:has-text("Suivant")').or(page.locator('button:has-text("Next")')); await nextButton.click(); await page.waitForTimeout(500); // Fill step 2 const categorySelect = page.locator('[role="combobox"]').first(); await categorySelect.click(); const categoryOption = page.locator('[role="option"]').first(); await categoryOption.click(); const descriptionField = page.locator('textarea').or(page.locator('input[multiline]')); await descriptionField.fill('Je rencontre un problème avec ma connexion internet.'); nextButton = page.locator('button:has-text("Suivant")').or(page.locator('button:has-text("Next")')); await nextButton.click(); await page.waitForTimeout(500); }); test('should display file upload area', async () => { const uploadArea = page.locator('text=Glissez').or(page.locator('text=Drop')).or(page.locator('input[type="file"]')); await expect(uploadArea.first()).toBeVisible(); }); test('should allow proceeding without files (optional)', async () => { // Should be able to proceed without uploading files const nextButton = page.locator('button:has-text("Suivant")').or(page.locator('button:has-text("Next")')); await nextButton.click(); await page.waitForTimeout(500); // Should be on step 4 const step4Content = page.locator('text=Confirmation').or(page.locator('text=Confirm')); await expect(step4Content).toBeVisible(); }); test('should handle file upload simulation', async () => { // Note: File upload testing in Playwright requires special handling // This test validates the UI elements are present const fileInput = page.locator('input[type="file"]'); await expect(fileInput).toBeAttached(); const uploadButton = page.locator('text=Choisir').or(page.locator('text=Choose')).or(page.locator('text=Sélectionner')); if (await uploadButton.isVisible()) { await expect(uploadButton).toBeVisible(); } }); }); test.describe('Step 4: Confirmation', () => { test.beforeEach(async () => { // Fill all steps to get to confirmation const nameField = page.locator('input').first(); const emailField = page.locator('input[type="email"]').first(); await nameField.fill('Jean Dupont'); await emailField.fill('<EMAIL>'); let nextButton = page.locator('button:has-text("Suivant")').or(page.locator('button:has-text("Next")')); await nextButton.click(); await page.waitForTimeout(500); // Fill step 2 const categorySelect = page.locator('[role="combobox"]').first(); await categorySelect.click(); const categoryOption = page.locator('[role="option"]').first(); await categoryOption.click(); const descriptionField = page.locator('textarea').or(page.locator('input[multiline]')); await descriptionField.fill('Je rencontre un problème avec ma connexion internet.'); nextButton = page.locator('button:has-text("Suivant")').or(page.locator('button:has-text("Next")')); await nextButton.click(); await page.waitForTimeout(500); // Skip file upload nextButton = page.locator('button:has-text("Suivant")').or(page.locator('button:has-text("Next")')); await nextButton.click(); await page.waitForTimeout(500); }); test('should display form summary', async () => { // Should show confirmation content const confirmationContent = page.locator('text=Confirmation').or(page.locator('text=Jean Dupont')); await expect(confirmationContent.first()).toBeVisible(); // Should show submit button const submitButton = page.locator('button:has-text("Envoyer")').or(page.locator('button[type="submit"]')); await expect(submitButton).toBeVisible(); }); test('should handle form submission', async () => { const submitButton = page.locator('button:has-text("Envoyer")').or(page.locator('button[type="submit"]')); await submitButton.click(); // Should show loading state or success message const loadingOrSuccess = page.locator('text=cours').or(page.locator('text=succès')).or(page.locator('.MuiCircularProgress-root')); await expect(loadingOrSuccess.first()).toBeVisible(); }); }); test.describe('Responsive Design', () => { test('should work on mobile devices', async () => { await page.setViewportSize({ width: 375, height: 667 }); await page.reload(); await page.waitForLoadState('networkidle'); // Form should be visible and functional on mobile const formContent = page.locator('input').or(page.locator('form')).first(); await expect(formContent).toBeVisible(); // Stepper should adapt to mobile const stepper = page.locator('.MuiStepper-root').or(page.locator('text=Informations')); await expect(stepper.first()).toBeVisible(); }); test('should work on tablet devices', async () => { await page.setViewportSize({ width: 768, height: 1024 }); await page.reload(); await page.waitForLoadState('networkidle'); // Form should be properly laid out on tablet const formContent = page.locator('input').or(page.locator('form')).first(); await expect(formContent).toBeVisible(); }); test('should work on desktop', async () => { await page.setViewportSize({ width: 1920, height: 1080 }); await page.reload(); await page.waitForLoadState('networkidle'); // Form should utilize full desktop layout const formContent = page.locator('input').or(page.locator('form')).first(); await expect(formContent).toBeVisible(); }); }); test.describe('Interactive Elements', () => { test('should show real-time validation feedback', async () => { const nameField = page.locator('input').first(); // Enter invalid data await nameField.fill('A'); await nameField.blur(); await page.waitForTimeout(500); // Should show validation feedback const feedback = page.locator('.MuiFormHelperText-root').or(page.locator('text=caractères')); await expect(feedback.first()).toBeVisible(); }); test('should display snackbar notifications', async () => { // This test would need specific triggers for snackbar notifications // For now, we'll check that the snackbar component exists in the DOM const snackbarContainer = page.locator('.MuiSnackbar-root').or(page.locator('[role="alert"]')); // Snackbar might not be visible initially, but should exist in DOM await expect(snackbarContainer).toBeAttached(); }); test('should handle button states correctly', async () => { // Next button should be disabled initially (no data) const nextButton = page.locator('button:has-text("Suivant")').or(page.locator('button:has-text("Next")')); // Fill required fields const nameField = page.locator('input').first(); const emailField = page.locator('input[type="email"]').first(); await nameField.fill('Jean Dupont'); await emailField.fill('<EMAIL>'); // Button should now be enabled await expect(nextButton).toBeEnabled(); }); }); test.describe('Professional UI/UX', () => { test('should display Free Mobile branding', async () => { // Check for Free Mobile colors and branding const brandingElements = page.locator('text=Free').or(page.locator('text=Support')); await expect(brandingElements.first()).toBeVisible(); }); test('should have modern Material-UI components', async () => { // Check for Material-UI classes const muiComponents = page.locator('.MuiCard-root').or(page.locator('.MuiTextField-root')).or(page.locator('.MuiButton-root')); await expect(muiComponents.first()).toBeVisible(); }); test('should display professional layout', async () => { // Check for proper card layout and spacing const cardLayout = page.locator('.MuiCard-root').or(page.locator('[elevation]')); await expect(cardLayout.first()).toBeVisible(); // Check for proper typography const typography = page.locator('h4').or(page.locator('h5')).or(page.locator('h6')); await expect(typography.first()).toBeVisible(); }); }); });