/** * ============================================= * [ANALYTICS] PREDICTIVE ANALYTICS DASHBOARD TESTS * Comprehensive testing of ML-powered analytics and insights * ============================================= */ import { test, expect, TestUtils } from '../../fixtures/test-fixtures'; import { testConfig } from '../../playwright.config'; test.describe('Predictive Analytics Dashboard', () => { test.beforeEach(async ({ adminPage }) => { // Navigate to predictive analytics dashboard await adminPage.goto('/dashboard/analytics/predictive'); await TestUtils.waitForStableNetwork(adminPage); }); test.describe('@analytics @smoke Dashboard Loading and Navigation', () => { test('should load predictive analytics dashboard with all components', async ({ adminPage }) => { // Verify main dashboard is loaded await TestUtils.waitForElement(adminPage, '[data-testid="predictive-analytics-dashboard"]'); const dashboard = adminPage.locator('[data-testid="predictive-analytics-dashboard"]'); await expect(dashboard).toBeVisible(); // Verify header components await expect(dashboard.locator('[data-testid="dashboard-header"]')).toBeVisible(); await expect(dashboard.locator('[data-testid="metric-selector"]')).toBeVisible(); await expect(dashboard.locator('[data-testid="time-range-selector"]')).toBeVisible(); await expect(dashboard.locator('[data-testid="refresh-interval-selector"]')).toBeVisible(); // Verify KPI cards await expect(dashboard.locator('[data-testid="kpi-cards"]')).toBeVisible(); const kpiCards = dashboard.locator('[data-testid="kpi-card"]'); expect(await kpiCards.count()).toBeGreaterThanOrEqual(3); // Verify main chart await expect(dashboard.locator('[data-testid="main-chart-container"]')).toBeVisible(); await expect(dashboard.locator('[data-testid="chart-content"]')).toBeVisible(); // Verify insights sections await expect(dashboard.locator('[data-testid="insights-section"]')).toBeVisible(); await expect(dashboard.locator('[data-testid="insights-panel"]')).toBeVisible(); await expect(dashboard.locator('[data-testid="risk-factors-panel"]')).toBeVisible(); await expect(dashboard.locator('[data-testid="recommendations-panel"]')).toBeVisible(); }); test('should allow metric selection and update dashboard', async ({ adminPage }) => { // Test different metrics const metrics = [ { value: 'satisfaction', label: 'Satisfaction Client' }, { value: 'churn', label: 'Risque de Désabonnement' }, { value: 'response_time', label: 'Temps de Réponse' }, { value: 'resolution', label: 'Taux de Résolution' }, { value: 'escalation', label: 'Escalades' }, { value: 'sentiment', label: 'Sentiment Global' } ]; for (const metric of metrics) { // Select metric await adminPage.selectOption('[data-testid="metric-selector"]', metric.value); await TestUtils.waitForStableNetwork(adminPage); // Verify chart updates const chartTitle = adminPage.locator('[data-testid="chart-header"] h3'); await expect(chartTitle).toContainText(metric.label); // Verify KPI values update const currentValue = adminPage.locator('[data-testid="kpi-card"]:first-child [data-testid="kpi-value"]'); await expect(currentValue).toBeVisible(); // Verify insights update for the metric const insightsPanel = adminPage.locator('[data-testid="insights-panel"]'); await expect(insightsPanel).toBeVisible(); } }); test('should support different time ranges', async ({ adminPage }) => { const timeRanges = [ { value: '7days', label: '7 derniers jours' }, { value: '30days', label: '30 derniers jours' }, { value: '90days', label: '3 derniers mois' }, { value: '365days', label: '12 derniers mois' } ]; for (const timeRange of timeRanges) { // Select time range await adminPage.selectOption('[data-testid="time-range-selector"]', timeRange.value); await TestUtils.waitForStableNetwork(adminPage); // Verify chart data updates const chartCanvas = adminPage.locator('[data-testid="chart-content"] canvas'); await expect(chartCanvas).toBeVisible(); // Verify data points are appropriate for time range const dataPoints = await adminPage.evaluate(() => { const canvas = document.querySelector('[data-testid="chart-content"] canvas') as HTMLCanvasElement; return canvas ? canvas.width > 0 : false; }); expect(dataPoints).toBeTruthy(); } }); }); test.describe('@analytics @regression ML Predictions and Insights', () => { test('should display ML predictions with confidence intervals', async ({ adminPage }) => { // Select satisfaction metric for detailed testing await adminPage.selectOption('[data-testid="metric-selector"]', 'satisfaction'); await TestUtils.waitForStableNetwork(adminPage); // Verify chart shows predictions const chartContainer = adminPage.locator('[data-testid="chart-content"]'); await expect(chartContainer).toBeVisible(); // Check chart legend for prediction elements const chartLegend = adminPage.locator('[data-testid="chart-content"] .chartjs-legend'); if (await chartLegend.isVisible()) { const legendItems = chartLegend.locator('.chartjs-legend-item'); const legendTexts = await legendItems.allTextContents(); expect(legendTexts.some(text => text.includes('Prédiction'))).toBeTruthy(); expect(legendTexts.some(text => text.includes('Intervalle de confiance'))).toBeTruthy(); } // Verify KPI shows prediction const predictedValue = adminPage.locator('[data-testid="kpi-card"]:nth-child(2) [data-testid="kpi-value"]'); await expect(predictedValue).toBeVisible(); const predictedLabel = adminPage.locator('[data-testid="kpi-card"]:nth-child(2) [data-testid="kpi-label"]'); await expect(predictedLabel).toContainText('Prédiction'); }); test('should show AI insights with confidence levels', async ({ adminPage }) => { await TestUtils.waitForElement(adminPage, '[data-testid="insights-panel"]'); const insightsPanel = adminPage.locator('[data-testid="insights-panel"]'); await expect(insightsPanel).toBeVisible(); // Check for insight items const insightItems = insightsPanel.locator('[data-testid="insight-item"]'); const insightCount = await insightItems.count(); if (insightCount > 0) { const firstInsight = insightItems.first(); // Verify insight structure await expect(firstInsight.locator('[data-testid="insight-title"]')).toBeVisible(); await expect(firstInsight.locator('[data-testid="insight-description"]')).toBeVisible(); await expect(firstInsight.locator('[data-testid="insight-confidence"]')).toBeVisible(); // Verify confidence percentage const confidenceText = await firstInsight.locator('[data-testid="insight-confidence"]').textContent(); expect(confidenceText).toMatch(/\d+% confiance/); // Verify insight types const insightClasses = await firstInsight.getAttribute('class'); expect(insightClasses).toMatch(/positive|warning|critical/); } }); test('should display risk factors with impact assessment', async ({ adminPage }) => { await TestUtils.waitForElement(adminPage, '[data-testid="risk-factors-panel"]'); const riskPanel = adminPage.locator('[data-testid="risk-factors-panel"]'); await expect(riskPanel).toBeVisible(); // Check for risk items const riskItems = riskPanel.locator('[data-testid="risk-item"]'); const riskCount = await riskItems.count(); if (riskCount > 0) { const firstRisk = riskItems.first(); // Verify risk structure await expect(firstRisk.locator('[data-testid="risk-factor"]')).toBeVisible(); await expect(firstRisk.locator('[data-testid="risk-impact"]')).toBeVisible(); await expect(firstRisk.locator('[data-testid="risk-probability"]')).toBeVisible(); // Verify impact levels const impactElement = firstRisk.locator('[data-testid="risk-impact"]'); const impactClass = await impactElement.getAttribute('class'); expect(impactClass).toMatch(/critical|high|medium|low/); // Verify probability percentage const probabilityText = await firstRisk.locator('[data-testid="risk-probability"]').textContent(); expect(probabilityText).toMatch(/Probabilité: \d+%/); } }); test('should provide actionable recommendations', async ({ adminPage }) => { await TestUtils.waitForElement(adminPage, '[data-testid="recommendations-panel"]'); const recommendationsPanel = adminPage.locator('[data-testid="recommendations-panel"]'); await expect(recommendationsPanel).toBeVisible(); // Check for recommendation items const recommendationItems = recommendationsPanel.locator('[data-testid="recommendation-item"]'); const recommendationCount = await recommendationItems.count(); if (recommendationCount > 0) { const firstRecommendation = recommendationItems.first(); // Verify recommendation structure await expect(firstRecommendation.locator('[data-testid="recommendation-title"]')).toBeVisible(); await expect(firstRecommendation.locator('[data-testid="recommendation-description"]')).toBeVisible(); await expect(firstRecommendation.locator('[data-testid="recommendation-priority"]')).toBeVisible(); await expect(firstRecommendation.locator('[data-testid="recommendation-impact"]')).toBeVisible(); // Verify priority levels const priorityElement = firstRecommendation.locator('[data-testid="recommendation-priority"]'); const priorityClass = await priorityElement.getAttribute('class'); expect(priorityClass).toMatch(/critical|high|medium|low/); // Verify impact description const impactText = await firstRecommendation.locator('[data-testid="recommendation-impact"]').textContent(); expect(impactText.length).toBeGreaterThan(10); } }); }); test.describe('@analytics @realtime Real-time Updates and Interactivity', () => { test('should auto-refresh data based on selected interval', async ({ adminPage }) => { // Set refresh interval to 1 minute for testing await adminPage.selectOption('[data-testid="refresh-interval-selector"]', '60000'); // Get initial timestamp const initialTimestamp = await adminPage.locator('[data-testid="last-updated"]').textContent(); // Wait for refresh (we'll wait a shorter time for testing) await adminPage.waitForTimeout(5000); // Trigger manual refresh to simulate auto-refresh await adminPage.reload(); await TestUtils.waitForStableNetwork(adminPage); // Verify timestamp updated const updatedTimestamp = await adminPage.locator('[data-testid="last-updated"]').textContent(); expect(updatedTimestamp).not.toBe(initialTimestamp); }); test('should support chart interactions and tooltips', async ({ adminPage }) => { await TestUtils.waitForElement(adminPage, '[data-testid="chart-content"]'); const chartContainer = adminPage.locator('[data-testid="chart-content"]'); const chartCanvas = chartContainer.locator('canvas'); await expect(chartCanvas).toBeVisible(); // Hover over chart to trigger tooltip await chartCanvas.hover({ position: { x: 200, y: 100 } }); // Wait for tooltip to appear (Chart.js tooltips) await adminPage.waitForTimeout(500); // Verify chart is interactive (canvas should respond to mouse events) const canvasRect = await chartCanvas.boundingBox(); expect(canvasRect).toBeTruthy(); expect(canvasRect!.width).toBeGreaterThan(0); expect(canvasRect!.height).toBeGreaterThan(0); }); test('should handle data loading states gracefully', async ({ adminPage }) => { // Change metric to trigger loading await adminPage.selectOption('[data-testid="metric-selector"]', 'churn'); // Check for loading indicators const loadingIndicators = adminPage.locator('[data-testid="loading"], .loading, .spinner'); // Wait for data to load await TestUtils.waitForStableNetwork(adminPage); // Verify dashboard is fully loaded const dashboard = adminPage.locator('[data-testid="predictive-analytics-dashboard"]'); await expect(dashboard).toBeVisible(); // Verify no loading indicators remain const remainingLoaders = await loadingIndicators.count(); expect(remainingLoaders).toBe(0); }); }); test.describe('@analytics @performance Performance and Responsiveness', () => { test('should load dashboard within acceptable time limits', async ({ adminPage }) => { const startTime = Date.now(); // Navigate to dashboard await adminPage.goto('/dashboard/analytics/predictive'); await TestUtils.waitForElement(adminPage, '[data-testid="predictive-analytics-dashboard"]'); const endTime = Date.now(); const loadTime = endTime - startTime; // Should load within 5 seconds expect(loadTime).toBeLessThan(5000); }); test('should handle large datasets efficiently', async ({ adminPage }) => { // Select longest time range await adminPage.selectOption('[data-testid="time-range-selector"]', '365days'); await TestUtils.waitForStableNetwork(adminPage); // Verify chart renders without performance issues const chartCanvas = adminPage.locator('[data-testid="chart-content"] canvas'); await expect(chartCanvas).toBeVisible(); // Verify page remains responsive await adminPage.click('[data-testid="metric-selector"]'); await adminPage.selectOption('[data-testid="metric-selector"]', 'satisfaction'); // Should update without significant delay await TestUtils.waitForStableNetwork(adminPage); await expect(chartCanvas).toBeVisible(); }); test('should be responsive across different viewport sizes', async ({ adminPage }) => { const viewports = [ { width: 1920, height: 1080 }, // Desktop { width: 1024, height: 768 }, // Tablet { width: 375, height: 667 } // Mobile ]; for (const viewport of viewports) { await adminPage.setViewportSize(viewport); await TestUtils.waitForStableNetwork(adminPage); // Verify dashboard is visible and functional const dashboard = adminPage.locator('[data-testid="predictive-analytics-dashboard"]'); await expect(dashboard).toBeVisible(); // Verify key components are accessible await expect(adminPage.locator('[data-testid="metric-selector"]')).toBeVisible(); await expect(adminPage.locator('[data-testid="kpi-cards"]')).toBeVisible(); await expect(adminPage.locator('[data-testid="chart-content"]')).toBeVisible(); // On mobile, some elements might be collapsed or reorganized if (viewport.width < 768) { // Mobile-specific checks const mobileMenu = adminPage.locator('[data-testid="mobile-menu"]'); if (await mobileMenu.isVisible()) { await expect(mobileMenu).toBeVisible(); } } } }); }); test.describe('@analytics @api API Integration and Data Accuracy', () => { test('should fetch predictive data from ML service', async ({ adminPage, mlContext }) => { // Test direct API call const response = await mlContext.get('/api/analytics/predictive?timeRange=30days&metric=satisfaction'); expect(response.ok()).toBeTruthy(); const data = await response.json(); expect(data).toHaveProperty('timeSeries'); expect(data).toHaveProperty('insights'); expect(data).toHaveProperty('riskFactors'); expect(data).toHaveProperty('recommendations'); expect(data).toHaveProperty('kpis'); // Verify time series data structure expect(data.timeSeries).toHaveProperty('labels'); expect(data.timeSeries).toHaveProperty('actualData'); expect(data.timeSeries).toHaveProperty('predictedData'); expect(data.timeSeries).toHaveProperty('confidenceUpper'); expect(data.timeSeries).toHaveProperty('confidenceLower'); // Verify data arrays have consistent length const { labels, actualData, predictedData } = data.timeSeries; expect(actualData.length).toBe(labels.length); expect(predictedData.length).toBe(labels.length); }); test('should handle API errors gracefully', async ({ adminPage }) => { // Simulate API error by intercepting requests await adminPage.route('**/api/analytics/predictive**', route => { route.fulfill({ status: 500, contentType: 'application/json', body: JSON.stringify({ error: 'Internal server error' }) }); }); // Reload dashboard to trigger API call await adminPage.reload(); // Verify error handling const errorMessage = adminPage.locator('[data-testid="error-message"], [data-testid="api-error"]'); if (await errorMessage.isVisible()) { await expect(errorMessage).toBeVisible(); } else { // Should show fallback data or graceful degradation const dashboard = adminPage.locator('[data-testid="predictive-analytics-dashboard"]'); await expect(dashboard).toBeVisible(); } }); test('should validate data consistency across metrics', async ({ mlContext }) => { const metrics = ['satisfaction', 'churn', 'response_time', 'resolution']; for (const metric of metrics) { const response = await mlContext.get(`/api/analytics/predictive?timeRange=30days&metric=${metric}`); expect(response.ok()).toBeTruthy(); const data = await response.json(); // Verify metric-specific data structure expect(data.kpis.current).toBeGreaterThanOrEqual(0); expect(data.kpis.confidence).toBeGreaterThanOrEqual(0); expect(data.kpis.confidence).toBeLessThanOrEqual(1); // Verify insights are relevant to metric if (data.insights.length > 0) { const insight = data.insights[0]; expect(insight).toHaveProperty('type'); expect(insight).toHaveProperty('confidence'); expect(['positive', 'warning', 'critical']).toContain(insight.type); } } }); }); test.describe('@analytics @accessibility Accessibility and Usability', () => { test('should be accessible with keyboard navigation', async ({ adminPage }) => { // Test tab navigation through dashboard elements await adminPage.keyboard.press('Tab'); await adminPage.keyboard.press('Tab'); await adminPage.keyboard.press('Tab'); // Verify focus is visible const focusedElement = adminPage.locator(':focus'); await expect(focusedElement).toBeVisible(); // Test metric selector with keyboard await adminPage.keyboard.press('Tab'); const metricSelector = adminPage.locator('[data-testid="metric-selector"]:focus'); if (await metricSelector.isVisible()) { await adminPage.keyboard.press('ArrowDown'); await adminPage.keyboard.press('Enter'); } }); test('should have proper ARIA labels and roles', async ({ adminPage }) => { // Check main dashboard ARIA attributes const dashboard = adminPage.locator('[data-testid="predictive-analytics-dashboard"]'); await expect(dashboard).toHaveAttribute('role', 'main'); // Check chart accessibility const chartContainer = adminPage.locator('[data-testid="chart-content"]'); await expect(chartContainer).toHaveAttribute('role', 'img'); await expect(chartContainer).toHaveAttribute('aria-label'); // Check KPI cards accessibility const kpiCards = adminPage.locator('[data-testid="kpi-card"]'); const firstKpiCard = kpiCards.first(); await expect(firstKpiCard).toHaveAttribute('role', 'region'); await expect(firstKpiCard).toHaveAttribute('aria-label'); // Check selectors accessibility const metricSelector = adminPage.locator('[data-testid="metric-selector"]'); await expect(metricSelector).toHaveAttribute('aria-label'); }); test('should support screen reader navigation', async ({ adminPage }) => { // Verify heading structure const headings = adminPage.locator('h1, h2, h3, h4, h5, h6'); const headingCount = await headings.count(); expect(headingCount).toBeGreaterThan(0); // Verify main heading const mainHeading = adminPage.locator('h2').first(); await expect(mainHeading).toContainText('Analyses Prédictives'); // Verify section headings const sectionHeadings = adminPage.locator('h4'); const sectionCount = await sectionHeadings.count(); expect(sectionCount).toBeGreaterThanOrEqual(3); // Insights, Risk Factors, Recommendations // Verify landmarks const landmarks = adminPage.locator('[role="main"], [role="region"], [role="navigation"]'); const landmarkCount = await landmarks.count(); expect(landmarkCount).toBeGreaterThan(0); }); }); });