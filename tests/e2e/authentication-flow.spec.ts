import { test, expect, Page } from '@playwright/test'; test.describe('Authentication Flow - E2E Testing', () => { let page: Page; test.beforeEach(async ({ browser }) => { page = await browser.newPage(); }); test.afterEach(async () => { await page.close(); }); test.describe('Login Process', () => { test('should display login form when not authenticated', async () => { await page.goto('/'); await page.waitForLoadState('networkidle'); // Check if login form is displayed const loginForm = page.locator('form'); const emailField = page.locator('input[name="email"], input[id="email"]'); const passwordField = page.locator('input[name="password"], input[id="password"]'); const loginButton = page.locator('button:has-text("Se connecter")'); // Verify login form elements are present if (await loginForm.isVisible()) { await expect(emailField).toBeVisible(); await expect(passwordField).toBeVisible(); await expect(loginButton).toBeVisible(); // Verify Free Mobile branding await expect(page.locator('text=Free Mobile')).toBeVisible(); } }); test('should authenticate with valid credentials', async () => { await page.goto('/'); await page.waitForLoadState('networkidle'); // Check if we need to login const loginButton = page.locator('button:has-text("Se connecter")'); if (await loginButton.isVisible()) { // Fill in credentials await page.fill('input[name="email"], input[id="email"]', '<EMAIL>'); await page.fill('input[name="password"], input[id="password"]', 'password'); // Click login button await loginButton.click(); await page.waitForLoadState('networkidle'); // Verify successful login - should redirect to dashboard const currentUrl = page.url(); expect(currentUrl).toContain('/dashboard'); // Verify dashboard elements are visible await expect(page.locator('text=Dashboard')).toBeVisible(); } }); test('should handle invalid credentials gracefully', async () => { await page.goto('/'); await page.waitForLoadState('networkidle'); const loginButton = page.locator('button:has-text("Se connecter")'); if (await loginButton.isVisible()) { // Fill in invalid credentials await page.fill('input[name="email"], input[id="email"]', '<EMAIL>'); await page.fill('input[name="password"], input[id="password"]', 'wrongpassword'); // Click login button await loginButton.click(); await page.waitForTimeout(2000); // Should show error message or remain on login page const errorMessage = page.locator('text=Erreur, text=Invalid, text=Incorrect'); const stillOnLogin = page.locator('button:has-text("Se connecter")'); // Either error message should be visible or still on login page const hasError = await errorMessage.isVisible(); const stillLogin = await stillOnLogin.isVisible(); expect(hasError || stillLogin).toBeTruthy(); } }); }); test.describe('Navigation After Authentication', () => { test.beforeEach(async () => { // Authenticate before each test await page.goto('/'); await page.waitForLoadState('networkidle'); const loginButton = page.locator('button:has-text("Se connecter")'); if (await loginButton.isVisible()) { await page.fill('input[name="email"], input[id="email"]', '<EMAIL>'); await page.fill('input[name="password"], input[id="password"]', 'password'); await loginButton.click(); await page.waitForLoadState('networkidle'); } }); test('should access dashboard after authentication', async () => { // Should be on dashboard const currentUrl = page.url(); expect(currentUrl).toContain('/dashboard'); // Verify dashboard navigation is available await expect(page.locator('text=Dashboard')).toBeVisible(); }); test('should access Administrator Panel after authentication', async () => { // Navigate to Administrator Panel await page.goto('/dashboard/administrator'); await page.waitForLoadState('networkidle'); // Verify Administrator Panel is accessible await expect(page.locator('h4:has-text("Panneau Administrateur")')).toBeVisible(); await expect(page.locator('[role="tab"]:has-text("Notifications")')).toBeVisible(); }); test('should maintain authentication across page refreshes', async () => { // Navigate to Administrator Panel await page.goto('/dashboard/administrator'); await page.waitForLoadState('networkidle'); // Refresh the page await page.reload(); await page.waitForLoadState('networkidle'); // Should still be authenticated and on the same page await expect(page.locator('h4:has-text("Panneau Administrateur")')).toBeVisible(); }); }); test.describe('Logout Process', () => { test.beforeEach(async () => { // Authenticate before each test await page.goto('/'); await page.waitForLoadState('networkidle'); const loginButton = page.locator('button:has-text("Se connecter")'); if (await loginButton.isVisible()) { await page.fill('input[name="email"], input[id="email"]', '<EMAIL>'); await page.fill('input[name="password"], input[id="password"]', 'password'); await loginButton.click(); await page.waitForLoadState('networkidle'); } }); test('should logout when logout button is clicked', async () => { // Look for logout button or user menu const logoutButton = page.locator('button:has-text("Déconnexion"), button:has-text("Logout"), button:has-text("Se déconnecter")'); const userMenu = page.locator('[aria-label="user menu"], [aria-label="account menu"]'); if (await logoutButton.isVisible()) { await logoutButton.click(); await page.waitForLoadState('networkidle'); // Should redirect to login page const loginForm = page.locator('button:has-text("Se connecter")'); await expect(loginForm).toBeVisible(); } else if (await userMenu.isVisible()) { await userMenu.click(); await page.waitForTimeout(500); const logoutOption = page.locator('text=Déconnexion, text=Logout, text=Se déconnecter'); if (await logoutOption.isVisible()) { await logoutOption.click(); await page.waitForLoadState('networkidle'); // Should redirect to login page const loginForm = page.locator('button:has-text("Se connecter")'); await expect(loginForm).toBeVisible(); } } }); }); test.describe('Protected Routes', () => { test('should redirect to login when accessing protected routes without authentication', async () => { // Clear any existing authentication await page.context().clearCookies(); await page.evaluate(() => localStorage.clear()); await page.evaluate(() => sessionStorage.clear()); // Try to access protected route directly await page.goto('/dashboard/administrator'); await page.waitForLoadState('networkidle'); // Should either redirect to login or show login form const currentUrl = page.url(); const loginButton = page.locator('button:has-text("Se connecter")'); const isOnLogin = currentUrl.includes('/login') || await loginButton.isVisible(); expect(isOnLogin).toBeTruthy(); }); test('should allow access to protected routes when authenticated', async () => { // Authenticate first await page.goto('/'); await page.waitForLoadState('networkidle'); const loginButton = page.locator('button:has-text("Se connecter")'); if (await loginButton.isVisible()) { await page.fill('input[name="email"], input[id="email"]', '<EMAIL>'); await page.fill('input[name="password"], input[id="password"]', 'password'); await loginButton.click(); await page.waitForLoadState('networkidle'); } // Now try to access protected route await page.goto('/dashboard/administrator'); await page.waitForLoadState('networkidle'); // Should be able to access the Administrator Panel await expect(page.locator('h4:has-text("Panneau Administrateur")')).toBeVisible(); }); }); test.describe('Session Management', () => { test('should handle session timeout gracefully', async () => { // Authenticate first await page.goto('/'); await page.waitForLoadState('networkidle'); const loginButton = page.locator('button:has-text("Se connecter")'); if (await loginButton.isVisible()) { await page.fill('input[name="email"], input[id="email"]', '<EMAIL>'); await page.fill('input[name="password"], input[id="password"]', 'password'); await loginButton.click(); await page.waitForLoadState('networkidle'); } // Clear session storage to simulate session timeout await page.evaluate(() => { localStorage.removeItem('free_mobile_auth_token'); sessionStorage.clear(); }); // Try to access protected route await page.goto('/dashboard/administrator'); await page.waitForLoadState('networkidle'); // Should handle session timeout (either redirect to login or show login form) const currentUrl = page.url(); const loginForm = page.locator('button:has-text("Se connecter")'); const handledTimeout = currentUrl.includes('/login') || await loginForm.isVisible(); expect(handledTimeout).toBeTruthy(); }); }); test.describe('Form Validation', () => { test('should validate email format', async () => { await page.goto('/'); await page.waitForLoadState('networkidle'); const loginButton = page.locator('button:has-text("Se connecter")'); if (await loginButton.isVisible()) { // Enter invalid email format await page.fill('input[name="email"], input[id="email"]', 'invalid-email'); await page.fill('input[name="password"], input[id="password"]', 'password'); // Try to submit await loginButton.click(); await page.waitForTimeout(1000); // Should show validation error or prevent submission const emailField = page.locator('input[name="email"], input[id="email"]'); const isInvalid = await emailField.evaluate((el: HTMLInputElement) => !el.validity.valid); expect(isInvalid).toBeTruthy(); } }); test('should require both email and password', async () => { await page.goto('/'); await page.waitForLoadState('networkidle'); const loginButton = page.locator('button:has-text("Se connecter")'); if (await loginButton.isVisible()) { // Try to submit with empty fields await loginButton.click(); await page.waitForTimeout(1000); // Should not proceed with empty fields const stillOnLogin = await loginButton.isVisible(); expect(stillOnLogin).toBeTruthy(); } }); }); });