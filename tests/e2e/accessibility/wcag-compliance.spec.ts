/** * ============================================= * ACCESSIBILITY COMPLIANCE TESTS * Comprehensive WCAG 2.1 AA compliance testing * ============================================= */ import { test, expect, TestUtils } from '../../fixtures/test-fixtures'; import { testConfig } from '../../playwright.config'; import AxeBuilder from '@axe-core/playwright'; test.describe('WCAG 2.1 AA Accessibility Compliance', () => { test.describe('@accessibility @smoke Automated Accessibility Scanning', () => { test('should pass axe-core accessibility scan on dashboard', async ({ authenticatedPage }) => { await authenticatedPage.goto('/dashboard'); await TestUtils.waitForStableNetwork(authenticatedPage); const accessibilityScanResults = await new AxeBuilder({ page: authenticatedPage }) .withTags(testConfig.accessibility.tags) .analyze(); expect(accessibilityScanResults.violations).toEqual([]); // Log accessibility score const passedRules = accessibilityScanResults.passes.length; const totalRules = passedRules + accessibilityScanResults.violations.length; const score = (passedRules / totalRules) * 100; console.log(`Dashboard accessibility score: ${score.toFixed(1)}%`); }); test('should pass accessibility scan on conversations page', async ({ authenticatedPage }) => { await authenticatedPage.goto('/dashboard/conversations'); await TestUtils.waitForStableNetwork(authenticatedPage); const accessibilityScanResults = await new AxeBuilder({ page: authenticatedPage }) .withTags(testConfig.accessibility.tags) .analyze(); expect(accessibilityScanResults.violations).toEqual([]); }); test('should pass accessibility scan on analytics dashboard', async ({ adminPage }) => { await adminPage.goto('/dashboard/analytics/predictive'); await TestUtils.waitForStableNetwork(adminPage); const accessibilityScanResults = await new AxeBuilder({ page: adminPage }) .withTags(testConfig.accessibility.tags) .analyze(); expect(accessibilityScanResults.violations).toEqual([]); }); test('should pass accessibility scan on login page', async ({ page }) => { await page.goto('/login'); await TestUtils.waitForStableNetwork(page); const accessibilityScanResults = await new AxeBuilder({ page }) .withTags(testConfig.accessibility.tags) .analyze(); expect(accessibilityScanResults.violations).toEqual([]); }); }); test.describe('@accessibility @regression Keyboard Navigation', () => { test('should support full keyboard navigation on dashboard', async ({ authenticatedPage }) => { await authenticatedPage.goto('/dashboard'); await TestUtils.waitForStableNetwork(authenticatedPage); // Start keyboard navigation await authenticatedPage.keyboard.press('Tab'); // Track focusable elements const focusableElements = []; for (let i = 0; i < 20; i++) { const focusedElement = await authenticatedPage.locator(':focus').first(); if (await focusedElement.isVisible()) { const tagName = await focusedElement.evaluate(el => el.tagName.toLowerCase()); const testId = await focusedElement.getAttribute('data-testid'); const ariaLabel = await focusedElement.getAttribute('aria-label'); focusableElements.push({ tagName, testId, ariaLabel, index: i }); // Verify focus is visible const focusStyles = await focusedElement.evaluate(el => { const styles = window.getComputedStyle(el); return { outline: styles.outline, outlineWidth: styles.outlineWidth, boxShadow: styles.boxShadow }; }); // Should have visible focus indicator const hasFocusIndicator = focusStyles.outline !== 'none' || focusStyles.outlineWidth !== '0px' || focusStyles.boxShadow !== 'none'; expect(hasFocusIndicator).toBeTruthy(); } await authenticatedPage.keyboard.press('Tab'); } // Should have navigated through multiple elements expect(focusableElements.length).toBeGreaterThan(5); console.log(`Keyboard navigable elements: ${focusableElements.length}`); }); test('should support keyboard navigation in conversations list', async ({ authenticatedPage }) => { await authenticatedPage.goto('/dashboard/conversations'); await TestUtils.waitForStableNetwork(authenticatedPage); // Navigate to conversations list await authenticatedPage.keyboard.press('Tab'); await authenticatedPage.keyboard.press('Tab'); // Find conversation items const conversationItems = authenticatedPage.locator('[data-testid="conversation-item"]'); const itemCount = await conversationItems.count(); if (itemCount > 0) { // Navigate through conversation items with arrow keys await authenticatedPage.keyboard.press('ArrowDown'); await authenticatedPage.keyboard.press('ArrowDown'); await authenticatedPage.keyboard.press('ArrowUp'); // Select conversation with Enter await authenticatedPage.keyboard.press('Enter'); // Should open conversation details await TestUtils.waitForElement(authenticatedPage, '[data-testid="conversation-details"]'); await expect(authenticatedPage.locator('[data-testid="conversation-details"]')).toBeVisible(); } }); test('should support keyboard navigation in forms', async ({ page }) => { await page.goto('/login'); // Navigate through form elements await page.keyboard.press('Tab'); // Email field await page.type('[data-testid="email-input"]', '<EMAIL>'); await page.keyboard.press('Tab'); // Password field await page.type('[data-testid="password-input"]', 'password123'); await page.keyboard.press('Tab'); // Submit button // Verify focus is on submit button const focusedElement = page.locator(':focus'); const buttonText = await focusedElement.textContent(); expect(buttonText?.toLowerCase()).toContain('connexion'); // Should be able to submit with Enter await page.keyboard.press('Enter'); }); test('should support skip links for main content', async ({ authenticatedPage }) => { await authenticatedPage.goto('/dashboard'); // Press Tab to focus first element (should be skip link) await authenticatedPage.keyboard.press('Tab'); const firstFocusedElement = authenticatedPage.locator(':focus'); const skipLinkText = await firstFocusedElement.textContent(); if (skipLinkText?.toLowerCase().includes('contenu principal')) { // Activate skip link await authenticatedPage.keyboard.press('Enter'); // Should focus main content const mainContent = authenticatedPage.locator('[role="main"], main, [data-testid="main-content"]'); await expect(mainContent).toBeFocused(); } }); }); test.describe('@accessibility @aria ARIA Labels and Roles', () => { test('should have proper ARIA labels on interactive elements', async ({ authenticatedPage }) => { await authenticatedPage.goto('/dashboard'); await TestUtils.waitForStableNetwork(authenticatedPage); // Check buttons have ARIA labels const buttons = authenticatedPage.locator('button'); const buttonCount = await buttons.count(); for (let i = 0; i < buttonCount; i++) { const button = buttons.nth(i); if (await button.isVisible()) { const ariaLabel = await button.getAttribute('aria-label'); const ariaLabelledBy = await button.getAttribute('aria-labelledby'); const textContent = await button.textContent(); // Button should have accessible name const hasAccessibleName = (ariaLabel && ariaLabel.trim().length > 0) || (ariaLabelledBy && ariaLabelledBy.trim().length > 0) || (textContent && textContent.trim().length > 0); expect(hasAccessibleName).toBeTruthy(); } } }); test('should have proper landmark roles', async ({ authenticatedPage }) => { await authenticatedPage.goto('/dashboard'); await TestUtils.waitForStableNetwork(authenticatedPage); // Check for main landmark const mainLandmark = authenticatedPage.locator('[role="main"], main'); await expect(mainLandmark).toBeVisible(); // Check for navigation landmark const navLandmark = authenticatedPage.locator('[role="navigation"], nav'); expect(await navLandmark.count()).toBeGreaterThan(0); // Check for banner/header const bannerLandmark = authenticatedPage.locator('[role="banner"], header'); expect(await bannerLandmark.count()).toBeGreaterThan(0); }); test('should have proper heading hierarchy', async ({ authenticatedPage }) => { await authenticatedPage.goto('/dashboard'); await TestUtils.waitForStableNetwork(authenticatedPage); // Get all headings const headings = await authenticatedPage.locator('h1, h2, h3, h4, h5, h6').all(); const headingLevels = []; for (const heading of headings) { if (await heading.isVisible()) { const tagName = await heading.evaluate(el => el.tagName.toLowerCase()); const level = parseInt(tagName.charAt(1)); headingLevels.push(level); } } // Should start with h1 or h2 expect(headingLevels[0]).toBeLessThanOrEqual(2); // Check heading hierarchy (no skipping levels) for (let i = 1; i < headingLevels.length; i++) { const currentLevel = headingLevels[i]; const previousLevel = headingLevels[i - 1]; // Should not skip more than one level expect(currentLevel - previousLevel).toBeLessThanOrEqual(1); } }); test('should have proper form labels', async ({ page }) => { await page.goto('/login'); // Check form inputs have labels const inputs = page.locator('input[type="email"], input[type="password"], input[type="text"]'); const inputCount = await inputs.count(); for (let i = 0; i < inputCount; i++) { const input = inputs.nth(i); if (await input.isVisible()) { const ariaLabel = await input.getAttribute('aria-label'); const ariaLabelledBy = await input.getAttribute('aria-labelledby'); const id = await input.getAttribute('id'); // Check for associated label let hasLabel = false; if (ariaLabel || ariaLabelledBy) { hasLabel = true; } else if (id) { const associatedLabel = page.locator(`label[for="${id}"]`); hasLabel = await associatedLabel.count() > 0; } expect(hasLabel).toBeTruthy(); } } }); test('should have proper table accessibility', async ({ authenticatedPage }) => { await authenticatedPage.goto('/dashboard/conversations'); await TestUtils.waitForStableNetwork(authenticatedPage); const tables = authenticatedPage.locator('table'); const tableCount = await tables.count(); for (let i = 0; i < tableCount; i++) { const table = tables.nth(i); if (await table.isVisible()) { // Check for table caption or aria-label const caption = table.locator('caption'); const ariaLabel = await table.getAttribute('aria-label'); const ariaLabelledBy = await table.getAttribute('aria-labelledby'); const hasAccessibleName = await caption.count() > 0 || (ariaLabel && ariaLabel.trim().length > 0) || (ariaLabelledBy && ariaLabelledBy.trim().length > 0); expect(hasAccessibleName).toBeTruthy(); // Check for table headers const headers = table.locator('th'); const headerCount = await headers.count(); if (headerCount > 0) { // Headers should have scope attribute for (let j = 0; j < headerCount; j++) { const header = headers.nth(j); const scope = await header.getAttribute('scope'); expect(scope).toMatch(/col|row|colgroup|rowgroup/); } } } } }); }); test.describe('@accessibility @visual Visual Accessibility', () => { test('should have sufficient color contrast', async ({ authenticatedPage }) => { await authenticatedPage.goto('/dashboard'); await TestUtils.waitForStableNetwork(authenticatedPage); // Use axe-core to check color contrast const accessibilityScanResults = await new AxeBuilder({ page: authenticatedPage }) .withTags(['wcag2aa']) .withRules(['color-contrast']) .analyze(); // Should have no color contrast violations const contrastViolations = accessibilityScanResults.violations.filter( violation => violation.id === 'color-contrast' ); expect(contrastViolations).toEqual([]); }); test('should be usable when zoomed to 200%', async ({ page }) => { await page.goto('/dashboard'); await TestUtils.waitForStableNetwork(page); // Zoom to 200% await page.evaluate(() => { document.body.style.zoom = '2'; }); await page.waitForTimeout(1000); // Verify main elements are still visible and functional await expect(page.locator('[data-testid="user-menu"]')).toBeVisible(); await expect(page.locator('[data-testid="navigation-menu"]')).toBeVisible(); await expect(page.locator('[data-testid="main-content"]')).toBeVisible(); // Should be able to interact with elements await page.click('[data-testid="conversations-tab"]'); await TestUtils.waitForStableNetwork(page); // Reset zoom await page.evaluate(() => { document.body.style.zoom = '1'; }); }); test('should support high contrast mode', async ({ page }) => { await page.goto('/dashboard'); // Simulate high contrast mode await page.addStyleTag({ content: ` @media (prefers-contrast: high) { * { background: black !important; color: white !important; border-color: white !important; } } ` }); await page.emulateMedia({ colorScheme: 'dark', reducedMotion: 'reduce' }); await TestUtils.waitForStableNetwork(page); // Verify elements are still visible await expect(page.locator('[data-testid="user-menu"]')).toBeVisible(); await expect(page.locator('[data-testid="navigation-menu"]')).toBeVisible(); }); test('should respect reduced motion preferences', async ({ page }) => { await page.emulateMedia({ reducedMotion: 'reduce' }); await page.goto('/dashboard'); await TestUtils.waitForStableNetwork(page); // Check that animations are disabled or reduced const animatedElements = page.locator('[class*="animate"], [class*="transition"]'); const elementCount = await animatedElements.count(); for (let i = 0; i < elementCount; i++) { const element = animatedElements.nth(i); if (await element.isVisible()) { const styles = await element.evaluate(el => { const computedStyles = window.getComputedStyle(el); return { animationDuration: computedStyles.animationDuration, transitionDuration: computedStyles.transitionDuration }; }); // Animations should be disabled or very short expect(styles.animationDuration).toMatch(/0s|none/); expect(styles.transitionDuration).toMatch(/0s|none/); } } }); }); test.describe('@accessibility @screen-reader Screen Reader Support', () => { test('should have proper live regions for dynamic content', async ({ authenticatedPage }) => { await authenticatedPage.goto('/dashboard/conversations'); await TestUtils.waitForStableNetwork(authenticatedPage); // Check for ARIA live regions const liveRegions = authenticatedPage.locator('[aria-live], [role="status"], [role="alert"]'); const liveRegionCount = await liveRegions.count(); // Should have live regions for dynamic updates expect(liveRegionCount).toBeGreaterThan(0); // Check live region properties for (let i = 0; i < liveRegionCount; i++) { const liveRegion = liveRegions.nth(i); if (await liveRegion.isVisible()) { const ariaLive = await liveRegion.getAttribute('aria-live'); const role = await liveRegion.getAttribute('role'); if (ariaLive) { expect(ariaLive).toMatch(/polite|assertive/); } if (role) { expect(role).toMatch(/status|alert|log/); } } } }); test('should announce page changes to screen readers', async ({ authenticatedPage }) => { await authenticatedPage.goto('/dashboard'); await TestUtils.waitForStableNetwork(authenticatedPage); // Navigate to different page await authenticatedPage.click('[data-testid="conversations-tab"]'); await TestUtils.waitForStableNetwork(authenticatedPage); // Check for page title update const pageTitle = await authenticatedPage.title(); expect(pageTitle).toContain('Conversations'); // Check for focus management const focusedElement = authenticatedPage.locator(':focus'); const isMainContentFocused = await focusedElement.evaluate(el => { return el.matches('[role="main"], main, h1, h2') || el.closest('[role="main"], main'); }); // Focus should be managed on page change expect(isMainContentFocused).toBeTruthy(); }); test('should provide proper error announcements', async ({ page }) => { await page.goto('/login'); // Submit form with invalid data await page.fill('[data-testid="email-input"]', 'invalid-email'); await page.fill('[data-testid="password-input"]', ''); await page.click('[data-testid="login-button"]'); // Check for error announcement const errorMessage = page.locator('[role="alert"], [aria-live="assertive"]'); if (await errorMessage.count() > 0) { await expect(errorMessage.first()).toBeVisible(); const errorText = await errorMessage.first().textContent(); expect(errorText?.length).toBeGreaterThan(0); } }); test('should provide proper loading state announcements', async ({ authenticatedPage }) => { await authenticatedPage.goto('/dashboard/analytics/predictive'); // Check for loading announcements const loadingIndicators = authenticatedPage.locator('[aria-live], [role="status"]'); const loadingCount = await loadingIndicators.count(); if (loadingCount > 0) { for (let i = 0; i < loadingCount; i++) { const indicator = loadingIndicators.nth(i); if (await indicator.isVisible()) { const ariaLabel = await indicator.getAttribute('aria-label'); const textContent = await indicator.textContent(); // Should have meaningful loading text const hasLoadingText = (ariaLabel && ariaLabel.toLowerCase().includes('chargement')) || (textContent && textContent.toLowerCase().includes('chargement')); expect(hasLoadingText).toBeTruthy(); } } } }); }); test.describe('@accessibility @mobile Mobile Accessibility', () => { test('should be accessible on mobile devices', async ({ page }) => { // Emulate mobile device await page.setViewportSize({ width: 375, height: 667 }); await page.goto('/dashboard'); await TestUtils.waitForStableNetwork(page); // Run accessibility scan on mobile const accessibilityScanResults = await new AxeBuilder({ page }) .withTags(testConfig.accessibility.tags) .analyze(); expect(accessibilityScanResults.violations).toEqual([]); }); test('should have proper touch target sizes', async ({ page }) => { await page.setViewportSize({ width: 375, height: 667 }); await page.goto('/dashboard'); await TestUtils.waitForStableNetwork(page); // Check touch target sizes const interactiveElements = page.locator('button, a, input, [role="button"]'); const elementCount = await interactiveElements.count(); for (let i = 0; i < Math.min(elementCount, 10); i++) { const element = interactiveElements.nth(i); if (await element.isVisible()) { const boundingBox = await element.boundingBox(); if (boundingBox) { // Touch targets should be at least 44x44 pixels expect(boundingBox.width).toBeGreaterThanOrEqual(44); expect(boundingBox.height).toBeGreaterThanOrEqual(44); } } } }); test('should support mobile screen reader gestures', async ({ page }) => { await page.setViewportSize({ width: 375, height: 667 }); await page.goto('/dashboard/conversations'); await TestUtils.waitForStableNetwork(page); // Simulate swipe gestures for navigation const conversationsList = page.locator('[data-testid="conversations-list"]'); if (await conversationsList.isVisible()) { // Should be navigable with swipe gestures await conversationsList.swipeLeft(); await page.waitForTimeout(500); await conversationsList.swipeRight(); await page.waitForTimeout(500); // List should still be functional await expect(conversationsList).toBeVisible(); } }); }); });