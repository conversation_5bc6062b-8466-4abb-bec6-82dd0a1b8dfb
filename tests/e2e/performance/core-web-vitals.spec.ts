/** * ============================================= * [PERFORMANCE] CORE WEB VITALS PERFORMANCE TESTS * Comprehensive testing of performance metrics and optimization * ============================================= */ import { test, expect, TestUtils } from '../../fixtures/test-fixtures'; import { testConfig } from '../../playwright.config'; test.describe('Core Web Vitals Performance', () => { test.describe('@performance @smoke Core Web Vitals Metrics', () => { test('should meet Largest Contentful Paint (LCP) thresholds', async ({ page }) => { // Navigate to main dashboard await page.goto('/dashboard'); // Measure LCP const lcpMetric = await page.evaluate(() => { return new Promise((resolve) => { new PerformanceObserver((list) => { const entries = list.getEntries(); const lastEntry = entries[entries.length - 1]; resolve(lastEntry.startTime); }).observe({ entryTypes: ['largest-contentful-paint'] }); // Fallback timeout setTimeout(() => resolve(null), 10000); }); }); if (lcpMetric) { // LCP should be under 2.5 seconds for good performance expect(lcpMetric).toBeLessThan(testConfig.performance.lcp); console.log(`LCP: ${lcpMetric}ms`); } }); test('should meet First Input Delay (FID) thresholds', async ({ page }) => { await page.goto('/dashboard'); await TestUtils.waitForStableNetwork(page); // Simulate user interaction and measure FID const fidMetric = await page.evaluate(() => { return new Promise((resolve) => { let fidValue = null; new PerformanceObserver((list) => { const entries = list.getEntries(); entries.forEach((entry) => { if (entry.name === 'first-input') { fidValue = entry.processingStart - entry.startTime; resolve(fidValue); } }); }).observe({ entryTypes: ['first-input'] }); // Trigger a click to measure FID const button = document.querySelector('[data-testid="conversations-tab"]'); if (button) { button.click(); } // Fallback timeout setTimeout(() => resolve(fidValue), 5000); }); }); if (fidMetric) { // FID should be under 100ms for good performance expect(fidMetric).toBeLessThan(testConfig.performance.fid); console.log(`FID: ${fidMetric}ms`); } }); test('should meet Cumulative Layout Shift (CLS) thresholds', async ({ page }) => { await page.goto('/dashboard'); // Measure CLS const clsMetric = await page.evaluate(() => { return new Promise((resolve) => { let clsValue = 0; new PerformanceObserver((list) => { const entries = list.getEntries(); entries.forEach((entry) => { if (!entry.hadRecentInput) { clsValue += entry.value; } }); }).observe({ entryTypes: ['layout-shift'] }); // Wait for page to stabilize setTimeout(() => resolve(clsValue), 5000); }); }); // CLS should be under 0.1 for good performance expect(clsMetric).toBeLessThan(testConfig.performance.cls); console.log(`CLS: ${clsMetric}`); }); test('should meet Time to Interactive (TTI) thresholds', async ({ page }) => { const startTime = Date.now(); await page.goto('/dashboard'); // Wait for page to be fully interactive await page.waitForLoadState('networkidle'); await page.waitForFunction(() => { // Check if main interactive elements are ready const conversationsTab = document.querySelector('[data-testid="conversations-tab"]'); const userMenu = document.querySelector('[data-testid="user-menu"]'); return conversationsTab && userMenu; }); const ttiTime = Date.now() - startTime; // TTI should be under 3.8 seconds for good performance expect(ttiTime).toBeLessThan(testConfig.performance.tti); console.log(`TTI: ${ttiTime}ms`); }); }); test.describe('@performance @regression Page Load Performance', () => { test('should load dashboard within acceptable time', async ({ page }) => { const startTime = Date.now(); await page.goto('/dashboard'); await TestUtils.waitForElement(page, '[data-testid="dashboard-content"]'); const loadTime = Date.now() - startTime; // Dashboard should load within 3 seconds expect(loadTime).toBeLessThan(3000); console.log(`Dashboard load time: ${loadTime}ms`); }); test('should load conversations page efficiently', async ({ authenticatedPage }) => { const startTime = Date.now(); await authenticatedPage.goto('/dashboard/conversations'); await TestUtils.waitForElement(authenticatedPage, '[data-testid="conversations-list"]'); const loadTime = Date.now() - startTime; // Conversations page should load within 2 seconds expect(loadTime).toBeLessThan(2000); console.log(`Conversations page load time: ${loadTime}ms`); }); test('should load analytics dashboard efficiently', async ({ adminPage }) => { const startTime = Date.now(); await adminPage.goto('/dashboard/analytics/predictive'); await TestUtils.waitForElement(adminPage, '[data-testid="predictive-analytics-dashboard"]'); const loadTime = Date.now() - startTime; // Analytics dashboard should load within 4 seconds (more complex) expect(loadTime).toBeLessThan(4000); console.log(`Analytics dashboard load time: ${loadTime}ms`); }); test('should handle navigation between pages efficiently', async ({ authenticatedPage }) => { // Start at dashboard await authenticatedPage.goto('/dashboard'); await TestUtils.waitForStableNetwork(authenticatedPage); const navigationTimes = []; const pages = [ { route: '/dashboard/conversations', testId: 'conversations-list' }, { route: '/dashboard/analytics', testId: 'analytics-dashboard' }, { route: '/dashboard', testId: 'dashboard-content' } ]; for (const pageInfo of pages) { const startTime = Date.now(); await authenticatedPage.goto(pageInfo.route); await TestUtils.waitForElement(authenticatedPage, `[data-testid="${pageInfo.testId}"]`); const navigationTime = Date.now() - startTime; navigationTimes.push(navigationTime); // Each navigation should be under 2 seconds expect(navigationTime).toBeLessThan(2000); } const averageNavigationTime = navigationTimes.reduce((a, b) => a + b, 0) / navigationTimes.length; console.log(`Average navigation time: ${averageNavigationTime}ms`); }); }); test.describe('@performance @api API Performance', () => { test('should respond to health checks quickly', async ({ apiContext }) => { const startTime = Date.now(); const response = await apiContext.get('/health'); const responseTime = Date.now() - startTime; expect(response.ok()).toBeTruthy(); expect(responseTime).toBeLessThan(500); // Health check should be under 500ms console.log(`Health check response time: ${responseTime}ms`); }); test('should handle conversation API calls efficiently', async ({ apiContext }) => { const startTime = Date.now(); const response = await apiContext.get('/api/conversations?limit=20'); const responseTime = Date.now() - startTime; expect(response.ok()).toBeTruthy(); expect(responseTime).toBeLessThan(1000); // Conversations API should be under 1 second console.log(`Conversations API response time: ${responseTime}ms`); }); test('should handle ML service calls within acceptable time', async ({ mlContext }) => { const mlEndpoints = [ '/api/ml/suggestions/generate', '/api/ml/auto-response/generate', '/api/ml/routing/route-conversation', '/api/ml/sentiment/analyze-escalation' ]; for (const endpoint of mlEndpoints) { const startTime = Date.now(); const response = await mlContext.post(endpoint, { data: { conversation_id: 'test-conv-performance', customer_message: 'Performance test message', platform: 'whatsapp', customer_id: 'test-customer-1' } }); const responseTime = Date.now() - startTime; expect(response.ok()).toBeTruthy(); expect(responseTime).toBeLessThan(2000); // ML services should be under 2 seconds console.log(`${endpoint} response time: ${responseTime}ms`); } }); test('should handle concurrent API requests efficiently', async ({ apiContext }) => { const concurrentRequests = 10; const promises = []; const startTime = Date.now(); for (let i = 0; i < concurrentRequests; i++) { promises.push(apiContext.get('/api/conversations?limit=5')); } const responses = await Promise.all(promises); const totalTime = Date.now() - startTime; // All requests should succeed for (const response of responses) { expect(response.ok()).toBeTruthy(); } // Concurrent requests should complete within reasonable time expect(totalTime).toBeLessThan(5000); console.log(`${concurrentRequests} concurrent requests completed in: ${totalTime}ms`); }); }); test.describe('@performance @memory Memory and Resource Usage', () => { test('should not have memory leaks during navigation', async ({ page }) => { // Get initial memory usage const initialMemory = await page.evaluate(() => { return (performance as any).memory ? (performance as any).memory.usedJSHeapSize : 0; }); // Navigate through multiple pages const routes = [ '/dashboard', '/dashboard/conversations', '/dashboard/analytics', '/dashboard/admin', '/dashboard' ]; for (const route of routes) { await page.goto(route); await TestUtils.waitForStableNetwork(page); // Force garbage collection if available await page.evaluate(() => { if ((window as any).gc) { (window as any).gc(); } }); } // Get final memory usage const finalMemory = await page.evaluate(() => { return (performance as any).memory ? (performance as any).memory.usedJSHeapSize : 0; }); if (initialMemory > 0 && finalMemory > 0) { const memoryIncrease = finalMemory - initialMemory; const memoryIncreasePercent = (memoryIncrease / initialMemory) * 100; // Memory increase should be reasonable (less than 50% increase) expect(memoryIncreasePercent).toBeLessThan(50); console.log(`Memory increase: ${memoryIncreasePercent.toFixed(2)}%`); } }); test('should handle large datasets efficiently', async ({ adminPage }) => { // Navigate to analytics with large dataset await adminPage.goto('/dashboard/analytics/predictive'); await adminPage.selectOption('[data-testid="time-range-selector"]', '365days'); await TestUtils.waitForStableNetwork(adminPage); // Measure performance after loading large dataset const performanceMetrics = await adminPage.evaluate(() => { const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming; return { domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart, loadComplete: navigation.loadEventEnd - navigation.loadEventStart, memoryUsage: (performance as any).memory ? (performance as any).memory.usedJSHeapSize : 0 }; }); // DOM content should load quickly even with large datasets expect(performanceMetrics.domContentLoaded).toBeLessThan(2000); console.log(`Large dataset DOM load time: ${performanceMetrics.domContentLoaded}ms`); }); test('should optimize image and asset loading', async ({ page }) => { await page.goto('/dashboard'); // Check for lazy loading implementation const images = await page.locator('img').all(); for (const image of images) { const loading = await image.getAttribute('loading'); const src = await image.getAttribute('src'); // Images should use lazy loading or be optimized if (src && !src.includes('data:')) { expect(loading).toBe('lazy'); } } // Check for optimized asset delivery const resourceEntries = await page.evaluate(() => { return performance.getEntriesByType('resource').map((entry: any) => ({ name: entry.name, transferSize: entry.transferSize, encodedBodySize: entry.encodedBodySize, decodedBodySize: entry.decodedBodySize })); }); // Check compression ratios for text assets const textAssets = resourceEntries.filter(entry => entry.name.includes('.js') || entry.name.includes('.css') ); for (const asset of textAssets) { if (asset.transferSize > 0 && asset.decodedBodySize > 0) { const compressionRatio = asset.transferSize / asset.decodedBodySize; // Assets should be compressed (ratio < 0.8) expect(compressionRatio).toBeLessThan(0.8); } } }); }); test.describe('@performance @mobile Mobile Performance', () => { test('should perform well on mobile devices', async ({ page }) => { // Emulate mobile device await page.emulate({ userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15', viewport: { width: 375, height: 667 }, deviceScaleFactor: 2, isMobile: true, hasTouch: true }); const startTime = Date.now(); await page.goto('/dashboard'); await TestUtils.waitForElement(page, '[data-testid="dashboard-content"]'); const mobileLoadTime = Date.now() - startTime; // Mobile load time should be reasonable (under 4 seconds) expect(mobileLoadTime).toBeLessThan(4000); console.log(`Mobile load time: ${mobileLoadTime}ms`); }); test('should handle touch interactions efficiently', async ({ page }) => { await page.emulate({ viewport: { width: 375, height: 667 }, isMobile: true, hasTouch: true }); await page.goto('/dashboard/conversations'); await TestUtils.waitForStableNetwork(page); // Test touch interactions const conversationItem = page.locator('[data-testid="conversation-item"]').first(); if (await conversationItem.isVisible()) { const startTime = Date.now(); await conversationItem.tap(); await TestUtils.waitForElement(page, '[data-testid="conversation-details"]'); const touchResponseTime = Date.now() - startTime; // Touch response should be immediate (under 300ms) expect(touchResponseTime).toBeLessThan(300); console.log(`Touch response time: ${touchResponseTime}ms`); } }); test('should optimize for slow network conditions', async ({ page }) => { // Simulate slow 3G network await page.route('**/*', async route => { await new Promise(resolve => setTimeout(resolve, 100)); // Add 100ms delay route.continue(); }); const startTime = Date.now(); await page.goto('/dashboard'); await TestUtils.waitForElement(page, '[data-testid="dashboard-content"]'); const slowNetworkLoadTime = Date.now() - startTime; // Should still load within reasonable time on slow network (under 8 seconds) expect(slowNetworkLoadTime).toBeLessThan(8000); console.log(`Slow network load time: ${slowNetworkLoadTime}ms`); }); }); test.describe('@performance @optimization Performance Optimizations', () => { test('should implement service worker caching', async ({ page }) => { await page.goto('/dashboard'); // Check if service worker is registered const serviceWorkerRegistered = await page.evaluate(() => { return 'serviceWorker' in navigator && navigator.serviceWorker.controller !== null; }); if (serviceWorkerRegistered) { // Test cache effectiveness await page.reload(); const cachedResources = await page.evaluate(() => { return performance.getEntriesByType('resource').filter((entry: any) => entry.transferSize === 0 && entry.decodedBodySize > 0 ).length; }); // Should have some cached resources expect(cachedResources).toBeGreaterThan(0); console.log(`Cached resources: ${cachedResources}`); } }); test('should use efficient bundling and code splitting', async ({ page }) => { await page.goto('/dashboard'); const resourceEntries = await page.evaluate(() => { return performance.getEntriesByType('resource') .filter((entry: any) => entry.name.includes('.js')) .map((entry: any) => ({ name: entry.name, size: entry.transferSize })); }); // Check for reasonable bundle sizes for (const resource of resourceEntries) { // Individual JS bundles should not be too large (under 1MB) expect(resource.size).toBeLessThan(1024 * 1024); } // Should have multiple smaller bundles rather than one large bundle expect(resourceEntries.length).toBeGreaterThan(1); console.log(`JavaScript bundles: ${resourceEntries.length}`); }); test('should implement efficient data fetching', async ({ authenticatedPage }) => { await authenticatedPage.goto('/dashboard/conversations'); // Monitor network requests const networkRequests = []; authenticatedPage.on('request', request => { if (request.url().includes('/api/')) { networkRequests.push({ url: request.url(), method: request.method(), timestamp: Date.now() }); } }); await TestUtils.waitForStableNetwork(authenticatedPage); // Should not make excessive API calls expect(networkRequests.length).toBeLessThan(10); // Should not make duplicate requests const uniqueRequests = new Set(networkRequests.map(req => `${req.method}:${req.url}`)); expect(uniqueRequests.size).toBe(networkRequests.length); console.log(`API requests made: ${networkRequests.length}`); }); }); });