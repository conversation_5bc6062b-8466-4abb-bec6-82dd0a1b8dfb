const { test, expect } = require('@playwright/test'); const TestHelpers = require('../utils/test-helpers'); test.describe('Authentication Tests', () => { let helpers; let testUsers; test.beforeEach(async ({ page }) => { helpers = new TestHelpers(page); // Load test users from fixtures const fs = require('fs'); const testData = JSON.parse(fs.readFileSync('./tests/fixtures/test-data.json', 'utf8')); testUsers = testData.users; // Clear session before each test await helpers.clearSession(); }); test.describe('Login Functionality', () => { test('should login successfully with valid credentials', async ({ page }) => { await page.goto('/login'); // Wait for login form await expect(page.locator('[data-testid="login-form"]')).toBeVisible(); // Fill login form await page.fill('[data-testid="email-input"]', testUsers.user.email); await page.fill('[data-testid="password-input"]', testUsers.user.password); // Submit form await page.click('[data-testid="login-button"]'); // Verify successful login await expect(page).toHaveURL(/.*dashboard/); await expect(page.locator('[data-testid="user-menu"]')).toBeVisible(); // Verify user info is displayed const userInfo = page.locator('[data-testid="user-info"]'); await expect(userInfo).toContainText(testUsers.user.firstName); }); test('should fail login with invalid email', async ({ page }) => { await page.goto('/login'); // Fill with invalid email await page.fill('[data-testid="email-input"]', '<EMAIL>'); await page.fill('[data-testid="password-input"]', 'password123'); // Submit form await page.click('[data-testid="login-button"]'); // Verify error message await expect(page.locator('[data-testid="error-message"]')).toBeVisible(); await expect(page.locator('[data-testid="error-message"]')).toContainText(/invalid.*credentials/i); // Verify still on login page await expect(page).toHaveURL(/.*login/); }); test('should fail login with wrong password', async ({ page }) => { await page.goto('/login'); // Fill with wrong password await page.fill('[data-testid="email-input"]', testUsers.user.email); await page.fill('[data-testid="password-input"]', 'wrongpassword'); // Submit form await page.click('[data-testid="login-button"]'); // Verify error message await expect(page.locator('[data-testid="error-message"]')).toBeVisible(); await expect(page.locator('[data-testid="error-message"]')).toContainText(/invalid.*credentials/i); }); test('should show validation errors for empty fields', async ({ page }) => { await page.goto('/login'); // Try to submit empty form await page.click('[data-testid="login-button"]'); // Verify validation errors await expect(page.locator('[data-testid="email-error"]')).toBeVisible(); await expect(page.locator('[data-testid="password-error"]')).toBeVisible(); // Verify form validation messages await expect(page.locator('[data-testid="email-error"]')).toContainText(/required/i); await expect(page.locator('[data-testid="password-error"]')).toContainText(/required/i); }); test('should validate email format', async ({ page }) => { await page.goto('/login'); // Fill with invalid email format await page.fill('[data-testid="email-input"]', 'invalid-email'); await page.fill('[data-testid="password-input"]', 'password123'); // Submit form await page.click('[data-testid="login-button"]'); // Verify email format validation await expect(page.locator('[data-testid="email-error"]')).toBeVisible(); await expect(page.locator('[data-testid="email-error"]')).toContainText(/valid.*email/i); }); }); test.describe('JWT Token Handling', () => { test('should store JWT token after successful login', async ({ page }) => { await helpers.login(testUsers.user); // Check if token is stored in localStorage const token = await page.evaluate(() => localStorage.getItem('authToken')); expect(token).toBeTruthy(); // Verify token format (basic JWT structure) expect(token.split('.')).toHaveLength(3); }); test('should include JWT token in API requests', async ({ page }) => { await helpers.login(testUsers.user); // Monitor API requests const apiRequests = []; page.on('request', request => { if (request.url().includes('/api/')) { apiRequests.push({ url: request.url(), headers: request.headers() }); } }); // Navigate to a page that makes API calls await page.goto('/dashboard'); await page.waitForTimeout(2000); // Verify Authorization header is present const authenticatedRequests = apiRequests.filter(req => req.headers.authorization && req.headers.authorization.startsWith('Bearer ') ); expect(authenticatedRequests.length).toBeGreaterThan(0); }); test('should redirect to login when token expires', async ({ page }) => { await helpers.login(testUsers.user); // Simulate expired token await page.evaluate(() => { localStorage.setItem('authToken', 'expired.token.here'); }); // Try to access protected page await page.goto('/dashboard'); // Should redirect to login await expect(page).toHaveURL(/.*login/); }); }); test.describe('Session Persistence', () => { test('should maintain session across page reloads', async ({ page }) => { await helpers.login(testUsers.user); // Reload page await page.reload(); // Should still be logged in await expect(page.locator('[data-testid="user-menu"]')).toBeVisible(); await expect(page).toHaveURL(/.*dashboard/); }); test('should maintain session in new tab', async ({ context }) => { const page1 = await context.newPage(); const helpers1 = new TestHelpers(page1); // Login in first tab await helpers1.login(testUsers.user); // Open new tab const page2 = await context.newPage(); await page2.goto('/dashboard'); // Should be logged in in new tab await expect(page2.locator('[data-testid="user-menu"]')).toBeVisible(); await page1.close(); await page2.close(); }); }); test.describe('Logout Functionality', () => { test('should logout successfully', async ({ page }) => { await helpers.login(testUsers.user); // Logout await helpers.logout(); // Verify redirect to login await expect(page).toHaveURL(/.*login/); // Verify token is removed const token = await page.evaluate(() => localStorage.getItem('authToken')); expect(token).toBeNull(); }); test('should clear session data on logout', async ({ page }) => { await helpers.login(testUsers.user); // Set some session data await page.evaluate(() => { localStorage.setItem('userPreferences', 'test-data'); sessionStorage.setItem('tempData', 'test-data'); }); // Logout await helpers.logout(); // Verify session data is cleared const userPrefs = await page.evaluate(() => localStorage.getItem('userPreferences')); const tempData = await page.evaluate(() => sessionStorage.getItem('tempData')); expect(userPrefs).toBeNull(); expect(tempData).toBeNull(); }); test('should prevent access to protected pages after logout', async ({ page }) => { await helpers.login(testUsers.user); await helpers.logout(); // Try to access protected page await page.goto('/dashboard'); // Should redirect to login await expect(page).toHaveURL(/.*login/); }); }); test.describe('Role-Based Access Control', () => { test('admin should access admin panel', async ({ page }) => { await helpers.login(testUsers.admin); // Navigate to admin panel await page.goto('/admin'); // Should have access await expect(page.locator('[data-testid="admin-panel"]')).toBeVisible(); await expect(page.locator('[data-testid="user-management"]')).toBeVisible(); }); test('regular user should not access admin panel', async ({ page }) => { await helpers.login(testUsers.user); // Try to access admin panel await page.goto('/admin'); // Should be redirected or show access denied const isRedirected = await page.url().includes('/dashboard'); const hasAccessDenied = await helpers.elementExists('[data-testid="access-denied"]'); expect(isRedirected || hasAccessDenied).toBeTruthy(); }); test('agent should access agent features', async ({ page }) => { await helpers.login(testUsers.agent); // Navigate to agent dashboard await page.goto('/agent'); // Should have access to agent features await expect(page.locator('[data-testid="agent-dashboard"]')).toBeVisible(); await expect(page.locator('[data-testid="ticket-queue"]')).toBeVisible(); }); }); });