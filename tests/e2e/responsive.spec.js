const { test, expect } = require('@playwright/test'); const TestHelpers = require('../utils/test-helpers'); test.describe('Responsive Design Tests', () => { let helpers; let testUsers; test.beforeEach(async ({ page }) => { helpers = new TestHelpers(page); // Load test users const fs = require('fs'); const testData = JSON.parse(fs.readFileSync('./tests/fixtures/test-data.json', 'utf8')); testUsers = testData.users; await helpers.login(testUsers.user); }); const viewports = [ { width: 1920, height: 1080, name: 'Desktop Large' }, { width: 1366, height: 768, name: 'Desktop Standard' }, { width: 1024, height: 768, name: 'Tablet Landscape' }, { width: 768, height: 1024, name: 'Tablet Portrait' }, { width: 414, height: 896, name: 'Mobile Large' }, { width: 375, height: 667, name: 'Mobile Standard' }, { width: 320, height: 568, name: 'Mobile Small' } ]; test.describe('Dashboard Responsive Layout', () => { viewports.forEach(viewport => { test(`should display dashboard correctly on ${viewport.name}`, async ({ page }) => { await page.setViewportSize({ width: viewport.width, height: viewport.height }); await page.goto('/dashboard'); // Verify main dashboard elements are visible await expect(page.locator('[data-testid="dashboard"]')).toBeVisible(); // Check navigation accessibility const hasDesktopNav = await helpers.elementExists('[data-testid="desktop-navigation"]'); const hasMobileNav = await helpers.elementExists('[data-testid="mobile-navigation"]'); const hasMobileMenuToggle = await helpers.elementExists('[data-testid="mobile-menu-toggle"]'); // Should have either desktop nav or mobile nav/toggle expect(hasDesktopNav || hasMobileNav || hasMobileMenuToggle).toBeTruthy(); // Verify content is not cut off const dashboard = page.locator('[data-testid="dashboard"]'); const boundingBox = await dashboard.boundingBox(); if (boundingBox) { expect(boundingBox.width).toBeLessThanOrEqual(viewport.width); expect(boundingBox.x).toBeGreaterThanOrEqual(0); } }); }); test('should adapt navigation for mobile devices', async ({ page }) => { // Test mobile navigation await page.setViewportSize({ width: 375, height: 667 }); await page.goto('/dashboard'); const mobileMenuToggle = '[data-testid="mobile-menu-toggle"]'; const mobileMenu = '[data-testid="mobile-menu"]'; if (await helpers.elementExists(mobileMenuToggle)) { // Test mobile menu toggle await page.click(mobileMenuToggle); await expect(page.locator(mobileMenu)).toBeVisible(); // Test navigation items in mobile menu const navItems = await page.locator(`${mobileMenu} [data-testid^="nav-"]`).count(); expect(navItems).toBeGreaterThan(0); // Close menu await page.click(mobileMenuToggle); await expect(page.locator(mobileMenu)).toBeHidden(); } }); }); test.describe('Chat Interface Responsive Layout', () => { viewports.forEach(viewport => { test(`should display chat interface correctly on ${viewport.name}`, async ({ page }) => { await page.setViewportSize({ width: viewport.width, height: viewport.height }); await page.goto('/chat'); // Verify chat interface elements await expect(page.locator('[data-testid="chat-interface"]')).toBeVisible(); await expect(page.locator('[data-testid="message-input"]')).toBeVisible(); await expect(page.locator('[data-testid="send-button"]')).toBeVisible(); // Test message input functionality const testMessage = `Test message on ${viewport.name}`; await page.fill('[data-testid="message-input"]', testMessage); await page.click('[data-testid="send-button"]'); // Verify message appears await expect(page.locator('[data-testid="chat-history"]')).toContainText(testMessage); // Check if chat history is properly sized const chatHistory = page.locator('[data-testid="chat-history"]'); const chatBounds = await chatHistory.boundingBox(); if (chatBounds) { expect(chatBounds.width).toBeLessThanOrEqual(viewport.width); } }); }); test('should handle virtual keyboard on mobile', async ({ page }) => { await page.setViewportSize({ width: 375, height: 667 }); await page.goto('/chat'); // Focus on message input await page.focus('[data-testid="message-input"]'); // Verify send button is still accessible await expect(page.locator('[data-testid="send-button"]')).toBeVisible(); // Test sending message with virtual keyboard await page.fill('[data-testid="message-input"]', 'Mobile keyboard test'); await page.click('[data-testid="send-button"]'); await expect(page.locator('[data-testid="chat-history"]')).toContainText('Mobile keyboard test'); }); }); test.describe('Form Responsive Layout', () => { test('should display forms correctly on different screen sizes', async ({ page }) => { // Test profile/settings form if available await page.goto('/profile'); for (const viewport of viewports) { await page.setViewportSize({ width: viewport.width, height: viewport.height }); // Check if form exists const hasForm = await helpers.elementExists('[data-testid="profile-form"]') || await helpers.elementExists('[data-testid="settings-form"]') || await helpers.elementExists('form'); if (hasForm) { const form = page.locator('form').first(); await expect(form).toBeVisible(); // Verify form doesn't overflow const formBounds = await form.boundingBox(); if (formBounds) { expect(formBounds.width).toBeLessThanOrEqual(viewport.width); } } } }); test('should stack form elements vertically on mobile', async ({ page }) => { await page.setViewportSize({ width: 375, height: 667 }); await page.goto('/profile'); // Check if form elements are stacked appropriately const formInputs = await page.locator('input[type="text"], input[type="email"]').count(); if (formInputs > 1) { // Verify inputs are stacked vertically (not side by side) const inputs = await page.locator('input[type="text"], input[type="email"]').all(); if (inputs.length >= 2) { const firstInputBounds = await inputs[0].boundingBox(); const secondInputBounds = await inputs[1].boundingBox(); if (firstInputBounds && secondInputBounds) { // Second input should be below first input (higher Y coordinate) expect(secondInputBounds.y).toBeGreaterThan(firstInputBounds.y); } } } }); }); test.describe('Content Overflow and Scrolling', () => { test('should handle horizontal overflow correctly', async ({ page }) => { await page.setViewportSize({ width: 320, height: 568 }); // Very small screen await page.goto('/dashboard'); // Check for horizontal scrollbar const hasHorizontalScroll = await page.evaluate(() => { return document.documentElement.scrollWidth > document.documentElement.clientWidth; }); // Should not have horizontal scroll on small screens expect(hasHorizontalScroll).toBeFalsy(); }); test('should maintain vertical scrolling functionality', async ({ page }) => { await page.setViewportSize({ width: 375, height: 667 }); await page.goto('/chat'); // Send multiple messages to create scrollable content for (let i = 0; i < 20; i++) { await page.fill('[data-testid="message-input"]', `Message ${i + 1}`); await page.click('[data-testid="send-button"]'); await page.waitForTimeout(100); } // Check if chat history is scrollable const chatHistory = page.locator('[data-testid="chat-history"]'); const isScrollable = await chatHistory.evaluate(element => { return element.scrollHeight > element.clientHeight; }); if (isScrollable) { // Test scrolling functionality await chatHistory.evaluate(element => { element.scrollTop = 0; // Scroll to top }); const scrollTop = await chatHistory.evaluate(element => element.scrollTop); expect(scrollTop).toBe(0); } }); }); test.describe('Touch and Gesture Support', () => { test('should support touch interactions on mobile', async ({ page }) => { await page.setViewportSize({ width: 375, height: 667 }); await page.goto('/dashboard'); // Test touch tap on buttons const buttons = await page.locator('button').count(); if (buttons > 0) { const firstButton = page.locator('button').first(); // Simulate touch tap await firstButton.tap(); // Verify button interaction works // (This is a basic test - specific behavior depends on button function) } }); test('should handle swipe gestures if implemented', async ({ page }) => { await page.setViewportSize({ width: 375, height: 667 }); await page.goto('/chat'); // Test swipe gesture on chat history (if implemented) const chatHistory = page.locator('[data-testid="chat-history"]'); if (await chatHistory.isVisible()) { const bounds = await chatHistory.boundingBox(); if (bounds) { // Simulate swipe down gesture await page.mouse.move(bounds.x + bounds.width / 2, bounds.y + 50); await page.mouse.down(); await page.mouse.move(bounds.x + bounds.width / 2, bounds.y + bounds.height - 50); await page.mouse.up(); // Verify gesture doesn't break the interface await expect(chatHistory).toBeVisible(); } } }); }); test.describe('Performance on Different Devices', () => { test('should load quickly on mobile devices', async ({ page }) => { await page.setViewportSize({ width: 375, height: 667 }); const startTime = Date.now(); await page.goto('/dashboard'); await page.waitForLoadState('networkidle'); const loadTime = Date.now() - startTime; // Mobile should load within 8 seconds (allowing for slower mobile networks) expect(loadTime).toBeLessThan(8000); }); test('should be responsive to user interactions', async ({ page }) => { await page.setViewportSize({ width: 375, height: 667 }); await page.goto('/chat'); // Test interaction responsiveness const startTime = Date.now(); await page.fill('[data-testid="message-input"]', 'Responsiveness test'); await page.click('[data-testid="send-button"]'); // Wait for message to appear await page.waitForSelector('[data-testid="user-message"]', { timeout: 5000 }); const responseTime = Date.now() - startTime; // Interaction should be responsive (under 2 seconds) expect(responseTime).toBeLessThan(2000); }); }); });