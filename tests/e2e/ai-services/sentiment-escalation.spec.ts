/** * ============================================= * SENTIMENT ESCALATION SERVICE TESTS * Comprehensive testing of real-time sentiment monitoring * ============================================= */ import { test, expect, TestUtils } from '../../fixtures/test-fixtures'; import { testConfig } from '../../playwright.config'; test.describe('Sentiment Escalation Service', () => { test.beforeEach(async ({ adminPage }) => { // Navigate to escalation monitoring page await adminPage.goto('/dashboard/admin/escalations'); await TestUtils.waitForStableNetwork(adminPage); }); test.describe('@ai @smoke Escalation Trigger Detection', () => { test('should detect negative sentiment and trigger escalation', async ({ mlContext, testData }) => { const negativeSentimentMessages = [ 'Je suis absolument furieux de votre service', 'C\'est inacceptable, je veux un remboursement', 'Votre équipe est incompétente', 'Je vais porter plainte contre vous' ]; for (const message of negativeSentimentMessages) { const response = await mlContext.post('/api/ml/sentiment/analyze-escalation', { data: { message_text: message, conversation_id: 'test-conv-negative', customer_id: 'test-customer-1', platform: 'whatsapp' } }); expect(response.ok()).toBeTruthy(); const result = await response.json(); if (result.escalation_alert) { expect(result.escalation_alert).toHaveProperty('trigger'); expect(result.escalation_alert).toHaveProperty('level'); expect(result.escalation_alert).toHaveProperty('confidence'); expect(result.escalation_alert).toHaveProperty('sentiment_score'); expect(result.escalation_alert).toHaveProperty('escalation_reason'); expect(result.escalation_alert).toHaveProperty('recommended_actions'); // Verify escalation levels expect(['immediate', 'urgent', 'high', 'medium']).toContain(result.escalation_alert.level); expect(result.escalation_alert.confidence).toBeGreaterThan(0.5); expect(result.escalation_alert.sentiment_score).toBeLessThan(0.5); } } }); test('should detect threat and legal mentions for immediate escalation', async ({ mlContext }) => { const threatMessages = [ 'Je vais vous poursuivre en justice', 'Mon avocat va vous contacter', 'Je porte plainte à la DGCCRF', 'Vous allez entendre parler de moi' ]; for (const message of threatMessages) { const response = await mlContext.post('/api/ml/sentiment/analyze-escalation', { data: { message_text: message, conversation_id: 'test-conv-threat', customer_id: 'test-customer-1', platform: 'whatsapp' } }); expect(response.ok()).toBeTruthy(); const result = await response.json(); expect(result.escalation_alert).toBeDefined(); expect(result.escalation_alert.level).toBe('immediate'); expect(['threat_detection', 'legal_mention']).toContain(result.escalation_alert.trigger); expect(result.escalation_alert.confidence).toBeGreaterThan(0.8); } }); test('should detect competitor mentions and cancellation intent', async ({ mlContext }) => { const competitorMessages = [ 'Orange propose de meilleures offres', 'Je passe chez SFR demain', 'Bouygues a un meilleur service client', 'Je veux résilier mon abonnement' ]; for (const message of competitorMessages) { const response = await mlContext.post('/api/ml/sentiment/analyze-escalation', { data: { message_text: message, conversation_id: 'test-conv-competitor', customer_id: 'test-customer-1', platform: 'whatsapp' } }); expect(response.ok()).toBeTruthy(); const result = await response.json(); if (result.escalation_alert) { expect(['competitor_mention', 'cancellation_intent']).toContain(result.escalation_alert.trigger); expect(['high', 'urgent']).toContain(result.escalation_alert.level); } } }); test('should handle VIP customer escalations with priority', async ({ mlContext }) => { const response = await mlContext.post('/api/ml/sentiment/analyze-escalation', { data: { message_text: 'Je ne suis pas satisfait du service', conversation_id: 'test-conv-vip', customer_id: 'test-customer-vip', // VIP customer platform: 'whatsapp' } }); expect(response.ok()).toBeTruthy(); const result = await response.json(); if (result.escalation_alert) { expect(result.escalation_alert.trigger).toBe('vip_customer_issue'); expect(['urgent', 'immediate']).toContain(result.escalation_alert.level); expect(result.escalation_alert.customer_segment).toBe('vip'); // VIP escalations should have specific recommended actions const actions = result.escalation_alert.recommended_actions; expect(actions).toContain('VIP service protocol'); } }); }); test.describe('@ai @regression Sentiment Decline Detection', () => { test('should detect sentiment decline over conversation', async ({ mlContext }) => { const conversationId = 'test-conv-decline'; const messages = [ { text: 'Bonjour, j\'ai une question', expected_sentiment: 0.7 }, { text: 'Votre réponse ne m\'aide pas', expected_sentiment: 0.4 }, { text: 'C\'est vraiment frustrant', expected_sentiment: 0.2 }, { text: 'Je suis très mécontent maintenant', expected_sentiment: 0.1 } ]; for (let i = 0; i < messages.length; i++) { const message = messages[i]; const response = await mlContext.post('/api/ml/sentiment/analyze-escalation', { data: { message_text: message.text, conversation_id: conversationId, customer_id: 'test-customer-1', platform: 'whatsapp' } }); expect(response.ok()).toBeTruthy(); const result = await response.json(); // Later messages should trigger escalation due to sentiment decline if (i >= 2) { expect(result.escalation_alert).toBeDefined(); expect(['sentiment_decline', 'negative_threshold']).toContain(result.escalation_alert.trigger); } } }); test('should track consecutive negative messages', async ({ mlContext }) => { const conversationId = 'test-conv-consecutive'; const negativeMessages = [ 'Ce service est nul', 'Vous ne m\'aidez pas du tout', 'C\'est de pire en pire', 'Je n\'en peux plus' ]; for (let i = 0; i < negativeMessages.length; i++) { const response = await mlContext.post('/api/ml/sentiment/analyze-escalation', { data: { message_text: negativeMessages[i], conversation_id: conversationId, customer_id: 'test-customer-1', platform: 'whatsapp' } }); expect(response.ok()).toBeTruthy(); const result = await response.json(); // Should escalate after 3 consecutive negative messages if (i >= 2) { expect(result.escalation_alert).toBeDefined(); expect(result.escalation_alert.trigger).toBe('frustration_pattern'); } } }); test('should consider customer escalation history', async ({ mlContext }) => { // Customer with previous escalations const response = await mlContext.post('/api/ml/sentiment/analyze-escalation', { data: { message_text: 'Encore un problème avec votre service', conversation_id: 'test-conv-history', customer_id: 'test-customer-repeat', // Customer with escalation history platform: 'whatsapp' } }); expect(response.ok()).toBeTruthy(); const result = await response.json(); if (result.escalation_alert) { expect(result.escalation_alert.trigger).toBe('repeated_complaints'); expect(result.escalation_alert.level).toMatch(/high|urgent/); // Should mention history in supervisor notes expect(result.escalation_alert.supervisor_notes).toContain('Previous escalations'); } }); }); test.describe('@ai @notifications Supervisor Notifications', () => { test('should send notifications to appropriate supervisors', async ({ mlContext, adminPage }) => { // Trigger an escalation const response = await mlContext.post('/api/ml/sentiment/analyze-escalation', { data: { message_text: 'Je veux parler à un superviseur immédiatement', conversation_id: 'test-conv-notification', customer_id: 'test-customer-1', platform: 'whatsapp' } }); expect(response.ok()).toBeTruthy(); const result = await response.json(); if (result.escalation_alert) { // Check that notification appears in admin dashboard await adminPage.reload(); await TestUtils.waitForElement(adminPage, '[data-testid="escalation-alerts"]'); const alertsPanel = adminPage.locator('[data-testid="escalation-alerts"]'); await expect(alertsPanel).toBeVisible(); // Should show the new escalation const latestAlert = alertsPanel.locator('[data-testid="escalation-item"]').first(); await expect(latestAlert).toBeVisible(); await expect(latestAlert.locator('[data-testid="escalation-level"]')).toContainText(result.escalation_alert.level); } }); test('should provide escalation details and recommended actions', async ({ adminPage }) => { // Navigate to escalation details await adminPage.click('[data-testid="escalation-item"]'); await TestUtils.waitForElement(adminPage, '[data-testid="escalation-details"]'); const detailsPanel = adminPage.locator('[data-testid="escalation-details"]'); await expect(detailsPanel).toBeVisible(); // Verify escalation information is displayed await expect(detailsPanel.locator('[data-testid="escalation-reason"]')).toBeVisible(); await expect(detailsPanel.locator('[data-testid="customer-info"]')).toBeVisible(); await expect(detailsPanel.locator('[data-testid="conversation-context"]')).toBeVisible(); await expect(detailsPanel.locator('[data-testid="recommended-actions"]')).toBeVisible(); await expect(detailsPanel.locator('[data-testid="supervisor-notes"]')).toBeVisible(); // Should have action buttons await expect(detailsPanel.locator('[data-testid="assign-supervisor"]')).toBeVisible(); await expect(detailsPanel.locator('[data-testid="escalate-further"]')).toBeVisible(); await expect(detailsPanel.locator('[data-testid="resolve-escalation"]')).toBeVisible(); }); test('should allow supervisors to take action on escalations', async ({ adminPage }) => { // Open escalation details await adminPage.click('[data-testid="escalation-item"]'); await TestUtils.waitForElement(adminPage, '[data-testid="escalation-details"]'); // Assign to supervisor await adminPage.click('[data-testid="assign-supervisor"]'); await TestUtils.waitForElement(adminPage, '[data-testid="supervisor-assignment-modal"]'); await adminPage.selectOption('[data-testid="supervisor-select"]', 'supervisor-1'); await adminPage.fill('[data-testid="assignment-notes"]', 'Taking immediate action on this escalation'); await adminPage.click('[data-testid="confirm-assignment"]'); // Verify assignment await TestUtils.waitForElement(adminPage, '[data-testid="assignment-success"]'); await expect(adminPage.locator('[data-testid="assigned-supervisor"]')).toContainText('supervisor-1'); }); }); test.describe('@ai @realtime Real-time Monitoring', () => { test('should monitor active conversations for sentiment changes', async ({ mlContext }) => { // Start monitoring const monitoringResponse = await mlContext.post('/api/ml/sentiment/start-monitoring', { data: { conversation_ids: ['test-conv-monitor-1', 'test-conv-monitor-2'] } }); expect(monitoringResponse.ok()).toBeTruthy(); // Simulate sentiment changes await mlContext.post('/api/ml/sentiment/analyze-escalation', { data: { message_text: 'Je deviens de plus en plus frustré', conversation_id: 'test-conv-monitor-1', customer_id: 'test-customer-1', platform: 'whatsapp' } }); // Check monitoring status const statusResponse = await mlContext.get('/api/ml/sentiment/monitoring-status'); expect(statusResponse.ok()).toBeTruthy(); const status = await statusResponse.json(); expect(status).toHaveProperty('active_conversations'); expect(status).toHaveProperty('escalations_detected'); expect(status).toHaveProperty('monitoring_since'); }); test('should respect escalation cooldown periods', async ({ mlContext }) => { const conversationId = 'test-conv-cooldown'; // First escalation const firstResponse = await mlContext.post('/api/ml/sentiment/analyze-escalation', { data: { message_text: 'Je suis très en colère', conversation_id: conversationId, customer_id: 'test-customer-1', platform: 'whatsapp' } }); expect(firstResponse.ok()).toBeTruthy(); const firstResult = await firstResponse.json(); expect(firstResult.escalation_alert).toBeDefined(); // Immediate second message (should be in cooldown) const secondResponse = await mlContext.post('/api/ml/sentiment/analyze-escalation', { data: { message_text: 'Toujours en colère', conversation_id: conversationId, customer_id: 'test-customer-1', platform: 'whatsapp' } }); expect(secondResponse.ok()).toBeTruthy(); const secondResult = await secondResponse.json(); expect(secondResult.escalation_alert).toBeNull(); // Should be blocked by cooldown }); test('should handle business hours context', async ({ mlContext }) => { // During business hours const businessHoursResponse = await mlContext.post('/api/ml/sentiment/analyze-escalation', { data: { message_text: 'J\'ai un problème urgent', conversation_id: 'test-conv-hours', customer_id: 'test-customer-1', platform: 'whatsapp', timestamp: '2024-01-15T14:00:00Z' // Business hours } }); expect(businessHoursResponse.ok()).toBeTruthy(); const businessResult = await businessHoursResponse.json(); // After hours const afterHoursResponse = await mlContext.post('/api/ml/sentiment/analyze-escalation', { data: { message_text: 'J\'ai un problème urgent', conversation_id: 'test-conv-hours-2', customer_id: 'test-customer-1', platform: 'whatsapp', timestamp: '2024-01-15T22:00:00Z' // After hours } }); expect(afterHoursResponse.ok()).toBeTruthy(); const afterResult = await afterHoursResponse.json(); // After hours escalations should have different handling if (afterResult.escalation_alert) { expect(afterResult.escalation_alert.metadata.business_hours).toBeFalsy(); expect(afterResult.escalation_alert.recommended_actions).toContain('Schedule callback'); } }); }); test.describe('@ai @analytics Escalation Analytics', () => { test('should track escalation metrics and trends', async ({ mlContext }) => { // Generate some escalations await mlContext.post('/api/ml/sentiment/analyze-escalation', { data: { message_text: 'Je suis mécontent', conversation_id: 'test-conv-analytics-1', customer_id: 'test-customer-1', platform: 'whatsapp' } }); await mlContext.post('/api/ml/sentiment/analyze-escalation', { data: { message_text: 'Service terrible', conversation_id: 'test-conv-analytics-2', customer_id: 'test-customer-2', platform: 'facebook' } }); // Get escalation analytics const analyticsResponse = await mlContext.get('/api/ml/sentiment/escalation-analytics?days=30'); expect(analyticsResponse.ok()).toBeTruthy(); const analytics = await analyticsResponse.json(); expect(analytics).toHaveProperty('total_escalations'); expect(analytics).toHaveProperty('escalations_by_trigger'); expect(analytics).toHaveProperty('escalations_by_level'); expect(analytics).toHaveProperty('resolution_rate'); expect(analytics).toHaveProperty('average_escalations_per_day'); expect(analytics).toHaveProperty('most_common_trigger'); }); test('should provide escalation trend analysis', async ({ mlContext }) => { const analyticsResponse = await mlContext.get('/api/ml/sentiment/escalation-analytics?days=30'); expect(analyticsResponse.ok()).toBeTruthy(); const analytics = await analyticsResponse.json(); // Should have trigger distribution expect(analytics.escalations_by_trigger).toBeDefined(); expect(typeof analytics.escalations_by_trigger).toBe('object'); // Should have level distribution expect(analytics.escalations_by_level).toBeDefined(); expect(typeof analytics.escalations_by_level).toBe('object'); // Should have resolution metrics expect(analytics.resolution_rate).toBeGreaterThanOrEqual(0); expect(analytics.resolution_rate).toBeLessThanOrEqual(1); }); }); test.describe('@ai @performance Performance and Reliability', () => { test('should analyze sentiment within acceptable time limits', async ({ mlContext }) => { const startTime = Date.now(); const response = await mlContext.post('/api/ml/sentiment/analyze-escalation', { data: { message_text: 'Performance test message for sentiment analysis', conversation_id: 'test-conv-performance', customer_id: 'test-customer-1', platform: 'whatsapp' } }); const endTime = Date.now(); const responseTime = endTime - startTime; expect(response.ok()).toBeTruthy(); expect(responseTime).toBeLessThan(1000); // Should analyze within 1 second }); test('should handle high volume of sentiment analysis requests', async ({ mlContext }) => { const concurrentRequests = 25; const promises = []; for (let i = 0; i < concurrentRequests; i++) { const promise = mlContext.post('/api/ml/sentiment/analyze-escalation', { data: { message_text: `Concurrent sentiment test ${i}`, conversation_id: `test-conv-concurrent-${i}`, customer_id: 'test-customer-1', platform: 'whatsapp' } }); promises.push(promise); } const responses = await Promise.all(promises); // All requests should succeed for (const response of responses) { expect(response.ok()).toBeTruthy(); } }); test('should maintain accuracy under load', async ({ mlContext }) => { const testMessages = [ { text: 'Je suis très content', expected_positive: true }, { text: 'C\'est absolument terrible', expected_positive: false }, { text: 'Service correct', expected_positive: null }, // Neutral { text: 'Je veux porter plainte', expected_positive: false } ]; for (const testMessage of testMessages) { const response = await mlContext.post('/api/ml/sentiment/analyze-escalation', { data: { message_text: testMessage.text, conversation_id: 'test-conv-accuracy', customer_id: 'test-customer-1', platform: 'whatsapp' } }); expect(response.ok()).toBeTruthy(); const result = await response.json(); // Verify sentiment accuracy if (testMessage.expected_positive === true) { expect(result.sentiment_score).toBeGreaterThan(0.6); } else if (testMessage.expected_positive === false) { expect(result.sentiment_score).toBeLessThan(0.4); if (result.escalation_alert) { expect(result.escalation_alert.confidence).toBeGreaterThan(0.5); } } } }); }); test.describe('@ai @edge Edge Cases', () => { test('should handle empty or malformed messages', async ({ mlContext }) => { const edgeCases = ['', ' ', null, undefined, '[AI][AI][AI]', 'a'.repeat(10000)]; for (const message of edgeCases) { const response = await mlContext.post('/api/ml/sentiment/analyze-escalation', { data: { message_text: message, conversation_id: 'test-conv-edge', customer_id: 'test-customer-1', platform: 'whatsapp' } }); // Should handle gracefully without crashing expect(response.status()).toBeLessThan(500); } }); test('should handle non-existent conversation context', async ({ mlContext }) => { const response = await mlContext.post('/api/ml/sentiment/analyze-escalation', { data: { message_text: 'Test message', conversation_id: 'non-existent-conversation', customer_id: 'non-existent-customer', platform: 'whatsapp' } }); expect(response.status()).toBeLessThan(500); if (response.ok()) { const result = await response.json(); // Should still provide sentiment analysis even without context expect(result).toHaveProperty('sentiment_score'); } }); }); });