/** * ============================================= * [TARGET] INTELLIGENT ROUTING SERVICE TESTS * Comprehensive testing of ML-powered agent assignment * ============================================= */ import { test, expect, TestUtils } from '../../fixtures/test-fixtures'; import { testConfig } from '../../playwright.config'; test.describe('Intelligent Routing Service', () => { test.beforeEach(async ({ adminPage }) => { // Navigate to routing management page await adminPage.goto('/dashboard/admin/routing'); await TestUtils.waitForStableNetwork(adminPage); }); test.describe('@ai @smoke Agent Scoring and Assignment', () => { test('should score agents based on skills and availability', async ({ mlContext, testData }) => { const routingRequest = { conversation_id: 'test-conv-routing-1', customer_message: 'Mon internet ne fonctionne plus', platform: 'whatsapp', customer_id: 'test-customer-1', urgency: 'medium' }; const response = await mlContext.post('/api/ml/routing/route-conversation', { data: routingRequest }); expect(response.ok()).toBeTruthy(); const result = await response.json(); // Verify routing decision structure expect(result).toHaveProperty('recommended_agent'); expect(result).toHaveProperty('alternative_agents'); expect(result).toHaveProperty('routing_confidence'); expect(result).toHaveProperty('priority'); expect(result).toHaveProperty('estimated_wait_time'); // Verify recommended agent details const recommendedAgent = result.recommended_agent; expect(recommendedAgent).toHaveProperty('agent_id'); expect(recommendedAgent).toHaveProperty('agent_name'); expect(recommendedAgent).toHaveProperty('score'); expect(recommendedAgent).toHaveProperty('availability'); expect(recommendedAgent).toHaveProperty('skill_match'); expect(recommendedAgent).toHaveProperty('reasoning'); // Score should be between 0 and 1 expect(recommendedAgent.score).toBeGreaterThanOrEqual(0); expect(recommendedAgent.score).toBeLessThanOrEqual(1); // Should have reasoning for the decision expect(recommendedAgent.reasoning).toBeInstanceOf(Array); expect(recommendedAgent.reasoning.length).toBeGreaterThan(0); }); test('should prioritize agents with matching skills', async ({ mlContext }) => { const skillTests = [ { message: 'Mon téléphone ne marche plus', expectedSkill: 'technical_support', intent: 'technical_issue' }, { message: 'Ma facture est incorrecte', expectedSkill: 'billing_support', intent: 'billing_question' }, { message: 'Je veux changer de forfait', expectedSkill: 'sales_support', intent: 'service_request' } ]; for (const skillTest of skillTests) { const response = await mlContext.post('/api/ml/routing/route-conversation', { data: { conversation_id: 'test-conv-skill', customer_message: skillTest.message, platform: 'whatsapp', customer_id: 'test-customer-1', urgency: 'medium' } }); expect(response.ok()).toBeTruthy(); const result = await response.json(); const recommendedAgent = result.recommended_agent; expect(recommendedAgent.metadata.skills).toContain(skillTest.expectedSkill); expect(recommendedAgent.skill_match).toBeGreaterThan(0.7); } }); test('should balance workload across agents', async ({ mlContext }) => { // Create multiple routing requests to test load balancing const routingPromises = []; for (let i = 0; i < 10; i++) { const promise = mlContext.post('/api/ml/routing/route-conversation', { data: { conversation_id: `test-conv-balance-${i}`, customer_message: 'Bonjour, j\'ai besoin d\'aide', platform: 'whatsapp', customer_id: 'test-customer-1', urgency: 'medium' } }); routingPromises.push(promise); } const responses = await Promise.all(routingPromises); const agentAssignments = new Map(); for (const response of responses) { expect(response.ok()).toBeTruthy(); const result = await response.json(); const agentId = result.recommended_agent.agent_id; agentAssignments.set(agentId, (agentAssignments.get(agentId) || 0) + 1); } // Verify load is distributed (no single agent gets more than 60% of assignments) const totalAssignments = responses.length; for (const [agentId, count] of agentAssignments) { const percentage = count / totalAssignments; expect(percentage).toBeLessThanOrEqual(0.6); } }); test('should consider agent performance in scoring', async ({ mlContext }) => { const response = await mlContext.post('/api/ml/routing/route-conversation', { data: { conversation_id: 'test-conv-performance', customer_message: 'J\'ai un problème urgent', platform: 'whatsapp', customer_id: 'test-customer-1', urgency: 'high' } }); expect(response.ok()).toBeTruthy(); const result = await response.json(); const recommendedAgent = result.recommended_agent; expect(recommendedAgent).toHaveProperty('performance_score'); expect(recommendedAgent.performance_score).toBeGreaterThanOrEqual(0); expect(recommendedAgent.performance_score).toBeLessThanOrEqual(1); // High urgency should prefer high-performance agents if (result.priority === 'high' || result.priority === 'urgent') { expect(recommendedAgent.performance_score).toBeGreaterThan(0.6); } }); }); test.describe('@ai @regression Priority and Urgency Handling', () => { test('should prioritize urgent conversations', async ({ mlContext }) => { const urgentResponse = await mlContext.post('/api/ml/routing/route-conversation', { data: { conversation_id: 'test-conv-urgent', customer_message: 'URGENT: Mon service est complètement coupé', platform: 'whatsapp', customer_id: 'test-customer-1', urgency: 'urgent' } }); expect(urgentResponse.ok()).toBeTruthy(); const urgentResult = await urgentResponse.json(); expect(urgentResult.priority).toBe('urgent'); expect(urgentResult.estimated_wait_time).toBeLessThan(60); // Less than 1 minute expect(urgentResult.recommended_agent.score).toBeGreaterThan(0.8); }); test('should handle VIP customer routing', async ({ mlContext }) => { const vipResponse = await mlContext.post('/api/ml/routing/route-conversation', { data: { conversation_id: 'test-conv-vip', customer_message: 'J\'ai un problème avec mon service', platform: 'whatsapp', customer_id: 'test-customer-vip', // VIP customer urgency: 'medium' } }); expect(vipResponse.ok()).toBeTruthy(); const vipResult = await vipResponse.json(); // VIP customers should get priority treatment expect(vipResult.priority).toMatch(/high|urgent/); expect(vipResult.estimated_wait_time).toBeLessThan(120); // Less than 2 minutes expect(vipResult.recommended_agent.score).toBeGreaterThan(0.8); // Should have VIP-specific reasoning const reasoning = vipResult.recommended_agent.reasoning.join(' '); expect(reasoning.toLowerCase()).toContain('vip'); }); test('should escalate when no suitable agents available', async ({ mlContext }) => { // Mock scenario where all agents are busy const response = await mlContext.post('/api/ml/routing/route-conversation', { data: { conversation_id: 'test-conv-no-agents', customer_message: 'J\'ai besoin d\'aide immédiatement', platform: 'whatsapp', customer_id: 'test-customer-1', urgency: 'urgent', force_no_agents: true // Test parameter } }); expect(response.ok()).toBeTruthy(); const result = await response.json(); if (result.recommended_agent === null) { expect(result.escalation_suggested).toBeTruthy(); expect(result.metadata).toHaveProperty('no_agents_available', true); expect(result.metadata).toHaveProperty('queue_position'); } }); }); test.describe('@ai @platform Platform-Specific Routing', () => { test('should route based on agent platform expertise', async ({ mlContext }) => { const platforms = ['whatsapp', 'facebook', 'instagram', 'twitter']; for (const platform of platforms) { const response = await mlContext.post('/api/ml/routing/route-conversation', { data: { conversation_id: `test-conv-platform-${platform}`, customer_message: 'Bonjour, j\'ai une question', platform: platform, customer_id: 'test-customer-1', urgency: 'medium' } }); expect(response.ok()).toBeTruthy(); const result = await response.json(); const recommendedAgent = result.recommended_agent; expect(recommendedAgent.platform_expertise).toBeGreaterThan(0.5); expect(recommendedAgent.metadata.platformSupport[platform]).toBeTruthy(); } }); test('should consider platform-specific experience', async ({ mlContext }) => { const response = await mlContext.post('/api/ml/routing/route-conversation', { data: { conversation_id: 'test-conv-experience', customer_message: 'Question complexe sur Instagram', platform: 'instagram', customer_id: 'test-customer-1', urgency: 'medium' } }); expect(response.ok()).toBeTruthy(); const result = await response.json(); const recommendedAgent = result.recommended_agent; expect(recommendedAgent.platform_expertise).toBeGreaterThan(0.6); // Should prefer agents with Instagram experience if (recommendedAgent.metadata.platformExperience) { expect(recommendedAgent.metadata.platformExperience.instagram).toBeGreaterThan(0); } }); }); test.describe('@ai @customer Customer Preference Learning', () => { test('should consider customer preferred agents', async ({ mlContext }) => { // Set up customer with preferred agent const response = await mlContext.post('/api/ml/routing/route-conversation', { data: { conversation_id: 'test-conv-preference', customer_message: 'Bonjour, comme d\'habitude', platform: 'whatsapp', customer_id: 'test-customer-with-preference', urgency: 'medium' } }); expect(response.ok()).toBeTruthy(); const result = await response.json(); const recommendedAgent = result.recommended_agent; expect(recommendedAgent.customer_preference).toBeGreaterThan(0.5); // Should mention preference in reasoning const reasoning = recommendedAgent.reasoning.join(' '); expect(reasoning.toLowerCase()).toMatch(/prefer|history|positive/); }); test('should learn from successful interactions', async ({ mlContext }) => { // Simulate successful interaction feedback await mlContext.post('/api/ml/routing/feedback', { data: { conversation_id: 'test-conv-success', agent_id: 'test-agent-1', customer_id: 'test-customer-1', satisfaction_rating: 5, resolution_success: true } }); // Next routing for same customer should prefer the successful agent const response = await mlContext.post('/api/ml/routing/route-conversation', { data: { conversation_id: 'test-conv-learned', customer_message: 'Nouvelle question', platform: 'whatsapp', customer_id: 'test-customer-1', urgency: 'medium' } }); expect(response.ok()).toBeTruthy(); const result = await response.json(); // Should have higher customer preference score for the successful agent if (result.recommended_agent.agent_id === 'test-agent-1') { expect(result.recommended_agent.customer_preference).toBeGreaterThan(0.7); } }); }); test.describe('@ai @performance Performance and Scalability', () => { test('should route conversations within acceptable time limits', async ({ mlContext }) => { const startTime = Date.now(); const response = await mlContext.post('/api/ml/routing/route-conversation', { data: { conversation_id: 'test-conv-performance', customer_message: 'Performance test', platform: 'whatsapp', customer_id: 'test-customer-1', urgency: 'medium' } }); const endTime = Date.now(); const responseTime = endTime - startTime; expect(response.ok()).toBeTruthy(); expect(responseTime).toBeLessThan(1000); // Should route within 1 second }); test('should handle concurrent routing requests', async ({ mlContext }) => { const concurrentRequests = 15; const promises = []; for (let i = 0; i < concurrentRequests; i++) { const promise = mlContext.post('/api/ml/routing/route-conversation', { data: { conversation_id: `test-conv-concurrent-${i}`, customer_message: `Concurrent routing test ${i}`, platform: 'whatsapp', customer_id: 'test-customer-1', urgency: 'medium' } }); promises.push(promise); } const responses = await Promise.all(promises); // All requests should succeed for (const response of responses) { expect(response.ok()).toBeTruthy(); } // Verify load distribution const agentCounts = new Map(); for (const response of responses) { const result = await response.json(); const agentId = result.recommended_agent.agent_id; agentCounts.set(agentId, (agentCounts.get(agentId) || 0) + 1); } // No single agent should get more than 50% of concurrent requests for (const [agentId, count] of agentCounts) { expect(count / concurrentRequests).toBeLessThanOrEqual(0.5); } }); test('should provide accurate wait time estimates', async ({ mlContext }) => { const response = await mlContext.post('/api/ml/routing/route-conversation', { data: { conversation_id: 'test-conv-wait-time', customer_message: 'Estimation du temps d\'attente', platform: 'whatsapp', customer_id: 'test-customer-1', urgency: 'medium' } }); expect(response.ok()).toBeTruthy(); const result = await response.json(); expect(result.estimated_wait_time).toBeGreaterThan(0); expect(result.estimated_wait_time).toBeLessThan(1800); // Less than 30 minutes // Wait time should correlate with agent availability const agent = result.recommended_agent; if (agent.availability === 'available') { expect(result.estimated_wait_time).toBeLessThan(60); // Less than 1 minute } else if (agent.availability === 'busy') { expect(result.estimated_wait_time).toBeGreaterThan(30); // More than 30 seconds } }); }); test.describe('@ai @analytics Routing Analytics', () => { test('should track routing decisions and outcomes', async ({ mlContext }) => { // Generate some routing decisions await mlContext.post('/api/ml/routing/route-conversation', { data: { conversation_id: 'test-conv-analytics-1', customer_message: 'Analytics test 1', platform: 'whatsapp', customer_id: 'test-customer-1', urgency: 'medium' } }); await mlContext.post('/api/ml/routing/route-conversation', { data: { conversation_id: 'test-conv-analytics-2', customer_message: 'Analytics test 2', platform: 'facebook', customer_id: 'test-customer-2', urgency: 'high' } }); // Get routing analytics const analyticsResponse = await mlContext.get('/api/ml/routing/analytics?days=30'); expect(analyticsResponse.ok()).toBeTruthy(); const analytics = await analyticsResponse.json(); expect(analytics).toHaveProperty('total_routing_decisions'); expect(analytics).toHaveProperty('high_confidence_decisions'); expect(analytics).toHaveProperty('escalation_rate'); expect(analytics).toHaveProperty('average_confidence'); expect(analytics).toHaveProperty('average_wait_time'); expect(analytics).toHaveProperty('routing_by_priority'); expect(analytics).toHaveProperty('agent_utilization'); }); test('should provide agent utilization metrics', async ({ mlContext }) => { const analyticsResponse = await mlContext.get('/api/ml/routing/analytics?days=30'); expect(analyticsResponse.ok()).toBeTruthy(); const analytics = await analyticsResponse.json(); expect(analytics.agent_utilization).toBeDefined(); // Should have utilization data for each agent for (const [agentId, utilization] of Object.entries(analytics.agent_utilization)) { expect(typeof utilization).toBe('number'); expect(utilization).toBeGreaterThanOrEqual(0); expect(utilization).toBeLessThanOrEqual(1); } }); }); test.describe('@ai @edge Edge Cases and Error Handling', () => { test('should handle invalid conversation data gracefully', async ({ mlContext }) => { const invalidRequests = [ { conversation_id: '', customer_message: 'Test', platform: 'whatsapp' }, { conversation_id: 'test', customer_message: '', platform: 'whatsapp' }, { conversation_id: 'test', customer_message: 'Test', platform: 'invalid' } ]; for (const request of invalidRequests) { const response = await mlContext.post('/api/ml/routing/route-conversation', { data: request }); // Should handle gracefully without crashing expect(response.status()).toBeLessThan(500); } }); test('should provide fallback routing when ML fails', async ({ mlContext }) => { const response = await mlContext.post('/api/ml/routing/route-conversation', { data: { conversation_id: 'test-conv-fallback', customer_message: 'Fallback test', platform: 'whatsapp', customer_id: 'test-customer-1', urgency: 'medium', force_ml_failure: true // Test parameter } }); expect(response.ok()).toBeTruthy(); const result = await response.json(); // Should still provide a routing decision expect(result).toHaveProperty('recommended_agent'); expect(result.metadata).toHaveProperty('fallback_routing', true); expect(result.routing_confidence).toBeLessThan(0.5); // Lower confidence for fallback }); }); });