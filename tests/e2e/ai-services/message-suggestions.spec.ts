/** * ============================================= * [AI] AI MESSAGE SUGGESTIONS SERVICE TESTS * Comprehensive testing of AI-powered message suggestions * ============================================= */ import { test, expect, TestUtils } from '../../fixtures/test-fixtures'; import { testConfig } from '../../playwright.config'; test.describe('AI Message Suggestions Service', () => { test.beforeEach(async ({ agentPage }) => { // Navigate to conversations page await agentPage.goto('/dashboard/conversations'); await TestUtils.waitForStableNetwork(agentPage); }); test.describe('@ai @smoke Message Suggestion Generation', () => { test('should generate contextual suggestions for customer message', async ({ agentPage, apiContext, testData }) => { // Open a test conversation await agentPage.click('[data-testid="conversation-test-conv-1"]'); await TestUtils.waitForElement(agentPage, '[data-testid="message-input"]'); // Simulate customer message const customerMessage = 'Bonjour, j\'ai un problème avec mon forfait mobile'; // Wait for AI suggestions to appear await TestUtils.waitForElement(agentPage, '[data-testid="ai-suggestions-panel"]'); // Verify suggestions are displayed const suggestionsPanel = agentPage.locator('[data-testid="ai-suggestions-panel"]'); await expect(suggestionsPanel).toBeVisible(); // Check that multiple suggestions are provided const suggestions = agentPage.locator('[data-testid="suggestion-item"]'); const suggestionCount = await suggestions.count(); expect(suggestionCount).toBeGreaterThan(0); expect(suggestionCount).toBeLessThanOrEqual(5); // Verify suggestion structure const firstSuggestion = suggestions.first(); await expect(firstSuggestion.locator('[data-testid="suggestion-text"]')).toBeVisible(); await expect(firstSuggestion.locator('[data-testid="suggestion-confidence"]')).toBeVisible(); await expect(firstSuggestion.locator('[data-testid="suggestion-category"]')).toBeVisible(); // Verify confidence scores are within expected range const confidenceText = await firstSuggestion.locator('[data-testid="suggestion-confidence"]').textContent(); const confidence = parseFloat(confidenceText?.replace('%', '') || '0') / 100; expect(confidence).toBeGreaterThanOrEqual(0.5); expect(confidence).toBeLessThanOrEqual(1.0); }); test('should adapt suggestions for different platforms', async ({ agentPage, mlContext }) => { const platforms = ['whatsapp', 'facebook', 'instagram', 'twitter']; for (const platform of platforms) { // Test suggestion generation for each platform const response = await mlContext.post('/api/ml/suggestions/generate', { data: { conversation_id: 'test-conv-1', customer_message: 'Je veux annuler mon abonnement', platform: platform, agent_id: 'test-agent-1' } }); expect(response.ok()).toBeTruthy(); const suggestions = await response.json(); // Verify platform-specific adaptations expect(suggestions).toHaveProperty('suggestions'); expect(suggestions.suggestions.length).toBeGreaterThan(0); for (const suggestion of suggestions.suggestions) { expect(suggestion).toHaveProperty('text'); expect(suggestion).toHaveProperty('platform_specific', true); expect(suggestion).toHaveProperty('platform', platform); // Platform-specific validations if (platform === 'twitter') { expect(suggestion.text.length).toBeLessThanOrEqual(280); } else if (platform === 'whatsapp') { expect(suggestion.text.length).toBeLessThanOrEqual(4096); } } } }); test('should provide suggestions with appropriate confidence levels', async ({ mlContext }) => { const testCases = [ { message: 'Bonjour, comment allez-vous ?', expectedCategory: 'greeting', minConfidence: 0.8 }, { message: 'Je veux résilier mon contrat immédiatement', expectedCategory: 'cancellation', minConfidence: 0.7 }, { message: 'Mon téléphone ne fonctionne plus', expectedCategory: 'technical_support', minConfidence: 0.6 } ]; for (const testCase of testCases) { const response = await mlContext.post('/api/ml/suggestions/generate', { data: { conversation_id: 'test-conv-1', customer_message: testCase.message, platform: 'whatsapp', agent_id: 'test-agent-1' } }); expect(response.ok()).toBeTruthy(); const result = await response.json(); expect(result.suggestions.length).toBeGreaterThan(0); const topSuggestion = result.suggestions[0]; expect(topSuggestion.confidence).toBeGreaterThanOrEqual(testCase.minConfidence); expect(topSuggestion.category).toBe(testCase.expectedCategory); } }); }); test.describe('@ai @regression Agent Feedback Integration', () => { test('should allow agents to accept suggestions', async ({ agentPage }) => { // Open conversation await agentPage.click('[data-testid="conversation-test-conv-1"]'); await TestUtils.waitForElement(agentPage, '[data-testid="ai-suggestions-panel"]'); // Click on first suggestion const firstSuggestion = agentPage.locator('[data-testid="suggestion-item"]').first(); await firstSuggestion.click(); // Verify suggestion is inserted into message input const messageInput = agentPage.locator('[data-testid="message-input"]'); const inputValue = await messageInput.inputValue(); expect(inputValue.length).toBeGreaterThan(0); // Send the message await agentPage.click('[data-testid="send-button"]'); // Verify feedback is recorded await TestUtils.waitForAPIResponse(agentPage, '**/api/ml/suggestions/feedback'); }); test('should allow agents to reject suggestions', async ({ agentPage }) => { // Open conversation await agentPage.click('[data-testid="conversation-test-conv-1"]'); await TestUtils.waitForElement(agentPage, '[data-testid="ai-suggestions-panel"]'); // Click reject button on first suggestion const firstSuggestion = agentPage.locator('[data-testid="suggestion-item"]').first(); await firstSuggestion.locator('[data-testid="reject-suggestion"]').click(); // Verify feedback modal appears await TestUtils.waitForElement(agentPage, '[data-testid="feedback-modal"]'); // Provide rejection reason await agentPage.selectOption('[data-testid="rejection-reason"]', 'not_relevant'); await agentPage.fill('[data-testid="feedback-comment"]', 'Suggestion not appropriate for this context'); // Submit feedback await agentPage.click('[data-testid="submit-feedback"]'); // Verify feedback is recorded await TestUtils.waitForAPIResponse(agentPage, '**/api/ml/suggestions/feedback'); }); test('should track suggestion usage analytics', async ({ mlContext }) => { // Generate suggestions await mlContext.post('/api/ml/suggestions/generate', { data: { conversation_id: 'test-conv-1', customer_message: 'Test message', platform: 'whatsapp', agent_id: 'test-agent-1' } }); // Submit feedback await mlContext.post('/api/ml/suggestions/feedback', { data: { suggestion_id: 'test-suggestion-1', feedback: 'accepted', agent_id: 'test-agent-1', conversation_id: 'test-conv-1' } }); // Get analytics const analyticsResponse = await mlContext.get('/api/ml/suggestions/analytics?agent_id=test-agent-1&days=30'); expect(analyticsResponse.ok()).toBeTruthy(); const analytics = await analyticsResponse.json(); expect(analytics).toHaveProperty('total_suggestions'); expect(analytics).toHaveProperty('accepted_suggestions'); expect(analytics).toHaveProperty('acceptance_rate'); expect(analytics.acceptance_rate).toBeGreaterThanOrEqual(0); expect(analytics.acceptance_rate).toBeLessThanOrEqual(1); }); }); test.describe('@ai @performance Performance and Quality', () => { test('should generate suggestions within acceptable time limits', async ({ mlContext }) => { const startTime = Date.now(); const response = await mlContext.post('/api/ml/suggestions/generate', { data: { conversation_id: 'test-conv-1', customer_message: 'Performance test message', platform: 'whatsapp', agent_id: 'test-agent-1' } }); const endTime = Date.now(); const responseTime = endTime - startTime; expect(response.ok()).toBeTruthy(); expect(responseTime).toBeLessThan(2000); // Should respond within 2 seconds }); test('should handle concurrent suggestion requests', async ({ mlContext }) => { const concurrentRequests = 10; const promises = []; for (let i = 0; i < concurrentRequests; i++) { const promise = mlContext.post('/api/ml/suggestions/generate', { data: { conversation_id: `test-conv-${i}`, customer_message: `Concurrent test message ${i}`, platform: 'whatsapp', agent_id: 'test-agent-1' } }); promises.push(promise); } const responses = await Promise.all(promises); // All requests should succeed for (const response of responses) { expect(response.ok()).toBeTruthy(); } }); test('should maintain suggestion quality across different message types', async ({ mlContext }) => { const messageTypes = [ { type: 'greeting', message: 'Bonjour', minConfidence: 0.8 }, { type: 'technical', message: 'Mon internet ne marche pas', minConfidence: 0.7 }, { type: 'billing', message: 'Ma facture est incorrecte', minConfidence: 0.7 }, { type: 'complaint', message: 'Je suis très mécontent', minConfidence: 0.6 } ]; for (const messageType of messageTypes) { const response = await mlContext.post('/api/ml/suggestions/generate', { data: { conversation_id: 'test-conv-1', customer_message: messageType.message, platform: 'whatsapp', agent_id: 'test-agent-1' } }); expect(response.ok()).toBeTruthy(); const result = await response.json(); expect(result.suggestions.length).toBeGreaterThan(0); // Check that at least one suggestion meets minimum confidence const hasHighConfidenceSuggestion = result.suggestions.some( (suggestion: any) => suggestion.confidence >= messageType.minConfidence ); expect(hasHighConfidenceSuggestion).toBeTruthy(); } }); }); test.describe('@ai @edge Edge Cases and Error Handling', () => { test('should handle empty or invalid messages gracefully', async ({ mlContext }) => { const invalidMessages = ['', ' ', null, undefined]; for (const message of invalidMessages) { const response = await mlContext.post('/api/ml/suggestions/generate', { data: { conversation_id: 'test-conv-1', customer_message: message, platform: 'whatsapp', agent_id: 'test-agent-1' } }); // Should handle gracefully without crashing expect(response.status()).toBeLessThan(500); } }); test('should handle very long messages', async ({ mlContext }) => { const longMessage = 'A'.repeat(10000); // 10KB message const response = await mlContext.post('/api/ml/suggestions/generate', { data: { conversation_id: 'test-conv-1', customer_message: longMessage, platform: 'whatsapp', agent_id: 'test-agent-1' } }); expect(response.ok()).toBeTruthy(); const result = await response.json(); expect(result.suggestions).toBeDefined(); }); test('should handle unsupported platforms gracefully', async ({ mlContext }) => { const response = await mlContext.post('/api/ml/suggestions/generate', { data: { conversation_id: 'test-conv-1', customer_message: 'Test message', platform: 'unsupported_platform', agent_id: 'test-agent-1' } }); // Should either succeed with default behavior or return appropriate error expect(response.status()).toBeLessThan(500); }); test('should handle missing conversation context', async ({ mlContext }) => { const response = await mlContext.post('/api/ml/suggestions/generate', { data: { conversation_id: 'non-existent-conversation', customer_message: 'Test message', platform: 'whatsapp', agent_id: 'test-agent-1' } }); // Should handle gracefully expect(response.status()).toBeLessThan(500); }); }); test.describe('@ai @accessibility Accessibility', () => { test('should be accessible with keyboard navigation', async ({ agentPage }) => { await agentPage.click('[data-testid="conversation-test-conv-1"]'); await TestUtils.waitForElement(agentPage, '[data-testid="ai-suggestions-panel"]'); // Test keyboard navigation through suggestions await agentPage.keyboard.press('Tab'); await agentPage.keyboard.press('Tab'); // Should be able to select suggestion with Enter await agentPage.keyboard.press('Enter'); // Verify suggestion was selected const messageInput = agentPage.locator('[data-testid="message-input"]'); const inputValue = await messageInput.inputValue(); expect(inputValue.length).toBeGreaterThan(0); }); test('should have proper ARIA labels and roles', async ({ agentPage }) => { await agentPage.click('[data-testid="conversation-test-conv-1"]'); await TestUtils.waitForElement(agentPage, '[data-testid="ai-suggestions-panel"]'); // Check ARIA attributes const suggestionsPanel = agentPage.locator('[data-testid="ai-suggestions-panel"]'); await expect(suggestionsPanel).toHaveAttribute('role', 'region'); await expect(suggestionsPanel).toHaveAttribute('aria-label'); const suggestions = agentPage.locator('[data-testid="suggestion-item"]'); const firstSuggestion = suggestions.first(); await expect(firstSuggestion).toHaveAttribute('role', 'button'); await expect(firstSuggestion).toHaveAttribute('aria-label'); }); }); });