/** * ============================================= * [PERFORMANCE] AUTOMATED RESPONSE SERVICE TESTS * Comprehensive testing of intelligent auto-response system * ============================================= */ import { test, expect, TestUtils } from '../../fixtures/test-fixtures'; import { testConfig } from '../../playwright.config'; test.describe('Automated Response Service', () => { test.beforeEach(async ({ agentPage }) => { // Navigate to conversations page await agentPage.goto('/dashboard/conversations'); await TestUtils.waitForStableNetwork(agentPage); }); test.describe('@ai @smoke Auto-Response Generation', () => { test('should generate automatic responses for common inquiries', async ({ mlContext, testData }) => { const commonInquiries = [ { message: 'Bonjour, quelles sont vos heures d\'ouverture ?', expectedIntent: 'hours_inquiry', shouldAutoRespond: true }, { message: 'Quel est mon solde actuel ?', expectedIntent: 'account_balance', shouldAutoRespond: true }, { message: 'Au revoir, merci pour votre aide', expectedIntent: 'goodbye', shouldAutoRespond: true } ]; for (const inquiry of commonInquiries) { const response = await mlContext.post('/api/ml/auto-response/generate', { data: { customer_message: inquiry.message, conversation_id: 'test-conv-1', platform: 'whatsapp', customer_id: 'test-customer-1' } }); expect(response.ok()).toBeTruthy(); const result = await response.json(); if (inquiry.shouldAutoRespond) { expect(result).toHaveProperty('auto_response'); expect(result.auto_response).toHaveProperty('text'); expect(result.auto_response).toHaveProperty('confidence'); expect(result.auto_response).toHaveProperty('intent', inquiry.expectedIntent); expect(result.auto_response.confidence).toBeGreaterThanOrEqual(0.8); } } }); test('should respect auto-response limits per conversation', async ({ mlContext }) => { const conversationId = 'test-conv-limit'; const maxAutoResponses = 3; // Send multiple messages that would trigger auto-responses for (let i = 0; i < maxAutoResponses + 2; i++) { const response = await mlContext.post('/api/ml/auto-response/generate', { data: { customer_message: 'Bonjour, comment allez-vous ?', conversation_id: conversationId, platform: 'whatsapp', customer_id: 'test-customer-1' } }); expect(response.ok()).toBeTruthy(); const result = await response.json(); if (i < maxAutoResponses) { // Should generate auto-response expect(result).toHaveProperty('auto_response'); } else { // Should not generate auto-response due to limit expect(result.auto_response).toBeNull(); } } }); test('should respect cooldown period between auto-responses', async ({ mlContext }) => { const conversationId = 'test-conv-cooldown'; // First auto-response const firstResponse = await mlContext.post('/api/ml/auto-response/generate', { data: { customer_message: 'Bonjour', conversation_id: conversationId, platform: 'whatsapp', customer_id: 'test-customer-1' } }); expect(firstResponse.ok()).toBeTruthy(); const firstResult = await firstResponse.json(); expect(firstResult).toHaveProperty('auto_response'); // Immediate second request (should be blocked by cooldown) const secondResponse = await mlContext.post('/api/ml/auto-response/generate', { data: { customer_message: 'Comment ça va ?', conversation_id: conversationId, platform: 'whatsapp', customer_id: 'test-customer-1' } }); expect(secondResponse.ok()).toBeTruthy(); const secondResult = await secondResponse.json(); expect(secondResult.auto_response).toBeNull(); }); }); test.describe('@ai @regression Escalation Detection', () => { test('should detect escalation triggers and avoid auto-response', async ({ mlContext }) => { const escalationTriggers = [ 'Je veux parler à un superviseur', 'C\'est absolument inacceptable', 'Je vais porter plainte', 'Votre service est nul', 'Je veux annuler mon abonnement' ]; for (const trigger of escalationTriggers) { const response = await mlContext.post('/api/ml/auto-response/generate', { data: { customer_message: trigger, conversation_id: 'test-conv-escalation', platform: 'whatsapp', customer_id: 'test-customer-1' } }); expect(response.ok()).toBeTruthy(); const result = await response.json(); // Should not auto-respond to escalation triggers expect(result.auto_response).toBeNull(); expect(result).toHaveProperty('escalation_detected', true); } }); test('should escalate VIP customer issues immediately', async ({ mlContext }) => { const response = await mlContext.post('/api/ml/auto-response/generate', { data: { customer_message: 'J\'ai un problème avec mon service', conversation_id: 'test-conv-vip', platform: 'whatsapp', customer_id: 'test-customer-vip' // VIP customer } }); expect(response.ok()).toBeTruthy(); const result = await response.json(); // VIP customers with negative sentiment should not get auto-responses if (result.sentiment_analysis?.label === 'negative') { expect(result.auto_response).toBeNull(); expect(result).toHaveProperty('vip_escalation', true); } }); test('should detect competitor mentions and escalate', async ({ mlContext }) => { const competitorMentions = [ 'Orange a un meilleur service', 'Je pense passer chez SFR', 'Bouygues propose de meilleures offres' ]; for (const mention of competitorMentions) { const response = await mlContext.post('/api/ml/auto-response/generate', { data: { customer_message: mention, conversation_id: 'test-conv-competitor', platform: 'whatsapp', customer_id: 'test-customer-1' } }); expect(response.ok()).toBeTruthy(); const result = await response.json(); expect(result.auto_response).toBeNull(); expect(result).toHaveProperty('competitor_mention_detected', true); } }); }); test.describe('@ai @business Business Hours Logic', () => { test('should adapt responses based on business hours', async ({ mlContext }) => { // Mock business hours check const businessHoursResponse = await mlContext.post('/api/ml/auto-response/generate', { data: { customer_message: 'Bonjour, j\'ai besoin d\'aide', conversation_id: 'test-conv-hours', platform: 'whatsapp', customer_id: 'test-customer-1', timestamp: '2024-01-15T14:00:00Z' // Business hours } }); expect(businessHoursResponse.ok()).toBeTruthy(); const businessResult = await businessHoursResponse.json(); if (businessResult.auto_response) { expect(businessResult.auto_response.text).not.toContain('en dehors des heures'); } // After hours const afterHoursResponse = await mlContext.post('/api/ml/auto-response/generate', { data: { customer_message: 'Bonjour, j\'ai besoin d\'aide', conversation_id: 'test-conv-hours-2', platform: 'whatsapp', customer_id: 'test-customer-1', timestamp: '2024-01-15T22:00:00Z' // After hours } }); expect(afterHoursResponse.ok()).toBeTruthy(); const afterResult = await afterHoursResponse.json(); if (afterResult.auto_response) { expect(afterResult.auto_response.text).toContain('en dehors des heures'); } }); test('should provide appropriate weekend responses', async ({ mlContext }) => { const weekendResponse = await mlContext.post('/api/ml/auto-response/generate', { data: { customer_message: 'Bonjour', conversation_id: 'test-conv-weekend', platform: 'whatsapp', customer_id: 'test-customer-1', timestamp: '2024-01-14T10:00:00Z' // Sunday } }); expect(weekendResponse.ok()).toBeTruthy(); const result = await weekendResponse.json(); if (result.auto_response) { // Should acknowledge weekend context const responseText = result.auto_response.text.toLowerCase(); expect( responseText.includes('weekend') || responseText.includes('lundi') || responseText.includes('prochaine ouverture') ).toBeTruthy(); } }); }); test.describe('@ai @platform Platform Adaptation', () => { test('should adapt responses for different platforms', async ({ mlContext }) => { const platforms = ['whatsapp', 'facebook', 'instagram', 'twitter']; const message = 'Bonjour, comment allez-vous ?'; for (const platform of platforms) { const response = await mlContext.post('/api/ml/auto-response/generate', { data: { customer_message: message, conversation_id: `test-conv-${platform}`, platform: platform, customer_id: 'test-customer-1' } }); expect(response.ok()).toBeTruthy(); const result = await response.json(); if (result.auto_response) { // Platform-specific validations switch (platform) { case 'twitter': expect(result.auto_response.text.length).toBeLessThanOrEqual(280); break; case 'instagram': expect(result.auto_response.text).toMatch(/[[AI]]/); // Should include emojis break; case 'whatsapp': expect(result.auto_response.text.length).toBeLessThanOrEqual(4096); break; } expect(result.auto_response).toHaveProperty('platform', platform); expect(result.auto_response).toHaveProperty('platform_adapted', true); } } }); test('should handle platform-specific features', async ({ mlContext }) => { // Facebook quick replies const facebookResponse = await mlContext.post('/api/ml/auto-response/generate', { data: { customer_message: 'Quelles sont mes options ?', conversation_id: 'test-conv-facebook', platform: 'facebook', customer_id: 'test-customer-1' } }); expect(facebookResponse.ok()).toBeTruthy(); const facebookResult = await facebookResponse.json(); if (facebookResult.auto_response) { expect(facebookResult.auto_response).toHaveProperty('quick_replies'); } // WhatsApp buttons const whatsappResponse = await mlContext.post('/api/ml/auto-response/generate', { data: { customer_message: 'J\'ai besoin d\'aide', conversation_id: 'test-conv-whatsapp', platform: 'whatsapp', customer_id: 'test-customer-1' } }); expect(whatsappResponse.ok()).toBeTruthy(); const whatsappResult = await whatsappResponse.json(); if (whatsappResult.auto_response) { expect(whatsappResult.auto_response).toHaveProperty('buttons'); } }); }); test.describe('@ai @performance Response Quality and Performance', () => { test('should maintain response quality across different intents', async ({ mlContext }) => { const intentTests = [ { intent: 'greeting', message: 'Bonjour', minConfidence: 0.9 }, { intent: 'hours_inquiry', message: 'Quelles sont vos heures ?', minConfidence: 0.8 }, { intent: 'account_balance', message: 'Quel est mon solde ?', minConfidence: 0.8 }, { intent: 'goodbye', message: 'Au revoir', minConfidence: 0.9 } ]; for (const test of intentTests) { const response = await mlContext.post('/api/ml/auto-response/generate', { data: { customer_message: test.message, conversation_id: 'test-conv-quality', platform: 'whatsapp', customer_id: 'test-customer-1' } }); expect(response.ok()).toBeTruthy(); const result = await response.json(); if (result.auto_response) { expect(result.auto_response.confidence).toBeGreaterThanOrEqual(test.minConfidence); expect(result.auto_response.intent).toBe(test.intent); expect(result.auto_response.text.length).toBeGreaterThan(10); } } }); test('should respond within acceptable time limits', async ({ mlContext }) => { const startTime = Date.now(); const response = await mlContext.post('/api/ml/auto-response/generate', { data: { customer_message: 'Performance test message', conversation_id: 'test-conv-performance', platform: 'whatsapp', customer_id: 'test-customer-1' } }); const endTime = Date.now(); const responseTime = endTime - startTime; expect(response.ok()).toBeTruthy(); expect(responseTime).toBeLessThan(1500); // Should respond within 1.5 seconds }); test('should handle high concurrent load', async ({ mlContext }) => { const concurrentRequests = 20; const promises = []; for (let i = 0; i < concurrentRequests; i++) { const promise = mlContext.post('/api/ml/auto-response/generate', { data: { customer_message: `Concurrent test ${i}`, conversation_id: `test-conv-concurrent-${i}`, platform: 'whatsapp', customer_id: 'test-customer-1' } }); promises.push(promise); } const responses = await Promise.all(promises); // All requests should succeed for (const response of responses) { expect(response.ok()).toBeTruthy(); } }); }); test.describe('@ai @analytics Analytics and Reporting', () => { test('should track auto-response usage analytics', async ({ mlContext }) => { // Generate some auto-responses await mlContext.post('/api/ml/auto-response/generate', { data: { customer_message: 'Bonjour', conversation_id: 'test-conv-analytics-1', platform: 'whatsapp', customer_id: 'test-customer-1' } }); await mlContext.post('/api/ml/auto-response/generate', { data: { customer_message: 'Au revoir', conversation_id: 'test-conv-analytics-2', platform: 'facebook', customer_id: 'test-customer-2' } }); // Get analytics const analyticsResponse = await mlContext.get('/api/ml/auto-response/analytics?days=30'); expect(analyticsResponse.ok()).toBeTruthy(); const analytics = await analyticsResponse.json(); expect(analytics).toHaveProperty('total_auto_responses'); expect(analytics).toHaveProperty('high_confidence_responses'); expect(analytics).toHaveProperty('auto_sent_responses'); expect(analytics).toHaveProperty('average_confidence'); expect(analytics).toHaveProperty('response_by_intent'); expect(analytics).toHaveProperty('response_by_platform'); expect(analytics).toHaveProperty('success_rate'); }); test('should provide intent distribution analytics', async ({ mlContext }) => { const analyticsResponse = await mlContext.get('/api/ml/auto-response/analytics?days=30'); expect(analyticsResponse.ok()).toBeTruthy(); const analytics = await analyticsResponse.json(); expect(analytics.response_by_intent).toBeDefined(); // Should have common intents const intents = Object.keys(analytics.response_by_intent); expect(intents).toContain('greeting'); expect(intents).toContain('goodbye'); }); }); test.describe('@ai @edge Edge Cases', () => { test('should handle malformed or empty messages', async ({ mlContext }) => { const edgeCases = ['', ' ', null, undefined, '[AI][AI][AI]']; for (const message of edgeCases) { const response = await mlContext.post('/api/ml/auto-response/generate', { data: { customer_message: message, conversation_id: 'test-conv-edge', platform: 'whatsapp', customer_id: 'test-customer-1' } }); // Should handle gracefully expect(response.status()).toBeLessThan(500); } }); test('should handle non-existent conversation gracefully', async ({ mlContext }) => { const response = await mlContext.post('/api/ml/auto-response/generate', { data: { customer_message: 'Test message', conversation_id: 'non-existent-conversation', platform: 'whatsapp', customer_id: 'test-customer-1' } }); expect(response.status()).toBeLessThan(500); }); }); });