import { test, expect, Page } from '@playwright/test'; /** * Comprehensive Test Suite for Modernized Support Client Page * Tests all enhanced features implemented in Phase 2 */ test.describe('Support Client Page - Modernized Features', () => { let page: Page; const BASE_URL = process.env.BASE_URL || 'http://localhost:3001'; test.beforeEach(async ({ browser }) => { page = await browser.newPage(); await page.goto(BASE_URL); // Wait for the application to load await page.waitForLoadState('networkidle'); // Navigate to Support page if not already there const currentUrl = page.url(); if (!currentUrl.includes('/support') && !currentUrl.includes('localhost:3001')) { // Try to find and click support navigation const supportLink = page.locator('text=Support').or(page.locator('[href*="support"]')).first(); if (await supportLink.isVisible()) { await supportLink.click(); await page.waitForLoadState('networkidle'); } } }); test.afterEach(async () => { await page.close(); }); test.describe('Chat en Direct (Live Chat) Functionality', () => { test('should display Chat en Direct tab with unread message badge', async () => { // Look for the Chat tab with badge functionality const chatTab = page.locator('text=Chat en Direct').or(page.locator('text=Chat Assistant')); await expect(chatTab).toBeVisible(); // Check if badge component is present (may be 0 initially) const badgeElement = page.locator('[data-testid="chat-badge"]').or( page.locator('.MuiBadge-badge') ); // Badge should exist even if count is 0 await expect(badgeElement).toBeAttached(); }); test('should show connection status indicator', async () => { // Click on Chat tab const chatTab = page.locator('text=Chat en Direct').or(page.locator('text=Chat Assistant')); await chatTab.click(); await page.waitForTimeout(1000); // Check for connection status chip const connectionStatus = page.locator('text=En ligne').or(page.locator('text=Hors ligne')); await expect(connectionStatus).toBeVisible(); // Check for online indicator icon const onlineIndicator = page.locator('[data-testid="connection-status"]').or( page.locator('.MuiChip-root') ); await expect(onlineIndicator).toBeVisible(); }); test('should display professional chat interface', async () => { // Navigate to Chat tab const chatTab = page.locator('text=Chat en Direct').or(page.locator('text=Chat Assistant')); await chatTab.click(); await page.waitForTimeout(1000); // Check for chat header with agent info const chatHeader = page.locator('text=Support Free Mobile'); await expect(chatHeader).toBeVisible(); // Check for agent status information const agentInfo = page.locator('text=agents en ligne').or(page.locator('text=en attente')); await expect(agentInfo).toBeVisible(); // Check for wait time indicator const waitTime = page.locator('text=Temps d\'attente').or(page.locator('text=attente:')); await expect(waitTime).toBeVisible(); }); test('should send and receive messages in real-time', async () => { // Navigate to Chat tab const chatTab = page.locator('text=Chat en Direct').or(page.locator('text=Chat Assistant')); await chatTab.click(); await page.waitForTimeout(1000); // Find message input field const messageInput = page.locator('input[placeholder*="message"]').or( page.locator('textarea[placeholder*="message"]') ); await expect(messageInput).toBeVisible(); // Type a test message const testMessage = 'Test message for automated testing'; await messageInput.fill(testMessage); // Find and click send button const sendButton = page.locator('button[type="submit"]').or( page.locator('[aria-label="Envoyer"]') ); await sendButton.click(); // Verify message appears in chat await expect(page.locator(`text=${testMessage}`)).toBeVisible(); // Wait for bot response (should appear within 2 seconds) await page.waitForTimeout(2000); // Check for bot response const botResponse = page.locator('text=Merci pour votre message').or( page.locator('text=Je comprends votre demande') ); await expect(botResponse).toBeVisible(); }); test('should show typing indicator during response', async () => { // Navigate to Chat tab const chatTab = page.locator('text=Chat en Direct').or(page.locator('text=Chat Assistant')); await chatTab.click(); await page.waitForTimeout(1000); // Send a message const messageInput = page.locator('input[placeholder*="message"]').or( page.locator('textarea[placeholder*="message"]') ); await messageInput.fill('Test typing indicator'); const sendButton = page.locator('button[type="submit"]').or( page.locator('[aria-label="Envoyer"]') ); await sendButton.click(); // Check for typing indicator (should appear briefly) const typingIndicator = page.locator('text=tape...').or( page.locator('[data-testid="typing-indicator"]') ); // Wait a moment for typing indicator to appear await page.waitForTimeout(500); // The typing indicator might be visible briefly // We'll check if it exists in the DOM even if not currently visible await expect(typingIndicator).toBeAttached(); }); test('should handle connection status changes', async () => { // Navigate to Chat tab const chatTab = page.locator('text=Chat en Direct').or(page.locator('text=Chat Assistant')); await chatTab.click(); await page.waitForTimeout(1000); // Initial connection should be online const onlineStatus = page.locator('text=En ligne'); await expect(onlineStatus).toBeVisible(); // Wait for potential connection status simulation (up to 30 seconds) // Note: In real implementation, connection changes are simulated randomly await page.waitForTimeout(5000); // Verify connection status element is still present const connectionStatus = page.locator('text=En ligne').or(page.locator('text=Hors ligne')); await expect(connectionStatus).toBeVisible(); }); }); test.describe('Statistiques du Support (Support Statistics)', () => { test('should display modernized analytics dashboard', async () => { // Navigate to Statistics tab const statsTab = page.locator('text=Statistiques').or(page.locator('text=Analytics')); await statsTab.click(); await page.waitForTimeout(1000); // Check for main title const title = page.locator('text=Statistiques du Support').or( page.locator('text=Analytics Dashboard') ); await expect(title).toBeVisible(); // Check for refresh button const refreshButton = page.locator('[aria-label*="Actualiser"]').or( page.locator('button[title*="refresh"]') ); await expect(refreshButton).toBeVisible(); // Check for last updated timestamp const lastUpdated = page.locator('text=Dernière mise à jour').or( page.locator('text=Last updated') ); await expect(lastUpdated).toBeVisible(); }); test('should show real-time status bar', async () => { // Navigate to Statistics tab const statsTab = page.locator('text=Statistiques').or(page.locator('text=Analytics')); await statsTab.click(); await page.waitForTimeout(1000); // Check for real-time status indicators const agentsOnline = page.locator('text=agents en ligne'); await expect(agentsOnline).toBeVisible(); const queueLength = page.locator('text=en file d\'attente'); await expect(queueLength).toBeVisible(); const averageWait = page.locator('text=Attente moyenne'); await expect(averageWait).toBeVisible(); const onlineUsers = page.locator('text=utilisateurs connectés'); await expect(onlineUsers).toBeVisible(); }); test('should display enhanced metrics cards', async () => { // Navigate to Statistics tab const statsTab = page.locator('text=Statistiques').or(page.locator('text=Analytics')); await statsTab.click(); await page.waitForTimeout(1000); // Check for Total Tickets card const totalTickets = page.locator('text=Total Tickets'); await expect(totalTickets).toBeVisible(); // Check for CSAT Score card const csatScore = page.locator('text=Score CSAT'); await expect(csatScore).toBeVisible(); // Check for Average Time card const avgTime = page.locator('text=Temps Moyen'); await expect(avgTime).toBeVisible(); // Check for Resolution Rate card const resolutionRate = page.locator('text=Taux de Résolution'); await expect(resolutionRate).toBeVisible(); // Check for trend indicators (up/down arrows) const trendIndicators = page.locator('[data-testid="trend-indicator"]').or( page.locator('.MuiSvgIcon-root') ); await expect(trendIndicators.first()).toBeVisible(); }); test('should refresh statistics when refresh button is clicked', async () => { // Navigate to Statistics tab const statsTab = page.locator('text=Statistiques').or(page.locator('text=Analytics')); await statsTab.click(); await page.waitForTimeout(1000); // Get initial timestamp const timestampElement = page.locator('text=Dernière mise à jour').or( page.locator('text=Last updated') ); const initialTimestamp = await timestampElement.textContent(); // Click refresh button const refreshButton = page.locator('[aria-label*="Actualiser"]').or( page.locator('button[title*="refresh"]') ); await refreshButton.click(); // Wait for refresh to complete await page.waitForTimeout(2000); // Check for loading state or updated timestamp const loadingIndicator = page.locator('[data-testid="loading"]').or( page.locator('.MuiCircularProgress-root') ); // Either loading indicator should appear or timestamp should update const updatedTimestamp = await timestampElement.textContent(); expect(updatedTimestamp).not.toBe(initialTimestamp); }); test('should show progress chips and status indicators', async () => { // Navigate to Statistics tab const statsTab = page.locator('text=Statistiques').or(page.locator('text=Analytics')); await statsTab.click(); await page.waitForTimeout(1000); // Check for progress chips (like "+12 aujourd'hui", "Excellent", etc.) const progressChips = page.locator('.MuiChip-root'); await expect(progressChips.first()).toBeVisible(); // Check for status indicators with colors const statusCards = page.locator('.MuiCard-root'); await expect(statusCards.first()).toBeVisible(); // Verify avatars with icons are present const avatars = page.locator('.MuiAvatar-root'); await expect(avatars.first()).toBeVisible(); }); }); test.describe('Responsive Design Validation', () => { test('should work correctly on mobile devices', async () => { // Set mobile viewport await page.setViewportSize({ width: 375, height: 667 }); await page.reload(); await page.waitForLoadState('networkidle'); // Test chat functionality on mobile const chatTab = page.locator('text=Chat en Direct').or(page.locator('text=Chat Assistant')); await chatTab.click(); await page.waitForTimeout(1000); // Verify chat interface is responsive const chatContainer = page.locator('.MuiCard-root').first(); await expect(chatContainer).toBeVisible(); // Test message input on mobile const messageInput = page.locator('input[placeholder*="message"]').or( page.locator('textarea[placeholder*="message"]') ); await expect(messageInput).toBeVisible(); }); test('should work correctly on tablet devices', async () => { // Set tablet viewport await page.setViewportSize({ width: 768, height: 1024 }); await page.reload(); await page.waitForLoadState('networkidle'); // Test statistics dashboard on tablet const statsTab = page.locator('text=Statistiques').or(page.locator('text=Analytics')); await statsTab.click(); await page.waitForTimeout(1000); // Verify cards layout on tablet const metricsCards = page.locator('.MuiCard-root'); await expect(metricsCards.first()).toBeVisible(); }); test('should work correctly on desktop', async () => { // Set desktop viewport await page.setViewportSize({ width: 1920, height: 1080 }); await page.reload(); await page.waitForLoadState('networkidle'); // Test full functionality on desktop const chatTab = page.locator('text=Chat en Direct').or(page.locator('text=Chat Assistant')); await chatTab.click(); await page.waitForTimeout(1000); // Verify all elements are properly displayed const chatHeader = page.locator('text=Support Free Mobile'); await expect(chatHeader).toBeVisible(); // Switch to statistics const statsTab = page.locator('text=Statistiques').or(page.locator('text=Analytics')); await statsTab.click(); await page.waitForTimeout(1000); // Verify all metrics cards are visible const totalTickets = page.locator('text=Total Tickets'); await expect(totalTickets).toBeVisible(); }); }); test.describe('Interactive Elements Validation', () => { test('should show tooltips on hover', async () => { // Navigate to Statistics tab const statsTab = page.locator('text=Statistiques').or(page.locator('text=Analytics')); await statsTab.click(); await page.waitForTimeout(1000); // Hover over refresh button to show tooltip const refreshButton = page.locator('[aria-label*="Actualiser"]').or( page.locator('button[title*="refresh"]') ); await refreshButton.hover(); // Wait for tooltip to appear await page.waitForTimeout(500); // Check for tooltip text const tooltip = page.locator('text=Actualiser').or(page.locator('[role="tooltip"]')); await expect(tooltip).toBeVisible(); }); test('should show snackbar notifications', async () => { // Navigate to Statistics tab const statsTab = page.locator('text=Statistiques').or(page.locator('text=Analytics')); await statsTab.click(); await page.waitForTimeout(1000); // Click refresh to trigger notification const refreshButton = page.locator('[aria-label*="Actualiser"]').or( page.locator('button[title*="refresh"]') ); await refreshButton.click(); // Wait for snackbar notification await page.waitForTimeout(2000); // Check for notification message const notification = page.locator('text=Statistiques mises à jour').or( page.locator('.MuiSnackbar-root') ); await expect(notification).toBeVisible(); }); test('should handle button states correctly', async () => { // Navigate to Chat tab const chatTab = page.locator('text=Chat en Direct').or(page.locator('text=Chat Assistant')); await chatTab.click(); await page.waitForTimeout(1000); // Check send button is initially disabled (empty input) const sendButton = page.locator('button[type="submit"]').or( page.locator('[aria-label="Envoyer"]') ); // Type message to enable button const messageInput = page.locator('input[placeholder*="message"]').or( page.locator('textarea[placeholder*="message"]') ); await messageInput.fill('Test message'); // Button should now be enabled await expect(sendButton).toBeEnabled(); // Clear input await messageInput.fill(''); // Button should be disabled again await expect(sendButton).toBeDisabled(); }); }); });