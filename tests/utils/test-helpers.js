const { expect } = require('@playwright/test'); /** * Test helper utilities for Playwright tests */ class TestHelpers { constructor(page) { this.page = page; } /** * Login with user credentials */ async login(credentials) { await this.page.goto('/login'); // Wait for login form to be visible await this.page.waitForSelector('[data-testid="login-form"]', { timeout: 10000 }); // Fill login form await this.page.fill('[data-testid="email-input"]', credentials.email); await this.page.fill('[data-testid="password-input"]', credentials.password); // Submit form await this.page.click('[data-testid="login-button"]'); // Wait for navigation or success indicator await this.page.waitForURL('**/dashboard', { timeout: 15000 }); // Verify login success await expect(this.page.locator('[data-testid="user-menu"]')).toBeVisible(); } /** * Logout current user */ async logout() { // Click user menu await this.page.click('[data-testid="user-menu"]'); // Click logout await this.page.click('[data-testid="logout-button"]'); // Wait for redirect to login await this.page.waitForURL('**/login', { timeout: 10000 }); } /** * Navigate to a specific page */ async navigateTo(path) { await this.page.goto(path); await this.page.waitForLoadState('networkidle'); } /** * Wait for API response */ async waitForApiResponse(urlPattern, timeout = 10000) { return await this.page.waitForResponse( response => response.url().includes(urlPattern) && response.status() === 200, { timeout } ); } /** * Check for console errors */ async checkConsoleErrors() { const errors = []; this.page.on('console', msg => { if (msg.type() === 'error') { errors.push(msg.text()); } }); return errors; } /** * Take screenshot with timestamp */ async takeScreenshot(name) { const timestamp = new Date().toISOString().replace(/[:.]/g, '-'); const filename = `${name}-${timestamp}.png`; await this.page.screenshot({ path: `test-results/screenshots/${filename}`, fullPage: true }); return filename; } /** * Fill form with data */ async fillForm(formData) { for (const [field, value] of Object.entries(formData)) { const selector = `[data-testid="${field}-input"], [name="${field}"], #${field}`; await this.page.fill(selector, value); } } /** * Wait for element to be visible */ async waitForElement(selector, timeout = 10000) { return await this.page.waitForSelector(selector, { state: 'visible', timeout }); } /** * Check if element exists */ async elementExists(selector) { try { await this.page.waitForSelector(selector, { timeout: 1000 }); return true; } catch { return false; } } /** * Get element text content */ async getElementText(selector) { const element = await this.page.locator(selector); return await element.textContent(); } /** * Click element with retry */ async clickWithRetry(selector, maxRetries = 3) { for (let i = 0; i < maxRetries; i++) { try { await this.page.click(selector); return; } catch (error) { if (i === maxRetries - 1) throw error; await this.page.waitForTimeout(1000); } } } /** * Verify page accessibility */ async checkAccessibility() { // Basic accessibility checks const issues = []; // Check for alt text on images const images = await this.page.locator('img').all(); for (const img of images) { const alt = await img.getAttribute('alt'); if (!alt) { issues.push('Image missing alt text'); } } // Check for form labels const inputs = await this.page.locator('input[type="text"], input[type="email"], input[type="password"]').all(); for (const input of inputs) { const id = await input.getAttribute('id'); const ariaLabel = await input.getAttribute('aria-label'); const hasLabel = id ? await this.page.locator(`label[for="${id}"]`).count() > 0 : false; if (!hasLabel && !ariaLabel) { issues.push('Input missing label or aria-label'); } } return issues; } /** * Mock API response */ async mockApiResponse(urlPattern, responseData, status = 200) { await this.page.route(urlPattern, route => { route.fulfill({ status, contentType: 'application/json', body: JSON.stringify(responseData) }); }); } /** * Clear all cookies and local storage */ async clearSession() { await this.page.context().clearCookies(); await this.page.evaluate(() => { localStorage.clear(); sessionStorage.clear(); }); } /** * Set viewport size */ async setViewport(width, height) { await this.page.setViewportSize({ width, height }); } /** * Verify responsive design */ async checkResponsiveDesign() { const viewports = [ { width: 1920, height: 1080, name: 'Desktop' }, { width: 1024, height: 768, name: 'Tablet' }, { width: 375, height: 667, name: 'Mobile' } ]; const results = []; for (const viewport of viewports) { await this.setViewport(viewport.width, viewport.height); await this.page.waitForTimeout(500); // Allow layout to adjust // Check if navigation is accessible const navVisible = await this.elementExists('[data-testid="navigation"]'); const menuVisible = await this.elementExists('[data-testid="mobile-menu"]'); results.push({ viewport: viewport.name, navigationAccessible: navVisible || menuVisible, width: viewport.width, height: viewport.height }); } return results; } } module.exports = TestHelpers;