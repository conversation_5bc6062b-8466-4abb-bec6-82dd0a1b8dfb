# Deployment Guide for ChatbotRNCP ## Overview This guide covers the deployment process for the ChatbotRNCP application using GitHub Actions and Vercel. ## Prerequisites ### Required Accounts - GitHub account with repository access - Vercel account connected to GitHub - MongoDB Atlas account (for production database) ### Required Secrets Configure these secrets in your GitHub repository settings: ``` VERCEL_TOKEN=your-vercel-token VERCEL_ORG_ID=your-vercel-org-id VERCEL_PROJECT_ID=your-vercel-project-id MONGODB_URI=your-production-mongodb-uri JWT_SECRET=your-secure-jwt-secret OPENAI_API_KEY=your-openai-api-key ``` ## Deployment Workflows ### 1. Production Deployment - **Trigger**: Push to `main` branch - **Workflow**: `.github/workflows/production.yaml` - **Steps**: 1. Run tests 2. Build frontend 3. Deploy to Vercel production ### 2. Staging Deployment - **Trigger**: Push to `develop` branch - **Workflow**: `.github/workflows/staging.yaml` - **Steps**: 1. Run tests 2. Build frontend 3. Deploy to Vercel preview (staging) ### 3. Preview Deployment - **Trigger**: Push to feature branches or PRs - **Workflow**: `.github/workflows/preview.yaml` - **Steps**: 1. Run tests 2. Build frontend 3. Deploy to Vercel preview 4. Comment PR with preview URL ## Environment Setup ### Development ```bash # Clone repository git clone https://github.com/Anderson-Archimede/ChatbotRNCP.git cd ChatbotRNCP/free-mobile-chatbot # Copy environment template cp env.template .env # Install dependencies npm run install:all # Start development servers npm run dev ``` ### Production Environment Variables Set these in Vercel dashboard: ``` NODE_ENV=production MONGODB_URI=mongodb+srv://user:<EMAIL>/chatbot JWT_SECRET=your-secure-jwt-secret-here OPENAI_API_KEY=sk-your-openai-key-here FRONTEND_URL=https://chatbotrncp.vercel.app ALLOWED_ORIGINS=https://chatbotrncp.vercel.app ``` ## Vercel Configuration The `vercel.json` file is configured to: - Build the React frontend - Route API calls to backend functions - Set proper security headers - Handle SPA routing ### Key Configuration Points - Frontend builds from `frontend/` directory - Backend API routes from `backend/api/` - Static files served with caching headers - CORS configured for production domain ## Database Setup ### MongoDB Atlas (Recommended) 1. Create MongoDB Atlas cluster 2. Configure network access (allow Vercel IPs) 3. Create database user 4. Get connection string 5. Add to environment variables ### Local Development ```bash # Using Docker docker-compose up -d mongodb # Or install MongoDB locally # Follow MongoDB installation guide ``` ## Testing ### Local Testing ```bash # Run all tests npm test # Run specific test suites npm run test:frontend npm run test:e2e npm run test:smoke ``` ### CI/CD Testing Tests run automatically on: - Pull requests - Pushes to main/develop - Manual workflow dispatch ## Monitoring ### Health Checks - Frontend: `https://chatbotrncp.vercel.app/` - Backend API: `https://chatbotrncp.vercel.app/api/health` - System Status: `https://chatbotrncp.vercel.app/api/status` ### Logs - Vercel function logs in dashboard - Application logs via Winston - Error tracking via Sentry (if configured) ## Troubleshooting ### Common Issues 1. **Build Failures** - Check Node.js version compatibility - Verify all dependencies are installed - Check for TypeScript errors 2. **Deployment Failures** - Verify Vercel secrets are set - Check environment variables - Review build logs 3. **Database Connection Issues** - Verify MongoDB URI format - Check network access settings - Confirm credentials 4. **API Errors** - Check CORS configuration - Verify environment variables - Review function logs ### Debug Commands ```bash # Check environment npm run validate:env # Test production build locally npm run build npm run production:start # Run health checks npm run production:health ``` ## Security Considerations - All secrets stored in GitHub/Vercel securely - HTTPS enforced in production - Security headers configured - Rate limiting enabled - Input validation implemented ## Support For deployment issues: 1. Check GitHub Actions logs 2. Review Vercel deployment logs 3. Verify environment configuration 4. Contact development team if needed